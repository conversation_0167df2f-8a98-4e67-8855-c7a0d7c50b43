#web: python manage.py run_gunicorn -b "0.0.0.0:$PORT" -w 6

# Procfile with nginx, pgbouncer, gunicorn and django-q
# web: bin/start-nginx bin/start-pgbouncer-stunnel gunicorn manage:application -c python:/app/gunicorn.py
# web: bin/start-nginx bin/start-pgbouncer-stunnel cd .. && gunicorn peakery.wsgi:application -c python:/app/gunicorn.py
# web: waitress-serve --port=$PORT wsgi:application
web: bin/start-pgbouncer waitress-serve --threads=30 --port=$PORT --expose-tracebacks wsgi:application
worker: python peakery/worker.py
