import unittest
import requests


class MyAPITest(unittest.TestCase):
    def setUp(self):
        # Set up the test environment
        self.base_url = "http://127.0.0.1:8000/api"
        self.headers = {"Content-Type": "application/json"}

    def test_orignal_peaks_map(self):
        # Define the test cases
        json_return_object = {
            "parameters": [
                {
                    "keyword": "",
                    "map_country": "",
                    "near": "",
                    "near_query": "",
                    "state_id": "",
                    "elev_min": "",
                    "elev_max": "",
                    "prom_min": "",
                    "prom_max": "",
                    "summits_min": "",
                    "summits_max": "",
                    "difficulty_min": "",
                    "difficulty_max": "",
                    "length_min": "",
                    "length_max": "",
                    "vertical_min": "",
                    "vertical_max": "",
                    "last_climbed_min": "",
                    "last_climbed_max": "",
                    "classics": "",
                    "in_challenge": "",
                    "you_climbed": "",
                    "has_gps": "",
                    "lat": "",
                    "lng": "",
                    "bounds": "",
                }
            ],
            "peaks": [
                {
                    "id": "",
                    "name": "",
                    "slug": "",
                    "is_classic": "",
                    "kom_user": "",
                    "first_ascent_user": "",
                    "summit_stewards": "",
                    "lat": "",
                    "lng": "",
                    "range": "",
                    "location_edited": "",
                    "elevation_edited": "",
                    "summit_count": "",
                    "elevation": "",
                    "prominence": "",
                    "your_summits": "",
                    "your_attempts": "",
                    "challenge_count": "",
                    "thumbnail_url": "",
                    "region": [],
                    "country": [],
                    "group": [],
                }
            ],
            "photos": [
                {
                    "id": "",
                    "peak_slug": "",
                    "summit_log_id": "",
                    "fullsize_url": "",
                    "thumbnail_url": "",
                    "photo_lat": "",
                    "photo_lng": "",
                    "username": "",
                    "caption": "",
                    "createdate": "",
                }
            ],
            "challenges": [
                {
                    "id": "",
                    "name": "",
                    "slug": "",
                    "peak_count": "",
                    "fullsize_url": "",
                    "thumbnail_url": "",
                    "challenge_lat": "",
                    "challenge_lng": "",
                }
            ],
        }

        # Make the API request
        url = f"{self.base_url}/peaks/map/?elev_min=0&elev_max=29500&prom_min=0&prom_max=29500&summits_min=0&summits_max=500&difficulty_min=1&difficulty_max=5&length_min=0&length_max=20&vertical_min=0&vertical_max=10000&last_climbed_min=0&last_climbed_max=11&classics=false&in_challenge=false&you_climbed=false&has_gps=false&bounds=-85.05112899999989,-106.56236049272928,85.05112877980659,272.6947028347678&zoom=0.3944626741523261&near=false&sort=popular"
        response = requests.get(url, headers=self.headers)

        # Assert the response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.headers["Content-Type"], "application/json")

        data = response.json()

        # Assert the existence of fields
        self.assertIn("parameters", data)
        self.assertIn("peaks", data)
        self.assertIn("photos", data)
        self.assertIn("challenges", data)
        # Assert that parameters, peaks, photos, and challenges are arrays
        self.assertIsInstance(data["parameters"], list)
        self.assertIsInstance(data["peaks"], list)
        self.assertIsInstance(data["photos"], list)
        self.assertIsInstance(data["challenges"], list)
        
        parameters = data["parameters"][0]
        for field in json_return_object["parameters"][0]:
            self.assertIn(field, parameters)

        for peak in data["peaks"]:
            for field in json_return_object["peaks"][0]:
                self.assertIn(field, peak)

        for photo in data["photos"]:
            for field in json_return_object["photos"][0]:
                self.assertIn(field, photo)

        for challenge in data["challenges"]:
            for field in json_return_object["challenges"][0]:
                self.assertIn(field, challenge)

    def test_peaks_map_peaks(self):
        # Define the test cases
        json_return_object = {
            "parameters": [
                {
                    "map_country": "",
                    "near": "",
                    "near_query": "",
                    "state_id": "",
                    "elev_min": "",
                    "elev_max": "",
                    "prom_min": "",
                    "prom_max": "",
                    "summits_min": "",
                    "summits_max": "",
                    "difficulty_min": "",
                    "difficulty_max": "",
                    "length_min": "",
                    "length_max": "",
                    "vertical_min": "",
                    "vertical_max": "",
                    "last_climbed_min": "",
                    "last_climbed_max": "",
                    "classics": "",
                    "in_challenge": "",
                    "you_climbed": "",
                    "has_gps": "",
                    "lat": "",
                    "lng": "",
                    "bounds": "",
                }
            ],
            "peaks": [
                {
                    "id": "",
                    "name": "",
                    "slug": "",
                    "is_classic": "",
                    "kom_user": "",
                    "first_ascent_user": "",
                    "summit_stewards": "",
                    "lat": "",
                    "lng": "",
                    "range": "",
                    "location_edited": "",
                    "elevation_edited": "",
                    "summit_count": "",
                    "elevation": "",
                    "prominence": "",
                    "your_summits": "",
                    "your_attempts": "",
                    "challenge_count": "",
                    "thumbnail_url": "",
                    "region": [],
                    "country": [],
                    "group": [],
                }
            ]
        }

        # Make the API request
        url = f"{self.base_url}/peaks/map/peaks/?elev_min=0&elev_max=29500&prom_min=0&prom_max=29500&summits_min=0&summits_max=500&difficulty_min=1&difficulty_max=5&length_min=0&length_max=20&vertical_min=0&vertical_max=10000&last_climbed_min=0&last_climbed_max=11&classics=false&in_challenge=false&you_climbed=false&has_gps=false&bounds=-85.05112899999989,-106.56236049272928,85.05112877980659,272.6947028347678&zoom=0.3944626741523261&near=false&sort=popular"
        response = requests.get(url, headers=self.headers)

        # Assert the response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.headers["Content-Type"], "application/json")

        data = response.json()

        self.assertIn("parameters", data)
        self.assertIn("peaks", data)

        self.assertIsInstance(data["parameters"], list)
        self.assertIsInstance(data["peaks"], list)
        
        parameters = data["parameters"][0]
        for field in json_return_object["parameters"][0]:
            self.assertIn(field, parameters)

        for peak in data["peaks"]:
            for field in json_return_object["peaks"][0]:
                self.assertIn(field, peak)

    def test_peaks_map_get_photos(self):
        json_return_object = {
            "photos": [
                {
                    "id": "",
                    "peak_slug": "",
                    "summit_log_id": "",
                    "fullsize_url": "",
                    "thumbnail_url": "",
                    "photo_lat": "",
                    "photo_lng": "",
                    "username": "",
                    "caption": "",
                    "createdate": "",
                }
            ],
        }

        # Make the API request
        url = f"{self.base_url}/peaks/map/photos/?elev_min=0&elev_max=29500&prom_min=0&prom_max=29500&summits_min=0&summits_max=500&difficulty_min=1&difficulty_max=5&length_min=0&length_max=20&vertical_min=0&vertical_max=10000&last_climbed_min=0&last_climbed_max=11&classics=false&in_challenge=false&you_climbed=false&has_gps=false&bounds=-85.05112899999989,-106.56236049272928,85.05112877980659,272.6947028347678&zoom=0.3944626741523261&near=false&sort=popular"
        response = requests.get(url, headers=self.headers)

        # Assert the response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.headers["Content-Type"], "application/json")

        data = response.json()

        self.assertIn("photos", data)
        self.assertIsInstance(data["photos"], list)

        for photo in data["photos"]:
            for field in json_return_object["photos"][0]:
                self.assertIn(field, photo)

    def test_peaks_map_challenges(self):
        # Define the test cases
        json_return_object = {
            "challenges": [
                {
                    "id": "",
                    "name": "",
                    "slug": "",
                    "peak_count": "",
                    "fullsize_url": "",
                    "thumbnail_url": "",
                    "challenge_lat": "",
                    "challenge_lng": "",
                }
            ],
        }

        # Make the API request
        url = f"{self.base_url}/peaks/map/challenges/?elev_min=0&elev_max=29500&prom_min=0&prom_max=29500&summits_min=0&summits_max=500&difficulty_min=1&difficulty_max=5&length_min=0&length_max=20&vertical_min=0&vertical_max=10000&last_climbed_min=0&last_climbed_max=11&classics=false&in_challenge=false&you_climbed=false&has_gps=false&bounds=-85.05112899999989,-106.56236049272928,85.05112877980659,272.6947028347678&zoom=0.3944626741523261&near=false&sort=popular"
        response = requests.get(url, headers=self.headers)

        # Assert the response
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.headers["Content-Type"], "application/json")

        data = response.json()

        self.assertIn("challenges", data)
        self.assertIsInstance(data["challenges"], list)

        for challenge in data["challenges"]:
            for field in json_return_object["challenges"][0]:
                self.assertIn(field, challenge)

    def test_mobile_peaks_map(self):
        # Make the API request with &sort=popular
        url = f"{self.base_url}/mobile/peaks/map/?bounds=-48.34112327379494,-148.12167800566888,79.99665329501238,-22.36537147131017&sort=popular"
        response = requests.get(url, headers=self.headers)
        assert_mobile_peaks_map(self, response)

        # Make the API request without sorting
        url = f"{self.base_url}/mobile/peaks/map/?bounds=-48.34112327379494,-148.12167800566888,79.99665329501238,-22.36537147131017"
        response = requests.get(url, headers=self.headers)
        assert_mobile_peaks_map(self, response)


def assert_mobile_peaks_map(self, response):
    json_return_object = {
        "status": "",
        "message": "",
        "data": {
            "parameters": [
                {
                    "bounds": "",
                    "elev_min": "",
                    "elev_max": "",
                    "prom_min": "",
                    "prom_max": "",
                    "last_climbed": "",
                    "summits_min": "",
                    "summits_max": "",
                    "difficulty_min": "",
                    "difficulty_max": "",
                    "route_distance_min": "",
                    "route_distance_max": "",
                    "vertical_gain_min": "",
                    "vertical_gain_max": ""
                }
            ],
            "peaks": [
                {
                    "id": "",
                    "name": "",
                    "slug": "",
                    "is_classic": "",
                    "lat": "",
                    "lng": "",
                    "peak_highlights": "",
                    "summit_count": "",
                    "elevation": "",
                    "prominence": "",
                    "miles_away": "",
                    "your_summits": "",
                    "your_attempts": "",
                    "challenge_count": "",
                    "thumbnail_url": "",
                    "region": [
                        {
                            "region_id": "",
                            "region_name": "",
                            "region_slug": "",
                            "country_name": "",
                        }
                    ],
                    "country": [
                        {
                            "country_id": "",
                            "country_name": "",
                            "country_slug": "",
                        }
                    ],
                    "group": []
                }]
        }
    }
    # Assert the response
    self.assertEqual(response.status_code, 200)
    self.assertEqual(response.headers["Content-Type"], "text/html; charset=utf-8")

    data = response.json()

    self.assertIn("status", data)
    self.assertEqual("success", data['status'])

    self.assertIn("message", data)
    self.assertEqual("OK", data['message'])

    self.assertIn("data", data)

    data = data['data']
    self.assertIsInstance(data["parameters"], list)
    self.assertIsInstance(data["peaks"], list)

    parameters = data["parameters"][0]
    for field in json_return_object["data"]["parameters"][0]:
        self.assertIn(field, parameters)

    for peak in data["peaks"]:
        for field in json_return_object["data"]["peaks"][0]:
            self.assertIn(field, peak)


if __name__ == "__main__":
    unittest.main()
