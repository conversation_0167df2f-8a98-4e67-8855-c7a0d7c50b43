/.DS_Store
/.idea
/db.sqlite3
.DS_Store
/peakery/.idea/misc.xml
/peakery/.idea/modules.xml
/peakery/.idea/peakery.iml
/peakery/.idea/vcs.xml
/peakery/.idea/workspace.xml
/peakery/explore/.DS_Store
/peakery/explore/templates/.DS_Store
/peakery/explore/templates/explore/.DS_Store
/peakery/favorites/.DS_Store
/peakery/ip2geo/.DS_Store
/peakery/items/.DS_Store
/peakery/items/management/.DS_Store
/peakery/items/templates/.DS_Store
/peakery/items/templates/items/.DS_Store
/peakery/main/.DS_Store
/peakery/notification/.DS_Store
/peakery/static/.DS_Store
/peakery/static/js/.DS_Store
/peakery/tempitems/.DS_Store
/peakery/templates/.DS_Store
peakery-venv/
peakery/manage.py
peakery/src/django-follow-master/django_follow.egg-info/top_level.txt
peakery/src/django-follow-master/django_follow.egg-info/SOURCES.txt
peakery/src/django-follow-master/django_follow.egg-info/PKG-INFO
peakery/src/django-follow-master/django_follow.egg-info/not-zip-safe
peakery/src/django-follow-master/django_follow.egg-info/dependency_links.txt
peakery/manage.py
peakery/local_settings.py.sample
peakery/peakery/manage.py
peakery/peakery/local_settings.py.sample
*.pyc
__pycache__/
/docker/local-database/postgres-persistence/
/docker/local-database/redis/
/peakery/static_minified
