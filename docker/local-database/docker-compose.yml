---
version: '3.9'
services:
  peakery-postgres-db:
    container_name: postgres
    image: postgis/postgis:17-3.4
    environment:
      POSTGRES_DB: d5kj34k6jv91u0
      POSTGRES_USER: u3i0vfudbi1kqu
      POSTGRES_PASSWORD: qwerty
      PGDATA: /data/postgres
    volumes:
      - ./01_create_role.sql:/docker-entrypoint-initdb.d/01_create_role.sql
      - ./postgres-persistence:/data/postgres
    expose:
      - "5432"
    ports:
      - "5432:5432"
    command: -p 5432 -c 'max_connections=200'
    shm_size: 1g # Increase size of shared memory segment so postgres can run vacuum analyze without issues.
  redis:
    image: redis:8.0.2-alpine
    ports:
      - '6379:6379'
    command: redis-server --save 20 1 --loglevel warning
    volumes:
      - ./redis:/data