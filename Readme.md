# Dev environment Setup

### Reference Documentation

1. Clone the repo
   ```sh
   git clone https://github.com/peakery/peakery-web2.git
   ```
2. (Optional) Create a virtual environment called "peakery-venv"
3. Be sure to use a Python 3.11.9 or similar, and pip version 23.3.1 or higher. To install the dependencies run
   ```sh
   pip install -r requirements.txt
   ```
4. There is another dependency not listed in requirements.txt that needs to be installed called GDAL. 

   Installation differs between OS, so you have to find how to install it on your system.

   For Windows OS you can do the following:
   
   * Find the python wheel on libraries_win directory
   * Inside your virtual environment, run pip install {wheelname} - This will install gdal.
   * Go to "peakery-venv/Lib\site-packages\osgeo" and check what gdalxxx.dll you have. In my case I have gdal304.dll
   * Add to your PATH environment variable the full path of the folder that contains that .dll
   * Go to peakery-venv/Lib/site-packages/django/contrib/gis/gdal/libgdal.py and check if the lib_names variable in line 23 contains your gdal version. If not, add it to the list. In my case, I had to add 'gdal304'
   * Restart your IDE if you have it open so it picks up the PATH update. GDAL should be working now.

5. Set the following environment variable in order to tell django to use the development settings:
   > DJANGO_SETTINGS_MODULE=peakery.local_settings

   For the remaining environment variables, reach out to someone in the project
6. Start the server 
   ```sh
    python manage.py runserver
    ```

For MacOS, you might need to install a few other things for the local setup

- Step 2 is mandatory
- Before running step 3, run brew install postgresql
- For step 4: brew install gdal

## Heroku Database Connection

Django doesn't provide a database pooling system, so this project relies on an external connection
pooling system known as PGBouncer. Heroku provides a straightforward way of setting it up know as buildpack.

[This documentation](https://devcenter.heroku.com/articles/on-dyno-postgres-connection-pooling) explains how to set it up.
This project deploys PGBouncer on the client side. 
To make this work, an environment variable is required:
```
DATABASE_URL=<db_driver>://<username>:<password>@<host>:<port>/<dbname>
```

If the app runs locally, the app will connect directly to the database defined by DATABASE_URL. 
If the app is deployed on heroku, before the application starts, pgbouncer will connect to the database first, and then
it will replace that environment variable with the pgbouncer connection details, so when the app starts, it connects to pgbouncer.

A few heroku pgbouncer specific environment variables that are relevant are:
- PGBOUNCER_DEFAULT_POOL_SIZE: Equivalent to max pool size. 
- PGBOUNCER_STATS_PERIOD: Interval in seconds to log statistics.
- PGBOUNCER_QUERY_WAIT_TIMEOUT: Determines how long the pooler allows a client waiting for a server connection assignment to wait before returning an error. 


## Local PostgreSQL and Redis Setup

1. Run the docker-compose.yml located at /docker/local-database/. This will start a postgres instance with the postGIS plugin, and run a CREATE ROLE command on startup. It will also start a redis instance.
2. Restore a database dump. The compose file is configured with the same database name and user as PROD, so I suggest restoring a dump from the production database. You can use the command `restore_local_postgres` in the commands folder and pass the dump file as an argument. (This may take a while...)
3. Configure the DATABASE_URL env variable to point to this instance
4. Configure the REDISQUEUE_URL and REDISCLOUD_URL env variable to point to the redis instance

For the local redis worker, you can execute `python manage.py run_redis_worker`

## API Docs
- [Android endpoints](https://peakery.docs.apiary.io/)
- [IOS endpoints](https://peakeryapiv2.docs.apiary.io/)


# Development Notes

To keep the peaks database up to date with peaks, from time to time an import of information from open street maps (osm) is performed.
Scripts related to this can be found in the peakery/items/management/commands/ directory.
There are some temporary tables created for this migration named items_item_osm, items_item_osm_country, items_item_osm_geocode and items_item_osm_region.
These tables **are not used by the application**, but they contain data that might (or might not) be helpful in a future import. 
For context, see [this issue](https://github.com/peakery/peakery-web2/issues/152).  

## Static files handling

All static files can be found on the /static/ directory. 
During development, files should be modified there. Every time we want to see the new changes impact production, run the following commands
- python manage.py collectstatic --clear
- python manage.py minify_static_files
- python manage.py push_static_files_to_s3

These commands will delete all files from STATIC_ROOT (Currently static_minified folder), then copy the files in /static/ to STATIC_ROOT, and then minify them.