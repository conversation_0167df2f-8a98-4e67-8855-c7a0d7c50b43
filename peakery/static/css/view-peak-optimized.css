/* View Peak Optimized CSS */

/* Remove main peak photo styles */
#remove-main-peak-photo {
    color: red;
    text-decoration: none;
}

#remove-main-peak-photo:hover {
    text-decoration: underline;
}

/* Modal and admin styles */
body.modal-open {
    overflow: visible;
}

.desktop-admin-link {
    color: #f24100;
}

/* Admin positioning for large screens */
@media screen and (min-width: 1920px) {
    #admin-stuff {
        position: absolute;
        top: -60px;
        right: 0px;
        z-index: 2;
    }
}

/* Table gadget styles */
table.gadget tr:first-child,
table.gadget tr:last-child {
    display: none !important;
}

/* Navigation styles */
#navbar-link-peaks > a:hover {
    color: #ffffff;
}

/* Stats and data styles */
p.statsleftrank, 
.stats-data-bottom {
    color: #999;
}

/* Mobile styles (max-width: 767px) */
@media screen and (max-width: 767px) {
    #log-your-climb {
        margin-left: 0px;
    }
    
    .stats-data-bottom, 
    .stats-data-highlight {
        margin-left: -6px;
    }
    
    .stats-data-highlight {
        margin-bottom: 5px;
        line-height: 20px;
    }
    
    .stats-data {
        margin-bottom: 5px;
    }
    
    .section-header {
        float: left;
        font-size: 14px;
        font-weight: 500;
    }
    
    .route-card-stats {
        font-size: 12px;
        color: #666;
    }
    
    #content-body {
        margin-top: 20px;
    }
    
    .content-pane {
        margin-top: 50px;
    }
    
    .peak-title {
        font-size: 12px;
        font-weight: 500;
    }
    
    .stats-header {
        font-size: 14px;
        margin-bottom: 10px;
    }
    
    .hero-photo-caption-username,
    .hero-photo-caption-peakname {
        width: 40%;
    }
    
    .featured-logs-thumbnail {
        display: none;
    }
}

/* Tablet styles (768px - 1023px) */
@media screen and (max-width: 1023px) and (min-width: 768px) {
    #log-your-climb {
        margin-left: 20px;
        padding: 15px 10px;
        width: 120px;
    }
    
    .peak-title {
        font-size: 16px;
        font-weight: 600;
    }
    
    .stats-data {
        margin-bottom: 10px;
    }
    
    .section-header {
        float: left;
        font-size: 16px;
        font-weight: 500;
    }
    
    .route-card-stats {
        font-size: 14px;
        color: #666;
    }
    
    .content-pane {
        margin-top: 0px;
    }
    
    .featured-logs-thumbnail {
        float: left;
        width: 100px;
    }
    
    .featured-logs-description {
        margin-left: 170px;
    }
}

/* Desktop styles (min-width: 1024px) */
@media screen and (min-width: 1024px) {
    #log-your-climb {
        margin-left: 50px;
        width: 160px;
    }
    
    .peak-title {
        font-size: 20px;
        font-weight: 600;
    }
    
    .stats-data {
        margin-bottom: 10px;
    }
    
    .section-header {
        float: left;
        font-size: 18px;
        font-weight: 500;
    }
    
    div.description {
        font-size: 16px;
        line-height: 28px;
    }
    
    .route-card-stats {
        font-size: 14px;
        color: #666;
    }
}

/* Map and UI styles */
.gm-style-mtc {
    opacity: .8;
}

/* Edit peak info styles */
#edit-peak-info {
    background-color: #ffece6;
}

#edit-peak-info #edit-peak-info-link {
    color: #f13f01;
}

#edit-peak-info:hover {
    background-color: #f24100;
}

#edit-peak-info:hover #edit-peak-info-link {
    color: #fff;
}

/* Gallery styles */
.blueimp-gallery > .description {
    position: absolute;
    bottom: 0px;
    width: 100%;
    text-align: center;
    color: #fff;
    margin-bottom: 2%;
    height: auto;
    display: none;
}

.blueimp-gallery-controls > .description {
    display: block;
}

.blueimp-gallery-controls > .description > .description-text {
    padding: 10px;
    background: rgba(0, 0, 0, 0.5);
}

/* Stats box styles */
div#stats.newbox {
    margin-top: 0px;
    width: 100%;
}

.sub-header-row {
    border-bottom: none;
}

/* Upload styles */
.qq-upload-list {
    box-shadow: none;
}

.qq-upload-button-selector {
    width: auto;
}

.qq-upload-list li.qq-upload-success {
    background-color: transparent;
    border-bottom: none;
    border-top: none;
}

/* Admin photo delete button */
.admin-delete-photo {
    color: #fff;
    border: solid 2px;
    border-radius: 15px;
    position: absolute;
    right: 7px;
    top: 7px;
    width: 25px;
    padding: 2px;
    padding-left: 5px;
    background: #ccc;
    cursor: pointer;
}

/* Peak SEO card styles */
.peak-seo-card {
    background-color: #fff;
    padding-top: 10px;
    padding-bottom: 10px;
    width: 25%;
}

.peak-seo-card-header {
    font-size: 18px;
    font-weight: 500;
    float: left;
}

.peak-seo-card-subheader {
    float: right;
    color: #999;
    font-size: 12px;
    font-weight: 300;
    line-height: 36px;
}

.peak-seo-info-left {
    font-size: 14px;
    float: left;
    line-height: 2.2em;
}

.peak-seo-info-right {
    float: right;
    color: #999;
    font-size: 12px;
    line-height: 30px;
    font-weight: 300;
}

/* Responsive peak SEO info */
@media screen and (min-width: 1680px) {
    .peak-seo-info-left {
        width: 300px;
    }
}

@media screen and (max-width: 1679px) and (min-width: 1440px) {
    .peak-seo-info-left {
        width: 200px;
    }
}

@media screen and (max-width: 1439px) and (min-width: 1280px) {
    .peak-seo-info-left {
        width: 200px;
    }
}

@media screen and (max-width: 1279px) {
    .peak-seo-info-right {
        display: none;
    }
    
    .peak-seo-info-left {
        width: 200px;
    }
}

/* Hover effects */
.hover-cell:hover, 
.table-hover-cell:hover {
    background-image: linear-gradient(to bottom, #fde1d6, #fde1d6) !important;
}

/* Slideshow styles */
#slideshow1 {
    border-bottom-left-radius: 0px !important;
}
