.navbar {
    border: none !important;
}

.main-header-row {
    border-bottom: none;
    position: absolute;
    top: 0px;
    width: 100%;
    max-width: 1280px;
    margin-left: 0px;
    margin-right: 0px;
}

.fixed-page-header {
    border-bottom: none;
    position: absolute;
    top: 0px;
    width: 100%;
    max-width: 1280px;
    margin-left: 0px;
    margin-right: 0px;
}

input[type=text], input[type=search], input[type=url], input[type=email], input[type=password], .btn, textarea {
    -webkit-border-radius: 12px;
    border-radius: 12px;
}

/* Auto Complete Styles */
.autocomplete-suggestions { border: 1px solid #999; background: #FFF; overflow: auto; }
.autocomplete-suggestion { padding: 10px 10px; white-space: nowrap; overflow: hidden; cursor: pointer;}
.autocomplete-no-suggestion { padding: 10px 10px; white-space: nowrap; overflow: hidden;}
.autocomplete-selected { background: #F0F0F0; }
.autocomplete-suggestions strong { font-weight: 700; color: #00b1f2; }
.autocomplete-group { padding: 2px 5px; }
.autocomplete-group strong { display: block; border-bottom: 1px solid #000; }

@media screen and (min-width: 768px) {
  .autocomplete-suggestions { max-height: 315px !important; }
}

.add-more-info-title {
    font-size: 18px;
    color: #00B1F2;
    letter-spacing: 0.34px;
}