form span.a span.holder {
    margin: 0;
    padding: 0;
    color: #999999;
    cursor: text;
    font-size: 15px;
    font-weight: normal;
    left: 33px;
    position: absolute;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
    top: 4px;
    white-space: nowrap;
    z-index: 1;
    line-height: 35px;
}
form span.a {
    position: relative;
}
form span.a input.email, form span.a.email input {
    background: url("../img/icn/email.png") no-repeat scroll 8px center transparent;
}
form span.a input.userData, form span.a.userData input {
    background: url("../img/icn/user_add.png") no-repeat scroll 8px center transparent;
}
form span.a input.password, form span.a.password input {
    background: url("../img/icn/lock.png") no-repeat scroll 8px center transparent;
}
form span.a {
    background: none repeat scroll 0 0 #FFFFFF;
    display: block;
    margin: 5px 0;
    position: relative;
}
form span.a input {
    border: 2px solid #DDDFE7;
    font-size: 15px;
    padding: 10px 10px 10px 30px;
}
form span.formError {
    display: block;
    color: red;
    font-size: 12px;
    margin: 5px 0;
}


#signInFormContainer {
    padding-top: 20px;
    padding-bottom: 5px;
}
#signInFormContainer a.a {
    display: block;
    margin: 10px 0;
    text-align: center;
}
#signInFormContainer a.a img {

}
#signInFormContainer h2 {
    display: block;
    margin: 0;
    padding: 0;
    text-align: center;
    margin-top: 11px;
}
#signInFormContainer form {
    margin: 0;
    padding:  0px;
    position: relative;
}
#signInFormContainer form span.a {
    background: none repeat scroll 0 0 #FFFFFF;
    display: block;
    margin: 5px 0;
    width: 220px;
    position: relative;
}
#signInFormContainer form span.a input {
    border: 2px solid #DDDFE7;
    font-size: 15px;
    padding: 10px 10px 10px 30px;
    width: 178px;
}
#signInFormContainer form span#forgotPassword {
    display: block;
    text-align: left;
}
#signInFormContainer form span#forgotPassword a {
    color: #444;
    line-height: normal;
    margin: 0;
    padding:  0;
    padding-left: 5px;
}
#signInFormContainer form span.b {
    display: block;
    margin-top: 10px;
    margin-bottom: 5px;
    text-align: center;
}
#signInFormContainer form span.b input {
    font-size: 15px;
    line-height: 25px;
    margin: 10px auto;
    padding: 10px 30px;
    text-align: center;
    text-transform: capitalize;
}