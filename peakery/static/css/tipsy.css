.tipsy { padding: 5px; font-size: 10px; opacity: 0.8; filter: alpha(opacity=80); background-repeat: no-repeat;  background-image: url(../img/tipsy.gif); }
.tipsy-inner { padding: 5px 8px 4px 8px; background-color: black; color: white; max-width: 200px; text-align: center; }
.tipsy-inner { -moz-border-radius:3px; -webkit-border-radius:3px; }
.tipsy-inner span { display: block; }
.tipsy-north { background-position: top center; }
.tipsy-south { background-position: bottom center; }
.tipsy-east { background-position: right center; }
.tipsy-west { background-position: left center; }
