html {
	background:#FFFFFF none repeat scroll 0 0;
	color:#000000;
	font-size:12px;
        
}
body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, form, fieldset, legend, input, button, textarea, p, blockquote, th, td {
	margin:0;
	padding:0;
}
/* tables still need 'cellspacing="0"' in the markup */
table {
	border-collapse:collapse;
	border-spacing:0;
}
fieldset, img, button {
	border:0 none;
}
address, caption, cite, code, dfn, em, th, var, optgroup {
	font-style:inherit;
	font-weight:inherit;
}
del, ins {
	text-decoration:none;
}
ol {
	list-style:decimal;
}
ol li {
	list-style: decimal outside;
}
ul {
	list-style:none
}
caption, th {
	text-align:center;
}
h1, h2, h3, h4, h5, h6 {
	font-size:100%;
	font-weight:normal;
}
:focus {
	outline: 0;
}
q:before, q:after {
	content:'';
}
abbr, acronym {
	border:0 none;
	font-variant:normal;
}
sup {
	vertical-align:text-top;
}
sub {
	vertical-align:text-bottom;
}
legend {
	color:#000;
}
input, button, textarea, select, optgroup, option {
	font-family:inherit;
	font-size:inherit;
	font-style:inherit;
	font-weight:inherit;
}
input, textarea, select {
	
}
input[type="file"] {
  cursor: pointer !important;
}

span.hr{display: block; height: 20px}

@import "reset.css";

.clearfix:after {
  content: ".";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
.fz12 {font-size: 12px;}
.fz13 {font-size: 13px;}
.fz14 {font-size: 14px;}
.fz15 {font-size: 15px;}
.fz16 {font-size: 16px;}
.fz17 {font-size: 17px;}
.fz18 {font-size: 18px;}
.fz19 {font-size: 19px;}
.fz20 {font-size: 20px;}
.fz21 {font-size: 18px;}
.clearfix {
  display: inline-block;
}
.lefted {
  float: left;
}
.lefted2 {
  float: left;
}
.righted {
  float: right;
  overflow: visible !important;
}
.paddingl-1 {
  padding-left: 10px !important;
}
.paddingl-2 {
  padding-left: 20px !important;
}
.paddingl-3 {
  padding-left: 30px !important;
}
.marginr-1 {
  margin-right: 10px;
}
.marginr-2 {
  margin-right: 20px;
}
.marginr-3 {
  margin-right: 30px;
}
.marginr-4 {
  margin-right: 40px;
}
.marginr-5 {
  margin-right: 50px;
}
.marginr-6 {
  margin-right: 60px;
}
.marginr-7 {
  margin-right: 70px;
}
.marginr-8 {
  margin-right: 80px;
}
.marginr-9 {
  margin-right: 90px;
}
.marginr-10 {
  margin-right: 100px;
}
.marginr-11 {
  margin-right: 110px;
}
.marginr-12 {
  margin-right: 120px;
}
.marginb-1 {
  margin-bottom: 10px;
}
.marginb-2 {
  margin-bottom: 20px;
}
.marginb-3 {
  margin-bottom: 30px;
}
.marginb-4 {
  margin-bottom: 40px;
}
nav {
    font-family: Avenir,Trebuchet MS,sans-serif;
}
section {
  display: block !important;
}
.font10px {
  font-size: 10px !important;
}
.font8px {
  font-size: 8px !important;
}
.font11px {
  font-size: 11px !important;
}
.font12px {
  font-size: 12px !important;
}
.font15px {
  font-size: 15px !important;
}
.font16px {
  font-size: 16px !important;
  line-height: 26px;
}

.font18px {
  font-size: 18px !important;
}

.fonttre {
  font-family: Avenir,Trebuchet MS,sans-serif;
}
a {
  color: #00B1F2;
  text-decoration: none;
}
p {
  line-height: 20px;
  margin-bottom: 20px;
}
h1, h2, h3, h4 {
  color: #404040;
  font-size: 21px;
  font-weight: 700;
  margin-bottom: 15px;

  font-family: Avenir,Trebuchet MS,sans-serif;
}
h1 {
  font-size: 24px;
}
h3 {
  font-size: 20px;
}
h4 {
  font-size: 18px;
}
body, html {
  height: 100%;
}
body {
  font-family: Avenir,Trebuchet MS,sans-serif;
}
body.wrapper_set1 .wrapper {
  width: 1160px !important;
}
body.wrapper_set1 .wrapper.full {
  width: 99% !important;
}
body.wrapper_set1 .wrapper.full .col_2 {
  width: 85% !important;
  float: right !important;
}
body.wrapper_set1 .wrapper.full .col_data {
  float: right !important;
  width: 80%;
}
body.wrapper_set1 .wrapper.full .col_search {
  float: left !important;
  width: 20%;
}
body.wrapper_set1 .wrapper.full .col_search form#explore input {
  width: 80% !important;
}
body.wrapper_set1 .wrapper.full .col_search form#explore input.btn {
  width: 70% !important;
}
body.wrapper_set1 footer {
  width: 1160px !important;
}
body.wrapper_set1 footer.full {
  width: 98% !important;
}
.wrapper {
  width: 1200px;
  margin: auto;
}
.wrapper {
  height: auto !important;
  margin: 0 auto -35px;
  min-height: 100%;
  position: relative;
    width: 1200px;
}
footer, .push {
  height: 35px;
}
.wrapper.auto {
  width: auto;
}
header {
  height: 140px;
  position: relative;
  display: block;
  padding: 5px 0 0 0;
}
header .logo {
  float: left;
  height: 90px;
  margin-right: 10px;
  width: 180px;
  position: absolute;
  margin-top: 14px;
}
header .logo a {
  display: block;
  height: 100%;
    color: transparent;
}
header .ads {
  display: inline-block;
  background: none repeat scroll 0 0;
  float: right;
  height: 86px;
  width: 730px;
}
header nav,
footer {
  
  display: block;
  background-color: rgba(239,239,239,1);
  clear: both;
  font-size: 16px;
  height: 44px;
  box-shadow: 0px 1px 1px #ccc;
}

/*  header nav */
header nav {
  margin: 10px 0;
  width: 100%;
    position: relative;
}
header nav ul {
}
header nav ul li.search {
  padding-left: 5px;
    float: right;
    position: relative;
    height: 35px;
}
header nav ul li.search span.a {
    opacity: 0.6;
    overflow: visible;
    margin-right:10px;
}
header nav ul li.search span.a span.holder {
    font-size: 12px;
    line-height: 16px;
    left: 12px;
    color: #888;

}
header nav ul li.search span.a.searchHover {
    opacity: 1 !important;
    box-shadow: 0 0 3px #FFFFFF;
}
header nav ul li.search span.a.searchHover span.holder {
    color: #888 !important;
    overflow: visible;
}
header nav ul.lefted {
position: relative;
top: 12px;
}
header nav ul.lefted li,
header nav ul.righted li {
  margin-right: 10px;
}
header nav ul.righted {
  margin-right: -14px;
  height: 35px;
  position: relative;
  top: 4px;
}
header nav ul.righted li {
}
header nav a {
  color: #333;
  display: block;
  height: 100%;
  padding: 0 40px;
  text-decoration: none;
  position: relative;
  z-index: 6;
}
header nav a.first:hover,
header nav a.first.active {
  transition: all 0.5s ease;
  color: #00B1F2;
}
header nav a:hover, .menu-open {
  color: #00B1F2;
}
header nav a:active {
  color: #00B1F2;
}
header nav a.active {
  color: #00B1F2;
}

header nav a.current {
  color: #30659b;

}
header nav .signUpBtn {
    padding: 6px 10px;
    text-shadow: none;
}
header nav form {
  margin-top: 2px;
}
#main {
  overflow: hidden;
  width: 100%;
}
#mainspace {
  margin-top: 20px;
}
.grayContainer {
    width: 1200px;
    margin-bottom: 40px;
    padding: 30px 0px;
    background-color:#eee;
}
.popular_peaks {
  border-top: 2px solid #D9D9D9;
  padding: 13px 5px;
  overflow: hidden;
  clear: both;
}
.popular_peaks ul.vert.peaks {
  max-width: 250px;
}
.popular_peaks ul.vert {
  float: left;
  color: #7F7F8D;
}
.popular_peaks ul.vert .list_title {
  color: #333333;
  font-weight: 700;
}
.popular_peaks ul.vert li a {
  color: #7F7F8D;
}
.mainbg {
  padding: 10px 20px;
}
.mainbg .backgrounder {
  width: 100%;
  height: 604px;
  position: absolute;
  left: 0px;
  z-index: 0;
}
.mainbg .backgrounder .stretch {
  width: 100%;
  height: 100%;
}
.mainbg h1 {
  padding-top: 0;
  margin-bottom: 0;
  text-align: center;
}

.mainbg h2 {
  text-align: center;
  margin-bottom: 20px;
}
.blue {
  color: #00B1F2;
}
.landing_text {
  position: relative;
  margin-bottom: 70px;
  width: 100%;
}
.landing_text .wrap {
  width: 730px;
  margin: 0px auto 40px auto;
  overflow: hidden;
  padding: 15px 35px;
  height: 180px;
  background-color: rgba(230,230,230,0.4);
  color: rgba(230,230,230,0.6);
  margin-top: 130px;
  -webkit-backdrop-filter: brightness(1.5) blur(5px); 
  backdrop-filter: brightness(1.5) blur(5px);
}
.landing_text .wrap h2.find {
  color: #333;
  text-shadow: 0 1px 1px #fff;
  margin: 7px 10px 10px 0 !important;
  float: left;
}
.landing_text .wrap .bar {
  margin: 0 !important;
}



/* homepage stuff - reorg so all goes in here */

form#homesearch .fieldset {
  float: left;
  display: inline-block;
  overflow: hidden;
}
form#homesearch .fieldset.lefted {
  width: 250px;
}
form#homesearch .fieldset.lefted:last-child {
  margin-right: 10px;
}
form#homesearch .fieldset.centerCol {
  margin: 16px 18px 0px 16px;
}
form#homesearch .fieldset.centerCol p {
  color: #666;
  font-size: 16px;
  font-weight: bold;
  text-transform: uppercase;
}
form#homesearch .fieldset:last-child {
  float: right !important;
}
#homesearch {
  margin-top: 20px;
}
#hpTwitterFollowButton {
  position: relative;
  top: 98px;
  left: 120px;
}
#hpFacebookLikeButton {
  position: relative;
  top: 120px;
}
#bgPhotoInfo {
  text-align: right;
  position: relative;
  top: 68px;
  color: white;
  font-size: 12px;
  line-height: 21px;
}



.box {
  background: #F2F2F2;
  border: 1px solid #DFDFDF;
  -webkit-box-shadow: 0 1px 3px 1px #9A9898;
}
.box2 {
  background: #F2F2F2;
  border: 1px solid #DFDFDF;
  -moz-box-shadow: 0 1px 3px 1px #9A9898;
  -webkit-box-shadow: 0 1px 3px 1px #9A9898;
}
.box h2 {
}
.box h2 a {
  text-decoration: none;
  color: inherit;
}
.box div {
  text-align: right;
}
.box div a {
  color: #404040;
  font-size: 16px;
  text-decoration: none;
}
.box.bar {
  height: 57px;
  padding: 0;
  margin-bottom: 20px;
}
.box.boxset1 {
  float: left;
  margin-left: 2px;
  margin-right: 48px;
  margin-top: 10px;
  padding: 22px 38px;
  width: 230px;
}
.box.boxset1 .header {
  overflow: hidden;
}
.box.boxset1 .header h2 {
  font-size: 18px !important;
}
.box.boxset1 .header span {
  float: right;
}
.homeblock {
  overflow: hidden;
  margin-top: 30px;
}
.home_box {
  background: #F2F2F2;
  border: 1px solid #DFDFDF;
  float: left;
  width: 362px;
  margin-right: 54px;
  margin-bottom: 30px;
}
.home_box .header {
  background: #254061;
  padding: 10px;
  overflow: hidden;
  margin-bottom: 15px;
  border: 1px solid #254061;
}
.home_box .header h2 {
  text-transform: uppercase;
  font-size: 18px;
  font-weight: 500;
  float: left;
  margin: 0;
}
.home_box .header h2 a {
  color: white;
  text-decoration: none;
}
.home_box .header span {
  float: right;
}
.home_box .header span a {
  padding-top: 3px;
  font-size: 12px;
  text-decoration: none !important;
  color: white !important;
  display: block;
}
.home_box .content {
  width: 320px;
  margin: auto;
}
.home_box .content.user {
  width: 200px;
    margin-left: 80px !important;
}
.box.boxset1 ul.activepeakbaggers {
  padding-left: 20px;
}
form.gray_inputs {
  overflow: hidden;
}
form.gray_inputs input.text {
  opacity: 0.8;
  padding-bottom: 8px;
  padding-top: 8px;
  font-size: 22px;
  width: 250px;
  margin-bottom: 5px !important;
  color: black;
}
.landing_text form.gray_inputs input.text {
  margin-right: 20px;
}
.wset1 {
  width: 988px;
  padding-left: 6px;
}
.wset2 {
  width: 300px;
}
.region_filter {
  width: 340px;
  float: right;
  overflow: hidden;

  padding: 15px;
  background-color: rgba(230,230,230,0.7);
  color: rgba(230,230,230,0.7);
}
.region_filter #worldmap {
  margin-bottom: 0px !important;
}
.region_filter div.title {
  height: 20px;
  color: #404040;
  font-size: 16px;
  text-shadow: 0 1px 1px #fff;
  font-weight: bold;
  margin-bottom: 17px;
  margin-top: 5px;
}

.likebox {
  bottom: 0;
  position: absolute;
  right: 56px;
}
.block {
  overflow: hidden;
  margin-bottom: 30px;
  padding-bottom: 10px;
}
.block h3 {
  font-size: 16px;
  margin-bottom: 5px;
}
.block p {
  color: #404040;
  line-height: 18px;
}
.block li {
  color: #404040;
}
ul.peaks_list1 {
}
ul.peaks_list1 li {
  margin-bottom: 30px;
}
ul.peaks_list1 li.item {
  min-height: 288px;
}
ul.peaks_list1 li h3 a {
  text-decoration: none;
  color: #404040;
}
ul.peaks_list1 li img {
  -moz-box-shadow: 2px 3px 5px #767676;
  -webkit-box-shadow: 2px 3px 5px #767676;
  margin-bottom: 10px;
  display: block;
}
ul.peaks_list1 li .info {
  color: #7F8EB7;
}
ul.peaks_list1 li .info li {
  margin-bottom: 6px !important;
}
.content_l {
  float: left;
  margin-left: 10px;
  width: 768px;
}
.content_r {
  float: right;
  width: 380px;
  margin-bottom: 15px;
}
.content_r.profile h1 {
  font-size: 18px;
}
.content_r #register {
  padding: 25px 0px;
}
.content_r ul.nearby_peaks {
  padding-top: 10px;
}
.content_r div.ad {
    z-index: 1 !important;
    position: relative;
    top: 10px;
    left: 35px;
}
.progressbar {
  border: 1px solid #9F9F9F;
  height: 17px;
}
.progressbar_set1 {
  margin-bottom: 20px;
}
.progressbar_set1 .progressbar {
  float: left;
  margin-right: 10px;
  width: 50%;
}
.progressbar_set1 .extra_info {
  display: inline-block;
  font-size: 14px;
}
.progressbar .progress {
  background: -webkit-gradient( linear, left bottom, left top, color-stop(0.17,rgb(67,120,18)), color-stop(0.49,rgb(83,154,25)), color-stop(0.81,rgb(114,188,50)) );
  background: -moz-linear-gradient( center bottom, rgb(67,120,18) 17%, rgb(83,154,25) 49%, rgb(114,188,50) 81% );
  height: 100%;
}
.progressbar .progress.blue {
  background: -webkit-gradient( linear, left bottom, left top, color-stop(0.17,#002FAA), color-stop(0.49,#002FAA), color-stop(0.81,#06F) );
  background: -moz-linear-gradient( center bottom, #002FAA 17%, #002FAA 49%, #06F 81% );
  height: 100%;
}
.extra_info {
  color: #B6BBD3;
  font-size: 10px;
  font-style: italic;
  display: block;
  text-transform: lowercase;
}
.extra_info.inline {
  display: inline-block;
}
.user_dashboard {
  margin-bottom: 15px;
  overflow: hidden;
}
.user_dashboard .avatar {
  float: left;
  margin-right: 21px;
}
.dashboard_wrapper {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid #e6e6e6;
  background: #F2F2F2;
  background: -moz-linear-gradient(top,#F2F2F2 0%,#FFF 100%);
  background: -webkit-gradient(linear,left top,left bottom,color-stop(0%,#F2F2F2),color-stop(100%,#FFF));
}
.user_dashboard .dashboard_box {
  float: left;
  width: 586px;
}
.user_dashboard .dashboard_box h2 {
  font-size: 24px;
  margin-bottom: 4px;
}
.user_dashboard .dashboard_box p {
  color: #837F7F;
  font-size: 14px;
  margin-bottom: 5px;
  padding-bottom: 10px;
}
.user_dashboard .dashboard_box p a {
  text-decoration: none;
  color: #837F7F;
}
.user_dashboard .dashboard_box p a:hover {
  text-decoration: underline;
}
.user_dashboard .dashboard_box div {
  overflow: hidden;
}
.user_dashboard .dashboard_box div h2 {
  float: left;
  width: 60%;
}
.user_dashboard .dashboard_box div div {
  float: right;
  width: 40%;
  text-align: right;
}
.user_dashboard .dashboard_box div div a {
}
.user_dashboard .dashboard_box .user_stats {
  height: 64px;
}
.user_dashboard .dashboard_box .user_stats li {
  border-right: 1px solid #D9D9D9;
  height: 49px;
  margin-right: 23px;
  padding-right: 10px;
}
.user_stats li.last {
  margin-right: 0 !important;
  border-right: none !important;
}
.user_dashboard .dashboard_box .user_stats li span {
  color: #333;
  font-size: 14px;
}
.user_dashboard .dashboard_box .user_stats li .value {
  color: #00B1F2;
  display: block;
  font-size: 33px;
  font-weight: bold;
}
.viewfullmap {
  left: 90px;
  position: absolute;
  top: 678px;
  padding: 5px 20px 5px 45px;
  filter: alpha(opacity=70);
  -moz-opacity: 0.7;
  khtml-opacity: 0.7;
  opacity: 0.7;
}
.viewfullmap a {
  text-decoration: none;
  color: inherit;
}
ul.comments {
}
ul.comments li {
  overflow: hidden;
  margin-bottom: 10px;
  padding-bottom: 10px;
    padding: 0;
}
ul.comments li .image {
  float: left;
  margin-right: 20px;
  width: 78px;
  text-align: center;
}
ul.comments li .image_desc {
  display: block;
  font-size: 10px;
  text-align: center;
  font-weight: 700;
}
ul.comments li .image img {
  margin-bottom: 5px;
  -webkit-box-shadow: 2px 2px 2px #999;
  -moz-box-shadow: 2px 2px 2px #999;
  box-shadow: 2px 2px 2px #999;
}
ul.comments li .comment_box {
  border: 1px solid #D3D3D3;
  background: none repeat scroll 0 0 #F9F9F9;
  float: left;
  padding: 8px 16px;
  position: relative;
  width: 632px;
}
ul.comments li .comment_box.blue_graded {
  background-image: -webkit-gradient(		 linear,		 left bottom,		 left top,		 color-stop(0.09,#D9D9D9),		 color-stop(0.54,#E6E6E6),		color-stop(0.87,#F2F2F2)		);
  background-image: -moz-linear-gradient(		 center bottom,		 #D9D9D9 9%,		 #E6E6E6 54%,		 #F2F2F2 87%		 );
  padding: 19px 16px;
}
ul.comments li .comment_box.blue_graded h2 {
  color: #000;
  font-size: 26px;
  margin-bottom: 8px;
}
ul.comments li .comment_box.blue_graded p {
  font-size: 15px;
  margin-bottom: 8px;
}
ul.comments li .comment_box.blue_graded .btn {
  margin-bottom: 10px;
}
ul.comments li .comment_box .arrow {
  height: 12px;
  left: -8px;
  position: absolute;
  top: 10px;
  width: 8px;
}
ul.comments li .comment_box .summit_photo {
  float: right;
  width: 215px;
  margin-left: 15px;
  text-align: right;
  overflow: hidden;
  padding-top: 10px;
}
ul.comments li .comment_box .summit_options {
  float: right;
  width: 320px;
  overflow: hidden;
  text-align: right;
}
ul.comments li .comment_box .summit_options span {
  display: inline-block;
  vertical-align: middle;
}
ul.comments li .comment_box .summit_options span a {
  text-decoration: none;
}
ul.comments li .comment_box .comment_meta {
  display: block;
  margin-bottom: 10px;
}
ul.comments li .comment_box .comment_meta a {
  padding-bottom: 10px;
  display: inline-block;
}
ul.comments li .comment_box .edit {
  display: inline-block;
  height: 24px;
  margin-right: 5px;
  vertical-align: middle;
  width: 20px;
}
ul.comments li .comment_box .comment_text {
  line-height: 20px;
}
ul.comments li .comment_box .comment_area {
  line-height: 20px;
  margin: 0 !important;
}
ul.comments li .comment_box .comment_area span {
  margin: 20px;
  display: block;
  text-align: center;
}
ul.comments li .comment_box .comment_area textarea {
  border: none !important;
  width: 100%;
  height: 60px;
  background: transparent;
  font-size: 14px;
  -webkit-box-shadow: 0px 0px 0px;
  -moz-box-shadow: 0px 0px 0px;
  box-shadow: 0px 0px 0px;
}
ul.comments li#comment_form .controls {
  text-align: right;
  clear: both;
  padding: 10px 0px;
}
form#add-comment input.btn {
  padding-bottom: 4px !important;
}
div.summitlogs {
  overflow: hidden;
  background: #F2F2F2;
  padding: 10px 12px;
  border: 1px solid #BFBFBF;
}
div.summitlogs h2.title {
  margin: -10px -12px 10px !important;
  background: #254061;
  color: white;
  padding: 10px 0 6px 13px;
  text-transform: uppercase;
  margin-bottom: 0px;
  font-weight: 700;
  font-size: 18px;
}
div.summitlogs h2.title span {
  color: white;
  text-transform: lowercase;
  font-size: 12px;
  font-weight: normal;
}
div.summitlogs h2.title span.linked {
  float: right;
  padding-right: 10px;
}
div.summitlogs h2.title span.linked a {
  text-decoration: underline;
  color: inherit;
}
div.summitlogs .submittext {
  font-size: 14px;
  line-height: 24px;
}
div.summitlogs div.action {
  overflow: hidden;
}
div.summitlogs div.action .a {
  float: left;
  width: 200px;
}
div.summitlogs div.action .a .peakimg {
  border: 9px solid white;
  position: relative;
  margin: auto auto 15px;
  width: 160px;
  height: 150px;
  -moz-border-radius-topleft: 130px;
  -moz-border-radius-topright: 130px;
  -moz-border-radius-bottomright: 35px;
  -moz-border-radius-bottomleft: 35px;
  border-top-left-radius: 130px;
  border-top-right-radius: 130px;
  border-bottom-right-radius: 35px;
  border-bottom-left-radius: 35px;
  -moz-box-shadow: 0 0 6px #222;
  -webkit-box-shadow: 0 0 6px #222;
    box-shadow: 0 0 6px #222;
  margin-top: 2px;
}
div.summitlogs div.action .a .peakimg img {
  width: 100%;
}

div.summitlogs div.action .a .peakimg .peakname {
  background: none repeat scroll 0 0 #30659b;
  bottom: -1px;
  color: #FFF;
  display: block;
  position: absolute;
  text-align: center;
  margin-bottom: 0;
  width: 100%;
  padding: 6px 0;
  -moz-border-radius-topleft: 0px;
  -moz-border-radius-topright: 0px;
  -moz-border-radius-bottomright: 16px;
  -moz-border-radius-bottomleft: 16px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  border-bottom-right-radius: 16px;
  border-bottom-left-radius: 16px;
}
div.summitlogs div.action .a .peakimg .name {
  display: block;
  font-size: 14px;
  font-weight: 700;
  text-shadow: 1px 1px 1px #666;
}
div.summitlogs div.action .b {
  float: right;
  width: 350px;
  padding: 22px 20px 0 0;
}
div.summitlogs div.action .b h1 {
  font-size: 28px;
  margin-bottom: 5px;
}
div.summitlogs div.summitlog {
  background: white;
  margin-bottom: 10px;
  overflow: hidden;
  padding: 10px;
    margin-top: 10px;
}
div.summitlogs div.summitlog .info-elevation {
}
div.summitlogs div.summitlog .info {
  float: left;
}
div.summitlogs div.summitlog .info .user-meta {
  overflow: visible;
  margin-bottom: 25px;
}
div.summitlogs div.summitlog .info .user-meta a.user-avatar {
  float: left;
  margin-right: 10px;
  padding-bottom: 3px;
}
div.summitlogs div.summitlog .info .user-meta a.user-avatar img {
  -webkit-box-shadow: 2px 2px 2px #b3b3b3;
  -moz-box-shadow: 2px 2px 2px #b3b3b3;
  box-shadow: 2px 2px 2px #b3b3b3;
}
div.summitlogs div.summitlog .info .user-meta h4 {
  float: left;
  width: 315px;
  margin: 0;
  padding: 5px 0px;
}
div.summitlogs div.summitlog .info .user-meta h4 a {
  text-decoration: none;
  color: inherit;
}
div.summitlogs div.summitlog .info .user-meta p {
  float: left;
  margin: 0;
  width: 315px;
}
div.summitlogs div.summitlog .info .user-meta ul.options {
  float: left;
  margin: 0;
  width: 315px;
}
div.summitlogs div.summitlog .info .user-meta ul.options li {
  float: right;
  padding-right: 10px;
}
div.summitlogs div.summitlog .info .user-meta ul.options li.text {
  padding-top: 2px;
}
div.summitlogs div.summitlog .info .user-meta ul.options li.text.share {
  padding-top: 5px;
  padding-left: 40px;
}
div.summitlogs div.summitlog .info .user-meta ul.options li.text a {
  text-decoration: none;
}
div.summitlogs div.summitlog .info .user-meta ul.options li.text a span.edit {
  padding-top: 3px;
  height: 20px;
  display: inline-block;
  padding-left: 25px;
}
div.summitlogs div.summitlog .info .user-meta ul.options li.icon {
}
div.summitlogs div.summitlog .info .log {
  font-size: 12px;
  line-height: 22px;
}
div.summitlogs div.summitlog .info .photos {
  float: right;
  width: 215px;
  padding-left: 10px;
}
div.summitlogs div.summitlog .info .photos ul {
  float: right;
  width: 215px;
}
div.summitlogs div.summitlog .info .photos ul li span {
  font-size: 11px;
}
div.summitlogs div.summitlog .info .photos ul li img {
  display: block;
}
div.newbox {
  background: -moz-linear-gradient(top,#F2F2F2 0%,#FFF 100%);
  background: -webkit-gradient(linear,left top,left bottom,color-stop(0%,#F2F2F2),color-stop(100%,#FFF));
  border: 1px solid #D3D3D3;
}
div.newbox.gray {
  background: #f1f2f3 !important;
}
div.newbox h2 {
  background: #254061;
  color: white;
  padding: 8px 0px 6px 13px;
  text-transform: uppercase;
  margin-bottom: 0px;
  font-weight: 700;
  font-size: 18px;
}
div.newbox h2.medium {
  font-size: 18px;
}
div.newbox h2 span {
  color: white;
  text-transform: lowercase;
  font-size: 13px;
  font-weight: normal;
    float: right;
    margin-right: 15px;
    text-align: right;
}
div.newbox h2 span.linked {
  float: right;
  padding-right: 10px;
}
div.newbox h2 span.linked a {
  text-decoration: none;
  color: inherit;
}
div.newbox div.content {
  padding: 10px;
}
div.newbox.shadow {
}
div.newbox div.btnbox {
  text-align: center;
  padding-bottom: 20px;
}
div.newbox div.stats p {
  margin-bottom: 10px;
}
div.newbox div.stats p.nomargin {
  margin-bottom: 0px;
}
div.newbox div.stats p strong {
  padding-right: 20px;
}
div#stats.newbox {
  margin-top: 35px;
  width: 370px;
}
div#stats.newbox a {
  text-decoration: none;
  color: inherit;
}
div#stats.newbox a.map3d {
  text-decoration: none;
  color: inherit;
}
div#stats.newbox a:hover {
  text-decoration: underline;
}
div#stats.newbox div.bagged-by {
  overflow: hidden;
  padding-top: 10px;
}
div#stats.newbox div.bagged-by div {
  overflow: hidden;
}
div#stats.newbox div.bagged-by div .l {
  float: left;
  font-weight: bold;
}
div#stats.newbox div.bagged-by div .r {
  float: right;
}
div#stats.newbox div.bagged-by div .r a {
  text-decoration: none;
  color: inherit;
}
div#stats.newbox div.bagged-by a.nobaggers {
  margin-top: 15px;
  display: block;
  text-align: center;
}
span.statsleft {
  float: left;
  width: 40%;
}
span.statsleftrank {
  float: left;
  margin-bottom: 0px;
}
p.statsleftrank {
  margin-bottom: 0px;
}
ul.highest_peaks {
}
ul.highest_peaks li {
  overflow: hidden;
}
ul.highest_peaks li img {
  float: left;
  margin-right: 10px;
  -webkit-box-shadow: 0px 1px 3px #222;
  -moz-box-shadow: 0px 1px 3px #222;
  box-shadow: 0px 1px 3px #222;
}
ul.highest_peaks li a {
  text-decoration: none;
  font-weight: bold;
  color: #333;
  font-size: 14px;
  display: block;
  padding-top: 7px;
}
ul.highest_peaks li span.d {
  color: #666;
}
div#nearest.newbox {
}
div#nearest.newbox ul {
    width: 295px;
    margin: 0 auto;
}
div#nearest.newbox ul li {
  margin-bottom: 30px;
}
div#nearest.newbox ul li a {
  color: #333;
  text-decoration: none;
  font-size: 14px;
  display: block;
  margin: 10px 0px;
  font-weight: bold;
}
div#nearest.newbox ul li a img {
}
div#nearest.newbox ul li span.numbers {
  display: block;
  overflow: hidden;
}
div#nearest.newbox ul li span.numbers span {
  color: #666;
}
div#nearest.newbox ul li span.numbers span.distance {
  float: right;
}
div#nearest.newbox ul li span.numbers span.elevation {
  float: left;
}
ul.baggers {
  list-style: none;
  overflow: hidden;
}
ul.baggers li {
  float: left;
  overflow: hidden;
  padding: 8px;
}
ul.baggers li a img {
}
ul.latest_comments {
}
ul.latest_comments li {
  margin-bottom: 20px;
  overflow: hidden;
}
ul.latest_comments li .image {
  float: left;
  margin-right: 7px;
  width: 84px;
}
ul.latest_comments li .comment_text {
  overflow: hidden;
}
ul.latest_comments li .dateinfo {
  font-size: 10px;
  font-style: italic;
  float: right;
  display: block;
}
.bluetext {
  color: #00B1F2;
}
.browntext {
  color: #A96B23;
}
.sep_top {
  border-top: 2px solid #346;
  font-size: 18px;
  margin-bottom: 20px;
  padding-top: 11px;
  width: 430px;
}
.sep_top.photos {
  border-top-width: 0;
}
.sep_top span.gris {
  color: #999;
}
.sep_top .entriesnum {
  color: #8C8D8F;
  display: inline;
  font-size: 13px;
  font-style: italic;
}
.gray_box {
  background: #F2F2F2;
  background: -moz-linear-gradient(top,#F2F2F2 0%,#FFF 100%);
  background: -webkit-gradient(linear,left top,left bottom,color-stop(0%,#F2F2F2),color-stop(100%,#FFF));
  border: 1px solid #E6E6E6;
  margin-bottom: 20px;
  margin-right: 6px;
}
.gray_box h1 {
  background: none repeat scroll 0 0 #254061;
  margin-left: -2px;
  margin-right: -1px;
  color: #FFF;
  padding: 5px 14px;
}
.gray_box h1 a.edit_btn {
  color: white;
  float: right;
  font-size: 15px;
  line-height: 20px;
    text-transform: lowercase;
}
.gray_box .content {
  padding: 0 15px;
}
.gray_box .block {
  margin-bottom: 30px;
}
.gray_box h3 {
  text-transform: capitalize;
    color: #333;
    font-weight: bold;
    font-size: 16px;
    margin: 20px 0px 8px 0px;
}
.gray_box a {
  color: #404040;
}
.gray_box a {
  color: #404040;
}
.gray_box.box_set1 {
  width: 300px;
  padding: 20px;
  font-size: 16px;
}
.tabs_content {
  background: none repeat scroll 0 0 #F2F2F2;
  border: 1px solid #CFCFCF;
  margin-bottom: 30px;
}
.wait_text {
  font-size: 10px;
  padding: 5px 0 0 0;
  display: block;
}
.tabs_content .centered ul {
  position: relative;
  left: 20px;
}

/* summit badges */
.summit_badges {
  overflow: hidden;
}
.summit_badges li {
  float: left;
    width: 170px;
    margin: 15px 31px;
}
.summit_badges li > a {
  box-shadow: 3px 3px 3px #b0b0b0;
    -moz-box-shadow: 3px 3px 3px #b0b0b0;
    -webkit-box-shadow: 3px 3px 3px #b0b0b0;
    border-radius: 68px 29px 14px 14px;
    -moz-border-radius: 68px 29px 14px 14px;
    -webkit-border-radius: 68px 29px 14px 14px;
  background: none repeat scroll 0 0 #FFF;
  color: #FFF;
  display: block;
  padding-bottom: 40px;
  padding-top: 11px;
  position: relative;
  text-decoration: none;
}
.summit_badges li > a.withoutNumber {
    border-radius: 68px 68px 14px 14px;
    -moz-border-radius: 68px 68px 14px 14px;
    -webkit-border-radius: 68px 68px 14px 14px;
}
.summit_badges li >a img {
  display: block;
  margin: 0 auto;
  width: 147px;
}
.summit_badges li >a:hover .peak {
  background: #254061;
}
.summit_badges li .thumb {
  min-height: 110px;
  width: 147px;
  margin-left: 11px;
  -moz-border-radius-topleft: 60px;
  -moz-border-radius-topright: 60px;
  -moz-border-radius-bottomright: px;
  -moz-border-radius-bottomleft: px;
  border-top-left-radius: 60px;
  border-top-right-radius: 60px;
  border-bottom-right-radius: px;
  border-bottom-left-radius: px;
  -moz-box-shadow: 0px 1px 4px 0px #333 inset;
  box-shadow: 0px 1px 4px 0px #333 inset;
}
.summit_badges li .peak {
  background: none repeat scroll 0 0 #30659b;
  bottom: 8px;
  display: block;
  font-size: 12px;
  left: 11px;
  padding-bottom: 2px;
  padding-top: 2px;
  position: absolute;
  text-align: center;
  text-decoration: none;
  width: 147px;
  -webkit-border-bottom-right-radius: 10px;
  -webkit-border-bottom-left-radius: 10px;
  -moz-border-radius-bottomright: 10px;
  -moz-border-radius-bottomleft: 10px;
  border-bottom-right-radius: 10px;
  border-bottom-left-radius: 10px;
}
.summit_badges li .item_name {
  display: block;
  margin-bottom: 4px;
  font-family: Avenir,Trebuchet MS,sans-serif;
  font-size: 12px;
}
.summit_badges li .item_info {
  font-size: 9px;
  font-weight: normal;
}
.tabs_content .seemore {
  color: #000;
  display: block;
  font-size: 17px;
  font-weight: 700;
  text-align: center;
  text-decoration: none;
  height: 20px;
}


.seemore {
  color: #000;
  display: block;
  font-size: 17px;
  font-weight: 700;
  text-align: center;
  text-decoration: none;
  height: 20px;
}

.tabs_content .seemore:hover {
  text-decoration: underline;
}
ul.view_by {
  margin-bottom: 20px;
    display: block;
}
ul.view_by li {
    display: inline-block;
    float: left;
    margin-right: 10px;
}
ul.view_by li:first-child {
  font-size: 15px;
  line-height: 28px;
  margin-right: 13px;
}
ul.view_by li a {
  display: block;
  padding: 6px 10px;
  text-decoration: none;
    font-size: 14px;
}
ul.view_by li a:hover,
ul.view_by li a.current {
  background: none repeat scroll 0 0 #00b1f2;
  color: #FFF;
  display: block;
}
ul.white_boxes_list {
}
ul.white_boxes_list >li {
  -moz-box-shadow: 0 1px 4px 1px #AAA;
  -webkit-box-shadow: 0 1px 4px 1px #AAA;
  background: none repeat scroll 0 0 #FFF;
  overflow: hidden;
  padding: 12px 18px;
  margin-bottom: 20px !important;
}
ul.white_boxes_list li .peak-info {
  float: left;
  width: 55%;
  margin-bottom: 1px;
}
ul.white_boxes_list li .peak-info a {
  float: left;
  margin-right: 10px;
}
ul.white_boxes_list li .peak-info a img {
}
ul.white_boxes_list li .peak-info p {
  color: black;
  float: left;
  font-family: Avenir,Trebuchet MS,sans-serif;
  font-size: 17px;
  width: 60%;
  line-height: 17px;
  padding-top: 3px;
}
ul.white_boxes_list li .peak-info p span {
  color: #837F7F;
  display: block;
  font-size: 13px;
}
.peak-info {
  overflow: hidden;
  margin-bottom: 20px;
}
.peak-info a {
  float: left;
  margin-right: 10px;
}
.peak-info a img {
}
.peak-info p {
  color: black;
  float: left;
  font-family: Avenir,Trebuchet MS,sans-serif;
  font-size: 17px;
  width: 60%;
  line-height: 17px;
  padding-top: 3px;
}
.peak-info p span {
  color: #837F7F;
  display: block;
  font-size: 13px;
}
ul.white_boxes_list li .edit-and-share-options {
  float: right;
}
ul.white_boxes_list li .edit-and-share-options ul li,
ul.white_boxes_list li .edit-and-share-options ul li a {
  color: #268031;
  font-size: 13px;
  font-weight: 700;
  line-height: 26px;
}
ul.white_boxes_list li .summit-info {
  clear: both;
  padding-top: 15px;
  overflow: hidden;
}
ul.white_boxes_list li .summit-info p {
  margin-bottom: 0;
}
ul.white_boxes_list li .summit-info a {
    text-decoration: underline;
}
ul.white_boxes_list li .summit-info .summit-photos {
  float: right;
  text-align: center;
  width: 200px;
}
ul.white_boxes_list li .summit-info .summit-photos .desc {
  color: #666;
  display: block;
  font-size: 11px;
  padding-top: 5px;
  text-align: left;
}
.change_avatar {
  display: block;
  height: 92px;
  text-indent: -4000px;
  width: 108px;
}
#edit_profile_form .about_me {
    height: 76px;
    resize: none;
    line-height: 18px;
}
ul.peakery_peak {
}
ul.peakery_peak li {
  margin-bottom: 10px;
  overflow: hidden;
}
ul.peakery_peak li a {
  float: left;
  margin-right: 2%;
  width: 37%;
}
ul.peakery_peak li a img {
}
ul.peakery_peak li p {
  color: #404040;
  float: left;
  font-size: 14px;
  font-weight: bold;
  line-height: 21px;
  padding-top: 2px;
  width: 60%;
}
ul.peakery_peak li p span {
  display: block;
}
.didyousummit {
  background: url('http://peakery.com/static/img/misc/did_you_summit_new.png?9ef19a913a07') no-repeat scroll left top transparent;
  height: 300px;
  top: 10px;
  position: absolute;
  width: 290px;
  z-index: 0;
  text-indent: -9000px;
}
.didyousummit:hover {
  background-position: left bottom;
}
.didyousummitagain {
    background: url('http://peakery.com/static/img/misc/you_summited.png?9ef19a913a07') no-repeat scroll 0 0 transparent;
  height: 300px;
  top: 10px;
  position: absolute;
  width: 290px;
  z-index: 0;
  text-indent: -9000px;
}
.didyousummitagain:hover {
  background-position: left bottom;
}


.didyousummit-map {
  background: url('http://peakery.com/static/img/misc/did_you_summit_bottom.png?63b46d66015a') no-repeat scroll right top transparent;
  height: 261px;
  top: 239px;
  right: 0;
  position: absolute;
  width: 262px;
  z-index: 0;
  text-indent: -9000px;
}
.didyousummit-map:hover {
  background-position: right bottom;
}
.didyousummitagain-map {
    background: url('http://peakery.com/static/img/misc/claimagain-map.png') no-repeat scroll 0 0 transparent;
    height: 300px;
    position: absolute;
    right: 0;
    text-indent: -9000px;
    top: 200px;
    width: 290px;
    z-index: 0;
}
.didyousummit-map.done {
  background: url('http://peakery.com/static/img/misc/s2.png?63b46d66015a') no-repeat scroll 0 0 transparent !important;
}
.didyousummit-map.done:hover {
  background: url('http://peakery.com/static/img/misc/s2.png?63b46d66015a') no-repeat scroll 0 0 transparent !important;
}
span.green-text {
    color:#6b8e23;
}
.peak-info-blog {
  margin-bottom: 0px !important;
}
.peak_title {
  margin: 15px 0px;
  font-size: 28px;
  font-weight: 700;
  font-family: Avenir,Trebuchet MS,sans-serif;
  text-shadow: 0px 2px 2px #bbb;
  filter: dropshadow(color=#bbb,offx=0,offy=2);
}
.peak_name {
  margin: 15px 0px;
  font-size: 14px;
  font-weight: 450;
  font-family: Avenir,Trebuchet MS,sans-serif;
  text-shadow: 0px 2px 2px #bbb;
  filter: dropshadow(color=#bbb,offx=0,offy=2);
}
.peak_name p {
  float: right;
  margin: 0px 0px;
  font-size: 12px;
  font-weight: 350;
  font-family: Avenir,Trebuchet MS,sans-serif;
  text-shadow: 0px 2px 2px #bbb;
  filter: dropshadow(color=#bbb,offx=0,offy=2);
}
.tabbed_box {
  background: #F2F2F2;
  position: relative;
  padding: 10px;
  margin-bottom: 0;
  border-bottom: 0;
  height: 500px;
}
.tabbed_box ul.tabs {
  position: absolute;
  right: -1px;
  top: -34px;
}
.tabbed_box ul.tabs li {
  background: none repeat scroll 0 0 #E3E3E3;
  border: 1px solid #BFBFBF;
  border-bottom: none;
  margin-right: 5px;
  height: 33px;
}
.tabbed_box ul.tabs li:last-child {
  margin-right: 0;
}
.tabbed_box ul.tabs li.current {
  background: #F2F2F2;
}
.tabbed_box ul.tabs li a {
  color: #666;
  display: block;
  font-size: 14px;
  padding: 9px 20px;
  text-decoration: none;
  font-weight: 500px;
}
.tabbed_box ul.tabs li a .icon {
}
.tabbed_box .tabbed_box_content {
}
.tabbed_box .tabbed_box_content div.credit, div.credit {
  position: absolute;
  right: 20px;
  top: 465px;
}
#bigphoto.facebox div.credit {
  top: auto;
  bottom: 30px !important;
}
.tabbed_box .tabbed_box_content div.credit span, div.credit span {
  color: white;
  font-size: 14px;
  text-shadow: 1px 1px 0 black;
}
.tabbed_box .tabbed_box_content div.credit a, div.credit a {
  text-decoration: none;
}
.tabbed_box .tabbed_box_content div.viewfull {
  left: 25px;
  position: absolute;
  top: 470px;
  padding: 5px 20px 5px 45px;
  filter: alpha(opacity=70);
  -moz-opacity: 0.7;
  khtml-opacity: 0.7;
  opacity: 0.7;
}
.tabbed_box .tabbed_box_content div.viewfull a {
  text-decoration: none;
  color: inherit;
}
.tabbed_box .tabbed_box_content span.viewfullmap {
  background: #254061;
  left: 10px;
  position: absolute;
  top: 445px;
  padding: 15px 20px 10px 15px;
  filter: alpha(opacity=90);
  -moz-opacity: 0.9;
  khtml-opacity: 0.9;
  opacity: 0.9;
}
.tabbed_box .tabbed_box_content span.viewfullmap a {
  text-decoration: none;
  color: inherit;
  color: #FFF;
  font-size: 16px;
  font-weight: 700;
}
.peak_desc {
  padding: 2px 10px 10px 10px;
  border-top: 0;
}
.peak_desc p, div.description {
  font-size: 14px;
  line-height: 24px;
  margin-bottom: 30px;
    color: #333;
    padding-top: 8px;
}
#peak_info_container div.description a {
    color: #00B1F2;
    text-decoration: underline;
}
#peak_info_container div.description a:hover {
    text-decoration: none;
}
.peak_desc .desc_footer {
  overflow: hidden;
  margin-bottom: 20px;
}
.peak_desc .desc_footer .links a#error_report {
  text-decoration: none;
}
.peak_desc .desc_footer .links {
  float: right;
  padding-right: 35px;
}
.peak_desc .source_info a {
  color: #8F8B8D;
  display: block;
  font-size: 10px;
  text-decoration: none;
}
.share_box {
  overflow: hidden;
  float: left;
}
.zoom_icn {
  margin-right: 5px;
  vertical-align: middle;
}
#peak-photos.photos {
  margin-left: 5px;
}
#peak-photos.photos li {
  margin-right: 0px !important;
  width: 130px;
  min-height: 150px;
  overflow: hidden;
}
#peak-photos.photos li a {
  display: block;
  text-decoration: none;
}
#peak-photos.photos li a:hover {
  text-decoration: underline;
}
#peak-photos.photos li a img {
}
#peak-photos.photos li span {
  font-size: 10px;
}
#peak-photos.photos li .peak_desc {
  color: #4B4A4A;
  font-size: 11px;
}
#peak_info_container {
  background: #F2F2F2;
  background: -moz-linear-gradient(top,#F2F2F2 0%,#FFF 100%);
  background: -webkit-gradient(linear,left top,left bottom,color-stop(0%,#F2F2F2),color-stop(100%,#FFF));
  margin-bottom: 20px;
  background: #F2F2F2;
  border: 1px solid #BFBFBF;
}
.new_bag {
  overflow: hidden;
}
.new_bag.congrats.set2 {
  width: 540px;
  margin: auto;
}
.new_bag.congrats.set2 h1 {
}
.new_bag.congrats.set2 h2 {
  color: #000;
  font-size: 28px;
}
.new_bag.congrats {
  width: 339px;
  margin: auto;
}
.new_bag.congrats h1 {
  text-align: center;
  font-weight: 700;
  margin-bottom: 15px;
}
.new_bag.congrats .peakimg {
  border: 9px solid white;
  position: relative;
  margin: auto auto 15px;
  width: 233px;
  border-radius: 130px 130px 35px 35px;
  -moz-border-radius: 130px 130px 35px 35px;
  -webkit-border-radius: 130px 130px 35px 35px;
  box-shadow: 0 0 4px #222;
  -moz-box-shadow: 0 0 4px #222;
  -webkit-box-shadow: 0 0 4px #222;
  margin-top: 2px;
    background: #fff;
}
.new_bag.congrats .peakimg.xtimes {
    border-radius: 130px 40px 35px 35px;
  -moz-border-radius: 130px 40px 35px 35px;
  -webkit-border-radius: 130px 40px 35px 35px;
}
.new_bag.congrats .peakimg img {
  width: 100%;
}
.new_bag.congrats .peakimgw {
    border-radius: 130px 130px 40px 40px;
}
.new_bag.congrats .peakimgw span.times {
    font-weight: bold;
    color: green;
    position: absolute;
    top: 8px;
    right: 0;
    font-size: 21px;
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    transform: rotate(45deg);
}
.new_bag.congrats .peakimg .peakname {
  background: none repeat scroll 0 0 #30659b;
  bottom: -1px;
  color: #FFF;
  display: block;
  position: absolute;
  text-align: center;
  margin-bottom: 0;
  width: 100%;
  padding: 6px 0;
  -moz-border-radius-topleft: 0px;
  -moz-border-radius-topright: 0px;
  -moz-border-radius-bottomright: 27px;
  -moz-border-radius-bottomleft: 27px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  border-bottom-right-radius: 27px;
  border-bottom-left-radius: 27px;
}
.new_bag.congrats .peakimg .name {
  display: block;
  font-size: 16px;
  font-weight: 700;
  text-shadow: 1px 1px 1px #666;
}
.new_bag.congrats .peakimg .info {
}
.new_bag.congrats h2 {
  font-size: 14px;
  text-align: center;
}
form.add_form {
  position: inherit !important;
}
form.add_form ul.horz {
  padding-bottom: 2px;
}
form.add_form .tip {
  display: inline-block;
  font-size: 11px;
  font-weight: normal;
  margin-left: 6px;
}
form.add_form label {
  display: block;
  font-weight: 700;
  margin-bottom: 6px;
  font-size: 14px;
}
form.add_form span {
  overflow: hidden;
  display: block;
  padding-bottom: 15px;
}
form.add_form .wset1 {
  width: 215px !important;
}
form.add_form fieldset {
  margin-bottom: 20px;
  overflow: hidden;
}
form.add_form fieldset .col_1 {
  float: left;
  margin-right: 7px;
  width: 224px;
}
form.add_form fieldset .col_2 {
  float: left;
  width: 230px;
}
form.add_form input[type="text"], form.add_form input[type="password"] {
  border: 2px solid #7F7F7F;
  padding: 8px 2px;
  width: 96%;
  font-size: 16px;
}

form.summitlog_form {
  position: inherit !important;
}
form.summitlog_form ul.horz {
  padding-bottom: 2px;
}
form.summitlog_form .tip {
  display: inline-block;
  font-size: 11px;
  font-weight: normal;
  margin-left: 6px;
}
form.summitlog_form label {
  display: block;
  font-weight: 700;
  margin-bottom: 6px;
  font-size: 14px;
}
form.summitlog_form span {
  overflow: hidden;
  /*display: block;*/
  padding-bottom: 15px;
}
form.summitlog_form .wset1 {
  width: 215px !important;
}
form.summitlog_form fieldset {
  margin-bottom: 20px;
  overflow: hidden;
}
form.summitlog_form fieldset .col_1 {
  float: left;
  margin-right: 7px;
  width: 224px;
}
form.summitlog_form fieldset .col_2 {
  float: left;
  width: 230px;
}
form.summitlog_form input[type="text"], form.summitlog_form input[type="password"] {
  border: 2px solid #7F7F7F;
  padding: 8px 2px;
  width: 96%;
  font-size: 16px;
}

.new_bag.add_peak {
  width: 750px !important;
  margin: auto;
}
.new_bag .peakname {
  color: #000;
  margin-bottom: 30px;
  overflow: hidden;
}
.new_bag .peakname .text {
  display: block;
  float: left;
  font-size: 19px;
  line-height: 24px;
  padding-top: 3px;
  padding-left: 15px;
  font-weight: 700;
  width: auto;
}
.new_bag .peakname .text .desc {
  color: #5E5959;
  display: block;
  font-size: 20px;
  display: inline-block;
}
.new_bag .peakname img {
  float: left;
  margin-right: 10px;
  width: 70px;
}
ul.add_photo {
}
ul.add_photo li {
  overflow: hidden;
}
ul.add_photo li .default_pic {
  float: left;
  margin-right: 10px;
}
ul.add_photo li .default_pic img {
}
ul.add_photo li .caption {
  float: left;
  width: 338px;
}
ul.add_photo li .caption textarea {
  height: 72px;
}
.linealign {
  line-height: 34px;
  margin-right: 19px;
}
.peak_info {
  background: none repeat scroll 0 0 #FFF;
  font-family: Avenir,Trebuchet MS,sans-serif;
  overflow: hidden;
  padding: 10px;
  -webkit-box-shadow: 2px 2px 4px #474747;
  -moz-box-shadow: 2px 2px 4px #474747;
  box-shadow: 2px 2px 4px #474747;
  margin-bottom: 10px;
  width: 520px;
}
.peak_info .col_1 {
  width: 309px;
  float: left;
  margin-right: 59px;
}
.peak_info p {
  font-size: 15px;
}
.peak_info .col_1 h3 {
  color: #000;
  font-size: 16px;
}
.peak_info .col_1 h3 span {
  display: block;
  font-weight: normal;
}
.peak_info .col_2 {
  float: left;
  width: 148px;
}
.peak_info ul.peak_photos {
}
.peak_info ul.peak_photos li {
}
.peak_info ul.peak_photos li img {
  max-width: 143px;
  display: block;
}
.peak_info ul.peak_photos li span {
  color: #666;
}
.peak_list_cont {
  overflow: hidden;
  margin-bottom: 20px;
}
.peak_list_cont .col_1 {
  float: left;
  width: 172px;
  margin-right: 13px;
}
.peak_list_cont .col_2 {
  float: left;
  width: 84%;
}
table.table_pl {
  font-size: 11px;
  width: 100%;
}
table.table_pl thead tr {
  background: none repeat scroll 0 0 #333333;
  color: white;
  font-size: 14px;
}
table.table_pl thead tr th {
  padding: 12px 12px;
  border: 1px solid #F2F2F2;
}
table.table_pl tr {
}
table.table_pl tr td {
  border: 1px solid #D9D9D9;
  color: #454040;
  padding: 8px 0 8px 5px;
}
table.table_pl tr td.peak_avatar {
  line-height: 51px;
}
table.table_pl tr td.peak_avatar img {
  float: left;
  margin-right: 5px;
  vertical-align: middle;
}
table.table_pl tr.bg {
  background: #F2F2F2;
}
footer {
  padding: 0 10px;
  width: 1180px;
  margin: auto auto 15px;
  position: relative;
}
footer .logo {
  background: url('http://peakery.com/static/img/misc/minilogo.png?beda46d8691f') no-repeat scroll 0 0 transparent;
  float: left;
  background-size: 96px 32px;
  width: 96px;
  height: 32px;
  margin: 8px 29px 0 0;
}
footer .logo a {
  display: block;
  height: 100%;
  text-indent: -5000px;
  color: transparent;
}
footer a {
  color: #333;
  text-shadow: 0 1px #F4F5F8;
  text-decoration: none;
}
footer a:hover {
  text-decoration: none;
  color: #00B1F2;
}
footer a:active {
  color: #00B1F2;
}
footer nav {
}
footer nav ul.lefted li,
footer nav ul.righted li {
  margin-right: 30px;
}
footer nav ul.righted {
  padding-right: 10px;
}
ul.horz {
}
footer ul.lefted2 li {
  margin-left: 76px;
}
footer ul.lefted2 {
  margin-top: 14px;
}
ul.horz li {
  float: left;
  margin-right: 1px;
}
div.share_box ul.horz li {
  float: left;
  margin-right: 1px;
  width: 80px;
}
div.share_box ul.horz li.google {
  padding-left: 25px !important;
  width: 68px !important;
}
.last {
  margin-right: 0 !important;
}
ul.vert {
}
ul.vert li {
  margin-bottom: 10px;
}
ul.vert li a {
  text-decoration: none;
    width: auto;
}
ul.vert li a:hover {
  text-decoration: underline;
}
ul.horz.buttons_1 {
  display: table;
}
ul.buttons_1 li {
  margin-right: 50px;
  margin-bottom: 30px;
}
textarea.elastic {
    resize: none;
}
form {
  position: relative;
  /* margin-bottom: 20px;*/
 /* overflow: hidden; */
}
form input[type='text'],
form textarea,
form input[type='password'] {
/*
  -moz-box-shadow: 1px 1px 1px #AEAEAE inset;
  -webkit-box-shadow: 1px 1px 1px #AEAEAE inset;
  box-shadow: 1px 1px 1px #AEAEAE inset;
  border: 1px solid #AEAEAE; */
  font-size: 14px;
  padding: 8px 10px;
}
form.form_set1 label {
  color: #3E3E3E;
  font-weight: bold;
  display: inline-block;
}
form fieldset {
  margin-bottom: 5px;
}
form.form_set1 textarea.about_me {
  width: 99%;
    height: 76px;
    resize: none;
}
form.form_set1 input[type='text'],
form.form_set1 input[type='password'] {
  width: 56%;
}
form.form_set1 label {
  display: inline-block;
  font-size: 11px;
  line-height: 22px;
  margin-right: 1.3em;
  text-align: right;
  width: 33%;
}
#edit_form {
  width: 440px;
}
form .col_set {
  overflow: hidden;
  margin-bottom: 15px;
}
form .col_set >div {
  float: left;
}
form .col_set .col_1 {
  width: 26%;
  margin-right: 1em;
}
form .col_set .col_2 {
  width: 65%;
}
form p {
  margin-bottom: 15px;
}
.title_action {
  color: black;
  font-size: 15px;
  font-weight: normal;
  padding-left: 20px;
  text-shadow: none;
}

.search_input {
  left: 12px;
}
#searchForPeak {
    font-size: 13px;
    font-weight: normal;
    padding: 3px 0 2px 12px;
    box-shadow: none;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    border: 1px solid #999;
    color: #111;
}
.tip {
  display: block;
  font-style: italic;
  color: #404040;
}
.tip a {
  text-decoration: none;
  color: inherit;
}
.tip a:hover {
  text-decoration: underline;
}
.btn {
  background: #00B1F2;
  color: white;
  display: inline-block;
  font-size: 16px;
  padding: 10px 15px;
  text-decoration: none;
  border: 0px solid #CCC;
  margin: 2px;
  z-index: 9999;
}


.btn:hover {
  background-image: -webkit-gradient( linear, left bottom, left top, color-stop(0.09,rgb(120,189,59)), color-stop(0.54,rgb(80,149,23)), color-stop(0.87,rgb(68,121,18)) );
  background-image: -moz-linear-gradient( center bottom, rgb(120,189,59) 9%, rgb(80,149,23) 54%, rgb(68,121,18) 87% );
  cursor: pointer;
  transition: 0.3s;
}
.btn:active {
  position: relative;
  top: 1px;
}
.btn img {
  margin-right: 4px;
  vertical-align: top;
}
.btn.set1 {
  height: 21px;
}
.btn.set1.short {
  font-size: 8px;
  height: 14px;
}
.set2 {
  height: 20px;
  padding-bottom: 6px;
  padding-top: 12px;
}
.btn.set2.input {
  height: 48px;
  padding: 0 30px;
}
.btn.set3 {
  font-size: 22px;
  font-weight: normal;
  height: 29px;
  padding-bottom: 9px;
  padding-top: 12px;
}
.btn.set3.input {
}
.btn.set2.input.delete {
  color: red;
  background: transparent;
  border: 0px;
  -moz-box-shadow: none !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  font-weight: normal;
}
.btn.set2.input.delete:hover {
  color: red;
  background: transparent;
  border: 0px;
  -moz-box-shadow: none;
  box-shadow: none;
  -webkit-box-shadow: none !important;
}
div.deleter {
  color: red;
  cursor: pointer;
  padding: 12px 0px;
}
a#bag1 {
  height: 36px;
  padding-top: 11px;
}
.btn.set4 {
  width: 380px;
  padding-bottom: 8px;
  padding-top: 11px;
}
.btn.set4:hover {
  text-decoration: none !important;
}
.btn.set4 .text {
  float: left;
  font-size: 24px;
  line-height: 27px;
  padding-top:20px;
}
.btn.set4 .num {
  color: #FEC;
  display: block;
  font-size: 33px;
}
.btn.set4 img {
  float: right;
}
.btn.set4_2 {
  padding-bottom: 6px;
  padding-top: 5px;
  width: 253px;
}
.btn.set4_2:hover {
  text-decoration: none !important;
}
.btn.set4_2 .text {
  float: left;
  font-size: 24px;
  padding: 15px;
}
.btn.set4_2 .num {
  color: #FEC;
  display: block;
  font-size: 33px;
}
.btn.set4_2 img {
  float: right;
}
#facebox {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 100;
  text-align: left;
}
#facebox .popup {
  position: relative;
  -webkit-box-shadow: 0 0 18px rgba(0,0,0,0.8);
  -moz-box-shadow: 0 0 18px rgba(0,0,0,0.8);
  box-shadow: 0 0 18px rgba(0,0,0,0.8);
}
#facebox .content {
  display: table;
    background: #f2f2f2; /* Old browsers */
    background: -moz-linear-gradient(top, #f2f2f2 9%, #f0f0f0 54%, #d9d9d9 87%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(9%,#f2f2f2), color-stop(54%,#f0f0f0), color-stop(87%,#d9d9d9)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #f2f2f2 9%,#f0f0f0 54%,#d9d9d9 87%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #f2f2f2 9%,#f0f0f0 54%,#d9d9d9 87%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #f2f2f2 9%,#f0f0f0 54%,#d9d9d9 87%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f2f2f2', endColorstr='#d9d9d9',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #f2f2f2 9%,#f0f0f0 54%,#d9d9d9 87%); /* W3C */
  padding: 20px;
}
#facebox .content >p:first-child {
  margin-top: 0;
}
#facebox .content >p:last-child {
  margin-bottom: 0;
}
#facebox .close {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 2px;
    display: block;
    background: url('http://peakery.com/static/img/facebox/closelabel.png?63b46d66015a') no-repeat right top transparent;
    width: 16px;
    height: 16px;
}
#facebox .close img {
  opacity: 0.3;
}
#facebox .close:hover img {
  opacity: 1.0;
}
#facebox .loading {
  text-align: center;
}
#facebox .image {
  text-align: center;
}
#facebox img {
  border: 0;
  margin: 0;
}
#facebox_overlay {
  position: fixed;
  top: 0px;
  left: 0px;
  height: 100%;
  width: 100%;
}
.facebox_hide {
  z-index: -100;
}
.facebox_overlayBG {
  background-color: #000;
  z-index: 99;
}
ul#summitlog-files li div.a {
    width:300px;
    height: 305px;
    text-align: center;
    /*border: 5px solid #FFF;
    box-shadow: 0 2px 2px #808080;
    -moz-box-shadow: 0 2px 2px #808080;
    -webkit-box-shadow: 0 2px 2px #808080;*/
    background-color: #33c1f5;
    margin: 2px;
}
ul#summitlog-files li div.a:hover {
    -webkit-box-shadow: 0 3px 6px rgba(0,0,0,0.4);
  -moz-box-shadow: 0 3px 6px rgba(0,0,0,0.4);
  box-shadow: 0 3px 6px rgba(0,0,0,0.4);
}
ul#summitlog-files li:last-child {
    float: left;
    width: auto;
    border: none;
    border-radius: 0;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    box-shadow: none;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    margin: 5px;
}
ul#summitlog-files li div.imageContainer {
    width: 300px;
    height: 305px;
    /*height: 190px;*/
    display: block;
    overflow: hidden;
}
ul#summitlog-files li div.textareaContainer {
    width: 300px !important;
    /*margin-top: -115px !important;*/
    display: block;
    margin: 0;
}
ul#summitlog-files li div.textareaContainer textarea {
    width: 300px !important;
    padding: 9px !important;
    resize: none;
    border: 1px solid #CCC;
}

span#same-route {
  cursor: pointer;
  padding: 0px 20px;
  display: inline;
  text-decoration: underline;
}
span#error-msg {
  color: red;
}
.sign_boxes {
  display: none;
  background-color: #ddeef6;
  position: absolute;
  width: 275px;
  z-index: 99;
  text-align: left;
  padding: 10px;
  top: 40px;
  left: 0;
  margin-right: -1px;
  color: #789;
  font-size: 11px;
  background-image: -webkit-gradient( linear, left bottom, left top, color-stop(0.09,#F2F2F2), color-stop(0.54,#F0F0F0), color-stop(0.87,#AAB0C7) );
  background-image: -moz-linear-gradient( center bottom, #F2F2F2 9%, #F0F0F0 54%, #AAB0C7 87% );
  -webkit-box-shadow: 1px 4px 5px #999;
  -moz-box-shadow: 1px 4px 5px #999;
  box-shadow: 1px 4px 5px #999;
}
a.signin {
  margin-top: 8px;
}
a.signup:hover {
  color: #FFF;
}
a.signup.btn.signUpBtn {
  z-index: 99;
}
#signin_menu {
  width: 222px !important;
}
#signin_menu form div {
  text-align: right;
}
#signin_menu input[type=text], #signin_menu input[type=password] {
  display: block;
  border: 2px solid #999;
  font-size: 18px;
  margin: 0 0 0px;
  padding: 10px 5px;
  width: 91%;
  margin-top: 10px;
  font-weight: normal;
}
#signin_menu p {
  margin: 0;
}
#signin_menu a {
  background: none repeat scroll 0 0 transparent;
  color: #666;
  display: inline-block;
  font-size: 12px;
  height: auto;
  padding: 0 0 0 5px;
  text-decoration: none;
  text-shadow: none;
  font-weight: normal;
}
#signin_menu label {
  font-weight: normal;
}
#signin_menu p.remember {
  padding: 10px 0;
}
#signin_menu p.forgot, #signin_menu p.complete {
  clear: both;
  margin: 5px 0;
}
#signin_menu p a {
  color: #27B!important;
}
#signup_menu {
}
#signup_menu form div {
  text-align: right;
}
#signup_menu p {
  line-height: 13px;
  margin-bottom: 12px;
}
#signup_menu label {
  font-weight: bold;
}
#register_container ul.errorlist li {
  color: red;
}
.blur {
  color: #999 !important;
}
div#login_error {
  background: none repeat scroll 0 0 #FFDFDF;
  border: 1px solid #999;
  color: red;
  font-size: 11px;
  line-height: 14px;
  padding: 8px 0;
  text-align: center !important;
}
ul.errorlist {
  color: #000;
  font-size: 11px;
  text-align: right !important;
  padding: 0;
}
ul.errorlist li {
  float: none;
  line-height: 15px;
  padding: 5px 10px;
}
div#upload-peak-photo h3 {
  text-align: center;
}
div#upload-peak-photo
form#upload-peak-photo div {
  padding: 10px 0px;
  min-width: 320px;
}
form#upload-peak-photo span {
  text-align: right;
  display: block;
}
form#upload-peak-photo #id_thumbnail {
  width: 100%;
}
div#upload-ajax-spinner {
  display: none;
  text-align: center;
  margin: 10px;
}
div#upload-done-button {
  width: 100%;
  text-align: right;
  display: none;
}
span#upload-done-text {
  text-align: center;
  font-size: 24px;
  font-weight: bold;
  margin-right: 10px;
}
div#routes-percent {
  padding: 15px 0px;
}
div#routes-percent p {
  margin-bottom: 15px;
}
div#routes-percent p:last-child {
  margin-bottom: 0px;
}
div#routes-percent p span {
  display: inline-block;
  background: #00B1F2;
  height: 20px;
  text-align: center;
  color: #fff;
  -webkit-box-shadow: 1px 1px 3px #787878;
  -moz-box-shadow: 1px 1px 3px #787878;
  box-shadow: 1px 1px 3px #787878;
  border: 2px solid #fff;
  margin-right: 15px;
}
div#directory_peak_list {
  float: left;
}
div#directory_peak_list ul {
}
div#directory_peak_list ul li {
  margin-bottom: 20px;
  overflow: hidden;
}
div#directory_peak_list ul li div {
  float: left;
}
div#directory_peak_list ul li .thumbnail {
  width: 102px;
}
div#directory_peak_list ul li .desc {
  width: 400px;
}
div#directory_peak_list ul li .desc h3 {
  font-size: 15px;
  margin-bottom: 1px;
}
div#directory_peak_list ul li .desc h3 a {
  color: #403E3E;
  text-decoration: none;
}
div#directory_peak_list ul li .desc h3 a:hover {
  text-decoration: underline;
}
div#directory_peak_list ul li .desc p {
  font-size: 15px;
  margin-bottom: 10px;
}
div#directory_peak_list ul li .desc span {
  display: block;
  color: #706B6B;
  font-size: 12px;
}
div#directory_pagination {
  padding: 10px 0px;
}
.directory {
  overflow: hidden;
  padding-top: 15px;
}
.directory .col_right {
  float: right;
  width: 420px;
}
.directory .col_right p {
  font-size: 16px;
  line-height: 26px;
}
.directory .col_left {
  float: left;
  margin: 0px 10px;
  width: 760px;
  position: relative;
}
.directory .col_left p {
    line-height: 30px;
    margin-bottom: 20px;
    font-size: 22px;
}
.directory .col_left2 {
  float: left;
  margin-right: 10px;
  width: 357px;
}
.directory .col_left2 p {
  font-size: 16px;
  line-height: 26px;
}
.directory .col_right2 {
  float: left;
  width: 812px;
  position: relative;
}

.directory h1 {
  font-family: Avenir,Trebuchet MS,sans-serif;
  font-size: 25px;
}
.directory .content-count {
  color: #888;
  right: 0;
  font-family: Avenir,Trebuchet MS,sans-serif;
  font-size: 14px;
  font-style: italic;
  font-weight: normal;
  line-height: 26px;
  text-indent: 0;
  position: absolute;
  top: -20px;
}
ul.peaks_w_list {
}
ul.peaks_w_list li {
  line-height: 36px;
}
ul.peaks_w_list li a {
  font-weight: bold;
  font-size: 20px;
}
ul.peaks_w_list .total_items {
  color: #666;
  margin-left: 10px;
  font-size: 14px;
}
#worldmap {
  position: relative;
  margin-bottom: 20px;
  margin: 0px 0px 20px 70px;
}
#worldmap .region {
  left: 0;
  outline-style: none;
  position: absolute;
  text-decoration: none;
  top: 0;
  z-index: 1;
}
#worldmap .region .id {
  display: none;
}
#worldmap .region .splash,	#worldmap .region .shade, #worldmap .region >img {
  visibility: hidden;
}
#worldmap .region .shade, #worldmap .region .splash {
  display: block;
  position: absolute;
}
#worldmap .region .shade {
  background: none repeat scroll 0 0 white;
  height: 100%;
  left: 0;
  opacity: 0.1;
  top: 0;
  width: 100%;
}
#worldmap .region .splash {
  background: none repeat scroll 0 0 #30659b;
    font-family: Avenir,Trebuchet MS,sans-serif;
  font-size: 17px;
  font-weight: 700;
  left: -142px;
  opacity: 0.95;
  padding: 10px 17px;
  top: -12px;
  z-index: 2;
}
#worldmap .region .splash h3 {
  white-space: nowrap;
}
#worldmap .region .splash ol {
  margin-bottom: 0;
  width: 315px;
}
#worldmap .region:hover .shade,	 #worldmap .region:hover .splash, #worldmap .region:hover img {
  cursor: pointer;
  visibility: visible;
}
#worldmap .region img:hover, #worldmap .region .shade:hover, #worldmap .region .splash:hover {
  visibility: visible !important;
}
#worldmap .region .splash h2 {
  color: #834325;
  font-size: 22px;
  margin: 0.2em;
}
#worldmap .region .splash h2 .sublabel {
  display: none;
}
.popup-body h1, .popup-layer .popup-body h1 a,	#worldmap .region .splash h3 {
  color: #34610F;
}
#worldmap .region .splash .text {
  color: #FFFFFF;

}
.popup-layer .popup-body h1 {
  font-size: 24px;
  margin-top: -0.167em;
}
#worldmap .region .splash a {
  color: #529918;
}
.popup-body ul.leader {
  max-height: 365px;
  min-width: 365px;
  overflow-x: hidden;
  overflow-y: auto;
}
.popup-body ul.leader .name {
  color: #888077;
  display: block;
  float: left;
  font-style: italic;
  line-height: 18px;
  text-align: right;
  white-space: nowrap;
  width: 8em;
}
.popup-body ul.leader .place {
  color: #529918;
  float: none;
  font-family: Avenir,Trebuchet MS,sans-serif;
  font-size: 14px;
  font-weight: bold;
  line-height: 18px;
  margin-left: -0.667em;
  margin-right: 1.333em;
  position: relative;
  right: -1.333em;
  text-align: left;
}
.popup-body ul.leader .user-image {
  float: left;
  margin-left: 0.5em;
  width: 30px;
}
.side-ad {
  margin-top: 1.667em;
}
.side-ad-close {
  margin-top: -0.333em;
}
b {
  color: #834325;
  font-style: italic;
  font-weight: normal;
}
.pagination {
  margin-bottom: 30px;
}
.pagination a {
  background: none repeat scroll 0 0 #5A9F20;
  border: 1px solid;
  color: white;
  font-size: 11px;
  padding: 4px 6px;
  text-decoration: none;
}
.pagination a:hover {
  border: 1px solid #666;
}
.pagination .prev {
}
.pagination .next {
}
.pagination .current {
  background: none repeat scroll 0 0 #D9D9D9;
  border: 1px solid;
  display: inline-block;
  font-weight: bold;
  padding: 4px 7px;
}
ul.peakbaggers {
}
ul.peakbaggers >li {
  -moz-box-shadow: 0 1px 2px #ccc;
  -webkit-box-shadow: 0 1px 1px #ccc;
  box-shadow: 0 1px 1px #ccc;
  background-color: #fff;
  overflow: hidden;
  padding: 12px;
  margin-bottom: 20px;
}
ul.peakbaggers li .avatar {
  float: left;
  margin-right: 20px;
  width: 280px;
}
ul.peakbaggers li .avatar img {
}
ul.peakbaggers li .content {
  float: left;
  width: 625px;
}
ul.peakbaggers li .content h2 a {
  color: inherit;
  text-decoration: none;
}
ul.peakbaggers li .content h2 a:hover {
  text-decoration: underline;
}
ul.peakbaggers li .content h2 span {
  font-family: Avenir,Trebuchet MS,sans-serif;
  font-size: 16px;
  font-weight: normal;
}
ul.peakbaggers li .content h2 span.blue {
    color: #00B1F2 !important;
}
ul.peakbaggers li .content p.latest {
  color: #807C7C;
  font-size: 15px;
  font-style: italic;
}
ul.peaks_bagged {
  overflow: hidden;
}
ul.peaks_bagged li {
  float: left;
  margin-right: 0;
  width: 120px;
  margin-right: 4px;
    margin: 7px 80px 7px 0px;
}
ul.peaks_bagged li.spaced {
  margin-right: 30px;
}
ul.peaks_bagged li img {
}
ul.peaks_bagged li .item_name {
  display: block;
  font-size: 14px;
  height: 40px;
  text-decoration: none;
}
ul.peaks_bagged li .item_name:hover {
  text-decoration: underline;
}
a#peak-list-item.False {
  opacity: 0.1;
  -webkit-opacity: 0.1;
  ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=10)";
  filter: alpha(opacity=10);
}
ul.peak_lists_page {
}
ul.peak_lists_page >li {
  background: none repeat scroll 0 0 #F7F7F7;
  background: -moz-linear-gradient(top,#eee 0%,#F7F7F7 100%);
  background: -webkit-gradient(linear,left top,left bottom,color-stop(0%,#eee),color-stop(100%,#f7f7f7));
  overflow: hidden;
  margin-bottom: 40px;
}
ul.peak_lists_page li .image {
  float: left;
  margin-right: 20px;
  width: 350px;
}
ul.peak_lists_page li .image img {
}
ul.peak_lists_page li .content {
  float: left;
  width: 804px;
  margin-top: 15px;
}
ul.peak_lists_page li .content h2 a {
  color: inherit;
  text-decoration: none;
}
ul.peak_lists_page li .content h2 a:hover {
  text-decoration: underline;
}
ul.peak_lists_page li .content h2 span {
  color: #807C7C;
  font-family: Avenir,Trebuchet MS,sans-serif;
  font-size: 14px;
  font-style: italic;
  font-weight: normal;
}
ul.peak_lists_page li .content p.latest {
  color: #807C7C;
  font-size: 15px;
  font-style: italic;
}
ul.included_peaks {
  overflow: hidden;
}
ul.included_peaks li {
  float: left;
  margin-right: 0;
  width: 120px;
  margin-right: 4px;
}
ul.included_peaks li.spaced {
  margin-right: 40px;
}
ul.included_peaks li img {
}
ul.included_peaks li .item_name {
  display: block;
  font-size: 11px;
  font-weight: bold;
  height: 40px;
  text-decoration: none;
}
ul.included_peaks li .item_name:hover {
  text-decoration: underline;
}
a#peak-list-item.False {
  opacity: 0.1;
  -webkit-opacity: 0.1;
  ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=10)";
  filter: alpha(opacity=10);
}
.loading1 {
  background: url('/static/img/misc/ajax1.gif?63b46d66015a') no-repeat center center;
}
.loading_submit {
  background: url('/static/img/misc/ajax1.gif?63b46d66015a') no-repeat center center !important;
  border: none;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  color: transparent;
  outline: none;
}
.peak_lists {
  overflow: hidden;
  padding-top: 15px;
}
.peak_lists .header {
  float: left;
  width: 100%;
}
.peak_lists .col_left {
  float: left;
  margin-right: 50px;
  width: 336px;
}
.peak_lists .col_right {
  float: left;
  width: 812px;
  position: relative;
}
.peak_lists h1 {
  font-family: Avenir,Trebuchet MS,sans-serif;
  font-size: 25px;
}
.peak_desc1 {
  margin-bottom: 30px;
}
.peak_desc1 h1 {
  font-size: 27px;
  color: black;
}
.peak_desc1 h1 .metadata {
  color: #7F8AAB;
  font-size: 18px;
  font-style: italic;
  font-weight: normal;
}
.peak_desc1 img {
  margin-bottom: 15px;
    box-shadow: 0 1px 8px #0E0D0D;
}
.peak_desc1 p {
  font-size: 14px;
  line-height: 22px;
  margin-bottom: 0;
  text-align: left;
  word-wrap: break-word;
}
ul.peak_desc_list {
  display: table;
  position: relative;
  top: 20px;
}
ul.peak_desc_list li {
  margin-bottom: 30px;
  overflow: hidden;
}
ul.peak_desc_list li div {
  float: left;
}
ul.peak_desc_list li .thumbnail {
  width: 138px;
}
ul.peak_desc_list li .desc {
  width: 674px;
}
ul.peak_desc_list li .desc .header{
}
ul.peak_desc_list li .desc .header h3 {
  font-size: 17px;
  margin-bottom: 1px;
    float: left;
    padding-right: 10px;
}
ul.peak_desc_list li .desc .header h3 a {
  color: #403E3E;
  text-decoration: none;
}
ul.peak_desc_list li .desc .header h3 a:hover {
  text-decoration: underline;
}
ul.peak_desc_list li .desc p {
  font-size: 14px;
  margin-bottom: 10px;
    line-height: 26px;
    float: left;
}
ul.peak_desc_list li .desc .header span {
  display: block;
  color: #706B6B;
  font-size: 12px;
    float: left;
    margin-top: 4px;
}
ul.users_lists {
    padding:10px 0 0 10px;
}
ul.users_lists li {
  overflow: hidden;
  margin-bottom: 15px;
}
ul.users_lists li a {
  color: #403E3E;
  font-size: 13px;
  text-decoration: none;
}
ul.users_lists li .avatar {
  float: left;
  width: 30%;
  margin-right: 10px;
}
ul.users_lists li .data {
  color: black;
  float: left;
  width: 60%;
}
ul.users_lists li .nickname {
  color: black;
  font-family: Avenir,Trebuchet MS,sans-serif;
  font-size: 19px;
  text-decoration: none;
  display: block;
}
ul.users_lists li p {
  margin-bottom: 10px;
  font-weight: normal;
  font-size: 12px;
}
ul.users_lists .progressbar {
  width: 60%;
  float: left;
  margin-right: 5px;
}
ul.users_lists .extra_info {
  font-size: 12px;
}
.peak_section_desc1 {
  margin-bottom: 30px;
    float: left;
    width: 100%;
}
.peak_section_desc1 h1 {
  font-size: 27px;
  color: black;
}
.peak_section_desc1 h1 .metadata {
  color: #7F8AAB;
  font-size: 18px;
  font-style: italic;
  font-weight: normal;
}
.peak_section_desc1 img {
  margin-bottom: 15px;
    width: 25%;
    float: left;
}
.peak_section_desc1 p {
  font-size: 24px;
  line-height: 36px;
  margin-bottom: 0;
  margin-left: 30px;
  word-wrap: break-word;
    width: 70%;
    float: left;
}
ul.users_lists_all {
    overflow: hidden;
    float:left;
    padding: 17px 1% 17px 1%;
    width:23%;
    height:190px;
}
ul.users_lists_all li {
  overflow: hidden;
  margin-bottom: 15px;
}
ul.users_lists_all li a {
  color: #403E3E;
  font-size: 12px;
  text-decoration: none;
}
ul.users_lists_all li .avatar {
  margin-right: 10px;
}
ul.users_lists_all li .data {
  color: black;
}
ul.users_lists_all li .nickname {
  color: black;
  font-family: Avenir,Trebuchet MS,sans-serif;
  font-size: 19px;
  text-decoration: none;
  display: block;
}
ul.users_lists_all li p {
  margin-bottom: 10px;
  font-weight: normal;
  font-size: 12px;
}
ul.users_lists_all .progressbar {
  width: 60%;
  float: left;
  margin-right: 5px;
}
ul.users_lists_all .extra_info {
  font-size: 12px;
}
.comment_edit {
}
.comment_edit img {
  float: left;
  margin-right: 10px !important;
}
.comment_edit .peakname {
  float: left;
  width: 49%;
}
.comment_edit .peakname .peak {
  color: #000;
  font-weight: bold;
}
.comment_edit .peakname span {
  display: block;
  color: #5E595E;
}
.comment_edit .thumbnail {
  overflow: hidden;
  margin-bottom: 20px;
}
.comment_edit form {
  clear: both;
}
.comment_edit textarea {
  width: 94%;
  margin-bottom: 15px;
}
form#edit_profile_form ul.errorlist {
  background: none !important;
  border: none !important;
  text-align: right !important;
  margin: 0 !important;
}
form#edit_profile_form ul.errorlist li {
  color: red;
}
form#change_password ul.errorlist {
  background: none !important;
  border: none !important;
  text-align: right !important;
  margin: 0 !important;
}
form#change_password ul.errorlist li {
  color: red;
}
ul.messages {
  padding: 10px 0px;
  cursor: pointer;
}
ul.messages li {
  padding: 10px;
  text-align: center;
  -webkit-box-shadow: 1px 1px 3px #7d7d7d;
  -moz-box-shadow: 1px 1px 3px #7d7d7d;
  box-shadow: 1px 1px 3px #7d7d7d;
  text-shadow: 1px 1px 0px #fff;
  filter: dropshadow(color=#fff,offx=1,offy=1);
}
ul.messages li.success {
  background: #b7f2b0;
}
ul.messages li.error {
  background: #eb9383;
}
ul.messages li.warning {
  background: #ede88b;
}
li.headlink {
}
li.headlink ul {
  display: none;
}
li.headlink ul {
  top: 131px;
  position: fixed;
  background: #f1f2f3;
  overflow: hidden;
  z-index: 100;
  width: 127px;
  -webkit-box-shadow: 1px 1px 5px #7a7a7a;
  -moz-box-shadow: 1px 1px 5px #7a7a7a;
  box-shadow: 1px 1px 5px #7a7a7a;
}
li.headlink:hover ul li {
  float: none;
}
li.headlink:hover ul {
  margin-top: 10px;
}
.explored {
  width: 160px;
}
textarea#comment {
  -moz-box-shadow: none !important;
}
div#explore_pagination {
  overflow: hidden;
  padding: 20px;
}
div#explore_pagination .results {
  float: left;
  width: 40%;
}
div#explore_pagination .links {
  float: right;
  width: 40%;
  text-align: right;
}
span.unknown-date {
  display: inline !important;
}
span.invalid-date {
  color: red;
  padding-bottom: 0px !important;
}
input#date {
  background: #fff url('/static/img/misc/cal.png?63b46d66015a') no-repeat 154px center;
  cursor: pointer;
}
/*form#explore input[type="text"] {
  width: 149px;
  color: #000;
}*/
div#flatpage {
  font-size: 14px;
  padding: 0px 20px 20px 20px;
}
div#flatpage b {
  color: black;
  font-weight: bolder;
  font-style: normal;
}
div#flatpage h2 {
  border-top: 2px solid;
  padding-top: 10px;
  margin-top: 20px;
  clear: both;
}
div#flatpage ul {
  list-style: disc outside none;
  padding-left: 30px;
  margin-top: 20px;
  margin-bottom: 20px;
}
div#flatpage ol {
  margin-left: 40px;
}
div#flatpage li {
  display: list-item;
  margin-bottom: 10px;
}
form#upload-peak-photo input {
  width: 93%;
}
form#upload-peak-photo input[type="submit"] {
  width: auto;
}
div#nearbypeaks {
  overflow: hidden;
}
div#nearbypeaks h3 {
  float: left;
  padding-top: 9px;
}
div#nearbypeaks a {
  float: right;
}
div#recent_summits {
}
div#recent_summits h1 {
  margin-top: 10px;
}
div#recent_summits div.summits {
  overflow: hidden;
}
div#recent_summits div.summits div.summit {
  width: 230px;
  min-height: 250px;
  float: left;
  padding: 0 0 10px 22px;
}
div#recent_summits div.summits div.summit img {
  -webkit-box-shadow: 2px 2px 7px #757575;
  -moz-box-shadow: 2px 2px 7px #757575;
  box-shadow: 2px 2px 7px #757575;
}
div#recent_summits div.summits div.summit div {
  padding-top: 10px;
}
div#recent_summits div.summits div.summit div a {
  font-size: 14px;
  text-decoration: none;
}
div#recent_summits div.summits div.summit span {
  display: block;
}
div#recent_summits div.summits div.summit span a {
}
div#recent_summits div.pagination {
  text-align: center;
  margin-top: 30px;
}
.hidden {
  display: none;
}
div#bigphoto a img#peak-img {
  width: 745px;
  height: 500px;
}

.em-btn-active {
  background: #0066FF;
}

.em-btn-active.hidden{
  display:none;
}

div#breadcrumbs {
  overflow: hidden;
}
div#breadcrumbs ul {
  list-style: none;
}
div#breadcrumbs ul li {
  float: left;
  margin-top: 5px;
  padding-right: 5px;
  font-size:18px;
}
div#breadcrumbs_last ul li {
  float: left;
  padding-right: 5px;
  font-size:18px;
}
textarea#id_comment {
  color: #000 !important;
}
div#login-form div.singup {
  text-align: right;
  margin-top: -30px;
}
div#login-form div.singup a {
  font-size: 11px;
}
div#photo-lb {
  overflow: hidden;
}
div#photo-lb div.img {
    display: block;
    width: 650px;
    height: 410px;
    background-position: center center;
    background-repeat: no-repeat;
    position: relative;
    margin-bottom: 10px;
}
div#photo-lb div.img a {
    display: inline-block;
    padding: 8px 10px 8px 46px;
    position: absolute;
    right: 10px;
    bottom: 10px;
    font-weight: bold;
    color: #000;
    text-decoration: none;
    font-size: 13px;
}
div#photo-lb div {
  overflow: hidden;
}
div#photo-lb div span {
  padding: 10px 0px;
  display: inline-block;
}
div#photo-lb div span.l {
  float: left;
  padding-top: 15px;
}
div#photo-lb div span.r {
  float: right;
}
div#photo-lb div span.r span {
  padding: 5px 0 0 40px;
  height: 18px;
}
div#photo-lb div.caption {
    display: block;
    overflow: hidden;
    padding-top: 10px;
    padding: 0 10px;
    width: 630px;

}
div#photo-lb div.caption p {
    display: block;
    text-align: center;
    font-size: 15px;

}
div#photo-lb div.controls {
    display: block;
    text-align: center;
    margin: 0 auto;
    width: 299px;
    margin-top: 10px;
}
div#photo-lb div.controls span a {
    display: inline-block;
    float: left;
    width: 117px;
    height: 31px;
    background-repeat: no-repeat;
    background-position: left top;
    text-indent: -99999px;
    text-align: left;
    margin: 0 15px;
}

input.placeholder {
    color: #999 !important;
}
input.skipper {
  color: #666;
  background: transparent;
  border: 0px;
  -moz-box-shadow: none !important;
  box-shadow: none !important;
  -webkit-box-shadow: none !important;
  font-weight: normal;
}
input.skipper:hover {
  color: #000;
  background: transparent;
  border: 0px;
  -moz-box-shadow: none;
  box-shadow: none;
  -webkit-box-shadow: none !important;
}
div#highest-peak {
  height: 50px;
  font-size: 13px;
  cursor: pointer;
}
div#highest-peak span.n {
  display: block;
  padding-top: 6px;
}
div#highest-peak span.e {
  color: #666;
  padding-top: 4px;
  font-size: 10px;
}
.explore-photo {
  overflow: hidden;
}
.explore-photo .peak {
  width: 170px;
  min-height: 200px;
  float: left;
  padding: 0 22px 10px 0;
}
.explore-photo .peak span a {
  text-decoration: none;
  color: #404040;
}
.explore-photo .peak span a:hover {
  text-decoration: underline;
}
.explore-photo .peak div a.name {
  font-weight: bold;
}
.explore-photo .peak .gray {
  color: #404040;
}
.add-more-fellows {
  font-size: 10px;
  padding-left: 19px;
  height: 14px;
  cursor: pointer;
  width: 200px;
}
div#about {
  font-family: Avenir,Trebuchet MS,sans-serif;
}
div#about a {
  color: #24BBF2;
  text-decoration: none;
}
div#about a:hover {
  color: #24BBF2;
  text-decoration: underline !important;
}
div#about div.header {
  text-align: left;
  padding: 35px 0 20px 30px;
  background: #F2F2F2;
  background: -moz-linear-gradient(top,#F2F2F2 0%,#FFF 100%);
  background: -webkit-gradient(linear,left top,left bottom,color-stop(0%,#F2F2F2),color-stop(100%,#FFF));
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#F2F2F2',endColorstr='#FFF',GradientType=0 );
}
div#about div.header h1 {
  font-size: 40px;
}
div#about div.header h6 {
  font-size: 16px;
  color: #7F7F7F;
  margin-bottom: 20px;
}
div#about div.header img {
  -webkit-box-shadow: 2px 2px 20px #808080;
  -moz-box-shadow: 2px 2px 20px #808080;
  box-shadow: 2px 2px 20px #808080;
  margin-bottom: 25px;
}
div#about div.body {
  width: 1100px;
  margin: auto;
}
div#about div.body .top-row {
  overflow: hidden;
  margin-bottom: 45px;
}
div#about div.body .top-row div.a {
  margin-left: 10px;
}
div#about div.body .top-row div {
  float: left;
  width: 200px;
  margin-right: 40px;
}
div#about div.body .top-row div.c {
  margin-right: 50px;
}
div#about div.body .top-row div h2 {
  color: #262626;
  font-size: 16px;
  line-height: 20px;
  margin-bottom: 12px;
}
div#about div.body .top-row div p {
  font-size: 12px;
  line-height: 18px;
  color: #595959;
  margin-bottom: 0px;
}
div#about div.body .views {
  overflow: hidden;
}
div#about div.body .views .image {
  float: left;
  text-decoration: none;
}
div#about div.body .views .txt {
  float: right;
  width: 420px;
  padding-top: 50px;
}
div#about div.body .views .txt h2 {
  font-size: 20px;
  color: #262626;
  line-height: 24px;
}
div#about div.body .views .txt p {
  font-size: 14px;
  color: #595959;
  line-height: 24px;
}
div#about div.body .peakpage-view {
  overflow: hidden;
}
div#about div.body .peakpage-view .image {
  float: right;
  text-decoration: none;
}
div#about div.body .peakpage-view .txt {
  float: left;
  width: 455px;
  padding-top: 50px;
}
div#about div.body .peakpage-view .txt h2 {
  font-size: 20px;
  color: #262626;
  line-height: 24px;
}
div#about div.body .peakpage-view .txt p {
  font-size: 14px;
  color: #595959;
  line-height: 24px;
}
div#about div.body .claim {
  overflow: hidden;
}
div#about div.body .claim .image {
  float: left;
  text-decoration: none;
}
div#about div.body .claim .txt {
  float: right;
  width: 580px;
  padding-top: 50px;
}
div#about div.body .claim .txt h2 {
  font-size: 20px;
  color: #262626;
  line-height: 24px;
}
div#about div.body .claim .txt p {
  font-size: 14px;
  color: #595959;
  line-height: 24px;
}
div#about div.body .claim .txt img {
  margin-top: 45px;
}
div#about div.body .user-page {
  overflow: hidden;
}
div#about div.body .user-page .image {
  float: right;
  text-decoration: none;
}
div#about div.body .user-page .txt {
  float: left;
  width: 455px;
  padding-top: 50px;
}
div#about div.body .user-page .txt h2 {
  font-size: 20px;
  color: #262626;
  line-height: 24px;
}
div#about div.body .user-page .txt p {
  font-size: 14px;
  color: #595959;
  line-height: 24px;
}
div#about div.body .user-page div#register {
  padding: 25px;
  width: 300px;
  margin-left: 10px;
}
div#about div.body .feedback {
  margin-top: 15px;
}
div#about div.body .datas {
  margin-top: 15px;
}
div#about div.body .datas {
}
div#about div.body .datas ul {
  list-style: disc;
  margin-bottom: 20px;
}
div#about div.body .datas ul li {
  margin: 5px 25px;
}
div#about div.txt div#register h2:first-child {
    display: block;
    text-align: center;
    font-size: 24px;
}
div#about div.txt div#register div.containerContainer h1.getYourOwnPeakPage {
    display: none;
}
div#about div.txt div#register div.containerContainer {
    padding-top: 0;
}
div#about div.txt div#register div.containerContainer span:last-child {
    display: block;
    text-align: center;
    margin-top: 10px;
}
div#first-peak-splash div {
  width: 500px !important;
  overflow: hidden;
}
.django-upload-image-form #upload-peak-photo {
  margin-bottom: 0;
    /*width: 420px;*/
}
.django-upload-image-form #upload-peak-photo h3 {
  font-size: 23px;
}
.django-upload-image-form #upload-peak-photo h5 {
  color: #514E52;
  font-family: Avenir,Trebuchet MS,sans-serif;
  font-size: 15px;
  font-weight: normal;
  margin-bottom: 25px;
  text-align: justify;
  text-decoration: none;
  text-shadow: none;
}
.django-upload-image-form #upload-peak-photo h6 {
  font-size: 23px;
  text-align: center;
  color: #555356;
  margin: 20px 0 10px 0;
}
.django-upload-image-form #upload-peak-photo span {
  font-size: 0.83em;
  margin-top: 3px;
  color: #535152;
  text-align: left;
}
.django-upload-image-form #upload-peak-photo span.submit_button {
  float: right;
}
.django-upload-image-form #upload-peak-photo span.submit_button p {
  float: left;
  font-size: 0.87em;
  padding-right: 5px;
  margin: 0;
  padding-top: 12px;
  color: #929292;
}
.django-upload-image-form #upload-peak-photo span.submit_button input {
  float: right;
}
div#seemore-summits {
  padding: 10px;
  background: white;
  text-align: center;
  cursor: pointer;
}
div#seemore-summits:hover {
  background: #ecf6fc;
}

div#seemore-summits-loading {
  padding: 10px;
  background: white;
  text-align: center;
}

div#seemore-summits-loading:hover {
  background: #ecf6fc;
}

div#big-3d-peak {
  display: none;
}
#viewport-3d {
  overflow: hidden;
}


/*
    Sign up with facebook new style
*/
#signup_menu {
    background: #d8dad7; /* Old browsers */
    background: -moz-linear-gradient(top, #d8dad7 0%, #f0f1f6 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#d8dad7), color-stop(100%,#f0f1f6)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #d8dad7 0%,#f0f1f6 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #d8dad7 0%,#f0f1f6 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #d8dad7 0%,#f0f1f6 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#d8dad7', endColorstr='#f0f1f6',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #d8dad7 0%,#f0f1f6 100%); /* W3C */
    text-align: center;
}
#signup_menu a {
    display: inline;
    margin:0;
    padding:0;
}
#signup_menu a:hover {
    background:none;
    text-shadow:none;
}
.signUpWithFb {
    padding: 35px 40px;
    text-align:center;
}
.signUpWithFb p {
    font-size:15px;
    color: #333;
    margin-top:25px;
}
.signUpWithFb p a,
.signUpWithFb p a:hover {
    color:#006EBF;
}

.signUpWithEmail {
    padding: 5px 45px;
    overflow:hidden;
}
.signUpWrapper h1 {
    display:block;
    margin: 5px 0;
    text-align:center;
    color:#222;
    font-size:25px;
}
.signUpWithEmail form span.a {
    background: #fff;
    display:block;
    width:279px;
    margin:5px 0px;
}
.signUpWithEmail form span.a input {
    background: #FFFFFF;
    border: 2px solid #DDDFE7;
    font-size: 15px;
    padding: 10px 10px 10px 30px;
    width: 236px;
}
.signUpWithEmail form span.a#showPasswordWrapper {
    display:block;
    margin: 5px 0px 0px 10px;
}
.signUpWithEmail form label[for="showPassword"] {
    margin-left:8px;
    font-weight:normal !important;
    font-size: 13px;
    color: #222;
}
.signUpWithEmail form p.terms {
    color:#333;
    display:block;
    text-align:center;
    font-size:10px;
    margin-top:10px;
}
.signUpWithEmail form p.terms a {
    text-decoration: underline;
    font-weight:bold;
}
.signUpWithEmail form p.terms a:hover {
    color: #333;
}
.signUpWithEmail form .btnWrapper {
    display: block;
    text-align: center !important;
}
.signUpWithEmail form .btnWrapper input.btn {
    line-height:25px;
    margin: 10px auto;
    text-align:center;
    pading: 0px 20px;
    text-transform: capitalize;
    font-size: 15px;
    padding: 10px 20px;
}
.signUpWelcome {
    padding: 5px 10px;
    width: 355px;
}
.signUpWelcome h1 {
    display: block;
    font-size: 30px;
    text-align: center;
}
.signUpWelcome h2 {
    text-align: justify;
    font-size: 17px;
}
.signUpWelcome form.connect-button {
    display: inline-block;
    margin: 0;
    padding: 0;
    float: left;
}
.signUpWelcome .buttonsContainer {
    display: block;
}
.signUpWelcome div#fb-root {
    display: none;
}
.signUpFollowOtherUsers {
    padding:  10px 20px;
}
.signUpFollowOtherUsers h2 {
    font-size: 16px;
}
.signUpFollowOtherUsers h2 span {
    color: green;
    font-size: 19px;
}
.signUpFollowOtherUsers .peakBaggersContainer {
    display: block;
    padding: 10px;
    background: #FFF;
    border: 1px solid #999;
}
.signUpFollowOtherUsers .peakBaggersContainer ul li {
    display: block;
    margin:  5px 0px;
    overflow: hidden;
    height: 40px;
}
.signUpFollowOtherUsers .peakBaggersContainer ul li .checkboxWrapper {
    float: left;
    height: 40px;
}
.signUpFollowOtherUsers .peakBaggersContainer ul li .checkboxWrapper input {
    margin: 14px 10px 0px 10px;
}
.signUpFollowOtherUsers .peakBaggersContainer ul li label {
    display: inline-block;
    margin-left: 10px;
    cursor: pointer;
}
.signUpFollowOtherUsers .peakBaggersContainer ul li label span {
    display: block;
}
.signUpFollowOtherUsers .peakBaggersContainer ul li img {
    float: left;
}
.signUpFollowOtherUsers .peakBaggersContainer ul li .clearfix:first-child span {
    display: block;
}
.signUpFollowOtherUsers .peakBaggersContainer ul li .clearfix:last-child {
    margin-left: 10px;
}
.signUpFollowOtherUsers .peakBaggersContainer ul li .clearfix h3 {
    font-size: 15px;
    margin: 0;
    padding:  0;
    color: #333;
    text-shadow: none;
}
.signUpFollowOtherUsers .peakBaggersContainer ul li .clearfix p {
    font-size: 11px;
    color: #999;
    margin: 0;
    padding: 0;
    margin-top: -4px;
}



#findFriendsContainer.signUpWelcome {
    padding:  0;
}
#findFriendsContainer span.formError {
    display: block;
    text-align: left;
    text-indent: 5px;
    color: red;
    margin-bottom: 10px;
    margin-top: -3px;
}
#findFriendsContainer #loginFailed {
    display: block;
    margin-top: 15px;
}
#findFriendsContainer #loginFailed h3 {
    font-size: 20px;
    margin: 0;
    color: red;
}
#findFriendsContainer #loginFailed p {
    font-size: 12px;
    color: #C61907;
    margin: 0;
    margin-top: 3px;
    text-align: justify;
}


li#userAuthenticated a {
    display: block;
    height: 40px;
    overflow: hidden;
}
li#userAuthenticated a span {
    display: block;
    padding: 3px 5px;
    margin-left: -30px;
}
li#userAuthenticated a span img {
    border: 2px solid #fff;
    float: left;
}
li#userAuthenticated a span p#userNameP {
    display: inline-block;
    line-height: 29px;
    padding:  0px;
    margin:  0px;
    margin-left: 10px;
    float: left;
}
li#userAuthenticated a span span#dropDownArrow {
    display: inline-block;
    float: right;
    padding: 0;
    width: 7px;
    height: 6px;
    margin-top: 12px;
    margin-left: 7px;
}
li#userAuthenticated:hover a span span#dropDownArrow, li#userAuthenticated a.active span span#dropDownArrow {
    background-position: top center;
}
ul#dropDownUserMenu {
    display: block;
    position: absolute;
    background: #efefef;
    box-shadow: 1px 1px 5px #7A7A7A;
    -moz-box-shadow: 1px 1px 5px #7A7A7A;
    -webkit-box-shadow: 1px 1px 5px #7A7A7A;*/
    overflow: hidden;
    z-index: 100;
}
ul#dropDownUserMenu li {
    list-style: none;
    float: none;
    display: block;
    width: 100%;
    margin:  0;
}
ul#dropDownUserMenu li a {
    display: block;
    cursor: pointer;
    margin-top: 8px;
}

li#notificationsCounter {
  margin-left: -40px;
  margin-top:-4px;
}
li#notificationsCounter a.btn.newNotifications {
    padding:  8px;
    line-height: 10px;
    box-shadow: none;
    z-index: 99;
}
li#notificationsCounter a.btn.noNotifications {
    padding:  8px;
    line-height: 8px;
    background: #7d7d7d;
    color: #FFF;
    box-shadow: none;
    text-shadow: none;
    z-index: 99;
}

#signInFormContainer {
    padding-top: 20px;
    padding-bottom: 5px;
}
#signInFormContainer a.a {
    display: block;
    margin: 10px 0;
    text-align: center;
}
#signInFormContainer a.a img {

}
#signInFormContainer h2 {
    display: block;
    margin: 0;
    padding: 0;
    text-align: center;
    margin-top: 11px;
}
#signInFormContainer form {
    margin: 0;
    padding:  0;
    position: relative;
}
#signInFormContainer form span.a {
    background: none repeat scroll 0 0 #FFFFFF;
    display: block;
    margin: 5px 0;
    width: 220px;
}
#signInFormContainer form span.a input {
    border: 2px solid #DDDFE7;
    font-size: 15px;
    padding: 10px 10px 10px 30px;
    width: 178px;
}

#signInFormContainer form span#forgotPassword {
    display: block;
    text-align: left;
}
#signInFormContainer form span#forgotPassword a {
    color: #333;
    line-height: normal;
    margin: 0;
    padding:  0;
    padding-left: 5px;
}
#signInFormContainer form span.b {
    display: block;
    margin-top: 10px;
    margin-bottom: 5px;
    text-align: center;
}
#signInFormContainer form span.b input {
    font-size: 15px;
    line-height: 25px;
    margin: 10px auto;
    padding: 10px 30px;
    text-align: center;
    text-transform: capitalize;
}

#getYourOwn {
    background: #cbd8f8; /* Old browsers */
    background: -moz-linear-gradient(top, #cbd8f8 0%, #ffffff 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#cbd8f8), color-stop(100%,#ffffff)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #cbd8f8 0%,#ffffff 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #cbd8f8 0%,#ffffff 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #cbd8f8 0%,#ffffff 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#cbd8f8', endColorstr='#ffffff',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #cbd8f8 0%,#ffffff 100%); /* W3C */
    box-shadow: 0px 2px 3px rgba(0,0,0,0.3);
    -moz-box-shadow: 0px 2px 3px rgba(0,0,0,0.3);
    -webkit-box-shadow: 0px 2px 3px rgba(0,0,0,0.3);
    margin-bottom: 20px;
    margin-right: 6px;
}
#getYourOwn .content {
    padding: 25px 20px;
    text-align: center;
    display: block;
}
#getYourOwn .content h1 {
    font-size: 22px;
}
#getYourOwn .content span.signUpWithEmail {
    display: block;
    margin-top: 10px;
    text-align: center;
    font-size: 12px;
}
#getYourOwn .content span.signUpWithEmail a {
    font-size: 13px;
    font-weight: bold;
    text-decoration: none;
}
#getYourOwn .content span.signUpWithEmail a:hover {
    text-decoration: underline;
}

ul.topCompanions li {
    width: 275px;
    display: block;
    margin-top: 18px;
}
ul.topCompanions li:first-child {
    margin-top: 0px;
}
ul.topCompanions li:last-child {
    margin-bottom: 0px;
}
ul.topCompanions li {
    display: block;
    text-decoration: none;
}
ul.topCompanions li span {
    display: block;
}
ul.topCompanions li span .leftCol {
    display: inline-block;
    float: left;
    margin-right: 10px;
}
ul.topCompanions li span .leftCol img {

}
ul.topCompanions li span .rightCol {
    display: inline-block;
    width: 220px;
}
ul.topCompanions li span .rightCol h4 {
    font-size: 16px;
    margin:  0;
    color: #333;
}
ul.topCompanions li span .rightCol p {
    font-size: 12px;
    margin: 0;
    color: #333;
    margin-top: 3px;
    text-decoration: none;
}
ul.peakery_peak span.peakInfo {
    float: right;
    width: 220px;
    display: inline-block;

}
ul.peakery_peak span.peakInfo h4 {
    color: #333;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 0;
    margin-top: 0;
}

ul.peakery_peak span.peakInfo a {
    display: block;
    float: none;

}
ul.peakery_peak span.peakInfo p {
    color: #333;
    font-size: 11px;
    font-weight: normal;
    margin-bottom: 0px;
    width: auto;
    display: block;
    float: none;
}
ul.peakery_peak span.peakInfo p.times {
    color: #333;
    font-size: 11px;
    font-weight: bold;
    margin-bottom: 0px;
    width: auto;
    float: none;
    display: block;
}


ul#peakListsCompleted {

}
ul#peakListsCompleted li span {

}
ul#peakListsCompleted li span .leftCol {
    display: inline-block;
    float: left;
    margin-right: 5px;
    width: 100px;
}
ul#peakListsCompleted li span .leftCol a {

}
ul#peakListsCompleted li span .leftCol a img {

}
ul#peakListsCompleted li span .rightCol {
    width: 230px;
    display: inline-block;
    float: left;
}
ul#peakListsCompleted li span .rightCol a {
    display: block;
    float: none;
    margin-bottom: 1px;
}
ul#peakListsCompleted li span .rightCol h4 {
    color: #333333;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 3px;
    margin-top: 0;
    display: block;
}

ul#peakListsCompleted li span .rightCol span.percentContainer {
    display: inline-block;
    float: left;
    height: 23px;
    width: 100px;
    overflow: visible;
}


span.percentContainer {
    display: inline-block;
    float: left;
    height: 23px;
    width: 100px;
    overflow: visible;
}

span.percent {
    background: #00b0f7;
    color: #FFF;
    display: block;
    font-weight: bold;
    height: 23px;
    line-height: 23px;
    text-align: center;
    width: 90%;
    border: 1px solid #FFF;
    box-shadow: 1px 1px 1px rgba(0,0,0,0.2);
    -moz-box-shadow: 1px 1px 1px rgba(0,0,0,0.2);
    -webkit-box-shadow: 1px 1px 1px rgba(0,0,0,0.2);
    float: left;
}

p.completed_list {
    padding-left: 120px;
    line-height: 24px;
}

ul#peakListsCompleted li span .rightCol span.percentContainer span.percent {
    background: #00B1F2;
    color: #FFF;
    display: block;
    font-weight: bold;
    height: 23px;
    line-height: 23px;
    text-align: center;
    width: 90%;
    border: 1px solid #FFF;
    box-shadow: 1px 1px 1px rgba(0,0,0,0.2);
    -moz-box-shadow: 1px 1px 1px rgba(0,0,0,0.2);
    -webkit-box-shadow: 1px 1px 1px rgba(0,0,0,0.2);
    float: left;
}
ul#peakListsCompleted li span .rightCol p {
    display: inline-block;
    float: right;
    font-size: 10px;
    font-weight: normal;
    margin: 0;
    padding: 0;
    text-align: center;
    width: 60px;
}


/*
    **NEW PEAKERY FORMS
*/
form.peakeryForm span.a {
    background: none repeat scroll 0 0 #FFFFFF;
    display: block;
    margin: 5px 0;
    width: 220px;
}
form.peakeryForm span.a input {
    border: 2px solid #DDDFE7;
    font-size: 15px;
    padding: 10px 10px 10px 30px;
    width: 178px;
}





/*
===================================================================================================
    *** PEAKERY NEWS MAIN CONTAINER
===================================================================================================
*/
.peakeryNewsMainContainer h1.title {
    color: #222;
}
.peakeryNewsMainContainer .nav {
    display: block;
    margin-bottom: 20px;
    margin-left: 8px;
}
.peakeryNewsMainContainer .nav p {
    display: inline-block;
    float: left;
    margin: 5px 20px 0 0;
    font-size: 14px;
    color: #777;
}
.peakeryNewsMainContainer .nav a.btn:first-child {
    margin-right: 20px;
}
.peakeryNewsMainContainer .nav a.btn {
    display: inline-block;
    float: left;
    line-height: normal;
    margin-right: 30px;
    padding: 5px 15px;
    box-shadow: none;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    background: none;
    color: #333;
    font-size: 16px;
}
.peakeryNewsMainContainer .nav a.btn.selected, .peakeryNewsMainContainer .nav a.btn:hover {
    background: #00b1f2;
    color: #FFF;
}
.peakeryNewsMainContainer ul#news {
padding: 0px 10px;
}
.peakeryNewsMainContainer ul#news li {
    display: block;
    background: #fff;
    overflow: hidden;
    margin-bottom: 10px;
    padding: 15px;
}
.peakeryNewsMainContainer ul#news li .newWrapper {
    display: block;
    width: 100%;
}
.peakeryNewsMainContainer ul#news li .newWrapper .leftCol {
    float: left;
    margin-right: 10px;
}
.peakeryNewsMainContainer ul#news li .newWrapper .centerCol {
    width: 620px;
    float: left;
    display: inline-block;
}
.peakeryNewsMainContainer ul#news li .newWrapper .centerCol span {
    display: inline-block;
}
.peakeryNewsMainContainer ul#news li .newWrapper .centerCol span:last-child,
.peakeryNewsMainContainer ul#news li .newWrapper .centerCol span.date {
    font-weight: normal;
    font-size: 12px;
    color: #777;
}
.peakeryNewsMainContainer ul#news li .newWrapper .centerCol span:first-child {
    font-weight: normal;
    font-size: 17px;
    color: #333;
}
.peakeryNewsMainContainer ul#news li .newWrapper .centerCol span:first-child a.username {
    font-weight: bold;
    color: #333;
    text-decoration: none;
}
.peakeryNewsMainContainer ul#news li .newWrapper .centerCol span:first-child a.username:hover {
    text-decoration: underline;
}
.peakeryNewsMainContainer ul#news li .newWrapper .centerCol span:first-child a.itemName {
    font-weight: bold;
    color: #1cb6f2;
    text-decoration: none;
}
.peakeryNewsMainContainer ul#news li .newWrapper .centerCol span:first-child a.itemName:hover {
    text-decoration: underline;
}
.peakeryNewsMainContainer ul#news li .newWrapper .centerCol div.comment {
    margin-top: 5px;
    font-size: 13px;
}
.peakeryNewsMainContainer ul#news li .newWrapper .centerCol div.comment p {
    text-align: justify;
    margin: 0;
}
.peakeryNewsMainContainer ul#news li .newWrapper .centerCol.wComment .comment {
    display: block;
    margin: 15px 0;
}
.peakeryNewsMainContainer ul#news li .newWrapper .centerCol ul.companions li {
    display: inline-block;
    padding: 0;
    margin: 5px;
    float: left;
    border-radius: 0;
}
.peakeryNewsMainContainer ul#news li .newWrapper .centerCol ul.companions li a {
    display: block;
    text-decoration: none;
}
.peakeryNewsMainContainer ul#news li .newWrapper .centerCol ul.companions li span {
    display: block;
}
.peakeryNewsMainContainer ul#news li .newWrapper .centerCol ul.companions li span img {
    float: left;
}
.peakeryNewsMainContainer ul#news li .newWrapper .centerCol ul.companions li span p {
    display: inline-block;
    font-size: 11px;
    font-weight: normal;
    line-height: 40px;
    margin-bottom: 0;
    margin-left: 10px;
    width: 90px;
}
/*.peakeryNewsMainContainer ul#news li .newWrapper .centerCol ul.companions li*/
.peakeryNewsMainContainer ul#news li .newWrapper .rightCol {
    display: inline-block;
    float: right;
    max-width: 390px;
    text-align: right;
}
.peakeryNewsMainContainer ul#news li .newWrapper .rightCol img {
    float: left;
    margin: 5px;
    display: inline-block;
    border-radius: 0;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
}

.new_bag {
  overflow: hidden;
    width: 750px !important;
}
#summit-field-date{overflow:hidden}
#summit-field-date div.a{
    width: 230px;
    line-height: 43px;
}
#summit-field-date div.a h4{}
#summit-field-date div.b{width:189px}
#summit-field-date div.c{
    width:331px;
    padding-top: 5px;
}
#summit-field-date div.c span.invalid-date {
    margin-top: 5px;
    margin-left: 16px;
}
#summit-field-date div.c span{}

textarea#summit{width:100%}
div.summitlog.caption{width:175px !important; margin-top: -6px;}
div.summitlog.caption textarea{width:175px !important; height: 115px !important;}
div.summitlog.checker{clear:both;}
div.summitlog.checker input{display:inline-block;margin-right: 5px;}
div.summitlog.checker label{font-size:11px; font-weight:normal;display:inline-block;cursor:pointer}

span.see-guidelines{text-decoration: underline; cursor: pointer; font-size: 10px; padding-left: 22px; margin-top: -7px;}

.new_summitlog_container{overflow:hidden;}
.new_summitlog_container div{float:left;}
.new_summitlog_container .avatar{width:70px;}
.new_summitlog_container .log{width:570px;}
.new_summitlog_container .log h3{font-size:18px; margin-bottom: 5px;}
.new_summitlog_container .log h3 a{color:#1CB6F2; text-decoration:none}
.new_summitlog_container .log div.log{margin-bottom: 10px;}
.new_summitlog_container .log span.date{display:block; color:gray;}
.new_summitlog_container .log div.users{padding-top: 5px;}
.new_summitlog_container .log div.users a{text-decoration:none}
.new_summitlog_container .log div.users span{margin-right:10px; display:inline-block; vertical-align:middle;}
.new_summitlog_container .photos{float:right; width:275px;}
.new_summitlog_container .photos div{float:right; width:80px; margin-left:10px;}
.new_summitlog_container .photos div.margintop{margin-top:10px}

div.outerclass {position: relative;}
div.innerclass {position:absolute; top: 0px; left: 0px;}

#easterEgg {
    color: red;
    cursor: pointer;
    font-size: 15px;
    font-style: italic;
    font-weight: bold;
    left: 176px;
    position: absolute;
    top: 5px;
    z-index: 10000;
}

#peakListsBox {
    border: 1px solid #DADADA;
    background-color: #F2F2F2;
    box-shadow: 0 3px 10px #C3C3C3;
    -moz-box-shadow: 0 3px 10px #C3C3C3;
    -webkit-box-shadow: 0 3px 10px #C3C3C3;
    display: block;
    padding: 25px 40px;
    font-family: Avenir,Trebuchet MS,sans-serif;
    margin-bottom: 70px;
}
#peakListsBox div.col {
    display: inline-block;
    float: left;
    padding: 0 10px;
    width: 350px;
    margin: 0 1px;
}
#peakListsBox div.col ul {
    margin-bottom: 25px;
    display: block;

}
#peakListsBox div.col ul li {
    font-size: 17px;
    color: #636363;
    line-height: 25px;

}
#peakListsBox div.col ul li.usa {
    padding-left: 30px;

}
#peakListsBox div.col ul li.usa:last-child {
    margin-bottom: 15px;
}
#peakListsBox div.col ul li span {
    color: #9d9d9d;
    margin-left: 3px;
}

#peakListsBox div.col ul li.title {
    font-weight: bold;
    color: #545454;
    font-size: 19px;
    margin-top: 15px;
}
#peakListsBox div.col ul li.title:first-child {
    margin-top: 0;
}

#peakListsBox div.col ul li a{
    color: #545454;
    text-decoration: none;

}

#peakListsBox div.col ul li a:hover{
    text-decoration: underline;

}
#peakListsBox div.col ul li.title span {
    color: #817f82;
}

p.timeSummited {
    padding: 5px 10px;
    background: #ecef67;
    width: auto !important;
    font-weight: bold;
    border: 1px solid #ccc;
    float: left;
    display: inline-block !important;
}
p.timeSummited span.ordinal {
    font-size: 11px !important;
    font-weight: bold !important;
    display: inline-block !important;
}
#userProfile_peakList {
    display: block;
    padding-left: 14px;
}
#userProfile_peakList li {
    margin: 5px 5px;
    width: 75px;
    height: 55px;
}
#homesearch input[type='text']{
    color: black !important;
}

span.right-top-edit-button {
    display: block;
}
span.right-top-edit-button a {
    color: #999999;
    font-size: 10px;
    font-weight: normal;
    height: 16px;
    padding-left: 20px;
    text-decoration: none;
}
span.right-top-edit-button a:hover {
    text-decoration: underline;
}

a.like_button {
    font-size: 14px;
    font-weight: normal;
    color: #FFF;
    text-decoration: none;
    text-transform: capitalize;
    display: inline-block;
    padding:  4px 14px;
    box-shadow: 0 1px 1px rgba(0,0,0,0.3);
    -moz-box-shadow: 0 1px 1px rgba(0,0,0,0.3);
    -webkit-box-shadow: 0 1px 1px rgba(0,0,0,0.3);
    background: #73bd33; /* Old browsers */
    background: -moz-linear-gradient(top, #73bd33 0%, #437712 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#73bd33), color-stop(100%,#437712)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #73bd33 0%,#437712 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #73bd33 0%,#437712 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #73bd33 0%,#437712 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#73bd33', endColorstr='#437712',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #73bd33 0%,#437712 100%); /* W3C */
}
a.like_button:hover {
    background: #437712; /* Old browsers */
    background: -moz-linear-gradient(top, #437712 0%, #73bd33 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#437712), color-stop(100%,#73bd33)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #437712 0%,#73bd33 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #437712 0%,#73bd33 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #437712 0%,#73bd33 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#437712', endColorstr='#73bd33',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #437712 0%,#73bd33 100%); /* W3C */
}
a.like_button span.likeIcon {
    width: 16px;
    height: 16px;
    display: inline-block;
    margin-right: 5px;
}
a.like_button.liked {
    background: #4f81bc; /* Old browsers */
    background: -moz-linear-gradient(top, #4f81bc 0%, #204a7e 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#4f81bc), color-stop(100%,#204a7e)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #4f81bc 0%,#204a7e 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #4f81bc 0%,#204a7e 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #4f81bc 0%,#204a7e 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#4f81bc', endColorstr='#204a7e',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #4f81bc 0%,#204a7e 100%); /* W3C */
}
a.like_button.liked:hover {
    background: #204a7e; /* Old browsers */
    background: -moz-linear-gradient(top, #204a7e 0%, #4f81bc 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#204a7e), color-stop(100%,#4f81bc)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #204a7e 0%,#4f81bc 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #204a7e 0%,#4f81bc 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #204a7e 0%,#4f81bc 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#204a7e', endColorstr='#4f81bc',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #204a7e 0%,#4f81bc 100%); /* W3C */
}

span.like-button {
    display: block;
    margin-bottom: 10px;
    float: right;
}
#contributors-headline {
  font-family: Avenir,Trebuchet MS,sans-serif;
  font-weight: 400;
  font-size: 40px;
  color: #404040;
  margin-bottom: 15px;
}
#contributors-subtext {
  font-size: 16px;
  color: #7F7F7F;
  margin-bottom: 20px;
  line-height: 30px;
}

#contributors-spacing {
  margin-bottom: 60px;
  font-size: 16px;
}

.somespace {
  margin-top: 10px;
}

.whitespace {
  margin: 2em 0;
}

.ad-leftcol {
  margin: auto auto 30px 20px;
}

.ad-peakpage-rightcol {
  margin: 18px auto auto 36px;
}
form span.a span.holder {
    margin: 0;
    padding: 0;
    color: #999999;
    cursor: text;
    font-size: 15px;
    font-weight: normal;
    left: 33px;
    position: absolute;
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
    top: 9px;
    white-space: nowrap;
    z-index: 1;
    line-height: 35px;
}
form span.a {
    position: relative;
}

form span.a {
    background: none repeat scroll 0 0 #FFFFFF;
    display: block;
    margin: 5px 0;
    position: relative;
}
form span.a input {
    border: 2px solid #DDDFE7;
    font-size: 15px;
    padding: 10px 10px 10px 30px;
}
form span.formError {
    display: block;
    color: red;
    font-size: 12px;
    margin: 5px 0;
}


#signInFormContainer {
    padding-top: 20px;
    padding-bottom: 5px;
}
#signInFormContainer a.a {
    display: block;
    margin: 10px 0;
    text-align: center;
}
#signInFormContainer a.a img {

}
#signInFormContainer h2 {
    display: block;
    margin: 0;
    padding: 0;
    text-align: center;
    margin-top: 11px;
}
#signInFormContainer form {
    margin: 0;
    padding:  0px;
    position: relative;
}
#signInFormContainer form span.a {
    background: none repeat scroll 0 0 #FFFFFF;
    display: block;
    margin: 5px 0;
    width: 220px;
    position: relative;
}
#signInFormContainer form span.a input {
    border: 2px solid #DDDFE7;
    font-size: 15px;
    padding: 10px 10px 10px 30px;
    width: 178px;
}
#signInFormContainer form span#forgotPassword {
    display: block;
    text-align: left;
}
#signInFormContainer form span#forgotPassword a {
    color: #333;
    line-height: normal;
    margin: 0;
    padding:  0;
    padding-left: 5px;
}
#signInFormContainer form span.b {
    display: block;
    margin-top: 10px;
    margin-bottom: 5px;
    text-align: center;
}
#signInFormContainer form span.b input {
    font-size: 15px;
    line-height: 25px;
    margin: 10px auto;
    padding: 10px 30px;
    text-align: center;
    text-transform: capitalize;
}

/*
=================================================================================================
    *** SPECIFIC ELEMENTS
=================================================================================================
*/
a#facebookButton, a#gmailButton {
    padding: 10px;
    background: #ffffff; /* Old browsers */
    background: -moz-linear-gradient(top, #ffffff 0%, #e9e9e9 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ffffff), color-stop(100%,#e9e9e9)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #ffffff 0%,#e9e9e9 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #ffffff 0%,#e9e9e9 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #ffffff 0%,#e9e9e9 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#e9e9e9',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #ffffff 0%,#e9e9e9 100%); /* W3C */
    border: 1px solid #d9d9d9;
}
a#facebookButton:hover, a#gmailButton:hover {
    background: #e9e9e9; /* Old browsers */
    background: -moz-linear-gradient(top, #e9e9e9 0%, #ffffff 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#e9e9e9), color-stop(100%,#ffffff)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #e9e9e9 0%,#ffffff 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #e9e9e9 0%,#ffffff 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #e9e9e9 0%,#ffffff 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#e9e9e9', endColorstr='#ffffff',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #e9e9e9 0%,#ffffff 100%); /* W3C */
}
a.redButton {
    padding: 5px 45px;
    background: #a90329; /* Old browsers */
    background: -moz-linear-gradient(top, #a90329 0%, #8f0222 44%, #6d0019 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#a90329), color-stop(44%,#8f0222), color-stop(100%,#6d0019)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #a90329 0%,#8f0222 44%,#6d0019 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #a90329 0%,#8f0222 44%,#6d0019 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #a90329 0%,#8f0222 44%,#6d0019 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#a90329', endColorstr='#6d0019',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #a90329 0%,#8f0222 44%,#6d0019 100%); /* W3C */
    box-shadow: 0 1px 2px rgba(0,0,0,0.3);
    -moz-box-shadow: 0 1px 2px rgba(0,0,0,0.3);
    -webkit-box-shadow: 0 1px 2px rgba(0,0,0,0.3);
}
a.redButton:hover {
    background: #6d0019; /* Old browsers */
    background: -moz-linear-gradient(top, #6d0019 0%, #8f0222 56%, #a90329 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#6d0019), color-stop(56%,#8f0222), color-stop(100%,#a90329)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #6d0019 0%,#8f0222 56%,#a90329 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #6d0019 0%,#8f0222 56%,#a90329 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #6d0019 0%,#8f0222 56%,#a90329 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#6d0019', endColorstr='#a90329',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #6d0019 0%,#8f0222 56%,#a90329 100%); /* W3C */
}
a#facebookButton {
    float: left;
    cursor: pointer;
}
a#gmailButton {
    display: inline-block;
    float: right;
}
span.helpText {
    display: inline-block !important;
    color: #555;
    margin: 0;
    padding: 0;
    padding-bottom: 0 !important;
    font-weight: normal;
    font-size: 11px;
    margin-left: 15px;
}



/*
=================================================================================================
    *** USER PROFILE EDIT
=================================================================================================
*/
#edit_settings h3.es-h3 {
    color: #333333;
    display: block;
    font-size: 20px;
    font-weight: normal;
    text-decoration: none;
    padding: 0;
    margin-top: 4px;
}
.tabs_content .tab {
    width: 440px;
}
#edit_settings a.es-a {
    color: #333333;
    display: block;
    font-size: 20px;
    font-weight: normal;
    margin-top: 25px;
    text-decoration: none;
}
#edit_settings a.es-a:hover {
    text-decoration: underline;
}
#edit_settings #notificationsSettings {
    padding:  20px 0px;
}
#edit_settings #notificationsSettings #notificationsSettingsWrapper {
    width: 440px;
    display: block;
}
#edit_settings #notificationsSettings #notificationsSettingsWrapper .leftCol {
    float: left;
    display: inline-block;
    width: 140px;
}
#edit_settings #notificationsSettings #notificationsSettingsWrapper .leftCol p {
    margin: 0;
    font-size: 13px;
    color: #111;
}
#edit_settings #notificationsSettings #notificationsSettingsWrapper .rightCol {
    float: right;
    display: inline-block;
    width: 290px;
    margin-left: 10px;
}
#edit_settings #notificationsSettings #notificationsSettingsWrapper .rightCol ul li{
    margin-bottom: 10px;
    display: block;
}
#edit_settings #notificationsSettings #notificationsSettingsWrapper .rightCol ul li span.checkboxWrapper {
    display: inline-block;
    float: left;
    margin: 5px 10px;
}
#edit_settings #notificationsSettings #notificationsSettingsWrapper .rightCol ul li label {
    display: inline-block;
    font-size: 12px;
    font-weight: normal;
    line-height: normal;
    margin: 0;
    padding: 0;
    text-align: left;
    width: 255px;
}
#edit_settings #notificationsSettings #notificationsSettingsWrapper .rightCol ul li label p {
    color: #333333;
    font-size: 13px;
    margin: 0;
}
#edit_settings #notificationsSettings #notificationsSettingsWrapper .rightCol ul li label span {
    color: #777;
    font-size: 11px;
    margin: 0;
    padding: 0;
}
#edit_settings .buttonsWrapper {
    width: 370px;
    margin: 0 36px;
}
#edit_settings p#otherIssues {
    display: block;
    text-align: center;
    color: #333;
    font-size: 17px;
    text-decoration: none;
    margin-top: 20px;
    margin-bottom: 0;
}



/*
=================================================================================================
    *** MEMBER LOGIN LIGHTBOX
=================================================================================================
*/
#peakeryLogin.lightbox {
    width: 250px;
    padding: 20px 15px;
    text-align: center;
}
#peakeryLogin.lightbox form.peakeryForm {
    width: 222px;
    margin: auto;
}


/*
=================================================================================================
    *** SIGNUP FACEBOX
=================================================================================================
*/
#signUpLightbox {
    display: block;
    width: 320px;
}
#signUpLightbox .main {
    display: block;
    padding: 40px 10px 5px 10px;
    text-align: center;
}
#signUpLightbox .main h1 {
    display: none;
}
#signUpLightbox .main a#emailSignUp {
    font-weight: bold;
    font-size: 18px;
    text-decoration: none;
}
#signUpLightbox .main a#emailSignUp:hover {
    text-decoration: underline;
}
#signUpLightbox .main span.alreadyAMember {
    display: block;
    margin-top: 40px;
    text-align: right;
}
#signUpLightbox .main span.alreadyAMember p {
    margin-bottom: 0;
}


/*
=================================================================================================
    *** COMMENT EDIT
=================================================================================================
*/
.comment_edit {
    width: 300px;
}
.comment_edit a#delete-comment {
    line-height: 40px;
    color: red;
    cursor: pointer;
}
.comment_edit.deleteComment {
    padding: 23px 0;
}
.comment_edit.deleteComment h1 {
    display: block;
    text-align: center;
    font-size: 18px;
}
.comment_edit.deleteComment .controls {
    display: block;
    text-align: center;
}
.comment_edit.deleteComment .controls .redButton {
    display: inline-block;
    float: left;
    margin-right: 10px;
    margin-left: 25px;
}
.comment_edit.deleteComment .controls a#cancel {
    display: inline-block;
    color: black;
    line-height: 30px;
    cursor: pointer;
}
.comment_edit.deleteComment .controls a#cancel:hover {
    text-decoration: underline;
}



/*
=================================================================================================
    *** SUMMIT BADGE PREVIEW
=================================================================================================
*/
#previewSummitLogLightbox {
    margin-bottom: 20px;
    box-shadow: 2px 2px 5px rgba(0,0,0,0.2);
    -moz-box-shadow: 2px 2px 5px rgba(0,0,0,0.2);
    -webkit-box-shadow: 2px 2px 5px rgba(0,0,0,0.2);
}
#previewSummitLogLightbox div.a {
    display: block;
}
#previewSummitLogLightbox div.a p.log {
    font-size: 13px;
    color: #222;
}
#previewSummitLogLightbox div.b {
    display: block;
}
#previewSummitLogLightbox div.b ul {
    margin: 10px 0;
}
#previewSummitLogLightbox div.b ul li {
    float: left;
    margin: 0 5px;
    width: 130px;
}
#previewSummitLogLightbox div.b ul li:first-child {
    margin-left: 40px;
}
#previewSummitLogLightbox div.b ul li div.upper {
    display: block;
}
#previewSummitLogLightbox div.b ul li div.lower {
    display: block;
}
#previewSummitLogLightbox div.b ul li div.lower span {
    font-size: 11px;
    color: #555;
    text-align: justify;
}



/*
=================================================================================================
    *** summitBoxStep2
=================================================================================================
*/
.facebox.summitBoxStep2 {
    width: 750px !important;
}
.facebox.summitBoxStep2 h2.title {
    font-size: 18px;
    font-weight: 700;
    color: #404040;
    text-shadow: 0 1px 1px #FFFFFF;
    display: block;
    margin-bottom: 10px;
}
.facebox.summitBoxStep2 fieldset {
    min-height: 160px;
}
.facebox.summitBoxStep2 fieldset span.a {
    width: 444px;
}
.facebox.summitBoxStep2 fieldset span.a input {
    width: 400px;
}
.facebox.summitBoxStep2 ul#users {
    width: 746px;
    padding: 10px 0;
    margin: auto;
    display: block;
}
.facebox.summitBoxStep2 ul#users li {
    display: inline-block;
    padding: 10px;
    margin: 5px 9px;
    position: relative;
    width: 210px;
    background: #ffffff;
    background: -moz-linear-gradient(top, #ffffff 0%, #f4f4f4 100%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ffffff), color-stop(100%,#f4f4f4));
    background: -webkit-linear-gradient(top, #ffffff 0%,#f4f4f4 100%);
    background: -o-linear-gradient(top, #ffffff 0%,#f4f4f4 100%);
    background: -ms-linear-gradient(top, #ffffff 0%,#f4f4f4 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#f4f4f4',GradientType=0 );
    background: linear-gradient(top, #ffffff 0%,#f4f4f4 100%);
    box-shadow: 0 2px 0 rgba(0,0,0,0.2);
    -moz-box-shadow: 0 2px 0 rgba(0,0,0,0.2);
    -webkit-box-shadow: 0 2px 0 rgba(0,0,0,0.2);
    min-height: 50px;
    float: left;
}
.facebox.summitBoxStep2 ul#users li.red {
    background: #f0dcde; /* Old browsers */
    background: -moz-linear-gradient(top, #f0dcde 0%, #c1514d 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f0dcde), color-stop(100%,#c1514d)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #f0dcde 0%,#c1514d 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #f0dcde 0%,#c1514d 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #f0dcde 0%,#c1514d 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f0dcde', endColorstr='#c1514d',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #f0dcde 0%,#c1514d 100%); /* W3C */
}
.facebox.summitBoxStep2 ul#users li div.user-lozenge {
    display: block;
    overflow: hidden;
}
.facebox.summitBoxStep2 ul#users li div.user-lozenge img {
    display: inline-block;
    float: left;
    margin-right: 10px;
}
.facebox.summitBoxStep2 ul#users li div.user-lozenge span {
    display: inline-block;
    width: 150px;
    overflow: visible;
}
.facebox.summitBoxStep2 ul#users li div.user-lozenge span p.username {
    display: block;
    font-size: 13px;
    color: #000;
    margin-bottom: 5px;
    font-weight: bold;
    width: 210px;
    height: 20px;
    overflow: hidden;
}
.facebox.summitBoxStep2 ul#users li div.user-lozenge span p.fullnameInputContainer input {
    background-attachment: scroll !important;
    background-clip: border-box !important;
    background-color: #FFFFFF;
    background-origin: padding-box !important;
    background-position: 5px center !important;
    background-repeat: no-repeat !important;
    background-size: auto auto !important;
    padding-left: 24px !important;
}
.facebox.summitBoxStep2 ul#users li div.user-lozenge span p.username.z {
    width: 151px;
    height: 20px;
    overflow: hidden;
}
.facebox.summitBoxStep2 ul#users li div.user-lozenge span p.fullname.z {
    width: 151px;
    height: 20px;
    overflow: hidden;
}
.facebox.summitBoxStep2 ul#users li div.user-lozenge span p.fullname {
    display: block;
    font-size: 12px;
    color: #333;
    margin-bottom: 0;
}
.facebox.summitBoxStep2 ul#users li div.user-lozenge span p.fullname input {
    background: none repeat scroll 0 0 #FFFFFF;
    border: 1px solid #CCCCCC;
    box-shadow: none;
    display: block;
    font-size: 11px;
    height: auto;
    width: 174px;
    padding: 4px 10px;
}
.facebox.summitBoxStep2 ul#users li a.remove-user {
    position: absolute;
    top: 3px;
    right: 3px;
    color: #666;
    text-decoration: none;
    cursor: pointer;
}
div.ac_results ul li {
    padding-top: 10px;
}
div.ac_results ul li img {
    float: left;
    margin-top: -10px;
    margin-right: 10px;
}
.facebox.summitBoxStep2 fieldset#routes div.routeUp span.a {
    display: inline-block;
    float: left;
}
.facebox.summitBoxStep2 fieldset#routes div.routeDown span.a {
    display: inline-block;
    float: left;
}
.facebox.summitBoxStep2 fieldset#routes div .arrowButton {
    display: inline-block;
    margin-left: 10px;
    margin-top: 6px;
    width: 32px;
    height: 40px;
    cursor: pointer;
    text-indent: -99999px;
    border: none;
    box-shadow: none;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    float: right;
}



/*
=================================================================================================
    *** WHEN DID YOU SUMMIT?
=================================================================================================
*/
.facebox.whenDidYouSummit {

}
.facebox.whenDidYouSummit fieldset.summitLogImages ul#summitlog-files li {
    display: inline-block;
    float: left;
    /*margin: 10px 20px;*/
}



/*
=================================================================================================
    *** SUMMIT 4 - summit_4.html
=================================================================================================
*/
.facebox.summitLogLast {
    width: 750px !important;
}
.facebox.summitLogLast .new_bag {
    height: auto;
}
.facebox.summitLogLast .new_bag .mainContainer {
    display: block;
}
.facebox.summitLogLast .new_bag .mainContainer .leftCol {
    display: inline-block;
    width: 320px;
    float: left;
    margin-right: 20px;
    text-align: center;
}
.facebox.summitLogLast .new_bag .mainContainer .leftCol div.a {
    display: block;
    float: none;
    margin: 5px 0;
}
.facebox.summitLogLast .new_bag .mainContainer .leftCol div.a h5 {
    font-size: 15px;
    margin-bottom: 8px;
    font-weight: normal;
    text-align: left;
}
.facebox.summitLogLast .new_bag .mainContainer .leftCol div.a h5 span.green-text {
    font-weight: bold;
    font-size: 16px;
}
.facebox.summitLogLast .new_bag .mainContainer .leftCol div.a p {
    margin: 0;
    font-size: 12px;
    color: #333;
    text-align: left;
}
.facebox.summitLogLast .new_bag .mainContainer .rightCol {
    display: inline-block;
    width: 410px;
    padding-top: 40px;
}
.facebox.summitLogLast .new_bag .mainContainer .rightCol ul.j li {
    display: block;
    margin-bottom: 10px;
}
.facebox.summitLogLast .new_bag .mainContainer .rightCol ul.j li:last-child {
    margin-top: 0;
}
.facebox.summitLogLast .new_bag .mainContainer .rightCol ul.j li .leftCol {
    float: left;
    display: inline-block;
    width: 70px;
    height: 70px;
    margin-right: 10px;
}
.facebox.summitLogLast .new_bag .mainContainer .rightCol ul.j li .leftCol span.big {
    color: green;
    font-size: 35px;
    font-weight: bold;
    line-height: 70px;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}
.facebox.summitLogLast .new_bag .mainContainer .rightCol ul.j li .rightCol {
    display: inline-block;
    margin: 0;
    padding: 0;
    width: 330px;
    padding-top: 24px;
}
.facebox.summitLogLast .new_bag .mainContainer .rightCol ul.j.k li .rightCol {
    padding-top: 0;
}
.facebox.summitLogLast .new_bag .mainContainer .rightCol ul.j li .rightCol span {
    font-weight: bold;
    font-size: 13px;
}
.facebox.summitLogLast .new_bag .mainContainer .rightCol ul.j li .rightCol span span.green-text {
    font-size: 15px;
}
.facebox.summitLogLast .new_bag .mainContainer .rightCol ul.j li .rightCol span.mini {
    font-weight: normal;
    font-size: 12px;
    color: #333;
    display: block;
}
.facebox.summitLogLast .new_bag .mainContainer .rightCol ul.j.k li .rightCol .progressbar {
    width: 170px;
    margin-right: 20px;
    overflow: visible;
    border: none;
}
.facebox.summitLogLast .new_bag .mainContainer .rightCol ul.j.k li .rightCol .progressbar .progress {
    background: #00B1F2;
    box-shadow: 1px 1px 3px rgba(0,0,0,0.5);
    -moz-box-shadow: 1px 1px 3px rgba(0,0,0,0.5);
    -webkit-box-shadow: 1px 1px 3px rgba(0,0,0,0.5);
    border: 1px solid #fff;
    float: left;
    text-align: center;
    color: #fff;
}
.facebox.summitLogLast .new_bag .mainContainer .rightCol ul.j.k li .rightCol .progressbar span.extra_info {
    font-weight: normal;
    font-style: normal;
    font-size: 11px;
    display: inline-block;
    line-height: 20px;
    margin-left: 20px;
    color: #333;
}
.facebox.summitLogLast .new_bag .mainContainer .rightCol div.remainingList {
    display: block;
    text-align: center;
    margin-top: -10px;
}
.facebox.summitLogLast .new_bag .mainContainer .rightCol div.remainingList a#remainingList {
    display: block;
    text-align: center;
    padding: 10px 0;
    font-size: 14px;
    color: #00B1F2;
    text-decoration: none;
}
.facebox.summitLogLast .new_bag .mainContainer .rightCol div.remainingList a#remainingList:hover {
    text-decoration: underline;
}
.facebox.summitLogLast .new_bag .block {
    display: block;
    float: none;
    margin-top: 20px;
    margin-bottom: 0;
}
.facebox.summitLogLast .new_bag .block p.sharing {
    margin: 0;
    color: #333;
    margin-bottom: 20px;
}
.facebox.summitLogLast .new_bag .block p.sharing strong {
    margin-right: 20px;
    color: #000;
}
.facebox.summitLogLast .new_bag .socialButtons .btn.soc {
    box-shadow: 0px 1px 0 rgba(255,255,255,0.3);
    -moz-box-shadow: 0px 1px 0 rgba(255,255,255,0.3);
    -webkit-box-shadow:  0px 1px 0 rgba(255,255,255,0.3);
    font-size: 13px;
    font-weight: normal;
    margin-right: 15px;
    padding: 8px 10px;
    line-height: 19px;
    background: #ffffff; /* Old browsers */
    background: -moz-linear-gradient(top, #ffffff 0%, #f3f3f3 50%, #ededed 51%, #ffffff 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ffffff), color-stop(50%,#f3f3f3), color-stop(51%,#ededed), color-stop(100%,#ffffff)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #ffffff 0%,#f3f3f3 50%,#ededed 51%,#ffffff 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #ffffff 0%,#f3f3f3 50%,#ededed 51%,#ffffff 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #ffffff 0%,#f3f3f3 50%,#ededed 51%,#ffffff 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#ffffff',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #ffffff 0%,#f3f3f3 50%,#ededed 51%,#ffffff 100%); /* W3C */
    color: #000;
}
.facebox.summitLogLast .new_bag .socialButtons .btn.soc:hover {
    background: #ffffff; /* Old browsers */
    background: -moz-linear-gradient(top, #ffffff 0%, #ededed 49%, #f3f3f3 50%, #ffffff 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ffffff), color-stop(49%,#ededed), color-stop(50%,#f3f3f3), color-stop(100%,#ffffff)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #ffffff 0%,#ededed 49%,#f3f3f3 50%,#ffffff 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #ffffff 0%,#ededed 49%,#f3f3f3 50%,#ffffff 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #ffffff 0%,#ededed 49%,#f3f3f3 50%,#ffffff 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#ffffff',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #ffffff 0%,#ededed 49%,#f3f3f3 50%,#ffffff 100%); /* W3C */
    color: #000;
}
.facebox.summitLogLast .new_bag .socialButtons .btn.soc.em-btn-active,
.facebox.summitLogLast .new_bag .socialButtons .btn.soc.fb-btn-active,
.facebox.summitLogLast .new_bag .socialButtons .btn.soc.tw-btn-active {
    background: #63abf7; /* Old browsers */
    background: -moz-linear-gradient(top, #63abf7 0%, #5e99ce 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#63abf7), color-stop(100%,#5e99ce)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #63abf7 0%,#5e99ce 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #63abf7 0%,#5e99ce 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #63abf7 0%,#5e99ce 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#63abf7', endColorstr='#5e99ce',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #63abf7 0%,#5e99ce 100%); /* W3C */
    color: #fff;
}
.facebox.summitLogLast .new_bag .socialButtons .btn.soc.em-btn-active:hover,
.facebox.summitLogLast .new_bag .socialButtons .btn.soc.fb-btn-active:hover,
.facebox.summitLogLast .new_bag .socialButtons .btn.soc.tw-btn-active:hover {
    background: #63abf7; /* Old browsers */
    background: -moz-linear-gradient(top, #63abf7 0%, #5e99ce 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#63abf7), color-stop(100%,#5e99ce)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #63abf7 0%,#5e99ce 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #63abf7 0%,#5e99ce 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #63abf7 0%,#5e99ce 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#63abf7', endColorstr='#5e99ce',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #63abf7 0%,#5e99ce 100%); /* W3C */
    color: #fff;
}
.facebox.summitLogLast .new_bag .socialButtons .btn.soc .icon {
    margin: 0;
    padding: 0;
    background: none;
    display: inline-block;
    float: left;
    margin-top: -2px;
    margin-right: 10px;
    width: 24px;
    height: 24px;
}

.facebox.summitLogLast .new_bag .socialButtons .btn.soc span.activate {
    width: 24px;
    height: 24px;
    margin: -2px 5px 0 -8px;
    float: left;
    display: none;
}



/*
=================================================================================================
    *** email send list
=================================================================================================
*/
div#route_up_container div.selector span {
    display: inline-block;
    float: left;
}
div#route_down_container div.selector span.leftArrow {
    display: inline-block;
    width: 30px;
    height: 30px;
    margin-right: 10px;
}
div#route_down_container div.selector span.dropDown {
    display: inline-block;
    width: 30px;
    height: 30px;
}
div#route_up_container div.selector span.leftArrow {
    display: inline-block;
    width: 30px;
    height: 30px;
    margin-right: 10px;
}
div#route_up_container div.selector span.dropDown {
    display: inline-block;
    width: 30px;
    height: 30px;
}



/*
=================================================================================================
    *** email send list
=================================================================================================
*/
.facebox.emailSendList p.ad {
    font-size: 12px;
    color: #333;
}
.facebox.emailSendList p.ad strong {
    font-size: 14px;
    color: #000;
    margin-right: 20px;
}
.facebox.emailSendList form#emailList {
    width: 380px;
    margin-bottom: 0;
}
.facebox.emailSendList form#emailList ul {
    overflow: scroll;
    height: 281px;
    width: 360px;
    padding: 10px;
    margin: 0 auto 10px auto;
}
.facebox.emailSendList form#emailList ul li {
    display: block;
    float: none;
    margin-bottom: 10px;
}
.facebox.emailSendList form#emailList ul li:last-child {
    margin-bottom: 0;
}
.facebox.emailSendList form#emailList ul li a.toggleEmailList {
    display: block;
    padding: 10px 5px;
    background: none;
}
.facebox.emailSendList form#emailList ul li a.toggleEmailList.deactivate {
    background: #63abf7; /* Old browsers */
    background: -moz-linear-gradient(top, #63abf7 0%, #5e99ce 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#63abf7), color-stop(100%,#5e99ce)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #63abf7 0%,#5e99ce 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #63abf7 0%,#5e99ce 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #63abf7 0%,#5e99ce 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#63abf7', endColorstr='#5e99ce',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #63abf7 0%,#5e99ce 100%); /* W3C */
}
.facebox.emailSendList form#emailList ul li a.toggleEmailList:hover {
    background: #5e99ce; /* Old browsers */
    background: -moz-linear-gradient(top, #5e99ce 0%, #63abf7 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#5e99ce), color-stop(100%,#63abf7)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #5e99ce 0%,#63abf7 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #5e99ce 0%,#63abf7 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #5e99ce 0%,#63abf7 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#5e99ce', endColorstr='#63abf7',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #5e99ce 0%,#63abf7 100%); /* W3C */
}
.facebox.emailSendList form#emailList ul li a.toggleEmailList:hover span.container span {
    color: #fff !important;
}
.facebox.emailSendList form#emailList ul li a.toggleEmailList.deactivate span {
    color: #fff;
}
.facebox.emailSendList form#emailList ul li a.toggleEmailList span {
    color: #000;
}
.facebox.emailSendList form#emailList ul li a.toggleEmailList span.container {
    display: block;
}
.facebox.emailSendList form#emailList ul li a.toggleEmailList span.container span {
    font-size: 12px;
    float: left;
}
.facebox.emailSendList form#emailList ul li a.toggleEmailList span.container span.activate {
    width: 16px;
    height: 16px;
    margin-right: 10px;
    display: inline-block;
}
.facebox.emailSendList form#emailList ul li a.toggleEmailList span.container span.name {
    width: 100px;
    margin-right: 10px;
}
.facebox.emailSendList form#emailList ul li a.toggleEmailList span.container span.email {
    width: 195px;
}
.facebox.emailSendList form#emailList a.clickToAddEmail {
    display: block;
    text-align: center;
    line-height: 45px;
    color: #555;
    margin-bottom: 20px;
    text-decoration: none;
    background: #ffffff; /* Old browsers */
    background: -moz-linear-gradient(top, #ffffff 0%, #f4f4f4 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#ffffff), color-stop(100%,#f4f4f4)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #ffffff 0%,#f4f4f4 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #ffffff 0%,#f4f4f4 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #ffffff 0%,#f4f4f4 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#f4f4f4',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #ffffff 0%,#f4f4f4 100%); /* W3C */
}
.facebox.emailSendList form#emailList a.clickToAddEmail:hover {
    background: #f4f4f4; /* Old browsers */
    background: -moz-linear-gradient(top, #f4f4f4 0%, #ffffff 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#f4f4f4), color-stop(100%,#ffffff)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #f4f4f4 0%,#ffffff 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #f4f4f4 0%,#ffffff 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #f4f4f4 0%,#ffffff 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f4f4f4', endColorstr='#ffffff',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #f4f4f4 0%,#ffffff 100%); /* W3C */
}
.facebox.emailSendList form#emailList a#sendlist-done {
    float: right;
}
/* fix margin problem */
div.summitlogs div.summitlog {
    margin-bottom: 0 !important;
}


/* Summit log likes & comments section: */
.user-meta {
    display: block;
    width: 725px;
    margin: 0 auto;
}
.user-meta .col {
    display: inline-block;
}
.user-meta .leftCol {
    float: left;
    width: 85px;
    margin-right: 20px;
}
.user-meta .leftCol img {
    box-shadow: 2px 2px 2px rgba(0,0,0,0.2);
    -moz-box-shadow: 2px 2px 2px rgba(0,0,0,0.2);
    -webkit-box-shadow: 2px 2px 2px rgba(0,0,0,0.2);
}
.user-meta .centerCol {
    float: left;
    width: 445px;
}
.user-meta .rightCol {
    float: right;
    width: 170px;
}
.log p {
    margin-bottom: 0;
}
div.comments {
    margin-top: 25px;
}
div.comments ul.comments li.comment {
    display: block;
    width: 725px;
    margin: 0 auto;
    margin-bottom: 10px;
}
div.comments ul.comments li.comment .leftCol {
    width: 40px;
    margin-right: 10px;
    display: inline-block;
    float: left;
}
div.comments ul.comments li.comment .leftCol img {
    margin: 0;
    padding: 0;
    box-shadow: none;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
}
div.comments ul.comments li.comment .rightCol {
    border: none;
    box-shadow: none;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    border-radius: 0;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    margin: 0;
    padding: 0;
    width: 642px;
    background: none;
}
div.comments ul.comments li.comment .rightCol h4.userName {
    display: inline-block;
    float: none;
    margin: 0;
    padding: 0;
    text-align: center;
    font-size: 15px;
    font-weight: bold;
    color: #222;
}
div.comments ul.comments li.comment .rightCol h4.userName span.date {
    margin-left: 10px;
    font-size: 11px;
    font-weight: normal;
    color: #888;
}
div.comments ul.comments li.comment .rightCol span.editComment {
    float: right;
}
div.comments ul.comments li.comment .rightCol span.editComment a.edit {
    width: 16px;
    height: 16px;
    padding-left: 20px;
    color: #999;
    text-decoration: none;
    font-weight: normal;
    font-size: 10px;
}
div.comments ul.comments li.comment .rightCol span.editComment a.edit:hover {
    text-decoration: underline;
}
div.comments ul.comments li.comment .rightCol .comment_text {
    display: block;
    margin-top: 5px;
}
div.comments ul.comments li.comment .rightCol .comment_text p.commentParagraph {
    text-align: justify;
    margin-bottom: 0;
}
div.comments ul.comments li#summit_comment_form {
    display: block;
    margin-bottom: 0;
    padding-bottom: 0;
}
div.comments ul.comments li#summit_comment_form .rightCol textarea {
    background: #FFF;
    border: 1px solid #CCC;
    border-radius: 8px;
    -moz-border-radius: 8px;
    -webkit-border-radius: 8px;
    box-shadow: none;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    height: 20px;
    padding: 10px;
    resize: none;
    width: 653px;
    font-size: 12px;
}
div.comments ul.comments li#summit_comment_form .rightCol textarea.summitLogByUserTextarea {
    width: 620px;
}
div.comments ul.comments li#summit_comment_form form {
    margin-bottom: 0 !important;
}




ul.userProfileSummitLog li div.upper {
    display: block;
}
ul.userProfileSummitLog li div.upper .peak-info {
    width: 550px;
    float: left;
    display: inline-block;
    margin-right: 5px;
}
ul.userProfileSummitLog li div.upper .peak-info a {
    float: left;
}
ul.userProfileSummitLog li div.upper .peak-info div.info {
    display: inline-block;
    width: 420px;
    float: left;
}
ul.userProfileSummitLog li div.upper .peak-info div.info p {
    margin-top: 7px;
    margin-bottom: 12px;
    float: none;
    display: block;
    text-align: left;
    font-weight: bold;
    font-size: 17px;
    width: 100%;
}
ul.userProfileSummitLog li div.upper .peak-info div.info span {
    display: block;
    float: none;
    color: #333;
}
ul.userProfileSummitLog li div.upper .peak-info div.info span.a {
    margin-bottom: 3px;
}
ul.userProfileSummitLog li div.upper .edit-and-share-options {
    display: inline-block;
    width: 130px;
}
ul.userProfileSummitLog li div.upper .edit-and-share-options div.editYourLog {
    display: block;
    float: none;
    text-align: right;
    margin-bottom: 10px;
}
ul.userProfileSummitLog li div.upper .edit-and-share-options div span.editComment {
    float: right;
    display: block;
}
ul.userProfileSummitLog li div.upper .edit-and-share-options div a.edit {
    width: 16px;
    height: 16px;
    padding-left: 20px;
    text-decoration: none;
    font-weight: normal;
    color: #999;
    font-size: 10px;
}
ul.userProfileSummitLog li div.upper .edit-and-share-options ul {
    text-align: right;
    display: block;
    float: right;
    text-align: right;
    width: 150px;
}
ul.userProfileSummitLog li div.upper .edit-and-share-options ul li {
    float: right;
    margin: 0 3px;
}
ul.userProfileSummitLog li div.upper .edit-and-share-options ul li span {

}
ul.userProfileSummitLog li div.upper span.editComment a.edit:hover {
    text-decoration: underline;
}
ul.userProfileSummitLog li div.lower {
    margin-top: 10px;
    display: block;
}
.ac_results {
	padding: 0px;
	border: 1px solid black;
	background-color: white;
	overflow: hidden;
	z-index: 99999;
}

.ac_results ul {
	width: 100%;
	list-style-position: outside;
	list-style: none;
	padding: 0;
	margin: 0;
}

.ac_results li {
	margin: 0px;
	padding: 2px 5px;
	cursor: default;
	display: block;
	/*
	if width will be 100% horizontal scrollbar will apear
	when scroll mode will be used
	*/
	/*width: 100%;*/
	font: menu;
	font-size: 12px;
	/*
	it is very important, if line-height not setted or setted
	in relative units scroll will be broken in firefox
	*/
	line-height: 16px;
	overflow: hidden;
}

.ac_odd {
	background-color: #eee;
}

.ac_over {
	background-color: #0A246A;
	color: white;
}
ul#summit-logs {
    display: block;
    width: 100%;
    width: 100%;
    margin-top: 10px;
    font-family: Avenir,Trebuchet MS,sans-serif;
}
ul#summit-logs li {
    display: block;
    margin-bottom: 10px;
    display: block;
    background-color: #fff;
}
div.summit-log-wrapper {
    display: block;
    padding: 10px;
}
div.summit-log-header {
    display: block;
    margin-bottom: 20px;
}
div.summit-log-header div.leftCol {
    width: 85px;
    float: left;
    display: inline-block;
    margin-right: 10px;

}
div.summit-log-header div.leftCol a.user-avatar {
    display: block;
    width: 85px;
    height: 85px;
    cursor: pointer;

}
div.summit-log-header div.leftCol a.user-avatar img {
    box-shadow: 2px 2px 2px #B3B3B3;
    -moz-box-shadow: 2px 2px 2px #B3B3B3;
    -webkit-box-shadow: 2px 2px 2px #B3B3B3;

}
div.summit-log-header div.centerCol {
    display: inline-block;
    width: 470px;

}
div.summit-log-header div.centerCol a.username {
    color: #404040;
    font-family: Avenir,Trebuchet MS,sans-serif;
    text-decoration: none;
    font-weight: 700;
    font-size: 18px;
    display: block;
    margin-bottom: 5px;

}
div.summit-log-header div.centerCol span.info {
    display: block;
    margin-bottom: 10px;

}
div.summit-log-header div.centerCol span.info p {
    margin-bottom: 3px;
}
div.summit-log-header div.centerCol p.timeSummited {
    margin: 0;

}
div.summit-log-header div.centerCol p.timeSummited span {
    display: block;

}
div.summit-log-header div.rightCol {
    display: inline-block;
    float: right;
    text-align: right;

}
div.summit-log-header div.rightCol span.like-button {
    display: block;
    margin-bottom: 10px;

}
div.summit-log-header div.rightCol span.like-button p.likesCounter {
    display: inline-block;
    padding-right: 5px;
}
div.summit-log-header div.rightCol ul.options {
    display: block;

}
div.summit-log-header div.rightCol ul.options li {
    float: right;
    margin-left: 5px;

}
div.summit-log-header div.rightCol ul.options li span {
    line-height: 25px;

}
div.summit-log-header div.rightCol ul.options li.icon {

}
div.summit-log-header div.rightCol ul.options li.icon span {
    display: block;

}
div.summit-log-header div.rightCol ul.options li.icon span a {
    display: block;
    width: 25px;
    height: 25px;
    cursor: pointer;

}
div.summit-log-header div.rightCol span.edit-summit-log {
    display: block;
    margin-bottom: 10px;
}
div.summit-log-header div.rightCol span.edit-summit-log a {
    color: #999999;
    font-size: 10px;
    font-weight: normal;
    height: 16px;
    padding-left: 20px;
    text-decoration: none;
}
div.summit-log-header div.rightCol span.edit-summit-log a:hover {
    text-decoration: underline;
}
div.summit-log-body {
    
}
div.summit-log-body div.log {
    display: block;
    margin-bottom: 20px;

}
div.summit-log-body div.log p {
    line-height: 20px;
    text-align: justify;
    display: block;
}
div.summit-log-body div.routes {
    display: block;
    margin-bottom: 20px;
}
div.summit-log-body div.routes p {
    margin: 0;
}
div.summit-log-body div.companions {
    display: block;
    margin-bottom: 20px;
}
div.summit-log-body div.companions p:first-child {
    margin-bottom: 10px;
}
div.summit-log-body div.companions ul.top-companions {
    margin: 0;
}
div.summit-log-body div.photos {
    margin-bottom: 20px;

}
ul.summit-log-photos {
    display: block;
    margin-bottom: 10px;
}
ul.summit-log-photos:last-child {
    margin-bottom: 0;
}
ul.summit-log-photos li {
    display: inline-block;
    float: left;
    margin: 0 5px;
    width: 230px;

}
ul.summit-log-photos li div.image {
    display: block;
    margin-bottom: 7px;

}
ul.summit-log-photos li div.image a {
    display: block;

}
ul.summit-log-photos li div.image a img {

}
ul.summit-log-photos li div.image-description {
    display: block;
    max-height: 70px;
}
ul.summit-log-photos li div.image-description span {
    color: #666666;
    font-size: 11px;
    font-weight: normal;
    margin-bottom: 0;
}
div.summit-log-comments {

}
ul.comments {
    
}
ul.comments li {
    padding: 0;
    margin-bottom: 20px !important;

}
ul.comments li div.leftCol {
    width: 40px;
    height: 40px;
    display: inline-block;
    float: left;
    margin-right: 10px;

}
ul.comments li div.leftCol a {
    display: block;
}
ul.comments li div.rightCol {
    float: right;
    display: inline-block;
    width: 670px;
    height: auto;
    margin: 0;
    padding: 0;

}
ul.comments li div.rightCol div.upper {
    display: block;
    margin-bottom: 5px;

}
ul.comments li div.rightCol div.upper h4.username {
    display: inline-block;
    float: left;
    color: #222;
    font-size: 15px;
    margin: 0;

}
ul.comments li div.rightCol div.upper h4.username span {
    color: #888;
    font-size: 11px;
    font-weight: normal;
    margin-left: 10px;

}
ul.comments li div.rightCol div.upper span.edit-comment {
    display: inline-block;
    float: right;

}
ul.comments li div.rightCol div.upper span.edit-comment a.edit-comment {
    color: #999999;
    font-size: 10px;
    font-weight: normal;
    height: 16px;
    padding-left: 20px;
    text-decoration: none;
}
ul.comments li div.rightCol div.upper span.edit-comment a.edit-comment:hover {
    text-decoration: underline;
}
ul.comments li div.rightCol div.lower {
    display: block;

}
ul.comments li div.rightCol div.lower p.comment {
    display: block;
    text-align: justify;
    margin: 0;

}
ul.comments li div.rightCol.textarea-wrapper {

}
ul.comments li div.rightCol.textarea-wrapper span.spinner {
    z-index: 1000;
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
ul.comments li div.rightCol.textarea-wrapper textarea {
    background: none repeat scroll 0 0 #FFFFFF;
    border: 1px solid #CCCCCC;
    box-shadow: none;
    font-size: 12px;
    height: 20px;
    padding: 10px;
    resize: none;
    width: 648px;
}
.summit-logs li {
    margin-bottom: 50px;
    display: block;

}
.summit-logs li .leftColumn {
    width: 380px;
    display: inline-block;
    float: left;
    margin-right: 10px;
    min-height: 1px;
}
.summit-logs li .rightColumn {
    display: inline-block;
    width: 670px;
    float: right;
}
.endless_container{
    text-align: center;
}
.endless_more {
    font-family: Avenir,Trebuchet MS,sans-serif;
    font-size: 17px;
    font-weight: 700;
    text-decoration: none;
}

div#slider-range,
div#prominence-slider-range {
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  height: 2px;
  overflow: visible;
  background-color: #ccc;
}
div#slider-range .ui-slider-range,
div#prominence-slider-range .ui-slider-range {
  background-color: #00a2de;
}
div#slider-range .ui-slider-handle,
div#prominence-slider-range .ui-slider-handle {
  display: block;
  width: 16px;
  height: 16px;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  border-radius: 8px;
  background-color: #00b2f2;
}
.block {
  overflow: visible !important;
}
#elevation-slider {
  padding-top: 10px;
  margin-left: 8px;
}
#elevation-slider p {
  margin-bottom: 10px;
  *zoom: 1;
  display: block;
}
#elevation-slider p:before,
#elevation-slider p:after {
  display: table;
  content: "";
}
#elevation-slider p:after {
  clear: both;
}
#elevation-slider input#amount {
  font-weight: normal;
  text-align: left;
  color: #404040;
}
#prominence-slider {
  padding-top: 10px;
  margin-left: 8px;
}
#prominence-slider p {
  margin-bottom: 10px;
  *zoom: 1;
  display: block;
}
#prominence-slider p:before,
#prominence-slider p:after {
  display: table;
  content: "";
}
#prominence-slider p:after {
  clear: both;
}
#prominence-slider input#prominence-amount {
  font-weight: normal;
  text-align: left;
  color: #404040;
}
