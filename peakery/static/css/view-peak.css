/*
    *** View Peak page Styles
    *** Peakery.com
*/



/* Specific elements: */
span.likeButtonWrapper {
    float: right;
    padding:  5px;
    text-align: right;
    margin-bottom: 8px;
}
span.likeButtonWrapper p.likesCounter {
    color: #333333;
    display: inline-block;
    float: none !important;
    font-size: 12px;
    margin-right: 5px !important;
    width: auto !important;
}
span.likeButtonWrapper p.likesCounter span {
    font-size: 13px;
    color: #222;
}
a.like_button {
    font-size: 14px;
    font-weight: normal;
    color: #FFF;
    text-decoration: none;
    text-transform: capitalize;
    display: inline-block;
    padding:  4px 14px;
    box-shadow: 0 1px 1px rgba(0,0,0,0.3);
    -moz-box-shadow: 0 1px 1px rgba(0,0,0,0.3);
    -webkit-box-shadow: 0 1px 1px rgba(0,0,0,0.3);
    background: #73bd33; /* Old browsers */
    background: -moz-linear-gradient(top, #73bd33 0%, #437712 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#73bd33), color-stop(100%,#437712)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #73bd33 0%,#437712 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #73bd33 0%,#437712 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #73bd33 0%,#437712 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#73bd33', endColorstr='#437712',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #73bd33 0%,#437712 100%); /* W3C */
}
a.like_button:hover {
    background: #437712; /* Old browsers */
    background: -moz-linear-gradient(top, #437712 0%, #73bd33 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#437712), color-stop(100%,#73bd33)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #437712 0%,#73bd33 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #437712 0%,#73bd33 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #437712 0%,#73bd33 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#437712', endColorstr='#73bd33',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #437712 0%,#73bd33 100%); /* W3C */
}
a.like_button span.likeIcon {
    width: 16px;
    height: 16px;
    display: inline-block;
    background: url("../img/icn/like.png") no-repeat top left transparent;
    margin-right: 5px;
}
a.like_button.liked {
    background: #4f81bc; /* Old browsers */
    background: -moz-linear-gradient(top, #4f81bc 0%, #204a7e 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#4f81bc), color-stop(100%,#204a7e)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #4f81bc 0%,#204a7e 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #4f81bc 0%,#204a7e 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #4f81bc 0%,#204a7e 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#4f81bc', endColorstr='#204a7e',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #4f81bc 0%,#204a7e 100%); /* W3C */
}
a.like_button.liked:hover {
    background: #204a7e; /* Old browsers */
    background: -moz-linear-gradient(top, #204a7e 0%, #4f81bc 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#204a7e), color-stop(100%,#4f81bc)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #204a7e 0%,#4f81bc 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #204a7e 0%,#4f81bc 100%); /* Opera11.10+ */
    background: -ms-linear-gradient(top, #204a7e 0%,#4f81bc 100%); /* IE10+ */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#204a7e', endColorstr='#4f81bc',GradientType=0 ); /* IE6-9 */
    background: linear-gradient(top, #204a7e 0%,#4f81bc 100%); /* W3C */
}
a.like_button.liked span.likeIcon {
    background: url("../img/icn/liked.png") no-repeat top left transparent;
}


/* fix margin problem */
div.summitlogs div.summitlog {
    margin-bottom: 0 !important;
}


/* Summit log likes & comments section: */
.user-meta {
    display: block;
    width: 725px;
    margin: 0 auto;
}
.user-meta .col {
    display: inline-block;
}
.user-meta .leftCol {
    float: left;
    width: 85px;
    margin-right: 20px;
}
.user-meta .leftCol img {
    box-shadow: 2px 2px 2px rgba(0,0,0,0.2);
    -moz-box-shadow: 2px 2px 2px rgba(0,0,0,0.2);
    -webkit-box-shadow: 2px 2px 2px rgba(0,0,0,0.2);
}
.user-meta .centerCol {
    float: left;
    width: 445px;
}
.user-meta .centerCol ul.options {
    width: 160px !important;
}
.user-meta .centerCol ul.options li {
    display: inline-block;
    float: left;
    margin: 0;
    padding: 0;
    line-height: 25px;
}
.user-meta .rightCol {
    float: right;
    width: 170px;
}
.user-meta .rightCol a.editSummit, .user-meta .centerCol a.editSummit {
    display: inline-block;
    text-align: right;
    width: 16px;
    height: 16px;
    background: url("../img/icn/edit_icn1.png") no-repeat center left transparent;
    padding-left: 20px;
    color: #999;
    text-decoration: none;
    font-weight: normal;
    font-size: 10px;
    margin: 0 14px 0 0;
}
.user-meta .rightCol a.editSummit:hover, .user-meta .centerCol a.editSummit:hover {
    text-decoration: underline;
}
.user-meta .rightCol ul.options {
    width: 170px !important;
    display: block;
}
.user-meta .rightCol ul.options li.time-summited,
.user-meta .rightCol ul.options li.timeSummited {
    display: block;
    float: none !important;
    margin-bottom: 5px;
    text-align: right;
}
.log p {
    margin-bottom: 0;
}
div.comments {
    margin-top: 5px;
    margin-bottom: 40px;
}
div.comments ul.comments li.comment {
    display: block;
    width: 725px;
    margin: 0 auto;
    margin-bottom: 10px;
}
div.comments ul.comments li.comment .leftCol {
    width: 40px;
    margin-right: 10px;
    display: inline-block;
    float: left;
}
div.comments ul.comments li.comment .leftCol img {
    margin: 0;
    padding: 0;
    box-shadow: none;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
}
div.comments ul.comments li.comment .rightCol {
    border: none;
    box-shadow: none;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    border-radius: 0;
    -moz-border-radius: 0;
    -webkit-border-radius: 0;
    margin: 0;
    padding: 0;
    width: 642px;
    background: none;
}
div.comments ul.comments li.comment .rightCol h4.userName {
    display: inline-block;
    float: none;
    margin: 0;
    padding: 0;
    text-align: center;
    font-size: 15px;
    font-weight: bold;
    color: #222;
}
div.comments ul.comments li.comment .rightCol h4.userName span.date {
    margin-left: 10px;
    font-size: 11px;
    font-weight: normal;
    color: #888;
}
div.comments ul.comments li.comment .rightCol span.editComment {
    float: right;
}
div.comments ul.comments li.comment .rightCol span.editComment a.edit {
    width: 16px;
    height: 16px;
    background: url("../img/icn/edit_icn1.png") no-repeat center left transparent;
    padding-left: 20px;
    color: #999;
    text-decoration: none;
    font-weight: normal;
    font-size: 10px;
}
div.comments ul.comments li.comment .rightCol span.editComment a.edit:hover {
    text-decoration: underline;
}
div.comments ul.comments li.comment .rightCol .comment_text {
    display: block;
    margin-top: 5px;
}
div.comments ul.comments li.comment .rightCol .comment_text p.commentParagraph {
    text-align: justify;
    margin-bottom: 0;
}
div.comments ul.comments li#summit_comment_form {
    display: block;
    margin-bottom: 0;
    padding-bottom: 0;
}
div.comments ul.comments li#summit_comment_form .rightCol textarea {
    background: #FFF;
    border: 1px solid #CCC;
    border-radius: 8px;
    -moz-border-radius: 8px;
    -webkit-border-radius: 8px;
    box-shadow: none;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    height: 20px;
    padding: 10px;
    resize: none;
    width: 653px;
    font-size: 12px
}
div.comments ul.comments li#summit_comment_form form {
    margin-bottom: 0 !important;
}
div.summit-info div.a.clearfix {
    margin-bottom: 25px;
    display: block;
}
div.summit-info div.a.clearfix p {
    color: #333;
    font-size: 14px;
    margin-bottom: 20px;
}

div.summit-info div.a p:first-child {
    width: 100%;
    text-align: justify;
    overflow: hidden;
    font-family: Avenir,Trebuchet MS,sans-serif;
}


div.summit-info div.b.clearfix {
    display: block;
    margin-bottom: 25px;
}
ul.peak_photos {
    display: block;
}
ul.peak_photos li {
    display: inline-block;
    float: left;
    padding: 5px;
    width: 230px;
    margin: 0 1px;
}
ul.peak_photos li:first-child {
}
ul.peak_photos li.w160 {
    width: 160px;
    margin: 0 15px;
}
ul.peak_photos li div.a {
    display: block;
}

ul.peak_photos li div.b {
    display: block;
    margin-top: 5px;
}

ul.peak_photos li div.h55 {
    height: 70px;
}
ul.peak_photos li div.h55 p {
    font-size: 10px;
    line-height: 15px;
}


ul.peak_photos li div.b span.desc {
    font-size: 11px;
    color: #666;
    font-weight: normal;
    margin-bottom: 0;
}

ul#peak-photos.viewPeak {
    margin: 0;
}
ul#peak-photos.viewPeak li {
    width: 230px;
    height: 160px;
    display: inline-block;
    float: left;
    background-repeat: no-repeat;
    background-position: center center;
    margin: 8px 14px;
    position: relative;
}
ul#peak-photos.viewPeak li span.data {
    background-color: rgba(0, 0, 0, 0.5);
    position: absolute;
    width: 100%;
    height: auto;
    bottom: 0;
    color: #FFFFFF;
}
ul#peak-photos.viewPeak li span.data p {
    font-weight: normal;
    text-align: left;
    display: block;
    font-size: 11px;
    color: #FFFFFF;
    margin: 0;
    padding: 0;
    line-height: normal;
}
ul#peak-photos.viewPeak li span.data p.bagger {
    font-weight: bold;
    line-height: 23px;
    padding: 0 10px;
}
ul#peak-photos.viewPeak li span.data p.caption {
    padding: 0 10px;
    height: 47px;
    overflow: hidden;
    display: none;
}
ul#peak-photos.viewPeak li a.photoLink {
    position: absolute;
    display: block;
    z-index: 10;
    width: 230px;
    height: 160px;
    border: none;
    background: none;
    padding: 0;
    margin: 0;
}
div#slideshowButton {
    display: block;
    text-align: right;
    padding: 5px 0;
}
a#seeSlideshow {
    color: #666;
}
a#seeSlideshow:hover {
    background-position: left bottom;
}
a#seeMorePhotos {
    display: block;
    margin: 20px 0 10px;
    background: #fff;
    padding: 10px;
    border-radius: 10px;
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
    text-decoration: none;
    text-transform: uppercase;
    font-weight: bold;
    color: #00B1F2;
}
a#seeMorePhotos:hover {
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    -moz-box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    -webkit-box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

div#seemore-summits {
    display: block;
    margin: 20px 0 10px;
    background: #fff;
    padding: 10px;
    border-radius: 10px;
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
    text-decoration: none;
    text-transform: uppercase;
    font-weight: bold;
    color: #00B1F2;
}
a#seemore-summtis:hover {
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    -moz-box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    -webkit-box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}





a#click_see_more_other_bagged {
    display: block;
    margin: 20px 0 10px;
    background: #fff;
    padding: 10px;
    border-radius: 10px;
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
    text-decoration: none;
    text-transform: uppercase;
    font-weight: bold;
    color: #00B1F2;
}
a#click_see_more_other_bagged {
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    -moz-box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    -webkit-box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}


ul.top-companions {
    display: block;
    margin-bottom: 10px;
}
ul.top-companions li {
    display: inline-block;
    float: left;
    width: 181px;
    height: 40px;
    margin-bottom: 15px;
}
ul.top-companions li span.companion {
    display: block;
}
ul.top-companions li span.companion span.image {
    display: inline-block;
    float: left;
    margin-right: 10px;
    width: 40px;
    height: 40px;
    overflow: hidden;
}
ul.top-companions li span.companion span.name {
    display: inline-block;
    line-height: 40px;
    font-size: 12px;
    width: 131px;
}
a#better-photo {
    display: block;
    color: #ccc;
    font-size: 11px;
}
a#better-photo:hover {
    color: #00B1F2;
}
div.qq-uploader {
    text-align: center;
}
div.qq-upload-button {
    margin: 2px auto;
}