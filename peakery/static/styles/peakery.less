@import "bootstrap/variables.less";
@import "bootstrap/mixins.less";



div#slider-range, div#prominence-slider-range{
	.border-radius(4px);
	height: 6px;
	overflow: visible;
	background-color: @grayLighter;
	.box-shadow(inset 1px 1px 4px rgba(0,0,0,0.3));
	.ui-slider-range {
        #gradient > .vertical(@blue, darken(@blue, 10%));
    }
	.ui-slider-handle {
		display: block;
		width: 16px;
		height: 16px;
		.border-radius(8px);
		#gradient > .vertical(@white, darken(@white, 10%));
	}
}

.block {
	overflow: visible !important;
}

#elevation-slider {
	padding-top: 10px;
	margin-left: 8px;
	p {
		margin-bottom: 10px;
		.clearfix;
		display: block;
	}
	input#amount {
		font-weight: normal;
		text-align: left;
		color: #404040;
	}
}

#prominence-slider {
	padding-top: 10px;
	margin-left: 8px;
	p {
		margin-bottom: 10px;
		.clearfix;
		display: block;
	}
	input#prominence-amount {
		font-weight: normal;
		text-align: left;
		color: #404040;
	}
}

