/* CHANGELISTS */

#changelist {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
}

#changelist .changelist-form-container {
    flex: 1 1 auto;
    min-width: 0;
}

#changelist table {
    width: 100%;
}

.change-list .hiddenfields { display:none; }

.change-list .filtered table {
    border-right: none;
}

.change-list .filtered {
    min-height: 400px;
}

.change-list .filtered .results, .change-list .filtered .paginator,
.filtered #toolbar, .filtered div.xfull {
    width: auto;
}

.change-list .filtered table tbody th {
    padding-right: 1em;
}

#changelist-form .results {
    overflow-x: auto;
    width: 100%;
}

#changelist .toplinks {
    border-bottom: 1px solid #ddd;
}

#changelist .paginator {
    color: #666;
    border-bottom: 1px solid #eee;
    background: #fff;
    overflow: hidden;
}

/* CHANGELIST TABLES */

#changelist table thead th {
    padding: 0;
    white-space: nowrap;
    vertical-align: middle;
}

#changelist table thead th.action-checkbox-column {
    width: 1.5em;
    text-align: center;
}

#changelist table tbody td.action-checkbox {
    text-align: center;
}

#changelist table tfoot {
    color: #666;
}

/* TOOLBAR */

#toolbar {
    padding: 8px 10px;
    margin-bottom: 15px;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
    background: #f8f8f8;
    color: #666;
}

#toolbar form input {
    border-radius: 4px;
    font-size: 14px;
    padding: 5px;
    color: #333;
}

#toolbar #searchbar {
    height: 19px;
    border: 1px solid #ccc;
    padding: 2px 5px;
    margin: 0;
    vertical-align: top;
    font-size: 13px;
    max-width: 100%;
}

#toolbar #searchbar:focus {
    border-color: #999;
}

#toolbar form input[type="submit"] {
    border: 1px solid #ccc;
    font-size: 13px;
    padding: 4px 8px;
    margin: 0;
    vertical-align: middle;
    background: #fff;
    box-shadow: 0 -15px 20px -10px rgba(0, 0, 0, 0.15) inset;
    cursor: pointer;
    color: #333;
}

#toolbar form input[type="submit"]:focus,
#toolbar form input[type="submit"]:hover {
    border-color: #999;
}

#changelist-search img {
    vertical-align: middle;
    margin-right: 4px;
}

/* FILTER COLUMN */

#changelist-filter {
    flex: 0 0 240px;
    order: 1;
    width: 240px;
    background: #f8f8f8;
    border-left: none;
    margin: 0 0 0 30px;
}

#changelist-filter h2 {
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 5px 15px;
    margin-bottom: 12px;
    border-bottom: none;
}

#changelist-filter h3 {
    font-weight: 400;
    font-size: 14px;
    padding: 0 15px;
    margin-bottom: 10px;
}

#changelist-filter ul {
    margin: 5px 0;
    padding: 0 15px 15px;
    border-bottom: 1px solid #eaeaea;
}

#changelist-filter ul:last-child {
    border-bottom: none;
}

#changelist-filter li {
    list-style-type: none;
    margin-left: 0;
    padding-left: 0;
}

#changelist-filter a {
    display: block;
    color: #999;
    text-overflow: ellipsis;
    overflow-x: hidden;
}

#changelist-filter li.selected {
    border-left: 5px solid #eaeaea;
    padding-left: 10px;
    margin-left: -15px;
}

#changelist-filter li.selected a {
    color: #5b80b2;
}

#changelist-filter a:focus, #changelist-filter a:hover,
#changelist-filter li.selected a:focus,
#changelist-filter li.selected a:hover {
    color: #036;
}

#changelist-filter #changelist-filter-clear a {
    font-size: 13px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eaeaea;
}

/* DATE DRILLDOWN */

.change-list ul.toplinks {
    display: block;
    float: left;
    padding: 0;
    margin: 0;
    width: 100%;
}

.change-list ul.toplinks li {
    padding: 3px 6px;
    font-weight: bold;
    list-style-type: none;
    display: inline-block;
}

.change-list ul.toplinks .date-back a {
    color: #999;
}

.change-list ul.toplinks .date-back a:focus,
.change-list ul.toplinks .date-back a:hover {
    color: #036;
}

/* PAGINATOR */

.paginator {
    font-size: 13px;
    padding-top: 10px;
    padding-bottom: 10px;
    line-height: 22px;
    margin: 0;
    border-top: 1px solid #ddd;
    width: 100%;
}

.paginator a:link, .paginator a:visited {
    padding: 2px 6px;
    background: #79aec8;
    text-decoration: none;
    color: #fff;
}

.paginator a.showall {
    border: none;
    background: none;
    color: #5b80b2;
}

.paginator a.showall:focus, .paginator a.showall:hover {
    background: none;
    color: #036;
}

.paginator .end {
    margin-right: 6px;
}

.paginator .this-page {
    padding: 2px 6px;
    font-weight: bold;
    font-size: 13px;
    vertical-align: top;
}

.paginator a:focus, .paginator a:hover {
    color: white;
    background: #036;
}

/* ACTIONS */

.filtered .actions {
    border-right: none;
}

#changelist table input {
    margin: 0;
    vertical-align: baseline;
}

#changelist table tbody tr.selected {
    background-color: #FFFFCC;
}

#changelist .actions {
    padding: 10px;
    background: #fff;
    border-top: none;
    border-bottom: none;
    line-height: 24px;
    color: #999;
    width: 100%;
}

#changelist .actions.selected {
    background: #fffccf;
    border-top: 1px solid #fffee8;
    border-bottom: 1px solid #edecd6;
}

#changelist .actions span.all,
#changelist .actions span.action-counter,
#changelist .actions span.clear,
#changelist .actions span.question {
    font-size: 13px;
    margin: 0 0.5em;
    display: none;
}

#changelist .actions:last-child {
    border-bottom: none;
}

#changelist .actions select {
    vertical-align: top;
    height: 24px;
    background: none;
    color: #000;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
    padding: 0 0 0 4px;
    margin: 0;
    margin-left: 10px;
}

#changelist .actions select:focus {
    border-color: #999;
}

#changelist .actions label {
    display: inline-block;
    vertical-align: middle;
    font-size: 13px;
}

#changelist .actions .button {
    font-size: 13px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background: #fff;
    box-shadow: 0 -15px 20px -10px rgba(0, 0, 0, 0.15) inset;
    cursor: pointer;
    height: 24px;
    line-height: 1;
    padding: 4px 8px;
    margin: 0;
    color: #333;
}

#changelist .actions .button:focus, #changelist .actions .button:hover {
    border-color: #999;
}
