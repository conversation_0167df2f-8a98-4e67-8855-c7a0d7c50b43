{"version": 3, "sources": ["?", "../client/js/util.js", "../client/js/export.js", "../client/js/version.js", "../client/js/features.js", "../client/js/promise.js", "../client/js/dnd.js"], "names": ["global", "qq", "element", "hide", "style", "display", "this", "attach", "type", "fn", "addEventListener", "attachEvent", "detach", "removeEventListener", "detachEvent", "contains", "descendant", "compareDocumentPosition", "insertBefore", "elementB", "parentNode", "remove", "<PERSON><PERSON><PERSON><PERSON>", "css", "styles", "Error", "opacity", "filter", "Math", "round", "extend", "hasClass", "name", "considerParent", "re", "RegExp", "test", "className", "addClass", "removeClass", "replace", "getByClass", "first", "candidates", "result", "querySelector", "querySelectorAll", "getElementsByTagName", "each", "idx", "val", "push", "getFirstByClass", "children", "child", "<PERSON><PERSON><PERSON><PERSON>", "nodeType", "nextS<PERSON>ling", "setText", "text", "innerText", "textContent", "clearText", "hasAttribute", "attrName", "attrVal", "exec", "getAttribute", "undefined", "canvasToBlob", "canvas", "mime", "quality", "dataUriToBlob", "toDataURL", "dataUri", "arrayBuffer", "byteString", "intArray", "mimeString", "createBlob", "data", "BlobBuilder", "window", "WebKitBlobBuilder", "MozBlobBuilder", "MSBlobBuilder", "blobBuilder", "append", "getBlob", "Blob", "split", "indexOf", "atob", "decodeURI", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "Uint8Array", "character", "charCodeAt", "log", "message", "level", "console", "isObject", "variable", "Object", "prototype", "toString", "call", "isFunction", "isArray", "value", "buffer", "constructor", "isItemList", "maybeItemList", "isNodeList", "maybeNodeList", "item", "namedItem", "isString", "maybeString", "trimStr", "string", "String", "trim", "format", "str", "args", "Array", "slice", "arguments", "newStr", "nextIdxToReplace", "substring", "isFile", "maybeFile", "File", "isFileList", "maybeFileList", "FileList", "isFileOrInput", "maybeFileOrInput", "isInput", "maybeInput", "notFile", "evaluateType", "normalizedType", "toLowerCase", "HTMLInputElement", "tagName", "isBlob", "maybeBlob", "isXhrUploadSupported", "input", "document", "createElement", "multiple", "FormData", "createXhrInstance", "upload", "XMLHttpRequest", "ActiveXObject", "error", "isFolderDropSupported", "dataTransfer", "items", "webkitGetAsEntry", "isFileChunkingSupported", "androidStock", "webkitSlice", "mozSlice", "sliceBlob", "fileOrBlob", "start", "end", "arrayBufferToHex", "bytesAsHex", "bytes", "byt", "byteAsHexStr", "readBlobToHex", "blob", "startOffset", "initialBlob", "fileReader", "FileReader", "promise", "Promise", "onload", "success", "onerror", "failure", "readAsA<PERSON>y<PERSON><PERSON>er", "second", "extendNested", "prop", "override", "target", "sourceFn", "super_", "source", "srcPropName", "srcPropVal", "arr", "elt", "from", "len", "hasOwnProperty", "getUniqueId", "c", "r", "random", "ie", "navigator", "userAgent", "ie7", "ie8", "ie10", "ie11", "edge", "safari", "vendor", "chrome", "opera", "firefox", "windows", "platform", "android", "ios6", "ios", "ios7", "ios8", "ios800", "iosChrome", "iosSafari", "iosSafariWebView", "preventDefault", "e", "returnValue", "toElement", "div", "html", "innerHTML", "iterableItem", "callback", "keyOrIndex", "retVal", "Storage", "key", "getItem", "char<PERSON>t", "bind", "oldFunc", "context", "newArgs", "concat", "apply", "obj2url", "obj", "temp", "prefixDone", "uristrings", "prefix", "add", "nextObj", "i", "nextTemp", "encodeURIComponent", "join", "obj2FormData", "formData", "arrayKeyName", "obj2Inputs", "form", "setAttribute", "append<PERSON><PERSON><PERSON>", "parseJson", "json", "JSON", "parse", "eval", "getExtension", "filename", "extIdx", "lastIndexOf", "substr", "getFilename", "blobOrFileInput", "fileName", "DisposeSupport", "disposers", "dispose", "disposer", "shift", "addDisposer", "disposeFunction", "define", "amd", "module", "exports", "version", "supportedFeatures", "testSupportsFileInputElement", "tempInput", "supported", "disabled", "ex", "isChrome21Or<PERSON>igher", "match", "isChrome14Or<PERSON><PERSON><PERSON>", "isCrossOriginXhrSupported", "withCredentials", "isXdrSupported", "XDomainRequest", "isCrossOriginAjaxSupported", "isFolderSelectionSupported", "webkitdirectory", "isLocalStorageSupported", "localStorage", "setItem", "isDragAndDropSupported", "span", "supportsUploading", "supportsUploadingBlobs", "supportsFileDrop", "supportsAjaxFileUploading", "supportsFolderDrop", "supportsChunking", "supportsResume", "supportsUploadViaPaste", "supportsUploadCors", "supportsDeleteFileXdr", "supportsDeleteFileCorsXhr", "supportsDeleteFileCors", "supportsFolderSelection", "supportsImagePreviews", "supportsUploadProgress", "postMessage", "ajaxUploading", "blobUploading", "canDetermineSize", "chunking", "deleteFileCors", "deleteFileCorsXdr", "deleteFileCorsXhr", "dialogElement", "HTMLDialogElement", "fileDrop", "folderDrop", "folderSelection", "imagePreviews", "imageValidation", "itemSizeValidation", "pause", "progressBar", "resume", "scaling", "tiffPreviews", "unlimitedScaledImageSize", "uploading", "uploadCors", "uploadCustomHeaders", "uploadNonMultipart", "uploadViaPaste", "isGenericPromise", "<PERSON><PERSON><PERSON><PERSON>", "then", "success<PERSON><PERSON>s", "failureArgs", "successCallbacks", "failureCallbacks", "doneCallbacks", "state", "onSuccess", "onFailure", "done", "DragAndDrop", "o", "uploadDroppedFiles", "files", "uploadDropZone", "filesAsArray", "options", "callbacks", "dropLog", "dropDisabled", "processingDroppedFilesComplete", "getElement", "traverseFileTree", "entry", "parseEntryPromise", "file", "fullPath", "indexOfNameInFullPath", "qqPath", "droppedFiles", "fileError", "code", "isDirectory", "getFilesInDirectory", "entries", "entriesLeft", "reader", "accumEntries", "existingPromise", "<PERSON><PERSON><PERSON><PERSON>", "createReader", "readEntries", "newEntries", "setTimeout", "handleDataTransfer", "pendingFolderPromises", "handleDataTransferPromise", "processingDroppedFiles", "allowMultipleItems", "dropError", "getAsFile", "pop", "setupDropzone", "dropArea", "dropZone", "UploadDropZone", "HIDE_ZONES_EVENT_NAME", "onEnter", "classes", "dropActive", "stopPropagation", "onLeaveNotDescendants", "onDrop", "disposeSupport", "uploadDropZones", "isFileDrag", "dragEvent", "fileDrag", "types", "leavingDocumentOut", "relatedTarget", "x", "y", "setupDragDrop", "dropZones", "dropZoneElements", "maybeHideDropZones", "HTMLElement", "setupExtraDropzone", "removeDropzone", "dzs", "splice", "targetEl", "errorSpecifics", "dragoverShouldBeCanceled", "disableDropOutside", "dropOutsideDisabled", "dropEffect", "isValidFileDrag", "effectTest", "dt", "<PERSON><PERSON><PERSON><PERSON>", "effectAllowed", "isOrSetDropDisabled", "isDisabled", "preventDrop", "triggerHidezonesEvent", "triggerUsingOldApi", "hideZonesEvent", "createEvent", "initEvent", "CustomEvent", "err", "dispatchEvent", "attachEvents", "effect", "onLeave", "elementFromPoint", "clientX", "clientY"], "mappings": ";CAAA,SAAUA,QCEV,GAAIC,IAAK,SAASC,GACd,YAEA,QACIC,KAAM,WAEF,MADAD,GAAQE,MAAMC,QAAU,OACjBC,MAIXC,OAAQ,SAASC,EAAMC,GAMnB,MALIP,GAAQQ,iBACRR,EAAQQ,iBAAiBF,EAAMC,GAAI,GAC5BP,EAAQS,aACfT,EAAQS,YAAY,KAAOH,EAAMC,GAE9B,WACHR,GAAGC,GAASU,OAAOJ,EAAMC,KAIjCG,OAAQ,SAASJ,EAAMC,GAMnB,MALIP,GAAQW,oBACRX,EAAQW,oBAAoBL,EAAMC,GAAI,GAC/BP,EAAQS,aACfT,EAAQY,YAAY,KAAON,EAAMC,GAE9BH,MAGXS,SAAU,SAASC,GAKf,QAAKA,IAKDd,IAAYc,IAIZd,EAAQa,SACDb,EAAQa,SAASC,MAGgC,EAA9CA,EAAWC,wBAAwBf,OAOrDgB,aAAc,SAASC,GAEnB,MADAA,GAASC,WAAWF,aAAahB,EAASiB,GACnCb,MAGXe,OAAQ,WAEJ,MADAnB,GAAQkB,WAAWE,YAAYpB,GACxBI,MAOXiB,IAAK,SAASC,GAEV,GAAqB,MAAjBtB,EAAQE,MACR,KAAM,IAAIH,IAAGwB,MAAM,6EAWvB,OAPsB,OAAlBD,EAAOE,SAC8B,gBAA1BxB,GAAQE,MAAMsB,SAAqD,SAArBxB,EAAe,UACpEsB,EAAOG,OAAS,iBAAmBC,KAAKC,MAAM,IAAML,EAAOE,SAAW,KAG9EzB,GAAG6B,OAAO5B,EAAQE,MAAOoB,GAElBlB,MAGXyB,SAAU,SAASC,EAAMC,GACrB,GAAIC,GAAK,GAAIC,QAAO,QAAUH,EAAO,QACrC,OAAOE,GAAGE,KAAKlC,EAAQmC,eAAiBJ,IAAkBC,EAAGE,KAAKlC,EAAQkB,WAAWiB,aAGzFC,SAAU,SAASN,GAIf,MAHK/B,IAAGC,GAAS6B,SAASC,KACtB9B,EAAQmC,WAAa,IAAML,GAExB1B,MAGXiC,YAAa,SAASP,GAClB,GAAIE,GAAK,GAAIC,QAAO,QAAUH,EAAO,QAErC,OADA9B,GAAQmC,UAAYnC,EAAQmC,UAAUG,QAAQN,EAAI,KAAKM,QAAQ,aAAc,IACtElC,MAGXmC,WAAY,SAASJ,EAAWK,GAC5B,GAAIC,GACAC,IAEJ,OAAIF,IAASxC,EAAQ2C,cACV3C,EAAQ2C,cAAc,IAAMR,GAE9BnC,EAAQ4C,iBACN5C,EAAQ4C,iBAAiB,IAAMT,IAG1CM,EAAazC,EAAQ6C,qBAAqB,KAE1C9C,GAAG+C,KAAKL,EAAY,SAASM,EAAKC,GAC1BjD,GAAGiD,GAAKnB,SAASM,IACjBO,EAAOO,KAAKD,KAGbR,EAAQE,EAAO,GAAKA,IAG/BQ,gBAAiB,SAASf,GACtB,MAAOpC,IAAGC,GAASuC,WAAWJ,GAAW,IAG7CgB,SAAU,WAIN,IAHA,GAAIA,MACAC,EAAQpD,EAAQqD,WAEbD,GACoB,IAAnBA,EAAME,UACNH,EAASF,KAAKG,GAElBA,EAAQA,EAAMG,WAGlB,OAAOJ,IAGXK,QAAS,SAASC,GAGd,MAFAzD,GAAQ0D,UAAYD,EACpBzD,EAAQ2D,YAAcF,EACfrD,MAGXwD,UAAW,WACP,MAAO7D,IAAGC,GAASwD,QAAQ,KAK/BK,aAAc,SAASC,GACnB,GAAIC,EAEJ,OAAI/D,GAAQ6D,eAEH7D,EAAQ6D,aAAaC,IAKkC,MAArD,WAAaE,KAAKhE,EAAQiE,aAAaH,KAG9CC,EAAU/D,EAAQ8D,GAEFI,SAAZH,GAKiC,MAA9B,WAAaC,KAAKD,QAMxC,WACG,YAEAhE,IAAGoE,aAAe,SAASC,EAAQC,EAAMC,GACrC,MAAOvE,IAAGwE,cAAcH,EAAOI,UAAUH,EAAMC,KAGnDvE,GAAGwE,cAAgB,SAASE,GACxB,GAAIC,GAAaC,EAgBbC,EAAUC,EAfVC,EAAa,SAASC,EAAMV,GACxB,GAAIW,GAAcC,OAAOD,aACjBC,OAAOC,mBACPD,OAAOE,gBACPF,OAAOG,cACXC,EAAcL,GAAe,GAAIA,EAErC,OAAIK,IACAA,EAAYC,OAAOP,GACZM,EAAYE,QAAQlB,IAGpB,GAAImB,OAAMT,IAAQzE,KAAM+D,IAyB3C,OAlBIM,GADAF,EAAQgB,MAAM,KAAK,GAAGC,QAAQ,WAAa,EAC9BC,KAAKlB,EAAQgB,MAAM,KAAK,IAGxBG,UAAUnB,EAAQgB,MAAM,KAAK,IAI9CZ,EAAaJ,EAAQgB,MAAM,KAAK,GAC3BA,MAAM,KAAK,GACXA,MAAM,KAAK,GAGhBf,EAAc,GAAImB,aAAYlB,EAAWmB,QACzClB,EAAW,GAAImB,YAAWrB,GAC1B3E,GAAG+C,KAAK6B,EAAY,SAAS5B,EAAKiD,GAC9BpB,EAAS7B,GAAOiD,EAAUC,WAAW,KAGlCnB,EAAWJ,EAAaG,IAGnC9E,GAAGmG,IAAM,SAASC,EAASC,GACnBnB,OAAOoB,UACFD,GAAmB,SAAVA,EAKNnB,OAAOoB,QAAQD,GACfnB,OAAOoB,QAAQD,GAAOD,GAGtBlB,OAAOoB,QAAQH,IAAI,IAAME,EAAQ,KAAOD,GAR5ClB,OAAOoB,QAAQH,IAAIC,KAc/BpG,GAAGuG,SAAW,SAASC,GACnB,MAAOA,KAAaA,EAASjD,UAAyD,oBAA7CkD,OAAOC,UAAUC,SAASC,KAAKJ,IAG5ExG,GAAG6G,WAAa,SAASL,GACrB,MAA6B,kBAAf,IASlBxG,GAAG8G,QAAU,SAASC,GAClB,MAAiD,mBAA1CN,OAAOC,UAAUC,SAASC,KAAKG,IACjCA,GAAS7B,OAAOY,aAAeiB,EAAMC,QAAUD,EAAMC,OAAOC,cAAgBnB,aAIrF9F,GAAGkH,WAAa,SAASC,GACrB,MAAyD,kCAAlDV,OAAOC,UAAUC,SAASC,KAAKO,IAK1CnH,GAAGoH,WAAa,SAASC,GACrB,MAAyD,sBAAlDZ,OAAOC,UAAUC,SAASC,KAAKS,IAGjCA,EAAcC,MAAQD,EAAcE,WAG7CvH,GAAGwH,SAAW,SAASC,GACnB,MAAuD,oBAAhDhB,OAAOC,UAAUC,SAASC,KAAKa,IAG1CzH,GAAG0H,QAAU,SAASC,GAClB,MAAIC,QAAOlB,UAAUmB,KACVF,EAAOE,OAGXF,EAAOpF,QAAQ,aAAc,KAOxCvC,GAAG8H,OAAS,SAASC,GAEjB,GAAIC,GAAQC,MAAMvB,UAAUwB,MAAMtB,KAAKuB,UAAW,GAC9CC,EAASL,EACTM,EAAmBD,EAAOzC,QAAQ,KAetC,OAbA3F,IAAG+C,KAAKiF,EAAM,SAAShF,EAAKC,GAQxB,GAJAmF,EAHgBA,EAAOE,UAAU,EAAGD,GAGfpF,EAFNmF,EAAOE,UAAUD,EAAmB,GAGnDA,EAAmBD,EAAOzC,QAAQ,KAAM0C,EAAmBpF,EAAI8C,QAG3DsC,EAAmB,EACnB,OAAO,IAIRD,GAGXpI,GAAGuI,OAAS,SAASC,GACjB,MAAOtD,QAAOuD,MAAsD,kBAA9ChC,OAAOC,UAAUC,SAASC,KAAK4B,IAGzDxI,GAAG0I,WAAa,SAASC,GACrB,MAAOzD,QAAO0D,UAA8D,sBAAlDnC,OAAOC,UAAUC,SAASC,KAAK+B,IAG7D3I,GAAG6I,cAAgB,SAASC,GACxB,MAAO9I,IAAGuI,OAAOO,IAAqB9I,GAAG+I,QAAQD,IAGrD9I,GAAG+I,QAAU,SAASC,EAAYC,GAC9B,GAAIC,GAAe,SAAS3I,GACxB,GAAI4I,GAAiB5I,EAAK6I,aAE1B,OAAIH,GAC0B,SAAnBE,EAGe,SAAnBA,EAGX,UAAIjE,OAAOmE,kBAC4C,8BAA/C5C,OAAOC,UAAUC,SAASC,KAAKoC,IAC3BA,EAAWzI,MAAQ2I,EAAaF,EAAWzI,WAKnDyI,EAAWM,SAC8B,UAArCN,EAAWM,QAAQF,eACfJ,EAAWzI,MAAQ2I,EAAaF,EAAWzI,QAS3DP,GAAGuJ,OAAS,SAASC,GACjB,GAAItE,OAAOO,MAAsD,kBAA9CgB,OAAOC,UAAUC,SAASC,KAAK4C,GAC9C,OAAO,GAIfxJ,GAAGyJ,qBAAuB,WACtB,GAAIC,GAAQC,SAASC,cAAc,QAGnC,OAFAF,GAAMnJ,KAAO,OAGU4D,SAAnBuF,EAAMG,UACc,mBAATpB,OACa,mBAAbqB,WACoC,SAAnC9J,GAAG+J,oBAAqBC,QAI5ChK,GAAG+J,kBAAoB,WACnB,GAAI7E,OAAO+E,eACP,MAAO,IAAIA,eAGf,KACI,MAAO,IAAIC,eAAc,sBAE7B,MAAOC,GAEH,MADAnK,IAAGmG,IAAI,wCAAyC,SACzC,OAIfnG,GAAGoK,sBAAwB,SAASC,GAChC,MAAOA,GAAaC,OAChBD,EAAaC,MAAMvE,OAAS,GAC5BsE,EAAaC,MAAM,GAAGC,kBAG9BvK,GAAGwK,wBAA0B,WACzB,OAAQxK,GAAGyK,gBACPzK,GAAGyJ,yBACuBtF,SAAzBsE,KAAK/B,UAAUwB,OAAsD/D,SAA/BsE,KAAK/B,UAAUgE,aAAyDvG,SAA5BsE,KAAK/B,UAAUiE,WAG1G3K,GAAG4K,UAAY,SAASC,EAAYC,EAAOC,GAGvC,OAFaF,EAAW3C,OAAS2C,EAAWF,UAAYE,EAAWH,aAErD9D,KAAKiE,EAAYC,EAAOC,IAG1C/K,GAAGgL,iBAAmB,SAAShE,GAC3B,GAAIiE,GAAa,GACbC,EAAQ,GAAIlF,YAAWgB,EAY3B,OAVAhH,IAAG+C,KAAKmI,EAAO,SAASlI,EAAKmI,GACzB,GAAIC,GAAeD,EAAIxE,SAAS,GAE5ByE,GAAarF,OAAS,IACtBqF,EAAe,IAAMA,GAGzBH,GAAcG,IAGXH,GAGXjL,GAAGqL,cAAgB,SAASC,EAAMC,EAAaxF,GAC3C,GAAIyF,GAAcxL,GAAG4K,UAAUU,EAAMC,EAAaA,EAAcxF,GAC5D0F,EAAa,GAAIC,YACjBC,EAAU,GAAI3L,IAAG4L,OAUrB,OARAH,GAAWI,OAAS,WAChBF,EAAQG,QAAQ9L,GAAGgL,iBAAiBS,EAAW9I,UAGnD8I,EAAWM,QAAUJ,EAAQK,QAE7BP,EAAWQ,kBAAkBT,GAEtBG,GAGX3L,GAAG6B,OAAS,SAASY,EAAOyJ,EAAQC,GAahC,MAZAnM,IAAG+C,KAAKmJ,EAAQ,SAASE,EAAMnJ,GACvBkJ,GAAgBnM,GAAGuG,SAAStD,IACRkB,SAAhB1B,EAAM2J,KACN3J,EAAM2J,OAEVpM,GAAG6B,OAAOY,EAAM2J,GAAOnJ,GAAK,IAG5BR,EAAM2J,GAAQnJ,IAIfR,GAaXzC,GAAGqM,SAAW,SAASC,EAAQC,GAC3B,GAAIC,MACAC,EAASF,EAASC,EAUtB,OARAxM,IAAG+C,KAAK0J,EAAQ,SAASC,EAAaC,GACNxI,SAAxBmI,EAAOI,KACPF,EAAOE,GAAeJ,EAAOI,IAGjCJ,EAAOI,GAAeC,IAGnBL,GAMXtM,GAAG2F,QAAU,SAASiH,EAAKC,EAAKC,GAC5B,GAAIF,EAAIjH,QACJ,MAAOiH,GAAIjH,QAAQkH,EAAKC,EAG5BA,GAAOA,GAAQ,CACf,IAAIC,GAAMH,EAAI7G,MAMd,KAJI+G,EAAO,IACPA,GAAQC,GAGLD,EAAOC,EAAKD,GAAQ,EACvB,GAAIF,EAAII,eAAeF,IAASF,EAAIE,KAAUD,EAC1C,MAAOC,EAGf,QAAO,GAIX9M,GAAGiN,YAAc,WACb,MAAO,uCAAuC1K,QAAQ,QAAS,SAAS2K,GAEpE,GAAIC,GAAoB,GAAhBxL,KAAKyL,SAAgB,CAC7B,QADyC,KAALF,EAAWC,EAAS,EAAJA,EAAU,GACrDxG,SAAS,OAM1B3G,GAAGqN,GAAK,WACJ,MAAOC,WAAUC,UAAU5H,QAAQ,WAAY,GAC3C2H,UAAUC,UAAU5H,QAAQ,cAAe,GAGnD3F,GAAGwN,IAAM,WACL,MAAOF,WAAUC,UAAU5H,QAAQ,aAAc,GAGrD3F,GAAGyN,IAAM,WACL,MAAOH,WAAUC,UAAU5H,QAAQ,aAAc,GAGrD3F,GAAG0N,KAAO,WACN,MAAOJ,WAAUC,UAAU5H,QAAQ,cAAe,GAGtD3F,GAAG2N,KAAO,WACN,MAAO3N,IAAGqN,MAAQC,UAAUC,UAAU5H,QAAQ,YAAa,GAG/D3F,GAAG4N,KAAO,WACN,MAAON,WAAUC,UAAU5H,QAAQ,SAAW,GAGlD3F,GAAG6N,OAAS,WACR,MAA4B1J,UAArBmJ,UAAUQ,QAAwBR,UAAUQ,OAAOnI,QAAQ,YAAa,GAGnF3F,GAAG+N,OAAS,WACR,MAA4B5J,UAArBmJ,UAAUQ,QAAwBR,UAAUQ,OAAOnI,QAAQ,aAAc,GAGpF3F,GAAGgO,MAAQ,WACP,MAA4B7J,UAArBmJ,UAAUQ,QAAwBR,UAAUQ,OAAOnI,QAAQ,YAAa,GAGnF3F,GAAGiO,QAAU,WACT,OAASjO,GAAG4N,SAAW5N,GAAG2N,QAAUL,UAAUC,UAAU5H,QAAQ,cAAe,GAA2BxB,SAArBmJ,UAAUQ,QAA6C,KAArBR,UAAUQ,QAGrI9N,GAAGkO,QAAU,WACT,MAA8B,UAAvBZ,UAAUa,UAGrBnO,GAAGoO,QAAU,WACT,MAAOd,WAAUC,UAAUnE,cAAczD,QAAQ,cAAe,GAKpE3F,GAAGyK,aAAe,WACd,MAAOzK,IAAGoO,WAAad,UAAUC,UAAUnE,cAAczD,QAAQ,UAAY,GAGjF3F,GAAGqO,KAAO,WACN,MAAOrO,IAAGsO,OAAShB,UAAUC,UAAU5H,QAAQ,aAAc,GAGjE3F,GAAGuO,KAAO,WACN,MAAOvO,IAAGsO,OAAShB,UAAUC,UAAU5H,QAAQ,aAAc,GAGjE3F,GAAGwO,KAAO,WACN,MAAOxO,IAAGsO,OAAShB,UAAUC,UAAU5H,QAAQ,aAAc,GAIjE3F,GAAGyO,OAAS,WACR,MAAOzO,IAAGsO,OAAShB,UAAUC,UAAU5H,QAAQ,eAAgB,GAGnE3F,GAAGsO,IAAM,WAEL,MAAOhB,WAAUC,UAAU5H,QAAQ,WAAY,GACxC2H,UAAUC,UAAU5H,QAAQ,WAAY,GACxC2H,UAAUC,UAAU5H,QAAQ,aAAc,GAGrD3F,GAAG0O,UAAY,WACX,MAAO1O,IAAGsO,OAAShB,UAAUC,UAAU5H,QAAQ,YAAa,GAGhE3F,GAAG2O,UAAY,WACX,MAAO3O,IAAGsO,QAAUtO,GAAG0O,aAAepB,UAAUC,UAAU5H,QAAQ,aAAc,GAGpF3F,GAAG4O,iBAAmB,WAClB,MAAO5O,IAAGsO,QAAUtO,GAAG0O,cAAgB1O,GAAG2O,aAM9C3O,GAAG6O,eAAiB,SAASC,GACrBA,EAAED,eACFC,EAAED,iBAEFC,EAAEC,aAAc,GAQxB/O,GAAGgP,UAAa,WACZ,GAAIC,GAAMtF,SAASC,cAAc,MACjC,OAAO,UAASsF,GACZD,EAAIE,UAAYD,CAChB,IAAIjP,GAAUgP,EAAI3L,UAElB,OADA2L,GAAI5N,YAAYpB,GACTA,MAKfD,GAAG+C,KAAO,SAASqM,EAAcC,GAC7B,GAAIC,GAAYC,CAEhB,IAAIH,EAEA,GAAIlK,OAAOsK,SAAWJ,EAAanI,cAAgB/B,OAAOsK,QACtD,IAAKF,EAAa,EAAGA,EAAaF,EAAarJ,SAC3CwJ,EAASF,EAASD,EAAaK,IAAIH,GAAaF,EAAaM,QAAQN,EAAaK,IAAIH,KAClFC,KAAW,GAFoCD,SAStD,IAAItP,GAAG8G,QAAQsI,IAAiBpP,GAAGkH,WAAWkI,IAAiBpP,GAAGoH,WAAWgI,GAC9E,IAAKE,EAAa,EAAGA,EAAaF,EAAarJ,SAC3CwJ,EAASF,EAASC,EAAYF,EAAaE,IACvCC,KAAW,GAFoCD,SAOtD,IAAItP,GAAGwH,SAAS4H,GACjB,IAAKE,EAAa,EAAGA,EAAaF,EAAarJ,SAC3CwJ,EAASF,EAASC,EAAYF,EAAaO,OAAOL,IAC9CC,KAAW,GAFoCD,SAQvD,KAAKA,IAAcF,GACf,GAAI3I,OAAOC,UAAUsG,eAAepG,KAAKwI,EAAcE,KACnDC,EAASF,EAASC,EAAYF,EAAaE,IACvCC,KAAW,GACX,OASxBvP,GAAG4P,KAAO,SAASC,EAASC,GACxB,GAAI9P,GAAG6G,WAAWgJ,GAAU,CACxB,GAAI7H,GAAQC,MAAMvB,UAAUwB,MAAMtB,KAAKuB,UAAW,EAElD,OAAO,YACH,GAAI4H,GAAU/P,GAAG6B,UAAWmG,EAI5B,OAHIG,WAAUpC,SACVgK,EAAUA,EAAQC,OAAO/H,MAAMvB,UAAUwB,MAAMtB,KAAKuB,aAEjD0H,EAAQI,MAAMH,EAASC,IAItC,KAAM,IAAIvO,OAAM,wCAmBpBxB,GAAGkQ,QAAU,SAASC,EAAKC,EAAMC,GAE7B,GAAIC,MACAC,EAAS,IACTC,EAAM,SAASC,EAASC,GACpB,GAAIC,GAAWP,EACR,QAAQjO,KAAKiO,GACdA,EACAA,EAAO,IAAMM,EAAI,IACjBA,CACY,eAAbC,GAAoC,cAAND,GAC/BJ,EAAWpN,KACa,gBAAZuN,GACFzQ,GAAGkQ,QAAQO,EAASE,GAAU,GACe,sBAA5ClK,OAAOC,UAAUC,SAASC,KAAK6J,GAChCG,mBAAmBD,GAAY,IAAMC,mBAAmBH,KACxDG,mBAAmBD,GAAY,IAAMC,mBAAmBH,IAqB9E,QAhBKJ,GAAcD,GACfG,EAAU,KAAKpO,KAAKiO,GAAU,MAAMjO,KAAKiO,GAAS,GAAK,IAAM,IAC7DE,EAAWpN,KAAKkN,GAChBE,EAAWpN,KAAKlD,GAAGkQ,QAAQC,KACqB,mBAAxC1J,OAAOC,UAAUC,SAASC,KAAKuJ,IAA8C,SAARA,EAC7EnQ,GAAG+C,KAAKoN,EAAK,SAASnN,EAAKC,GACvBuN,EAAIvN,EAAKD,KAEU,SAARmN,GAAiC,OAARA,GAAiC,gBAARA,GACjEnQ,GAAG+C,KAAKoN,EAAK,SAAS/D,EAAMnJ,GACxBuN,EAAIvN,EAAKmJ,KAGbkE,EAAWpN,KAAK0N,mBAAmBR,GAAQ,IAAMQ,mBAAmBT,IAGpEC,EACOE,EAAWO,KAAKN,GAEhBD,EAAWO,KAAKN,GAClBhO,QAAQ,KAAM,IACdA,QAAQ,OAAQ,MAI7BvC,GAAG8Q,aAAe,SAASX,EAAKY,EAAUC,GAmBtC,MAlBKD,KACDA,EAAW,GAAIjH,WAGnB9J,GAAG+C,KAAKoN,EAAK,SAASV,EAAKxM,GACvBwM,EAAMuB,EAAeA,EAAe,IAAMvB,EAAM,IAAMA,EAElDzP,GAAGuG,SAAStD,GACZjD,GAAG8Q,aAAa7N,EAAK8N,EAAUtB,GAE1BzP,GAAG6G,WAAW5D,GACnB8N,EAASxL,OAAOkK,EAAKxM,KAGrB8N,EAASxL,OAAOkK,EAAKxM,KAItB8N,GAGX/Q,GAAGiR,WAAa,SAASd,EAAKe,GAC1B,GAAIxH,EAeJ,OAbKwH,KACDA,EAAOvH,SAASC,cAAc,SAGlC5J,GAAG8Q,aAAaX,GACZ5K,OAAQ,SAASkK,EAAKxM,GAClByG,EAAQC,SAASC,cAAc,SAC/BF,EAAMyH,aAAa,OAAQ1B,GAC3B/F,EAAMyH,aAAa,QAASlO,GAC5BiO,EAAKE,YAAY1H,MAIlBwH,GAOXlR,GAAGqR,UAAY,SAASC,MAEpB,MAAIpM,QAAOqM,MAAQvR,GAAG6G,WAAW0K,KAAKC,OAC3BD,KAAKC,MAAMF,MAEXG,KAAK,IAAMH,KAAO,MAUjCtR,GAAG0R,aAAe,SAASC,GACvB,GAAIC,GAASD,EAASE,YAAY,KAAO,CAEzC,IAAID,EAAS,EACT,MAAOD,GAASG,OAAOF,EAAQD,EAAS5L,OAAS6L,IAIzD5R,GAAG+R,YAAc,SAASC,GAGtB,MAAIhS,IAAG+I,QAAQiJ,GAEJA,EAAgBjL,MAAMxE,QAAQ,YAAa,IAE7CvC,GAAGuI,OAAOyJ,IACkB,OAA7BA,EAAgBC,UAAkD9N,SAA7B6N,EAAgBC,SAC9CD,EAAgBC,SAIxBD,EAAgBjQ,MAM3B/B,GAAGkS,eAAiB,WAChB,GAAIC,KAEJ,QAEIC,QAAS,WACL,GAAIC,EACJ,GACIA,GAAWF,EAAUG,QACjBD,GACAA,UAGDA,IAIX/R,OAAQ,WACJ,GAAI0H,GAAOG,SAEX9H,MAAKkS,YAAYvS,GAAGgI,EAAK,IAAI1H,OAAO2P,MAAM5P,KAAM4H,MAAMvB,UAAUwB,MAAMtB,KAAKuB,UAAW,MAI1FoK,YAAa,SAASC,GAClBL,EAAUjP,KAAKsP,SCt2B9B,WACG,YACsB,mBAAXC,SAAyBA,OAAOC,IACvCD,OAAO,WACH,MAAOzS,MAGY,mBAAX2S,SAA0BA,OAAOC,QAC7CD,OAAOC,QAAU5S,GAGjBD,OAAOC,GAAKA,MCXpBA,GAAG6S,QAAU,SCAb7S,GAAG8S,kBAAqB,WACpB,YAkBA,SAASC,KACL,GACIC,GADAC,GAAY,CAGhB,KACID,EAAYrJ,SAASC,cAAc,SACnCoJ,EAAUzS,KAAO,OACjBP,GAAGgT,GAAW9S,OAEV8S,EAAUE,WACVD,GAAY,GAGpB,MAAOE,GACHF,GAAY,EAGhB,MAAOA,GAIX,QAASG,KACL,OAAQpT,GAAG+N,UAAY/N,GAAGgO,UAC+C7J,SAArEmJ,UAAUC,UAAU8F,MAAM,uCAIlC,QAASC,KACL,OAAQtT,GAAG+N,UAAY/N,GAAGgO,UAC+C7J,SAArEmJ,UAAUC,UAAU8F,MAAM,uCAIlC,QAASE,KACL,GAAIrO,OAAO+E,eAAgB,CAIvB,MAA+B9F,UAHrBnE,GAAG+J,oBAGFyJ,gBAGf,OAAO,EAIX,QAASC,KACL,MAAiCtP,UAA1Be,OAAOwO,eAKlB,QAASC,KACL,QAAIJ,KAIGE,IAGX,QAASG,KAEL,MAA2DzP,UAApDwF,SAASC,cAAc,SAASiK,gBAG3C,QAASC,KACL,IACI,QAAS5O,OAAO6O,cAEZ/T,GAAG6G,WAAW3B,OAAO6O,aAAaC,SAE1C,MAAO7J,GAEH,OAAO,GAIf,QAAS8J,KACL,GAAIC,GAAOvK,SAASC,cAAc,OAElC,QAAQ,aAAesK,IAAS,eAAiBA,IAAQ,UAAYA,MAChElU,GAAGoO,YAAcpO,GAAGsO,MAhG7B,GAAI6F,GACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,CAwHJ,OAnCAd,GAAoBpB,IAEpBuB,EAA4BH,GAAqBnU,GAAGyJ,uBAEpD2K,EAAyBE,IAA8BtU,GAAGyK,eAE1D4J,EAAmBC,GAA6BL,IAEhDM,EAAqBF,GAAoBjB,IAEzCoB,EAAmBF,GAA6BtU,GAAGwK,0BAEnDiK,EAAiBH,GAA6BE,GAAoBV,IAElEY,EAAyBJ,GAA6BhB,IAEtDqB,EAAqBR,IAA6ChQ,SAAvBe,OAAOgQ,aAA6BZ,GAE/EO,EAA4BtB,IAE5BqB,EAAwBnB,IAExBqB,EAAyBnB,IAEzBoB,EAA0BnB,IAE1BoB,EAAwBV,GAAmDnQ,SAAtBe,OAAOwG,WAE5DuJ,EAA0B,WACtB,QAAIX,KACQtU,GAAGyK,iBAAmBzK,GAAG0O,iBAMrCyG,cAAeb,EACfc,cAAehB,EACfiB,iBAAkBf,EAClBgB,SAAUd,EACVe,eAAgBT,EAChBU,kBAAmBZ,EACnBa,kBAAmBZ,EACnBa,gBAAiBxQ,OAAOyQ,kBACxBC,SAAUvB,EACVwB,WAAYtB,EACZuB,gBAAiBf,EACjBgB,cAAef,EACfgB,gBAAiBhB,EACjBiB,mBAAoB3B,EACpB4B,MAAO1B,EACP2B,YAAalB,EACbmB,OAAQ3B,EACR4B,QAASrB,GAAyBZ,EAClCkC,aAActW,GAAG6N,SACjB0I,0BAA2BvW,GAAGsO,MAC9BkI,UAAWrC,EACXsC,WAAY9B,EACZ+B,oBAAqBpC,EACrBqC,mBAAoBrC,EACpBsC,eAAgBlC,MChKxB1U,GAAG6W,iBAAmB,SAASC,GAC3B,YACA,UAAUA,GAAgBA,EAAaC,MAAQ/W,GAAG6G,WAAWiQ,EAAaC,QAG9E/W,GAAG4L,QAAU,WACT,YAEA,IAAIoL,GAAaC,EACbC,KACAC,KACAC,KACAC,EAAQ,CAEZrX,IAAG6B,OAAOxB,MACN0W,KAAM,SAASO,EAAWC,GAgBtB,MAfc,KAAVF,GACIC,GACAJ,EAAiBhU,KAAKoU,GAEtBC,GACAJ,EAAiBjU,KAAKqU,IAGrBF,KAAU,EACfE,GAAaA,EAAUtH,MAAM,KAAMgH,GAE9BK,GACLA,EAAUrH,MAAM,KAAM+G,GAGnB3W,MAGXmX,KAAM,SAASnI,GAQX,MAPc,KAAVgI,EACAD,EAAclU,KAAKmM,GAGnBA,EAASY,MAAM,KAAsB9L,SAAhB8S,EAA4BD,EAAcC,GAG5D5W,MAGXyL,QAAS,WAgBL,MAfAuL,GAAQ,EACRL,EAAc7O,UAEV+O,EAAiBnR,QACjB/F,GAAG+C,KAAKmU,EAAkB,SAASlU,EAAKqM,GACpCA,EAASY,MAAM,KAAM+G,KAIzBI,EAAcrR,QACd/F,GAAG+C,KAAKqU,EAAe,SAASpU,EAAKqM,GACjCA,EAASY,MAAM,KAAM+G,KAItB3W,MAGX2L,QAAS,WAgBL,MAfAqL,IAAQ,EACRJ,EAAc9O,UAEVgP,EAAiBpR,QACjB/F,GAAG+C,KAAKoU,EAAkB,SAASnU,EAAKqM,GACpCA,EAASY,MAAM,KAAMgH,KAIzBG,EAAcrR,QACd/F,GAAG+C,KAAKqU,EAAe,SAASpU,EAAKqM,GACjCA,EAASY,MAAM,KAAMgH,KAItB5W,SClFnBL,GAAGyX,YAAc,SAASC,GACtB,YAoBA,SAASC,GAAmBC,EAAOC,GAE/B,GAAIC,GAAe7P,MAAMvB,UAAUwB,MAAMtB,KAAKgR,EAE9CG,GAAQC,UAAUC,QAAQ,WAAaL,EAAM7R,OAAS,mBACtD8R,EAAeK,cAAa,GAC5BH,EAAQC,UAAUG,+BAA+BL,EAAcD,EAAeO,cAGlF,QAASC,GAAiBC,GACtB,GAAIC,GAAoB,GAAIvY,IAAG4L,OAoD/B,OAlDI0M,GAAM/P,OACN+P,EAAME,KAAK,SAASA,GAChB,GAAIzW,GAAOuW,EAAMvW,KACb0W,EAAWH,EAAMG,SACjBC,EAAwBD,EAAS9S,QAAQ5D,EAG7C0W,GAAWA,EAAS3G,OAAO,EAAG4G,GAGH,MAAvBD,EAAS9I,OAAO,KAChB8I,EAAWA,EAAS3G,OAAO,IAG/B0G,EAAKG,OAASF,EACdG,EAAa1V,KAAKsV,GAClBD,EAAkBzM,WAEtB,SAAS+M,GACLd,EAAQC,UAAUC,QAAQ,oBAAsBK,EAAMG,SAAW,sBAAwBI,EAAUC,KAAO,IAAK,SAC/GP,EAAkBvM,YAGjBsM,EAAMS,aACXC,EAAoBV,GAAOvB,KACvB,SAAwBkC,GACpB,GAAIC,GAAcD,EAAQlT,MAE1B/F,IAAG+C,KAAKkW,EAAS,SAASjW,EAAKsV,GAC3BD,EAAiBC,GAAOd,KAAK,WACzB0B,GAAe,EAEK,IAAhBA,GACAX,EAAkBzM,cAKzBmN,EAAQlT,QACTwS,EAAkBzM,WAI1B,SAAqB+M,GACjBd,EAAQC,UAAUC,QAAQ,oBAAsBK,EAAMG,SAAW,sBAAwBI,EAAUC,KAAO,IAAK,SAC/GP,EAAkBvM,YAKvBuM,EAIX,QAASS,GAAoBV,EAAOa,EAAQC,EAAcC,GACtD,GAAI1N,GAAU0N,GAAmB,GAAIrZ,IAAG4L,QACpC0N,EAAYH,GAAUb,EAAMiB,cAmBhC,OAjBAD,GAAUE,YACN,SAAqBP,GACjB,GAAIQ,GAAaL,EAAeA,EAAapJ,OAAOiJ,GAAWA,CAE3DA,GAAQlT,OACR2T,WAAW,WACPV,EAAoBV,EAAOgB,EAAWG,EAAY9N,IACnD,GAGHA,EAAQG,QAAQ2N,IAIxB9N,EAAQK,SAGLL,EAGX,QAASgO,GAAmBtP,EAAcwN,GACtC,GAAI+B,MACAC,EAA4B,GAAI7Z,IAAG4L,OA4CvC,OA1CAmM,GAAQC,UAAU8B,yBAClBjC,EAAeK,cAAa,GAExB7N,EAAauN,MAAM7R,OAAS,IAAMgS,EAAQgC,oBAC1ChC,EAAQC,UAAUG,mCAClBJ,EAAQC,UAAUgC,UAAU,oBAAqB,IACjDnC,EAAeK,cAAa,GAC5B2B,EAA0B7N,YAG1B4M,KAEI5Y,GAAGoK,sBAAsBC,GACzBrK,GAAG+C,KAAKsH,EAAaC,MAAO,SAAStH,EAAKsE,GACtC,GAAIgR,GAAQhR,EAAKiD,kBAEb+N,KAEIA,EAAM/P,OACNqQ,EAAa1V,KAAKoE,EAAK2S,aAIvBL,EAAsB1W,KAAKmV,EAAiBC,GAAOd,KAAK,WACpDoC,EAAsBM,MACe,IAAjCN,EAAsB7T,QACtB8T,EAA0B/N,gBAQ9C8M,EAAevO,EAAauN,MAGK,IAAjCgC,EAAsB7T,QACtB8T,EAA0B/N,WAI3B+N,EAGX,QAASM,GAAcC,GACnB,GAAIC,GAAW,GAAIra,IAAGsa,gBAClBC,sBA9JoB,eA+JpBta,QAASma,EACTI,QAAS,SAAS1L,GACd9O,GAAGoa,GAAU/X,SAAS0V,EAAQ0C,QAAQC,YACtC5L,EAAE6L,mBAENC,sBAAuB,SAAS9L,GAC5B9O,GAAGoa,GAAU9X,YAAYyV,EAAQ0C,QAAQC,aAE7CG,OAAQ,SAAS/L,GACb6K,EAAmB7K,EAAEzE,aAAcgQ,GAAUtD,KACzC,WACIY,EAAmBiB,EAAcyB,IAErC,WACItC,EAAQC,UAAUC,QAAQ,sEAAuE,aAcjH,OARA6C,GAAevI,YAAY,WACvB8H,EAASjI,YAGbpS,GAAGoa,GAAUtW,aAtLY,qBAsL4B9D,GAAGoa,GAAUla,OAElE6a,EAAgB7X,KAAKmX,GAEdA,EAGX,QAASW,GAAWC,GAChB,GAAIC,EASJ,OAPAlb,IAAG+C,KAAKkY,EAAU5Q,aAAa8Q,MAAO,SAAS1L,EAAKxM,GAChD,GAAY,UAARA,EAEA,MADAiY,IAAW,GACJ,IAIRA,EAWX,QAASE,GAAmBtM,GACxB,MAAI9O,IAAGiO,WACKa,EAAEuM,cAGVrb,GAAG6N,SACIiB,EAAEwM,EAAI,GAAKxM,EAAEyM,EAAI,EAGb,IAARzM,EAAEwM,GAAmB,IAARxM,EAAEyM,EAG1B,QAASC,KACL,GAAIC,GAAY1D,EAAQ2D,iBAEpBC,EAAqB,WACjBjC,WAAW,WACP1Z,GAAG+C,KAAK0Y,EAAW,SAASzY,EAAKqX,GAC7Bra,GAAGqa,GAAUvW,aApOJ,qBAoO4C9D,GAAGqa,GAAUna,OAClEF,GAAGqa,GAAU/X,YAAYyV,EAAQ0C,QAAQC,eAE9C,IAGX1a,IAAG+C,KAAK0Y,EAAW,SAASzY,EAAKqX,GAC7B,GAAIxC,GAAiBsC,EAAcE,EAG/BoB,GAAU1V,QAAU/F,GAAG8S,kBAAkB8C,UACzCkF,EAAexa,OAAOqJ,SAAU,YAAa,SAASmF,IAC7C+I,EAAeK,gBAAkB8C,EAAWlM,IAC7C9O,GAAG+C,KAAK0Y,EAAW,SAASzY,EAAKqX,GAGzBA,YAAoBuB,cACpB5b,GAAGqa,GAAUvW,aArPZ,qBAuPD9D,GAAGqa,GAAU/Y,KAAKlB,QAAS,gBAQnD0a,EAAexa,OAAOqJ,SAAU,YAAa,SAASmF,GAC9CsM,EAAmBtM,IACnB6M,MAORb,EAAexa,OAAON,GAAG2J,UAAUvG,WAAW,GAAI,aAAc,SAAS0L,GACrE6M,MAGJb,EAAexa,OAAOqJ,SAAU,OAAQ,SAASmF,GAC7CA,EAAED,iBACF8M,MAGJb,EAAexa,OAAOqJ,SAlRE,eAkR+BgS,GAnR3D,GAAI5D,GAGAgD,KACAnC,KACAkC,EAAiB,GAAI9a,IAAGkS,cAE5B6F,IACI2D,oBACA3B,oBAAoB,EACpBU,SACIC,WAAY,MAEhB1C,UAAW,GAAIhY,IAAGyX,YAAYO,WAGlChY,GAAG6B,OAAOkW,EAASL,GAAG,GAsQtB8D,IAEAxb,GAAG6B,OAAOxB,MACNwb,mBAAoB,SAAS5b,GACzB8X,EAAQ2D,iBAAiBxY,KAAKjD,GAC9Bka,EAAcla,IAGlB6b,eAAgB,SAAS7b,GACrB,GAAIyQ,GACAqL,EAAMhE,EAAQ2D,gBAElB,KAAKhL,IAAKqL,GACN,GAAIA,EAAIrL,KAAOzQ,EACX,MAAO8b,GAAIC,OAAOtL,EAAG,IAKjC0B,QAAS,WACL0I,EAAe1I,UACfpS,GAAG+C,KAAKgY,EAAiB,SAAS/X,EAAKqX,GACnCA,EAASjI,gBAMzBpS,GAAGyX,YAAYO,UAAY,WACvB,YAEA,QACI8B,uBAAwB,aACxB3B,+BAAgC,SAASP,EAAOqE,KAChDjC,UAAW,SAASlB,EAAMoD,GACtBlc,GAAGmG,IAAI,2BAA6B2S,EAAO,2BAA6BoD,EAAiB,IAAK,UAElGjE,QAAS,SAAS7R,EAASC,GACvBrG,GAAGmG,IAAIC,EAASC,MAK5BrG,GAAGsa,eAAiB,SAAS5C,GACzB,YAiBA,SAASyE,KACL,MAAOnc,IAAG6N,UAAa7N,GAAGiO,WAAajO,GAAGkO,UAG9C,QAASkO,GAAmBtN,GAEnBuN,IAGGF,EACArB,EAAexa,OAAOqJ,SAAU,WAAY,SAASmF,GACjDA,EAAED,mBAGNiM,EAAexa,OAAOqJ,SAAU,WAAY,SAASmF,GAC7CA,EAAEzE,eACFyE,EAAEzE,aAAaiS,WAAa,OAC5BxN,EAAED,oBAKdwN,GAAsB,GAI9B,QAASE,GAAgBzN,GAGrB,IAAK9O,GAAG8S,kBAAkB8C,SACtB,OAAO,CAGX,IAAI4G,GAAYC,EAAK3N,EAAEzE,aAEvBqS,EAAW1c,GAAG6N,QAQd,OADA2O,MAAaxc,GAAGqN,OAAQrN,GAAG8S,kBAAkB8C,WAAuC,SAArB6G,EAAGE,cAC3DF,GAAMD,IAAeC,EAAG7E,QAAW8E,GAAYD,EAAGtB,MAAMra,UAAY2b,EAAGtB,MAAMra,SAAS,UAGjG,QAAS8b,GAAoBC,GAIzB,MAHmB1Y,UAAf0Y,IACAC,EAAcD,GAEXC,EAGX,QAASC,KAGL,QAASC,KACLC,EAAiBtT,SAASuT,YAAY,SACtCD,EAAeE,UAAUpF,EAAQwC,uBAAuB,GAAM,GAJlE,GAAI0C,EAOJ,IAAI/X,OAAOkY,YACP,IACIH,EAAiB,GAAIG,aAAYrF,EAAQwC,uBAE7C,MAAO8C,GACHL,QAIJA,IAGJrT,UAAS2T,cAAcL,GAG3B,QAASM,KACLzC,EAAexa,OAAOL,EAAS,WAAY,SAAS6O,GAChD,GAAKyN,EAAgBzN,GAArB,CAMA,GAAI0O,GAASxd,GAAGqN,MAAQrN,GAAG8S,kBAAkB8C,SAAW,KAAO9G,EAAEzE,aAAasS,aAE1E7N,GAAEzE,aAAaiS,WADJ,SAAXkB,GAAgC,aAAXA,EACO,OAEA,OAGhC1O,EAAE6L,kBACF7L,EAAED,oBAGNiM,EAAexa,OAAOL,EAAS,YAAa,SAAS6O,GACjD,IAAK8N,IAAuB,CACxB,IAAKL,EAAgBzN,GACjB,MAEJiJ,GAAQyC,QAAQ1L,MAIxBgM,EAAexa,OAAOL,EAAS,YAAa,SAAS6O,GACjD,GAAKyN,EAAgBzN,GAArB,CAIAiJ,EAAQ0F,QAAQ3O,EAEhB,IAAIuM,GAAgB1R,SAAS+T,iBAAiB5O,EAAE6O,QAAS7O,EAAE8O,QAEvD5d,IAAGK,MAAMS,SAASua,IAItBtD,EAAQ6C,sBAAsB9L,MAGlCgM,EAAexa,OAAOL,EAAS,OAAQ,SAAS6O,GAC5C,IAAK8N,IAAuB,CACxB,IAAKL,EAAgBzN,GACjB,MAGJA,GAAED,iBACFC,EAAE6L,kBACF5C,EAAQ8C,OAAO/L,GAEfiO,OAjJZ,GACIhF,GAAS9X,EAAS6c,EAAaT,EAD/BvB,EAAiB,GAAI9a,IAAGkS,cAG5B6F,IACI9X,QAAS,KACTua,QAAS,SAAS1L,KAClB2O,QAAS,SAAS3O,KAElB8L,sBAAuB,SAAS9L,KAChC+L,OAAQ,SAAS/L,MAGrB9O,GAAG6B,OAAOkW,EAASL,GACnBzX,EAAU8X,EAAQ9X,QAyIlBmc,IACAmB,IAEAvd,GAAG6B,OAAOxB,MACN6X,aAAc,SAAS2E,GACnB,MAAOD,GAAoBC,IAG/BzK,QAAS,WACL0I,EAAe1I,WAGnBgG,WAAY,WACR,MAAOnY,QN3eYiF", "file": "dnd.min.js", "sourcesContent": [null, "/*globals window, navigator, document, FormData, File, HTMLInputElement, XMLHttpRequest, Blob, Storage, ActiveXObject */\n/* jshint -W079 */\nvar qq = function(element) {\n    \"use strict\";\n\n    return {\n        hide: function() {\n            element.style.display = \"none\";\n            return this;\n        },\n\n        /** Returns the function which detaches attached event */\n        attach: function(type, fn) {\n            if (element.addEventListener) {\n                element.addEventListener(type, fn, false);\n            } else if (element.attachEvent) {\n                element.attachEvent(\"on\" + type, fn);\n            }\n            return function() {\n                qq(element).detach(type, fn);\n            };\n        },\n\n        detach: function(type, fn) {\n            if (element.removeEventListener) {\n                element.removeEventListener(type, fn, false);\n            } else if (element.attachEvent) {\n                element.detachEvent(\"on\" + type, fn);\n            }\n            return this;\n        },\n\n        contains: function(descendant) {\n            // The [W3C spec](http://www.w3.org/TR/domcore/#dom-node-contains)\n            // says a `null` (or ostensibly `undefined`) parameter\n            // passed into `Node.contains` should result in a false return value.\n            // IE7 throws an exception if the parameter is `undefined` though.\n            if (!descendant) {\n                return false;\n            }\n\n            // compareposition returns false in this case\n            if (element === descendant) {\n                return true;\n            }\n\n            if (element.contains) {\n                return element.contains(descendant);\n            } else {\n                /*jslint bitwise: true*/\n                return !!(descendant.compareDocumentPosition(element) & 8);\n            }\n        },\n\n        /**\n         * Insert this element before elementB.\n         */\n        insertBefore: function(elementB) {\n            elementB.parentNode.insertBefore(element, elementB);\n            return this;\n        },\n\n        remove: function() {\n            element.parentNode.removeChild(element);\n            return this;\n        },\n\n        /**\n         * Sets styles for an element.\n         * Fixes opacity in IE6-8.\n         */\n        css: function(styles) {\n            /*jshint eqnull: true*/\n            if (element.style == null) {\n                throw new qq.Error(\"Can't apply style to node as it is not on the HTMLElement prototype chain!\");\n            }\n\n            /*jshint -W116*/\n            if (styles.opacity != null) {\n                if (typeof element.style.opacity !== \"string\" && typeof (element.filters) !== \"undefined\") {\n                    styles.filter = \"alpha(opacity=\" + Math.round(100 * styles.opacity) + \")\";\n                }\n            }\n            qq.extend(element.style, styles);\n\n            return this;\n        },\n\n        hasClass: function(name, considerParent) {\n            var re = new RegExp(\"(^| )\" + name + \"( |$)\");\n            return re.test(element.className) || !!(considerParent && re.test(element.parentNode.className));\n        },\n\n        addClass: function(name) {\n            if (!qq(element).hasClass(name)) {\n                element.className += \" \" + name;\n            }\n            return this;\n        },\n\n        removeClass: function(name) {\n            var re = new RegExp(\"(^| )\" + name + \"( |$)\");\n            element.className = element.className.replace(re, \" \").replace(/^\\s+|\\s+$/g, \"\");\n            return this;\n        },\n\n        getByClass: function(className, first) {\n            var candidates,\n                result = [];\n\n            if (first && element.querySelector) {\n                return element.querySelector(\".\" + className);\n            }\n            else if (element.querySelectorAll) {\n                return element.querySelectorAll(\".\" + className);\n            }\n\n            candidates = element.getElementsByTagName(\"*\");\n\n            qq.each(candidates, function(idx, val) {\n                if (qq(val).hasClass(className)) {\n                    result.push(val);\n                }\n            });\n            return first ? result[0] : result;\n        },\n\n        getFirstByClass: function(className) {\n            return qq(element).getByClass(className, true);\n        },\n\n        children: function() {\n            var children = [],\n                child = element.firstChild;\n\n            while (child) {\n                if (child.nodeType === 1) {\n                    children.push(child);\n                }\n                child = child.nextSibling;\n            }\n\n            return children;\n        },\n\n        setText: function(text) {\n            element.innerText = text;\n            element.textContent = text;\n            return this;\n        },\n\n        clearText: function() {\n            return qq(element).setText(\"\");\n        },\n\n        // Returns true if the attribute exists on the element\n        // AND the value of the attribute is NOT \"false\" (case-insensitive)\n        hasAttribute: function(attrName) {\n            var attrVal;\n\n            if (element.hasAttribute) {\n\n                if (!element.hasAttribute(attrName)) {\n                    return false;\n                }\n\n                /*jshint -W116*/\n                return (/^false$/i).exec(element.getAttribute(attrName)) == null;\n            }\n            else {\n                attrVal = element[attrName];\n\n                if (attrVal === undefined) {\n                    return false;\n                }\n\n                /*jshint -W116*/\n                return (/^false$/i).exec(attrVal) == null;\n            }\n        }\n    };\n};\n\n(function() {\n    \"use strict\";\n\n    qq.canvasToBlob = function(canvas, mime, quality) {\n        return qq.dataUriToBlob(canvas.toDataURL(mime, quality));\n    };\n\n    qq.dataUriToBlob = function(dataUri) {\n        var arrayBuffer, byteString,\n            createBlob = function(data, mime) {\n                var BlobBuilder = window.BlobBuilder ||\n                        window.WebKitBlobBuilder ||\n                        window.MozBlobBuilder ||\n                        window.MSBlobBuilder,\n                    blobBuilder = BlobBuilder && new BlobBuilder();\n\n                if (blobBuilder) {\n                    blobBuilder.append(data);\n                    return blobBuilder.getBlob(mime);\n                }\n                else {\n                    return new Blob([data], {type: mime});\n                }\n            },\n            intArray, mimeString;\n\n        // convert base64 to raw binary data held in a string\n        if (dataUri.split(\",\")[0].indexOf(\"base64\") >= 0) {\n            byteString = atob(dataUri.split(\",\")[1]);\n        }\n        else {\n            byteString = decodeURI(dataUri.split(\",\")[1]);\n        }\n\n        // extract the MIME\n        mimeString = dataUri.split(\",\")[0]\n            .split(\":\")[1]\n            .split(\";\")[0];\n\n        // write the bytes of the binary string to an ArrayBuffer\n        arrayBuffer = new ArrayBuffer(byteString.length);\n        intArray = new Uint8Array(arrayBuffer);\n        qq.each(byteString, function(idx, character) {\n            intArray[idx] = character.charCodeAt(0);\n        });\n\n        return createBlob(arrayBuffer, mimeString);\n    };\n\n    qq.log = function(message, level) {\n        if (window.console) {\n            if (!level || level === \"info\") {\n                window.console.log(message);\n            }\n            else\n            {\n                if (window.console[level]) {\n                    window.console[level](message);\n                }\n                else {\n                    window.console.log(\"<\" + level + \"> \" + message);\n                }\n            }\n        }\n    };\n\n    qq.isObject = function(variable) {\n        return variable && !variable.nodeType && Object.prototype.toString.call(variable) === \"[object Object]\";\n    };\n\n    qq.isFunction = function(variable) {\n        return typeof (variable) === \"function\";\n    };\n\n    /**\n     * Check the type of a value.  Is it an \"array\"?\n     *\n     * @param value value to test.\n     * @returns true if the value is an array or associated with an `ArrayBuffer`\n     */\n    qq.isArray = function(value) {\n        return Object.prototype.toString.call(value) === \"[object Array]\" ||\n            (value && window.ArrayBuffer && value.buffer && value.buffer.constructor === ArrayBuffer);\n    };\n\n    // Looks for an object on a `DataTransfer` object that is associated with drop events when utilizing the Filesystem API.\n    qq.isItemList = function(maybeItemList) {\n        return Object.prototype.toString.call(maybeItemList) === \"[object DataTransferItemList]\";\n    };\n\n    // Looks for an object on a `NodeList` or an `HTMLCollection`|`HTMLFormElement`|`HTMLSelectElement`\n    // object that is associated with collections of Nodes.\n    qq.isNodeList = function(maybeNodeList) {\n        return Object.prototype.toString.call(maybeNodeList) === \"[object NodeList]\" ||\n            // If `HTMLCollection` is the actual type of the object, we must determine this\n            // by checking for expected properties/methods on the object\n            (maybeNodeList.item && maybeNodeList.namedItem);\n    };\n\n    qq.isString = function(maybeString) {\n        return Object.prototype.toString.call(maybeString) === \"[object String]\";\n    };\n\n    qq.trimStr = function(string) {\n        if (String.prototype.trim) {\n            return string.trim();\n        }\n\n        return string.replace(/^\\s+|\\s+$/g, \"\");\n    };\n\n    /**\n     * @param str String to format.\n     * @returns {string} A string, swapping argument values with the associated occurrence of {} in the passed string.\n     */\n    qq.format = function(str) {\n\n        var args =  Array.prototype.slice.call(arguments, 1),\n            newStr = str,\n            nextIdxToReplace = newStr.indexOf(\"{}\");\n\n        qq.each(args, function(idx, val) {\n            var strBefore = newStr.substring(0, nextIdxToReplace),\n                strAfter = newStr.substring(nextIdxToReplace + 2);\n\n            newStr = strBefore + val + strAfter;\n            nextIdxToReplace = newStr.indexOf(\"{}\", nextIdxToReplace + val.length);\n\n            // End the loop if we have run out of tokens (when the arguments exceed the # of tokens)\n            if (nextIdxToReplace < 0) {\n                return false;\n            }\n        });\n\n        return newStr;\n    };\n\n    qq.isFile = function(maybeFile) {\n        return window.File && Object.prototype.toString.call(maybeFile) === \"[object File]\";\n    };\n\n    qq.isFileList = function(maybeFileList) {\n        return window.FileList && Object.prototype.toString.call(maybeFileList) === \"[object FileList]\";\n    };\n\n    qq.isFileOrInput = function(maybeFileOrInput) {\n        return qq.isFile(maybeFileOrInput) || qq.isInput(maybeFileOrInput);\n    };\n\n    qq.isInput = function(maybeInput, notFile) {\n        var evaluateType = function(type) {\n            var normalizedType = type.toLowerCase();\n\n            if (notFile) {\n                return normalizedType !== \"file\";\n            }\n\n            return normalizedType === \"file\";\n        };\n\n        if (window.HTMLInputElement) {\n            if (Object.prototype.toString.call(maybeInput) === \"[object HTMLInputElement]\") {\n                if (maybeInput.type && evaluateType(maybeInput.type)) {\n                    return true;\n                }\n            }\n        }\n        if (maybeInput.tagName) {\n            if (maybeInput.tagName.toLowerCase() === \"input\") {\n                if (maybeInput.type && evaluateType(maybeInput.type)) {\n                    return true;\n                }\n            }\n        }\n\n        return false;\n    };\n\n    qq.isBlob = function(maybeBlob) {\n        if (window.Blob && Object.prototype.toString.call(maybeBlob) === \"[object Blob]\") {\n            return true;\n        }\n    };\n\n    qq.isXhrUploadSupported = function() {\n        var input = document.createElement(\"input\");\n        input.type = \"file\";\n\n        return (\n            input.multiple !== undefined &&\n                typeof File !== \"undefined\" &&\n                typeof FormData !== \"undefined\" &&\n                typeof (qq.createXhrInstance()).upload !== \"undefined\");\n    };\n\n    // Fall back to ActiveX is native XHR is disabled (possible in any version of IE).\n    qq.createXhrInstance = function() {\n        if (window.XMLHttpRequest) {\n            return new XMLHttpRequest();\n        }\n\n        try {\n            return new ActiveXObject(\"MSXML2.XMLHTTP.3.0\");\n        }\n        catch (error) {\n            qq.log(\"Neither XHR or ActiveX are supported!\", \"error\");\n            return null;\n        }\n    };\n\n    qq.isFolderDropSupported = function(dataTransfer) {\n        return dataTransfer.items &&\n            dataTransfer.items.length > 0 &&\n            dataTransfer.items[0].webkitGetAsEntry;\n    };\n\n    qq.isFileChunkingSupported = function() {\n        return !qq.androidStock() && //Android's stock browser cannot upload Blobs correctly\n            qq.isXhrUploadSupported() &&\n            (File.prototype.slice !== undefined || File.prototype.webkitSlice !== undefined || File.prototype.mozSlice !== undefined);\n    };\n\n    qq.sliceBlob = function(fileOrBlob, start, end) {\n        var slicer = fileOrBlob.slice || fileOrBlob.mozSlice || fileOrBlob.webkitSlice;\n\n        return slicer.call(fileOrBlob, start, end);\n    };\n\n    qq.arrayBufferToHex = function(buffer) {\n        var bytesAsHex = \"\",\n            bytes = new Uint8Array(buffer);\n\n        qq.each(bytes, function(idx, byt) {\n            var byteAsHexStr = byt.toString(16);\n\n            if (byteAsHexStr.length < 2) {\n                byteAsHexStr = \"0\" + byteAsHexStr;\n            }\n\n            bytesAsHex += byteAsHexStr;\n        });\n\n        return bytesAsHex;\n    };\n\n    qq.readBlobToHex = function(blob, startOffset, length) {\n        var initialBlob = qq.sliceBlob(blob, startOffset, startOffset + length),\n            fileReader = new FileReader(),\n            promise = new qq.Promise();\n\n        fileReader.onload = function() {\n            promise.success(qq.arrayBufferToHex(fileReader.result));\n        };\n\n        fileReader.onerror = promise.failure;\n\n        fileReader.readAsArrayBuffer(initialBlob);\n\n        return promise;\n    };\n\n    qq.extend = function(first, second, extendNested) {\n        qq.each(second, function(prop, val) {\n            if (extendNested && qq.isObject(val)) {\n                if (first[prop] === undefined) {\n                    first[prop] = {};\n                }\n                qq.extend(first[prop], val, true);\n            }\n            else {\n                first[prop] = val;\n            }\n        });\n\n        return first;\n    };\n\n    /**\n     * Allow properties in one object to override properties in another,\n     * keeping track of the original values from the target object.\n     *\n     * Note that the pre-overriden properties to be overriden by the source will be passed into the `sourceFn` when it is invoked.\n     *\n     * @param target Update properties in this object from some source\n     * @param sourceFn A function that, when invoked, will return properties that will replace properties with the same name in the target.\n     * @returns {object} The target object\n     */\n    qq.override = function(target, sourceFn) {\n        var super_ = {},\n            source = sourceFn(super_);\n\n        qq.each(source, function(srcPropName, srcPropVal) {\n            if (target[srcPropName] !== undefined) {\n                super_[srcPropName] = target[srcPropName];\n            }\n\n            target[srcPropName] = srcPropVal;\n        });\n\n        return target;\n    };\n\n    /**\n     * Searches for a given element (elt) in the array, returns -1 if it is not present.\n     */\n    qq.indexOf = function(arr, elt, from) {\n        if (arr.indexOf) {\n            return arr.indexOf(elt, from);\n        }\n\n        from = from || 0;\n        var len = arr.length;\n\n        if (from < 0) {\n            from += len;\n        }\n\n        for (; from < len; from += 1) {\n            if (arr.hasOwnProperty(from) && arr[from] === elt) {\n                return from;\n            }\n        }\n        return -1;\n    };\n\n    //this is a version 4 UUID\n    qq.getUniqueId = function() {\n        return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, function(c) {\n            /*jslint eqeq: true, bitwise: true*/\n            var r = Math.random() * 16 | 0, v = c == \"x\" ? r : (r & 0x3 | 0x8);\n            return v.toString(16);\n        });\n    };\n\n    //\n    // Browsers and platforms detection\n    qq.ie = function() {\n        return navigator.userAgent.indexOf(\"MSIE\") !== -1 ||\n            navigator.userAgent.indexOf(\"Trident\") !== -1;\n    };\n\n    qq.ie7 = function() {\n        return navigator.userAgent.indexOf(\"MSIE 7\") !== -1;\n    };\n\n    qq.ie8 = function() {\n        return navigator.userAgent.indexOf(\"MSIE 8\") !== -1;\n    };\n\n    qq.ie10 = function() {\n        return navigator.userAgent.indexOf(\"MSIE 10\") !== -1;\n    };\n\n    qq.ie11 = function() {\n        return qq.ie() && navigator.userAgent.indexOf(\"rv:11\") !== -1;\n    };\n\n    qq.edge = function() {\n        return navigator.userAgent.indexOf(\"Edge\") >= 0;\n    };\n\n    qq.safari = function() {\n        return navigator.vendor !== undefined && navigator.vendor.indexOf(\"Apple\") !== -1;\n    };\n\n    qq.chrome = function() {\n        return navigator.vendor !== undefined && navigator.vendor.indexOf(\"Google\") !== -1;\n    };\n\n    qq.opera = function() {\n        return navigator.vendor !== undefined && navigator.vendor.indexOf(\"Opera\") !== -1;\n    };\n\n    qq.firefox = function() {\n        return (!qq.edge() && !qq.ie11() && navigator.userAgent.indexOf(\"Mozilla\") !== -1 && navigator.vendor !== undefined && navigator.vendor === \"\");\n    };\n\n    qq.windows = function() {\n        return navigator.platform === \"Win32\";\n    };\n\n    qq.android = function() {\n        return navigator.userAgent.toLowerCase().indexOf(\"android\") !== -1;\n    };\n\n    // We need to identify the Android stock browser via the UA string to work around various bugs in this browser,\n    // such as the one that prevents a `Blob` from being uploaded.\n    qq.androidStock = function() {\n        return qq.android() && navigator.userAgent.toLowerCase().indexOf(\"chrome\") < 0;\n    };\n\n    qq.ios6 = function() {\n        return qq.ios() && navigator.userAgent.indexOf(\" OS 6_\") !== -1;\n    };\n\n    qq.ios7 = function() {\n        return qq.ios() && navigator.userAgent.indexOf(\" OS 7_\") !== -1;\n    };\n\n    qq.ios8 = function() {\n        return qq.ios() && navigator.userAgent.indexOf(\" OS 8_\") !== -1;\n    };\n\n    // iOS 8.0.0\n    qq.ios800 = function() {\n        return qq.ios() && navigator.userAgent.indexOf(\" OS 8_0 \") !== -1;\n    };\n\n    qq.ios = function() {\n        /*jshint -W014 */\n        return navigator.userAgent.indexOf(\"iPad\") !== -1\n            || navigator.userAgent.indexOf(\"iPod\") !== -1\n            || navigator.userAgent.indexOf(\"iPhone\") !== -1;\n    };\n\n    qq.iosChrome = function() {\n        return qq.ios() && navigator.userAgent.indexOf(\"CriOS\") !== -1;\n    };\n\n    qq.iosSafari = function() {\n        return qq.ios() && !qq.iosChrome() && navigator.userAgent.indexOf(\"Safari\") !== -1;\n    };\n\n    qq.iosSafariWebView = function() {\n        return qq.ios() && !qq.iosChrome() && !qq.iosSafari();\n    };\n\n    //\n    // Events\n\n    qq.preventDefault = function(e) {\n        if (e.preventDefault) {\n            e.preventDefault();\n        } else {\n            e.returnValue = false;\n        }\n    };\n\n    /**\n     * Creates and returns element from html string\n     * Uses innerHTML to create an element\n     */\n    qq.toElement = (function() {\n        var div = document.createElement(\"div\");\n        return function(html) {\n            div.innerHTML = html;\n            var element = div.firstChild;\n            div.removeChild(element);\n            return element;\n        };\n    }());\n\n    //key and value are passed to callback for each entry in the iterable item\n    qq.each = function(iterableItem, callback) {\n        var keyOrIndex, retVal;\n\n        if (iterableItem) {\n            // Iterate through [`Storage`](http://www.w3.org/TR/webstorage/#the-storage-interface) items\n            if (window.Storage && iterableItem.constructor === window.Storage) {\n                for (keyOrIndex = 0; keyOrIndex < iterableItem.length; keyOrIndex++) {\n                    retVal = callback(iterableItem.key(keyOrIndex), iterableItem.getItem(iterableItem.key(keyOrIndex)));\n                    if (retVal === false) {\n                        break;\n                    }\n                }\n            }\n            // `DataTransferItemList` & `NodeList` objects are array-like and should be treated as arrays\n            // when iterating over items inside the object.\n            else if (qq.isArray(iterableItem) || qq.isItemList(iterableItem) || qq.isNodeList(iterableItem)) {\n                for (keyOrIndex = 0; keyOrIndex < iterableItem.length; keyOrIndex++) {\n                    retVal = callback(keyOrIndex, iterableItem[keyOrIndex]);\n                    if (retVal === false) {\n                        break;\n                    }\n                }\n            }\n            else if (qq.isString(iterableItem)) {\n                for (keyOrIndex = 0; keyOrIndex < iterableItem.length; keyOrIndex++) {\n                    retVal = callback(keyOrIndex, iterableItem.charAt(keyOrIndex));\n                    if (retVal === false) {\n                        break;\n                    }\n                }\n            }\n            else {\n                for (keyOrIndex in iterableItem) {\n                    if (Object.prototype.hasOwnProperty.call(iterableItem, keyOrIndex)) {\n                        retVal = callback(keyOrIndex, iterableItem[keyOrIndex]);\n                        if (retVal === false) {\n                            break;\n                        }\n                    }\n                }\n            }\n        }\n    };\n\n    //include any args that should be passed to the new function after the context arg\n    qq.bind = function(oldFunc, context) {\n        if (qq.isFunction(oldFunc)) {\n            var args =  Array.prototype.slice.call(arguments, 2);\n\n            return function() {\n                var newArgs = qq.extend([], args);\n                if (arguments.length) {\n                    newArgs = newArgs.concat(Array.prototype.slice.call(arguments));\n                }\n                return oldFunc.apply(context, newArgs);\n            };\n        }\n\n        throw new Error(\"first parameter must be a function!\");\n    };\n\n    /**\n     * obj2url() takes a json-object as argument and generates\n     * a querystring. pretty much like jQuery.param()\n     *\n     * how to use:\n     *\n     *    `qq.obj2url({a:'b',c:'d'},'http://any.url/upload?otherParam=value');`\n     *\n     * will result in:\n     *\n     *    `http://any.url/upload?otherParam=value&a=b&c=d`\n     *\n     * @param  Object JSON-Object\n     * @param  String current querystring-part\n     * @return String encoded querystring\n     */\n    qq.obj2url = function(obj, temp, prefixDone) {\n        /*jshint laxbreak: true*/\n        var uristrings = [],\n            prefix = \"&\",\n            add = function(nextObj, i) {\n                var nextTemp = temp\n                    ? (/\\[\\]$/.test(temp)) // prevent double-encoding\n                    ? temp\n                    : temp + \"[\" + i + \"]\"\n                    : i;\n                if ((nextTemp !== \"undefined\") && (i !== \"undefined\")) {\n                    uristrings.push(\n                        (typeof nextObj === \"object\")\n                            ? qq.obj2url(nextObj, nextTemp, true)\n                            : (Object.prototype.toString.call(nextObj) === \"[object Function]\")\n                            ? encodeURIComponent(nextTemp) + \"=\" + encodeURIComponent(nextObj())\n                            : encodeURIComponent(nextTemp) + \"=\" + encodeURIComponent(nextObj)\n                    );\n                }\n            };\n\n        if (!prefixDone && temp) {\n            prefix = (/\\?/.test(temp)) ? (/\\?$/.test(temp)) ? \"\" : \"&\" : \"?\";\n            uristrings.push(temp);\n            uristrings.push(qq.obj2url(obj));\n        } else if ((Object.prototype.toString.call(obj) === \"[object Array]\") && (typeof obj !== \"undefined\")) {\n            qq.each(obj, function(idx, val) {\n                add(val, idx);\n            });\n        } else if ((typeof obj !== \"undefined\") && (obj !== null) && (typeof obj === \"object\")) {\n            qq.each(obj, function(prop, val) {\n                add(val, prop);\n            });\n        } else {\n            uristrings.push(encodeURIComponent(temp) + \"=\" + encodeURIComponent(obj));\n        }\n\n        if (temp) {\n            return uristrings.join(prefix);\n        } else {\n            return uristrings.join(prefix)\n                .replace(/^&/, \"\")\n                .replace(/%20/g, \"+\");\n        }\n    };\n\n    qq.obj2FormData = function(obj, formData, arrayKeyName) {\n        if (!formData) {\n            formData = new FormData();\n        }\n\n        qq.each(obj, function(key, val) {\n            key = arrayKeyName ? arrayKeyName + \"[\" + key + \"]\" : key;\n\n            if (qq.isObject(val)) {\n                qq.obj2FormData(val, formData, key);\n            }\n            else if (qq.isFunction(val)) {\n                formData.append(key, val());\n            }\n            else {\n                formData.append(key, val);\n            }\n        });\n\n        return formData;\n    };\n\n    qq.obj2Inputs = function(obj, form) {\n        var input;\n\n        if (!form) {\n            form = document.createElement(\"form\");\n        }\n\n        qq.obj2FormData(obj, {\n            append: function(key, val) {\n                input = document.createElement(\"input\");\n                input.setAttribute(\"name\", key);\n                input.setAttribute(\"value\", val);\n                form.appendChild(input);\n            }\n        });\n\n        return form;\n    };\n\n    /**\n     * Not recommended for use outside of Fine Uploader since this falls back to an unchecked eval if JSON.parse is not\n     * implemented.  For a more secure JSON.parse polyfill, use Douglas Crockford's json2.js.\n     */\n    qq.parseJson = function(json) {\n        /*jshint evil: true*/\n        if (window.JSON && qq.isFunction(JSON.parse)) {\n            return JSON.parse(json);\n        } else {\n            return eval(\"(\" + json + \")\");\n        }\n    };\n\n    /**\n     * Retrieve the extension of a file, if it exists.\n     *\n     * @param filename\n     * @returns {string || undefined}\n     */\n    qq.getExtension = function(filename) {\n        var extIdx = filename.lastIndexOf(\".\") + 1;\n\n        if (extIdx > 0) {\n            return filename.substr(extIdx, filename.length - extIdx);\n        }\n    };\n\n    qq.getFilename = function(blobOrFileInput) {\n        /*jslint regexp: true*/\n\n        if (qq.isInput(blobOrFileInput)) {\n            // get input value and remove path to normalize\n            return blobOrFileInput.value.replace(/.*(\\/|\\\\)/, \"\");\n        }\n        else if (qq.isFile(blobOrFileInput)) {\n            if (blobOrFileInput.fileName !== null && blobOrFileInput.fileName !== undefined) {\n                return blobOrFileInput.fileName;\n            }\n        }\n\n        return blobOrFileInput.name;\n    };\n\n    /**\n     * A generic module which supports object disposing in dispose() method.\n     * */\n    qq.DisposeSupport = function() {\n        var disposers = [];\n\n        return {\n            /** Run all registered disposers */\n            dispose: function() {\n                var disposer;\n                do {\n                    disposer = disposers.shift();\n                    if (disposer) {\n                        disposer();\n                    }\n                }\n                while (disposer);\n            },\n\n            /** Attach event handler and register de-attacher as a disposer */\n            attach: function() {\n                var args = arguments;\n                /*jslint undef:true*/\n                this.addDisposer(qq(args[0]).attach.apply(this, Array.prototype.slice.call(arguments, 1)));\n            },\n\n            /** Add disposer to the collection */\n            addDisposer: function(disposeFunction) {\n                disposers.push(disposeFunction);\n            }\n        };\n    };\n}());\n", "/* globals define, module, global, qq */\n(function() {\n    \"use strict\";\n    if (typeof define === \"function\" && define.amd) {\n        define(function() {\n            return qq;\n        });\n    }\n    else if (typeof module !== \"undefined\" && module.exports) {\n        module.exports = qq;\n    }\n    else {\n        global.qq = qq;\n    }\n}());\n", "/*global qq */\nqq.version = \"5.14.2\";\n", "/* globals qq */\nqq.supportedFeatures = (function() {\n    \"use strict\";\n\n    var supportsUploading,\n        supportsUploadingBlobs,\n        supportsFileDrop,\n        supportsAjaxFileUploading,\n        supportsFolderDrop,\n        supportsChunking,\n        supportsResume,\n        supportsUploadViaPaste,\n        supportsUploadCors,\n        supportsDeleteFileXdr,\n        supportsDeleteFileCorsXhr,\n        supportsDeleteFileCors,\n        supportsFolderSelection,\n        supportsImagePreviews,\n        supportsUploadProgress;\n\n    function testSupportsFileInputElement() {\n        var supported = true,\n            tempInput;\n\n        try {\n            tempInput = document.createElement(\"input\");\n            tempInput.type = \"file\";\n            qq(tempInput).hide();\n\n            if (tempInput.disabled) {\n                supported = false;\n            }\n        }\n        catch (ex) {\n            supported = false;\n        }\n\n        return supported;\n    }\n\n    //only way to test for Filesystem API support since webkit does not expose the DataTransfer interface\n    function isChrome21OrHigher() {\n        return (qq.chrome() || qq.opera()) &&\n            navigator.userAgent.match(/Chrome\\/[2][1-9]|Chrome\\/[3-9][0-9]/) !== undefined;\n    }\n\n    //only way to test for complete Clipboard API support at this time\n    function isChrome14OrHigher() {\n        return (qq.chrome() || qq.opera()) &&\n            navigator.userAgent.match(/Chrome\\/[1][4-9]|Chrome\\/[2-9][0-9]/) !== undefined;\n    }\n\n    //Ensure we can send cross-origin `XMLHttpRequest`s\n    function isCrossOriginXhrSupported() {\n        if (window.XMLHttpRequest) {\n            var xhr = qq.createXhrInstance();\n\n            //Commonly accepted test for XHR CORS support.\n            return xhr.withCredentials !== undefined;\n        }\n\n        return false;\n    }\n\n    //Test for (terrible) cross-origin ajax transport fallback for IE9 and IE8\n    function isXdrSupported() {\n        return window.XDomainRequest !== undefined;\n    }\n\n    // CORS Ajax requests are supported if it is either possible to send credentialed `XMLHttpRequest`s,\n    // or if `XDomainRequest` is an available alternative.\n    function isCrossOriginAjaxSupported() {\n        if (isCrossOriginXhrSupported()) {\n            return true;\n        }\n\n        return isXdrSupported();\n    }\n\n    function isFolderSelectionSupported() {\n        // We know that folder selection is only supported in Chrome via this proprietary attribute for now\n        return document.createElement(\"input\").webkitdirectory !== undefined;\n    }\n\n    function isLocalStorageSupported() {\n        try {\n            return !!window.localStorage &&\n                // unpatched versions of IE10/11 have buggy impls of localStorage where setItem is a string\n                qq.isFunction(window.localStorage.setItem);\n        }\n        catch (error) {\n            // probably caught a security exception, so no localStorage for you\n            return false;\n        }\n    }\n\n    function isDragAndDropSupported() {\n        var span = document.createElement(\"span\");\n\n        return (\"draggable\" in span || (\"ondragstart\" in span && \"ondrop\" in span)) &&\n            !qq.android() && !qq.ios();\n    }\n\n    supportsUploading = testSupportsFileInputElement();\n\n    supportsAjaxFileUploading = supportsUploading && qq.isXhrUploadSupported();\n\n    supportsUploadingBlobs = supportsAjaxFileUploading && !qq.androidStock();\n\n    supportsFileDrop = supportsAjaxFileUploading && isDragAndDropSupported();\n\n    supportsFolderDrop = supportsFileDrop && isChrome21OrHigher();\n\n    supportsChunking = supportsAjaxFileUploading && qq.isFileChunkingSupported();\n\n    supportsResume = supportsAjaxFileUploading && supportsChunking && isLocalStorageSupported();\n\n    supportsUploadViaPaste = supportsAjaxFileUploading && isChrome14OrHigher();\n\n    supportsUploadCors = supportsUploading && (window.postMessage !== undefined || supportsAjaxFileUploading);\n\n    supportsDeleteFileCorsXhr = isCrossOriginXhrSupported();\n\n    supportsDeleteFileXdr = isXdrSupported();\n\n    supportsDeleteFileCors = isCrossOriginAjaxSupported();\n\n    supportsFolderSelection = isFolderSelectionSupported();\n\n    supportsImagePreviews = supportsAjaxFileUploading && window.FileReader !== undefined;\n\n    supportsUploadProgress = (function() {\n        if (supportsAjaxFileUploading) {\n            return !qq.androidStock() && !qq.iosChrome();\n        }\n        return false;\n    }());\n\n    return {\n        ajaxUploading: supportsAjaxFileUploading,\n        blobUploading: supportsUploadingBlobs,\n        canDetermineSize: supportsAjaxFileUploading,\n        chunking: supportsChunking,\n        deleteFileCors: supportsDeleteFileCors,\n        deleteFileCorsXdr: supportsDeleteFileXdr, //NOTE: will also return true in IE10, where XDR is also supported\n        deleteFileCorsXhr: supportsDeleteFileCorsXhr,\n        dialogElement: !!window.HTMLDialogElement,\n        fileDrop: supportsFileDrop,\n        folderDrop: supportsFolderDrop,\n        folderSelection: supportsFolderSelection,\n        imagePreviews: supportsImagePreviews,\n        imageValidation: supportsImagePreviews,\n        itemSizeValidation: supportsAjaxFileUploading,\n        pause: supportsChunking,\n        progressBar: supportsUploadProgress,\n        resume: supportsResume,\n        scaling: supportsImagePreviews && supportsUploadingBlobs,\n        tiffPreviews: qq.safari(), // Not the best solution, but simple and probably accurate enough (for now)\n        unlimitedScaledImageSize: !qq.ios(), // false simply indicates that there is some known limit\n        uploading: supportsUploading,\n        uploadCors: supportsUploadCors,\n        uploadCustomHeaders: supportsAjaxFileUploading,\n        uploadNonMultipart: supportsAjaxFileUploading,\n        uploadViaPaste: supportsUploadViaPaste\n    };\n\n}());\n", "/*globals qq*/\n\n// Is the passed object a promise instance?\nqq.isGenericPromise = function(maybePromise) {\n    \"use strict\";\n    return !!(maybePromise && maybePromise.then && qq.isFunction(maybePromise.then));\n};\n\nqq.Promise = function() {\n    \"use strict\";\n\n    var successArgs, failureArgs,\n        successCallbacks = [],\n        failureCallbacks = [],\n        doneCallbacks = [],\n        state = 0;\n\n    qq.extend(this, {\n        then: function(onSuccess, onFailure) {\n            if (state === 0) {\n                if (onSuccess) {\n                    successCallbacks.push(onSuccess);\n                }\n                if (onFailure) {\n                    failureCallbacks.push(onFailure);\n                }\n            }\n            else if (state === -1) {\n                onFailure && onFailure.apply(null, failureArgs);\n            }\n            else if (onSuccess) {\n                onSuccess.apply(null, successArgs);\n            }\n\n            return this;\n        },\n\n        done: function(callback) {\n            if (state === 0) {\n                doneCallbacks.push(callback);\n            }\n            else {\n                callback.apply(null, failureArgs === undefined ? successArgs : failureArgs);\n            }\n\n            return this;\n        },\n\n        success: function() {\n            state = 1;\n            successArgs = arguments;\n\n            if (successCallbacks.length) {\n                qq.each(successCallbacks, function(idx, callback) {\n                    callback.apply(null, successArgs);\n                });\n            }\n\n            if (doneCallbacks.length) {\n                qq.each(doneCallbacks, function(idx, callback) {\n                    callback.apply(null, successArgs);\n                });\n            }\n\n            return this;\n        },\n\n        failure: function() {\n            state = -1;\n            failureArgs = arguments;\n\n            if (failureCallbacks.length) {\n                qq.each(failureCallbacks, function(idx, callback) {\n                    callback.apply(null, failureArgs);\n                });\n            }\n\n            if (doneCallbacks.length) {\n                qq.each(doneCallbacks, function(idx, callback) {\n                    callback.apply(null, failureArgs);\n                });\n            }\n\n            return this;\n        }\n    });\n};\n", "/*globals qq, document, CustomEvent*/\nqq.DragAndDrop = function(o) {\n    \"use strict\";\n\n    var options,\n        HIDE_ZONES_EVENT_NAME = \"qq-hidezones\",\n        HIDE_BEFORE_ENTER_ATTR = \"qq-hide-dropzone\",\n        uploadDropZones = [],\n        droppedFiles = [],\n        disposeSupport = new qq.DisposeSupport();\n\n    options = {\n        dropZoneElements: [],\n        allowMultipleItems: true,\n        classes: {\n            dropActive: null\n        },\n        callbacks: new qq.DragAndDrop.callbacks()\n    };\n\n    qq.extend(options, o, true);\n\n    function uploadDroppedFiles(files, uploadDropZone) {\n        // We need to convert the `FileList` to an actual `Array` to avoid iteration issues\n        var filesAsArray = Array.prototype.slice.call(files);\n\n        options.callbacks.dropLog(\"Grabbed \" + files.length + \" dropped files.\");\n        uploadDropZone.dropDisabled(false);\n        options.callbacks.processingDroppedFilesComplete(filesAsArray, uploadDropZone.getElement());\n    }\n\n    function traverseFileTree(entry) {\n        var parseEntryPromise = new qq.Promise();\n\n        if (entry.isFile) {\n            entry.file(function(file) {\n                var name = entry.name,\n                    fullPath = entry.fullPath,\n                    indexOfNameInFullPath = fullPath.indexOf(name);\n\n                // remove file name from full path string\n                fullPath = fullPath.substr(0, indexOfNameInFullPath);\n\n                // remove leading slash in full path string\n                if (fullPath.charAt(0) === \"/\") {\n                    fullPath = fullPath.substr(1);\n                }\n\n                file.qqPath = fullPath;\n                droppedFiles.push(file);\n                parseEntryPromise.success();\n            },\n            function(fileError) {\n                options.callbacks.dropLog(\"Problem parsing '\" + entry.fullPath + \"'.  FileError code \" + fileError.code + \".\", \"error\");\n                parseEntryPromise.failure();\n            });\n        }\n        else if (entry.isDirectory) {\n            getFilesInDirectory(entry).then(\n                function allEntriesRead(entries) {\n                    var entriesLeft = entries.length;\n\n                    qq.each(entries, function(idx, entry) {\n                        traverseFileTree(entry).done(function() {\n                            entriesLeft -= 1;\n\n                            if (entriesLeft === 0) {\n                                parseEntryPromise.success();\n                            }\n                        });\n                    });\n\n                    if (!entries.length) {\n                        parseEntryPromise.success();\n                    }\n                },\n\n                function readFailure(fileError) {\n                    options.callbacks.dropLog(\"Problem parsing '\" + entry.fullPath + \"'.  FileError code \" + fileError.code + \".\", \"error\");\n                    parseEntryPromise.failure();\n                }\n            );\n        }\n\n        return parseEntryPromise;\n    }\n\n    // Promissory.  Guaranteed to read all files in the root of the passed directory.\n    function getFilesInDirectory(entry, reader, accumEntries, existingPromise) {\n        var promise = existingPromise || new qq.Promise(),\n            dirReader = reader || entry.createReader();\n\n        dirReader.readEntries(\n            function readSuccess(entries) {\n                var newEntries = accumEntries ? accumEntries.concat(entries) : entries;\n\n                if (entries.length) {\n                    setTimeout(function() { // prevent stack overflow, however unlikely\n                        getFilesInDirectory(entry, dirReader, newEntries, promise);\n                    }, 0);\n                }\n                else {\n                    promise.success(newEntries);\n                }\n            },\n\n            promise.failure\n        );\n\n        return promise;\n    }\n\n    function handleDataTransfer(dataTransfer, uploadDropZone) {\n        var pendingFolderPromises = [],\n            handleDataTransferPromise = new qq.Promise();\n\n        options.callbacks.processingDroppedFiles();\n        uploadDropZone.dropDisabled(true);\n\n        if (dataTransfer.files.length > 1 && !options.allowMultipleItems) {\n            options.callbacks.processingDroppedFilesComplete([]);\n            options.callbacks.dropError(\"tooManyFilesError\", \"\");\n            uploadDropZone.dropDisabled(false);\n            handleDataTransferPromise.failure();\n        }\n        else {\n            droppedFiles = [];\n\n            if (qq.isFolderDropSupported(dataTransfer)) {\n                qq.each(dataTransfer.items, function(idx, item) {\n                    var entry = item.webkitGetAsEntry();\n\n                    if (entry) {\n                        //due to a bug in Chrome's File System API impl - #149735\n                        if (entry.isFile) {\n                            droppedFiles.push(item.getAsFile());\n                        }\n\n                        else {\n                            pendingFolderPromises.push(traverseFileTree(entry).done(function() {\n                                pendingFolderPromises.pop();\n                                if (pendingFolderPromises.length === 0) {\n                                    handleDataTransferPromise.success();\n                                }\n                            }));\n                        }\n                    }\n                });\n            }\n            else {\n                droppedFiles = dataTransfer.files;\n            }\n\n            if (pendingFolderPromises.length === 0) {\n                handleDataTransferPromise.success();\n            }\n        }\n\n        return handleDataTransferPromise;\n    }\n\n    function setupDropzone(dropArea) {\n        var dropZone = new qq.UploadDropZone({\n            HIDE_ZONES_EVENT_NAME: HIDE_ZONES_EVENT_NAME,\n            element: dropArea,\n            onEnter: function(e) {\n                qq(dropArea).addClass(options.classes.dropActive);\n                e.stopPropagation();\n            },\n            onLeaveNotDescendants: function(e) {\n                qq(dropArea).removeClass(options.classes.dropActive);\n            },\n            onDrop: function(e) {\n                handleDataTransfer(e.dataTransfer, dropZone).then(\n                    function() {\n                        uploadDroppedFiles(droppedFiles, dropZone);\n                    },\n                    function() {\n                        options.callbacks.dropLog(\"Drop event DataTransfer parsing failed.  No files will be uploaded.\", \"error\");\n                    }\n                );\n            }\n        });\n\n        disposeSupport.addDisposer(function() {\n            dropZone.dispose();\n        });\n\n        qq(dropArea).hasAttribute(HIDE_BEFORE_ENTER_ATTR) && qq(dropArea).hide();\n\n        uploadDropZones.push(dropZone);\n\n        return dropZone;\n    }\n\n    function isFileDrag(dragEvent) {\n        var fileDrag;\n\n        qq.each(dragEvent.dataTransfer.types, function(key, val) {\n            if (val === \"Files\") {\n                fileDrag = true;\n                return false;\n            }\n        });\n\n        return fileDrag;\n    }\n\n    // Attempt to determine when the file has left the document.  It is not always possible to detect this\n    // in all cases, but it is generally possible in all browsers, with a few exceptions.\n    //\n    // Exceptions:\n    // * IE10+ & Safari: We can't detect a file leaving the document if the Explorer window housing the file\n    //                   overlays the browser window.\n    // * IE10+: If the file is dragged out of the window too quickly, IE does not set the expected values of the\n    //          event's X & Y properties.\n    function leavingDocumentOut(e) {\n        if (qq.firefox()) {\n            return !e.relatedTarget;\n        }\n\n        if (qq.safari()) {\n            return e.x < 0 || e.y < 0;\n        }\n\n        return e.x === 0 && e.y === 0;\n    }\n\n    function setupDragDrop() {\n        var dropZones = options.dropZoneElements,\n\n            maybeHideDropZones = function() {\n                setTimeout(function() {\n                    qq.each(dropZones, function(idx, dropZone) {\n                        qq(dropZone).hasAttribute(HIDE_BEFORE_ENTER_ATTR) && qq(dropZone).hide();\n                        qq(dropZone).removeClass(options.classes.dropActive);\n                    });\n                }, 10);\n            };\n\n        qq.each(dropZones, function(idx, dropZone) {\n            var uploadDropZone = setupDropzone(dropZone);\n\n            // IE <= 9 does not support the File API used for drag+drop uploads\n            if (dropZones.length && qq.supportedFeatures.fileDrop) {\n                disposeSupport.attach(document, \"dragenter\", function(e) {\n                    if (!uploadDropZone.dropDisabled() && isFileDrag(e)) {\n                        qq.each(dropZones, function(idx, dropZone) {\n                            // We can't apply styles to non-HTMLElements, since they lack the `style` property.\n                            // Also, if the drop zone isn't initially hidden, let's not mess with `style.display`.\n                            if (dropZone instanceof HTMLElement &&\n                                qq(dropZone).hasAttribute(HIDE_BEFORE_ENTER_ATTR)) {\n\n                                qq(dropZone).css({display: \"block\"});\n                            }\n                        });\n                    }\n                });\n            }\n        });\n\n        disposeSupport.attach(document, \"dragleave\", function(e) {\n            if (leavingDocumentOut(e)) {\n                maybeHideDropZones();\n            }\n        });\n\n        // Just in case we were not able to detect when a dragged file has left the document,\n        // hide all relevant drop zones the next time the mouse enters the document.\n        // Note that mouse events such as this one are not fired during drag operations.\n        disposeSupport.attach(qq(document).children()[0], \"mouseenter\", function(e) {\n            maybeHideDropZones();\n        });\n\n        disposeSupport.attach(document, \"drop\", function(e) {\n            e.preventDefault();\n            maybeHideDropZones();\n        });\n\n        disposeSupport.attach(document, HIDE_ZONES_EVENT_NAME, maybeHideDropZones);\n    }\n\n    setupDragDrop();\n\n    qq.extend(this, {\n        setupExtraDropzone: function(element) {\n            options.dropZoneElements.push(element);\n            setupDropzone(element);\n        },\n\n        removeDropzone: function(element) {\n            var i,\n                dzs = options.dropZoneElements;\n\n            for (i in dzs) {\n                if (dzs[i] === element) {\n                    return dzs.splice(i, 1);\n                }\n            }\n        },\n\n        dispose: function() {\n            disposeSupport.dispose();\n            qq.each(uploadDropZones, function(idx, dropZone) {\n                dropZone.dispose();\n            });\n        }\n    });\n};\n\nqq.DragAndDrop.callbacks = function() {\n    \"use strict\";\n\n    return {\n        processingDroppedFiles: function() {},\n        processingDroppedFilesComplete: function(files, targetEl) {},\n        dropError: function(code, errorSpecifics) {\n            qq.log(\"Drag & drop error code '\" + code + \" with these specifics: '\" + errorSpecifics + \"'\", \"error\");\n        },\n        dropLog: function(message, level) {\n            qq.log(message, level);\n        }\n    };\n};\n\nqq.UploadDropZone = function(o) {\n    \"use strict\";\n\n    var disposeSupport = new qq.DisposeSupport(),\n        options, element, preventDrop, dropOutsideDisabled;\n\n    options = {\n        element: null,\n        onEnter: function(e) {},\n        onLeave: function(e) {},\n        // is not fired when leaving element by hovering descendants\n        onLeaveNotDescendants: function(e) {},\n        onDrop: function(e) {}\n    };\n\n    qq.extend(options, o);\n    element = options.element;\n\n    function dragoverShouldBeCanceled() {\n        return qq.safari() || (qq.firefox() && qq.windows());\n    }\n\n    function disableDropOutside(e) {\n        // run only once for all instances\n        if (!dropOutsideDisabled) {\n\n            // for these cases we need to catch onDrop to reset dropArea\n            if (dragoverShouldBeCanceled) {\n                disposeSupport.attach(document, \"dragover\", function(e) {\n                    e.preventDefault();\n                });\n            } else {\n                disposeSupport.attach(document, \"dragover\", function(e) {\n                    if (e.dataTransfer) {\n                        e.dataTransfer.dropEffect = \"none\";\n                        e.preventDefault();\n                    }\n                });\n            }\n\n            dropOutsideDisabled = true;\n        }\n    }\n\n    function isValidFileDrag(e) {\n        // e.dataTransfer currently causing IE errors\n        // IE9 does NOT support file API, so drag-and-drop is not possible\n        if (!qq.supportedFeatures.fileDrop) {\n            return false;\n        }\n\n        var effectTest, dt = e.dataTransfer,\n        // do not check dt.types.contains in webkit, because it crashes safari 4\n        isSafari = qq.safari();\n\n        // dt.effectAllowed is none in Safari 5\n        // dt.types.contains check is for firefox\n\n        // dt.effectAllowed crashes IE 11 & 10 when files have been dragged from\n        // the filesystem\n        effectTest = qq.ie() && qq.supportedFeatures.fileDrop ? true : dt.effectAllowed !== \"none\";\n        return dt && effectTest && (dt.files || (!isSafari && dt.types.contains && dt.types.contains(\"Files\")));\n    }\n\n    function isOrSetDropDisabled(isDisabled) {\n        if (isDisabled !== undefined) {\n            preventDrop = isDisabled;\n        }\n        return preventDrop;\n    }\n\n    function triggerHidezonesEvent() {\n        var hideZonesEvent;\n\n        function triggerUsingOldApi() {\n            hideZonesEvent = document.createEvent(\"Event\");\n            hideZonesEvent.initEvent(options.HIDE_ZONES_EVENT_NAME, true, true);\n        }\n\n        if (window.CustomEvent) {\n            try {\n                hideZonesEvent = new CustomEvent(options.HIDE_ZONES_EVENT_NAME);\n            }\n            catch (err) {\n                triggerUsingOldApi();\n            }\n        }\n        else {\n            triggerUsingOldApi();\n        }\n\n        document.dispatchEvent(hideZonesEvent);\n    }\n\n    function attachEvents() {\n        disposeSupport.attach(element, \"dragover\", function(e) {\n            if (!isValidFileDrag(e)) {\n                return;\n            }\n\n            // dt.effectAllowed crashes IE 11 & 10 when files have been dragged from\n            // the filesystem\n            var effect = qq.ie() && qq.supportedFeatures.fileDrop ? null : e.dataTransfer.effectAllowed;\n            if (effect === \"move\" || effect === \"linkMove\") {\n                e.dataTransfer.dropEffect = \"move\"; // for FF (only move allowed)\n            } else {\n                e.dataTransfer.dropEffect = \"copy\"; // for Chrome\n            }\n\n            e.stopPropagation();\n            e.preventDefault();\n        });\n\n        disposeSupport.attach(element, \"dragenter\", function(e) {\n            if (!isOrSetDropDisabled()) {\n                if (!isValidFileDrag(e)) {\n                    return;\n                }\n                options.onEnter(e);\n            }\n        });\n\n        disposeSupport.attach(element, \"dragleave\", function(e) {\n            if (!isValidFileDrag(e)) {\n                return;\n            }\n\n            options.onLeave(e);\n\n            var relatedTarget = document.elementFromPoint(e.clientX, e.clientY);\n            // do not fire when moving a mouse over a descendant\n            if (qq(this).contains(relatedTarget)) {\n                return;\n            }\n\n            options.onLeaveNotDescendants(e);\n        });\n\n        disposeSupport.attach(element, \"drop\", function(e) {\n            if (!isOrSetDropDisabled()) {\n                if (!isValidFileDrag(e)) {\n                    return;\n                }\n\n                e.preventDefault();\n                e.stopPropagation();\n                options.onDrop(e);\n\n                triggerHidezonesEvent();\n            }\n        });\n    }\n\n    disableDropOutside();\n    attachEvents();\n\n    qq.extend(this, {\n        dropDisabled: function(isDisabled) {\n            return isOrSetDropDisabled(isDisabled);\n        },\n\n        dispose: function() {\n            disposeSupport.dispose();\n        },\n\n        getElement: function() {\n            return element;\n        }\n    });\n};\n"]}