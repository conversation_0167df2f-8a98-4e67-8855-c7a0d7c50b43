{"version": 3, "sources": ["?", "../client/js/util.js", "../client/js/export.js", "../client/js/version.js", "../client/js/features.js", "../client/js/promise.js", "../client/js/dnd.js"], "names": ["global", "qq", "element", "hide", "style", "display", "this", "attach", "type", "fn", "addEventListener", "attachEvent", "detach", "removeEventListener", "detachEvent", "contains", "descendant", "compareDocumentPosition", "insertBefore", "elementB", "parentNode", "remove", "<PERSON><PERSON><PERSON><PERSON>", "css", "styles", "Error", "opacity", "filter", "Math", "round", "extend", "hasClass", "name", "considerParent", "re", "RegExp", "test", "className", "addClass", "removeClass", "replace", "getByClass", "first", "candidates", "result", "querySelector", "querySelectorAll", "getElementsByTagName", "each", "idx", "val", "push", "getFirstByClass", "children", "child", "<PERSON><PERSON><PERSON><PERSON>", "nodeType", "nextS<PERSON>ling", "setText", "text", "innerText", "textContent", "clearText", "hasAttribute", "attrName", "attrVal", "exec", "getAttribute", "undefined", "canvasToBlob", "canvas", "mime", "quality", "dataUriToBlob", "toDataURL", "dataUri", "arrayBuffer", "byteString", "createBlob", "data", "BlobBuilder", "window", "WebKitBlobBuilder", "MozBlobBuilder", "MSBlobBuilder", "blobBuilder", "append", "getBlob", "Blob", "intArray", "mimeString", "split", "indexOf", "atob", "decodeURI", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "Uint8Array", "character", "charCodeAt", "log", "message", "level", "console", "isObject", "variable", "Object", "prototype", "toString", "call", "isFunction", "isArray", "value", "buffer", "constructor", "isItemList", "maybeItemList", "isNodeList", "maybeNodeList", "item", "namedItem", "isString", "maybeString", "trimStr", "string", "String", "trim", "format", "str", "args", "Array", "slice", "arguments", "newStr", "nextIdxToReplace", "strBefore", "substring", "strAfter", "isFile", "maybeFile", "File", "isFileList", "maybeFileList", "FileList", "isFileOrInput", "maybeFileOrInput", "isInput", "maybeInput", "notFile", "evaluateType", "normalizedType", "toLowerCase", "HTMLInputElement", "tagName", "isBlob", "maybeBlob", "isXhrUploadSupported", "input", "document", "createElement", "multiple", "FormData", "createXhrInstance", "upload", "XMLHttpRequest", "ActiveXObject", "error", "isFolderDropSupported", "dataTransfer", "items", "webkitGetAsEntry", "isFileChunkingSupported", "androidStock", "webkitSlice", "mozSlice", "sliceBlob", "fileOrBlob", "start", "end", "slicer", "arrayBufferToHex", "bytesAsHex", "bytes", "byt", "byteAsHexStr", "readBlobToHex", "blob", "startOffset", "initialBlob", "fileReader", "FileReader", "promise", "Promise", "onload", "success", "onerror", "failure", "readAsA<PERSON>y<PERSON><PERSON>er", "second", "extendNested", "prop", "override", "target", "sourceFn", "super_", "source", "srcPropName", "srcPropVal", "arr", "elt", "from", "len", "hasOwnProperty", "getUniqueId", "c", "r", "random", "v", "ie", "navigator", "userAgent", "ie7", "ie8", "ie10", "ie11", "edge", "safari", "vendor", "chrome", "opera", "firefox", "windows", "platform", "android", "ios6", "ios", "ios7", "ios8", "ios800", "iosChrome", "iosSafari", "iosSafariWebView", "preventDefault", "e", "returnValue", "toElement", "div", "html", "innerHTML", "iterableItem", "callback", "keyOrIndex", "retVal", "Storage", "key", "getItem", "char<PERSON>t", "bind", "oldFunc", "context", "newArgs", "concat", "apply", "obj2url", "obj", "temp", "prefixDone", "uristrings", "prefix", "add", "nextObj", "i", "nextTemp", "encodeURIComponent", "join", "obj2FormData", "formData", "arrayKeyName", "obj2Inputs", "form", "setAttribute", "append<PERSON><PERSON><PERSON>", "parseJson", "json", "JSON", "parse", "eval", "getExtension", "filename", "extIdx", "lastIndexOf", "substr", "getFilename", "blobOrFileInput", "fileName", "DisposeSupport", "disposers", "dispose", "disposer", "shift", "addDisposer", "disposeFunction", "define", "amd", "module", "exports", "version", "supportedFeatures", "supportsUploading", "supportsUploadingBlobs", "supportsFileDrop", "supportsAjaxFileUploading", "supportsFolderDrop", "supportsChunking", "supportsResume", "supportsUploadViaPaste", "supportsUploadCors", "supportsDeleteFileXdr", "supportsDeleteFileCorsXhr", "supportsDeleteFileCors", "supportsFolderSelection", "supportsImagePreviews", "supportsUploadProgress", "testSupportsFileInputElement", "supported", "tempInput", "disabled", "ex", "isChrome21Or<PERSON>igher", "match", "isChrome14Or<PERSON><PERSON><PERSON>", "isCrossOriginXhrSupported", "xhr", "withCredentials", "isXdrSupported", "XDomainRequest", "isCrossOriginAjaxSupported", "isFolderSelectionSupported", "webkitdirectory", "isLocalStorageSupported", "localStorage", "setItem", "isDragAndDropSupported", "span", "postMessage", "ajaxUploading", "blobUploading", "canDetermineSize", "chunking", "deleteFileCors", "deleteFileCorsXdr", "deleteFileCorsXhr", "dialogElement", "HTMLDialogElement", "fileDrop", "folderDrop", "folderSelection", "imagePreviews", "imageValidation", "itemSizeValidation", "pause", "progressBar", "resume", "scaling", "tiffPreviews", "unlimitedScaledImageSize", "uploading", "uploadCors", "uploadCustomHeaders", "uploadNonMultipart", "uploadViaPaste", "isGenericPromise", "<PERSON><PERSON><PERSON><PERSON>", "then", "success<PERSON><PERSON>s", "failureArgs", "successCallbacks", "failureCallbacks", "doneCallbacks", "state", "onSuccess", "onFailure", "done", "DragAndDrop", "o", "options", "HIDE_ZONES_EVENT_NAME", "HIDE_BEFORE_ENTER_ATTR", "uploadDropZones", "droppedFiles", "disposeSupport", "dropZoneElements", "allowMultipleItems", "classes", "dropActive", "callbacks", "uploadDroppedFiles", "files", "uploadDropZone", "filesAsArray", "dropLog", "dropDisabled", "processingDroppedFilesComplete", "getElement", "traverseFileTree", "entry", "parseEntryPromise", "file", "fullPath", "indexOfNameInFullPath", "qqPath", "fileError", "code", "isDirectory", "getFilesInDirectory", "allEntriesRead", "entries", "entriesLeft", "readFailure", "reader", "accumEntries", "existingPromise", "<PERSON><PERSON><PERSON><PERSON>", "createReader", "readEntries", "readSuccess", "newEntries", "setTimeout", "handleDataTransfer", "pendingFolderPromises", "handleDataTransferPromise", "processingDroppedFiles", "dropError", "getAsFile", "pop", "setupDropzone", "dropArea", "dropZone", "UploadDropZone", "onEnter", "stopPropagation", "onLeaveNotDescendants", "onDrop", "isFileDrag", "dragEvent", "fileDrag", "types", "leavingDocumentOut", "relatedTarget", "x", "y", "setupDragDrop", "dropZones", "maybeHideDropZones", "HTMLElement", "setupExtraDropzone", "removeDropzone", "dzs", "splice", "targetEl", "errorSpecifics", "preventDrop", "dropOutsideDisabled", "onLeave", "dragoverShouldBeCanceled", "disableDropOutside", "dropEffect", "isValidFileDrag", "effectTest", "dt", "<PERSON><PERSON><PERSON><PERSON>", "effectAllowed", "isOrSetDropDisabled", "isDisabled", "triggerHidezonesEvent", "hideZonesEvent", "triggerUsingOldApi", "createEvent", "initEvent", "CustomEvent", "err", "dispatchEvent", "attachEvents", "effect", "elementFromPoint", "clientX", "clientY"], "mappings": ";CAAA,SAAUA;ICEV,IAAIC,KAAK,SAASC;QACd;QAEA;YACIC,MAAM;gBACFD,QAAQE,MAAMC,UAAU;gBACxB,OAAOC;;YAIXC,QAAQ,SAASC,MAAMC;gBACnB,IAAIP,QAAQQ,kBAAkB;oBAC1BR,QAAQQ,iBAAiBF,MAAMC,IAAI;uBAChC,IAAIP,QAAQS,aAAa;oBAC5BT,QAAQS,YAAY,OAAOH,MAAMC;;gBAErC,OAAO;oBACHR,GAAGC,SAASU,OAAOJ,MAAMC;;;YAIjCG,QAAQ,SAASJ,MAAMC;gBACnB,IAAIP,QAAQW,qBAAqB;oBAC7BX,QAAQW,oBAAoBL,MAAMC,IAAI;uBACnC,IAAIP,QAAQS,aAAa;oBAC5BT,QAAQY,YAAY,OAAON,MAAMC;;gBAErC,OAAOH;;YAGXS,UAAU,SAASC;gBAKf,KAAKA,YAAY;oBACb,OAAO;;gBAIX,IAAId,YAAYc,YAAY;oBACxB,OAAO;;gBAGX,IAAId,QAAQa,UAAU;oBAClB,OAAOb,QAAQa,SAASC;uBACrB;oBAEH,UAAUA,WAAWC,wBAAwBf,WAAW;;;YAOhEgB,cAAc,SAASC;gBACnBA,SAASC,WAAWF,aAAahB,SAASiB;gBAC1C,OAAOb;;YAGXe,QAAQ;gBACJnB,QAAQkB,WAAWE,YAAYpB;gBAC/B,OAAOI;;YAOXiB,KAAK,SAASC;gBAEV,IAAItB,QAAQE,SAAS,MAAM;oBACvB,MAAM,IAAIH,GAAGwB,MAAM;;gBAIvB,IAAID,OAAOE,WAAW,MAAM;oBACxB,WAAWxB,QAAQE,MAAMsB,YAAY,mBAAoBxB,QAAe,YAAM,aAAa;wBACvFsB,OAAOG,SAAS,mBAAmBC,KAAKC,MAAM,MAAML,OAAOE,WAAW;;;gBAG9EzB,GAAG6B,OAAO5B,QAAQE,OAAOoB;gBAEzB,OAAOlB;;YAGXyB,UAAU,SAASC,MAAMC;gBACrB,IAAIC,KAAK,IAAIC,OAAO,UAAUH,OAAO;gBACrC,OAAOE,GAAGE,KAAKlC,QAAQmC,iBAAiBJ,kBAAkBC,GAAGE,KAAKlC,QAAQkB,WAAWiB;;YAGzFC,UAAU,SAASN;gBACf,KAAK/B,GAAGC,SAAS6B,SAASC,OAAO;oBAC7B9B,QAAQmC,aAAa,MAAML;;gBAE/B,OAAO1B;;YAGXiC,aAAa,SAASP;gBAClB,IAAIE,KAAK,IAAIC,OAAO,UAAUH,OAAO;gBACrC9B,QAAQmC,YAAYnC,QAAQmC,UAAUG,QAAQN,IAAI,KAAKM,QAAQ,cAAc;gBAC7E,OAAOlC;;YAGXmC,YAAY,SAASJ,WAAWK;gBAC5B,IAAIC,YACAC;gBAEJ,IAAIF,SAASxC,QAAQ2C,eAAe;oBAChC,OAAO3C,QAAQ2C,cAAc,MAAMR;uBAElC,IAAInC,QAAQ4C,kBAAkB;oBAC/B,OAAO5C,QAAQ4C,iBAAiB,MAAMT;;gBAG1CM,aAAazC,QAAQ6C,qBAAqB;gBAE1C9C,GAAG+C,KAAKL,YAAY,SAASM,KAAKC;oBAC9B,IAAIjD,GAAGiD,KAAKnB,SAASM,YAAY;wBAC7BO,OAAOO,KAAKD;;;gBAGpB,OAAOR,QAAQE,OAAO,KAAKA;;YAG/BQ,iBAAiB,SAASf;gBACtB,OAAOpC,GAAGC,SAASuC,WAAWJ,WAAW;;YAG7CgB,UAAU;gBACN,IAAIA,eACAC,QAAQpD,QAAQqD;gBAEpB,OAAOD,OAAO;oBACV,IAAIA,MAAME,aAAa,GAAG;wBACtBH,SAASF,KAAKG;;oBAElBA,QAAQA,MAAMG;;gBAGlB,OAAOJ;;YAGXK,SAAS,SAASC;gBACdzD,QAAQ0D,YAAYD;gBACpBzD,QAAQ2D,cAAcF;gBACtB,OAAOrD;;YAGXwD,WAAW;gBACP,OAAO7D,GAAGC,SAASwD,QAAQ;;YAK/BK,cAAc,SAASC;gBACnB,IAAIC;gBAEJ,IAAI/D,QAAQ6D,cAAc;oBAEtB,KAAK7D,QAAQ6D,aAAaC,WAAW;wBACjC,OAAO;;oBAIX,OAAO,WAAaE,KAAKhE,QAAQiE,aAAaH,cAAc;uBAE3D;oBACDC,UAAU/D,QAAQ8D;oBAElB,IAAIC,YAAYG,WAAW;wBACvB,OAAO;;oBAIX,OAAO,WAAaF,KAAKD,YAAY;;;;;KAMpD;QACG;QAEAhE,GAAGoE,eAAe,SAASC,QAAQC,MAAMC;YACrC,OAAOvE,GAAGwE,cAAcH,OAAOI,UAAUH,MAAMC;;QAGnDvE,GAAGwE,gBAAgB,SAASE;YACxB,IAAIC,aAAaC,YACbC,aAAa,SAASC,MAAMR;gBACxB,IAAIS,cAAcC,OAAOD,eACjBC,OAAOC,qBACPD,OAAOE,kBACPF,OAAOG,eACXC,cAAcL,eAAe,IAAIA;gBAErC,IAAIK,aAAa;oBACbA,YAAYC,OAAOP;oBACnB,OAAOM,YAAYE,QAAQhB;uBAE1B;oBACD,OAAO,IAAIiB,OAAMT;wBAAQvE,MAAM+D;;;eAGvCkB,UAAUC;YAGd,IAAIf,QAAQgB,MAAM,KAAK,GAAGC,QAAQ,aAAa,GAAG;gBAC9Cf,aAAagB,KAAKlB,QAAQgB,MAAM,KAAK;mBAEpC;gBACDd,aAAaiB,UAAUnB,QAAQgB,MAAM,KAAK;;YAI9CD,aAAaf,QAAQgB,MAAM,KAAK,GAC3BA,MAAM,KAAK,GACXA,MAAM,KAAK;YAGhBf,cAAc,IAAImB,YAAYlB,WAAWmB;YACzCP,WAAW,IAAIQ,WAAWrB;YAC1B3E,GAAG+C,KAAK6B,YAAY,SAAS5B,KAAKiD;gBAC9BT,SAASxC,OAAOiD,UAAUC,WAAW;;YAGzC,OAAOrB,WAAWF,aAAac;;QAGnCzF,GAAGmG,MAAM,SAASC,SAASC;YACvB,IAAIrB,OAAOsB,SAAS;gBAChB,KAAKD,SAASA,UAAU,QAAQ;oBAC5BrB,OAAOsB,QAAQH,IAAIC;uBAGvB;oBACI,IAAIpB,OAAOsB,QAAQD,QAAQ;wBACvBrB,OAAOsB,QAAQD,OAAOD;2BAErB;wBACDpB,OAAOsB,QAAQH,IAAI,MAAME,QAAQ,OAAOD;;;;;QAMxDpG,GAAGuG,WAAW,SAASC;YACnB,OAAOA,aAAaA,SAASjD,YAAYkD,OAAOC,UAAUC,SAASC,KAAKJ,cAAc;;QAG1FxG,GAAG6G,aAAa,SAASL;YACrB,cAAc,aAAe;;QASjCxG,GAAG8G,UAAU,SAASC;YAClB,OAAON,OAAOC,UAAUC,SAASC,KAAKG,WAAW,oBAC5CA,SAAS/B,OAAOc,eAAeiB,MAAMC,UAAUD,MAAMC,OAAOC,gBAAgBnB;;QAIrF9F,GAAGkH,aAAa,SAASC;YACrB,OAAOV,OAAOC,UAAUC,SAASC,KAAKO,mBAAmB;;QAK7DnH,GAAGoH,aAAa,SAASC;YACrB,OAAOZ,OAAOC,UAAUC,SAASC,KAAKS,mBAAmB,uBAGpDA,cAAcC,QAAQD,cAAcE;;QAG7CvH,GAAGwH,WAAW,SAASC;YACnB,OAAOhB,OAAOC,UAAUC,SAASC,KAAKa,iBAAiB;;QAG3DzH,GAAG0H,UAAU,SAASC;YAClB,IAAIC,OAAOlB,UAAUmB,MAAM;gBACvB,OAAOF,OAAOE;;YAGlB,OAAOF,OAAOpF,QAAQ,cAAc;;QAOxCvC,GAAG8H,SAAS,SAASC;YAEjB,IAAIC,OAAQC,MAAMvB,UAAUwB,MAAMtB,KAAKuB,WAAW,IAC9CC,SAASL,KACTM,mBAAmBD,OAAOzC,QAAQ;YAEtC3F,GAAG+C,KAAKiF,MAAM,SAAShF,KAAKC;gBACxB,IAAIqF,YAAYF,OAAOG,UAAU,GAAGF,mBAChCG,WAAWJ,OAAOG,UAAUF,mBAAmB;gBAEnDD,SAASE,YAAYrF,MAAMuF;gBAC3BH,mBAAmBD,OAAOzC,QAAQ,MAAM0C,mBAAmBpF,IAAI8C;gBAG/D,IAAIsC,mBAAmB,GAAG;oBACtB,OAAO;;;YAIf,OAAOD;;QAGXpI,GAAGyI,SAAS,SAASC;YACjB,OAAO1D,OAAO2D,QAAQlC,OAAOC,UAAUC,SAASC,KAAK8B,eAAe;;QAGxE1I,GAAG4I,aAAa,SAASC;YACrB,OAAO7D,OAAO8D,YAAYrC,OAAOC,UAAUC,SAASC,KAAKiC,mBAAmB;;QAGhF7I,GAAG+I,gBAAgB,SAASC;YACxB,OAAOhJ,GAAGyI,OAAOO,qBAAqBhJ,GAAGiJ,QAAQD;;QAGrDhJ,GAAGiJ,UAAU,SAASC,YAAYC;YAC9B,IAAIC,eAAe,SAAS7I;gBACxB,IAAI8I,iBAAiB9I,KAAK+I;gBAE1B,IAAIH,SAAS;oBACT,OAAOE,mBAAmB;;gBAG9B,OAAOA,mBAAmB;;YAG9B,IAAIrE,OAAOuE,kBAAkB;gBACzB,IAAI9C,OAAOC,UAAUC,SAASC,KAAKsC,gBAAgB,6BAA6B;oBAC5E,IAAIA,WAAW3I,QAAQ6I,aAAaF,WAAW3I,OAAO;wBAClD,OAAO;;;;YAInB,IAAI2I,WAAWM,SAAS;gBACpB,IAAIN,WAAWM,QAAQF,kBAAkB,SAAS;oBAC9C,IAAIJ,WAAW3I,QAAQ6I,aAAaF,WAAW3I,OAAO;wBAClD,OAAO;;;;YAKnB,OAAO;;QAGXP,GAAGyJ,SAAS,SAASC;YACjB,IAAI1E,OAAOO,QAAQkB,OAAOC,UAAUC,SAASC,KAAK8C,eAAe,iBAAiB;gBAC9E,OAAO;;;QAIf1J,GAAG2J,uBAAuB;YACtB,IAAIC,QAAQC,SAASC,cAAc;YACnCF,MAAMrJ,OAAO;YAEb,OACIqJ,MAAMG,aAAa5F,oBACRwE,SAAS,sBACTqB,aAAa,sBACZhK,GAAGiK,oBAAqBC,WAAW;;QAIvDlK,GAAGiK,oBAAoB;YACnB,IAAIjF,OAAOmF,gBAAgB;gBACvB,OAAO,IAAIA;;YAGf;gBACI,OAAO,IAAIC,cAAc;cAE7B,OAAOC;gBACHrK,GAAGmG,IAAI,yCAAyC;gBAChD,OAAO;;;QAIfnG,GAAGsK,wBAAwB,SAASC;YAChC,OAAOA,aAAaC,SAChBD,aAAaC,MAAMzE,SAAS,KAC5BwE,aAAaC,MAAM,GAAGC;;QAG9BzK,GAAG0K,0BAA0B;YACzB,QAAQ1K,GAAG2K,kBACP3K,GAAG2J,2BACFhB,KAAKjC,UAAUwB,UAAU/D,aAAawE,KAAKjC,UAAUkE,gBAAgBzG,aAAawE,KAAKjC,UAAUmE,aAAa1G;;QAGvHnE,GAAG8K,YAAY,SAASC,YAAYC,OAAOC;YACvC,IAAIC,SAASH,WAAW7C,SAAS6C,WAAWF,YAAYE,WAAWH;YAEnE,OAAOM,OAAOtE,KAAKmE,YAAYC,OAAOC;;QAG1CjL,GAAGmL,mBAAmB,SAASnE;YAC3B,IAAIoE,aAAa,IACbC,QAAQ,IAAIrF,WAAWgB;YAE3BhH,GAAG+C,KAAKsI,OAAO,SAASrI,KAAKsI;gBACzB,IAAIC,eAAeD,IAAI3E,SAAS;gBAEhC,IAAI4E,aAAaxF,SAAS,GAAG;oBACzBwF,eAAe,MAAMA;;gBAGzBH,cAAcG;;YAGlB,OAAOH;;QAGXpL,GAAGwL,gBAAgB,SAASC,MAAMC,aAAa3F;YAC3C,IAAI4F,cAAc3L,GAAG8K,UAAUW,MAAMC,aAAaA,cAAc3F,SAC5D6F,aAAa,IAAIC,cACjBC,UAAU,IAAI9L,GAAG+L;YAErBH,WAAWI,SAAS;gBAChBF,QAAQG,QAAQjM,GAAGmL,iBAAiBS,WAAWjJ;;YAGnDiJ,WAAWM,UAAUJ,QAAQK;YAE7BP,WAAWQ,kBAAkBT;YAE7B,OAAOG;;QAGX9L,GAAG6B,SAAS,SAASY,OAAO4J,QAAQC;YAChCtM,GAAG+C,KAAKsJ,QAAQ,SAASE,MAAMtJ;gBAC3B,IAAIqJ,gBAAgBtM,GAAGuG,SAAStD,MAAM;oBAClC,IAAIR,MAAM8J,UAAUpI,WAAW;wBAC3B1B,MAAM8J;;oBAEVvM,GAAG6B,OAAOY,MAAM8J,OAAOtJ,KAAK;uBAE3B;oBACDR,MAAM8J,QAAQtJ;;;YAItB,OAAOR;;QAaXzC,GAAGwM,WAAW,SAASC,QAAQC;YAC3B,IAAIC,aACAC,SAASF,SAASC;YAEtB3M,GAAG+C,KAAK6J,QAAQ,SAASC,aAAaC;gBAClC,IAAIL,OAAOI,iBAAiB1I,WAAW;oBACnCwI,OAAOE,eAAeJ,OAAOI;;gBAGjCJ,OAAOI,eAAeC;;YAG1B,OAAOL;;QAMXzM,GAAG2F,UAAU,SAASoH,KAAKC,KAAKC;YAC5B,IAAIF,IAAIpH,SAAS;gBACb,OAAOoH,IAAIpH,QAAQqH,KAAKC;;YAG5BA,OAAOA,QAAQ;YACf,IAAIC,MAAMH,IAAIhH;YAEd,IAAIkH,OAAO,GAAG;gBACVA,QAAQC;;YAGZ,MAAOD,OAAOC,KAAKD,QAAQ,GAAG;gBAC1B,IAAIF,IAAII,eAAeF,SAASF,IAAIE,UAAUD,KAAK;oBAC/C,OAAOC;;;YAGf,QAAQ;;QAIZjN,GAAGoN,cAAc;YACb,OAAO,uCAAuC7K,QAAQ,SAAS,SAAS8K;gBAEpE,IAAIC,IAAI3L,KAAK4L,WAAW,KAAK,GAAGC,IAAIH,KAAK,MAAMC,IAAKA,IAAI,IAAM;gBAC9D,OAAOE,EAAE7G,SAAS;;;QAM1B3G,GAAGyN,KAAK;YACJ,OAAOC,UAAUC,UAAUhI,QAAQ,aAAa,KAC5C+H,UAAUC,UAAUhI,QAAQ,gBAAgB;;QAGpD3F,GAAG4N,MAAM;YACL,OAAOF,UAAUC,UAAUhI,QAAQ,eAAe;;QAGtD3F,GAAG6N,MAAM;YACL,OAAOH,UAAUC,UAAUhI,QAAQ,eAAe;;QAGtD3F,GAAG8N,OAAO;YACN,OAAOJ,UAAUC,UAAUhI,QAAQ,gBAAgB;;QAGvD3F,GAAG+N,OAAO;YACN,OAAO/N,GAAGyN,QAAQC,UAAUC,UAAUhI,QAAQ,cAAc;;QAGhE3F,GAAGgO,OAAO;YACN,OAAON,UAAUC,UAAUhI,QAAQ,WAAW;;QAGlD3F,GAAGiO,SAAS;YACR,OAAOP,UAAUQ,WAAW/J,aAAauJ,UAAUQ,OAAOvI,QAAQ,cAAc;;QAGpF3F,GAAGmO,SAAS;YACR,OAAOT,UAAUQ,WAAW/J,aAAauJ,UAAUQ,OAAOvI,QAAQ,eAAe;;QAGrF3F,GAAGoO,QAAQ;YACP,OAAOV,UAAUQ,WAAW/J,aAAauJ,UAAUQ,OAAOvI,QAAQ,cAAc;;QAGpF3F,GAAGqO,UAAU;YACT,QAASrO,GAAGgO,WAAWhO,GAAG+N,UAAUL,UAAUC,UAAUhI,QAAQ,gBAAgB,KAAK+H,UAAUQ,WAAW/J,aAAauJ,UAAUQ,WAAW;;QAGhJlO,GAAGsO,UAAU;YACT,OAAOZ,UAAUa,aAAa;;QAGlCvO,GAAGwO,UAAU;YACT,OAAOd,UAAUC,UAAUrE,cAAc3D,QAAQ,gBAAgB;;QAKrE3F,GAAG2K,eAAe;YACd,OAAO3K,GAAGwO,aAAad,UAAUC,UAAUrE,cAAc3D,QAAQ,YAAY;;QAGjF3F,GAAGyO,OAAO;YACN,OAAOzO,GAAG0O,SAAShB,UAAUC,UAAUhI,QAAQ,eAAe;;QAGlE3F,GAAG2O,OAAO;YACN,OAAO3O,GAAG0O,SAAShB,UAAUC,UAAUhI,QAAQ,eAAe;;QAGlE3F,GAAG4O,OAAO;YACN,OAAO5O,GAAG0O,SAAShB,UAAUC,UAAUhI,QAAQ,eAAe;;QAIlE3F,GAAG6O,SAAS;YACR,OAAO7O,GAAG0O,SAAShB,UAAUC,UAAUhI,QAAQ,iBAAiB;;QAGpE3F,GAAG0O,MAAM;YAEL,OAAOhB,UAAUC,UAAUhI,QAAQ,aAAa,KACzC+H,UAAUC,UAAUhI,QAAQ,aAAa,KACzC+H,UAAUC,UAAUhI,QAAQ,eAAe;;QAGtD3F,GAAG8O,YAAY;YACX,OAAO9O,GAAG0O,SAAShB,UAAUC,UAAUhI,QAAQ,cAAc;;QAGjE3F,GAAG+O,YAAY;YACX,OAAO/O,GAAG0O,UAAU1O,GAAG8O,eAAepB,UAAUC,UAAUhI,QAAQ,eAAe;;QAGrF3F,GAAGgP,mBAAmB;YAClB,OAAOhP,GAAG0O,UAAU1O,GAAG8O,gBAAgB9O,GAAG+O;;QAM9C/O,GAAGiP,iBAAiB,SAASC;YACzB,IAAIA,EAAED,gBAAgB;gBAClBC,EAAED;mBACC;gBACHC,EAAEC,cAAc;;;QAQxBnP,GAAGoP,YAAa;YACZ,IAAIC,MAAMxF,SAASC,cAAc;YACjC,OAAO,SAASwF;gBACZD,IAAIE,YAAYD;gBAChB,IAAIrP,UAAUoP,IAAI/L;gBAClB+L,IAAIhO,YAAYpB;gBAChB,OAAOA;;;QAKfD,GAAG+C,OAAO,SAASyM,cAAcC;YAC7B,IAAIC,YAAYC;YAEhB,IAAIH,cAAc;gBAEd,IAAIxK,OAAO4K,WAAWJ,aAAavI,gBAAgBjC,OAAO4K,SAAS;oBAC/D,KAAKF,aAAa,GAAGA,aAAaF,aAAazJ,QAAQ2J,cAAc;wBACjEC,SAASF,SAASD,aAAaK,IAAIH,aAAaF,aAAaM,QAAQN,aAAaK,IAAIH;wBACtF,IAAIC,WAAW,OAAO;4BAClB;;;uBAMP,IAAI3P,GAAG8G,QAAQ0I,iBAAiBxP,GAAGkH,WAAWsI,iBAAiBxP,GAAGoH,WAAWoI,eAAe;oBAC7F,KAAKE,aAAa,GAAGA,aAAaF,aAAazJ,QAAQ2J,cAAc;wBACjEC,SAASF,SAASC,YAAYF,aAAaE;wBAC3C,IAAIC,WAAW,OAAO;4BAClB;;;uBAIP,IAAI3P,GAAGwH,SAASgI,eAAe;oBAChC,KAAKE,aAAa,GAAGA,aAAaF,aAAazJ,QAAQ2J,cAAc;wBACjEC,SAASF,SAASC,YAAYF,aAAaO,OAAOL;wBAClD,IAAIC,WAAW,OAAO;4BAClB;;;uBAIP;oBACD,KAAKD,cAAcF,cAAc;wBAC7B,IAAI/I,OAAOC,UAAUyG,eAAevG,KAAK4I,cAAcE,aAAa;4BAChEC,SAASF,SAASC,YAAYF,aAAaE;4BAC3C,IAAIC,WAAW,OAAO;gCAClB;;;;;;;QASxB3P,GAAGgQ,OAAO,SAASC,SAASC;YACxB,IAAIlQ,GAAG6G,WAAWoJ,UAAU;gBACxB,IAAIjI,OAAQC,MAAMvB,UAAUwB,MAAMtB,KAAKuB,WAAW;gBAElD,OAAO;oBACH,IAAIgI,UAAUnQ,GAAG6B,WAAWmG;oBAC5B,IAAIG,UAAUpC,QAAQ;wBAClBoK,UAAUA,QAAQC,OAAOnI,MAAMvB,UAAUwB,MAAMtB,KAAKuB;;oBAExD,OAAO8H,QAAQI,MAAMH,SAASC;;;YAItC,MAAM,IAAI3O,MAAM;;QAmBpBxB,GAAGsQ,UAAU,SAASC,KAAKC,MAAMC;YAE7B,IAAIC,iBACAC,SAAS,KACTC,MAAM,SAASC,SAASC;gBACpB,IAAIC,WAAWP,OACR,QAAQrO,KAAKqO,QACdA,OACAA,OAAO,MAAMM,IAAI,MACjBA;gBACN,IAAKC,aAAa,eAAiBD,MAAM,aAAc;oBACnDJ,WAAWxN,YACC2N,YAAY,WACd7Q,GAAGsQ,QAAQO,SAASE,UAAU,QAC7BtK,OAAOC,UAAUC,SAASC,KAAKiK,aAAa,sBAC7CG,mBAAmBD,YAAY,MAAMC,mBAAmBH,aACxDG,mBAAmBD,YAAY,MAAMC,mBAAmBH;;;YAK9E,KAAKJ,cAAcD,MAAM;gBACrBG,SAAU,KAAKxO,KAAKqO,QAAU,MAAMrO,KAAKqO,QAAS,KAAK,MAAM;gBAC7DE,WAAWxN,KAAKsN;gBAChBE,WAAWxN,KAAKlD,GAAGsQ,QAAQC;mBACxB,IAAK9J,OAAOC,UAAUC,SAASC,KAAK2J,SAAS,2BAA6BA,QAAQ,aAAc;gBACnGvQ,GAAG+C,KAAKwN,KAAK,SAASvN,KAAKC;oBACvB2N,IAAI3N,KAAKD;;mBAEV,WAAYuN,QAAQ,eAAiBA,QAAQ,eAAiBA,QAAQ,UAAW;gBACpFvQ,GAAG+C,KAAKwN,KAAK,SAAShE,MAAMtJ;oBACxB2N,IAAI3N,KAAKsJ;;mBAEV;gBACHmE,WAAWxN,KAAK8N,mBAAmBR,QAAQ,MAAMQ,mBAAmBT;;YAGxE,IAAIC,MAAM;gBACN,OAAOE,WAAWO,KAAKN;mBACpB;gBACH,OAAOD,WAAWO,KAAKN,QAClBpO,QAAQ,MAAM,IACdA,QAAQ,QAAQ;;;QAI7BvC,GAAGkR,eAAe,SAASX,KAAKY,UAAUC;YACtC,KAAKD,UAAU;gBACXA,WAAW,IAAInH;;YAGnBhK,GAAG+C,KAAKwN,KAAK,SAASV,KAAK5M;gBACvB4M,MAAMuB,eAAeA,eAAe,MAAMvB,MAAM,MAAMA;gBAEtD,IAAI7P,GAAGuG,SAAStD,MAAM;oBAClBjD,GAAGkR,aAAajO,KAAKkO,UAAUtB;uBAE9B,IAAI7P,GAAG6G,WAAW5D,MAAM;oBACzBkO,SAAS9L,OAAOwK,KAAK5M;uBAEpB;oBACDkO,SAAS9L,OAAOwK,KAAK5M;;;YAI7B,OAAOkO;;QAGXnR,GAAGqR,aAAa,SAASd,KAAKe;YAC1B,IAAI1H;YAEJ,KAAK0H,MAAM;gBACPA,OAAOzH,SAASC,cAAc;;YAGlC9J,GAAGkR,aAAaX;gBACZlL,QAAQ,SAASwK,KAAK5M;oBAClB2G,QAAQC,SAASC,cAAc;oBAC/BF,MAAM2H,aAAa,QAAQ1B;oBAC3BjG,MAAM2H,aAAa,SAAStO;oBAC5BqO,KAAKE,YAAY5H;;;YAIzB,OAAO0H;;QAOXtR,GAAGyR,YAAY,SAASC;YAEpB,IAAI1M,OAAO2M,QAAQ3R,GAAG6G,WAAW8K,KAAKC,QAAQ;gBAC1C,OAAOD,KAAKC,MAAMF;mBACf;gBACH,OAAOG,KAAK,MAAMH,OAAO;;;QAUjC1R,GAAG8R,eAAe,SAASC;YACvB,IAAIC,SAASD,SAASE,YAAY,OAAO;YAEzC,IAAID,SAAS,GAAG;gBACZ,OAAOD,SAASG,OAAOF,QAAQD,SAAShM,SAASiM;;;QAIzDhS,GAAGmS,cAAc,SAASC;YAGtB,IAAIpS,GAAGiJ,QAAQmJ,kBAAkB;gBAE7B,OAAOA,gBAAgBrL,MAAMxE,QAAQ,aAAa;mBAEjD,IAAIvC,GAAGyI,OAAO2J,kBAAkB;gBACjC,IAAIA,gBAAgBC,aAAa,QAAQD,gBAAgBC,aAAalO,WAAW;oBAC7E,OAAOiO,gBAAgBC;;;YAI/B,OAAOD,gBAAgBrQ;;QAM3B/B,GAAGsS,iBAAiB;YAChB,IAAIC;YAEJ;gBAEIC,SAAS;oBACL,IAAIC;oBACJ,GAAG;wBACCA,WAAWF,UAAUG;wBACrB,IAAID,UAAU;4BACVA;;6BAGDA;;gBAIXnS,QAAQ;oBACJ,IAAI0H,OAAOG;oBAEX9H,KAAKsS,YAAY3S,GAAGgI,KAAK,IAAI1H,OAAO+P,MAAMhQ,MAAM4H,MAAMvB,UAAUwB,MAAMtB,KAAKuB,WAAW;;gBAI1FwK,aAAa,SAASC;oBAClBL,UAAUrP,KAAK0P;;;;;KCt2B9B;QACG;QACA,WAAWC,WAAW,cAAcA,OAAOC,KAAK;YAC5CD,OAAO;gBACH,OAAO7S;;eAGV,WAAW+S,WAAW,eAAeA,OAAOC,SAAS;YACtDD,OAAOC,UAAUhT;eAEhB;YACDD,OAAOC,KAAKA;;;ICXpBA,GAAGiT,UAAU;ICAbjT,GAAGkT,oBAAqB;QACpB;QAEA,IAAIC,mBACAC,wBACAC,kBACAC,2BACAC,oBACAC,kBACAC,gBACAC,wBACAC,oBACAC,uBACAC,2BACAC,wBACAC,yBACAC,uBACAC;QAEJ,SAASC;YACL,IAAIC,YAAY,MACZC;YAEJ;gBACIA,YAAYvK,SAASC,cAAc;gBACnCsK,UAAU7T,OAAO;gBACjBP,GAAGoU,WAAWlU;gBAEd,IAAIkU,UAAUC,UAAU;oBACpBF,YAAY;;cAGpB,OAAOG;gBACHH,YAAY;;YAGhB,OAAOA;;QAIX,SAASI;YACL,QAAQvU,GAAGmO,YAAYnO,GAAGoO,YACtBV,UAAUC,UAAU6G,MAAM,2CAA2CrQ;;QAI7E,SAASsQ;YACL,QAAQzU,GAAGmO,YAAYnO,GAAGoO,YACtBV,UAAUC,UAAU6G,MAAM,2CAA2CrQ;;QAI7E,SAASuQ;YACL,IAAI1P,OAAOmF,gBAAgB;gBACvB,IAAIwK,MAAM3U,GAAGiK;gBAGb,OAAO0K,IAAIC,oBAAoBzQ;;YAGnC,OAAO;;QAIX,SAAS0Q;YACL,OAAO7P,OAAO8P,mBAAmB3Q;;QAKrC,SAAS4Q;YACL,IAAIL,6BAA6B;gBAC7B,OAAO;;YAGX,OAAOG;;QAGX,SAASG;YAEL,OAAOnL,SAASC,cAAc,SAASmL,oBAAoB9Q;;QAG/D,SAAS+Q;YACL;gBACI,SAASlQ,OAAOmQ,gBAEZnV,GAAG6G,WAAW7B,OAAOmQ,aAAaC;cAE1C,OAAO/K;gBAEH,OAAO;;;QAIf,SAASgL;YACL,IAAIC,OAAOzL,SAASC,cAAc;YAElC,QAAQ,eAAewL,QAAS,iBAAiBA,QAAQ,YAAYA,UAChEtV,GAAGwO,cAAcxO,GAAG0O;;QAG7ByE,oBAAoBe;QAEpBZ,4BAA4BH,qBAAqBnT,GAAG2J;QAEpDyJ,yBAAyBE,8BAA8BtT,GAAG2K;QAE1D0I,mBAAmBC,6BAA6B+B;QAEhD9B,qBAAqBF,oBAAoBkB;QAEzCf,mBAAmBF,6BAA6BtT,GAAG0K;QAEnD+I,iBAAiBH,6BAA6BE,oBAAoB0B;QAElExB,yBAAyBJ,6BAA6BmB;QAEtDd,qBAAqBR,sBAAsBnO,OAAOuQ,gBAAgBpR,aAAamP;QAE/EO,4BAA4Ba;QAE5Bd,wBAAwBiB;QAExBf,yBAAyBiB;QAEzBhB,0BAA0BiB;QAE1BhB,wBAAwBV,6BAA6BtO,OAAO6G,eAAe1H;QAE3E8P,yBAA0B;YACtB,IAAIX,2BAA2B;gBAC3B,QAAQtT,GAAG2K,mBAAmB3K,GAAG8O;;YAErC,OAAO;;QAGX;YACI0G,eAAelC;YACfmC,eAAerC;YACfsC,kBAAkBpC;YAClBqC,UAAUnC;YACVoC,gBAAgB9B;YAChB+B,mBAAmBjC;YACnBkC,mBAAmBjC;YACnBkC,iBAAiB/Q,OAAOgR;YACxBC,UAAU5C;YACV6C,YAAY3C;YACZ4C,iBAAiBpC;YACjBqC,eAAepC;YACfqC,iBAAiBrC;YACjBsC,oBAAoBhD;YACpBiD,OAAO/C;YACPgD,aAAavC;YACbwC,QAAQhD;YACRiD,SAAS1C,yBAAyBZ;YAClCuD,cAAc3W,GAAGiO;YACjB2I,2BAA2B5W,GAAG0O;YAC9BmI,WAAW1D;YACX2D,YAAYnD;YACZoD,qBAAqBzD;YACrB0D,oBAAoB1D;YACpB2D,gBAAgBvD;;;IChKxB1T,GAAGkX,mBAAmB,SAASC;QAC3B;QACA,UAAUA,gBAAgBA,aAAaC,QAAQpX,GAAG6G,WAAWsQ,aAAaC;;IAG9EpX,GAAG+L,UAAU;QACT;QAEA,IAAIsL,aAAaC,aACbC,uBACAC,uBACAC,oBACAC,QAAQ;QAEZ1X,GAAG6B,OAAOxB;YACN+W,MAAM,SAASO,WAAWC;gBACtB,IAAIF,UAAU,GAAG;oBACb,IAAIC,WAAW;wBACXJ,iBAAiBrU,KAAKyU;;oBAE1B,IAAIC,WAAW;wBACXJ,iBAAiBtU,KAAK0U;;uBAGzB,IAAIF,WAAW,GAAG;oBACnBE,aAAaA,UAAUvH,MAAM,MAAMiH;uBAElC,IAAIK,WAAW;oBAChBA,UAAUtH,MAAM,MAAMgH;;gBAG1B,OAAOhX;;YAGXwX,MAAM,SAASpI;gBACX,IAAIiI,UAAU,GAAG;oBACbD,cAAcvU,KAAKuM;uBAElB;oBACDA,SAASY,MAAM,MAAMiH,gBAAgBnT,YAAYkT,cAAcC;;gBAGnE,OAAOjX;;YAGX4L,SAAS;gBACLyL,QAAQ;gBACRL,cAAclP;gBAEd,IAAIoP,iBAAiBxR,QAAQ;oBACzB/F,GAAG+C,KAAKwU,kBAAkB,SAASvU,KAAKyM;wBACpCA,SAASY,MAAM,MAAMgH;;;gBAI7B,IAAII,cAAc1R,QAAQ;oBACtB/F,GAAG+C,KAAK0U,eAAe,SAASzU,KAAKyM;wBACjCA,SAASY,MAAM,MAAMgH;;;gBAI7B,OAAOhX;;YAGX8L,SAAS;gBACLuL,SAAS;gBACTJ,cAAcnP;gBAEd,IAAIqP,iBAAiBzR,QAAQ;oBACzB/F,GAAG+C,KAAKyU,kBAAkB,SAASxU,KAAKyM;wBACpCA,SAASY,MAAM,MAAMiH;;;gBAI7B,IAAIG,cAAc1R,QAAQ;oBACtB/F,GAAG+C,KAAK0U,eAAe,SAASzU,KAAKyM;wBACjCA,SAASY,MAAM,MAAMiH;;;gBAI7B,OAAOjX;;;;IClFnBL,GAAG8X,cAAc,SAASC;QACtB;QAEA,IAAIC,SACAC,wBAAwB,gBACxBC,yBAAyB,oBACzBC,sBACAC,mBACAC,iBAAiB,IAAIrY,GAAGsS;QAE5B0F;YACIM;YACAC,oBAAoB;YACpBC;gBACIC,YAAY;;YAEhBC,WAAW,IAAI1Y,GAAG8X,YAAYY;;QAGlC1Y,GAAG6B,OAAOmW,SAASD,GAAG;QAEtB,SAASY,mBAAmBC,OAAOC;YAE/B,IAAIC,eAAe7Q,MAAMvB,UAAUwB,MAAMtB,KAAKgS;YAE9CZ,QAAQU,UAAUK,QAAQ,aAAaH,MAAM7S,SAAS;YACtD8S,eAAeG,aAAa;YAC5BhB,QAAQU,UAAUO,+BAA+BH,cAAcD,eAAeK;;QAGlF,SAASC,iBAAiBC;YACtB,IAAIC,oBAAoB,IAAIrZ,GAAG+L;YAE/B,IAAIqN,MAAM3Q,QAAQ;gBACd2Q,MAAME,KAAK,SAASA;oBAChB,IAAIvX,OAAOqX,MAAMrX,MACbwX,WAAWH,MAAMG,UACjBC,wBAAwBD,SAAS5T,QAAQ5D;oBAG7CwX,WAAWA,SAASrH,OAAO,GAAGsH;oBAG9B,IAAID,SAASxJ,OAAO,OAAO,KAAK;wBAC5BwJ,WAAWA,SAASrH,OAAO;;oBAG/BoH,KAAKG,SAASF;oBACdnB,aAAalV,KAAKoW;oBAClBD,kBAAkBpN;mBAEtB,SAASyN;oBACL1B,QAAQU,UAAUK,QAAQ,sBAAsBK,MAAMG,WAAW,wBAAwBG,UAAUC,OAAO,KAAK;oBAC/GN,kBAAkBlN;;mBAGrB,IAAIiN,MAAMQ,aAAa;gBACxBC,oBAAoBT,OAAOhC,KACvB,SAAS0C,eAAeC;oBACpB,IAAIC,cAAcD,QAAQhU;oBAE1B/F,GAAG+C,KAAKgX,SAAS,SAAS/W,KAAKoW;wBAC3BD,iBAAiBC,OAAOvB,KAAK;4BACzBmC,eAAe;4BAEf,IAAIA,gBAAgB,GAAG;gCACnBX,kBAAkBpN;;;;oBAK9B,KAAK8N,QAAQhU,QAAQ;wBACjBsT,kBAAkBpN;;mBAI1B,SAASgO,YAAYP;oBACjB1B,QAAQU,UAAUK,QAAQ,sBAAsBK,MAAMG,WAAW,wBAAwBG,UAAUC,OAAO,KAAK;oBAC/GN,kBAAkBlN;;;YAK9B,OAAOkN;;QAIX,SAASQ,oBAAoBT,OAAOc,QAAQC,cAAcC;YACtD,IAAItO,UAAUsO,mBAAmB,IAAIpa,GAAG+L,WACpCsO,YAAYH,UAAUd,MAAMkB;YAEhCD,UAAUE,YACN,SAASC,YAAYT;gBACjB,IAAIU,aAAaN,eAAeA,aAAa/J,OAAO2J,WAAWA;gBAE/D,IAAIA,QAAQhU,QAAQ;oBAChB2U,WAAW;wBACPb,oBAAoBT,OAAOiB,WAAWI,YAAY3O;uBACnD;uBAEF;oBACDA,QAAQG,QAAQwO;;eAIxB3O,QAAQK;YAGZ,OAAOL;;QAGX,SAAS6O,mBAAmBpQ,cAAcsO;YACtC,IAAI+B,4BACAC,4BAA4B,IAAI7a,GAAG+L;YAEvCiM,QAAQU,UAAUoC;YAClBjC,eAAeG,aAAa;YAE5B,IAAIzO,aAAaqO,MAAM7S,SAAS,MAAMiS,QAAQO,oBAAoB;gBAC9DP,QAAQU,UAAUO;gBAClBjB,QAAQU,UAAUqC,UAAU,qBAAqB;gBACjDlC,eAAeG,aAAa;gBAC5B6B,0BAA0B1O;mBAEzB;gBACDiM;gBAEA,IAAIpY,GAAGsK,sBAAsBC,eAAe;oBACxCvK,GAAG+C,KAAKwH,aAAaC,OAAO,SAASxH,KAAKsE;wBACtC,IAAI8R,QAAQ9R,KAAKmD;wBAEjB,IAAI2O,OAAO;4BAEP,IAAIA,MAAM3Q,QAAQ;gCACd2P,aAAalV,KAAKoE,KAAK0T;mCAGtB;gCACDJ,sBAAsB1X,KAAKiW,iBAAiBC,OAAOvB,KAAK;oCACpD+C,sBAAsBK;oCACtB,IAAIL,sBAAsB7U,WAAW,GAAG;wCACpC8U,0BAA0B5O;;;;;;uBAO7C;oBACDmM,eAAe7N,aAAaqO;;gBAGhC,IAAIgC,sBAAsB7U,WAAW,GAAG;oBACpC8U,0BAA0B5O;;;YAIlC,OAAO4O;;QAGX,SAASK,cAAcC;YACnB,IAAIC,WAAW,IAAIpb,GAAGqb;gBAClBpD,uBAAuBA;gBACvBhY,SAASkb;gBACTG,SAAS,SAASpM;oBACdlP,GAAGmb,UAAU9Y,SAAS2V,QAAQQ,QAAQC;oBACtCvJ,EAAEqM;;gBAENC,uBAAuB,SAAStM;oBAC5BlP,GAAGmb,UAAU7Y,YAAY0V,QAAQQ,QAAQC;;gBAE7CgD,QAAQ,SAASvM;oBACbyL,mBAAmBzL,EAAE3E,cAAc6Q,UAAUhE,KACzC;wBACIuB,mBAAmBP,cAAcgD;uBAErC;wBACIpD,QAAQU,UAAUK,QAAQ,uEAAuE;;;;YAMjHV,eAAe1F,YAAY;gBACvByI,SAAS5I;;YAGbxS,GAAGmb,UAAUrX,aAAaoU,2BAA2BlY,GAAGmb,UAAUjb;YAElEiY,gBAAgBjV,KAAKkY;YAErB,OAAOA;;QAGX,SAASM,WAAWC;YAChB,IAAIC;YAEJ5b,GAAG+C,KAAK4Y,UAAUpR,aAAasR,OAAO,SAAShM,KAAK5M;gBAChD,IAAIA,QAAQ,SAAS;oBACjB2Y,WAAW;oBACX,OAAO;;;YAIf,OAAOA;;QAWX,SAASE,mBAAmB5M;YACxB,IAAIlP,GAAGqO,WAAW;gBACd,QAAQa,EAAE6M;;YAGd,IAAI/b,GAAGiO,UAAU;gBACb,OAAOiB,EAAE8M,IAAI,KAAK9M,EAAE+M,IAAI;;YAG5B,OAAO/M,EAAE8M,MAAM,KAAK9M,EAAE+M,MAAM;;QAGhC,SAASC;YACL,IAAIC,YAAYnE,QAAQM,kBAEpB8D,qBAAqB;gBACjB1B,WAAW;oBACP1a,GAAG+C,KAAKoZ,WAAW,SAASnZ,KAAKoY;wBAC7Bpb,GAAGob,UAAUtX,aAAaoU,2BAA2BlY,GAAGob,UAAUlb;wBAClEF,GAAGob,UAAU9Y,YAAY0V,QAAQQ,QAAQC;;mBAE9C;;YAGXzY,GAAG+C,KAAKoZ,WAAW,SAASnZ,KAAKoY;gBAC7B,IAAIvC,iBAAiBqC,cAAcE;gBAGnC,IAAIe,UAAUpW,UAAU/F,GAAGkT,kBAAkB+C,UAAU;oBACnDoC,eAAe/X,OAAOuJ,UAAU,aAAa,SAASqF;wBAClD,KAAK2J,eAAeG,kBAAkB0C,WAAWxM,IAAI;4BACjDlP,GAAG+C,KAAKoZ,WAAW,SAASnZ,KAAKoY;gCAG7B,IAAIA,oBAAoBiB,eACpBrc,GAAGob,UAAUtX,aAAaoU,yBAAyB;oCAEnDlY,GAAGob,UAAU9Z;wCAAKlB,SAAS;;;;;;;;YAQnDiY,eAAe/X,OAAOuJ,UAAU,aAAa,SAASqF;gBAClD,IAAI4M,mBAAmB5M,IAAI;oBACvBkN;;;YAOR/D,eAAe/X,OAAON,GAAG6J,UAAUzG,WAAW,IAAI,cAAc,SAAS8L;gBACrEkN;;YAGJ/D,eAAe/X,OAAOuJ,UAAU,QAAQ,SAASqF;gBAC7CA,EAAED;gBACFmN;;YAGJ/D,eAAe/X,OAAOuJ,UAAUoO,uBAAuBmE;;QAG3DF;QAEAlc,GAAG6B,OAAOxB;YACNic,oBAAoB,SAASrc;gBACzB+X,QAAQM,iBAAiBpV,KAAKjD;gBAC9Bib,cAAcjb;;YAGlBsc,gBAAgB,SAAStc;gBACrB,IAAI6Q,GACA0L,MAAMxE,QAAQM;gBAElB,KAAKxH,KAAK0L,KAAK;oBACX,IAAIA,IAAI1L,OAAO7Q,SAAS;wBACpB,OAAOuc,IAAIC,OAAO3L,GAAG;;;;YAKjC0B,SAAS;gBACL6F,eAAe7F;gBACfxS,GAAG+C,KAAKoV,iBAAiB,SAASnV,KAAKoY;oBACnCA,SAAS5I;;;;;IAMzBxS,GAAG8X,YAAYY,YAAY;QACvB;QAEA;YACIoC,wBAAwB;YACxB7B,gCAAgC,SAASL,OAAO8D;YAChD3B,WAAW,SAASpB,MAAMgD;gBACtB3c,GAAGmG,IAAI,6BAA6BwT,OAAO,6BAA6BgD,iBAAiB,KAAK;;YAElG5D,SAAS,SAAS3S,SAASC;gBACvBrG,GAAGmG,IAAIC,SAASC;;;;IAK5BrG,GAAGqb,iBAAiB,SAAStD;QACzB;QAEA,IAAIM,iBAAiB,IAAIrY,GAAGsS,kBACxB0F,SAAS/X,SAAS2c,aAAaC;QAEnC7E;YACI/X,SAAS;YACTqb,SAAS,SAASpM;YAClB4N,SAAS,SAAS5N;YAElBsM,uBAAuB,SAAStM;YAChCuM,QAAQ,SAASvM;;QAGrBlP,GAAG6B,OAAOmW,SAASD;QACnB9X,UAAU+X,QAAQ/X;QAElB,SAAS8c;YACL,OAAO/c,GAAGiO,YAAajO,GAAGqO,aAAarO,GAAGsO;;QAG9C,SAAS0O,mBAAmB9N;YAExB,KAAK2N,qBAAqB;gBAGtB,IAAIE,0BAA0B;oBAC1B1E,eAAe/X,OAAOuJ,UAAU,YAAY,SAASqF;wBACjDA,EAAED;;uBAEH;oBACHoJ,eAAe/X,OAAOuJ,UAAU,YAAY,SAASqF;wBACjD,IAAIA,EAAE3E,cAAc;4BAChB2E,EAAE3E,aAAa0S,aAAa;4BAC5B/N,EAAED;;;;gBAKd4N,sBAAsB;;;QAI9B,SAASK,gBAAgBhO;YAGrB,KAAKlP,GAAGkT,kBAAkB+C,UAAU;gBAChC,OAAO;;YAGX,IAAIkH,YAAYC,KAAKlO,EAAE3E,cAEvB8S,WAAWrd,GAAGiO;YAOdkP,aAAand,GAAGyN,QAAQzN,GAAGkT,kBAAkB+C,WAAW,OAAOmH,GAAGE,kBAAkB;YACpF,OAAOF,MAAMD,eAAeC,GAAGxE,UAAWyE,YAAYD,GAAGvB,MAAM/a,YAAYsc,GAAGvB,MAAM/a,SAAS;;QAGjG,SAASyc,oBAAoBC;YACzB,IAAIA,eAAerZ,WAAW;gBAC1ByY,cAAcY;;YAElB,OAAOZ;;QAGX,SAASa;YACL,IAAIC;YAEJ,SAASC;gBACLD,iBAAiB7T,SAAS+T,YAAY;gBACtCF,eAAeG,UAAU7F,QAAQC,uBAAuB,MAAM;;YAGlE,IAAIjT,OAAO8Y,aAAa;gBACpB;oBACIJ,iBAAiB,IAAII,YAAY9F,QAAQC;kBAE7C,OAAO8F;oBACHJ;;mBAGH;gBACDA;;YAGJ9T,SAASmU,cAAcN;;QAG3B,SAASO;YACL5F,eAAe/X,OAAOL,SAAS,YAAY,SAASiP;gBAChD,KAAKgO,gBAAgBhO,IAAI;oBACrB;;gBAKJ,IAAIgP,SAASle,GAAGyN,QAAQzN,GAAGkT,kBAAkB+C,WAAW,OAAO/G,EAAE3E,aAAa+S;gBAC9E,IAAIY,WAAW,UAAUA,WAAW,YAAY;oBAC5ChP,EAAE3E,aAAa0S,aAAa;uBACzB;oBACH/N,EAAE3E,aAAa0S,aAAa;;gBAGhC/N,EAAEqM;gBACFrM,EAAED;;YAGNoJ,eAAe/X,OAAOL,SAAS,aAAa,SAASiP;gBACjD,KAAKqO,uBAAuB;oBACxB,KAAKL,gBAAgBhO,IAAI;wBACrB;;oBAEJ8I,QAAQsD,QAAQpM;;;YAIxBmJ,eAAe/X,OAAOL,SAAS,aAAa,SAASiP;gBACjD,KAAKgO,gBAAgBhO,IAAI;oBACrB;;gBAGJ8I,QAAQ8E,QAAQ5N;gBAEhB,IAAI6M,gBAAgBlS,SAASsU,iBAAiBjP,EAAEkP,SAASlP,EAAEmP;gBAE3D,IAAIre,GAAGK,MAAMS,SAASib,gBAAgB;oBAClC;;gBAGJ/D,QAAQwD,sBAAsBtM;;YAGlCmJ,eAAe/X,OAAOL,SAAS,QAAQ,SAASiP;gBAC5C,KAAKqO,uBAAuB;oBACxB,KAAKL,gBAAgBhO,IAAI;wBACrB;;oBAGJA,EAAED;oBACFC,EAAEqM;oBACFvD,QAAQyD,OAAOvM;oBAEfuO;;;;QAKZT;QACAiB;QAEAje,GAAG6B,OAAOxB;YACN2Y,cAAc,SAASwE;gBACnB,OAAOD,oBAAoBC;;YAG/BhL,SAAS;gBACL6F,eAAe7F;;YAGnB0G,YAAY;gBACR,OAAOjZ;;;;GN3eY+E", "file": "dnd.js", "sourcesContent": [null, "/*globals window, navigator, document, FormData, File, HTMLInputElement, XMLHttpRequest, Blob, Storage, ActiveXObject */\n/* jshint -W079 */\nvar qq = function(element) {\n    \"use strict\";\n\n    return {\n        hide: function() {\n            element.style.display = \"none\";\n            return this;\n        },\n\n        /** Returns the function which detaches attached event */\n        attach: function(type, fn) {\n            if (element.addEventListener) {\n                element.addEventListener(type, fn, false);\n            } else if (element.attachEvent) {\n                element.attachEvent(\"on\" + type, fn);\n            }\n            return function() {\n                qq(element).detach(type, fn);\n            };\n        },\n\n        detach: function(type, fn) {\n            if (element.removeEventListener) {\n                element.removeEventListener(type, fn, false);\n            } else if (element.attachEvent) {\n                element.detachEvent(\"on\" + type, fn);\n            }\n            return this;\n        },\n\n        contains: function(descendant) {\n            // The [W3C spec](http://www.w3.org/TR/domcore/#dom-node-contains)\n            // says a `null` (or ostensibly `undefined`) parameter\n            // passed into `Node.contains` should result in a false return value.\n            // IE7 throws an exception if the parameter is `undefined` though.\n            if (!descendant) {\n                return false;\n            }\n\n            // compareposition returns false in this case\n            if (element === descendant) {\n                return true;\n            }\n\n            if (element.contains) {\n                return element.contains(descendant);\n            } else {\n                /*jslint bitwise: true*/\n                return !!(descendant.compareDocumentPosition(element) & 8);\n            }\n        },\n\n        /**\n         * Insert this element before elementB.\n         */\n        insertBefore: function(elementB) {\n            elementB.parentNode.insertBefore(element, elementB);\n            return this;\n        },\n\n        remove: function() {\n            element.parentNode.removeChild(element);\n            return this;\n        },\n\n        /**\n         * Sets styles for an element.\n         * Fixes opacity in IE6-8.\n         */\n        css: function(styles) {\n            /*jshint eqnull: true*/\n            if (element.style == null) {\n                throw new qq.Error(\"Can't apply style to node as it is not on the HTMLElement prototype chain!\");\n            }\n\n            /*jshint -W116*/\n            if (styles.opacity != null) {\n                if (typeof element.style.opacity !== \"string\" && typeof (element.filters) !== \"undefined\") {\n                    styles.filter = \"alpha(opacity=\" + Math.round(100 * styles.opacity) + \")\";\n                }\n            }\n            qq.extend(element.style, styles);\n\n            return this;\n        },\n\n        hasClass: function(name, considerParent) {\n            var re = new RegExp(\"(^| )\" + name + \"( |$)\");\n            return re.test(element.className) || !!(considerParent && re.test(element.parentNode.className));\n        },\n\n        addClass: function(name) {\n            if (!qq(element).hasClass(name)) {\n                element.className += \" \" + name;\n            }\n            return this;\n        },\n\n        removeClass: function(name) {\n            var re = new RegExp(\"(^| )\" + name + \"( |$)\");\n            element.className = element.className.replace(re, \" \").replace(/^\\s+|\\s+$/g, \"\");\n            return this;\n        },\n\n        getByClass: function(className, first) {\n            var candidates,\n                result = [];\n\n            if (first && element.querySelector) {\n                return element.querySelector(\".\" + className);\n            }\n            else if (element.querySelectorAll) {\n                return element.querySelectorAll(\".\" + className);\n            }\n\n            candidates = element.getElementsByTagName(\"*\");\n\n            qq.each(candidates, function(idx, val) {\n                if (qq(val).hasClass(className)) {\n                    result.push(val);\n                }\n            });\n            return first ? result[0] : result;\n        },\n\n        getFirstByClass: function(className) {\n            return qq(element).getByClass(className, true);\n        },\n\n        children: function() {\n            var children = [],\n                child = element.firstChild;\n\n            while (child) {\n                if (child.nodeType === 1) {\n                    children.push(child);\n                }\n                child = child.nextSibling;\n            }\n\n            return children;\n        },\n\n        setText: function(text) {\n            element.innerText = text;\n            element.textContent = text;\n            return this;\n        },\n\n        clearText: function() {\n            return qq(element).setText(\"\");\n        },\n\n        // Returns true if the attribute exists on the element\n        // AND the value of the attribute is NOT \"false\" (case-insensitive)\n        hasAttribute: function(attrName) {\n            var attrVal;\n\n            if (element.hasAttribute) {\n\n                if (!element.hasAttribute(attrName)) {\n                    return false;\n                }\n\n                /*jshint -W116*/\n                return (/^false$/i).exec(element.getAttribute(attrName)) == null;\n            }\n            else {\n                attrVal = element[attrName];\n\n                if (attrVal === undefined) {\n                    return false;\n                }\n\n                /*jshint -W116*/\n                return (/^false$/i).exec(attrVal) == null;\n            }\n        }\n    };\n};\n\n(function() {\n    \"use strict\";\n\n    qq.canvasToBlob = function(canvas, mime, quality) {\n        return qq.dataUriToBlob(canvas.toDataURL(mime, quality));\n    };\n\n    qq.dataUriToBlob = function(dataUri) {\n        var arrayBuffer, byteString,\n            createBlob = function(data, mime) {\n                var BlobBuilder = window.BlobBuilder ||\n                        window.WebKitBlobBuilder ||\n                        window.MozBlobBuilder ||\n                        window.MSBlobBuilder,\n                    blobBuilder = BlobBuilder && new BlobBuilder();\n\n                if (blobBuilder) {\n                    blobBuilder.append(data);\n                    return blobBuilder.getBlob(mime);\n                }\n                else {\n                    return new Blob([data], {type: mime});\n                }\n            },\n            intArray, mimeString;\n\n        // convert base64 to raw binary data held in a string\n        if (dataUri.split(\",\")[0].indexOf(\"base64\") >= 0) {\n            byteString = atob(dataUri.split(\",\")[1]);\n        }\n        else {\n            byteString = decodeURI(dataUri.split(\",\")[1]);\n        }\n\n        // extract the MIME\n        mimeString = dataUri.split(\",\")[0]\n            .split(\":\")[1]\n            .split(\";\")[0];\n\n        // write the bytes of the binary string to an ArrayBuffer\n        arrayBuffer = new ArrayBuffer(byteString.length);\n        intArray = new Uint8Array(arrayBuffer);\n        qq.each(byteString, function(idx, character) {\n            intArray[idx] = character.charCodeAt(0);\n        });\n\n        return createBlob(arrayBuffer, mimeString);\n    };\n\n    qq.log = function(message, level) {\n        if (window.console) {\n            if (!level || level === \"info\") {\n                window.console.log(message);\n            }\n            else\n            {\n                if (window.console[level]) {\n                    window.console[level](message);\n                }\n                else {\n                    window.console.log(\"<\" + level + \"> \" + message);\n                }\n            }\n        }\n    };\n\n    qq.isObject = function(variable) {\n        return variable && !variable.nodeType && Object.prototype.toString.call(variable) === \"[object Object]\";\n    };\n\n    qq.isFunction = function(variable) {\n        return typeof (variable) === \"function\";\n    };\n\n    /**\n     * Check the type of a value.  Is it an \"array\"?\n     *\n     * @param value value to test.\n     * @returns true if the value is an array or associated with an `ArrayBuffer`\n     */\n    qq.isArray = function(value) {\n        return Object.prototype.toString.call(value) === \"[object Array]\" ||\n            (value && window.ArrayBuffer && value.buffer && value.buffer.constructor === ArrayBuffer);\n    };\n\n    // Looks for an object on a `DataTransfer` object that is associated with drop events when utilizing the Filesystem API.\n    qq.isItemList = function(maybeItemList) {\n        return Object.prototype.toString.call(maybeItemList) === \"[object DataTransferItemList]\";\n    };\n\n    // Looks for an object on a `NodeList` or an `HTMLCollection`|`HTMLFormElement`|`HTMLSelectElement`\n    // object that is associated with collections of Nodes.\n    qq.isNodeList = function(maybeNodeList) {\n        return Object.prototype.toString.call(maybeNodeList) === \"[object NodeList]\" ||\n            // If `HTMLCollection` is the actual type of the object, we must determine this\n            // by checking for expected properties/methods on the object\n            (maybeNodeList.item && maybeNodeList.namedItem);\n    };\n\n    qq.isString = function(maybeString) {\n        return Object.prototype.toString.call(maybeString) === \"[object String]\";\n    };\n\n    qq.trimStr = function(string) {\n        if (String.prototype.trim) {\n            return string.trim();\n        }\n\n        return string.replace(/^\\s+|\\s+$/g, \"\");\n    };\n\n    /**\n     * @param str String to format.\n     * @returns {string} A string, swapping argument values with the associated occurrence of {} in the passed string.\n     */\n    qq.format = function(str) {\n\n        var args =  Array.prototype.slice.call(arguments, 1),\n            newStr = str,\n            nextIdxToReplace = newStr.indexOf(\"{}\");\n\n        qq.each(args, function(idx, val) {\n            var strBefore = newStr.substring(0, nextIdxToReplace),\n                strAfter = newStr.substring(nextIdxToReplace + 2);\n\n            newStr = strBefore + val + strAfter;\n            nextIdxToReplace = newStr.indexOf(\"{}\", nextIdxToReplace + val.length);\n\n            // End the loop if we have run out of tokens (when the arguments exceed the # of tokens)\n            if (nextIdxToReplace < 0) {\n                return false;\n            }\n        });\n\n        return newStr;\n    };\n\n    qq.isFile = function(maybeFile) {\n        return window.File && Object.prototype.toString.call(maybeFile) === \"[object File]\";\n    };\n\n    qq.isFileList = function(maybeFileList) {\n        return window.FileList && Object.prototype.toString.call(maybeFileList) === \"[object FileList]\";\n    };\n\n    qq.isFileOrInput = function(maybeFileOrInput) {\n        return qq.isFile(maybeFileOrInput) || qq.isInput(maybeFileOrInput);\n    };\n\n    qq.isInput = function(maybeInput, notFile) {\n        var evaluateType = function(type) {\n            var normalizedType = type.toLowerCase();\n\n            if (notFile) {\n                return normalizedType !== \"file\";\n            }\n\n            return normalizedType === \"file\";\n        };\n\n        if (window.HTMLInputElement) {\n            if (Object.prototype.toString.call(maybeInput) === \"[object HTMLInputElement]\") {\n                if (maybeInput.type && evaluateType(maybeInput.type)) {\n                    return true;\n                }\n            }\n        }\n        if (maybeInput.tagName) {\n            if (maybeInput.tagName.toLowerCase() === \"input\") {\n                if (maybeInput.type && evaluateType(maybeInput.type)) {\n                    return true;\n                }\n            }\n        }\n\n        return false;\n    };\n\n    qq.isBlob = function(maybeBlob) {\n        if (window.Blob && Object.prototype.toString.call(maybeBlob) === \"[object Blob]\") {\n            return true;\n        }\n    };\n\n    qq.isXhrUploadSupported = function() {\n        var input = document.createElement(\"input\");\n        input.type = \"file\";\n\n        return (\n            input.multiple !== undefined &&\n                typeof File !== \"undefined\" &&\n                typeof FormData !== \"undefined\" &&\n                typeof (qq.createXhrInstance()).upload !== \"undefined\");\n    };\n\n    // Fall back to ActiveX is native XHR is disabled (possible in any version of IE).\n    qq.createXhrInstance = function() {\n        if (window.XMLHttpRequest) {\n            return new XMLHttpRequest();\n        }\n\n        try {\n            return new ActiveXObject(\"MSXML2.XMLHTTP.3.0\");\n        }\n        catch (error) {\n            qq.log(\"Neither XHR or ActiveX are supported!\", \"error\");\n            return null;\n        }\n    };\n\n    qq.isFolderDropSupported = function(dataTransfer) {\n        return dataTransfer.items &&\n            dataTransfer.items.length > 0 &&\n            dataTransfer.items[0].webkitGetAsEntry;\n    };\n\n    qq.isFileChunkingSupported = function() {\n        return !qq.androidStock() && //Android's stock browser cannot upload Blobs correctly\n            qq.isXhrUploadSupported() &&\n            (File.prototype.slice !== undefined || File.prototype.webkitSlice !== undefined || File.prototype.mozSlice !== undefined);\n    };\n\n    qq.sliceBlob = function(fileOrBlob, start, end) {\n        var slicer = fileOrBlob.slice || fileOrBlob.mozSlice || fileOrBlob.webkitSlice;\n\n        return slicer.call(fileOrBlob, start, end);\n    };\n\n    qq.arrayBufferToHex = function(buffer) {\n        var bytesAsHex = \"\",\n            bytes = new Uint8Array(buffer);\n\n        qq.each(bytes, function(idx, byt) {\n            var byteAsHexStr = byt.toString(16);\n\n            if (byteAsHexStr.length < 2) {\n                byteAsHexStr = \"0\" + byteAsHexStr;\n            }\n\n            bytesAsHex += byteAsHexStr;\n        });\n\n        return bytesAsHex;\n    };\n\n    qq.readBlobToHex = function(blob, startOffset, length) {\n        var initialBlob = qq.sliceBlob(blob, startOffset, startOffset + length),\n            fileReader = new FileReader(),\n            promise = new qq.Promise();\n\n        fileReader.onload = function() {\n            promise.success(qq.arrayBufferToHex(fileReader.result));\n        };\n\n        fileReader.onerror = promise.failure;\n\n        fileReader.readAsArrayBuffer(initialBlob);\n\n        return promise;\n    };\n\n    qq.extend = function(first, second, extendNested) {\n        qq.each(second, function(prop, val) {\n            if (extendNested && qq.isObject(val)) {\n                if (first[prop] === undefined) {\n                    first[prop] = {};\n                }\n                qq.extend(first[prop], val, true);\n            }\n            else {\n                first[prop] = val;\n            }\n        });\n\n        return first;\n    };\n\n    /**\n     * Allow properties in one object to override properties in another,\n     * keeping track of the original values from the target object.\n     *\n     * Note that the pre-overriden properties to be overriden by the source will be passed into the `sourceFn` when it is invoked.\n     *\n     * @param target Update properties in this object from some source\n     * @param sourceFn A function that, when invoked, will return properties that will replace properties with the same name in the target.\n     * @returns {object} The target object\n     */\n    qq.override = function(target, sourceFn) {\n        var super_ = {},\n            source = sourceFn(super_);\n\n        qq.each(source, function(srcPropName, srcPropVal) {\n            if (target[srcPropName] !== undefined) {\n                super_[srcPropName] = target[srcPropName];\n            }\n\n            target[srcPropName] = srcPropVal;\n        });\n\n        return target;\n    };\n\n    /**\n     * Searches for a given element (elt) in the array, returns -1 if it is not present.\n     */\n    qq.indexOf = function(arr, elt, from) {\n        if (arr.indexOf) {\n            return arr.indexOf(elt, from);\n        }\n\n        from = from || 0;\n        var len = arr.length;\n\n        if (from < 0) {\n            from += len;\n        }\n\n        for (; from < len; from += 1) {\n            if (arr.hasOwnProperty(from) && arr[from] === elt) {\n                return from;\n            }\n        }\n        return -1;\n    };\n\n    //this is a version 4 UUID\n    qq.getUniqueId = function() {\n        return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, function(c) {\n            /*jslint eqeq: true, bitwise: true*/\n            var r = Math.random() * 16 | 0, v = c == \"x\" ? r : (r & 0x3 | 0x8);\n            return v.toString(16);\n        });\n    };\n\n    //\n    // Browsers and platforms detection\n    qq.ie = function() {\n        return navigator.userAgent.indexOf(\"MSIE\") !== -1 ||\n            navigator.userAgent.indexOf(\"Trident\") !== -1;\n    };\n\n    qq.ie7 = function() {\n        return navigator.userAgent.indexOf(\"MSIE 7\") !== -1;\n    };\n\n    qq.ie8 = function() {\n        return navigator.userAgent.indexOf(\"MSIE 8\") !== -1;\n    };\n\n    qq.ie10 = function() {\n        return navigator.userAgent.indexOf(\"MSIE 10\") !== -1;\n    };\n\n    qq.ie11 = function() {\n        return qq.ie() && navigator.userAgent.indexOf(\"rv:11\") !== -1;\n    };\n\n    qq.edge = function() {\n        return navigator.userAgent.indexOf(\"Edge\") >= 0;\n    };\n\n    qq.safari = function() {\n        return navigator.vendor !== undefined && navigator.vendor.indexOf(\"Apple\") !== -1;\n    };\n\n    qq.chrome = function() {\n        return navigator.vendor !== undefined && navigator.vendor.indexOf(\"Google\") !== -1;\n    };\n\n    qq.opera = function() {\n        return navigator.vendor !== undefined && navigator.vendor.indexOf(\"Opera\") !== -1;\n    };\n\n    qq.firefox = function() {\n        return (!qq.edge() && !qq.ie11() && navigator.userAgent.indexOf(\"Mozilla\") !== -1 && navigator.vendor !== undefined && navigator.vendor === \"\");\n    };\n\n    qq.windows = function() {\n        return navigator.platform === \"Win32\";\n    };\n\n    qq.android = function() {\n        return navigator.userAgent.toLowerCase().indexOf(\"android\") !== -1;\n    };\n\n    // We need to identify the Android stock browser via the UA string to work around various bugs in this browser,\n    // such as the one that prevents a `Blob` from being uploaded.\n    qq.androidStock = function() {\n        return qq.android() && navigator.userAgent.toLowerCase().indexOf(\"chrome\") < 0;\n    };\n\n    qq.ios6 = function() {\n        return qq.ios() && navigator.userAgent.indexOf(\" OS 6_\") !== -1;\n    };\n\n    qq.ios7 = function() {\n        return qq.ios() && navigator.userAgent.indexOf(\" OS 7_\") !== -1;\n    };\n\n    qq.ios8 = function() {\n        return qq.ios() && navigator.userAgent.indexOf(\" OS 8_\") !== -1;\n    };\n\n    // iOS 8.0.0\n    qq.ios800 = function() {\n        return qq.ios() && navigator.userAgent.indexOf(\" OS 8_0 \") !== -1;\n    };\n\n    qq.ios = function() {\n        /*jshint -W014 */\n        return navigator.userAgent.indexOf(\"iPad\") !== -1\n            || navigator.userAgent.indexOf(\"iPod\") !== -1\n            || navigator.userAgent.indexOf(\"iPhone\") !== -1;\n    };\n\n    qq.iosChrome = function() {\n        return qq.ios() && navigator.userAgent.indexOf(\"CriOS\") !== -1;\n    };\n\n    qq.iosSafari = function() {\n        return qq.ios() && !qq.iosChrome() && navigator.userAgent.indexOf(\"Safari\") !== -1;\n    };\n\n    qq.iosSafariWebView = function() {\n        return qq.ios() && !qq.iosChrome() && !qq.iosSafari();\n    };\n\n    //\n    // Events\n\n    qq.preventDefault = function(e) {\n        if (e.preventDefault) {\n            e.preventDefault();\n        } else {\n            e.returnValue = false;\n        }\n    };\n\n    /**\n     * Creates and returns element from html string\n     * Uses innerHTML to create an element\n     */\n    qq.toElement = (function() {\n        var div = document.createElement(\"div\");\n        return function(html) {\n            div.innerHTML = html;\n            var element = div.firstChild;\n            div.removeChild(element);\n            return element;\n        };\n    }());\n\n    //key and value are passed to callback for each entry in the iterable item\n    qq.each = function(iterableItem, callback) {\n        var keyOrIndex, retVal;\n\n        if (iterableItem) {\n            // Iterate through [`Storage`](http://www.w3.org/TR/webstorage/#the-storage-interface) items\n            if (window.Storage && iterableItem.constructor === window.Storage) {\n                for (keyOrIndex = 0; keyOrIndex < iterableItem.length; keyOrIndex++) {\n                    retVal = callback(iterableItem.key(keyOrIndex), iterableItem.getItem(iterableItem.key(keyOrIndex)));\n                    if (retVal === false) {\n                        break;\n                    }\n                }\n            }\n            // `DataTransferItemList` & `NodeList` objects are array-like and should be treated as arrays\n            // when iterating over items inside the object.\n            else if (qq.isArray(iterableItem) || qq.isItemList(iterableItem) || qq.isNodeList(iterableItem)) {\n                for (keyOrIndex = 0; keyOrIndex < iterableItem.length; keyOrIndex++) {\n                    retVal = callback(keyOrIndex, iterableItem[keyOrIndex]);\n                    if (retVal === false) {\n                        break;\n                    }\n                }\n            }\n            else if (qq.isString(iterableItem)) {\n                for (keyOrIndex = 0; keyOrIndex < iterableItem.length; keyOrIndex++) {\n                    retVal = callback(keyOrIndex, iterableItem.charAt(keyOrIndex));\n                    if (retVal === false) {\n                        break;\n                    }\n                }\n            }\n            else {\n                for (keyOrIndex in iterableItem) {\n                    if (Object.prototype.hasOwnProperty.call(iterableItem, keyOrIndex)) {\n                        retVal = callback(keyOrIndex, iterableItem[keyOrIndex]);\n                        if (retVal === false) {\n                            break;\n                        }\n                    }\n                }\n            }\n        }\n    };\n\n    //include any args that should be passed to the new function after the context arg\n    qq.bind = function(oldFunc, context) {\n        if (qq.isFunction(oldFunc)) {\n            var args =  Array.prototype.slice.call(arguments, 2);\n\n            return function() {\n                var newArgs = qq.extend([], args);\n                if (arguments.length) {\n                    newArgs = newArgs.concat(Array.prototype.slice.call(arguments));\n                }\n                return oldFunc.apply(context, newArgs);\n            };\n        }\n\n        throw new Error(\"first parameter must be a function!\");\n    };\n\n    /**\n     * obj2url() takes a json-object as argument and generates\n     * a querystring. pretty much like jQuery.param()\n     *\n     * how to use:\n     *\n     *    `qq.obj2url({a:'b',c:'d'},'http://any.url/upload?otherParam=value');`\n     *\n     * will result in:\n     *\n     *    `http://any.url/upload?otherParam=value&a=b&c=d`\n     *\n     * @param  Object JSON-Object\n     * @param  String current querystring-part\n     * @return String encoded querystring\n     */\n    qq.obj2url = function(obj, temp, prefixDone) {\n        /*jshint laxbreak: true*/\n        var uristrings = [],\n            prefix = \"&\",\n            add = function(nextObj, i) {\n                var nextTemp = temp\n                    ? (/\\[\\]$/.test(temp)) // prevent double-encoding\n                    ? temp\n                    : temp + \"[\" + i + \"]\"\n                    : i;\n                if ((nextTemp !== \"undefined\") && (i !== \"undefined\")) {\n                    uristrings.push(\n                        (typeof nextObj === \"object\")\n                            ? qq.obj2url(nextObj, nextTemp, true)\n                            : (Object.prototype.toString.call(nextObj) === \"[object Function]\")\n                            ? encodeURIComponent(nextTemp) + \"=\" + encodeURIComponent(nextObj())\n                            : encodeURIComponent(nextTemp) + \"=\" + encodeURIComponent(nextObj)\n                    );\n                }\n            };\n\n        if (!prefixDone && temp) {\n            prefix = (/\\?/.test(temp)) ? (/\\?$/.test(temp)) ? \"\" : \"&\" : \"?\";\n            uristrings.push(temp);\n            uristrings.push(qq.obj2url(obj));\n        } else if ((Object.prototype.toString.call(obj) === \"[object Array]\") && (typeof obj !== \"undefined\")) {\n            qq.each(obj, function(idx, val) {\n                add(val, idx);\n            });\n        } else if ((typeof obj !== \"undefined\") && (obj !== null) && (typeof obj === \"object\")) {\n            qq.each(obj, function(prop, val) {\n                add(val, prop);\n            });\n        } else {\n            uristrings.push(encodeURIComponent(temp) + \"=\" + encodeURIComponent(obj));\n        }\n\n        if (temp) {\n            return uristrings.join(prefix);\n        } else {\n            return uristrings.join(prefix)\n                .replace(/^&/, \"\")\n                .replace(/%20/g, \"+\");\n        }\n    };\n\n    qq.obj2FormData = function(obj, formData, arrayKeyName) {\n        if (!formData) {\n            formData = new FormData();\n        }\n\n        qq.each(obj, function(key, val) {\n            key = arrayKeyName ? arrayKeyName + \"[\" + key + \"]\" : key;\n\n            if (qq.isObject(val)) {\n                qq.obj2FormData(val, formData, key);\n            }\n            else if (qq.isFunction(val)) {\n                formData.append(key, val());\n            }\n            else {\n                formData.append(key, val);\n            }\n        });\n\n        return formData;\n    };\n\n    qq.obj2Inputs = function(obj, form) {\n        var input;\n\n        if (!form) {\n            form = document.createElement(\"form\");\n        }\n\n        qq.obj2FormData(obj, {\n            append: function(key, val) {\n                input = document.createElement(\"input\");\n                input.setAttribute(\"name\", key);\n                input.setAttribute(\"value\", val);\n                form.appendChild(input);\n            }\n        });\n\n        return form;\n    };\n\n    /**\n     * Not recommended for use outside of Fine Uploader since this falls back to an unchecked eval if JSON.parse is not\n     * implemented.  For a more secure JSON.parse polyfill, use Douglas Crockford's json2.js.\n     */\n    qq.parseJson = function(json) {\n        /*jshint evil: true*/\n        if (window.JSON && qq.isFunction(JSON.parse)) {\n            return JSON.parse(json);\n        } else {\n            return eval(\"(\" + json + \")\");\n        }\n    };\n\n    /**\n     * Retrieve the extension of a file, if it exists.\n     *\n     * @param filename\n     * @returns {string || undefined}\n     */\n    qq.getExtension = function(filename) {\n        var extIdx = filename.lastIndexOf(\".\") + 1;\n\n        if (extIdx > 0) {\n            return filename.substr(extIdx, filename.length - extIdx);\n        }\n    };\n\n    qq.getFilename = function(blobOrFileInput) {\n        /*jslint regexp: true*/\n\n        if (qq.isInput(blobOrFileInput)) {\n            // get input value and remove path to normalize\n            return blobOrFileInput.value.replace(/.*(\\/|\\\\)/, \"\");\n        }\n        else if (qq.isFile(blobOrFileInput)) {\n            if (blobOrFileInput.fileName !== null && blobOrFileInput.fileName !== undefined) {\n                return blobOrFileInput.fileName;\n            }\n        }\n\n        return blobOrFileInput.name;\n    };\n\n    /**\n     * A generic module which supports object disposing in dispose() method.\n     * */\n    qq.DisposeSupport = function() {\n        var disposers = [];\n\n        return {\n            /** Run all registered disposers */\n            dispose: function() {\n                var disposer;\n                do {\n                    disposer = disposers.shift();\n                    if (disposer) {\n                        disposer();\n                    }\n                }\n                while (disposer);\n            },\n\n            /** Attach event handler and register de-attacher as a disposer */\n            attach: function() {\n                var args = arguments;\n                /*jslint undef:true*/\n                this.addDisposer(qq(args[0]).attach.apply(this, Array.prototype.slice.call(arguments, 1)));\n            },\n\n            /** Add disposer to the collection */\n            addDisposer: function(disposeFunction) {\n                disposers.push(disposeFunction);\n            }\n        };\n    };\n}());\n", "/* globals define, module, global, qq */\n(function() {\n    \"use strict\";\n    if (typeof define === \"function\" && define.amd) {\n        define(function() {\n            return qq;\n        });\n    }\n    else if (typeof module !== \"undefined\" && module.exports) {\n        module.exports = qq;\n    }\n    else {\n        global.qq = qq;\n    }\n}());\n", "/*global qq */\nqq.version = \"5.14.2\";\n", "/* globals qq */\nqq.supportedFeatures = (function() {\n    \"use strict\";\n\n    var supportsUploading,\n        supportsUploadingBlobs,\n        supportsFileDrop,\n        supportsAjaxFileUploading,\n        supportsFolderDrop,\n        supportsChunking,\n        supportsResume,\n        supportsUploadViaPaste,\n        supportsUploadCors,\n        supportsDeleteFileXdr,\n        supportsDeleteFileCorsXhr,\n        supportsDeleteFileCors,\n        supportsFolderSelection,\n        supportsImagePreviews,\n        supportsUploadProgress;\n\n    function testSupportsFileInputElement() {\n        var supported = true,\n            tempInput;\n\n        try {\n            tempInput = document.createElement(\"input\");\n            tempInput.type = \"file\";\n            qq(tempInput).hide();\n\n            if (tempInput.disabled) {\n                supported = false;\n            }\n        }\n        catch (ex) {\n            supported = false;\n        }\n\n        return supported;\n    }\n\n    //only way to test for Filesystem API support since webkit does not expose the DataTransfer interface\n    function isChrome21OrHigher() {\n        return (qq.chrome() || qq.opera()) &&\n            navigator.userAgent.match(/Chrome\\/[2][1-9]|Chrome\\/[3-9][0-9]/) !== undefined;\n    }\n\n    //only way to test for complete Clipboard API support at this time\n    function isChrome14OrHigher() {\n        return (qq.chrome() || qq.opera()) &&\n            navigator.userAgent.match(/Chrome\\/[1][4-9]|Chrome\\/[2-9][0-9]/) !== undefined;\n    }\n\n    //Ensure we can send cross-origin `XMLHttpRequest`s\n    function isCrossOriginXhrSupported() {\n        if (window.XMLHttpRequest) {\n            var xhr = qq.createXhrInstance();\n\n            //Commonly accepted test for XHR CORS support.\n            return xhr.withCredentials !== undefined;\n        }\n\n        return false;\n    }\n\n    //Test for (terrible) cross-origin ajax transport fallback for IE9 and IE8\n    function isXdrSupported() {\n        return window.XDomainRequest !== undefined;\n    }\n\n    // CORS Ajax requests are supported if it is either possible to send credentialed `XMLHttpRequest`s,\n    // or if `XDomainRequest` is an available alternative.\n    function isCrossOriginAjaxSupported() {\n        if (isCrossOriginXhrSupported()) {\n            return true;\n        }\n\n        return isXdrSupported();\n    }\n\n    function isFolderSelectionSupported() {\n        // We know that folder selection is only supported in Chrome via this proprietary attribute for now\n        return document.createElement(\"input\").webkitdirectory !== undefined;\n    }\n\n    function isLocalStorageSupported() {\n        try {\n            return !!window.localStorage &&\n                // unpatched versions of IE10/11 have buggy impls of localStorage where setItem is a string\n                qq.isFunction(window.localStorage.setItem);\n        }\n        catch (error) {\n            // probably caught a security exception, so no localStorage for you\n            return false;\n        }\n    }\n\n    function isDragAndDropSupported() {\n        var span = document.createElement(\"span\");\n\n        return (\"draggable\" in span || (\"ondragstart\" in span && \"ondrop\" in span)) &&\n            !qq.android() && !qq.ios();\n    }\n\n    supportsUploading = testSupportsFileInputElement();\n\n    supportsAjaxFileUploading = supportsUploading && qq.isXhrUploadSupported();\n\n    supportsUploadingBlobs = supportsAjaxFileUploading && !qq.androidStock();\n\n    supportsFileDrop = supportsAjaxFileUploading && isDragAndDropSupported();\n\n    supportsFolderDrop = supportsFileDrop && isChrome21OrHigher();\n\n    supportsChunking = supportsAjaxFileUploading && qq.isFileChunkingSupported();\n\n    supportsResume = supportsAjaxFileUploading && supportsChunking && isLocalStorageSupported();\n\n    supportsUploadViaPaste = supportsAjaxFileUploading && isChrome14OrHigher();\n\n    supportsUploadCors = supportsUploading && (window.postMessage !== undefined || supportsAjaxFileUploading);\n\n    supportsDeleteFileCorsXhr = isCrossOriginXhrSupported();\n\n    supportsDeleteFileXdr = isXdrSupported();\n\n    supportsDeleteFileCors = isCrossOriginAjaxSupported();\n\n    supportsFolderSelection = isFolderSelectionSupported();\n\n    supportsImagePreviews = supportsAjaxFileUploading && window.FileReader !== undefined;\n\n    supportsUploadProgress = (function() {\n        if (supportsAjaxFileUploading) {\n            return !qq.androidStock() && !qq.iosChrome();\n        }\n        return false;\n    }());\n\n    return {\n        ajaxUploading: supportsAjaxFileUploading,\n        blobUploading: supportsUploadingBlobs,\n        canDetermineSize: supportsAjaxFileUploading,\n        chunking: supportsChunking,\n        deleteFileCors: supportsDeleteFileCors,\n        deleteFileCorsXdr: supportsDeleteFileXdr, //NOTE: will also return true in IE10, where XDR is also supported\n        deleteFileCorsXhr: supportsDeleteFileCorsXhr,\n        dialogElement: !!window.HTMLDialogElement,\n        fileDrop: supportsFileDrop,\n        folderDrop: supportsFolderDrop,\n        folderSelection: supportsFolderSelection,\n        imagePreviews: supportsImagePreviews,\n        imageValidation: supportsImagePreviews,\n        itemSizeValidation: supportsAjaxFileUploading,\n        pause: supportsChunking,\n        progressBar: supportsUploadProgress,\n        resume: supportsResume,\n        scaling: supportsImagePreviews && supportsUploadingBlobs,\n        tiffPreviews: qq.safari(), // Not the best solution, but simple and probably accurate enough (for now)\n        unlimitedScaledImageSize: !qq.ios(), // false simply indicates that there is some known limit\n        uploading: supportsUploading,\n        uploadCors: supportsUploadCors,\n        uploadCustomHeaders: supportsAjaxFileUploading,\n        uploadNonMultipart: supportsAjaxFileUploading,\n        uploadViaPaste: supportsUploadViaPaste\n    };\n\n}());\n", "/*globals qq*/\n\n// Is the passed object a promise instance?\nqq.isGenericPromise = function(maybePromise) {\n    \"use strict\";\n    return !!(maybePromise && maybePromise.then && qq.isFunction(maybePromise.then));\n};\n\nqq.Promise = function() {\n    \"use strict\";\n\n    var successArgs, failureArgs,\n        successCallbacks = [],\n        failureCallbacks = [],\n        doneCallbacks = [],\n        state = 0;\n\n    qq.extend(this, {\n        then: function(onSuccess, onFailure) {\n            if (state === 0) {\n                if (onSuccess) {\n                    successCallbacks.push(onSuccess);\n                }\n                if (onFailure) {\n                    failureCallbacks.push(onFailure);\n                }\n            }\n            else if (state === -1) {\n                onFailure && onFailure.apply(null, failureArgs);\n            }\n            else if (onSuccess) {\n                onSuccess.apply(null, successArgs);\n            }\n\n            return this;\n        },\n\n        done: function(callback) {\n            if (state === 0) {\n                doneCallbacks.push(callback);\n            }\n            else {\n                callback.apply(null, failureArgs === undefined ? successArgs : failureArgs);\n            }\n\n            return this;\n        },\n\n        success: function() {\n            state = 1;\n            successArgs = arguments;\n\n            if (successCallbacks.length) {\n                qq.each(successCallbacks, function(idx, callback) {\n                    callback.apply(null, successArgs);\n                });\n            }\n\n            if (doneCallbacks.length) {\n                qq.each(doneCallbacks, function(idx, callback) {\n                    callback.apply(null, successArgs);\n                });\n            }\n\n            return this;\n        },\n\n        failure: function() {\n            state = -1;\n            failureArgs = arguments;\n\n            if (failureCallbacks.length) {\n                qq.each(failureCallbacks, function(idx, callback) {\n                    callback.apply(null, failureArgs);\n                });\n            }\n\n            if (doneCallbacks.length) {\n                qq.each(doneCallbacks, function(idx, callback) {\n                    callback.apply(null, failureArgs);\n                });\n            }\n\n            return this;\n        }\n    });\n};\n", "/*globals qq, document, CustomEvent*/\nqq.DragAndDrop = function(o) {\n    \"use strict\";\n\n    var options,\n        HIDE_ZONES_EVENT_NAME = \"qq-hidezones\",\n        HIDE_BEFORE_ENTER_ATTR = \"qq-hide-dropzone\",\n        uploadDropZones = [],\n        droppedFiles = [],\n        disposeSupport = new qq.DisposeSupport();\n\n    options = {\n        dropZoneElements: [],\n        allowMultipleItems: true,\n        classes: {\n            dropActive: null\n        },\n        callbacks: new qq.DragAndDrop.callbacks()\n    };\n\n    qq.extend(options, o, true);\n\n    function uploadDroppedFiles(files, uploadDropZone) {\n        // We need to convert the `FileList` to an actual `Array` to avoid iteration issues\n        var filesAsArray = Array.prototype.slice.call(files);\n\n        options.callbacks.dropLog(\"Grabbed \" + files.length + \" dropped files.\");\n        uploadDropZone.dropDisabled(false);\n        options.callbacks.processingDroppedFilesComplete(filesAsArray, uploadDropZone.getElement());\n    }\n\n    function traverseFileTree(entry) {\n        var parseEntryPromise = new qq.Promise();\n\n        if (entry.isFile) {\n            entry.file(function(file) {\n                var name = entry.name,\n                    fullPath = entry.fullPath,\n                    indexOfNameInFullPath = fullPath.indexOf(name);\n\n                // remove file name from full path string\n                fullPath = fullPath.substr(0, indexOfNameInFullPath);\n\n                // remove leading slash in full path string\n                if (fullPath.charAt(0) === \"/\") {\n                    fullPath = fullPath.substr(1);\n                }\n\n                file.qqPath = fullPath;\n                droppedFiles.push(file);\n                parseEntryPromise.success();\n            },\n            function(fileError) {\n                options.callbacks.dropLog(\"Problem parsing '\" + entry.fullPath + \"'.  FileError code \" + fileError.code + \".\", \"error\");\n                parseEntryPromise.failure();\n            });\n        }\n        else if (entry.isDirectory) {\n            getFilesInDirectory(entry).then(\n                function allEntriesRead(entries) {\n                    var entriesLeft = entries.length;\n\n                    qq.each(entries, function(idx, entry) {\n                        traverseFileTree(entry).done(function() {\n                            entriesLeft -= 1;\n\n                            if (entriesLeft === 0) {\n                                parseEntryPromise.success();\n                            }\n                        });\n                    });\n\n                    if (!entries.length) {\n                        parseEntryPromise.success();\n                    }\n                },\n\n                function readFailure(fileError) {\n                    options.callbacks.dropLog(\"Problem parsing '\" + entry.fullPath + \"'.  FileError code \" + fileError.code + \".\", \"error\");\n                    parseEntryPromise.failure();\n                }\n            );\n        }\n\n        return parseEntryPromise;\n    }\n\n    // Promissory.  Guaranteed to read all files in the root of the passed directory.\n    function getFilesInDirectory(entry, reader, accumEntries, existingPromise) {\n        var promise = existingPromise || new qq.Promise(),\n            dirReader = reader || entry.createReader();\n\n        dirReader.readEntries(\n            function readSuccess(entries) {\n                var newEntries = accumEntries ? accumEntries.concat(entries) : entries;\n\n                if (entries.length) {\n                    setTimeout(function() { // prevent stack overflow, however unlikely\n                        getFilesInDirectory(entry, dirReader, newEntries, promise);\n                    }, 0);\n                }\n                else {\n                    promise.success(newEntries);\n                }\n            },\n\n            promise.failure\n        );\n\n        return promise;\n    }\n\n    function handleDataTransfer(dataTransfer, uploadDropZone) {\n        var pendingFolderPromises = [],\n            handleDataTransferPromise = new qq.Promise();\n\n        options.callbacks.processingDroppedFiles();\n        uploadDropZone.dropDisabled(true);\n\n        if (dataTransfer.files.length > 1 && !options.allowMultipleItems) {\n            options.callbacks.processingDroppedFilesComplete([]);\n            options.callbacks.dropError(\"tooManyFilesError\", \"\");\n            uploadDropZone.dropDisabled(false);\n            handleDataTransferPromise.failure();\n        }\n        else {\n            droppedFiles = [];\n\n            if (qq.isFolderDropSupported(dataTransfer)) {\n                qq.each(dataTransfer.items, function(idx, item) {\n                    var entry = item.webkitGetAsEntry();\n\n                    if (entry) {\n                        //due to a bug in Chrome's File System API impl - #149735\n                        if (entry.isFile) {\n                            droppedFiles.push(item.getAsFile());\n                        }\n\n                        else {\n                            pendingFolderPromises.push(traverseFileTree(entry).done(function() {\n                                pendingFolderPromises.pop();\n                                if (pendingFolderPromises.length === 0) {\n                                    handleDataTransferPromise.success();\n                                }\n                            }));\n                        }\n                    }\n                });\n            }\n            else {\n                droppedFiles = dataTransfer.files;\n            }\n\n            if (pendingFolderPromises.length === 0) {\n                handleDataTransferPromise.success();\n            }\n        }\n\n        return handleDataTransferPromise;\n    }\n\n    function setupDropzone(dropArea) {\n        var dropZone = new qq.UploadDropZone({\n            HIDE_ZONES_EVENT_NAME: HIDE_ZONES_EVENT_NAME,\n            element: dropArea,\n            onEnter: function(e) {\n                qq(dropArea).addClass(options.classes.dropActive);\n                e.stopPropagation();\n            },\n            onLeaveNotDescendants: function(e) {\n                qq(dropArea).removeClass(options.classes.dropActive);\n            },\n            onDrop: function(e) {\n                handleDataTransfer(e.dataTransfer, dropZone).then(\n                    function() {\n                        uploadDroppedFiles(droppedFiles, dropZone);\n                    },\n                    function() {\n                        options.callbacks.dropLog(\"Drop event DataTransfer parsing failed.  No files will be uploaded.\", \"error\");\n                    }\n                );\n            }\n        });\n\n        disposeSupport.addDisposer(function() {\n            dropZone.dispose();\n        });\n\n        qq(dropArea).hasAttribute(HIDE_BEFORE_ENTER_ATTR) && qq(dropArea).hide();\n\n        uploadDropZones.push(dropZone);\n\n        return dropZone;\n    }\n\n    function isFileDrag(dragEvent) {\n        var fileDrag;\n\n        qq.each(dragEvent.dataTransfer.types, function(key, val) {\n            if (val === \"Files\") {\n                fileDrag = true;\n                return false;\n            }\n        });\n\n        return fileDrag;\n    }\n\n    // Attempt to determine when the file has left the document.  It is not always possible to detect this\n    // in all cases, but it is generally possible in all browsers, with a few exceptions.\n    //\n    // Exceptions:\n    // * IE10+ & Safari: We can't detect a file leaving the document if the Explorer window housing the file\n    //                   overlays the browser window.\n    // * IE10+: If the file is dragged out of the window too quickly, IE does not set the expected values of the\n    //          event's X & Y properties.\n    function leavingDocumentOut(e) {\n        if (qq.firefox()) {\n            return !e.relatedTarget;\n        }\n\n        if (qq.safari()) {\n            return e.x < 0 || e.y < 0;\n        }\n\n        return e.x === 0 && e.y === 0;\n    }\n\n    function setupDragDrop() {\n        var dropZones = options.dropZoneElements,\n\n            maybeHideDropZones = function() {\n                setTimeout(function() {\n                    qq.each(dropZones, function(idx, dropZone) {\n                        qq(dropZone).hasAttribute(HIDE_BEFORE_ENTER_ATTR) && qq(dropZone).hide();\n                        qq(dropZone).removeClass(options.classes.dropActive);\n                    });\n                }, 10);\n            };\n\n        qq.each(dropZones, function(idx, dropZone) {\n            var uploadDropZone = setupDropzone(dropZone);\n\n            // IE <= 9 does not support the File API used for drag+drop uploads\n            if (dropZones.length && qq.supportedFeatures.fileDrop) {\n                disposeSupport.attach(document, \"dragenter\", function(e) {\n                    if (!uploadDropZone.dropDisabled() && isFileDrag(e)) {\n                        qq.each(dropZones, function(idx, dropZone) {\n                            // We can't apply styles to non-HTMLElements, since they lack the `style` property.\n                            // Also, if the drop zone isn't initially hidden, let's not mess with `style.display`.\n                            if (dropZone instanceof HTMLElement &&\n                                qq(dropZone).hasAttribute(HIDE_BEFORE_ENTER_ATTR)) {\n\n                                qq(dropZone).css({display: \"block\"});\n                            }\n                        });\n                    }\n                });\n            }\n        });\n\n        disposeSupport.attach(document, \"dragleave\", function(e) {\n            if (leavingDocumentOut(e)) {\n                maybeHideDropZones();\n            }\n        });\n\n        // Just in case we were not able to detect when a dragged file has left the document,\n        // hide all relevant drop zones the next time the mouse enters the document.\n        // Note that mouse events such as this one are not fired during drag operations.\n        disposeSupport.attach(qq(document).children()[0], \"mouseenter\", function(e) {\n            maybeHideDropZones();\n        });\n\n        disposeSupport.attach(document, \"drop\", function(e) {\n            e.preventDefault();\n            maybeHideDropZones();\n        });\n\n        disposeSupport.attach(document, HIDE_ZONES_EVENT_NAME, maybeHideDropZones);\n    }\n\n    setupDragDrop();\n\n    qq.extend(this, {\n        setupExtraDropzone: function(element) {\n            options.dropZoneElements.push(element);\n            setupDropzone(element);\n        },\n\n        removeDropzone: function(element) {\n            var i,\n                dzs = options.dropZoneElements;\n\n            for (i in dzs) {\n                if (dzs[i] === element) {\n                    return dzs.splice(i, 1);\n                }\n            }\n        },\n\n        dispose: function() {\n            disposeSupport.dispose();\n            qq.each(uploadDropZones, function(idx, dropZone) {\n                dropZone.dispose();\n            });\n        }\n    });\n};\n\nqq.DragAndDrop.callbacks = function() {\n    \"use strict\";\n\n    return {\n        processingDroppedFiles: function() {},\n        processingDroppedFilesComplete: function(files, targetEl) {},\n        dropError: function(code, errorSpecifics) {\n            qq.log(\"Drag & drop error code '\" + code + \" with these specifics: '\" + errorSpecifics + \"'\", \"error\");\n        },\n        dropLog: function(message, level) {\n            qq.log(message, level);\n        }\n    };\n};\n\nqq.UploadDropZone = function(o) {\n    \"use strict\";\n\n    var disposeSupport = new qq.DisposeSupport(),\n        options, element, preventDrop, dropOutsideDisabled;\n\n    options = {\n        element: null,\n        onEnter: function(e) {},\n        onLeave: function(e) {},\n        // is not fired when leaving element by hovering descendants\n        onLeaveNotDescendants: function(e) {},\n        onDrop: function(e) {}\n    };\n\n    qq.extend(options, o);\n    element = options.element;\n\n    function dragoverShouldBeCanceled() {\n        return qq.safari() || (qq.firefox() && qq.windows());\n    }\n\n    function disableDropOutside(e) {\n        // run only once for all instances\n        if (!dropOutsideDisabled) {\n\n            // for these cases we need to catch onDrop to reset dropArea\n            if (dragoverShouldBeCanceled) {\n                disposeSupport.attach(document, \"dragover\", function(e) {\n                    e.preventDefault();\n                });\n            } else {\n                disposeSupport.attach(document, \"dragover\", function(e) {\n                    if (e.dataTransfer) {\n                        e.dataTransfer.dropEffect = \"none\";\n                        e.preventDefault();\n                    }\n                });\n            }\n\n            dropOutsideDisabled = true;\n        }\n    }\n\n    function isValidFileDrag(e) {\n        // e.dataTransfer currently causing IE errors\n        // IE9 does NOT support file API, so drag-and-drop is not possible\n        if (!qq.supportedFeatures.fileDrop) {\n            return false;\n        }\n\n        var effectTest, dt = e.dataTransfer,\n        // do not check dt.types.contains in webkit, because it crashes safari 4\n        isSafari = qq.safari();\n\n        // dt.effectAllowed is none in Safari 5\n        // dt.types.contains check is for firefox\n\n        // dt.effectAllowed crashes IE 11 & 10 when files have been dragged from\n        // the filesystem\n        effectTest = qq.ie() && qq.supportedFeatures.fileDrop ? true : dt.effectAllowed !== \"none\";\n        return dt && effectTest && (dt.files || (!isSafari && dt.types.contains && dt.types.contains(\"Files\")));\n    }\n\n    function isOrSetDropDisabled(isDisabled) {\n        if (isDisabled !== undefined) {\n            preventDrop = isDisabled;\n        }\n        return preventDrop;\n    }\n\n    function triggerHidezonesEvent() {\n        var hideZonesEvent;\n\n        function triggerUsingOldApi() {\n            hideZonesEvent = document.createEvent(\"Event\");\n            hideZonesEvent.initEvent(options.HIDE_ZONES_EVENT_NAME, true, true);\n        }\n\n        if (window.CustomEvent) {\n            try {\n                hideZonesEvent = new CustomEvent(options.HIDE_ZONES_EVENT_NAME);\n            }\n            catch (err) {\n                triggerUsingOldApi();\n            }\n        }\n        else {\n            triggerUsingOldApi();\n        }\n\n        document.dispatchEvent(hideZonesEvent);\n    }\n\n    function attachEvents() {\n        disposeSupport.attach(element, \"dragover\", function(e) {\n            if (!isValidFileDrag(e)) {\n                return;\n            }\n\n            // dt.effectAllowed crashes IE 11 & 10 when files have been dragged from\n            // the filesystem\n            var effect = qq.ie() && qq.supportedFeatures.fileDrop ? null : e.dataTransfer.effectAllowed;\n            if (effect === \"move\" || effect === \"linkMove\") {\n                e.dataTransfer.dropEffect = \"move\"; // for FF (only move allowed)\n            } else {\n                e.dataTransfer.dropEffect = \"copy\"; // for Chrome\n            }\n\n            e.stopPropagation();\n            e.preventDefault();\n        });\n\n        disposeSupport.attach(element, \"dragenter\", function(e) {\n            if (!isOrSetDropDisabled()) {\n                if (!isValidFileDrag(e)) {\n                    return;\n                }\n                options.onEnter(e);\n            }\n        });\n\n        disposeSupport.attach(element, \"dragleave\", function(e) {\n            if (!isValidFileDrag(e)) {\n                return;\n            }\n\n            options.onLeave(e);\n\n            var relatedTarget = document.elementFromPoint(e.clientX, e.clientY);\n            // do not fire when moving a mouse over a descendant\n            if (qq(this).contains(relatedTarget)) {\n                return;\n            }\n\n            options.onLeaveNotDescendants(e);\n        });\n\n        disposeSupport.attach(element, \"drop\", function(e) {\n            if (!isOrSetDropDisabled()) {\n                if (!isValidFileDrag(e)) {\n                    return;\n                }\n\n                e.preventDefault();\n                e.stopPropagation();\n                options.onDrop(e);\n\n                triggerHidezonesEvent();\n            }\n        });\n    }\n\n    disableDropOutside();\n    attachEvents();\n\n    qq.extend(this, {\n        dropDisabled: function(isDisabled) {\n            return isOrSetDropDisabled(isDisabled);\n        },\n\n        dispose: function() {\n            disposeSupport.dispose();\n        },\n\n        getElement: function() {\n            return element;\n        }\n    });\n};\n"]}