!function(e) {
    "use strict";
    var t = /MSIE/.test(navigator.userAgent);
    function i(n, c, s, o) {
        if (!c.busy) {
            var l = n[0].parentNode
              , a = n[c.currSlide]
              , u = n[c.nextSlide];
            if (0 !== l.cycleTimeout || s)
                if (s || !l.cyclePause) {
                    c.before.length && e.each(c.before, function(e, t) {
                        t.apply(u, [a, u, c, o])
                    });
                    c.nextSlide != c.currSlide && (c.busy = 1,
                    e.fn.cycle.custom(a, u, c, function() {
                        t && this.style.removeAttribute("filter"),
                        e.each(c.after, function(e, t) {
                            t.apply(u, [a, u, c, o])
                        }),
                        d(c)
                    }));
                    var r = c.nextSlide + 1 == n.length;
                    c.nextSlide = r ? 0 : c.nextSlide + 1,
                    c.currSlide = r ? n.length - 1 : c.nextSlide - 1
                } else
                    d(c)
        }
        function d(e) {
            e.timeout && (l.cycleTimeout = setTimeout(function() {
                i(n, e, 0, !e.rev)
            }, e.timeout))
        }
    }
    function n(e, t, n) {
        var c = e[0].parentNode
          , s = c.cycleTimeout;
        return s && (clearTimeout(s),
        c.cycleTimeout = 0),
        t.nextSlide = t.currSlide + n,
        t.nextSlide < 0 ? t.nextSlide = e.length - 1 : t.nextSlide >= e.length && (t.nextSlide = 0),
        i(e, t, 1, n >= 0),
        !1
    }
    e.fn.cycle = function(c) {
        return this.each(function() {
            c = c || {},
            this.cycleTimeout && clearTimeout(this.cycleTimeout),
            this.cycleTimeout = 0,
            this.cyclePause = 0;
            var s = e(this)
              , o = c.slideExpr ? e(c.slideExpr, this) : s.children()
              , l = o.get();
            if (l.length < 2)
                window.console && console.log("terminating; too few slides: " + l.length);
            else {
                var a = e.extend({}, e.fn.cycle.defaults, c || {}, e.metadata ? s.metadata() : e.meta ? s.data() : {})
                  , u = e.isFunction(s.data) ? s.data(a.metaAttr) : null;
                u && (a = e.extend(a, u)),
                a.before = a.before ? [a.before] : [],
                a.after = a.after ? [a.after] : [],
                a.after.unshift(function() {
                    a.busy = 0
                });
                var r = this.className;
                a.width = parseInt((r.match(/w:(\d+)/) || [])[1], 10) || a.width,
                a.height = parseInt((r.match(/h:(\d+)/) || [])[1], 10) || a.height,
                a.timeout = parseInt((r.match(/t:(\d+)/) || [])[1], 10) || a.timeout,
                "static" == s.css("position") && s.css("position", "relative"),
                a.width && s.width(a.width),
                a.height && "auto" != a.height && s.height(a.height);
                o.css({
                    position: "absolute",
                    top: 0
                }).each(function(t) {
                    e(this).css("z-index", l.length - t)
                }),
                e(l[0]).css("opacity", 1).show(),
                t && l[0].style.removeAttribute("filter"),
                a.fit && a.width && o.width(a.width),
                a.fit && a.height && "auto" != a.height && o.height(a.height),
                a.pause && s.hover(function() {
                    this.cyclePause = 1
                }, function() {
                    this.cyclePause = 0
                });
                var d = e.fn.cycle.transitions[a.fx];
                if (d && d(s, o, a),
                o.each(function() {
                    var t = e(this);
                    this.cycleH = a.fit && a.height ? a.height : t.height(),
                    this.cycleW = a.fit && a.width ? a.width : t.width()
                }),
                a.cssFirst && e(o[0]).css(a.cssFirst),
                a.timeout)
                    for (a.speed.constructor == String && (a.speed = {
                        slow: 600,
                        fast: 200
                    }[a.speed] || 400),
                    a.sync || (a.speed = a.speed / 2); a.timeout - a.speed < 250; )
                        a.timeout += a.speed;
                a.speedIn = a.speed,
                a.speedOut = a.speed,
                a.slideCount = l.length,
                a.currSlide = 0,
                a.nextSlide = 1;
                var h = o[0];
                a.before.length && a.before[0].apply(h, [h, h, a, !0]),
                a.after.length > 1 && a.after[1].apply(h, [h, h, a, !0]),
                a.click && !a.next && (a.next = a.click),
                a.next && e(a.next).unbind("click.cycle").bind("click.cycle", function() {
                    return n(l, a, a.rev ? -1 : 1)
                }),
                a.prev && e(a.prev).unbind("click.cycle").bind("click.cycle", function() {
                    return n(l, a, a.rev ? 1 : -1)
                }),
                a.timeout && (this.cycleTimeout = setTimeout(function() {
                    i(l, a, 0, !a.rev)
                }, a.timeout + (a.delay || 0)))
            }
        })
    }
    ,
    e.fn.cycle.custom = function(t, i, n, c) {
        var s = e(t)
          , o = e(i);
        o.css(n.cssBefore);
        var l = function() {
            o.animate(n.animIn, n.speedIn, n.easeIn, c)
        };
        s.animate(n.animOut, n.speedOut, n.easeOut, function() {
            s.css(n.cssAfter),
            n.sync || l()
        }),
        n.sync && l()
    }
    ,
    e.fn.cycle.transitions = {
        fade: function(e, t, i) {
            t.not(":eq(0)").hide(),
            i.cssBefore = {
                opacity: 0,
                display: "block"
            },
            i.cssAfter = {
                display: "none"
            },
            i.animOut = {
                opacity: 0
            },
            i.animIn = {
                opacity: 1
            }
        },
        fadeout: function(t, i, n) {
            n.before.push(function(t, i, n, c) {
                e(t).css("zIndex", n.slideCount + (!0 === c ? 1 : 0)),
                e(i).css("zIndex", n.slideCount + (!0 === c ? 0 : 1))
            }),
            i.not(":eq(0)").hide(),
            n.cssBefore = {
                opacity: 1,
                display: "block",
                zIndex: 1
            },
            n.cssAfter = {
                display: "none",
                zIndex: 0
            },
            n.animOut = {
                opacity: 0
            },
            n.animIn = {
                opacity: 1
            }
        }
    },
    e.fn.cycle.ver = function() {
        return "Lite-1.7"
    }
    ,
    e.fn.cycle.defaults = {
        animIn: {},
        animOut: {},
        fx: "fade",
        after: null,
        before: null,
        cssBefore: {},
        cssAfter: {},
        delay: 0,
        fit: 0,
        height: "auto",
        metaAttr: "cycle",
        next: null,
        pause: !1,
        prev: null,
        speed: 1e3,
        slideExpr: null,
        sync: !0,
        timeout: 4e3
    }
}(jQuery);
