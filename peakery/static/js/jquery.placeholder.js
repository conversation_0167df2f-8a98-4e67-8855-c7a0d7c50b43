/*! http://mths.be/placeholder v1.8.4 by @mathias*/
(function($) {
    var e = 'placeholder'in document.createElement('input')
      , a = 'placeholder'in document.createElement('textarea');
    if (e && a) {
        $.fn.placeholder = function() {
            return this
        }
        ;
        $.fn.placeholder.input = $.fn.placeholder.textarea = true
    } else {
        $.fn.placeholder = function() {
            return this.filter((e ? 'textarea' : ':input') + '[placeholder]').bind('focus.placeholder', b).bind('blur.placeholder', d).trigger('blur.placeholder').end()
        }
        ;
        $.fn.placeholder.input = e;
        $.fn.placeholder.textarea = a;
        $(function() {
            $('form').bind('submit.placeholder', function() {
                var f = $('.placeholder', this).each(b);
                setTimeout(function() {
                    f.each(d)
                }, 10)
            })
        });
        $(window).bind('unload.placeholder', function() {
            $('.placeholder').val('')
        })
    }
    function c(g) {
        var f = {}
          , h = /^jQuery\d+$/;
        $.each(g.attributes, function(k, j) {
            if (j.specified && !h.test(j.name)) {
                f[j.name] = j.value
            }
        });
        return f
    }
    function b() {
        var f = $(this);
        if (f.val() === f.attr('placeholder') && f.hasClass('placeholder')) {
            if (f.data('placeholder-password')) {
                f.hide().next().attr('id', f.removeAttr('id').data('placeholder-id')).show().focus()
            } else {
                f.val('').removeClass('placeholder')
            }
        }
    }
    function d() {
        var j, i = $(this), f = i, h = this.id;
        if (i.val() === '') {
            if (i.is(':password')) {
                if (!i.data('placeholder-textinput')) {
                    try {
                        j = i.clone().attr({
                            type: 'text'
                        })
                    } catch (g) {
                        j = $('<input>').attr($.extend(c(this), {
                            type: 'text'
                        }))
                    }
                    j.removeAttr('name').data('placeholder-password', true).data('placeholder-id', h).bind('focus.placeholder', b);
                    i.data('placeholder-textinput', j).data('placeholder-id', h).before(j)
                }
                i = i.removeAttr('id').hide().prev().attr('id', h).show()
            }
            i.addClass('placeholder').val(i.attr('placeholder'))
        } else {
            i.removeClass('placeholder')
        }
    }
}(jQuery));
