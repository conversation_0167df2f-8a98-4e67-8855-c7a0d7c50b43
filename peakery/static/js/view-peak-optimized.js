/**
 * View Peak Optimized JavaScript
 * Consolidated and optimized JavaScript for peak view page
 */

// Global variables
var initial_highlights = [];
var uploaderIdle = true;
var viewer, topo, outdoors;
var center = null;
var map_bounds;
var init = false;
var photos_displayed = 0;
var photos_page = 1;
var photos = [];
var pageX, pageY, mapX, mapY;
var iconstyle;
var combineFromId, combineToId;
var timers = {};
var mapExpanded = false;

// Initialize page
$(document).ready(function() {
    initializePhotoHovers();
    initializeFacebox();
    initializeMobileApp();
    initializeLogClimb();
    initializePeakSearch();
    initializeRoutes();
    initializeAdminFeatures();
    initializeMapCanvas();
    initializePhotoGallery();
    initializeSlideshow();
    initializeHighlights();
    initializePhotoUpload();
    initializeWindowResize();
    
    // Load photos on page load
    loadPhotos(photos_page);
    
    // Update map canvas size
    updateMapCanvasSize();
});

// Photo hover effects
function initializePhotoHovers() {
    $("#photos-list, #more-photos-list").on('mouseenter', 'div', function() {
        $(this).children('.user-photo-info').fadeIn(200);
    }).on('mouseleave', 'div', function() {
        $(this).children('.user-photo-info').fadeOut(200);
    });
}

// Facebox initialization
function initializeFacebox() {
    $(document).bind('beforeReveal.facebox', function() {
        $('.content, .close').show();
    }).bind('loading.facebox', function() {
        $('.content, .close').hide();
    });
}

// Mobile app integration
function initializeMobileApp() {
    $("#mobile-app-map-link").on('click', function() {
        if (typeof Android !== 'undefined') {
            Android.peakInfo(peakObject);
        }
    });
}

// Log climb functionality
function initializeLogClimb() {
    $('#log-climb-log-manually .navbar-primary .log-climb-dropdown-label-div').html('Log climb of another peak');
    $('#log-climb-log-this-peak .navbar-primary').attr('href', '/peaks/log_climb/?peak=' + peak_id);
    $('#log-climb-log-this-peak .navbar-primary .log-climb-dropdown-label-div').html('Log ' + (typeof peak !== 'undefined' ? peak.name : ''));
    $('#log-climb-log-this-peak').show();
}

// Peak search functionality
function initializePeakSearch() {
    var leftColWidth = $('div#explore .leftCol').width();
    $('li.headlink').css('width', leftColWidth);
    
    var clearPeakName = $('a#clear_peak_name');
    var clearNearLocation = $('a#clear_near_location');
    var peakInput = $('input#q');
    var locationInput = $('input#n');
    
    // Show clear buttons if inputs have values
    if (peakInput.val() !== "") clearPeakName.css('display', 'block');
    if (locationInput.val() !== "") clearNearLocation.css('display', 'block');
    
    // Input event handlers
    peakInput.keyup(function() {
        clearPeakName.css('display', $(this).val() !== "" ? 'block' : 'none');
    });
    
    locationInput.keyup(function() {
        clearNearLocation.css('display', $(this).val() !== "" ? 'block' : 'none');
    });
    
    // Clear button handlers
    clearPeakName.click(function() {
        peakInput.val('');
        $(this).css('display', 'none');
    });
    
    clearNearLocation.click(function() {
        locationInput.val('');
        $(this).css('display', 'none');
    });
    
    $('#peak-search').css({left: '0px'});
    
    // Peak row click handler
    $(document).on('click', '.peak-row', function(e) {
        var url = $(this).data('url');
        if (url !== '') {
            window.location.href = url;
        }
    });
}

// Route initialization
function initializeRoutes() {
    // Add GPX to route maps (template variables will be processed server-side)
}

// Admin features initialization
function initializeAdminFeatures() {
    if (typeof isUserSuperuser !== 'undefined' && isUserSuperuser) {
        initializeElevationEdit();
        initializeProminenceEdit();
        initializeRangeEdit();
        initializeChallengeEdit();
        initializeRouteCombiners();
        initializeChallengeLinks();
        initializePhotoRemoval();
    }
    
    if (typeof isUserStaff !== 'undefined' && isUserStaff) {
        initializeMainPhotoRemoval();
    }
}

// Elevation editing
function initializeElevationEdit() {
    $(document).on('click', '.admin-edit-elevation', function(e) {
        var peakId = $(this).data('peakid');
        var elevation = $(this).data('elevation');
        
        $('#peak-elevation-id').val(peakId);
        $('#peak-elevation').val(elevation);
        $("#peak-elevation-ft").click();
        $("#edit-elevation-message").html('').hide();
        
        setTimeout(function() {
            $('#peak-elevation').focus().select();
        }, 500);
    });
    
    $("#peak-elevation-m").on('change', setElevationMeters);
    $("#peak-elevation-ft").on('change', setElevationFeet);
    
    $('#save-peak-elevation').on('click', function() {
        if ($('#peak-elevation').val() !== '') {
            $(this).prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i>');
            adminUpdatePeakElevation();
        }
    });
}

// Prominence editing
function initializeProminenceEdit() {
    $(document).on('click', '.admin-edit-prominence', function(e) {
        var peakId = $(this).data('peakid');
        var prominence = $(this).data('prominence');
        
        $('#peak-prominence-id').val(peakId);
        $('#peak-prominence').val(prominence);
        $("#peak-prominence-ft").click();
        $("#edit-prominence-message").html('').hide();
        
        setTimeout(function() {
            $('#peak-prominence').focus().select();
        }, 500);
    });
    
    $("#peak-prominence-m").on('change', setProminenceMeters);
    $("#peak-prominence-ft").on('change', setProminenceFeet);
    
    $('#save-peak-prominence').on('click', function() {
        $(this).prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i>');
        adminUpdatePeakProminence();
    });
}

// Range editing
function initializeRangeEdit() {
    $(document).on('click', '.admin-edit-range', function(e) {
        var peakId = $(this).data('peakid');
        var range = $(this).data('range');
        
        $('#peak-range-id').val(peakId);
        $('#peak-range').val(range);
        $("#edit-range-message").html('').hide();
        
        setTimeout(function() {
            $('#peak-range').focus().select();
        }, 500);
    });
    
    $('#save-peak-range').on('click', function() {
        $(this).prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i>');
        adminUpdatePeakRange();
    });
}

// Challenge editing
function initializeChallengeEdit() {
    $(document).on('click', '.admin-edit-challenge-checkbox', function(e) {
        var peakId = $(this).data('peakid');
        var challengeId = $(this).data('challengeid');
        var isChecked = $('input[id=admin-edit-challenge-' + challengeId + ']:checked').val();
        var action = isChecked === 'on' ? 'add' : 'remove';
        
        // Make AJAX call (URL will be processed server-side)
        $.post('/admin/update-peak-challenge/', {
            id: peakId,
            challengeid: challengeId,
            action: action
        }, function(data) {
            alert(data);
        });
    });
}

// Route combiners
function initializeRouteCombiners() {
    $('.combine-from-route').on('click', function() {
        $(".combine-to-route, .combine-to-gpx").attr('disabled', true).attr('checked', false);
        
        $(".combine-from-route").each(function() {
            if (this.checked) {
                combineFromId = $(this).val();
                $(".combine-to-route").each(function() {
                    if ($(this).val() !== combineFromId) {
                        $(this).attr('disabled', false);
                    }
                });
            }
        });
    });
    
    $('.combine-to-route').on('click', function() {
        $(".combine-to-gpx").attr('disabled', true).attr('checked', false);
        
        $(".combine-to-route").each(function() {
            if (this.checked) {
                combineToId = $(this).val();
                $(".combine-to-gpx").each(function() {
                    if ($(this).val() === combineFromId || $(this).val() === combineToId) {
                        $(this).attr('disabled', false);
                    }
                });
            }
        });
    });
}

// Challenge links
function initializeChallengeLinks() {
    $('#see-all-challenges-link').on('click', function() {
        $('#more-challenges').show();
        $('#see-fewer-challenges-link').show();
        $(this).hide();
    });
    
    $('#see-fewer-challenges-link').on('click', function() {
        $('#more-challenges').hide();
        $('#see-all-challenges-link').show();
        $(this).hide();
    });
}

// Map canvas initialization
function initializeMapCanvas() {
    var mapHeight = $('#peak-photo-col').height();
    $('#map-canvas').height(mapHeight);
    
    $('#map-canvas').mousemove(function(e) {
        var offset = $(this).offset();
        pageX = e.pageX;
        pageY = e.pageY;
        mapX = (e.pageX - offset.left);
        mapY = (e.pageY - offset.top);
    });
    
    $('#map-canvas').on('touchstart', function(e) {
        var offset = $(this).offset();
        pageX = e.originalEvent.touches[0].pageX;
        pageY = e.originalEvent.touches[0].pageY;
        mapX = (pageX - offset.left);
        mapY = (pageY - offset.top);
    });
}

// Utility functions
function openUrl(url) {
    window.location.href = url;
}

function round(value, decimals) {
    return Number(Math.round(value + 'e' + decimals) + 'e-' + decimals);
}

function getRepString(rep) {
    rep = rep + '';
    if (rep < 1000) return rep;
    return (rep / 1000).toFixed(rep % 1000 !== 0) + 'K';
}

function numberWithCommas(x) {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

function updateURLParameter(url, param, paramVal) {
    var newAdditionalURL = "";
    var tempArray = url.split("#");
    var baseURL = tempArray[0];
    var additionalURL = tempArray[1];
    var temp = "";

    if (additionalURL) {
        tempArray = additionalURL.split("&");
        for (var i = 0; i < tempArray.length; i++) {
            if (tempArray[i].split('=')[0] !== param) {
                newAdditionalURL += temp + tempArray[i];
                temp = "&";
            }
        }
    }

    var rows_txt = temp + "" + param + "=" + paramVal;
    return newAdditionalURL + rows_txt;
}

// Admin update functions
function adminUpdatePeakElevation() {
    var peakId = $('#peak-elevation-id').val();
    var elevation = $('#peak-elevation').val();
    var units = $('input[name=peak-elevation-units]:checked').val();

    $.post('/admin/update-peak-elevation/', {
        id: peakId,
        elevation: elevation,
        units: units
    }, function(data) {
        $('#save-peak-elevation').prop('disabled', false).html('Save');
        var output = $("#edit-elevation-message");
        output.html(data).show();
        $('#edit-elevation').modal('hide');

        // Update UI with new elevation
        if (units !== 'ft') {
            var elevationMeters = elevation;
            elevation = Math.round(elevation / 0.3048);
        } else {
            var elevationFeet = parseFloat(elevation);
            var elevationMeters = Math.round(elevationFeet * 0.3048);
        }

        $('.admin-edit-elevation').data('elevation', elevation);
        var elevationFormatted = numberWithCommas(elevation) + ' ft / ' + numberWithCommas(elevationMeters) + ' m';
        $('.peak-elevation-formatted').html(elevationFormatted);
    });
}

function setElevationMeters() {
    if ($('#peak-elevation').val() !== '') {
        var elevationFeet = parseFloat($('#peak-elevation').val());
        var elevationMeters = Math.round(elevationFeet * 0.3048);
        $('#peak-elevation').val(elevationMeters);
    }
    $('#peak-elevation').attr('placeholder', 'in meters...');
}

function setElevationFeet() {
    if ($('#peak-elevation').val() !== '') {
        var elevationMeters = parseFloat($('#peak-elevation').val());
        var elevationFeet = Math.round(elevationMeters / 0.3048);
        $('#peak-elevation').val(elevationFeet);
    }
    $('#peak-elevation').attr('placeholder', 'in feet...');
}

function adminUpdatePeakProminence() {
    var peakId = $('#peak-prominence-id').val();
    var prominence = $('#peak-prominence').val();
    var units = $('input[name=peak-prominence-units]:checked').val();

    $.post('/admin/update-peak-prominence/', {
        id: peakId,
        prominence: prominence,
        units: units
    }, function(data) {
        $('#save-peak-prominence').prop('disabled', false).html('Save');
        var output = $("#edit-prominence-message");
        output.html(data).show();
        $('#edit-prominence').modal('hide');

        // Update UI with new prominence
        if (prominence !== '') {
            if (units !== 'ft') {
                var prominenceMeters = prominence;
                prominence = Math.round(prominence / 0.3048);
            } else {
                var prominenceFeet = parseFloat(prominence);
                var prominenceMeters = Math.round(prominenceFeet * 0.3048);
            }

            $('.admin-edit-prominence').data('prominence', prominence);
            var prominenceFormatted = numberWithCommas(prominence) + ' ft / ' + numberWithCommas(prominenceMeters) + ' m';
            $('.peak-prominence-formatted').html(prominenceFormatted);
            $('.peak-prominence-missing').hide();
            $('.peak-prominence-data').show();
        } else {
            $('.admin-edit-prominence').data('prominence', '');
            $('.peak-prominence-formatted').html('');
            $('.peak-prominence-data').hide();
            $('.peak-prominence-missing').show();
        }
    });
}

function setProminenceMeters() {
    if ($('#peak-prominence').val() !== '') {
        var prominenceFeet = parseFloat($('#peak-prominence').val());
        var prominenceMeters = Math.round(prominenceFeet * 0.3048);
        $('#peak-prominence').val(prominenceMeters);
    }
    $('#peak-prominence').attr('placeholder', 'in meters...');
}

function setProminenceFeet() {
    if ($('#peak-prominence').val() !== '') {
        var prominenceMeters = parseFloat($('#peak-prominence').val());
        var prominenceFeet = Math.round(prominenceMeters / 0.3048);
        $('#peak-prominence').val(prominenceFeet);
    }
    $('#peak-prominence').attr('placeholder', 'in feet...');
}

function adminUpdatePeakRange() {
    var peakId = $('#peak-range-id').val();
    var range = $('#peak-range').val();

    $.post('/admin/update-peak-range/', {
        id: peakId,
        range: range
    }, function(data) {
        $('#save-peak-range').prop('disabled', false).html('Save');
        var output = $("#edit-range-message");
        output.html(data).show();
        $('#edit-range').modal('hide');

        $('.peak-range-div').data('url', '/peaks/#range=' + encodeURIComponent(range));
        $('.peak-range-formatted').html(range).attr('href', '/peaks/#range=' + encodeURIComponent(range));
        $('.admin-edit-range').data('range', range);

        if (range !== '') {
            $('.peak-range-missing').hide();
            $('.peak-range-data').show();
        } else {
            $('.peak-range-data').hide();
            $('.peak-range-missing').show();
        }
    });
}

// Photo loading functionality
function loadPhotos(page) {
    if (page === 1) {
        $('#ajax-data-loading').css('display', 'inline');
        $('#photos-list, #more-photos-list').empty();
    } else {
        $('#more-ajax-data-loading').css('display', 'inline');
    }

    var photoCount = (typeof peakPhotosCount !== 'undefined' ? peakPhotosCount : '0').replace(',', '');
    var totalPages = Math.floor(parseInt(photoCount) / 12);
    var photoIndex = 1;

    $.getJSON('/api/peak-photos/?peak_id=' + peak_id + '&page=' + page, function(data) {
        $.each(data, function(key, val) {
            if (key === 'photos') {
                $.each(val, function(photokey, photoval) {
                    var photoCaption = photoval.caption === 'None' ? '' : photoval.caption;
                    var adminMode = readCookie('admin-mode');
                    var removePhotoDivStyle = adminMode === 'true' ? '' : 'display: none;';

                    var photoHtml = buildPhotoHtml(photoval, photoCaption, removePhotoDivStyle, photoIndex);

                    if (page === 1) {
                        $('#photos-list').append(photoHtml);
                    } else {
                        $('#more-photos-list').append(photoHtml);
                    }

                    photoIndex++;
                    photos_displayed++;
                    photos.push(photoval.fullsize_url);
                });

                $("time.timeago").timeago();
            }
        });

        // Fill with Flickr photos if needed
        if (photos.length < 12) {
            loadFlickrPhotos(photoIndex);
        }

        // Hide loading indicators
        if (page === 1) {
            $('#ajax-data-loading').css('display', 'none');
        } else {
            $('#more-ajax-data-loading').css('display', 'none');
        }

        // Show/hide photos footer
        if (photos_displayed < photoCount) {
            $('#see-more-photos-div, #photos-footer').show();
        } else {
            $('#see-more-photos-div, #photos-footer').hide();
        }

        photos_page++;
    });
}

function buildPhotoHtml(photoval, photoCaption, removePhotoDivStyle, photoIndex) {
    var divClass = 'col-lg-3 col-md-3 col-sm-4 col-xs-6';
    var borderClass = 'photo-grid-' + photoIndex.toString();
    var photoCaptionClass = 'user-photo-info peak-photo-with-caption';

    return '<a id="gallery-photo-' + photoval.photo_id + '" class="gallery-link" ' +
           'data-user="' + photoval.username + '" data-credit="" ' +
           'data-description="' + photoCaption + '" data-gallery ' +
           'href="' + photoval.fullsize_url + '">' +
           '<div class="' + divClass + ' ' + borderClass + '" ' +
           'style="cursor: pointer; padding: 0; background-image: url(\'' + photoval.thumbnail_url + '\'); ' +
           'overflow: hidden; background-size: cover; background-position: center center; ' +
           'background-repeat: no-repeat;">' +
           '<div class="top-photos">' +
           '<div class="hover-photos">' +
           '<div><img src="/static/img/spacer.png" class="img-responsive peakeryPhoto photography peakimg-responsive"></div>' +
           '</div>' +
           '<div class="' + photoCaptionClass + '" style="display: none;">' +
           '<span class="data photo-caption" style="position: absolute; bottom: -5px; left: 10px; color: #fff;">' +
           '<p class="bagger" style="white-space: normal; font-size: 10px;">' + photoCaption + '</p>' +
           '<p class="bagger" style="font-size: 10px;">' + photoval.username + '&nbsp;&bull;&nbsp;' +
           '<time class="timeago" datetime="' + photoval.created + 'T00:00:00">' + photoval.created + '</time></p>' +
           '</span>' +
           '</div>' +
           '<div style="' + removePhotoDivStyle + '" data-photoid="' + photoval.photo_id + '" ' +
           'class="admin-delete-photo remove-photo desktop-admin-link hidden-xs hidden-sm hidden-md">' +
           '<i class="fa fa-times"></i>' +
           '</div>' +
           '</div>' +
           '</div>' +
           '</a>';
}

function loadFlickrPhotos(photoIndex) {
    var toIndex = 50;
    var lat = typeof peak !== 'undefined' ? peak.lat : 0;
    var lng = typeof peak !== 'undefined' ? peak.long : 0;

    $.getJSON('https://api.flickr.com/services/rest/?method=flickr.photos.search&api_key=********************************&license=1,2,3,4,5,6,7,8,9,10&has_geo=1&lat=' + lat + '&lon=' + lng + '&radius=.2&radius_units=mi&extras=date_taken%2Cowner_name%2Curl_l&per_page=' + toIndex + '&page=1&format=json&jsoncallback=?', function(data) {
        $.each(data, function(key, val) {
            if (key === 'photos') {
                $.each(val, function(key, val) {
                    if (key === 'photo') {
                        $.each(val, function(photokey, photoval) {
                            if (typeof photoval.url_l !== 'undefined' && photos_displayed < 12) {
                                var flickrPhotoHtml = buildFlickrPhotoHtml(photoval, photoIndex);
                                $('#photos-list').append(flickrPhotoHtml);
                                photoIndex++;
                                photos_displayed++;
                                photos.push(photoval.url_l);
                            }
                        });
                    }
                });
                $("time.timeago").timeago();
            }
        });
    });
}

function buildFlickrPhotoHtml(photoval, photoIndex) {
    var divClass = 'col-lg-3 col-md-3 col-sm-4 col-xs-6';
    var borderClass = 'photo-grid-' + photoIndex.toString();
    var photoCaptionClass = 'user-photo-info peak-photo-with-caption';
    var photoDate = new Date(photoval.datetaken.replace(/-/g, "/"));
    var formattedDate = $.datepicker.formatDate('yy-mm-dd', photoDate);

    return '<a class="gallery-link" ' +
           'data-photo-url="https://www.flickr.com/photos/' + photoval.owner + '/' + photoval.id + '" ' +
           'data-user="' + photoval.ownername + '" data-credit="via Flickr" ' +
           'data-description="' + photoval.title + '" data-gallery ' +
           'href="' + photoval.url_l + '">' +
           '<div class="' + divClass + ' ' + borderClass + '" ' +
           'style="cursor: pointer; padding: 0; background-image: url(\'' + photoval.url_l + '\'); ' +
           'overflow: hidden; background-size: cover; background-position: center center; ' +
           'background-repeat: no-repeat;">' +
           '<div class="top-photos">' +
           '<div class="hover-photos">' +
           '<div><img src="/static/img/spacer.png" class="img-responsive peakeryPhoto photography peakimg-responsive"></div>' +
           '</div>' +
           '<div class="' + photoCaptionClass + '" style="display: none;">' +
           '<span class="data photo-caption" style="position: absolute; bottom: -5px; left: 10px; color: #fff;">' +
           '<p class="bagger" style="white-space: normal; font-size: 10px;">' + photoval.title + '</p>' +
           '<p class="bagger" style="font-size: 10px;">' + photoval.ownername + '&nbsp;&bull;&nbsp;' +
           '<time class="timeago" datetime="' + formattedDate + 'T00:00:00">' + formattedDate + '</time></p>' +
           '</span>' +
           '</div>' +
           '</div>' +
           '</div>' +
           '</a>';
}

// Photo gallery initialization
function initializePhotoGallery() {
    $('#blueimp-gallery').on('open', function(event) {
        $('body,html').css('overflow', 'visible');
    });

    $("#blueimp-gallery").on('slide', function(event, index, slide) {
        var gallery = $('#blueimp-gallery').data('gallery');
        var caption = gallery.list[index].getAttribute('data-description');
        var username = gallery.list[index].getAttribute('data-user');
        var credit = gallery.list[index].getAttribute('data-credit');
        var photoUrl = gallery.list[index].getAttribute('data-photo-url');
        var captionNode = gallery.container.find('.description-text-caption');
        var usernameNode = gallery.container.find('.description-text-user');

        captionNode.empty();
        usernameNode.empty();

        if (caption) {
            captionNode[0].appendChild(document.createTextNode(caption));
        }

        if (username) {
            var newDiv = document.createElement('div');
            if (credit) {
                newDiv.innerHTML = '<a style="color: #fff;" target="_blank" href=' + photoUrl + '"/static">' +
                                 '<i style="margin-right: 5px;" class="fa fa-camera" aria-hidden="true"></i> ' +
                                 username + ' ' + credit + '</a>';
            } else {
                newDiv.innerHTML = '<a style="color: #fff;" href="/members/' + username + '/">' +
                                 '<i style="margin-right: 5px;" class="fa fa-camera" aria-hidden="true"></i> ' +
                                 username + '</a>';
            }
            usernameNode[0].appendChild(newDiv);
        }
    });

    $('#see-more-photos').on('click', function() {
        loadPhotos(photos_page);
    });
}

// Slideshow initialization
function initializeSlideshow() {
    $('#slideshow1').cycle({
        fx: 'fade'
    });
}

// Highlights functionality
function initializeHighlights() {
    if (typeof isUserAuthenticated !== 'undefined' && isUserAuthenticated) {
        $('#edit-highlights-link-div').on('click', 'a', function() {
            $('#highlights-header, #highlights-content').hide();
            $('#edit-highlights-header').fadeIn(300);
            $('#edit-highlights-form').fadeIn(300, function() {
                if (typeof autosize !== 'undefined') {
                    autosize($('.peak-highlight-input'));
                    autosize.update($('.peak-highlight-input'));
                }
            });
            return false;
        });
    } else {
        $('#edit-highlights-link-div').on('click', 'a', function() {
            if ($('#navbar-login-link').is(':visible')) {
                $('#navbar-login-link').click();
            }
            return false;
        });
    }

    $('#edit-highlights-cancel').click(function() {
        resetHighlightsForm();
        return false;
    });

    $('#peak-highlights-fieldset').on('keyup', 'textarea', function() {
        var index = $(this).data('index');
        var numFields = $('.peak-highlight-input').length;

        if (index === numFields && $(this).val().length > 0) {
            var newIndex = index + 1;
            if ($('#peak-highlight-' + newIndex).length === 0) {
                addHighlightField(newIndex);
            }
        }

        if (typeof autosize !== 'undefined') {
            autosize($('.peak-highlight-input'));
            autosize.update($('.peak-highlight-input'));
        }
        return false;
    });

    $('#edit-highlights-save').click(function(event) {
        saveHighlights();
        event.preventDefault();
    });
}

function resetHighlightsForm() {
    $('#peak-highlights-fieldset').empty();
    var newIndex = 1;
    var haveHighlights = false;

    for (var i = 0; i < initial_highlights.length; i++) {
        addHighlightField(newIndex, initial_highlights[i]);
        haveHighlights = true;
        newIndex++;
    }

    addHighlightField(newIndex);

    $('#edit-highlights-header, #edit-highlights-form').hide();
    $('#highlights-header').fadeIn(300);

    if (haveHighlights) {
        $('#highlights-content').fadeIn(300);
        $('#edit-highlights-link').html('edit highlights');
    } else {
        $('#highlights-content').fadeOut(300);
        $('#edit-highlights-link').html('add a highlight!');
    }
}

function addHighlightField(index, value) {
    value = value || '';
    var placeholder = 'write a 1-2 sentence highlight about ' + (typeof peak !== 'undefined' ? peak.name : 'this peak') + '...';

    $('#peak-highlights-fieldset').append(
        '<div><textarea class="peak-highlight-input" name="peak-highlight-' + index + '" ' +
        'data-index="' + index + '" id="peak-highlight-' + index + '" ' +
        'style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" ' +
        'placeholder="' + placeholder + '">' + value + '</textarea></div>'
    );
}

function saveHighlights() {
    var url = "/peaks/edit_highlights/" + peak_id + "/";
    $('#edit-highlights-save').html('<i class="fa fa-spinner fa-spin fa-fw"></i>').prop("disabled", true);

    $.ajax({
        type: "POST",
        url: url,
        data: $("#edithighlights_form").serialize(),
        success: function(data) {
            updateHighlightsUI(data);
        }
    });
}

function updateHighlightsUI(data) {
    $('#peak-highlights-fieldset, #highlights-list').empty();
    var haveHighlights = false;

    $.each(data, function(key, val) {
        if (key === 'highlights') {
            var newIndex = 1;
            initial_highlights = [];

            $.each(val, function(highlightkey, highlightval) {
                haveHighlights = true;
                addHighlightField(newIndex, highlightval);
                $('#highlights-list').append('<li style="list-style: initial; margin-bottom: 20px;">' + highlightval + '</li>');
                initial_highlights.push(highlightval);
                newIndex++;
            });
        }
    });

    addHighlightField(newIndex);

    $('#edit-highlights-link-div').html('<a id="edit-highlights-link" style="cursor: pointer; color: #00B1F2; font-weight: 500;">edit highlights</a>');
    $('#edit-highlights-save').html('Save highlights').prop("disabled", false);
    $('#edit-highlights-header, #edit-highlights-form').hide();
    $('#highlights-header').fadeIn(300);

    if (haveHighlights) {
        $('#highlights-content').fadeIn(300);
        $('#edit-highlights-link').html('edit highlights');
    } else {
        $('#highlights-content').fadeOut(300);
        $('#edit-highlights-link').html('add a highlight!');
    }
}

// Photo upload initialization
function initializePhotoUpload() {
    $('.add-photo').click(function() {
        var windowWidth = $(window).width();
        if (windowWidth < 768) {
            $('.qq-upload-button-selector:eq(1)').find('input').trigger('click');
        } else {
            $('#add-photo').modal('show');
        }
        return false;
    });

    $('#add-photo-close-success, #add-photo-close-error').click(function() {
        $('#add-photo').modal('hide');
        return false;
    });

    $('#add-photo-button').click(function() {
        $('#add-photo-file').trigger('click');
        return false;
    });

    $('#add-photo').on('shown.bs.modal', function(e) {
        $('.loading').hide();
        var windowWidth = $(window).width();
        if (windowWidth >= 768) {
            $('#add-photo-form-container').show();
            $('#add-photo-success-container, #add-photo-error-message-container').hide();
            $('#add-photo-error-message').html('');
        }
        $('.qq-upload-button-text').html('Add jpg peak photo');
        $('.qq-upload-button-selector').find('input').prop('disabled', false);
        $('.qq-upload-list-selector').hide();
        $('.qq-upload-button-selector').show();
    });
}

// Photo removal for admin users
function initializePhotoRemoval() {
    $('#photos-list, #more-photos-list').on('click', '.remove-photo', function(e) {
        var photoId = $(this).data('photoid');
        showConfirmModal('Delete this photo?', photoId, 'confirm-remove-photo');
        return false;
    });

    $('body').on('click', '.confirm-remove-photo', function(e) {
        $('#confirm-modal').modal('hide');
        $('#photos-list').empty();
        var photoId = $(this).data('photoid');

        $.post('/api/delete-photo/', {photo_id: photoId}, function(data) {
            if (data.success) {
                photos_displayed = 0;
                photos_page = 1;
                photos = [];
                loadPhotos(1);
            } else {
                alert('An error occurred deleting this photo, please try again later');
            }
        });
    });
}

// Main photo removal for staff users
function initializeMainPhotoRemoval() {
    $('#remove-main-peak-photo').on('click', function(event) {
        event.preventDefault();
        var peakName = typeof peak !== 'undefined' ? peak.name : 'this peak';

        if (confirm('Are you sure you want to delete the main photo for ' + peakName + '?')) {
            $.post('/api/delete-main-photo/', {peak_id: peak_id}, function(data) {
                if (data.success) {
                    location.reload(true);
                } else {
                    alert('An error occurred when trying to delete this photo, please try again later.');
                }
            });
        }
    });
}

// Window resize handler
function initializeWindowResize() {
    $(window).resize(function() {
        var windowWidth = $(window).width();

        // Handle photo modal visibility
        if (windowWidth < 768) {
            if ($('#mobile-add-photo-error-message').html() === '') {
                $('#add-photo').modal('hide');
            }
        } else {
            if (!uploaderIdle) {
                $('.qq-upload-button-text').html('<i class="fa fa-spinner fa-spin"></i>');
                $('.qq-upload-button-selector').find('input').prop('disabled', true);
                $('.qq-upload-button-text').blur();
                $('#add-photo').modal('show');
            }
        }

        // Update map canvas size
        $("div#map-canvas").height($('#peak-photo-col').height());
        $("div#map-canvas").width($('#peak-photo-col').width());
        updateMapCanvasSize();
    });
}

// Map controls and utilities
function setMapControls() {
    var check = checkIfMapboxStyleIsLoaded();
    if (!check) {
        setTimeout(function() {
            setMapControls();
        }, 200);
        return;
    }

    var mapUnits = readCookie('map_units');
    if (mapUnits === 'meters') {
        toggleMapUnits(mapUnits);
    }
}

function addExtraMapLayers() {
    var check = checkIfMapboxStyleIsLoaded();
    if (!check) {
        setTimeout(function() {
            addExtraMapLayers();
        }, 200);
        return;
    }
    // No extra map layers necessary for this view
}

function updateMapCanvasSize() {
    setTimeout(function() {
        const div = document.getElementById('peak-map-col');
        if (!div) return;

        const minWidth = 1260;
        const minHeight = 945;
        const divWidth = div.offsetWidth;
        const divHeight = div.offsetHeight;

        if (divWidth >= minWidth && divHeight >= minHeight) {
            div.style.backgroundSize = 'cover';
        } else {
            div.style.backgroundSize = minWidth + 'px ' + minHeight + 'px';
        }

        // Center orange peak marker
        var centerMarkerTop = ($('#map-canvas').height() / 2) - 10;
        var centerMarkerLeft = ($('#map-canvas').width() / 2) - 10;
        $('#center-peak-marker').css({
            'top': centerMarkerTop,
            'left': centerMarkerLeft
        });
    }, 100);
}

// Utility functions
function showConfirmModal(title, dataId, confirmClass) {
    $('#confirm-modal-label').html(title);
    $('#confirm-modal-body').html(
        '<div style="margin-top: 20px; text-align: center;">' +
        '<a data-photoid="' + dataId + '" class="btn btn-primary ' + confirmClass + '" style="width: 100px;">Delete</a>' +
        '<button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">Cancel</button>' +
        '</div>'
    );
    $('#confirm-modal').modal('show');
    $('.modal-backdrop').hide();
}

function readCookie(name) {
    var nameEQ = name + "=";
    var ca = document.cookie.split(';');
    for (var i = 0; i < ca.length; i++) {
        var c = ca[i];
        while (c.charAt(0) === ' ') c = c.substring(1, c.length);
        if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
}

// Peak search modal handlers
function initializePeakSearchModal() {
    $('#peak-search').on('hide.bs.modal', function(e) {
        $('#peak-search').removeClass('modal fade right');
    });

    $('#peak-search').on('hidden.bs.modal', function(e) {
        $('#peak-search').attr('style', 'left: 0px; right: auto; width: 240px; position: absolute; display: inline-block;');
        $('#peak-search').addClass('hidden-xs hidden-sm');
        $('#mobile-collapse-nav').hide();
    });
}

// Window resize event listener
window.addEventListener('resize', updateMapCanvasSize);
