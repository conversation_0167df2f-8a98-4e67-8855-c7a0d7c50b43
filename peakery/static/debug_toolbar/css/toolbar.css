/* Debug Toolbar CSS Reset, adapted from <PERSON>'s CSS Reset */
#djDebug {color:#000;background:#FFF;}
#djDebug, #djDebug div, #djDebug span, #djDebug applet, #djDebug object, #djDebug iframe,
#djDebug h1, #djDebug h2, #djDebug h3, #djDebug h4, #djDebug h5, #djDebug h6, #djDebug p, #djDebug blockquote, #djDebug pre,
#djDebug a, #djDebug abbr, #djDebug acronym, #djDebug address, #djDebug big, #djDebug cite, #djDebug code,
#djDebug del, #djDebug dfn, #djDebug em, #djDebug font, #djDebug img, #djDebug ins, #djDebug kbd, #djDebug q, #djDebug s, #djDebug samp,
#djDebug small, #djDebug strike, #djDebug strong, #djDebug sub, #djDebug sup, #djDebug tt, #djDebug var,
#djDebug b, #djDebug u, #djDebug i, #djDebug center,
#djDebug dl, #djDebug dt, #djDebug dd, #djDebug ol, #djDebug ul, #djDebug li,
#djDebug fieldset, #djDebug form, #djDebug label, #djDebug legend,
#djDebug table, #djDebug caption, #djDebug tbody, #djDebug tfoot, #djDebug thead, #djDebug tr, #djDebug th, #djDebug td {
	margin:0;
	padding:0;
	border:0;
	outline:0;
	font-size:12px;
	line-height:1.5em;
	color:#000;
	vertical-align:baseline;
	background:transparent;
	font-family:sans-serif;
	text-align:left;
}

#djDebug #djDebugToolbar {
	background:#111;
	width:200px;
	z-index:100000000;
	position:fixed;
	top:0;
	bottom:0;
	right:0;
	opacity:0.9;
}

#djDebug #djDebugToolbar small {
	color:#999;
}

#djDebug #djDebugToolbar ul {
	margin:0;
	padding:0;
	list-style:none;
}

#djDebug #djDebugToolbar li {
	border-bottom:1px solid #222;
	color:#fff;
	display:block;
	font-weight:bold;
	float:none;
	margin:0;
	padding:0;
	position:relative;
	width:auto;
}

#djDebug #djDebugToolbar li>a,
#djDebug #djDebugToolbar li>div.contentless  {
	font-weight:normal;
	font-style:normal;
	text-decoration:none;
	display:block;
	font-size:16px;
	padding:10px 10px 5px 25px;
	color:#fff;
}

#djDebug #djDebugToolbar li a:hover {
	color:#111;
	background-color:#ffc;
}

#djDebug #djDebugToolbar li.active {
	background-image:url(../img/indicator.png);
	background-repeat:no-repeat;
	background-position:left center;
	background-color:#333;
	padding-left:10px;	
}

#djDebug #djDebugToolbar li.active a:hover {
	color:#b36a60;
	background-color:transparent;
}

#djDebug #djDebugToolbar li small {
	font-size:12px;
	color:#999;
	font-style:normal;
	text-decoration:none;
	font-variant:small-caps;
}

#djDebug #djDebugToolbarHandle {
	position:fixed;
	background:#fff;
	border:1px solid #111;
	top:30px;
	right:0;
	z-index:100000000;
	opacity:0.75;
}

#djDebug a#djShowToolBarButton {
	display:block;
	height:75px;
	width:30px;
	border-right:none;
	border-bottom:4px solid #fff;
	border-top:4px solid #fff;
	border-left:4px solid #fff;
	color:#fff;
	font-size:10px;
	font-weight:bold;
	text-decoration:none;
	text-align:center;
	text-indent:-999999px;
	background:#000 url(../img/djdt_vertical.png) no-repeat left center;
	opacity:0.5;
}

#djDebug a#djShowToolBarButton:hover {
	background-color:#111;
	padding-right:6px;
	border-top-color:#FFE761;
	border-left-color:#FFE761;
	border-bottom-color:#FFE761;
	opacity:1.0;
}

#djDebug code {
	display:block;
	font-family:Consolas, Monaco, "Bitstream Vera Sans Mono", "Lucida Console", monospace;
	white-space:pre;
	overflow:auto;
}

#djDebug tr.djDebugOdd {
	background-color:#f5f5f5;
}

#djDebug .panelContent {
	display:none;
	position:fixed;
	margin:0;
	top:0;
	right:200px;
	bottom:0;
	left:0px;
	background-color:#eee;
	color:#666;
	z-index:100000000;
}

#djDebug .panelContent > div {
	border-bottom:1px solid #ddd;
}

#djDebug .djDebugPanelTitle {
	position:absolute;
	background-color:#ffc;
	color:#666;
	padding-left:20px;
	top:0;
	right:0;
	left:0;
	height:50px;
}

#djDebug .djDebugPanelTitle code {
	display:inline;
	font-size:inherit;
}

#djDebug .djDebugPanelContent {
	position:absolute;
	top:50px;
	right:0;
	bottom:0;
	left:0;
	height:auto;
	padding:0 0 0 20px;
}

#djDebug .djDebugPanelContent .scroll {
	height:100%;
	overflow:auto;
	display:block;
	padding:0 10px 0 0;
}

#djDebug h3 {
	font-size:24px;
	font-weight:normal;
	line-height:50px;
}

#djDebug h4 {
	font-size:20px;
	font-weight:bold;
	margin-top:0.8em;
}

#djDebug .panelContent table {
	border:1px solid #ccc;
	border-collapse:collapse;
	width:100%;
	background-color:#fff;
	display:block;
	margin-top:0.8em;
	overflow: auto;
}
#djDebug .panelContent tbody td,
#djDebug .panelContent tbody th {
	vertical-align:top;
	padding:2px 3px;
}
#djDebug .panelContent thead th {
	padding:1px 6px 1px 3px;
	text-align:left;
	font-weight:bold;
	font-size:14px;
}
#djDebug .panelContent tbody th {
	width:12em;
	text-align:right;
	color:#666;
	padding-right:.5em;
}

#djDebug .djTemplateHideContextDiv {
	background-color:#fff;
}

/*
#djDebug .panelContent p a:hover, #djDebug .panelContent dd a:hover {
	color:#111;
	background-color:#ffc;
}

#djDebug .panelContent p {
	padding:0 5px;
}

#djDebug .panelContent p, #djDebug .panelContent table, #djDebug .panelContent ol, #djDebug .panelContent ul, #djDebug .panelContent dl {
	margin:5px 0 15px;
	background-color:#fff;
}
#djDebug .panelContent table {
	clear:both;
	border:0;
	padding:0;
	margin:0;
	border-collapse:collapse;
	border-spacing:0;
}

#djDebug .panelContent table a {
	color:#000;
	padding:2px 4px;
}
#djDebug .panelContent table a:hover {
	background-color:#ffc;
}

#djDebug .panelContent table th {
	background-color:#333;
	font-weight:bold;
	color:#fff;
	padding:3px 7px 3px;
	text-align:left;
	cursor:pointer;
}
#djDebug .panelContent table td {
	padding:5px 10px;
	font-size:14px;
	background:#fff;
	color:#000;
	vertical-align:top;
	border:0;
}
#djDebug .panelContent table tr.djDebugOdd td {
  background:#eee;
}
*/

#djDebug .panelContent .djDebugClose {
	text-indent:-9999999px;
	display:block;
	position:absolute;
	top:4px;
	right:15px;
	height:40px;
	width:40px;
	background:url(../img/close.png) no-repeat center center;
}

#djDebug .panelContent .djDebugClose:hover {
	background-image:url(../img/close_hover.png);
}

#djDebug .panelContent .djDebugClose.djDebugBack {
	background-image:url(../img/back.png);
}

#djDebug .panelContent .djDebugClose.djDebugBack:hover  {
	background-image:url(../img/back_hover.png);
}

#djDebug .panelContent dt, #djDebug .panelContent dd {
	display:block;
}

#djDebug .panelContent dt {
	margin-top:0.75em;
}

#djDebug .panelContent dd {
	margin-left:10px;
}

#djDebug a.toggleTemplate {
	padding:4px;
	background-color:#bbb;
	-moz-border-radius:3px;
	-webkit-border-radius:3px;
}

#djDebug a.toggleTemplate:hover {
	padding:4px;
	background-color:#444;
	color:#ffe761;
	-moz-border-radius:3px;
	-webkit-border-radius:3px;
}


#djDebug a.djTemplateShowContext, #djDebug a.djTemplateShowContext span.toggleArrow {
	color:#999;
}

#djDebug a.djTemplateShowContext:hover,  #djDebug a.djTemplateShowContext:hover span.toggleArrow {
	color:#000;
	cursor:pointer;
}

#djDebug .djDebugSqlWrap {
	position:relative;
}

#djDebug .djDebugSql {
	z-index:100000002;
}

#djDebug .djSQLHideStacktraceDiv tbody th {
	text-align: left;
}

#djDebug .djSqlExplain td {
	white-space: pre;
}

#djDebug span.djDebugLineChart {
	background-color:#777;
	height:3px;
	position:absolute;
	bottom:0;
	top:0;
	left:0;
	display:block;
	z-index:1000000001;
}
#djDebug span.djDebugLineChartWarning {
	background-color:#900;
}

#djDebug .highlight  { color:#000; }
#djDebug .highlight .err { color:#000; } /* Error */
#djDebug .highlight .g { color:#000; } /* Generic */
#djDebug .highlight .k { color:#000; font-weight:bold } /* Keyword */
#djDebug .highlight .o { color:#000; } /* Operator */
#djDebug .highlight .n { color:#000; } /* Name */
#djDebug .highlight .mi { color:#000; font-weight:bold } /* Literal.Number.Integer */
#djDebug .highlight .l { color:#000; } /* Literal */
#djDebug .highlight .x { color:#000; } /* Other */
#djDebug .highlight .p { color:#000; } /* Punctuation */
#djDebug .highlight .m { color:#000; font-weight:bold } /* Literal.Number */
#djDebug .highlight .s { color:#333 } /* Literal.String */
#djDebug .highlight .w { color:#888888 } /* Text.Whitespace */
#djDebug .highlight .il { color:#000; font-weight:bold } /* Literal.Number.Integer.Long */
#djDebug .highlight .na { color:#333 } /* Name.Attribute */
#djDebug .highlight .nt { color:#000; font-weight:bold } /* Name.Tag */
#djDebug .highlight .nv { color:#333 } /* Name.Variable */
#djDebug .highlight .s2 { color:#333 } /* Literal.String.Double */
#djDebug .highlight .cp { color:#333 } /* Comment.Preproc */
