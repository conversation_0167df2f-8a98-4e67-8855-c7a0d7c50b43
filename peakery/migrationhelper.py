from cities.models import *
from items.models import *
from django.contrib.gis.geos.point import Point

def fix_cities():
	cities = City.objects.filter(location = None)
	for city in cities:
		print("Fixing City: %s" % (city))
		city.location = Point(float(city.location_x),float(city.location_y))
		city.save()


def fix_districts():
	districts = District.objects.filter(location=None)
	for district in districts:
		print("Fixing District: %s" % (district))
		district.location = Point(float(district.location_x),float(district.location_y))
		district.save()

def fix_items():
	items = Item.objects.filter(location=None)
	for item in items:
		print("Fixing Item: %s" % (item))
		item.location = Point(float(item.long),float(item.lat))
		item.save()
