from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.shortcuts import get_object_or_404

class Command(BaseCommand):
    args = ''
    help = 'find duplicated usernames'

    def handle(self,*args,**options):

        users = User.objects.all().order_by('id')
        for user in users:
            duplicates = User.objects.filter(username__iexact=user.username)
            if duplicates:
                for duplicate in duplicates:
                    if duplicate.id != user.id:
                        print user.id, user.username, duplicate.id, duplicate.username
                        duplicate.username = "%s1" % duplicate.username
                        duplicate.username = duplicate.username.lower()
                        duplicate.save()
                        user.username = user.username.lower()
                        user.save()
                        print user.username, duplicate.username