
from django.contrib.auth.models import User
from django.core.management.base import BaseCommand
#from importer.importer import users
from django.db.models.aggregates import Sum

class Command(BaseCommand):
    args = ''
    help = 'the place for the csv file is project_dir/importer/csv/'

    def fix_rank(self,days):
        baggers = User.objects.all()
        baggers = baggers.annotate(Sum('loc_stats__count_%s_unique' % (days)))
        key_exclude ='loc_stats__count_%s_unique'  %  (days)
        exclude = {key_exclude: None}
        baggers = baggers.exclude(**exclude)
        baggers = baggers.order_by('user').distinct()
        baggers = baggers.order_by('-loc_stats__count_%s_unique__sum' % (days), 'date_joined')

        ranking = 1
        #ranking_check = -1
        for bagger in baggers:
            #actual_ranking_check = eval("bagger.loc_stats__count_%s__sum" % (days))
            try:
                profile = bagger.profile
                if days == "all":
                    profile.ranking_all = ranking
                elif days == "365":
                    profile.ranking_365 = ranking
                else:
                    profile.ranking_30 = ranking
                profile.save()
                #if ranking_check != actual_ranking_check:
                #    ranking_check = actual_ranking_check
                ranking = ranking + 1
                self.stdout.write('.')
                self.stdout.flush()
            except:
                self.stdout.write('?')
                pass


        return ranking

    def handle(self,*args,**options):
        from accounts.models import Person
        self.stdout.write('Processing...\n')
        Person.objects.update(ranking_all = None,ranking_365= None,ranking_30= None)
        self.stdout.write('Processing Ranking all:\n')
        last_all = self.fix_rank("all")
        self.stdout.write('Processing Ranking 365:\n')
        last_365 = self.fix_rank("365")
        self.stdout.write('\nProcessing Ranking 30:\n')
        last_30 = self.fix_rank("30")
        Person.objects.filter(ranking_all=None).update(ranking_all=last_all)
        Person.objects.filter(ranking_365=None).update(ranking_365=last_365)
        Person.objects.filter(ranking_30=None).update(ranking_30=last_30)

