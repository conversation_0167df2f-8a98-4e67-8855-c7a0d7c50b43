from django.core.management.base import BaseCommand
import urllib.request
import peakery.settings


class Command(BaseCommand):
    args = ''
    help = 'Cache Top Contributor Pages'

    def handle(self,*args,**options):
        link = "%sapi/contributors/list/?type=all" % peakery.settings.SITE_URL
        print('Caching top contributors', link)
        with urllib.request.urlopen(link):
            print('Caching top contributors', link)

        link = "%sapi/contributors/list/?type=info_corrections" % peakery.settings.SITE_URL
        with urllib.request.urlopen(link):
            print('Caching top contributors', link)

        link = "%sapi/contributors/list/?type=peak_photos" % peakery.settings.SITE_URL
        with urllib.request.urlopen(link):
            print('Caching top contributors', link)

        link = "%sapi/contributors/list/?type=peaks_added" % peakery.settings.SITE_URL
        with urllib.request.urlopen(link):
            print('Caching top contributors', link)

        link = "%sapi/contributors/list/?type=routes_added" % peakery.settings.SITE_URL
        with urllib.request.urlopen(link):
            print('Caching top contributors', link)
