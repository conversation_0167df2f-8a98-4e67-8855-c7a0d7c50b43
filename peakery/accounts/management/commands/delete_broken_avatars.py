
from django.core.management.base import BaseCommand

class Command(BaseCommand):
    args = ''
    help = 'delete broken avatars'

    def handle(self,*args,**options):
        from avatar.models import Avatar
        from django.contrib.auth.models import User
        from django.conf import settings
        import os
        users = User.objects.all().order_by('id')

        for user in users:

            avatar = Avatar.objects.filter(user=user)

            if avatar:

                dir = '%s/avatars/%s/' % (settings.MEDIA_ROOT, user.username)
                if not os.path.exists(dir):
                    print user.username
                    avatar.delete()