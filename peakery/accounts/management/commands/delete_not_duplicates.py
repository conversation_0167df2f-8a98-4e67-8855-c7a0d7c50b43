
from django.core.management.base import BaseCommand

class Command(BaseCommand):
    args = ''
    help = 'delete blank route'

    def handle(self,*args,**options):
        from peakery.items.models import ItemDuplicate
        itemduplicates = ItemDuplicate.objects.filter(distance__gt = 50).order_by('id')

        for itemduplicate in itemduplicates:
            print itemduplicate.id
            if itemduplicate.item.name != itemduplicate.item_dup.name:
                itemduplicate.delete()
