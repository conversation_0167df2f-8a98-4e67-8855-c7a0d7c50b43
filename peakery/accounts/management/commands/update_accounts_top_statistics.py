import datetime
from django.core.management.base import BaseCommand
from peakery.accounts.models import UserTopFiveClimbedCountries, UserTopFiveClimbedRegions, UserTopFiveClimbedRanges, \
    UserTopFiveClimbedCompanions
from django.db import connection, transaction


def update_top_five_climbed_countries():
    print("Updating accounts_usertopfiveclimbedcountries")
    sql = """    
        WITH RankedData AS (
            SELECT 
                x.id AS user_id,
                z.name AS country,
                z.slug,
                SUM(y.count_all_unique) AS peak_count,
                ROW_NUMBER() OVER (PARTITION BY x.id ORDER BY SUM(y.count_all_unique) DESC) AS rank
            FROM 
                auth_user x
            JOIN 
                accounts_userlocationstats_view y ON x.id = y.user_id
            JOIN 
                cities_country z ON y.country_id = z.id
            GROUP BY  
                x.id, z.name, z.slug
        )
        SELECT user_id, country, slug, peak_count
        FROM RankedData
        WHERE rank <= 5
        ORDER BY user_id, peak_count DESC;
    """

    with connection.cursor() as cursor:
        cursor.execute(sql)
        top_five_data = cursor.fetchall()
        columns = [col[0] for col in cursor.description]

    new_records = [
        UserTopFiveClimbedCountries(
            user_id=row[columns.index("user_id")],
            country=row[columns.index("country")],
            slug=row[columns.index("slug")],
            peak_count=row[columns.index("peak_count")]
        )
        for row in top_five_data
    ]

    # Perform database update efficiently
    with transaction.atomic():  # Ensures atomicity
        UserTopFiveClimbedCountries.objects.all().delete()  # Delete old records
        UserTopFiveClimbedCountries.objects.bulk_create(new_records)  # Insert new data in bulk

    print(f"Updated {len(new_records)} records successfully.")


def update_top_five_climbed_regions():
    print("Updating accounts_usertopfiveclimbedregions")
    sql = """              
            WITH RankedData AS (
                SELECT 
                    x.id AS user_id,
                    z.name AS region,
                    zz.slug AS country_slug,
                    z.slug AS region_slug,
                    SUM(y.count_all_unique) AS peak_count,
                    ROW_NUMBER() OVER (PARTITION BY x.id ORDER BY SUM(y.count_all_unique) DESC) AS rank
                FROM 
                    auth_user x
                JOIN 
                    accounts_userlocationstats_view y ON x.id = y.user_id
                JOIN 
                    cities_region z ON y.region_id = z.id
                JOIN 
                    cities_country zz ON z.country_id = zz.id
                GROUP BY  
                    x.id, z.name, zz.slug, z.slug
            )
            SELECT user_id, region, country_slug, region_slug, peak_count
            FROM RankedData
            WHERE rank <= 5
            ORDER BY user_id, peak_count DESC;
        """

    with connection.cursor() as cursor:
        cursor.execute(sql)
        top_five_data = cursor.fetchall()
        columns = [col[0] for col in cursor.description]

    new_records = [
        UserTopFiveClimbedRegions(
            user_id=row[columns.index("user_id")],
            region=row[columns.index("region")],
            country_slug=row[columns.index("country_slug")],
            region_slug=row[columns.index("region_slug")],
            peak_count=row[columns.index("peak_count")]
        )
        for row in top_five_data
    ]

    # Perform database update efficiently
    with transaction.atomic():  # Ensures atomicity
        UserTopFiveClimbedRegions.objects.all().delete()  # Delete old records
        UserTopFiveClimbedRegions.objects.bulk_create(new_records)  # Insert new data in bulk

    print(f"Updated {len(new_records)} records successfully.")


def update_top_five_climbed_ranges():
    print("Updating accounts_usertopfiveclimbedranges")
    sql = """              
            WITH RankedData AS (
                SELECT 
                    x.id AS user_id,
                    z.range,
                    COUNT(DISTINCT z.id) AS peak_count,
                    ROW_NUMBER() OVER (PARTITION BY x.id ORDER BY COUNT(DISTINCT z.id) DESC) AS rank
                FROM 
                    auth_user x
                JOIN 
                    items_summitlog y ON x.id = y.user_id
                JOIN 
                    items_item z ON y.item_id = z.id
                WHERE 
                    y.attempt = FALSE 
                    AND y.status = 1
                    AND LENGTH(z.range) > 0
                GROUP BY 
                    x.id, z.range
            )
            SELECT user_id, range, peak_count
            FROM RankedData
            WHERE rank <= 5
            ORDER BY user_id, peak_count DESC;
        """

    with connection.cursor() as cursor:
        cursor.execute(sql)
        top_five_data = cursor.fetchall()
        columns = [col[0] for col in cursor.description]

    new_records = [
        UserTopFiveClimbedRanges(
            user_id=row[columns.index("user_id")],
            range=row[columns.index("range")],
            peak_count=row[columns.index("peak_count")],
        )
        for row in top_five_data
    ]

    # Perform database update efficiently
    with transaction.atomic():  # Ensures atomicity
        UserTopFiveClimbedRanges.objects.all().delete()  # Delete old records
        UserTopFiveClimbedRanges.objects.bulk_create(new_records)  # Insert new data in bulk

    print(f"Updated {len(new_records)} records successfully.")


def update_top_five_climbed_companions():
    print("Updating accounts_usertopfiveclimbedcompanions")
    sql = """              
            WITH RankedData AS (
                SELECT 
                    a.user_id,
                    c.username,
                    COUNT(a.id) AS peak_count,
                    COALESCE(
                        REPLACE(
                            REPLACE(f.avatar, '/', '/resized/100/'), 
                            'avatars/resized/100/', 
                            'avatars/'
                        ), 
                        'img/default-user.png'
                    ) AS avatar_url,
                    ROW_NUMBER() OVER (PARTITION BY a.user_id ORDER BY COUNT(a.id) DESC) AS rank
                FROM 
                    items_companions a
                JOIN 
                    accounts_userrelation b ON b.id = a.user_relation_id
                JOIN 
                    auth_user c ON c.id = b.to_user_id
                INNER JOIN 
                    avatar_avatar f ON f.user_id = b.to_user_id
                GROUP BY 
                    a.user_id, c.username, f.avatar
            )
            SELECT user_id, username, peak_count, avatar_url
            FROM RankedData
            WHERE rank <= 5
            ORDER BY user_id, peak_count DESC;
        """

    with connection.cursor() as cursor:
        cursor.execute(sql)
        top_five_data = cursor.fetchall()
        columns = [col[0] for col in cursor.description]

    new_records = [
        UserTopFiveClimbedCompanions(
            user_id=row[columns.index("user_id")],
            username=row[columns.index("username")],
            peak_count=row[columns.index("peak_count")],
            avatar_url=row[columns.index("avatar_url")],
        )
        for row in top_five_data
    ]

    # Perform database update efficiently
    with transaction.atomic():  # Ensures atomicity
        UserTopFiveClimbedCompanions.objects.all().delete()  # Delete old records
        UserTopFiveClimbedCompanions.objects.bulk_create(new_records)  # Insert new data in bulk

    print(f"Updated {len(new_records)} records successfully.")


class Command(BaseCommand):
    args = ''
    help = 'Updates several tables related to accounts statistics'

    def handle(self,*args,**options):
        if datetime.datetime.today().weekday() == 5:
            print("Starting update accounts top statistics job")
            update_top_five_climbed_countries()
            update_top_five_climbed_regions()
            update_top_five_climbed_ranges()
            update_top_five_climbed_companions()
            print("Finished update accounts top statistics job")
        else:
            print("Today is not Saturday. Skipping update accounts top statistics job")
