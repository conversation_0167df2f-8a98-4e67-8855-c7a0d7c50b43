
from django.core.management.base import BaseCommand

class Command(BaseCommand):
    args = ''
    help = 'delete user summit dates'

    def handle(self,*args,**options):
        from items.models import SummitLog
        from datetime import date
        start_date = date(2011, 1, 1)
        end_date = date(2011, 4, 5)

        summitlogs = SummitLog.objects.filter(user = 1478, created__range=(start_date,end_date)).order_by('id')

        for summitlog in summitlogs:
            print summitlog.id
            summitlog.date_entered = False
            summitlog.save()