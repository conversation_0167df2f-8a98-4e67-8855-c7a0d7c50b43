from django.core.management.base import BaseCommand
from django.contrib.auth.models import User

class Command(BaseCommand):
    args = ''
    help = 'Make user relations from peakery users'

    def handle(self, *args, **options):
        from items.models import SummitFellowBagger
        from accounts.models import UserRelation
        
        self.stdout.write('Starting process...\n')
        summits = SummitFellowBagger.objects.all()

        for s in summits:
            check_user = User.objects.filter(email=s.email)

            if not check_user:
                check_user = User.objects.filter(username=s.name)

            if check_user:
                check_user = check_user[0]
                s.resolved_relation = True
                self.stdout.write('Created relation between ' + s.summit_log.user.username + ' and ' + check_user.username + '\n')
                UserRelation(from_user=s.summit_log.user, to_user=check_user, source=2, active=True).save()

        self.stdout.write('Done\n')