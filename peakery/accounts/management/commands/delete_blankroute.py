
from django.core.management.base import BaseCommand

class Command(BaseCommand):
    args = ''
    help = 'delete blank route'

    def handle(self,*args,**options):
        from items.models import Item,ItemGroupItem, SummitLog, SummitRoute
        summitlog_list = SummitLog.objects.all()

        for summitlog in summitlog_list:
            print summitlog.id
            if summitlog.route_up_id == 1767:
                print "***************************deleting_blank_route****************************"
                summitlog.route_up_id = None
                summitlog.save()
            if summitlog.route_down_id == 1767:
                print "***************************deleting_blank_route****************************"
                summitlog.route_down_id = None
                summitlog.save()