from django.core.management.base import BaseCommand
from django.contrib.auth.models import User

class Command(BaseCommand):
    args = ''
    help = 'Transfer FellowBaggers to Companions'

    def handle(self, *args, **options):
        from items.models import SummitFellowBagger,Companions
        from accounts.models import UserRelation

        self.stdout.write('Starting process...\n')
        summits = SummitFellowBagger.objects.all()

        for s in summits:
            check_user = User.objects.filter(email=s.email)

            if not check_user:
                check_user = User.objects.filter(username=s.name)
            if check_user:
                check_user = check_user[0]
                s.resolved_relation = True

                userrelation = UserRelation.objects.filter(from_user=s.summit_log.user, to_user=check_user, source=2, active=True)
                if userrelation:
                    userrelation = userrelation[0]
                    self.stdout.write('Created Companion between ' + s.summit_log.user.username + ' and ' + check_user.username + '\n')
                    companion = Companions(summit_log = s.summit_log, user_relation = userrelation, user = s.summit_log.user)
                    companion.save()
            #self.stdout.write(".")

        self.stdout.write('Done\n')