from django.core.management.base import BaseCommand
from django.db import connection
from peakery.utils.utils import dictfetchall
import urllib.request
import peakery.settings

class Command(BaseCommand):
    args = ''
    help = 'Cache Profile Pages'

    def handle(self,*args,**options):

        counter = 1

        sql = "select a.user_id, b.username, count(a.id) as num_summits from items_summitlog a, auth_user b where a.user_id = b.id and a.created > '2016-07-29' group by a.user_id, b.username having count(a.id) > 1"

        with connection.cursor() as cursor:
            cursor.execute(sql)
            users = dictfetchall(cursor)

            for u in users:
                link = "%smembers/%s" % (peakery.settings.SITE_URL, u['username'])
                with urllib.request.urlopen(link):
                    print('%s: Caching user %s' % (counter, u['username']))
                    counter+=1