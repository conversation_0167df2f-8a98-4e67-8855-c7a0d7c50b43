
from django.core.management.base import BaseCommand

class Command(BaseCommand):
    args = ''
    help = 'delete broken avatars'

    def handle(self,*args,**options):
        from avatar.models import Avatar
        from django.contrib.auth.models import User
        from django.conf import settings
        from django.utils.encoding import smart_str
        import os
        import csv
        users = User.objects.all().order_by('id')

        writer = csv.writer(open('static/broken_avatars.csv', 'w'))

        for user in users:

            avatar = Avatar.objects.filter(user=user)

            if avatar:

                dir = '%s/avatars/%s/' % (settings.MEDIA_ROOT, user.username)
                if not os.path.exists(dir):

                    fullname = '%s %s' % (smart_str(user.first_name), smart_str(user.last_name))

                    writer.writerow([user.username, fullname, user.email])
