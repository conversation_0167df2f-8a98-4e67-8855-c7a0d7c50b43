from django.core.management.base import BaseCommand
import datetime
from peakery.accounts.models import UserContributorStats
from django.db import connection
from peakery.utils.utils import dictfetchall


class Command(BaseCommand):
    args = ''
    help = 'Update Contributor Stats Table'

    def handle(self,*args,**options):

        #Peak info corrections
        counter = 1
        last_count = 0
        last_rank = 1

        sql = "select a.id as user_id, count(b.id) as peak_info_corrections from auth_user a, items_itemcorrection b where a.id > 1 and a.id = b.user_id and b.field != 6 group by a.id order by count(b.id) desc"

        with connection.cursor() as cursor:
            cursor.execute(sql)
            peak_info_corrections = dictfetchall(cursor)

        for p in peak_info_corrections:
            date_updated = datetime.datetime.now()
            if p['peak_info_corrections']==last_count:
                rank = last_rank
            else:
                rank = counter
            user_stats = UserContributorStats.objects.filter(user_id=p['user_id'])
            if user_stats:
                sql = "update accounts_usercontributorstats set peak_info_corrections_count = %s, peak_info_corrections_rank = %s, date_updated = %s where user_id = %s"
                with connection.cursor() as cursor:
                    cursor.execute(sql, [p['peak_info_corrections'], rank, date_updated, p['user_id']])
            else:
                sql = "insert into accounts_usercontributorstats (user_id, peak_info_corrections_count, peak_info_corrections_rank, date_updated) values (%s, %s, %s, %s)"
                with connection.cursor() as cursor:
                    cursor.execute(sql, [p['user_id'], p['peak_info_corrections'], rank, date_updated])
            counter+=1
            last_rank = rank
            last_count = p['peak_info_corrections']

        #Peak photos
        counter = 1
        last_count = 0
        last_rank = 1

        sql = "select a.id as user_id, count(b.id) as peak_photos from auth_user a, items_itemphoto b where a.id > 1 and a.id = b.user_id and b.summit_log_id is null group by a.id order by count(b.id) desc"

        with connection.cursor() as cursor:
            cursor.execute(sql)
            peak_photos = dictfetchall(cursor)

        for p in peak_photos:
            date_updated = datetime.datetime.now()
            if p['peak_photos']==last_count:
                rank = last_rank
            else:
                rank = counter
            user_stats = UserContributorStats.objects.filter(user_id=p['user_id'])
            if user_stats:
                sql = "update accounts_usercontributorstats set peak_photos_count = %s, peak_photos_rank = %s, date_updated = %s where user_id = %s"
                with connection.cursor() as cursor:
                    cursor.execute(sql, [p['peak_photos'], rank, date_updated, p['user_id']])
            else:
                sql = "insert into accounts_usercontributorstats (user_id, peak_photos_count, peak_photos_rank, date_updated) values (%s, %s, %s, %s)"
                with connection.cursor() as cursor:
                    cursor.execute(sql, [p['user_id'], p['peak_photos'], rank, date_updated])
            counter+=1
            last_rank = rank
            last_count = p['peak_photos']

        #Peaks added
        counter = 1
        last_count = 0
        last_rank = 1

        sql = "select a.id as user_id, count(b.id) as peaks_added from auth_user a, items_item b where a.id > 1 and a.id = b.user_id group by a.id order by count(b.id) desc"

        with connection.cursor() as cursor:
            cursor.execute(sql)
            peaks_added = dictfetchall(cursor)

        for p in peaks_added:
            date_updated = datetime.datetime.now()
            if p['peaks_added']==last_count:
                rank = last_rank
            else:
                rank = counter
            user_stats = UserContributorStats.objects.filter(user_id=p['user_id'])
            if user_stats:
                sql = "update accounts_usercontributorstats set peak_count = %s, peak_rank = %s, date_updated = %s where user_id = %s"
                with connection.cursor() as cursor:
                    cursor.execute(sql, [p['peaks_added'], rank, date_updated, p['user_id']])
            else:
                sql = "insert into accounts_usercontributorstats (user_id, peak_count, peak_rank, date_updated) values (%s, %s, %s, %s)"
                with connection.cursor() as cursor:
                    cursor.execute(sql, [p['user_id'], p['peaks_added'], rank, date_updated])
            counter+=1
            last_rank = rank
            last_count = p['peaks_added']

        #Summit routes added
        counter = 1
        last_count = 0
        last_rank = 1

        sql = "select a.id as user_id, count(b.id) as summit_routes from auth_user a, items_peakroute b where a.id > 1 and a.id = b.user_id group by a.id order by count(b.id) desc"

        with connection.cursor() as cursor:
            cursor.execute(sql)
            summit_routes = dictfetchall(cursor)

        for p in summit_routes:
            date_updated = datetime.datetime.now()
            if p['summit_routes']==last_count:
                rank = last_rank
            else:
                rank = counter
            user_stats = UserContributorStats.objects.filter(user_id=p['user_id'])
            if user_stats:
                sql = "update accounts_usercontributorstats set summit_routes_count = %s, summit_routes_rank = %s, date_updated = %s where user_id = %s"
                with connection.cursor() as cursor:
                    cursor.execute(sql, [p['summit_routes'], rank, date_updated, p['user_id']])
            else:
                sql = "insert into accounts_usercontributorstats (user_id, summit_routes_count, summit_routes_rank, date_updated) values (%s, %s, %s, %s)"
                with connection.cursor() as cursor:
                    cursor.execute(sql, [p['user_id'], p['summit_routes'], rank, date_updated])
            counter+=1
            last_rank = rank
            last_count = p['summit_routes']