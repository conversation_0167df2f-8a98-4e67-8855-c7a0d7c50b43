
from django.core.management.base import BaseCommand

class Command(BaseCommand):
    args = ''
    help = 'fix tempitems processing'

    def handle(self,*args,**options):
        from items.models import Item, ItemCountry, ItemRegion
        from tempitems.models import TempItem

        tempitems = TempItem.objects.filter(status = 3)

        for tempitem in tempitems:

            completed = False

            print '%s' % tempitem.name

            itemexist = Item.objects.filter(name=tempitem.name, lat=tempitem.lat, long=tempitem.long)
            if not itemexist:
                item = Item(name=tempitem.name,lat=tempitem.lat,long=tempitem.long,elevation=tempitem.elevation,
                            range=tempitem.range,user=tempitem.user)
                item.save()
                completed = True

            saved_items = Item.objects.filter(name=tempitem.name, lat=tempitem.lat, long=tempitem.long)[:1]
            saved_item = saved_items[0]

            for country in tempitem.country.all():
                itemcountryexists = ItemCountry.objects.filter(item=saved_item,country=country)
                if not itemcountryexists:
                    itemcountry = ItemCountry(item=saved_item,country= country)
                    itemcountry.save()
                    completed = True

            for region in tempitem.region.all():
                itemregionexists = ItemRegion.objects.filter(item=saved_item,region=region)
                if not itemregionexists:
                    itemregion  = ItemRegion(item=saved_item,region = region)
                    itemregion.save()
                    completed = True

            if completed:
                saved_item.send_approved_email()
                tempitem.delete()









