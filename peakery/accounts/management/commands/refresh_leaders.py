
from django.contrib.auth.models import User
from django.core.management.base import BaseCommand
#from importer.importer import users
from django.db.models.aggregates import Sum

# @hourly bash -lc 'source /home/<USER>/virtualenvs/peakery-qa.django.pro/bin/activate && cd /home/<USER>/www/peakery-qa.django.pro/appery && python manage.py refresh_leaders && deactivate > /home/<USER>/www/peakery-qa.django.pro/logs/leaders.log'
# @hourly bash -lc 'source /home/<USER>/virtualenvs/peakery.com/bin/activate && cd /home/<USER>/www/peakery.com/appery && python manage.py refresh_leaders && deactivate > /home/<USER>/www/peakery.com/logs/leaders.log'

class Command(BaseCommand):
    args = ''
    help = 'Refresh Leaders Table'

    def handle(self,*args,**options):
        from django.db import connection, transaction
        with connection.cursor() as cursor:

            cursor.execute("SELECT recreate_leaders();")
            transaction.commit_unless_managed()
            self.stdout.write("New Count :%s" % 1)


