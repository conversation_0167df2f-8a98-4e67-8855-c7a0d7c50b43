DROP TABLE IF EXISTS "public"."accounts_userlocationstats";
DROP VIEW IF EXISTS "public"."accounts_userlocationstats_view";
DROP VIEW IF EXISTS "public"."accounts_userlocationstats_view_normal_central";
DROP VIEW IF EXISTS "public"."accounts_userlocationstats_view_unique_central";
DROP VIEW IF EXISTS "public"."accounts_userlocationstats_view_normal";
DROP VIEW IF EXISTS "public"."accounts_userlocationstats_view_unique";

--CREATE VIEW TO HANDLE TOTAL DATA
CREATE VIEW "public"."accounts_userlocationstats_view_normal"
AS
SELECT DISTINCT ON (slog.id) slog.user_id, coalesce(iregion.region_id, 0) as region_id, icountry.country_id,  slog.item_id,
1 AS count_all,
CASE WHEN ((slog.date > (now() - '1 year'::interval)) AND (slog.date_entered = true)) THEN 1 ELSE 0 END
AS count_365,
CASE WHEN ((slog.date > (now() - '30 days'::interval)) AND (slog.date_entered = true)) THEN 1 ELSE 0 END
AS count_30 FROM items_item_country icountry, items_summitlog slog, (items_item item LEFT JOIN items_item_region iregion ON ((iregion.item_id = item.id)))
WHERE ((slog.item_id = item.id) AND (icountry.item_id = item.id))
ORDER BY slog.id;

--CREATE VIEW TO HANDLE UNIQUE DATA
CREATE VIEW "public"."accounts_userlocationstats_view_unique"
AS
SELECT DISTINCT ON (slog.user_id, slog.item_id) slog.id as slog_id, slog.user_id, coalesce(iregion.region_id, 0) as region_id, icountry.country_id, slog.item_id,
1 AS count_all,
CASE WHEN ((slog.date > (now() - '1 year'::interval)) AND (slog.date_entered = true)) THEN 1 ELSE 0 END
AS count_365,
CASE WHEN ((slog.date > (now() - '30 days'::interval)) AND (slog.date_entered = true)) THEN 1 ELSE 0 END
AS count_30 FROM items_item_country
Web: www.invisionworld.coicountry, items_summitlog slog, (items_item item LEFT JOIN items_item_region iregion ON ((iregion.item_id = item.id)))
WHERE ((slog.item_id = item.id) AND (icountry.item_id = item.id))
ORDER BY slog.user_id, slog.item_id;

--CREATE DEFAULT VIEW THAT CONTAINS CENTRALIZED DATA FOR NORMAL
CREATE VIEW "public"."accounts_userlocationstats_view_normal_central"
AS SELECT CASE WHEN (foo.region_id IS NULL) THEN ((((((foo.user_id)::text || '0'::text) || (foo.country_id)::text) || '0'::text) || ((random() * (100)::double precision))::integer))::double precision
ELSE ((((((foo.user_id)::text || '0'::text) || (foo.country_id)::text) || '0'::text) || (foo.region_id)::text))::double precision END
AS id, foo.user_id, foo.country_id, foo.region_id,
sum(foo.count_all) AS count_all,
sum(foo.count_365) AS count_365,
sum(foo.count_30) AS count_30
FROM
accounts_userlocationstats_view_normal foo
GROUP BY foo.user_id, foo.country_id, foo.region_id;

--CREATE DEFAULT VIEW THAT CONTAINS CENTRALIZED DATA FOR UNIQUE
CREATE VIEW "public"."accounts_userlocationstats_view_unique_central"
AS SELECT CASE WHEN (foo.region_id IS NULL) THEN ((((((foo.user_id)::text || '0'::text) || (foo.country_id)::text) || '0'::text) || ((random() * (100)::double precision))::integer))::double precision
ELSE ((((((foo.user_id)::text || '0'::text) || (foo.country_id)::text) || '0'::text) || (foo.region_id)::text))::double precision END
AS id, foo.user_id, foo.country_id, foo.region_id,
sum(foo.count_all) AS count_all,
sum(foo.count_365) AS count_365,
sum(foo.count_30) AS count_30
FROM
accounts_userlocationstats_view_unique foo
GROUP BY foo.user_id, foo.country_id, foo.region_id;

--CREATE DEFAULT VIEW THAT CONTAINS ALL DATA
CREATE VIEW "public"."accounts_userlocationstats_view"
AS SELECT CASE WHEN (foo.region_id IS NULL) THEN ((((((foo.user_id)::text || '0'::text) || (foo.country_id)::text) || '0'::text) || ((random() * (100)::double precision))::integer))::double precision
ELSE ((((((foo.user_id)::text || '0'::text) || (foo.country_id)::text) || '0'::text) || (foo.region_id)::text))::double precision END
AS id, foo.user_id, foo.country_id, foo.region_id,
foo.count_all  AS count_all,
foo.count_365 AS count_365,
foo.count_30 AS count_30,
foo2.count_all AS count_all_unique,
foo2.count_365 AS count_365_unique,
foo2.count_30 AS count_30_unique
FROM
accounts_userlocationstats_view_normal_central foo,
accounts_userlocationstats_view_unique_central foo2
WHERE foo.user_id = foo2.user_id AND foo.country_id = foo2.country_id AND foo.region_id = foo2.region_id;

CREATE OR REPLACE FUNCTION recreate_leaders() RETURNS INT AS $$
DECLARE
	ret INTEGER := 0;
BEGIN
	BEGIN
		drop table accounts_userlocationstats;
	exception WHEN others then

	END;
	BEGIN
		CREATE TABLE accounts_userlocationstats AS SELECT * FROM accounts_userlocationstats_view;
		CREATE UNIQUE INDEX "user_locationstats_index1" ON "public"."accounts_userlocationstats" USING btree(user_id ASC NULLS LAST, country_id ASC NULLS LAST, region_id ASC NULLS LAST);
		COMMENT ON INDEX "public"."user_locationstats_index1" IS NULL;
		CREATE INDEX "user_locationstats_index2" ON "public"."accounts_userlocationstats" USING btree(country_id ASC NULLS LAST);
		COMMENT ON INDEX "public"."user_locationstats_index2" IS NULL;
		CREATE INDEX "user_locationstats_index3" ON "public"."accounts_userlocationstats" USING btree(region_id ASC NULLS LAST);
		COMMENT ON INDEX "public"."user_locationstats_index3" IS NULL;
		CREATE INDEX "user_locationstats_index4" ON "public"."accounts_userlocationstats" USING btree(count_all DESC NULLS LAST);
		COMMENT ON INDEX "public"."user_locationstats_index4" IS NULL;
		CREATE INDEX "user_locationstats_index5" ON "public"."accounts_userlocationstats" USING btree(count_365 DESC NULLS LAST);
		COMMENT ON INDEX "public"."user_locationstats_index5" IS NULL;
		CREATE INDEX "user_locationstats_index6" ON "public"."accounts_userlocationstats" USING btree(count_30 DESC NULLS LAST);
		COMMENT ON INDEX "public"."user_locationstats_index6" IS NULL;
		CREATE INDEX "user_locationstats_index7" ON "public"."accounts_userlocationstats" USING btree(count_all_unique DESC NULLS LAST);
		COMMENT ON INDEX "public"."user_locationstats_index7" IS NULL;
		CREATE INDEX "user_locationstats_index8" ON "public"."accounts_userlocationstats" USING btree(count_365_unique DESC NULLS LAST);
		COMMENT ON INDEX "public"."user_locationstats_index8" IS NULL;
		CREATE INDEX "user_locationstats_index9" ON "public"."accounts_userlocationstats" USING btree(count_30_unique DESC NULLS LAST);
		COMMENT ON INDEX "public"."user_locationstats_index9" IS NULL;
	exception WHEN others then
	END;
	create or replace rule delete_accounts_userlocationstats as ON delete
	to accounts_userlocationstats
	do instead nothing;

	select count(*) as ret into ret from accounts_userlocationstats;
	RETURN ret;
END;
$$ LANGUAGE 'plpgsql';