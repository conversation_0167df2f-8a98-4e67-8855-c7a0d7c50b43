from django.conf import settings
import urllib.request

# dictfectall for cursors
def dictfetchall(cursor):
    "Return all rows from a cursor as a dict"
    columns = [col[0] for col in cursor.description]
    return [
        dict(zip(columns, row))
        for row in cursor.fetchall()
    ]


# update member profile page cache
def update_member_profile_cache(user):
    link = "%smembers/%s" % (settings.SITE_URL, user.username)
    with urllib.request.urlopen(link):
        print("profile cache updated for", user)
