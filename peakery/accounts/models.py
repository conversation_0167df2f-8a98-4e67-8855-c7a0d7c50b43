import calendar
from avatar.models import Avatar
from django.contrib.sites.models import Site
from peakery.accounts.managers import <PERSON><PERSON>anager, UserLocationStatsManager
from peakery.cities.models import City, Region, Country
from django.contrib.auth.models import User
from django.contrib.gis.db import models
from peakery.django_extensions.db.fields import CreationDateTimeField, ModificationDateTimeField
from peakery.cache import cache_manager
from django.db.models import Max
from operator import itemgetter
from django.template.loader import render_to_string
from django.urls import reverse
from django.utils.http import quote
import datetime
from django.conf import settings
from peakery.follow.models import Follow
from django.db import connection

def dictfetchall(cursor):
    "Return all rows from a cursor as a dict"
    columns = [col[0] for col in cursor.description]
    return [
        dict(zip(columns, row))
        for row in cursor.fetchall()
    ]




class Person(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    about_me = models.TextField(null = True, blank = True)
    website = models.URLField(null = True, blank = True)
    location_city = models.ForeignKey(City, null = True, blank = True, related_name='people_city', on_delete=models.DO_NOTHING)
    location_region = models.ForeignKey(Region,null=True,blank=True,related_name='people_region', on_delete=models.DO_NOTHING)
    location_country = models.ForeignKey(Country,null=True,blank=True,related_name='people_country', on_delete=models.DO_NOTHING)
    location = models.PointField(blank = True,default = None,null=True)
    location_name = models.CharField(max_length=255, null=True, blank=True)
    favorite_item = models.URLField(null=True, blank=True)
    next_item_goal = models.URLField(null = True, blank = True)
    last_summit_date = models.DateField(null=True,blank=True)
    ranking_all = models.IntegerField(null=True, blank=True)
    ranking_365 = models.IntegerField(null=True,blank=True)
    ranking_30 = models.IntegerField(null=True,blank=True)
    facebook_page = models.URLField(null=True, blank=True)
    twitter_username = models.CharField(max_length=50, null=True, blank=True)
    flicker_page = models.URLField(null=True, blank=True)
    instagram_username = models.CharField(max_length=50, null=True, blank=True)
    location_area_1 = models.CharField(max_length=255, null=True, blank=True)
    location_area_2 = models.CharField(max_length=255, null=True, blank=True)

    was_edited_info = models.BooleanField(default=False)

    latest_contribution = models.DateTimeField(null = True, blank = True)
    old_id = models.IntegerField(null = True, blank = True)

    #Audit Fields
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()

    #Signup source
    signup_source = models.CharField(max_length=255, null=True, blank=True)

    firebase_uid = models.CharField(max_length=50, null=True, blank=True)

    objects = PersonManager()
    
    class Meta:
        verbose_name_plural = 'people'
    
    def __unicode__(self):
        return "%s" % self.user

    def __str__(self):
        return "%s" % self.user

    def get_absolute_url(self):
        domain = Site.objects.get_current().domain
        return 'http://%s%s' % (domain, reverse('user_profile', args=[self.user.username]))

    def get_absolute_path(self):
        return reverse('user_profile', args=[self.user.username])

    def fix_user_stat_rank(self):
        ranking_30 = Person.objects.aggregate(ranking_30 = Max('ranking_30'))['ranking_30']
        ranking_365 = Person.objects.aggregate(ranking_365 = Max('ranking_365'))['ranking_365']
        ranking_all = Person.objects.aggregate(ranking_all = Max('ranking_all'))['ranking_all']
        self.ranking_30 = ranking_30
        self.ranking_365 = ranking_365
        self.ranking_all = ranking_all
        self.save()

    def send_welcome_email(self):
        from django.core.mail import send_mail
        from django.utils.html import strip_tags
        self.fix_user_stat_rank()
        user_profile_url = self.get_absolute_url()
        subject = 'welcome to peakery!'
        html_message = render_to_string('accounts/mails/welcome.html', {'user':self.user, 'user_profile_url':user_profile_url, 's3_url':settings.S3_URL, 'site_url':settings.SITE_URL})
        plain_message = strip_tags(html_message)
        #email_message(subject, body, to=[self.user.email], from_email=settings.DEFAULT_FROM_EMAIL)
        send_mail(
            subject,
            plain_message,
            settings.DEFAULT_FROM_EMAIL,
            [self.user.email],
            html_message=html_message,
            fail_silently=False,
        )


    def fix_last_summit_date(self):
        try:
            last_summit_date = self.user.summit_log.select_related('item').all().filter(date_entered=True).order_by('-date')[0].date
        except:
            last_summit_date = None
        self.last_summit_date = last_summit_date
        self.save()

    def summits(self):
        return self.user.summit_log.select_related('item').all().order_by('-date')

    def lastest_summit(self):
        return self.user.summit_log.select_related('item').all().order_by('-date')[:1]

    def lastest_summit_from_list(self, list):
        items_id = list.items.all().values_list('id', flat=True)
        return self.user.summit_log.select_related('item').filter(item__id__in = items_id).order_by('-date')[:1]

    def summits_5(self):
        return self.user.summit_log.select_related('item').all().order_by('-date')[0:5]

    def summits_10(self):
        return self.user.summit_log.select_related('item').all().order_by('-date')[0:10]

    def summits_18(self):
        return self.user.summit_log.select_related('item').all().order_by('-date')[0:18]

    def summit_badges(self):
        items_list = set()
        summits_logs = list()

        summits = self.user.summit_log.all().order_by('-date')

        for s in summits:
            if not s.item in items_list:
                items_list.add(s.item)
                summits_logs.append(s)

        return summits_logs

    def summits_18_items(self):
        if self.summit_badges():
            return self.summit_badges()[0:18]
    
    def new_peak_badges_count(self):
        return len(self.summit_badges())
    
    def summited(self, item):
        return self.user.summit_log.filter(item=item).exists()
    property = property(summited)

    def peaks_bagged_count(self):
        return self.user.summit_log.all().count()

    #rewriting follow stuff because it doesn't work
    def is_user_following(self, user):
        sql = "select a.id from follow_follow a where user_id = %s and follower_id = %s"
        follower = len(list(Follow.objects.raw(sql, [self.user_id, user.id])))
        if follower > 0:
            return True
        else:
            return False

    def is_following_user(self, user):
        sql = "select a.id from follow_follow a where user_id = %s and follower_id = %s"
        follower = len(list(Follow.objects.raw(sql, [user.id, self.user_id])))
        if follower > 0:
            return True
        else:
            return False

    def does_follow_user(self, user):
        sql = "select a.id from follow_follow a where user_id = %s and follower_id = %s"
        follower = len(list(Follow.objects.raw(sql, [user.id, self.user_id])))
        if follower > 0:
            return True
        else:
            return False

    def get_followers_for_user(self):
        sql = "select a.id, a.username, e.location_name, coalesce(replace(replace(c.avatar, '/', '/resized/100/'), 'avatars/resized/100/', 'avatars/'),'img/default-user.png') as avatar_url, count(d.id) as summit_count " + \
            "from auth_user a " + \
            "join follow_follow b on b.follower_id = a.id and b.user_id = %s " + \
            "left join avatar_avatar c on c.user_id = b.follower_id  " + \
            "join items_summitlog d on d.user_id = b.follower_id " + \
            "join accounts_person e on e.user_id = b.follower_id " + \
            "group by a.id, a.username, e.location_name, c.avatar "
        followers = User.objects.raw(sql, [self.user_id])

        return followers

    def get_everything_user_follows(self):
        sql = "select a.id, a.username, e.location_name, coalesce(replace(replace(c.avatar, '/', '/resized/100/'), 'avatars/resized/100/', 'avatars/'),'img/default-user.png') as avatar_url, count(d.id) as summit_count " + \
            "from auth_user a " + \
            "join follow_follow b on b.user_id = a.id and b.follower_id = %s " + \
            "left join avatar_avatar c on c.user_id = a.id  " + \
            "join items_summitlog d on d.user_id = a.id " + \
            "join accounts_person e on e.user_id = a.id " + \
            "group by a.id, a.username, e.location_name, c.avatar "
        followings = User.objects.raw(sql, [self.user_id])

        return followings

    #new user profile page stats
    def get_profile_stats(self):

        sql = "select a.id, count(distinct b.item_id) as peak_count from auth_user a, items_summitlog b where a.id = %s and a.id = b.user_id and b.attempt = false and b.status = 1 group by a.id"
        with connection.cursor() as cursor:
            cursor.execute(sql, [self.user.id])
            if cursor.rowcount > 0:
                peaks_bagged = dictfetchall(cursor)[0]
                peaks_bagged_count = peaks_bagged['peak_count']
            else:
                peaks_bagged_count = 0

        sql = "select a.id, a.username, coalesce(ST_Y(b.location),0) as location_x, coalesce(ST_X(b.location),0) as location_y from auth_user a, accounts_person b where a.id = %s and a.id = b.user_id "
        basecamp = list(User.objects.raw(sql, [self.user.id]))
        if basecamp:
            basecamp_x = basecamp[0].location_x
            basecamp_y = basecamp[0].location_y
        else:
            basecamp_x = '0'
            basecamp_y = '0'

        sql = "select count(a.id) as total_summits from items_summitlog a where a.user_id = %s and a.attempt = False and status = 1 "
        with connection.cursor() as cursor:
            cursor.execute(sql, [self.user.id])
            if cursor.rowcount > 0:
                result = dictfetchall(cursor)[0]
                total_summits = result['total_summits']
            else:
                total_summits = 0

        sql = "select count(a.id) as total_attempts from items_summitlog a where a.user_id = %s and a.attempt = True and status = 1 "
        with connection.cursor() as cursor:
            cursor.execute(sql, [self.user.id])
            if cursor.rowcount > 0:
                result = dictfetchall(cursor)[0]
                total_attempts = result['total_attempts']
            else:
                total_attempts = 0

        sql = "select count(a.id) as photos_count from items_itemphoto a where a.item_id > 0 and a.user_id = %s "
        with connection.cursor() as cursor:
            cursor.execute(sql, [self.user.id])
            if cursor.rowcount > 0:
                result = dictfetchall(cursor)[0]
                total_photos = result['photos_count']
            else:
                total_photos = 0

        return {'peaks_bagged': peaks_bagged_count, 'total_summits': total_summits, 'total_attempts': total_attempts, 'total_photos': total_photos, 'basecamp_x': basecamp_x, 'basecamp_y': basecamp_y}

    def peaks_bagged_rank_all(self):
        sql = "select a.id, count(distinct b.item_id) as peak_count from auth_user a, items_summitlog b where a.id = %s and a.id = b.user_id and b.attempt = false and b.status = 1 group by a.id"
        with connection.cursor() as cursor:
            cursor.execute(sql, [self.user.id])
            if cursor.rowcount > 0:
                peaks_bagged = dictfetchall(cursor)[0]
                peaks_bagged_count = peaks_bagged['peak_count']
            else:
                peaks_bagged_count = 0

        sql = "select a.id, count(distinct b.item_id) from auth_user a, items_summitlog b where a.id = b.user_id and b.attempt = false and b.status = 1 group by a.id having count(distinct b.item_id) > %s"
        return len(list(User.objects.raw(sql, [peaks_bagged_count]))) + 1

    def peaks_bagged_count_year(self):
        from datetime import datetime
        current_year = 'Jan 1 %s' % datetime.now().strftime('%Y')
        year_summits = self.user.summit_log.filter(date__gte=datetime.strptime(current_year, '%b %d %Y')).count()
        return year_summits

    def peaks_bagged_count_year_2011(self):
        from datetime import date
        start_date = date(2011, 1, 1)
        end_date = date(2011, 12, 31)
        year_summits = self.user.summit_log.filter(date__range=(start_date,end_date), date_entered = True).count()
        return year_summits

    def peaks_bagged_count_specific_year(self, year):
        from datetime import date
        start_date = date(year, 1, 1)
        end_date = date(year, 12, 31)
        year_summits = self.user.summit_log.filter(date__range=(start_date,end_date), date_entered = True).count()
        return year_summits

    def peaks_bagged_count_unique(self):
        count_qs = self.user.summit_log.all().values('item').distinct()
        peaks_bagged = count_qs.count()
        return peaks_bagged

    def peaks_bagged_count_unique_year(self):
        from datetime import datetime
        current_year = 'Jan 1 %s' % datetime.now().strftime('%Y')
        count_qs = self.user.summit_log.filter(date__gte=datetime.strptime(current_year, '%b %d %Y')).values('item').distinct()
        peaks_bagged_year = count_qs.count()
        return peaks_bagged_year

    def peaks_bagged_count_unique_year_2011(self):
        from datetime import date
        start_date = date(2011, 1, 1)
        end_date = date(2011, 12, 31)
        count_qs = self.user.summit_log.filter(date__range=(start_date,end_date), date_entered = True).values('item').distinct()
        peaks_bagged_year = count_qs.count()
        return peaks_bagged_year

    def peaks_bagged_count_unique_specific_year(self, year):
        from datetime import date
        start_date = date(year, 1, 1)
        end_date = date(year, 12, 31)
        count_qs = self.user.summit_log.filter(date__range=(start_date,end_date), date_entered = True).values('item').distinct()
        peaks_bagged_year = count_qs.count()
        return peaks_bagged_year

    def peaks_bagged_count_unique_month(self):
        from datetime import datetime

        days = [day for day in calendar.Calendar().itermonthdays(2011, datetime.now().month)]
        temp_days = list()

        for d in days:
            if not d == 0:
                temp_days.append(d)

        month_range = [temp_days[0], temp_days[len(temp_days) - 1]]

        current_month_start = datetime(datetime.now().year, datetime.now().month, month_range[0])
        current_month_end = datetime(datetime.now().year, datetime.now().month, month_range[1])

        month_summits = self.user.summit_log.filter(date__gte=current_month_start, date__lte=current_month_end).order_by('date')
        peaks_bagged_month = month_summits.count()

        return peaks_bagged_month

    def photos_added_count(self):
        return self.user.item_photos.select_related('item').all().count()

    def photos_added(self):
        return self.user.item_photos.select_related('item').all().order_by('-created')

    def days_since_last_summit(self):
        logs = self.user.summit_log.filter(status=1).order_by('-date')
        if logs:
            diff = datetime.date.today() - logs[0].date
            return diff.days
        else:
            return -1

    def days_since_last_summit_display(self):
        logs = self.user.summit_log.filter(status=1).order_by('-date')
        if logs:
            diff = datetime.date.today() - logs[0].date
            return '%s days since last summit' % diff.days
        else:
            return 'no summits yet'

    def get_last_summit(self):
        logs = self.user.summit_log.filter(status=1).order_by('-date')[:1]
        if logs:
            return logs[0]
        else:
            return None

    def highest_peak_bagged(self):
        summit = self.user.summit_log.select_related('item').annotate(Max('item__elevation')).order_by('-item__elevation__max')
        if summit:
            item = summit[0].item
            return item
        return False

    def most_prominent_peak_bagged(self):
        summit = self.user.summit_log.select_related('item').annotate(Max('item__prominence')).order_by('-item__prominence__max')
        if summit:
            item = summit[0].item
            return item
        return False

    def get_climbing_regions(self):
        region_list = []
        summits = self.user.summit_log.select_related('item').all()
        if summits:
            for s in summits:
                if s.item.has_region:
                    regions = s.item.region.all()
                    for r in regions:
                        if region_list.count(r.id) == 0:
                            region_list.append(r.id)
        return region_list

    def top_climbing_regions(self):
        regions_id = self.get_climbing_regions()
        if regions_id:
            result = []
            regions = Region.objects.select_related('country').filter(id__in=regions_id)
            for region in regions:
                count = self.user.summit_log.filter(item__region=region).count()
                result.append( (region, count) )
            result = sorted(result, key=itemgetter(1), reverse=True)
            return result[:5]
        return False


    def top_companions(self):
        companions = UserRelation.objects.raw('select i.user_relation_id as id,count(*) as count from items_companions i where user_id = %s group by i.user_relation_id order by count desc limit 5', [self.user.id])
        return companions

    def latest_comments(self):
        return self.user.item_comments.all().order_by('-created')[:5]

    def get_lists(self):
        from peakery.items.models import ItemGroup
        username = self.user.username
        cache_val = cache_manager.get_peak_list_for_user(username)
        if cache_val:
            return cache_val
        bagged_ids = [p.get('item') for p in self.user.summit_log.all().values('item')]
        groups = ItemGroup.objects.filter(items__id__in = bagged_ids).distinct()
        item_lists = []
        for g in groups:
            total, bagged, percent = g.get_user_bagging_stats(bagged_ids)
            """ group object, total, bagged items, percent progress """
            item_lists.append( (g, total, bagged, percent ) )
        item_lists = sorted(item_lists, key=itemgetter(3), reverse=True)
        cache_manager.set_peak_list_for_user(username, item_lists)
        return item_lists

    def get_favorite_item(self):
        if self.favorite_item:
            return self.get_item_from_url(self.favorite_item)
        return False

    def get_next_item_goal(self):
        if self.next_item_goal:
            return self.get_item_from_url(self.next_item_goal)
        return False

    def get_item_from_url(self, url):
        from peakery.items.models import Item
        list = url.split('/') # split the url
        parts = [value for value in list if value != ''] # remove all '' from list
        slug = parts[len(parts)-1] # the last index is a item slug
        try:
            item = Item.objects.get(slug_new_text=slug)
            return item
        except Item.DoesNotExist:
            pass
        return False

    def get_lists_completed(self):
        lists = self.get_lists()
        #print lists
        completed = []
        if lists:
            for i in lists:
                if i[3] > 99:
                    completed.append(i)
        return completed


    def get_list(self, list):
        from peakery.items.models import ItemGroup
        bagged_ids = [p.get('item') for p in self.user.summit_log.all().values('item')]
        groups = ItemGroup.objects.filter(items__id__in = bagged_ids, id = list.id).distinct()
        item_lists = []
        for g in groups:
            total, bagged, percent = g.get_user_bagging_stats(bagged_ids)
            """ group object, total, bagged items, percent progress """
            item_lists.append( (g, total, bagged, percent ) )
        item_lists = sorted(item_lists, key=itemgetter(3), reverse=True)
        return item_lists

    def get_list_stats(self, list):
        lists = self.get_list(list)
        for l in lists:
            if l[0].id == list.id:
                return l
        return []

    def user_items(self, order=None):
        #summits = self.user.summit_log.select_related('item').all() DOES NOT HIT THE ITEM_LOCATION_POINT TYPE :S
        if order is not None:
            summits = self.user.summit_log.all().order_by(order).only('item')
        else:
            summits = self.user.summit_log.all().only('item')
        count = summits.count()
        #summits = SummitLog.objects.raw('select * from items_summitlog, items_item where items_item.id = items_summitlog.item_id and items_summitlog.user_id = %i' % self.id)
        items = []
        for s in summits:
            items.append(s.item)
        return count, items

    def get_items_summited(self):
        item_list = []
        summits = self.user.summit_log.select_related('item').all()
        if summits:
            for s in summits:
                if item_list.count(s.item.id) == 0:
                    item_list.append(s.item.id)
        return item_list

    def get_most_claimed_item(self):
        summits = self.user.summit_log.all()
        summits_set = set()
        for summit in summits:
            summits_set.add(summit)
        items = [[x.summit_times(),x ]for x in summits_set]
        items.sort(key=itemgetter(0),reverse=True)
        item = None
        if items:
            item = items[0][1]
        return item

    def get_closest_item(self):
        item_list = []
        sql = "select a.id, c.id as item_id, c.name, " + \
            "ACOS( SIN(c.lat*PI()/180)*SIN(ST_Y(a.location)*PI()/180) + COS(c.lat*PI()/180)*COS(ST_Y(a.location)*PI()/180)*COS(ST_X(a.location)*PI()/180-c.long*PI()/180) ) * 6371000 as distance_in_meters " + \
            "from accounts_person a, items_item c " + \
            "where a.user_id = %s " + \
            "and not exists (select 1 from items_summitlog x where x.item_id = c.id and x.user_id = %s) " + \
            "order by distance_in_meters asc limit 1 "
        user_closest_item = User.objects.raw(sql, [self.user_id, self.user_id])

        for i in user_closest_item:
            if item_list.count(i.item_id) == 0:
                    item_list.append(i.item_id)
        return item_list

    def get_closest_item_not_summited(self):
        item_list = []

        sql = "select a.id, c.id as item_id, c.name, " + \
            "ACOS( SIN(c.lat*PI()/180)*SIN(ST_Y(a.location)*PI()/180) + COS(c.lat*PI()/180)*COS(ST_Y(a.location)*PI()/180)*COS(ST_X(a.location)*PI()/180-c.long*PI()/180) ) * 6371000 as distance_in_meters " + \
            "from accounts_person a, items_item c " + \
            "where a.user_id = %s " + \
            "and c.summitlog_count = 0 " + \
            "order by distance_in_meters asc limit 1 "
        user_closest_item = User.objects.raw(sql, [self.user_id])

        for i in user_closest_item:
            if item_list.count(i.item_id) == 0:
                    item_list.append(i.item_id)
        return item_list

    def get_completion_radius_for_user(self):
        result = 0
        if self.location:
            sql = "select a.id, c.id as item_id, c.name, " + \
                "ST_DistanceSphere(st_makepoint(%s, %s),st_makepoint(c.long, c.lat)) as distance_in_meters " + \
                "from accounts_person a, items_item c " + \
                "where a.user_id = %s " + \
                "and not exists (select 1 from items_summitlog x where x.item_id = c.id and x.user_id = %s) " + \
                "order by ST_SetSRID(ST_MakePoint(%s, %s), 4326) <#> c.location limit 1 "
            user_closest_item = User.objects.raw(sql, [self.location.x, self.location.y, self.user_id, self.user_id, self.location.x, self.location.y])
            for i in user_closest_item:
                result = i.distance_in_meters
        return result

    def contributor_stats(self):

        peak_info_corrections_count = 0
        peak_info_corrections_rank = 0
        peak_photos_count = 0
        peak_photos_rank = 0
        peak_count = 0
        peak_rank = 0
        summit_routes_count = 0
        summit_routes_rank = 0

        stats = UserContributorStats.objects.filter(user_id=self.user_id)
        if stats:
            for s in stats:
                if s.peak_info_corrections_count:
                    peak_info_corrections_count = s.peak_info_corrections_count
                if s.peak_info_corrections_rank:
                    peak_info_corrections_rank = s.peak_info_corrections_rank
                if s.peak_photos_count:
                    peak_photos_count = s.peak_photos_count
                if s.peak_photos_rank:
                    peak_photos_rank = s.peak_photos_rank
                if s.peak_count:
                    peak_count = s.peak_count
                if s.peak_rank:
                    peak_rank = s.peak_rank
                if s.summit_routes_count:
                    summit_routes_count = s.summit_routes_count
                if s.summit_routes_rank:
                    summit_routes_rank = s.summit_routes_rank

        return {'peak_info_corrections_count': peak_info_corrections_count, 'peak_info_corrections_rank': peak_info_corrections_rank, 'peak_photos_count': peak_photos_count, 'peak_photos_rank': peak_photos_rank, 'peak_count': peak_count, 'peak_rank': peak_rank, 'summit_routes_count': summit_routes_count, 'summit_routes_rank': summit_routes_rank}

    def edit_stats(self):

        sql = "select a.peak_photos_count, b.peaks_added_count, c.peak_locations_count, d.peak_info_count from " + \
            "(select count(a.id) as peak_photos_count from items_itemcorrection a where a.field = 6 and a.user_id = %s) a, " + \
            "(select count(a.id) as peaks_added_count from items_item a where a.user_id = %s) b, " + \
            "(select count(distinct a.item_id) as peak_locations_count from items_itemcorrection a where a.user_id = %s and a.field = 2 and a.status = 2) c, " + \
            "(select count(a.id) as peak_info_count from items_itemcorrection a where a.user_id = %s and a.field != 2 and a.status = 2) d "
        with connection.cursor() as cursor:
            cursor.execute(sql, [self.user_id, self.user_id, self.user_id, self.user_id])
            result = dictfetchall(cursor)

        peak_locations_count = result[0]['peak_locations_count']
        peak_photos_count = result[0]['peak_photos_count']
        peak_info_count = result[0]['peak_info_count']
        peaks_added_count = result[0]['peaks_added_count']

        sql = "select a.peak_photos_count, b.peaks_added_count, c.peak_locations_count, d.peak_info_count from " + \
              "(select count(a.id) as peak_photos_count from items_itemcorrection a where a.field = 6 and a.created >= now() - interval '1 week') a, " + \
              "(select count(a.id) as peaks_added_count from items_item a where a.created >= now() - interval '1 week') b, " + \
              "(select count(distinct a.item_id) as peak_locations_count from items_itemcorrection a where a.created >= now() - interval '1 week' and a.field = 2 and a.status = 2) c, " + \
              "(select count(a.id) as peak_info_count from items_itemcorrection a where a.created >= now() - interval '1 week' and a.field != 2 and a.status = 2) d "
        with connection.cursor() as cursor:
            cursor.execute(sql)
            result = dictfetchall(cursor)

        all_locations_count = result[0]['peak_locations_count']
        all_photos_count = result[0]['peak_photos_count']
        all_info_count = result[0]['peak_info_count']
        all_added_count = result[0]['peaks_added_count']

        return {'peak_locations_count': peak_locations_count, 'peak_photos_count': peak_photos_count, 'peak_info_count': peak_info_count, 'peaks_added_count': peaks_added_count, 'all_locations_count': all_locations_count, 'all_photos_count': all_photos_count, 'all_info_count': all_info_count, 'all_added_count': all_added_count}

    def has_avatar(self):
        return Avatar.objects.filter(user=self.user).exists()

    def avatar_url(self):
        import urllib.parse
        avatars = Avatar.objects.filter(user=self.user)

        if avatars:
            for a in avatars:
                path_list = str(a.avatar).split("/")
                resized_path = path_list[0]+"/"+path_list[1]+"/resized/400/"+path_list[2]
            return "%s%s" % (settings.MEDIA_URL, urllib.parse.quote(resized_path))
        else:
            return "%simg/default-user.png" % settings.MEDIA_URL

    def show_on_the_web(self):
        show = False
        if self.twitter_username or self.website or self.facebook_page or self.flicker_page:
            show = True
        return show

    def get_location_name(self):
        if self.location_name:
            return quote(self.location_name)

    def get_location(self):
        return False
    def get_html_tooltip(self):
        return render_to_string('accounts/ajax/tooltip.html', {'i':self})

    def get_user_news_count(self):
        sql = "select count(a.id) as news_count from notification_notice a, notification_noticetype b where a.recipient_id = %s and a.unseen is true and a.on_site is true and a.notice_type_id = b.id and b.display_type = 'you' "
        with connection.cursor() as cursor:
            cursor.execute(sql, [self.user_id])
            result = dictfetchall(cursor)

        ncount = result[0]['news_count']
        #ncount = Notice.objects.notices_for(self.user,unseen=True).count()
        if ncount > 99:
            ncount = 99
        return ncount

    def refresh_latest_cache_for_user(self):
        cache_manager.delete_mobile_summits_user_json(self.user_id, 'imperial')
        cache_manager.delete_mobile_summits_user_json(self.user_id, 'metric')


class UserLocationStats(models.Model):
    user = models.ForeignKey(User, editable=False,related_name='loc_stats', on_delete=models.CASCADE)
    country = models.ForeignKey(Country, on_delete=models.DO_NOTHING)
    region = models.ForeignKey(Region,blank=True,null=True, on_delete=models.DO_NOTHING)
    count_all = models.IntegerField()
    count_365 = models.IntegerField()
    count_30 = models.IntegerField()
    count_all_unique = models.IntegerField()
    count_365_unique = models.IntegerField()
    count_30_unique = models.IntegerField()

    objects = UserLocationStatsManager()

    #class Meta:
    #    abstract = True
    def __unicode__(self):
        try:
            return u'User %s: Bagged %s in %s' % (self.user,self.count_all,  self.region)
        except:
            return u'User %s: Bagged %s in %s' % (self.user,self.count_all,  self.country)

    def save(self):
        pass

class UserContributorStats(models.Model):
    user = models.ForeignKey(User, editable=False,related_name='contributor_stats', on_delete=models.CASCADE)
    peak_info_corrections_count = models.IntegerField()
    peak_info_corrections_rank = models.IntegerField()
    peak_photos_count = models.IntegerField()
    peak_photos_rank = models.IntegerField()
    peak_count = models.IntegerField()
    peak_rank = models.IntegerField()
    summit_routes_count = models.IntegerField()
    summit_routes_rank = models.IntegerField()
    date_updated = ModificationDateTimeField()

    def __unicode__(self):
        return "%s Contributor Stats" % self.user.username

GMAIL_SOURCE = 0
PEAKERY_SOURCE = 1
INPUT_SOURCE = 2

SOURCE_CHOICES = (
    (GMAIL_SOURCE, 'Gmail'),
    (PEAKERY_SOURCE, 'Peakery'),
    (INPUT_SOURCE, 'Input')
)

class UserRelation(models.Model):
    from_user = models.ForeignKey(User, null=False, blank=False, on_delete=models.CASCADE)
    to_user = models.ForeignKey(User, related_name='relations', null=True, blank=True,default=None, on_delete=models.CASCADE)
    to_uid = models.CharField(max_length=255, null=True, blank=True)
    first_name = models.CharField(max_length=100, null=True, blank=True)
    last_name = models.CharField(max_length=100, null=True, blank=True)
    email_db = models.EmailField(null=True,blank= True)
    source = models.SmallIntegerField(choices=SOURCE_CHOICES, null=False, blank=False)
    active = models.BooleanField(default=True)

    def get_email(self):
        if self.to_user:
            email = self.to_user.email
        else:
            email = self.email_db
        return email

    def set_email(self,value):
        self.email_db = value

    email = property(get_email, set_email)

    def __unicode__(self):
        if self.to_user :
            message = u'relation from: %s to %s' % (self.from_user,self.to_user)
        else:
            message = u'relation from: %s to %s' %( self.from_user,self.first_name)
        return message

    @staticmethod
    def check_relation(from_user, to_user, email, to_uid):
        if to_user:
            exists = UserRelation.objects.filter(from_user=from_user, to_user=to_user)
        elif email:
            exists = UserRelation.objects.filter(from_user= from_user, email_db = email)
        elif to_uid:
            exists = UserRelation.objects.filter(from_user= from_user, to_uid=to_uid)

        if exists:
            return exists[0]
        else:
            return None

    def save(self, *args, **kwargs):
        if not self.id:
            exists = UserRelation.check_relation(self.from_user, self.to_user, self.email, self.to_uid)
            if exists:
                if not exists.active:
                    exists.active = True
                    exists.save()
            else:
                super(UserRelation, self).save(*args, **kwargs)
        else:
            super(UserRelation, self).save(*args, **kwargs)

    def disable_relation(self):
        self.active = False
        self.save()

    def get_relation_box(self):
        user_relation = {}

        user_relation["id"] = self.id

        if self.source == PEAKERY_SOURCE:
            user_relation["username"] = self.to_user.username
            user_relation["full_name"] = self.to_user.first_name + " " + self.to_user.last_name
            if user_relation["full_name"] == " ":
                user_relation["full_name"] = ""
            try:
                user_relation["avatar_url"] = self.to_user.profile.avatar_url()
            except:
                pass
        elif self.source == GMAIL_SOURCE:
            user_relation["username"] = self.email
            user_relation["full_name"] = self.first_name
            user_relation["avatar_url"] = settings.GMAIL_AVATAR
        elif self.source == INPUT_SOURCE:
            user_relation["username"] = self.email
            user_relation["full_name"] = self.first_name
            user_relation["avatar_url"] = settings.INPUT_AVATAR

        return render_to_string('relation_box.html', {'user_relation':user_relation, })


class UserTopFiveClimbedCountries(models.Model):
    user_id = models.IntegerField(null=False, blank=False, db_index=True)
    country = models.CharField(max_length=50, null=False, blank=False)
    slug = models.CharField(max_length=50, null=False, blank=False)
    peak_count = models.IntegerField(null=False, blank=False)
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()


class UserTopFiveClimbedRegions(models.Model):
    user_id = models.IntegerField(null=False, blank=False, db_index=True)
    region = models.CharField(max_length=50, null=False, blank=False)
    country_slug = models.CharField(max_length=50, null=False, blank=False)
    region_slug = models.CharField(max_length=50, null=False, blank=False)
    peak_count = models.IntegerField(null=False, blank=False)
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()


class UserTopFiveClimbedRanges(models.Model):
    user_id = models.IntegerField(null=False, blank=False, db_index=True)
    range = models.CharField(max_length=300, null=False, blank=False)
    peak_count = models.IntegerField(null=False, blank=False)
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()


class UserTopFiveClimbedCompanions(models.Model):
    user_id = models.IntegerField(null=False, blank=False, db_index=True)
    username = models.CharField(max_length=50, null=False, blank=False)
    peak_count = models.IntegerField(null=False, blank=False)
    avatar_url = models.CharField(max_length=500, null=False, blank=False)
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()
