import re
from django.forms.widgets import PasswordInput
from peakery.accounts.models import Person
from django.contrib.auth.models import User
from django.contrib.auth.forms import UserCreationForm, PasswordResetForm
from django import forms
from django.forms import ModelForm
from django.forms import widgets
from django.core.exceptions import ValidationError
from django.contrib.auth.tokens import default_token_generator
from django.contrib.sites.shortcuts import get_current_site
from django.contrib.auth import get_user_model
from django.utils.http import urlsafe_base64_encode
from django.utils.encoding import force_bytes
from django.template import loader


class ExtendedUserCreationForm(UserCreationForm):
    email = forms.EmailField(label="Email", max_length=100, widget=forms.TextInput(attrs={'class':'signup_email'}),
        error_messages = {'invalid': "This value is not valid a email."})
    #captcha = recaptcha.Recaptcha<PERSON>ield()
    first_name = forms.CharField(required=True)
    last_name = forms.CharField(required=True)
    username = forms.CharField(required=False)

    class Meta:
        model = User
        fields = ("username","first_name", "last_name")

    def clean_email(self):
        if User.objects.filter(email__iexact=self.cleaned_data['email']):
            raise forms.ValidationError(u'Email already tied to a peakery account.')
        return self.cleaned_data['email']

    def clean_username(self):
        username = self.cleaned_data["username"]
        return username
#        raise forms.ValidationError(_("A user with that username already exists."))

class UserForm(ModelForm):
    class Meta:
        model = User
        fields = ('username', 'email',)

    def clean_username(self):
        username = self.cleaned_data['username']
        if not re.match("^[A-Za-z0-9-]*$", username):
            raise forms.ValidationError(u'Only allowed letters, numbers and dashes')
        # exists = User.objects.filter(username__iexact= username)
        # if exists:
        #     raise forms.ValidationError(u'Username already exists')

        return self.cleaned_data['username']

class PersonUserNameForm(ModelForm):
    class Meta:
        model = User
        fields = ('username',)

    def clean_username(self):
        username = self.cleaned_data['username']
        if len(username) > 30:
            raise forms.ValidationError(u'Username cannot exceed 30 characters')
        if not re.match("^[A-Za-z0-9-]*$", username):
            raise forms.ValidationError(u'Only allowed letters, numbers and dashes')
        exists = User.objects.filter(username__iexact= username)
        if exists:
            raise forms.ValidationError(u'Username already exists')

        return self.cleaned_data['username']

    
class PersonForm(ModelForm):
    class Meta:
        model = Person
        fields = ('about_me','website','favorite_item', 'next_item_goal','facebook_page','twitter_username','flicker_page', 'location_name')
        
class ChangePasswordForm(forms.Form):
    new_password = forms.CharField(widget=PasswordInput(), required=False)

class ImportContactsForm(forms.Form):
    email = forms.EmailField()
    password = forms.CharField(widget=widgets.PasswordInput)

class EmailValidationOnForgotPassword(PasswordResetForm):
    def clean_email(self):
        email = self.cleaned_data['email']
        if not User.objects.filter(email__iexact=email, is_active=True).exists():
            raise ValidationError("There is no user registered with the specified email address!")

        return email

    def save(self, domain_override=None,
             subject_template_name='registration/password_reset_subject.txt',
             email_template_name='registration/password_reset_email.html',
             use_https=False, token_generator=default_token_generator,
             from_email=None, request=None):
        """
        Generates a one-use only link for resetting password and sends to the
        user.
        """
        from django.core.mail import send_mail
        UserModel = get_user_model()
        email = self.cleaned_data["email"]
        active_users = UserModel._default_manager.filter(
            email__iexact=email, is_active=True)
        for user in active_users:
            if not domain_override:
                current_site = get_current_site(request)
                site_name = current_site.name
                domain = current_site.domain
            else:
                site_name = domain = domain_override
            c = {
                'email': user.email,
                'domain': domain,
                'site_name': site_name,
                'uid': urlsafe_base64_encode(force_bytes(user.pk)),
                'user': user,
                'token': token_generator.make_token(user),
                'protocol': 'https' if use_https else 'http',
            }
            subject = loader.render_to_string(subject_template_name, c)
            # Email subject *must not* contain newlines
            subject = ''.join(subject.splitlines())
            email = loader.render_to_string(email_template_name, c)
            send_mail(subject, email, from_email, [user.email])