from django.urls import path, re_path
from django.views.decorators.csrf import csrf_exempt
import django.contrib.auth.views
from django.views.generic import TemplateView
from peakery.accounts.forms import EmailValidationOnForgotPassword

from peakery.accounts.views import user_profile, login, login_and_reload, logout, register, change_password, login_user_lite, edit as user_edit
from peakery.accounts.views import upload_avatar, s3_profile_photo_upload, s3_profile_photo_init, sign_up_welcome
from peakery.accounts.views import sign_up_choose_name, sign_up_follow_other_users
from peakery.accounts.views import user_followers, user_followings, get_more_user_followers, get_more_user_followings
from peakery.accounts.views import get_profile_follow, follow_user, unfollow_user, password_reset_done

urlpatterns = [
    path('', user_profile, name='user_profile'),
    path('login/', login, name='login'),
    re_path(r'^login_reload/(?P<user>[-@.\w]+)/$',login_and_reload ,name='login_and_reload'),
    path('logout/', logout, name='logout'),
    path('register/', csrf_exempt(register), name='register'),
    path('change-password/', change_password, name='change_password'),
    #url(r'^news/$','user_news',name='user_news'),
    #url(r'^news/(?P<filter>following|you|all)/$','user_news',name='user_news'),


    path('login-user-lite/', login_user_lite, name='login_user_lite'),
    path('password_reset/', django.contrib.auth.views.PasswordResetView.as_view(), {'template_name':'accounts/reset_password_form.html', 'email_template_name':'accounts/reset_password_email.html', 'subject_template_name':'accounts/reset_password_subject.txt', 'from_email':'peakery <<EMAIL>>', 'password_reset_form': EmailValidationOnForgotPassword}, name='password-reset'),
    path('password_reset/done/', password_reset_done, name='password_reset_done'),
    #url(r'^reset/(?P<uidb36>[0-9A-Za-z]+)-(?P<token>.+)/$', django.contrib.auth.views.password_reset_confirm, {'template_name':'accounts/reset_password_confirm.html'}),
    re_path(r'^reset/(?P<uidb64>[0-9A-Za-z]+)-(?P<token>.+)/$', django.contrib.auth.views.PasswordResetConfirmView.as_view(), {'template_name':'accounts/reset_password_confirm.html', 'post_reset_redirect' : '/accounts/reset/done/'}, name='password_reset_confirm'),
    path('reset/done/', django.contrib.auth.views.PasswordResetCompleteView.as_view(), {'template_name':'accounts/reset_password_complete.html'}, name='password_reset_complete'),
    path('edit/', csrf_exempt(user_edit), name='user_edit'),
    path('upload_avatar/', csrf_exempt(upload_avatar), name='upload_avatar'),
    path('s3_profile_photo_upload/', csrf_exempt(s3_profile_photo_upload), name='s3_profile_photo_upload'),
    path('s3_profile_photo_init/', s3_profile_photo_init, name='s3_profile_photo_init'),
    #url(r'^sign-up/$', direct_to_template,{'template':'accounts/sign_up.html'}, name="sign_up"),
    path('sign-up/', TemplateView.as_view(template_name="accounts/sign_up.html")),
    path('sign-up-content/', TemplateView.as_view(template_name="accounts/sign_up_content.html")),
    #url(r'^sign-up-email/$', direct_to_template,{'template':'accounts/sign_up_with_email.html'}, name="sign_up_email"),
    path('sign-up-email/', TemplateView.as_view(template_name="accounts/sign_up_with_email.html")),
    path('sign-up-welcome/', sign_up_welcome, name="sign_up_welcome"),
    path('sign-up-choose-name/<int:step>/', csrf_exempt(sign_up_choose_name), name='sign_up_choose_name'),
    path('sign-up-follow-other-users/', sign_up_follow_other_users, name="sign_up_follow_other_users"),
    path('followers/<int:user_id>/', user_followers, name='user_followers'),
    path('followings/<int:user_id>/', user_followings, name='user_followings'),
    path('more-followers/<int:user_id>/', get_more_user_followers, name='get_more_user_followers'),
    path('more-followings/<int:user_id>/', get_more_user_followings, name='get_more_user_followings'),
    path('profile_follow/<int:user_id>/', get_profile_follow,name='profile_follow'),#, bagger.username')

    path('follow/<int:user_id>/', csrf_exempt(follow_user), name='follow_user'),
    path('unfollow/<int:user_id>/', csrf_exempt(unfollow_user), name='unfollow_user'),
]