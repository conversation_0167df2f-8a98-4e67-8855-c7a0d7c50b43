from django import template
from peakery.follow.models import Follow
register = template.Library()

@register.tag
def is_following(parser, token):
    print("is_following accounts tag")
    """
        {% is_following <user1> <user2> %}
    """
    parts = token.split_contents()
    if len(parts) < 3:
        raise template.TemplateSyntaxError("'is_following' tag must be of the form:  {% is_following <user1> <user2> %}")
    return IsFollowingNode(parts[1], parts[2])


class IsFollowingNode(template.Node):
    def __init__(self, var1, var2):
        self.var1 = var1
        self.var2 = var2

    def render(self, context):
        try:
            user1 = template.Variable(self.var1).resolve(context)
            user2 = template.Variable(self.var2).resolve(context)
            
            if Follow.objects.is_user_following(user1, user2):
                context['is_following'] = True
            else:
                context['is_following'] = False
        except template.VariableDoesNotExist:
            context['is_following'] = False
        return u""