#from easy_thumbnails.files import get_thumbnailer
from peakery.items.models import Item
from peakery.accounts.forms import ExtendedUserCreationForm
from datetime import datetime,timedelta

from django import template
register = template.Library()

@register.inclusion_tag('accounts/register.html')
def register_form(action, request, generate_id=''):
    form = ExtendedUserCreationForm()
    return {'form':form, 'action':action, 'generated_id':generate_id, 'request':request}

#@register.inclusion_tag('accounts/avatar.html')
#def avatar(user, sizex=80, sizey=80, option=''):
#    crop = False
#    if option == 'crop':
#        crop = True
#    thumbnailer = get_thumbnailer(user.get_profile().avatar)
#    url = thumbnailer.get_thumbnail( {'size':(sizex, sizey), 'crop':crop} )
#    return {'url':url, 'media_url':settings.MEDIA_URL}

def user_summited(user, item_id):
    peak = Item.objects.get(id=item_id)
    return user.profile.summited(peak)
register.simple_tag(user_summited)

@register.inclusion_tag('items/ajax/peakbaggers_summits.html')
def summits_filter(user,timefilter,country_id=None,region_id=None):
    summits = user.summit_log.select_related('item')
    if timefilter not in ['all']:
        summits = summits.filter(date_entered = True)
        timespan = datetime.now() - timedelta(days = int(timefilter))
        summits = summits.filter(date__gt = timespan)
    if region_id:
        summits = summits.filter(item__region = region_id)
    elif country_id:
        summits = summits.filter(item__country = country_id)
    return {'summits':summits[0:10]}

@register.filter
def members_rank_page(rank):
    try:
        page_number = int(rank) / 20
        page_number = page_number + 1
        return page_number
    except (ValueError, ZeroDivisionError):
        return None