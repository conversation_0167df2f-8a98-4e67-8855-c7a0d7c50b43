{% extends "base.html" %}
{% load static %}
{% block css %}
    <link href="{{MEDIA_URL}}css/view-peak.css?v=1" rel="stylesheet" type="text/css" media="screen" />
    <link href="{{MEDIA_URL}}css/lightboxes/django-follow.css" rel="stylesheet" type="text/css" media="screen" />
    <link href="{{MEDIA_URL}}css/jquery.lightbox-0.5.css" rel="stylesheet" type="text/css" media="screen" />
    <link href="{{MEDIA_URL}}css/jquery.lightbox-0.5.css" rel="stylesheet" type="text/css" media="screen" />
    <link href="{{MEDIA_URL}}css/tipsy.css" rel="stylesheet" type="text/css" media="screen" />
{% endblock %}

{% load avatar_tags %}
{% block title %}{{ bagger }}'s challenges{% endblock %}
{% block titlemeta %}{{ bagger }}'s challenges{% endblock %}
{% block description %}{{ bagger }}'s progress in {{ total_count }} Peak Challenge{{ total_count|pluralize:"s" }} with details on peaks completed/remaining, season grid, and month grid{% endblock %}
{% block image_rel %}{% avatar_url bagger 135 %}{% endblock %}

{% block extrajs %}
    <script type="text/javascript" src="{% static 'js/jquery.lightbox-0.5.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/jquery.tipsy.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/handlebars.1.0.0.beta.3.js' %}"></script>
{% endblock %}

{% block profile_active %}{% if user == bagger %}active{% endif %}{% endblock %}

{% block mobile_header_follow %}
{% if user.is_authenticated %}{% if user.username|stringformat:"s" != bagger|stringformat:"s" %}<a href="javascript:void(0);" id="mobile-follow-{{ bagger.id }}" style="font-size: 11px; font-weight: 300; margin-left: 15px; {% if following %}display: none;{% else %}display: inline;{% endif %}">follow</a><a href="javascript:void(0);" id="mobile-unfollow-{{ bagger.id }}" style="font-size: 11px; font-weight: 300; margin-left: 15px; color: #00b330; {% if following %}display: inline;{% else %}display: none;{% endif %}">following</a>{% else %}<a href="/members/{{ bagger }}/edit/" style="font-size: 11px; font-weight: 300; margin-left: 15px; {% if following %}display: none;{% else %}display: inline;{% endif %}">edit</a>{% endif %}{% endif %}
{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row hidden-xs">
        <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 65px; border-radius: 12px 12px 0 0;"><span style="font-size: 24px; font-weight: 600;">
            <div id="breadcrumbs" style="margin-left: -5px;">
                <ul>
                    <li style="margin-top: 2px;"><a class="modal-link" data-toggle="modal" data-target="#avatar-modal"><img style="height: 50px; width: 50px; border-radius:10px;" src="{{ MEDIA_URL }}{{ avatar.avatar_url|urlencode }}"></a><div style="float: right; margin-left: 15px; font-weight: 600; font-size: 20px;">{{ bagger }}{% if user.is_authenticated %}{% if user.username|stringformat:"s" == bagger|stringformat:"s" %}<a class="profile-edit-link hidden-xs" style="font-size: 12px; margin-left: 10px;" href="/members/{{ bagger }}/edit/">edit</a>{% endif %}{% endif %}</div></li>
                </ul>
                <div class="pull-right hidden-xs" style="margin-top: -5px;">
                    <a style="{{ subnav_info_style }}" class="region-header-sub-links" href="/members/{{ bagger }}/">Info</a><a style="{{ subnav_map_style }}" class="region-header-sub-links" href="/members/{{ bagger }}/map/">Map</a><a style="{{ subnav_badges_style }}" class="region-header-sub-links" href="/members/{{ bagger }}/badges/">Badges</a><a style="{{ subnav_summits_style }}" class="region-header-sub-links" href="/members/{{ bagger }}/summits/">Climbs</a><a style="{{ subnav_challenges_style }}" class="region-header-sub-links" href="/members/{{ bagger }}/challenges/">Challenges</a><a style="{{ subnav_p_style }}" class="region-header-sub-links" href="/members/{{ bagger }}/photos/">Photos</a>{% if user.is_authenticated %}{% if user.username|stringformat:"s" != bagger|stringformat:"s" %}<div class="region-header-sub-links hidden-xs hidden-sm" style="float: right; margin-top: 3px;"><a href="javascript:void(0);" id="follow-{{ bagger.id }}" class="peakeryFollowButton followButton btn btn-secondary" style="font-size: 12px; float: right; width: 165px; {% if following %}display: none;{% else %}display: block;{% endif %}">Follow</a><a href="javascript:void(0);" id="unfollow-{{ bagger.id }}" class="peakeryFollowButton btn btn-secondary unfollowButton" style="font-size: 12px; float: right; width: 165px; background-color: rgb(0, 179, 48); border-color: rgb(0, 179, 48); {% if following %}display: block;{% else %}display: none;{% endif %}">You are following</a></div>{% endif %}{% endif %}
                </div>
            </div>
        </span></div>
    </div>
{% endblock %}

{% block content %}

<style>
   @media screen and (max-width: 767px) and (min-width: 1px) {
       #content-body {
           margin-top: 20px;
       }
       .content-pane {
           margin-top: 99px;
       }
       .mobile-regions-subnav-fixed {
           margin-top: 50px;
       }
       #selectMobileChallengefilter {
           margin-top: 7px;
       }
       .challenge-divider {
            height: 40px;
        }
       .season-peak-name {
            width: 500px;
        }
       .filter-bar {
           top: 120px;
       }
   }
   @media screen and (max-width: 1023px) and (min-width: 768px) {
       .content-pane {
           margin-top: 49px;
       }
       #selectMobileChallengefilter {
           margin-top: 7px;
       }
       .challenge-divider {
            height: 40px;
        }
       .mobile-regions-subnav-fixed {
           margin-top: 71px;
       }
       .filter-bar {
            top: 141px;
        }
   }
   @media screen and (max-width: 820px) and (min-width: 768px) {
       .season-peak-name {
            width: 140px;
        }
   }
   @media screen and (max-width: 870px) and (min-width: 821px) {
       .season-peak-name {
            width: 200px;
        }
   }
   @media screen and (max-width: 920px) and (min-width: 871px) {
       .season-peak-name {
            width: 250px;
        }
   }
   @media screen and (max-width: 1023px) and (min-width: 921px) {
       .season-peak-name {
            width: 290px;
        }
   }
    @media screen and (min-width: 1024px) {
        .content-pane {
           margin-top: 49px;
       }
        .mobile-regions-subnav-fixed {
           margin-top: 0px;
       }
        #selectMobileChallengefilter {
           margin-top: 0px;
       }
        .challenge-divider {
            height: 40px;
        }
        .user-challenge-peak-season-on {
            width: 150px !important;
        }
        .regions-subnav-fixed {
            top: 141px;
        }
    }
    @media screen and (min-width: 1024px) {
        .user-challenge-season-table > thead > tr > th {
            height: 60px;
            vertical-align: middle;
            font-size: 14px;
        }
    }
    @media screen and (min-width: 1px) and (max-width: 1023px) {
        .user-challenge-season-table > thead > tr > th {
            height: 60px;
            vertical-align: middle;
            font-size: 11px;
        }
    }
    @media screen and (min-width: 1px) and (max-width: 1220px) {
        .month-peak-name {
            width: 130px !important;
        }
    }
    @media screen and (min-width: 1221px) and (max-width: 1240px) {
        .month-peak-name {
            width: 150px !important;
        }
    }
    @media screen and (min-width: 1241px) and (max-width: 1260px) {
        .month-peak-name {
            width: 170px !important;
        }
    }
    @media screen and (min-width: 1261px) {
        .month-peak-name {
            width: 190px !important;
        }
    }

    @media screen and (min-width: 1024px) and (max-width: 1050px) {
        .season-peak-name {
            width: 170px !important;
        }
    }
    @media screen and (min-width: 1051px) and (max-width: 1100px) {
        .season-peak-name {
            width: 220px !important;
        }
    }
    @media screen and (min-width: 1101px) and (max-width: 1150px) {
        .season-peak-name {
            width: 270px !important;
        }
    }
    @media screen and (min-width: 1151px) and (max-width: 1200px) {
        .season-peak-name {
            width: 320px !important;
        }
    }
    @media screen and (min-width: 1201px) and (max-width: 1250px) {
        .season-peak-name {
            width: 370px !important;
        }
    }
    @media screen and (min-width: 1251px) {
        .season-peak-name {
            width: 400px !important;
        }
    }
</style>

<div class="container">

    <!-- Mobile header -->
    <div class="row sub-header-row hidden-lg hidden-md hidden-sm" style="height: 50px; position: fixed; width: 100%; z-index: 2; top: 70px;">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999; display: flex; justify-content: space-between;">
            <a style="{{ subnav_info_style }}" class="mobile-header-sub-links" href="/members/{{ bagger }}/">Info</a>{% if IS_MOBILE_APP_ACCESS != 'True' %}<a style="{{ subnav_map_style }}" class="mobile-header-sub-links" href="/members/{{ bagger }}/map/">Map</a>{% endif %}<a style="{{ subnav_badges_style }}" class="mobile-header-sub-links" href="/members/{{ bagger }}/badges/">Badges</a><a style="{{ subnav_summits_style }}" class="mobile-header-sub-links" href="/members/{{ bagger }}/summits/">Climbs</a><a style="{{ subnav_challenges_style }}" class="mobile-header-sub-links" href="/members/{{ bagger }}/challenges/">Challenges</a><a style="{{ subnav_p_style }}" class="mobile-header-sub-links" href="/members/{{ bagger }}/photos/">Photos</a>
        </div>
    </div>
    <!-- End mobile header -->

    <div class="row sub-header-row hidden-xs hidden-sm regions-subnav-fixed" style="height: 50px; padding-right: 0px;">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999;">
            <span style="font-size: 14px; float: left;"><span id="challenge-count" style="color: #f24100;">{{ total_count }}</span> <span id="challenge-count-dscr">challenge{{ total_count|pluralize:"s" }}</span> in</span>
            <span class="select" id="selectRegionFilter" style="float: left; margin-top: 4px;">
                <button class="btn btn-default region-filter-button" style="margin-bottom: 0px; background-color: #f2f2f2; border-color: #f2f2f2; padding: 8px 8px; font-size: 15px;" data-toggle="dropdown">
                    <span id="regionfilter-title" style="color: #00B1F2;">all regions</span>
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu regionfilter-list" style="height: auto; left: inherit; overflow: scroll; max-height: 70vh;">
                    <li style="float: none;"><a class="regionfilter-item" data-type="" data-count="{{ total_count }}" data-value="" href="#">all regions</a></li>
                    {% for r in regions %}
                    <li style="float: none;"><a id="regionfilter-{{ r.type }}-{{ r.id }}" class="regionfilter-item" data-type="{{ r.type }}" data-count="{{ r.challenge_count }}" data-value="{{ r.id }}" href="#">{{ r.region_name }} &bull; {{ r.challenge_count }} challenge{{ r.challenge_count|pluralize:"s" }}</a></li>
                    {% endfor %}
                </ul>
            </span>
            <span class="pull-right">Sort by:<a style="margin-left: 10px; color: rgb(153, 153, 153); font-weight: 300;" id="sort-pct-complete" class="region-header-sort-links ajax-link" onclick="loadChallenges(challengeRegion, challengeRegionType, 'pct_complete', '1');">% Completed</a><a style="margin-left: 35px; color: rgb(153, 153, 153); font-weight: 300;" id="sort-num-peaks-summited" class="region-header-sort-links ajax-link" onclick="loadChallenges(challengeRegion, challengeRegionType, 'num_peaks_summited', '1');"># Peaks climbed</a><a style="margin-left: 35px; color: rgb(153, 153, 153); font-weight: 300;" id="sort-most-recent-summit" class="region-header-sort-links ajax-link" onclick="loadChallenges(challengeRegion, challengeRegionType, 'most_recent_summit', '1');">Most recent climb</a></span>
        </div>
    </div>

    <div class="row content-pane" style="border-bottom: none;">

        <div class="col-md-12">
            <div class="row sub-header-row hidden-md hidden-lg filter-bar" style="height: 50px; padding-right: 0px;">
                <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12" style="height: 50px;line-height: 26px;">
                    <div style="float: left; width: 50%; height: 100%; border-right: solid 1px #cfcfcf;">
                        <div class="select" id="selectMobileRegionfilter" style="margin-top: 11px; margin-left: 5px;">
                            <select id="mobileRegionfilterSelect" style="width: 96%; -webkit-appearance: none; border: 0; background: none; color: #00b1f2;">
                                <option value="" data-type="">All regions</option>
                                {% for r in regions %}
                                <option value="{{ r.id }}" data-type="{{ r.type }}">{{ r.region_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div style="float: left; margin-left: 10px; width: 45%;">
                        <div class="select" id="selectMobileSortChallenges" style="margin-top: 11px; margin-left: 5px;">
                            <select id="mobileSortChallengesSelect" style="width: 96%; -webkit-appearance: none; border: 0; background: none; color: #00b1f2;">
                                <option value="pct_complete">% Completed</option>
                                <option value="num_peaks_summited"># Peaks summited</option>
                                <option value="most_recent_summit">Most recent summit</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-12">
            <div class="row" id="ajax-data-loading" style="display: none;">
              <div class="col-md-12 data-loading" style="text-align: center; color: #666;">
                <i class="fa fa-spinner fa-spin fa-5x"></i>
              </div>
            </div>
            <div class="row" id="challenges-list">
            </div>
        </div>
    </div>
    <div class="row dark-background-row hidden-lg hidden-md hidden-sm">
        <div class="row bottom-padding">
            <div style="height: 178px;"></div>
        </div>
    </div>
    <div class="row dark-background-row hidden-xs">
        <div class="row bottom-padding">
            <div style="height: 53px;"></div>
        </div>
    </div>
    <div id="marker-tooltip" data-url="" data-index="" style="cursor: pointer; display: none; position:fixed; width: 200px; height: 150px; background: rgba(0, 0, 0, 0.3); margin: 15px;"></div>
</div>

<script type="text/javascript">

    // Hide mobile filter bar on on scroll down
    var didScroll;
    var lastScrollTop = 0;
    var delta = 5;

    $(window).scroll(function(event){
        didScroll = true;
    });

    document.addEventListener("touchstart", ScrollStart, false);

    setInterval(function() {
        if (didScroll) {
            hasScrolled();
            didScroll = false;
        }
    }, 100);

    function ScrollStart() {
        //start of scroll event for iOS
        hasScrolled();
    }

    function hasScrolled() {
        var st = $(this).scrollTop();

        // Make sure they scroll more than delta
        if (Math.abs(lastScrollTop - st) <= delta)
            return;

        // If they scrolled down and are past the filter bar, add class .filter-scrollup.
        // This is necessary so you never see what is "behind" the navbar.
        if (st > lastScrollTop && st > 50) {
            // Scroll Down
            $('.filter-bar').hide();
        } else {
            // Scroll Up
            if (st + $(window).height() < $(document).height()) {
                $('.filter-bar').show();
            }
        }
        lastScrollTop = st;
    }

    var challengeRegion = 'all';
    var challengeRegionType = 'all';
    var challengeSort = 'most_recent_summit';
    var challengePeaks = {};
    var photoViewHover = false;

    function loadChallenges(region, regiontype, sort, page) {

        challengeRegion = region;
        challengeRegionType = regiontype;
        challengeSort = sort;

        $('#pagination').css('display','none');
        window.scrollTo(0, 0);

        if (challengeRegion != '' && challengeRegionType != '') {
            var challengeCount = $('#regionfilter-'+challengeRegionType+'-'+challengeRegion).data('count');
            $('#challenge-count').html(challengeCount);
            if (challengeCount > 1) {
                $('#challenge-count-dscr').html('challenges');
            } else {
                $('#challenge-count-dscr').html('challenge');
            }
        } else {
            var challengeCount = {{ total_count }};
            $('#challenge-count').html(challengeCount);
            if (challengeCount > 1) {
                $('#challenge-count-dscr').html('challenges');
            } else {
                $('#challenge-count-dscr').html('challenge');
            }
        }

        switch(challengeSort) {
            case 'most_recent_summit':
                $('#sort-most-recent-summit').css('color', '#f24100');
                $('#sort-most-recent-summit').css('font-weight', '500');
                $('#sort-num-peaks-summited').css('color', '#999');
                $('#sort-num-peaks-summited').css('font-weight', '300');
                $('#sort-pct-complete').css('color', '#999');
                $('#sort-pct-complete').css('font-weight', '300');
                $('#mobile-sortchallenges-title').html('Most recent summit');
                break;
            case 'num_peaks_summited':
                $('#sort-most-recent-summit').css('color', '#999');
                $('#sort-most-recent-summit').css('font-weight', '300');
                $('#sort-num-peaks-summited').css('color', '#f24100');
                $('#sort-num-peaks-summited').css('font-weight', '500');
                $('#sort-pct-complete').css('color', '#999');
                $('#sort-pct-complete').css('font-weight', '300');
                $('#mobile-sortchallenges-title').html('# Peaks summited');
                break;
            case 'pct_complete':
                $('#sort-most-recent-summit').css('color', '#999');
                $('#sort-most-recent-summit').css('font-weight', '300');
                $('#sort-num-peaks-summited').css('color', '#999');
                $('#sort-num-peaks-summited').css('font-weight', '300');
                $('#sort-pct-complete').css('color', '#f24100');
                $('#sort-pct-complete').css('font-weight', '500');
                $('#mobile-sortchallenges-title').html('% Completed');
                break;
            default:
                $('#sort-most-recent-summit').css('color', '#999');
                $('#sort-most-recent-summit').css('font-weight', '300');
                $('#sort-num-peaks-summited').css('color', '#999');
                $('#sort-num-peaks-summited').css('font-weight', '300');
                $('#sort-pct-complete').css('color', '#f24100');
                $('#sort-pct-complete').css('font-weight', '500');
                $('#mobile-sortchallenges-title').html('% Completed');
        }

        window.location.hash = 'region='+challengeRegion+'&regiontype='+challengeRegionType+'&order='+challengeSort+'&page='+page;

        $('#challenges-list').empty();
        $('#ajax-data-loading').css('display', 'inline');
        var counter = 1;
        $.getJSON('{% url "user_challenges_list" %}?user={{ profile.user_id }}&region='+challengeRegion+'&regiontype='+challengeRegionType+'&sort='+challengeSort, function(data) {

            var pctComplete;
            var totalPeaks;
            var progressClass;
            $.each( data, function( key, val ) {
                if (key=='challenges') {

                    $.each( val, function( challengekey, challengeval ) {
                        pctComplete = Math.round(challengeval.current_round_count / challengeval.peak_count * 100);
                        if (pctComplete == 0 && challengeval.current_round_count > 0) {
                            pctComplete = 1;
                        }
                        if (pctComplete == 100 && challengeval.current_round_count != challengeval.peak_count) {
                            pctComplete = 99;
                        }
                        if (pctComplete == 100) {
                            progressClass = 'progress-bar-green';
                        } else {
                            progressClass = 'progress-bar-blue';
                        }
                        if (pctComplete == 100) {
                            progressClass = 'progress-bar-green';
                        } else {
                            progressClass = 'progress-bar-blue';
                        }
                        totalPeaks = challengeval.peak_count;
                        var roundsCompleted = challengeval.rounds_completed;
                        var currentRound = parseInt(roundsCompleted) + 1;
                        var i = 1;
                        var completedHtml = '';
                        var currentRoundHtml = '';
                        var completedRoundHtml = ''
                        var challengeNameClass = 'challenge-name';
                        var challengeProgressClass = 'progress';
                        var challengeLastSummitClass = 'challenge-last-summit';
                        if (roundsCompleted > 0) {
                            currentRoundHtml = 'Round ' + currentRound + ': ';
                            challengeNameClass = 'challenge-name challenge-name-two-bars';
                            challengeProgressClass = 'progress progress-two-bars';
                            challengeLastSummitClass = 'challenge-last-summit challenge-last-summit-two-bars';
                            while (i <= roundsCompleted) {
                                if (challengeval.current_round_count > 0) {
                                    completedRoundHtml = 'Round ' + i + ': ';
                                }
                                completedHtml = '<div class="toggle-details hidden-xs ' + challengeProgressClass + '" data-challengeid="' + challengeval.id + '"><div class="progress-bar progress-bar-green" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 100%;"><span class="challenge-show">' + completedRoundHtml + totalPeaks + ' out of ' + totalPeaks + ' peaks - completed!</span></div></div>';
                                i++;
                            }
                        }

                        var dividerDiv = '';
                        if (counter > 1) {
                            dividerDiv = '<div class="row dark-background-row"><div class="challenge-divider"></div></div>';
                        }

                        var currentHtml = '';
                        if (challengeval.current_round_count >= 0) {
                            currentHtml = '<div class="toggle-details ' + challengeProgressClass + '" data-challengeid="' + challengeval.id + '"><div class="progress-bar ' + progressClass + '" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: ' + pctComplete + '%;"><span class="challenge-show">' + currentRoundHtml + challengeval.current_round_count + ' out of ' + totalPeaks + ' peaks ('+ pctComplete + '%)</span></div></div>';
                        }

                        var display_date = $.format.date(challengeval.most_recent_summit[0].summitlog_date + 'T00:00:00', 'MMM, d yyyy');
                        var desktop_date = '<span class="hidden-xs"> on <a href="/' + challengeval.most_recent_summit[0].peak_slug + '/summits/' + challengeval.most_recent_summit[0].summitlog_id + '/">' + challengeval.most_recent_summit[0].summitlog_date + '</a></span>';
                        var mobile_date = '<span class="hidden-sm hidden-md hidden-lg">&nbsp;&bull;&nbsp;<a href="/' + challengeval.most_recent_summit[0].peak_slug + '/summits/' + challengeval.most_recent_summit[0].summitlog_id + '"><time class="timeago" datetime="' + challengeval.most_recent_summit[0].summitlog_date + 'T00:00:00">' + challengeval.most_recent_summit[0].summitlog_date + '</time></a></span>';

                        $('#challenges-list').append('<div class="col-md-12">' + dividerDiv + '<div class="row">  <div class="col-lg-3 col-md-3 col-sm-3 col-xs-4 hover-photos" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + challengeval.thumbnail + '\'); background-size: cover; background-position: center top; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><a class="challenge-link" href="/challenges/' + challengeval.slug + '/"><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography leftpeak-responsive leftthird-responsive"></a></div></div>   <div class="col-lg-9 col-md-9 col-sm-9 col-xs-8 leftpeak-responsive leftthird-responsive hover-row challenge-container" data-challengeid="' + challengeval.id + '" data-challengeslug="' + challengeval.slug + '" style="cursor: pointer; display: table; background-color: #fff; padding-left: 0px; padding-right: 0px;"><div class="leftpeak-responsive" style="aspect-ratio: 12/3; padding-top: 3%; vertical-align: middle; padding-left: 15px; padding-right: 15px;"><div class="' + challengeNameClass + '"><span><a class="challenge-link" href="/challenges/' + challengeval.slug + '/" style="color: #000;">' + challengeval.name + '</a></span></div>' + completedHtml + currentHtml + '<div class="hidden-xs ' + challengeLastSummitClass + '"><span class="hidden-lg hidden-md hidden-sm"><a href="/' + challengeval.most_recent_summit[0].peak_slug + '">' + challengeval.most_recent_summit[0].peak_name + '</a>' + desktop_date + mobile_date + '</span><span class="hidden-xs"><a class="toggle-details" data-challengeid="' + challengeval.id + '" id="see-progress-details-' + challengeval.id + '">See progress details</a></span></div></div></div></div></div><div class="user-challenge-details-container hidden-xs" id="details-container-' + challengeval.id + '"><div class="user-challenge-details" id="details-' + challengeval.id + '"></div></div>');
                        counter++;
                    });

                }
            });
            $("time.timeago").timeago();
            $('#ajax-data-loading').css('display', 'none');

        });

    }

    function loadChallengePeaks(challengeId, type) {

        var detailsDiv = '#details-' + challengeId;
        $(detailsDiv).empty();
        $(detailsDiv).append('<div class="row sub-header-row" style="height: 50px; width: 100%; display: inline-block; margin-left: 0px;"><div class="col-md-12" style="height: 50px; line-height: 50px;"><a id="view-photo-' + challengeId + '" style="margin-left: 0px; color: #f24100; font-weight: 500;" class="region-header-sub-links ajax-link" onclick="loadChallengePeaks(' + challengeId + ',\'all\');">Photo view</a><a id="view-season-' + challengeId + '" class="region-header-sub-links ajax-link" onclick="loadChallengeSeasons(' + challengeId + ',\'all\');">Season grid</a><a id="view-month-' + challengeId + '" class="region-header-sub-links ajax-link hidden-xs hidden-sm hidden-md" onclick="loadChallengeMonths(' + challengeId + ',\'all\');">Month grid</a><span class="pull-right"><a id="filter-challenge-peaks-all-' + challengeId + '" class="region-header-sort-links ajax-link filter-challenge-peaks-all" onclick="loadChallengePeaks(' + challengeId + ',\'all\');">All peaks</a><a style="margin-left: 35px;" id="filter-challenge-peaks-summited-' + challengeId + '" class="region-header-sort-links ajax-link filter-challenge-peaks-summited" onclick="loadChallengePeaks(' + challengeId + ',\'summited\');">Summited</a><a style="margin-left: 35px;" id="filter-challenge-peaks-unsummited-' + challengeId + '" class="region-header-sort-links ajax-link filter-challenge-peaks-unsummited" onclick="loadChallengePeaks(' + challengeId + ',\'unsummited\');">Unsummited</a></span></div></div>');
        $.getJSON('{% url "user_challenge_peaks" %}?user={{ profile.user_id }}&challenge='+challengeId+'&type='+type, function(data) {
            $.each( data, function( key, val ) {
                if (key=='peaks') {
                    var photoHtml = '';
                    $.each( val, function( peakkey, peakval ) {
                        if (peakval.summit_count > 0) {
                            imgClass = 'user-challenge-photo-summited';
                        } else if (type == 'all' && peakval.summit_count == 0) {
                            imgClass = 'user-challenge-photo-unsummited';
                        } else {
                            imgClass = 'user-challenge-photo-summited';
                        }
                        photoHtml = photoHtml + '<div class="user-challenge-photo"><img data-peakid="' + peakval.peak_id + '" data-peakslug="' + peakval.slug + '" class="user-challenge-photo-img ' + imgClass + '" src="{{ MEDIA_URL }}' + peakval.fullsize_url + '"></div>';
                        //add peak info to tooltip array
                        //build summits string
                        var summits, tooltip_your_summits;
                        if (peakval.total_summits > 0) {
                            summits = '<a href="/' + peakval.slug + '/summits/">' + peakval.total_summits + '</a>';
                        } else {
                            summits = '<a href="/' + peakval.slug + '/summits/">0</a><br /><a href="/' + peakval.slug + '/">Be the first!</a>';
                        }
                        if (peakval.your_summits > 0) {
                            summits = summits + '<br /><span style="color: #3fc663; font-weight: 500;">' + peakval.your_summits + 'x by you</span>';
                            tooltip_your_summits = '&nbsp;<span style="color: #3fc663; font-weight: 500;">(You ' + peakval.your_summits + 'x)</span>';
                        } else {
                            tooltip_your_summits = '';
                        }

                        //build tooltip string
                        var tooltip_html, tooltip_width, tooltip_height, marker_top, marker_left;
                        if (peakval.thumbnail_url.indexOf('default.png.350x245_q95_crop.jpg') < 0) {
                            tooltip_html = '<img class="map-tooltip-img hover-photos-hover" src="{{ MEDIA_URL }}' + peakval.fullsize_url + '"><div class="map-tooltip-info"><div class="map-tooltip-peak-name ellipsis">' + peakval.name + '</div><div class="map-tooltip-peak-stats"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + tooltip_your_summits + '</div></div>';
                            tooltip_width = 220;
                            tooltip_height = 165;
                        } else {
                            tooltip_html = '<div class="map-tooltip-info"><div class="map-tooltip-peak-name ellipsis">' + peakval.name + '</div><div class="map-tooltip-peak-stats"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + tooltip_your_summits + '</div></div>';
                            tooltip_width = 220;
                            tooltip_height = 50;
                        }
                        challengePeaks[peakval.peak_id] = {'html':tooltip_html, 'width':tooltip_width, 'height':tooltip_height};
                    });
                    $(detailsDiv).append('<div>' + photoHtml + '</div>');
                }
            });
        });

        switch(type) {
            case 'all':
                $('.filter-challenge-peaks-all').css('color', '#f24100');
                $('.filter-challenge-peaks-all').css('font-weight', '500');
                $('.filter-challenge-peaks-summited').css('color', '#999');
                $('.filter-challenge-peaks-summited').css('font-weight', '300');
                $('.filter-challenge-peaks-unsummited').css('color', '#999');
                $('.filter-challenge-peaks-unsummited').css('font-weight', '300');
                break;
            case 'summited':
                $('.filter-challenge-peaks-all').css('color', '#999');
                $('.filter-challenge-peaks-all').css('font-weight', '300');
                $('.filter-challenge-peaks-summited').css('color', '#f24100');
                $('.filter-challenge-peaks-summited').css('font-weight', '500');
                $('.filter-challenge-peaks-unsummited').css('color', '#999');
                $('.filter-challenge-peaks-unsummited').css('font-weight', '300');
                break;
            case 'unsummited':
                $('.filter-challenge-peaks-all').css('color', '#999');
                $('.filter-challenge-peaks-all').css('font-weight', '300');
                $('.filter-challenge-peaks-summited').css('color', '#999');
                $('.filter-challenge-peaks-summited').css('font-weight', '300');
                $('.filter-challenge-peaks-unsummited').css('color', '#f24100');
                $('.filter-challenge-peaks-unsummited').css('font-weight', '500');
                break;
            default:
                $('.filter-challenge-peaks-all').css('color', '#f24100');
                $('.filter-challenge-peaks-all').css('font-weight', '500');
                $('.filter-challenge-peaks-summited').css('color', '#999');
                $('.filter-challenge-peaks-summited').css('font-weight', '300');
                $('.filter-challenge-peaks-unsummited').css('color', '#999');
                $('.filter-challenge-peaks-unsummited').css('font-weight', '300');
        }

    }

    function loadChallengeSeasons(challengeId, type) {

        var detailsDiv = '#details-' + challengeId;
        $(detailsDiv).empty();
        $(detailsDiv).append('<div class="row sub-header-row" style="height: 58px; width: 100%; display: inline-block; margin-left: 0px;"><div class="col-md-12" style="height: 58px; line-height: 58px;"><a id="view-photo-' + challengeId + '" style="margin-left: 0px;" class="region-header-sub-links ajax-link" onclick="loadChallengePeaks(' + challengeId + ', \'all\');">Photo view</a><a style="color: #f24100; font-weight: 500;" id="view-season-' + challengeId + '" class="region-header-sub-links ajax-link" onclick="loadChallengeSeasons(' + challengeId + ', \'all\');">Season grid</a><a id="view-month-' + challengeId + '" class="region-header-sub-links ajax-link hidden-xs hidden-sm hidden-md" onclick="loadChallengeMonths(' + challengeId + ', \'all\');">Month grid</a><span class="pull-right"><a id="filter-challenge-seasons-all-' + challengeId + '" class="region-header-sort-links ajax-link filter-challenge-seasons-all" onclick="loadChallengeSeasons(' + challengeId + ',\'all\');">All peaks</a><a style="margin-left: 35px;" id="filter-challenge-seasons-summited-' + challengeId + '" class="region-header-sort-links ajax-link filter-challenge-seasons-summited" onclick="loadChallengeSeasons(' + challengeId + ',\'summited\');">Summited</a><a style="margin-left: 35px;" id="filter-challenge-seasons-unsummited-' + challengeId + '" class="region-header-sort-links ajax-link filter-challenge-seasons-unsummited" onclick="loadChallengeSeasons(' + challengeId + ',\'unsummited\');">Unsummited</a></span></div></div>');
        $.getJSON('{% url "user_challenge_seasons" %}?user={{ profile.user_id }}&challenge='+challengeId+'&type='+type, function(data) {
            $.each( data, function( key, val ) {
                if (key=='peaks') {
                    var counter = 1;
                    var winterCounter = 0;
                    var springCounter = 0;
                    var summerCounter = 0;
                    var fallCounter = 0;
                    var seasonHtml = '';
                    var seasonHtmlHeader = '';
                    var winterClass, springClass, summerClass, fallClass, winterDate, springDate, summerDate, fallDate, winterUrl, springUrl, summerUrl, fallUrl;
                    $.each( val, function( peakkey, peakval ) {
                        if (peakval.winter_date != '') {
                            winterClass = 'hover-photos user-challenge-peak-season-on';
                            winterDate = peakval.winter_date;
                            winterUrl = 'style="cursor: pointer;" onclick="openUrl(\'/' + peakval.slug + '/summits/' + peakval.winter_summit_id + '\');" ';
                            winterCounter ++;
                        } else {
                            winterClass = 'hover-cell user-challenge-peak-season-off';
                            winterDate = '';
                            winterUrl = '';
                        }
                        if (peakval.spring_date != '') {
                            springClass = 'hover-photos user-challenge-peak-season-on';
                            springDate = peakval.spring_date;
                            springUrl = 'style="cursor: pointer;" onclick="openUrl(\'/' + peakval.slug + '/summits/' + peakval.spring_summit_id + '\');" ';
                            springCounter ++;
                        } else {
                            springClass = 'hover-cell user-challenge-peak-season-off';
                            springDate = '';
                            springUrl = '';
                        }
                        if (peakval.summer_date != '') {
                            summerClass = 'hover-photos user-challenge-peak-season-on';
                            summerDate = peakval.summer_date;
                            summerUrl = 'style="cursor: pointer;" onclick="openUrl(\'/' + peakval.slug + '/summits/' + peakval.summer_summit_id + '\');" ';
                            summerCounter ++;
                        } else {
                            summerClass = 'hover-cell user-challenge-peak-season-off';
                            summerDate = '';
                            summerUrl = '';
                        }
                        if (peakval.fall_date != '') {
                            fallClass = 'hover-photos user-challenge-peak-season-on';
                            fallDate = peakval.fall_date;
                            fallUrl = 'style="cursor: pointer;" onclick="openUrl(\'/' + peakval.slug + '/summits/' + peakval.fall_summit_id + '\');" ';
                            fallCounter ++;
                        } else {
                            fallClass = 'hover-cell user-challenge-peak-season-off';
                            fallDate = '';
                            fallUrl = '';
                        }
                        seasonHtml = seasonHtml + '<tr><td style="cursor: pointer;" class="hover-cell user-challenge-peak-name" onclick="openUrl(\'/' + peakval.slug + '\');"><img style="float: left; margin-right: 10px; width: 80px; height: 58px;" src="{{ MEDIA_URL }}' + peakval.fullsize_url + '"><div class="ellipsis season-peak-name" style="float: left; height: 58px; line-height: 58px;">' + peakval.name + '</div></td><td style="cursor: pointer;" class="hover-cell user-challenge-peak-elevation" onclick="openUrl(\'/' + peakval.slug + '\');">' + numberWithCommas(Math.floor(peakval.elevation)) + ' ft / ' + numberWithCommas(Math.floor(peakval.elevation * .3048)) + ' m</td><td ' + winterUrl + 'class="' + winterClass + '">' + winterDate + '</td><td ' + springUrl + 'class="' + springClass + '">' + springDate + '</td><td ' + summerUrl + 'class="' + summerClass + '">' + summerDate + '</td><td ' + fallUrl + 'class="' + fallClass + '">' + fallDate + '</td></tr>';
                        counter ++;
                    });
                    counter --;
                    var winterPct = parseInt((winterCounter / counter) * 100);
                    var springPct = parseInt((springCounter / counter) * 100);
                    var summerPct = parseInt((summerCounter / counter) * 100);
                    var fallPct = parseInt((fallCounter / counter) * 100);
                    if (type == 'all') {
                        seasonHtmlHeader = '<table class="table table-striped table-bordered user-challenge-season-table"><thead><tr><th>Peak name</th><th style="width: 130px !important; text-align: center;">Elevation</th><th style="width: 100px !important;">Winter (' + winterPct + '%)</th><th style="width: 100px !important;">Spring (' + springPct + '%)</th><th style="width: 100px !important;">Summer (' + summerPct + '%)</th><th style="width: 100px !important;">Fall (' + fallPct + '%)</th></tr></thead><tbody>';
                    } else {
                        seasonHtmlHeader = '<table class="table table-striped table-bordered user-challenge-season-table"><thead><tr><th>Peak name</th><th style="width: 130px !important; text-align: center;">Elevation</th><th style="width: 100px !important;">Winter</th><th style="width: 100px !important;">Spring</th><th style="width: 100px !important;">Summer</th><th style="width: 100px !important;">Fall</th></tr></thead><tbody>';
                    }
                    seasonHtml = seasonHtmlHeader + seasonHtml + '</tbody></table>';
                    $(detailsDiv).append('<div style="margin-left: 0px; margin-top: 0px; margin-bottom: 0px;">' + seasonHtml + '</div>');
                }
            });
        });

        switch(type) {
            case 'all':
                $('.filter-challenge-seasons-all').css('color', '#f24100');
                $('.filter-challenge-seasons-all').css('font-weight', '500');
                $('.filter-challenge-seasons-summited').css('color', '#999');
                $('.filter-challenge-seasons-summited').css('font-weight', '300');
                $('.filter-challenge-seasons-unsummited').css('color', '#999');
                $('.filter-challenge-seasons-unsummited').css('font-weight', '300');
                break;
            case 'summited':
                $('.filter-challenge-seasons-all').css('color', '#999');
                $('.filter-challenge-seasons-all').css('font-weight', '300');
                $('.filter-challenge-seasons-summited').css('color', '#f24100');
                $('.filter-challenge-seasons-summited').css('font-weight', '500');
                $('.filter-challenge-seasons-unsummited').css('color', '#999');
                $('.filter-challenge-seasons-unsummited').css('font-weight', '300');
                break;
            case 'unsummited':
                $('.filter-challenge-seasons-all').css('color', '#999');
                $('.filter-challenge-seasons-all').css('font-weight', '300');
                $('.filter-challenge-seasons-summited').css('color', '#999');
                $('.filter-challenge-seasons-summited').css('font-weight', '300');
                $('.filter-challenge-seasons-unsummited').css('color', '#f24100');
                $('.filter-challenge-seasons-unsummited').css('font-weight', '500');
                break;
            default:
                $('.filter-challenge-seasons-all').css('color', '#f24100');
                $('.filter-challenge-seasons-all').css('font-weight', '500');
                $('.filter-challenge-seasons-summited').css('color', '#999');
                $('.filter-challenge-seasons-summited').css('font-weight', '300');
                $('.filter-challenge-seasons-unsummited').css('color', '#999');
                $('.filter-challenge-seasons-unsummited').css('font-weight', '300');
        }

    }

    function loadChallengeMonths(challengeId, type) {

        var detailsDiv = '#details-' + challengeId;
        $(detailsDiv).empty();
        $(detailsDiv).append('<div class="row sub-header-row" style="height: 50px; width: 100%; display: inline-block; margin-left: 0px;"><div class="col-md-12" style="height: 50px; line-height: 50px;"><a id="view-photo-' + challengeId + '" style="margin-left: 0px;" class="region-header-sub-links ajax-link" onclick="loadChallengePeaks(' + challengeId + ', \'all\');">Photo view</a><a id="view-season-' + challengeId + '" class="region-header-sub-links ajax-link" onclick="loadChallengeSeasons(' + challengeId + ', \'all\');">Season grid</a><a style="color: #f24100; font-weight: 500;" id="view-month-' + challengeId + '" class="region-header-sub-links ajax-link hidden-xs hidden-sm hidden-md" onclick="loadChallengeMonths(' + challengeId + ', \'all\');">Month grid</a><span class="pull-right"><a id="filter-challenge-months-all-' + challengeId + '" class="region-header-sort-links ajax-link filter-challenge-months-all" onclick="loadChallengeMonths(' + challengeId + ',\'all\');">All peaks</a><a style="margin-left: 35px;" id="filter-challenge-months-summited-' + challengeId + '" class="region-header-sort-links ajax-link filter-challenge-months-summited" onclick="loadChallengeMonths(' + challengeId + ',\'summited\');">Summited</a><a style="margin-left: 35px;" id="filter-challenge-months-unsummited-' + challengeId + '" class="region-header-sort-links ajax-link filter-challenge-months-unsummited" onclick="loadChallengeMonths(' + challengeId + ',\'unsummited\');">Unsummited</a></span></div></div>');
        $.getJSON('{% url "user_challenge_months" %}?user={{ profile.user_id }}&challenge='+challengeId+'&type='+type, function(data) {
            $.each( data, function( key, val ) {
                if (key=='peaks') {
                    var counter = 1;
                    var janCounter = 0;
                    var febCounter = 0;
                    var marCounter = 0;
                    var aprCounter = 0;
                    var mayCounter = 0;
                    var junCounter = 0;
                    var julCounter = 0;
                    var augCounter = 0;
                    var sepCounter = 0;
                    var octCounter = 0;
                    var novCounter = 0;
                    var decCounter = 0;
                    var monthHtml = '';
                    var monthHtmlHeader = '';
                    var janClass, febClass, marClass, aprClass, mayClass, junClass, julClass, augClass, sepClass, octClass, novClass, decClass, janDate, febDate, marDate, aprDate, mayDate, junDate, julDate, augDate, sepDate, octDate, novDate, decDate, janUrl, febUrl, marUrl, aprUrl, mayUrl, junUrl, julUrl, augUrl, sepUrl, octUrl, novUrl, decUrl;
                    $.each( val, function( peakkey, peakval ) {
                        if (peakval.jan_date != '') {
                            janClass = 'hover-photos user-challenge-peak-month-on';
                            janDate = peakval.jan_date;
                            janUrl = 'style="cursor: pointer;" onclick="openUrl(\'/' + peakval.slug + '/summits/' + peakval.jan_summit_id + '\');" ';
                            janCounter++;
                        } else {
                            janClass = 'hover-cell user-challenge-peak-month-off';
                            janDate = '';
                            janUrl = '';
                        }
                        if (peakval.feb_date != '') {
                            febClass = 'hover-photos user-challenge-peak-month-on';
                            febDate = peakval.feb_date;
                            febUrl = 'style="cursor: pointer;" onclick="openUrl(\'/' + peakval.slug + '/summits/' + peakval.feb_summit_id + '\');" ';
                            febCounter++;
                        } else {
                            febClass = 'hover-cell user-challenge-peak-month-off';
                            febDate = '';
                            febUrl = '';
                        }
                        if (peakval.mar_date != '') {
                            marClass = 'hover-photos user-challenge-peak-month-on';
                            marDate = peakval.mar_date;
                            marUrl = 'style="cursor: pointer;" onclick="openUrl(\'/' + peakval.slug + '/summits/' + peakval.mar_summit_id + '\');" ';
                            marCounter++;
                        } else {
                            marClass = 'hover-cell user-challenge-peak-month-off';
                            marDate = '';
                            marUrl = '';
                        }
                        if (peakval.apr_date != '') {
                            aprClass = 'hover-photos user-challenge-peak-month-on';
                            aprDate = peakval.apr_date;
                            aprUrl = 'style="cursor: pointer;" onclick="openUrl(\'/' + peakval.slug + '/summits/' + peakval.apr_summit_id + '\');" ';
                            aprCounter++;
                        } else {
                            aprClass = 'hover-cell user-challenge-peak-month-off';
                            aprDate = '';
                            aprUrl = '';
                        }
                        if (peakval.may_date != '') {
                            mayClass = 'hover-photos user-challenge-peak-month-on';
                            mayDate = peakval.may_date;
                            mayUrl = 'style="cursor: pointer;" onclick="openUrl(\'/' + peakval.slug + '/summits/' + peakval.may_summit_id + '\');" ';
                            mayCounter++;
                        } else {
                            mayClass = 'hover-cell user-challenge-peak-month-off';
                            mayDate = '';
                            mayUrl = '';
                        }
                        if (peakval.jun_date != '') {
                            junClass = 'hover-photos user-challenge-peak-month-on';
                            junDate = peakval.jun_date;
                            junUrl = 'style="cursor: pointer;" onclick="openUrl(\'/' + peakval.slug + '/summits/' + peakval.jun_summit_id + '\');" ';
                            junCounter++;
                        } else {
                            junClass = 'hover-cell user-challenge-peak-month-off';
                            junDate = '';
                            junUrl = '';
                        }
                        if (peakval.jul_date != '') {
                            julClass = 'hover-photos user-challenge-peak-month-on';
                            julDate = peakval.jul_date;
                            julUrl = 'style="cursor: pointer;" onclick="openUrl(\'/' + peakval.slug + '/summits/' + peakval.jul_summit_id + '\');" ';
                            julCounter++;
                        } else {
                            julClass = 'hover-cell user-challenge-peak-month-off';
                            julDate = '';
                            julUrl = '';
                        }
                        if (peakval.aug_date != '') {
                            augClass = 'hover-photos user-challenge-peak-month-on';
                            augDate = peakval.aug_date;
                            augUrl = 'style="cursor: pointer;" onclick="openUrl(\'/' + peakval.slug + '/summits/' + peakval.aug_summit_id + '\');" ';
                            augCounter++;
                        } else {
                            augClass = 'hover-cell user-challenge-peak-month-off';
                            augDate = '';
                            augUrl = '';
                        }
                        if (peakval.sep_date != '') {
                            sepClass = 'hover-photos user-challenge-peak-month-on';
                            sepDate = peakval.sep_date;
                            sepUrl = 'style="cursor: pointer;" onclick="openUrl(\'/' + peakval.slug + '/summits/' + peakval.sep_summit_id + '\');" ';
                            sepCounter++;
                        } else {
                            sepClass = 'hover-cell user-challenge-peak-month-off';
                            sepDate = '';
                            sepUrl = '';
                        }
                        if (peakval.oct_date != '') {
                            octClass = 'hover-photos user-challenge-peak-month-on';
                            octDate = peakval.oct_date;
                            octUrl = 'style="cursor: pointer;" onclick="openUrl(\'/' + peakval.slug + '/summits/' + peakval.oct_summit_id + '\');" ';
                            octCounter++;
                        } else {
                            octClass = 'hover-cell user-challenge-peak-month-off';
                            octDate = '';
                            octUrl = '';
                        }
                        if (peakval.nov_date != '') {
                            novClass = 'hover-photos user-challenge-peak-month-on';
                            novDate = peakval.nov_date;
                            novUrl = 'style="cursor: pointer;" onclick="openUrl(\'/' + peakval.slug + '/summits/' + peakval.nov_summit_id + '\');" ';
                            novCounter++;
                        } else {
                            novClass = 'hover-cell user-challenge-peak-month-off';
                            novDate = '';
                            novUrl = '';
                        }
                        if (peakval.dec_date != '') {
                            decClass = 'hover-photos user-challenge-peak-month-on';
                            decDate = peakval.dec_date;
                            decUrl = 'style="cursor: pointer;" onclick="openUrl(\'/' + peakval.slug + '/summits/' + peakval.dec_summit_id + '\');" ';
                            decCounter++;
                        } else {
                            decClass = 'hover-cell user-challenge-peak-month-off';
                            decDate = '';
                            decUrl = '';
                        }
                        counter ++;
                        monthHtml = monthHtml + '<tr><td style="cursor: pointer;" class="hover-cell user-challenge-peak-name" onclick="openUrl(\'/' + peakval.slug + '\');"><img style="float: left; margin-right: 10px; width: 80px; height: 58px;" src="{{ MEDIA_URL }}' + peakval.fullsize_url + '"><div class="ellipsis month-peak-name" style="float: left; height: 58px; line-height: 58px;">' + peakval.name + '</div></td><td style="cursor: pointer;" class="hover-cell user-challenge-peak-elevation" onclick="openUrl(\'/' + peakval.slug + '\');">' + numberWithCommas(Math.floor(peakval.elevation)) + ' ft / ' + numberWithCommas(Math.floor(peakval.elevation * .3048)) + ' m</td><td ' + janUrl + ' class="' + janClass + '">' + janDate + '</td><td ' + febUrl + ' class="' + febClass + '">' + febDate + '</td><td ' + marUrl + ' class="' + marClass + '">' + marDate + '</td><td ' + aprUrl + ' class="' + aprClass + '">' + aprDate + '</td><td ' + mayUrl + ' class="' + mayClass + '">' + mayDate + '</td><td ' + junUrl + ' class="' + junClass + '">' + junDate + '</td><td ' + julUrl + ' class="' + julClass + '">' + julDate + '</td><td ' + augUrl + ' class="' + augClass + '">' + augDate + '</td><td ' + sepUrl + ' class="' + sepClass + '">' + sepDate + '</td><td ' + octUrl + ' class="' + octClass + '">' + octDate + '</td><td ' + novUrl + ' class="' + novClass + '">' + novDate + '</td><td ' + decUrl + ' class="' + decClass + '">' + decDate + '</td></tr>';
                    });
                    counter --;
                    var janPct = parseInt((janCounter / counter) * 100);
                    var febPct = parseInt((febCounter / counter) * 100);
                    var marPct = parseInt((marCounter / counter) * 100);
                    var aprPct = parseInt((aprCounter / counter) * 100);
                    var mayPct = parseInt((mayCounter / counter) * 100);
                    var junPct = parseInt((junCounter / counter) * 100);
                    var julPct = parseInt((julCounter / counter) * 100);
                    var augPct = parseInt((augCounter / counter) * 100);
                    var sepPct = parseInt((sepCounter / counter) * 100);
                    var octPct = parseInt((octCounter / counter) * 100);
                    var novPct = parseInt((novCounter / counter) * 100);
                    var decPct = parseInt((decCounter / counter) * 100);
                    if (type == 'all') {
                        monthHtmlHeader = '<table class="table table-striped table-bordered user-challenge-season-table hidden-xs hidden-sm hidden-md"><thead><tr><th>Peak name</th><th style="text-align: center;">Elevation</th><th style="text-align: center;">Jan<br />' + janPct + '%</th><th style="text-align: center;">Feb<br />' + febPct + '%</th><th style="text-align: center;">Mar<br />' + marPct + '%</th><th style="text-align: center;">Apr<br />' + aprPct + '%</th><th style="text-align: center;">May<br />' + mayPct + '%</th><th style="text-align: center;">Jun<br />' + junPct + '%</th><th style="text-align: center;">Jul<br />' + julPct + '%</th><th style="text-align: center;">Aug<br />' + augPct + '%</th><th style="text-align: center;">Sep<br />' + sepPct + '%</th><th style="text-align: center;">Oct<br />' + octPct + '%</th><th style="text-align: center;">Nov<br />' + novPct + '%</th><th style="text-align: center;">Dec<br />' + decPct + '%</th></tr></thead><tbody>';
                    } else {
                        monthHtmlHeader = '<table class="table table-striped table-bordered user-challenge-season-table hidden-xs hidden-sm hidden-md"><thead><tr><th>Peak name</th><th style="text-align: center;">Elevation</th><th style="text-align: center;">Jan</th><th style="text-align: center;">Feb</th><th style="text-align: center;">Mar</th><th style="text-align: center;">Apr</th><th style="text-align: center;">May</th><th style="text-align: center;">Jun</th><th style="text-align: center;">Jul</th><th style="text-align: center;">Aug</th><th style="text-align: center;">Sep</th><th style="text-align: center;">Oct</th><th style="text-align: center;">Nov</th><th style="text-align: center;">Dec</th></tr></thead><tbody>';
                    }
                    monthHtml = monthHtmlHeader + monthHtml + '</tbody></table>';
                    $(detailsDiv).append('<div style="margin-left: 0px; margin-top: 0px; margin-bottom: 0px;">' + monthHtml + '</div>');
                }
            });
        });

        switch(type) {
            case 'all':
                $('.filter-challenge-months-all').css('color', '#f24100');
                $('.filter-challenge-months-all').css('font-weight', '500');
                $('.filter-challenge-months-summited').css('color', '#999');
                $('.filter-challenge-months-summited').css('font-weight', '300');
                $('.filter-challenge-months-unsummited').css('color', '#999');
                $('.filter-challenge-months-unsummited').css('font-weight', '300');
                break;
            case 'summited':
                $('.filter-challenge-months-all').css('color', '#999');
                $('.filter-challenge-months-all').css('font-weight', '300');
                $('.filter-challenge-months-summited').css('color', '#f24100');
                $('.filter-challenge-months-summited').css('font-weight', '500');
                $('.filter-challenge-months-unsummited').css('color', '#999');
                $('.filter-challenge-months-unsummited').css('font-weight', '300');
                break;
            case 'unsummited':
                $('.filter-challenge-months-all').css('color', '#999');
                $('.filter-challenge-months-all').css('font-weight', '300');
                $('.filter-challenge-months-summited').css('color', '#999');
                $('.filter-challenge-months-summited').css('font-weight', '300');
                $('.filter-challenge-months-unsummited').css('color', '#f24100');
                $('.filter-challenge-months-unsummited').css('font-weight', '500');
                break;
            default:
                $('.filter-challenge-months-all').css('color', '#f24100');
                $('.filter-challenge-months-all').css('font-weight', '500');
                $('.filter-challenge-months-summited').css('color', '#999');
                $('.filter-challenge-months-summited').css('font-weight', '300');
                $('.filter-challenge-months-unsummited').css('color', '#999');
                $('.filter-challenge-months-unsummited').css('font-weight', '300');
        }

    }

    function numberWithCommas(x) {
        return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    $(document).ready(function() {

        $('#mobileRegionfilterSelect option:selected').text($('#mobileRegionfilterSelect option:selected').text()+' \u25BE');
        $('#mobileSortChallengesSelect option:selected').text($('#mobileSortChallengesSelect option:selected').text()+' \u25BE');

        var viewport_height = $(window).height();
        var viewport_width = $(window).width();
        var footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
        var data_loading_margin = (viewport_height - footer_height) / 3;
        $('.data-loading').css('margin-top', data_loading_margin);
        $(window).resize(function() {
            viewport_height = $(window).height();
            viewport_width = $(window).width();
            footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
            data_loading_margin = (viewport_height - footer_height) / 3;
            $('.data-loading').css('margin-top', data_loading_margin);
        });

        var vars = [], hash, region, regiontype, sort, page;
        var q = document.URL.split('#')[1];
        if(q != undefined){
            q = q.split('&');
            for(var i = 0; i < q.length; i++){
                hash = q[i].split('=');
                vars.push(hash[1]);
                vars[hash[0]] = hash[1];
            }
        }

        if (vars['region'] != undefined) {
            region = vars['region'];
            if (region != '') {
                var region_html = $('.regionfilter-item[data-value='+region+']').html();
                var region_name = region_html.split('\u2022');
                $("#regionfilter-title").html(region_name[0]);
                $("#mobile-regionfilter-title").html(region_name[0]);
            }
        } else {
            region = '';
        }

        if (vars['regiontype'] != undefined) {
            regiontype = vars['regiontype'];
        } else {
            regiontype = '';
        }

        if (vars['order'] != undefined) {
            sort = vars['order'];
        } else {
            sort = 'pct_complete';
        }

        if (vars['page'] != undefined) {
            page = vars['page'];
        } else {
            page = '1';
        }

        $('#challenges-list').on('mouseover', '.user-challenge-photo-img', function(e) {
            e.stopPropagation();
            var peak_id = $(this).data('peakid');
            var tooltip_height = challengePeaks[peak_id].height;
            var tooltip_width = challengePeaks[peak_id].width;

            var bottom = $(window).height();
            var right = $(window).width();

            var marker_top;
            var marker_left;

            if (e.clientY < (bottom/2)) {
                marker_top = e.clientY;
            } else {
                marker_top = e.clientY - tooltip_height - 45;
            }

            if (e.clientX < (right/3)) {
                marker_left = e.clientX;
            } else if (e.clientX >= (right/3) && e.clientX < ((right/3)*2)) {
                marker_left = e.clientX - (tooltip_width/2) - 15;
            } else {
                marker_left = e.clientX - tooltip_width - 30;
            }

            if (isTouchDevice()) {
                //nothing to do yet
            } else {
                photoViewHover = true;
                var url = '/' + $(this).data('peakslug');
                $('#marker-tooltip').data('url',url);
                $('#marker-tooltip').html(challengePeaks[peak_id].html).css({
                    'left': marker_left,
                    'top': marker_top,
                    'width': challengePeaks[peak_id].width,
                    'height': challengePeaks[peak_id].height
                }).show();
            }
        });

        $('#challenges-list').on('click', '.user-challenge-photo-img', function(e) {
            e.stopPropagation();
            if (isTouchDevice()) {
                var peak_id = $(this).data('peakid');
                var tooltip_height = challengePeaks[peak_id].height;
                var tooltip_width = challengePeaks[peak_id].width;

                var bottom = $(window).height();
                var right = $(window).width();

                var marker_top;
                var marker_left;

                if (e.clientY < (bottom/2)) {
                    marker_top = e.clientY;
                } else {
                    marker_top = e.clientY - tooltip_height - 45;
                }

                if (e.clientX < (right/3)) {
                    marker_left = e.clientX;
                } else if (e.clientX >= (right/3) && e.clientX < ((right/3)*2)) {
                    marker_left = e.clientX - (tooltip_width/2) - 15;
                } else {
                    marker_left = e.clientX - tooltip_width - 30;
                }

                if (photoViewHover) {
                    photoViewHover = false;
                    $('#marker-tooltip').hide();
                } else {
                    photoViewHover = true;
                    var url = '/' + $(this).data('peakslug');
                    $('#marker-tooltip').data('url',url);
                    $('#marker-tooltip').html(challengePeaks[peak_id].html).css({
                        'left': marker_left,
                        'top': marker_top,
                        'width': challengePeaks[peak_id].width,
                        'height': challengePeaks[peak_id].height
                    }).show();
                }
            } else {
                var url = '/' + $(this).data('peakslug');
                openUrl(url);
            }
        });

        $('#challenges-list').on('mouseout', '.user-challenge-photo-img', function(e) {
           $('#marker-tooltip').hide();
        });

        $('#marker-tooltip').on('mouseover', function(e) {
            if (isTouchDevice()) {
                window.location.href = $(this).data('url');
            }
        });

        $('body').on('click', function(e) {
            photoViewHover = false;
            $('#marker-tooltip').hide();
        });

        $(window).on('scroll', function(e) {
            photoViewHover = false;
            $('#marker-tooltip').hide();
        });

        $('.regionfilter-item').on('click', function(e) {

            e.preventDefault();
            var region_html = $(this).html();
            var region_name = region_html.split('\u2022');
            $("#regionfilter-title").html(region_name[0]);
            $("#mobile-regionfilter-title").html(region_name[0]);
            challengeRegion = $(this).data('value');
            challengeRegionType = $(this).data('type');
            $('#mobileRegionfilterSelect').val(challengeRegion);
            loadChallenges(challengeRegion, challengeRegionType, challengeSort, '1');

        });

        $('#mobileSortChallengesSelect').on('change', function() {

            $('#mobileSortChallengesSelect option').each( function( index, element ){
                $(this).text($(this).text().replace(' \u25BE',''));
            });
            $('#mobileSortChallengesSelect option:selected').text($('#mobileSortChallengesSelect option:selected').text()+' \u25BE');
            challengeSort = $(this).val();
            loadChallenges(challengeRegion, challengeRegionType, challengeSort, '1');

        });

        $('#mobileRegionfilterSelect').on('change', function() {

            $("#regionfilter-title").html($( "#mobileRegionfilterSelect option:selected" ).text());
            $('#mobileRegionfilterSelect option').each( function( index, element ){
                $(this).text($(this).text().replace(' \u25BE',''));
            });
            $('#mobileRegionfilterSelect option:selected').text($('#mobileRegionfilterSelect option:selected').text()+' \u25BE');
            challengeRegion = $( "#mobileRegionfilterSelect option:selected").val();
            challengeRegionType = $( "#mobileRegionfilterSelect option:selected").data('type');
            loadChallenges(challengeRegion, challengeRegionType, challengeSort, '1');

        });

        $('#challenges-list').on('click', '.challenge-link', function(e) {
            e.stopPropagation();
        });

        $('#challenges-list').on('click', '.toggle-details', function(e) {

            e.stopPropagation();
            var challengeId = $(this).data('challengeid');
            var detailsDiv = '#details-container-' + challengeId;
            var detailsLink = '#see-progress-details-' + challengeId;
            if ($(detailsDiv).is('.open')) {
                $(detailsLink).html('See progress details');
                $(detailsDiv).slideUp(300).removeClass('open');
            } else {
                $(detailsLink).html('Hide progress details');
                $(detailsDiv).slideDown(300).addClass('open');
                loadChallengePeaks(challengeId, 'all');
            }

        });

        $('#challenges-list').on('click', '.challenge-container', function(e) {

            var challengeId = $(this).data('challengeid');
            var detailsDiv = '#details-container-' + challengeId;
            var detailsLink = '#see-progress-details-' + challengeId;
            if ($(detailsDiv).is('.open')) {
                $(detailsLink).html('See progress details');
                $(detailsDiv).slideUp(300).removeClass('open');
            } else {
                $(detailsLink).html('Hide progress details');
                $(detailsDiv).slideDown(300).addClass('open');
                loadChallengePeaks(challengeId, 'all');
            }

        });

        $('#unfollow-{{ bagger.id }}').hover(
        function(){
            $(this).removeClass('unfollowButton').addClass('unfollowButtonHoverProfile').text('Unfollow').css('background-color','#f24100').css('border-color','#f24100');
        },
        function(){
            $(this).removeClass('unfollowButtonHoverProfile').addClass('unfollowButton').text('You are following').css('background-color','#00b330').css('border-color','#00b330');
        });

        $("#unfollow-{{ bagger.id }}").on('click',function (){
            var btn = $("#unfollow-{{ bagger.id }}");
            var follow_btn = $("#follow-{{ bagger.id }}");
            btn.html('<i class="fa fa-spinner fa-spin"></i>');
            $.post('/accounts/unfollow/{{ bagger.id }}/',function(data){
                if (data == "True"){
                    follow_btn.html('Follow');
                    $("#unfollow-{{ bagger.id }}").hide();
                    $("#follow-{{ bagger.id }}").show();
                    $("#mobile-unfollow-{{ bagger.id }}").hide();
                    $("#mobile-follow-{{ bagger.id }}").show();
                }
            });
        });

        $("#follow-{{ bagger.id }}").on('click',function (){
            var btn = $("#follow-{{ bagger.id }}");
            var unfollow_btn = $("#unfollow-{{ bagger.id }}");
            btn.html('<i class="fa fa-spinner fa-spin"></i>');
            $.post('/accounts/follow/{{ bagger.id }}/',function(data){
                if (data == "True"){
                    unfollow_btn.html('You are following');
                    $("#follow-{{ bagger.id }}").hide();
                    $("#unfollow-{{ bagger.id }}").show();
                    $("#mobile-follow-{{ bagger.id }}").hide();
                    $("#mobile-unfollow-{{ bagger.id }}").show();
                }
            });
        });

        $("#mobile-unfollow-{{ bagger.id }}").on('click',function (){
            var btn = $("#mobile-unfollow-{{ bagger.id }}");
            var follow_btn = $("#mobile-follow-{{ bagger.id }}");
            btn.html('<i class="fa fa-spinner fa-spin"></i>');
            $.post('/accounts/unfollow/{{ bagger.id }}/',function(data){
                if (data == "True"){
                    follow_btn.html('follow');
                    $("#mobile-unfollow-{{ bagger.id }}").hide();
                    $("#mobile-follow-{{ bagger.id }}").show();
                    $("#unfollow-{{ bagger.id }}").hide();
                    $("#follow-{{ bagger.id }}").show();
                }
            });
        });

        $("#mobile-follow-{{ bagger.id }}").on('click',function (){
            var btn = $("#mobile-follow-{{ bagger.id }}");
            var unfollow_btn = $("#mobile-unfollow-{{ bagger.id }}");
            btn.html('<i class="fa fa-spinner fa-spin"></i>');
            $.post('/accounts/follow/{{ bagger.id }}/',function(data){
                if (data == "True"){
                    unfollow_btn.html('following');
                    $("#mobile-follow-{{ bagger.id }}").hide();
                    $("#mobile-unfollow-{{ bagger.id }}").show();
                    $("#follow-{{ bagger.id }}").hide();
                    $("#unfollow-{{ bagger.id }}").show();
                }
            });
        });

        loadChallenges(region, regiontype, sort, page);

    });

    function openUrl(url) {
        window.location.href = url;
    }

    function hasClass(element, cls) {
        return (' ' + element.className + ' ').indexOf(' ' + cls + ' ') > -1;
    }

</script>

{% endblock %}
