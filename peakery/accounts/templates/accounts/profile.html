{% extends "base.html" %}
{% load static %}
{% load account_tags item_tags %}

{% block css %}
    <link href="{{MEDIA_URL}}css/view-peak.css?v=1" rel="stylesheet" type="text/css" media="screen" />
    <link href="{{MEDIA_URL}}css/lightboxes/django-follow.css" rel="stylesheet" type="text/css" media="screen" />
    <link href="{{MEDIA_URL}}css/jquery.lightbox-0.5.css" rel="stylesheet" type="text/css" media="screen" />
    <link href="{{MEDIA_URL}}css/jquery.lightbox-0.5.css" rel="stylesheet" type="text/css" media="screen" />
    <link href="{{MEDIA_URL}}css/tipsy.css" rel="stylesheet" type="text/css" media="screen" />
{% endblock %}

{% load avatar_tags %}
{% block title %}{{ bagger }}'s info & stats{% endblock %}
{% block titlemeta %}{{ bagger }}'s info & stats{% endblock %}
{% block description %}{{ bagger }}'s mountain hikes and climbs. Includes stats, badges, map of peaks climbed, trip reports, and photos by {{ bagger }}.{% endblock %}
{% block image_rel %}{% if bagger.avatar %} {% avatar_url bagger 135 %}{% endif %}{% endblock %}

{% block mapbox %}
{% include "mapbox/mapbox.html" %}
{% endblock %}

{% block extrajs %}
    <script type="text/javascript" src="{% static 'js/jquery.lightbox-0.5.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/jquery.tipsy.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/handlebars.1.0.0.beta.3.js' %}"></script>
{% endblock %}

{% block profile_active %}{% if user == bagger %}active{% endif %}{% endblock %}

{% block mobile_header_follow %}
<a href="javascript:void(0);" id="mobile-follow-{{ bagger.id }}" class="followButton nonmember-links" style="font-size: 11px; font-weight: 300; margin-left: 15px; display: none;">follow</a><a href="javascript:void(0);" id="mobile-unfollow-{{ bagger.id }}" class="unfollowButton nonmember-links" style="font-size: 11px; font-weight: 300; margin-left: 15px; color: #00b330; display: none;">following</a><a class="member-links" href="/members/{{ bagger }}/edit" style="font-size: 11px; font-weight: 300; margin-left: 15px;">edit</a>
{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row hidden-xs">
        <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 65px; border-radius: 12px 12px 0 0;"><span style="font-size: 20px; font-weight: 600;">
            <div id="breadcrumbs" style="margin-left: -5px;">
                <ul>
                    <li style="margin-top: 2px;"><a class="modal-link" data-toggle="modal" data-target="#avatar-modal"><img style="height: 50px; width: 50px; border-radius: 10px;" src="{{ MEDIA_URL }}{{ avatar.avatar_url|urlencode }}"></a><div style="float: right; margin-left: 15px; font-weight: 600; font-size: 20px;">{{ bagger }}<a class="profile-edit-link hidden-xs member-links" style="font-size: 12px; margin-left: 10px;" href="/members/{{ bagger }}/edit">edit</a></div></li>
                </ul>
                <div class="pull-right hidden-xs" style="margin-top: -5px;">
                    <a class="region-header-sub-links" href="/members/{{ bagger }}/">Info</a><a class="region-header-sub-links" href="/members/{{ bagger }}/map/">Map</a><a class="region-header-sub-links" href="/members/{{ bagger }}/badges/">Badges</a><a class="region-header-sub-links" href="/members/{{ bagger }}/summits/">Climbs</a><a class="region-header-sub-links" href="/members/{{ bagger }}/challenges/">Challenges</a><a class="region-header-sub-links" href="/members/{{ bagger }}/photos/">Photos</a><div class="region-header-sub-links hidden-xs hidden-sm nonmember-links" style="float: right; margin-top: 3px;"><a href="javascript:void(0);" id="follow-{{ bagger.id }}" class="peakeryFollowButton followButton btn btn-secondary" style="font-size: 12px; float: right; width: 165px; display: none;">Follow</a><a href="javascript:void(0);" id="unfollow-{{ bagger.id }}" class="peakeryFollowButton btn btn-secondary unfollowButton" style="font-size: 12px; float: right; width: 165px; background-color: rgb(0, 179, 48); border-color: rgb(0, 179, 48); display: none;">You are following</a></div>
                </div>
            </div>
        </span></div>
    </div>
{% endblock %}

{% block content %}

    <style type="text/css">

        body.modal-open {
            overflow: visible;
        }

        .member-links, .nonmember-links, .nonlogin-links {
            display: none;
        }

        #photos-list {
            background-color: #f2f2f2;
        }

        @media screen and (min-width: 768px) {
            .content-pane {
                margin-top: 0px;
            }
        }

        @media screen and (max-width: 767px) and (min-width: 1px) {
            #content-body {
                margin-top: 20px;
            }
            .content-pane {
                margin-top: 50px;
            }
            .peakeryFollowButton {
                font-size: 10px !important;
                width: 130px !important;
            }
            .avatar-modal-dialog {
                width: 300px !important;
            }
            .big-avatar-img {
                height: 260px;
                width: 260px;
            }
            .no-border-mobile {
                border: none !important;
            }
            .summit-card-peak-stats {
                position: initial;
            }
            .summit-card-peak-title {
                position: relative;
            }
        }

        @media screen and (max-width: 1023px) and (min-width: 768px) {
            .modal-dialog {
                width: 700px;
            }
            .avatar-modal-dialog {
                width: 430px !important;
            }
            .big-avatar-img {
                height: 400px;
                width: 400px;
            }
        }

        @media screen and (min-width: 1024px) {
            .modal-dialog {
                width: 800px;
            }
            .avatar-modal-dialog {
                width: 430px !important;
            }
            .big-avatar-img {
                height: 400px;
                width: 400px;
            }
            .latest-peaks-summits {
                font-size: 16px !important;
                font-weight: 300 !important;
            }
        }

    </style>

<div class="container">

    <!-- Mobile header -->
    <div class="row sub-header-row hidden-lg hidden-md hidden-sm" style="height: 50px; position: fixed; width: 100%; z-index: 2;">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999; display: flex; justify-content: space-between;">
            <a style="{{ subnav_info_style }}" class="mobile-header-sub-links" href="/members/{{ bagger }}/">Info</a>{% if IS_MOBILE_APP_ACCESS != 'True' %}<a class="mobile-header-sub-links" href="/members/{{ bagger }}/map/">Map</a>{% endif %}<a class="mobile-header-sub-links" href="/members/{{ bagger }}/badges/">Badges</a><a class="mobile-header-sub-links" href="/members/{{ bagger }}/summits/">Climbs</a><a class="mobile-header-sub-links" href="/members/{{ bagger }}/challenges/">Challenges</a><a class="mobile-header-sub-links" href="/members/{{ bagger }}/photos/">Photos</a>
        </div>
    </div>
    <!-- End mobile header -->

    <!-- Top photos block -->
    {% if photos_added %}
    <div id="photos-list" class="row content-pane">
        {% for p in photos_added|slice:"0:1" %}
            <a href="/{{ p.slug }}/">
                <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-1 hover-photos" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ MEDIA_URL }}{{ p.thumbnail_url }}'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;">
                    <div class="top-photos">
                        <div>
                            <div>
                                <img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive">
                            </div>
                        </div>
                        <div class="user-photo-info">
                            <span class="data photo-caption" style="position: absolute; bottom: -5px; left: 10px; color: #fff;">
                                <p class="bagger" style="font-size: 10px;">{{ p.peak_name }}</p>
                            </span>
                        </div>
                    </div>
                </div>
            </a>
        {% endfor %}
        {% for p in photos_added|slice:"1:2" %}
            <a href="/{{ p.slug }}/">
                <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-2 hover-photos" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ MEDIA_URL }}{{ p.thumbnail_url }}'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;">
                    <div class="top-photos">
                        <div>
                            <div>
                                <img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive">
                            </div>
                        </div>
                        <div class="user-photo-info">
                            <span class="data photo-caption" style="position: absolute; bottom: -5px; left: 10px; color: #fff;">
                                <p class="bagger" style="font-size: 10px;">{{ p.peak_name }}</p>
                            </span>
                        </div>
                    </div>
                </div>
            </a>
        {% endfor %}
        {% for p in photos_added|slice:"2:3" %}
            <a href="/{{ p.slug }}/">
                <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-3 hidden-xs hover-photos" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ MEDIA_URL }}{{ p.thumbnail_url }}'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;">
                    <div class="top-photos">
                        <div>
                            <div>
                                <img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive">
                            </div>
                        </div>
                        <div class="user-photo-info">
                            <span class="data photo-caption" style="position: absolute; bottom: -5px; left: 10px; color: #fff;">
                                <p class="bagger" style="font-size: 10px;">{{ p.peak_name }}</p>
                            </span>
                        </div>
                    </div>
                </div>
            </a>
        {% endfor %}
        {% for p in photos_added|slice:"3:4" %}
            <a href="/{{ p.slug }}/">
                <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-4 hidden-xs hidden-sm hover-photos" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ MEDIA_URL }}{{ p.thumbnail_url }}'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;">
                    <div class="top-photos">
                        <div>
                            <div>
                                <img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive">
                            </div>
                        </div>
                        <div class="user-photo-info">
                            <span class="data photo-caption" style="position: absolute; bottom: -5px; left: 10px; color: #fff;">
                                <p class="bagger" style="font-size: 10px;">{{ p.peak_name }}</p>
                            </span>
                        </div>
                    </div>
                </div>
            </a>
        {% endfor %}
    </div>
    {% else %}
    <div id="photos-list" class="row content-pane"></div>
    <!-- End top photos block -->
    {% endif %}

    <div class="row">
        {% if profile_stats.peaks_bagged > 0 %}
        <div onclick="openUrl('/members/{{ bagger }}/badges/');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell" style="cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-giant-stats">Peaks climbed</div>
                <p class="profile-giant-red"><a style="color: {{ peaks_bagged_color }};" href="/members/{{ bagger }}/badges/">{{ profile_stats.peaks_bagged }}</a></p>
                <p class="profile-data-bottom"><a id="peaks-bagged-rank" href=""></a></p>
            </div>
        </div>
        {% else %}
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell" style="cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-giant-stats">Peaks climbed</div>
                <p class="profile-giant-red">0</p>
            </div>
        </div>
        {% endif %}
        {% if profile_stats.total_summits > 0 %}
        <div onclick="openUrl('/members/{{ bagger }}/badges/');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell" style="cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-giant-stats">Total climbs</div>
                <p class="profile-giant-blue"><a href="/members/{{ bagger }}/badges/">{{ profile_stats.total_summits }}</a></p>
                {% if profile_stats.total_attempts > 0 %}<p class="profile-data-bottom"><a href="/members/{{ bagger }}/summits/#order=attempts&page=1">also {{ profile_stats.total_attempts }} attempts</a></p>{% endif %}
            </div>
        </div>
        {% else %}
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell" style="cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-giant-stats">Total climbs</div>
                <p class="profile-giant-blue">0</p>
            </div>
        </div>
        {% endif %}
        {% if profile_stats.total_photos > 0 %}
        <div onclick="openUrl('/members/{{ bagger }}/photos/');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell" style="cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-giant-stats">Photos added</div>
                <p class="profile-giant-blue"><a href="/members/{{ bagger }}/photos/">{{ profile_stats.total_photos }}</a></p>
                <p class="profile-data-bottom"><a href="/members/{{ bagger }}/photos/">see all photos</a></p>
            </div>
        </div>
        {% else %}
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell" style="cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-giant-stats">Photos added</div>
                <p class="profile-giant-blue">0</p>
            </div>
        </div>
        {% endif %}

        <div id="last-summit-div" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell" style="border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-giant-stats">Days since last climb</div>
                <p id="days-since-last-summit" class="profile-giant-blue"></p>
                <p id="last-summit-link-div" class="profile-data-bottom"><a id="last-summit-link" href=""></a></p>
                <p id="last-summit-missing" class="profile-data-missing" style="display: none;">no info yet</p>
            </div>
        </div>

        <div class="col-lg-6 col-md-6 col-sm-8 col-xs-12 peakimg-responsive peakimgdata-responsive hover-cell" style="border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0; aspect-ratio:8/3; overflow:scroll;">
            <div>
                <div class="profile-header-with-text">About {{ bagger }}<div class="pull-right"><a class="profile-edit-link member-links" href="/members/{{ bagger }}/edit">edit</a></div></div>
                {% if profile.about_me %}
                <p id="about-bagger-teaser-mobile" class="profile-about-bagger hidden-lg hidden-md hidden-sm">{{ profile.about_me|truncatechars:195 }}{% if profile.about_me|length > 195 %} <a style="cursor: pointer;" class="more-about-bagger">more</a>{% endif %}</p>
                <p id="about-bagger-teaser" class="profile-about-bagger hidden-xs">{{ profile.about_me|truncatechars:200 }}{% if profile.about_me|length > 200 %} <a style="cursor: pointer;" class="more-about-bagger">more</a>{% endif %}</p>
                <p id="about-bagger-complete" style="display: none;" class="profile-about-bagger">{{ profile.about_me }} <a style="cursor: pointer;" class="less-about-bagger">less</a></p>
                {% else %}
                    <p class="profile-data-missing">no info yet</p>
                {% endif %}
                <p id="about-bagger-social" class="profile-data-bottom">
                    {% if profile.facebook_page %}<a class="profile-social-icons" href="{{ profile.facebook_page }}" target="_blank"><i class="fab fa-facebook" aria-hidden="true"></i></a>{% endif %}
                    {% if profile.twitter_username %}<a class="profile-social-icons" href="https://twitter.com/{{ profile.twitter_username }}" target="_blank"><i class="fab fa-twitter" aria-hidden="true"></i></a>{% endif %}
                    {% if profile.instagram_username %}<a class="profile-social-icons" href="https://www.instagram.com/{{ profile.instagram_username }}" target="_blank"><i class="fab fa-instagram" aria-hidden="true"></i></a>{% endif %}
                    {% if profile.flicker_page %}<a class="profile-social-icons" href="{{ profile.flicker_page }}" target="_blank"><i class="fab fa-flickr" aria-hidden="true"></i></a>{% endif %}
                    {% if profile.website %}<a class="profile-social-icons profile-social-icon-website" href="{{ profile.website }}" target="_blank"><i class="fa fa-desktop" aria-hidden="true"></i></a>{% endif %}
                </p>
            </div>
        </div>

        {% if profile.location_name %}
        <div onclick="openUrl('/map/#lat={{ profile_stats.basecamp_x }}&lng={{ profile_stats.basecamp_y }}&n={{ profile.location_name|urlencode }}');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell" style="cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-text">Home basecamp<div class="pull-right"><a class="profile-edit-link member-links" href="/members/{{ bagger }}/edit">edit</a></div></div>
                <p class="profile-data-highlight profile-data-basecamp">{{ profile.location_name }}</p>
                <p class="profile-data-bottom">member since {{ profile.created|date:"M Y" }}</p>
            </div>
        </div>
        {% else %}
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell" style="cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-text">Home basecamp<div class="pull-right"><a class="profile-edit-link member-links" href="/members/{{ bagger }}/edit">edit</a></div></div>
                <p class="profile-data-missing">no info yet</p>
                <p class="profile-data-bottom">member since {{ profile.created|date:"M Y" }}</p>
            </div>
        </div>
        {% endif %}

        <div id="followers-following-stat" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell has-followers" style="display: none; cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-giant-stats">Followers / Following</div>
                <p class="profile-giant-blue profile-followers-stats"><span class="followers-count"></span> / <span class="followings-count"></span></p>
                <p class="profile-data-bottom hidden-xs">see all followers and following</p>
            </div>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell has-no-followers" style="border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-giant-stats">Followers / Following</div>
                <p class="profile-giant-blue profile-followers-stats"><span class="followers-count"></span> / <span class="followings-count"></span></p>
            </div>
        </div>

        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell" style="border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-text">Awards<div class="pull-right"><a class="profile-modal-link" data-toggle="modal" data-target="#about-awards">about</a></div></div>

                <table class="profile-stats-table" style="width: 100%;">
                    <tr class="stats-data profile-awards-stats-data">
                        <td><a href="/members/{{ bagger }}/badges/#type=kom&order=latest&page=1"><img class="profile-awards-icon-kom" src="{% static 'img/profile_award_crown.png' %}"><span class="profile-subdata-highlight profile-award-names">King of Mountain</span></a></td>
                        <td align="right"><div id="kom_count" class="profile-subdata-grey profile-award-stats"></div></td>
                    </tr>

                    <tr class="stats-data profile-awards-stats-data">
                        <td><a href="/members/{{ bagger }}/badges/#type=first&order=latest&page=1"><img class="profile-awards-icon-first" src="{% static 'img/profile_award_flag.png' %}"><span class="profile-subdata-highlight profile-award-names">First Ascent</span></a></td>
                        <td align="right"><div id="first_ascents_count" class="profile-subdata-grey profile-award-stats"></div></td>
                    </tr>

                    <tr class="stats-data profile-awards-stats-data">
                        <td><a href="/members/{{ bagger }}/badges/#type=steward&order=latest&page=1"><img class="profile-awards-icon-steward" src="{% static 'img/profile_award_shield.png' %}"><span class="profile-subdata-highlight profile-award-names">Summit Steward</span></a></td>
                        <td align="right"><div id="summit_stewards_count" class="profile-subdata-grey profile-award-stats"></div></td>
                    </tr>

                </table>
            </div>
        </div>

        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell" style="border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-text">Vertical gain<div class="pull-right"><a class="profile-modal-link" data-toggle="modal" data-target="#about-vert-distance">about</a></div></div>
                <table class="profile-stats-table">
                {% if summit_stats.elevation_gain_all_time > 0 or summit_stats.elevation_gain_90_days > 0 or summit_stats.elevation_gain_30_days > 0 %}
                    {% if summit_stats.elevation_gain_30_days > 0 %}
                    <tr class="stats-data profile-awards-stats-data hidden-xs">
                        <td>{% ft_m_by_ft summit_stats.elevation_gain_30_days %}</td>
                        <td align="right" class="profile-subdata-grey profile-award-stats">last 30 days</td>
                    </tr>
                    {% endif %}
                    {% if summit_stats.elevation_gain_90_days > 0 %}
                    <tr class="stats-data profile-awards-stats-data hidden-xs">
                        <td>{% ft_m_by_ft summit_stats.elevation_gain_90_days %}</td>
                        <td align="right" class="profile-subdata-grey profile-award-stats">last 90 days</td>
                    </tr>
                    {% endif %}
                    {% if summit_stats.elevation_gain_all_time > 0 %}
                    <tr class="stats-data profile-awards-stats-data hidden-xs">
                        <td>{% ft_m_by_ft summit_stats.elevation_gain_all_time %}</td>
                        <td align="right" class="profile-subdata-grey profile-award-stats">all-time</td>
                    </tr>
                    <tr class="stats-data profile-awards-stats-data hidden-sm hidden-md hidden-lg">
                        <td>{% ft_m_by_ft summit_stats.elevation_gain_all_time %}</td>
                    </tr>
                    <tr class="stats-data profile-awards-stats-data hidden-sm hidden-md hidden-lg">
                        <td>all-time</td>
                    </tr>
                    {% endif %}
                {% else %}
                    <tr><td class="profile-data-missing">no info yet</td></tr>
                {% endif %}
                </table>
            </div>
        </div>

        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell" style="border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-text">Distance<div class="pull-right"><a class="profile-modal-link" data-toggle="modal" data-target="#about-vert-distance">about</a></div></div>
                <table class="profile-stats-table">
                {% if summit_stats.total_distance_all_time > 0 or summit_stats.total_distance_90_days > 0 or summit_stats.total_distance_30_days > 0 %}
                    {% if summit_stats.total_distance_30_days > 0 %}
                    <tr class="stats-data profile-awards-stats-data hidden-xs">
                        <td>{% mi_km_by_mi_int summit_stats.total_distance_30_days %}</td>
                        <td align="right" class="profile-subdata-grey profile-award-stats">last 30 days</td>
                    </tr>
                    {% endif %}
                    {% if summit_stats.total_distance_90_days > 0 %}
                    <tr class="stats-data profile-awards-stats-data hidden-xs">
                        <td>{% mi_km_by_mi_int summit_stats.total_distance_90_days %}</td>
                        <td align="right" class="profile-subdata-grey profile-award-stats">last 90 days</td>
                    </tr>
                    {% endif %}
                    {% if summit_stats.total_distance_all_time > 0 %}
                    <tr class="stats-data profile-awards-stats-data hidden-xs">
                        <td>{% mi_km_by_mi_int summit_stats.total_distance_all_time %}</td>
                        <td align="right" class="profile-subdata-grey profile-award-stats">all-time</td>
                    </tr>
                    <tr class="stats-data profile-awards-stats-data hidden-sm hidden-md hidden-lg">
                        <td>{% mi_km_by_mi_int summit_stats.total_distance_all_time %}</td>
                    </tr>
                    <tr class="stats-data profile-awards-stats-data hidden-sm hidden-md hidden-lg">
                        <td>all-time</td>
                    </tr>
                    {% endif %}
                {% else %}
                    <tr><td class="profile-data-missing">no info yet</td></tr>
                {% endif %}
                </table>
            </div>
        </div>

        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell" style="border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-text">Difficulty<div class="pull-right"><a class="profile-modal-link" data-toggle="modal" data-target="#about-vert-distance">about</a></div></div>
                <table class="profile-stats-table">
                {% if summit_difficulty.class_one_two_count > 0 or summit_difficulty.class_three_four_count > 0 or summit_difficulty.class_five_count > 0 %}
                    {% if summit_difficulty.class_one_two_count > 0 %}
                    <tr class="stats-data profile-awards-stats-data">
                        <td>Class 1/2</td>
                        <td align="right" class="profile-subdata-grey profile-award-stats">{{ summit_difficulty.class_one_two_count }} peak{{ summit_difficulty.class_one_two_count|pluralize:"s" }}</td>
                    </tr>
                    {% endif %}
                    {% if summit_difficulty.class_three_four_count > 0 %}
                    <tr class="stats-data profile-awards-stats-data">
                        <td>Class 3/4</td>
                        <td align="right" class="profile-subdata-grey profile-award-stats">{{ summit_difficulty.class_three_four_count }} peak{{ summit_difficulty.class_three_four_count|pluralize:"s" }}</td>
                    </tr>
                    {% endif %}
                    {% if summit_difficulty.class_five_count > 0 %}
                    <tr class="stats-data profile-awards-stats-data">
                        <td>Class 5+</td>
                        <td align="right" class="profile-subdata-grey profile-award-stats">{{ summit_difficulty.class_five_count }} peak{{ summit_difficulty.class_five_count|pluralize:"s" }}</td>
                    </tr>
                    {% endif %}
                {% else %}
                    <tr><td class="profile-data-missing">no info yet</td></tr>
                {% endif %}
                </table>
            </div>
        </div>

        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell" style="height: 325px; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-text profile-top-stats-div">Top countries</div>
                {% for c in top_countries %}
                    <div class="profile-top-stats-div">
                        <span class="profile-subdata-highlight profile-top-stats">
                            <div class="pull-right profile-subdata-grey hidden-xs">{{ c.peak_count }} peak{{ c.peak_count|pluralize:"s" }}</div>
                            <div class="ellipsis" style="overflow: hidden; color: #00B1F2; padding-right: 5px;"><a href="/region/{{ c.slug }}-mountains/">{{ c.country }}</a></div>
                        </span>
                    </div>
                {% empty %}
                    <p class="profile-data-missing">no info yet</p>
                {% endfor %}
            </div>
        </div>

        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell" style="height: 325px; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-text profile-top-stats-div">Top regions</div>
                {% for r in top_regions %}
                    <div class="profile-top-stats-div">
                        <span class="profile-subdata-highlight profile-top-stats">
                            <div class="pull-right profile-subdata-grey hidden-xs">{{ r.peak_count }} peak{{ r.peak_count|pluralize:"s" }}</div>
                            <div class="ellipsis" style="overflow: hidden; color: #00B1F2; padding-right: 5px;"><a href="/{{ r.country_slug }}-mountains/{{ r.region_slug }}">{{ r.region }}</a></div>
                        </span>
                    </div>
                {% empty %}
                    <p class="profile-data-missing">no info yet</p>
                {% endfor %}
            </div>
        </div>

        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell hidden-sm" style="height: 325px; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-text profile-top-stats-div">Top ranges</div>
                {% for r in top_ranges %}
                    <div class="profile-top-stats-div">
                        <span class="profile-subdata-black profile-top-stats">
                            <div class="pull-right profile-subdata-grey hidden-xs">{{ r.peak_count }} peak{{ r.peak_count|pluralize:"s" }}</div>
                            <div class="ellipsis" style="overflow: hidden; color: #00B1F2; padding-right: 5px;"><a href="/peaks/#range={{ r.range|urlencode }}">{{ r.range }}</a></div>
                        </span>
                    </div>
                {% empty %}
                    <p class="profile-data-missing">no info yet</p>
                {% endfor %}
            </div>
        </div>

        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell" style="height: 325px; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-text profile-top-stats-div">Top companions</div>
                {% for t in top_companions %}
                    <div class="profile-top-stats-div top-companions-div">
                        <a style="float: left;" href="/members/{{ t.username }}"><img src="{{ MEDIA_URL }}{{ t.avatar_url|urlencode }}" style="height: 30px; margin-right: 15px;"></a>
                        <div class="profile-subdata-highlight profile-top-stats">
                            <div class="pull-right profile-subdata-grey hidden-xs">{{ t.peak_count }} peak{{ t.peak_count|pluralize:"s" }}</div>
                            <div class="ellipsis" style="overflow: hidden; color: #00B1F2; padding-right: 5px;"><a href="/members/{{ t.username }}">{{ t.username }}</a></div>
                        </div>
                    </div>
                {% empty %}
                    <p class="profile-data-missing">no info yet</p>
                {% endfor %}
            </div>
        </div>

    </div>

    <div class="row dark-background-row">
        <div class="sp-60"></div>
    </div>

    <div class="row" style="background-color: #fff;">
        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12" style="border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0; padding-bottom: 20px;">
            <div>
                <div class="profile-header-with-text profile-top-stats-div" style="margin-bottom: 0px;">Latest climbs</div>
                {% for s in latest_summits %}
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 summit-card-header" style="background-color: #fff; letter-spacing: 1.0px; padding-left: 0px; padding-right: 0px;">
                        <div onclick="openUrl('/{{ s.summit.peak_slug }}');" class="hover-cell stats-header summit-card-peak-title" style="height: 70px; margin-top: 0px; margin-bottom: 0px; cursor: pointer;">
                            <div class="summit-list-stats summit-card-peak-stats" style="float: right; letter-spacing: .3px;">
                                {% if s.summit.attempt %}<span style="color: #ff0000;">Attempt</span>&nbsp;•&nbsp;{% endif %}{{ s.summit_elevation }}&nbsp;•&nbsp;{{ s.summit_region }}&nbsp;•&nbsp;<time class="timeago" datetime="{{ s.summit.summitlog_date }}T00:00:00">{{ s.summit.summitlog_date }}</time>
                            </div>
                            <div class="ellipsis" style="overflow: hidden;">
                                <img src="{{ MEDIA_URL }}{{ s.summit_photo }}" style="height: 50px; width: 70px;">
                                <a class="latest-peaks-summits" href="/{{ s.summit.peak_slug }}">{{ s.summit.peak_name }}</a>
                            </div>
                        </div>
                    </div>
                {% empty %}
                    <p class="profile-data-missing">no info yet</p>
                {% endfor %}
            </div>
        </div>
        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12" style="border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0; padding-bottom: 20px;">
            <div>
                <div class="profile-header-with-text profile-top-stats-div" style="margin-bottom: 0px;">Latest challenges</div>
                {% for c in latest_challenges %}
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 summit-card-header" style="background-color: #fff; letter-spacing: 1.0px; padding-left: 0px; padding-right: 0px;">
                        <div onclick="openUrl('/challenges/{{ c.challenge.slug }}');" class="hover-cell stats-header summit-card-peak-title" style="height: 70px; margin-top: 0px; margin-bottom: 0px; cursor: pointer;">
                            <div class="summit-list-stats summit-card-peak-stats" style="float: right; letter-spacing: .3px;">{% if c.current_round_number > 1 %}Round {{ c.current_round_number }}&nbsp;•&nbsp;{% endif %}{{ c.current_round_count }} of {{ c.challenge.total_peaks }} peaks / {{ c.current_round_pct }}%</div>
                            <div class="ellipsis" style="overflow: hidden;">
                                <img src="{{ MEDIA_URL }}images/{{ c.challenge.thumbnail }}" style="height: 50px; width: 70px;">
                                <a class="latest-peaks-summits" href="/challenges/{{ c.challenge.slug }}">{{ c.challenge.name }}</a>
                            </div>
                        </div>
                    </div>
                {% empty %}
                    <p class="profile-data-missing">no info yet</p>
                {% endfor %}
            </div>
        </div>
    </div>

    <div class="row dark-background-row">
        <div class="sp-60"></div>
    </div>

    <div class="row">

        {% if highest_peak_bagged %}
        <a href="/{{ highest_peak_bagged.slug }}">
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-1 {{ highest_peak_bagged.hover_class }}" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ MEDIA_URL }}{{ highest_peak_bagged.thumbnail_url }}'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;">
            <div class="top-photos">
                <div>
                    <div>
                        <img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive">
                    </div>
                </div>
                <div class="{{ highest_peak_bagged.info_class }}">
                    <span class="data photo-caption" style="position: absolute; bottom: -5px; left: 10px; color: #fff;">
                        <p class="bagger ellipsis" style="font-size: 10px;">{{ highest_peak_bagged.name }}</p>
                    </span>
                </div>
            </div>
        </div>
        </a>
        {% else %}
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-1 hidden-sm hidden-xs" style="padding-right: 0px; padding-left: 0px; background-image: url('{% static 'img/default.png' %}'); background-size: cover;">
            <div class="top-photos">
                <div>
                    <div>
                        <img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive">
                    </div>
                </div>
                <div class="empty-photo-info"></div>
            </div>
        </div>
        {% endif %}

        {% if most_prominent_peak_bagged %}
        <a href="/{{ most_prominent_peak_bagged.slug }}">
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-2 hidden-xs {{ most_prominent_peak_bagged.hover_class }}" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ MEDIA_URL }}{{ most_prominent_peak_bagged.thumbnail_url }}'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;">
            <div class="top-photos">
                <div>
                    <div>
                        <img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive">
                    </div>
                </div>
                <div class="{{ most_prominent_peak_bagged.info_class }}">
                    <span class="data photo-caption" style="position: absolute; bottom: -5px; left: 10px; color: #fff;">
                        <p class="bagger ellipsis" style="font-size: 10px;">{{ most_prominent_peak_bagged.name }}</p>
                    </span>
                </div>
            </div>
        </div>
        </a>
        {% else %}
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-2 hidden-sm hidden-xs" style="padding-right: 0px; padding-left: 0px; background-image: url('{% static 'img/default.png' %}'); background-size: cover;">
            <div class="top-photos">
                <div>
                    <div>
                        <img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive">
                    </div>
                </div>
                <div class="empty-photo-info"></div>
            </div>
        </div>
        {% endif %}

        {% if most_summited_peak %}
        <a href="/{{ most_summited_peak.slug }}">
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-3 no-border-mobile {{ most_summited_peak.hover_class }}" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ MEDIA_URL }}{{ most_summited_peak.thumbnail_url }}'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;">
            <div class="top-photos">
                <div>
                    <div>
                        <img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive">
                    </div>
                </div>
                <div class="{{ most_summited_peak.info_class }}">
                    <span class="data photo-caption" style="position: absolute; bottom: -5px; left: 10px; color: #fff;">
                        <p class="bagger ellipsis" style="font-size: 10px;">{{ most_summited_peak.name }}</p>
                    </span>
                </div>
            </div>
        </div>
        </a>
        {% else %}
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-3 hidden-sm hidden-xs" style="padding-right: 0px; padding-left: 0px; background-image: url('{% static 'img/default.png' %}'); background-size: cover;">
            <div class="top-photos">
                <div>
                    <div>
                        <img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive">
                    </div>
                </div>
                <div class="empty-photo-info"></div>
            </div>
        </div>
        {% endif %}

        {% if favorite_item.id > 0 %}
        <a href="/{{ favorite_item.slug }}">
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-4 hidden-sm hidden-xs {{ favorite_item.hover_class }}" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ favorite_item.thumbnail_url }}'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;">
            <div class="top-photos">
                <div>
                    <div>
                        <img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive">
                    </div>
                </div>
                <div class="{{ favorite_item.info_class }}">
                    <span class="data photo-caption" style="position: absolute; bottom: -5px; left: 10px; color: #fff;">
                        <p class="bagger ellipsis" style="font-size: 10px;">{{ favorite_item.name }}</p>
                    </span>
                </div>
            </div>
        </div>
        </a>
        {% else %}
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-4 hidden-sm hidden-xs" style="padding-right: 0px; padding-left: 0px; background-image: url('{% static 'img/default.png' %}'); background-size: cover;">
            <div class="top-photos">
                <div>
                    <div>
                        <img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive">
                    </div>
                </div>
                <div class="empty-photo-info"></div>
            </div>
        </div>
        {% endif %}

        {% if highest_peak_bagged %}
        <div onclick="openUrl('/{{ highest_peak_bagged.slug }}');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell" style="cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-text">Highest peak</div>
                <p class="profile-data-highlight ellipsis"><a href="/{{ highest_peak_bagged.slug }}">{{ highest_peak_bagged.name }}</a></p>
                <p class="profile-data-black">{{ highest_peak_bagged.elevation }}</p>
                <p class="profile-data-bottom">feels like {{ highest_peak_bagged.feels_like }}% of sea level O<sub>2</sub></p>
            </div>
        </div>
        {% else %}
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell" style="border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-text">Highest peak</div>
                <p class="profile-data-missing">no info yet</p>
            </div>
        </div>
        {% endif %}

        {% if most_prominent_peak_bagged %}
        <div onclick="openUrl('/{{ most_prominent_peak_bagged.slug }}');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell hidden-xs" style="cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-text">Most prominent peak</div>
                <p class="profile-data-highlight ellipsis"><a href="/{{ most_prominent_peak_bagged.slug }}">{{ most_prominent_peak_bagged.name }}</a></p>
                <p class="profile-data-black">{{ most_prominent_peak_bagged.elevation }} prom.</p>
            </div>
        </div>
        {% else %}
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell hidden-xs" style="border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-text">Most prominent peak</div>
                <p class="profile-data-missing">no info yet</p>
            </div>
        </div>
        {% endif %}

        {% if most_summited_peak %}
        <div onclick="openUrl('/{{ most_summited_peak.slug }}');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell" style="cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-text">Most climbed peak</div>
                <p class="profile-data-highlight ellipsis"><a href="/{{ most_summited_peak.slug }}">{{ most_summited_peak.name }}</a></p>
                <p class="profile-data-black">{{ most_summited_peak.elevation }}</p>
                <p class="profile-data-bottom">{{ most_summited_peak.summit_count }}x summited</p>
            </div>
        </div>
        {% else %}
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell" style="border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-text">Most climbed peak</div>
                <p class="profile-data-missing">no info yet</p>
            </div>
        </div>
        {% endif %}

        {% if favorite_item.id > 0 %}
        <div onclick="openUrl('/{{ favorite_item.slug }}');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell hidden-sm hidden-xs" style="cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-text">Favorite peak<div class="pull-right"><a class="profile-edit-link member-links" href="/members/{{ bagger }}/edit">edit</a></div></div>
                <p class="profile-data-highlight ellipsis"><a href="/{{ favorite_item.slug }}">{{ favorite_item.name }}</a></p>
                <p class="profile-data-black">{{ favorite_item.elevation }}</p>
            </div>
        </div>
        {% else %}
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell hidden-sm hidden-xs" style="border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-text">Favorite peak<div class="pull-right"><a class="profile-edit-link member-links" href="/members/{{ bagger }}/edit">edit</a></div></div>
                <p class="profile-data-missing">no info yet</p>
            </div>
        </div>
        {% endif %}

    </div>

    <div class="row dark-background-row">
        <div class="sp-60"></div>
    </div>

    <div class="row">

        {% if profile_stats.basecamp_x != 0 %}
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive photo-grid-1" style="padding-right: 0px; padding-left: 0px; background-image: url('{% static 'img/default.png' %}'); background-size: cover;">
            <div id="map-canvas" style="width: 320px; height: 240px; cursor: pointer;"></div>
            <div id="marker-tooltip" data-url="" style="cursor: pointer; display: none; position:absolute; width: 200px; height: 150px; background: rgba(0, 0, 0, 0.3); margin: 15px;"></div>
        </div>
        {% else %}
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive photo-grid-1" style="padding-right: 0px; padding-left: 0px; background-image: url('{% static 'img/default.png' %}'); background-size: cover;">
            <div class="top-photos">
                <div>
                    <div>
                        <img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive">
                    </div>
                </div>
                <div class="empty-photo-info"></div>
            </div>
        </div>
        {% endif %}

        {% if nearest_peak %}
        <a href="/{{ nearest_peak.slug }}">
        <div id="nearest-to-home-goal-div" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive photo-grid-2 {{ nearest_peak.hover_class }}" style="padding-right: 0px; padding-left: 0px;
                background-image: url('{% if 'default' not in nearest_peak.thumbnail_url %}{{ MEDIA_URL }}{{ nearest_peak.thumbnail_url }}{% else %}{% static 'img/default.png' %}{% endif %}'); background-size: cover;">
            <div class="top-photos">
                <div class="{{ nearest_peak.info_class }}">
                    <span class="data photo-caption" style="position: absolute; bottom: -5px; left: 10px; color: #fff;">
                        <p class="bagger ellipsis" style="font-size: 10px;">{{ nearest_peak.name }}</p>
                    </span>
                </div>
            </div>
        </div>
        </a>
        {% else %}
        <div id="nearest-to-home-goal-div" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive photo-grid-2" style="padding-right: 0px; padding-left: 0px; background-image: url('{% static 'img/default.png' %}'); background-size: cover;">
            <div class="top-photos">
                <div>
                    <div>
                        <img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive">
                    </div>
                </div>
                <div class="empty-photo-info"></div>
            </div>
        </div>
        {% endif %}

        {% if nearest_peak_not_summited %}
        <a href="/{{ nearest_peak_not_summited.slug }}">
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive photo-grid-3 hidden-xs {{ nearest_peak_not_summited.hover_class }}" style="padding-right: 0px; padding-left: 0px;
                background-image: url('{% if 'default' not in nearest_peak_not_summited.thumbnail_url %}{{ MEDIA_URL }}{{ nearest_peak_not_summited.thumbnail_url }}{% else %}{% static 'img/default.png' %}{% endif %}'); background-size: cover;">
            <div class="top-photos">
                <div class="{{ nearest_peak_not_summited.info_class }}">
                    <span class="data photo-caption" style="position: absolute; bottom: -5px; left: 10px; color: #fff;">
                        <p class="bagger ellipsis" style="font-size: 10px;">{{ nearest_peak_not_summited.name }}</p>
                    </span>
                </div>
            </div>
        </div>
        </a>
        {% else %}
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive photo-grid-3 hidden-xs" style="padding-right: 0px; padding-left: 0px; background-image: url('{% static 'img/default.png' %}'); background-size: cover;">
            <div class="top-photos">
                <div>
                    <div>
                        <img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive">
                    </div>
                </div>
                <div class="empty-photo-info"></div>
            </div>
        </div>
        {% endif %}

        {% if next_item_goal %}
        <a href="/{{ next_item_goal.slug_new_text }}">
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive photo-grid-4 hover-photos hidden-sm hidden-xs" style="padding-right: 0px; padding-left: 0px; background-image: url('{% static 'img/default.png' %}'); background-size: cover;">
            <div class="top-photos">
                <div>
                    <div>
                        <img src="{{ next_item_goal.get_thumbnail_745 }}" class="img-responsive peakeryPhoto photography peakimg-responsive">
                    </div>
                </div>
            </div>
        </div>
        </a>
        {% else %}
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive photo-grid-4 hidden-sm hidden-xs" style="padding-right: 0px; padding-left: 0px; background-image: url('{% static 'img/default.png' %}'); background-size: cover;">
            <div class="top-photos">
                <div>
                    <div>
                        <img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive">
                    </div>
                </div>
                <div class="empty-photo-info"></div>
            </div>
        </div>
        {% endif %}

    </div>

    <div class="row">

        {% if profile_stats.basecamp_x != 0 %}
        <div onclick="openUrl('/peaks/#sort_key=distance&sort_dir=asc&q=&n={{ profile.location_name|urlencode }}&page=1');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell" style="cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-text profile-completion-radius-header">Completion radius<div class="pull-right"><a class="profile-modal-link profile-about-completion-radius" data-toggle="modal" data-target="#about-completion-radius">about</a></div></div>
                <p class="profile-data-highlight">{{ completion_radius_miles }} mi / {{ completion_radius_km }} km</p>
                <p class="profile-data-black">around basecamp</p>
            </div>
        </div>
        {% else %}
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell" style="border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-text profile-completion-radius-header">Completion radius<div class="pull-right"><a class="profile-modal-link profile-about-completion-radius" data-toggle="modal" data-target="#about-completion-radius">about</a></div></div>
                <p class="profile-data-missing member-links"><a style="color: #BF3929" href="/members/{{ bagger }}/edit">add your home basecamp</a></p>
                <p class="profile-data-missing nonmember-links">not specified</p>
                <p class="profile-data-missing nonlogin-links">not specified</p>
            </div>
        </div>
        {% endif %}

        {% if nearest_peak %}
        <div onclick="openUrl('/{{ nearest_peak.slug }}');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell" style="cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-text">Nearest to home goal</div>
                <p class="profile-data-highlight ellipsis"><a href="/{{ nearest_peak.slug }}">{{ nearest_peak.name }}</a></p>
                <p class="profile-data-black">{{ nearest_peak.elevation }}</p>
                <p class="profile-data-bottom">{{ nearest_peak.distance }} from basecamp</p>
            </div>
        </div>
        {% else %}
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell" style="border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-text">Nearest to home goal</div>
                <p class="profile-data-missing">no info yet</p>
            </div>
        </div>
        {% endif %}

        {% if nearest_peak_not_summited %}
        <div onclick="openUrl('/{{ nearest_peak_not_summited.slug }}');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell hidden-xs" style="cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-text">Nearest First Ascent goal</div>
                <p class="profile-data-highlight ellipsis"><a href="/{{ nearest_peak_not_summited.slug }}">{{ nearest_peak_not_summited.name }}</a></p>
                <p class="profile-data-black">{{ nearest_peak_not_summited.elevation }}</p>
                <p class="profile-data-bottom">{{ nearest_peak_not_summited.distance }} from basecamp</p>
            </div>
        </div>
        {% else %}
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell hidden-xs" style="border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-text">Nearest First Ascent goal</div>
                <p class="profile-data-missing">no info yet</p>
            </div>
        </div>
        {% endif %}

        {% if next_item_goal %}
        <div onclick="openUrl('/{{ next_item_goal.slug_new_text }}');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell hidden-sm hidden-xs" style="cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-text">Next peak goal<div class="pull-right"><a class="profile-edit-link member-links" href="/members/{{ bagger }}/edit">edit</a></div></div>
                <p class="profile-data-highlight ellipsis"><a href="/{{ next_item_goal.slug_new_text }}">{{ next_item_goal.name }}</a></p>
                <p class="profile-data-black">{{ next_item_goal.get_elevation }}</p>
            </div>
        </div>
        {% else %}
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell hidden-sm hidden-xs" style="border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-text">Next summit goal<div class="pull-right"><a class="profile-edit-link member-links" href="/members/{{ bagger }}/edit">edit</a></div></div>
                <p class="profile-data-missing">no info yet</p>
            </div>
        </div>
        {% endif %}

    </div>

    <div class="row dark-background-row">
        <div class="sp-60"></div>
    </div>

    <div id="contributor-stats" class="row" >

        <div onclick="openUrl('/top-contributors/#type=info_corrections');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell" style="cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-giant-stats">Peak info corrections</div>
                <p id="peak-info-corrections-count" class="profile-giant-green"></p>
                <p style="display: none;" id="peak-info-corrections-rank" class="profile-data-bottom"></p>
            </div>
        </div>

        <div onclick="openUrl('/top-contributors/#type=peak_photos');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell" style="cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-giant-stats">Peak photos added</div>
                <p id="peak-photos-count" class="profile-giant-green"></p>
                <p style="display: none;" id="peak-photos-rank" class="profile-data-bottom"></p>
            </div>
        </div>

        <div onclick="openUrl('/top-contributors/#type=peaks_added');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell" style="cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-giant-stats">Missing peaks added</div>
                <p id="peak-count" class="profile-giant-green"></p>
                <p style="display: none;" id="peak-rank" class="profile-data-bottom"></p>
            </div>
        </div>

        <div onclick="openUrl('/top-contributors/#type=routes_added');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive peakimgdata-responsive hover-cell hidden-sm" style="cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            <div>
                <div class="profile-header-with-giant-stats">Routes added</div>
                <p id="summit-routes-count" class="profile-giant-green"></p>
                <p style="display: none;" id="summit-routes-rank" class="profile-data-bottom"></p>
            </div>
        </div>

    </div>

    <div class="row hidden-lg hidden-md hidden-sm">
        <div style="height: 178px;"></div>
    </div>

    <div class="row hidden-xs">
        <div style="height: 54px;"></div>
    </div>

</div>

<div class="about-awards-modal modal fade" id="about-awards" tabindex="-1" role="dialog" aria-labelledby="about-awards-label">
    <div class="modal-dialog" role="document" style="max-width: 600px;">
        <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
              <h4 class="modal-title" id="about-awards-label">About <span style="color: #f24100;">peakery awards</span></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="margin-bottom: 20px; font-size: 21px;">Log climbs on peakery to earn awards:</div>
                </div>
                <div class="row">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2"><img src="{% static 'img/about-awards-badge.png' %}" style="height: 75px;"></div>
                    <div class="col-lg-10 col-md-10 col-sm-10 col-xs-10" style="margin-bottom: 16px; font-size: 15px; margin-top: 10px;"><strong>Peak Badges</strong>: Earn a special badge for each unique peak you summit. See all of your badges on your Badges page.</div>
                </div>
                <div class="row">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2"><img src="{% static 'img/about-awards-flag.png' %}" style="height: 75px;"></div>
                    <div class="col-lg-10 col-md-10 col-sm-10 col-xs-10" style="margin-bottom: 16px; font-size: 15px; margin-top: 10px;"><strong>First Ascent Award</strong>: Only 1 available per peak. Goes to the first peakery member to log a successful summit of a peak. Snag this award and the peak will forever bear your name.</div>
                </div>
                <div class="row">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2"><img src="{% static 'img/about-awards-crown.png' %}" style="height: 75px;"></div>
                    <div class="col-lg-10 col-md-10 col-sm-10 col-xs-10" style="margin-bottom: 16px; font-size: 15px; margin-top: 10px;"><strong>King of the Mountain Award</strong>: Only 1 available per peak. Summit a peak more times than any other member. Beware: this award can be lost!</div>
                </div>
                <div class="row">
                    <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2"><img src="{% static 'img/about-awards-shield.png' %}" style="height: 75px;"></div>
                    <div class="col-lg-10 col-md-10 col-sm-10 col-xs-10" style="margin-bottom: 16px; font-size: 15px; margin-top: 10px;"><strong>Summit Steward</strong>: Summit a peak at least 5 times to become one of its Summit Stewards. As Steward of a peak, you’re encouraged to keep that peak’s info up-to-date on peakery and spread goodwill on your future climbs up the peak.</div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="about-completion-radius-modal modal fade" id="about-completion-radius" tabindex="-1" role="dialog" aria-labelledby="about-completion-radius-label">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
              <h4 class="modal-title" id="about-completion-radius-label">A note on <span style="color: #f24100;">Completion Radius</span></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12" style="margin-bottom: 20px; font-size: 15px; line-height: 32px;">The Completion Radius is the radial distance from a member’s "home basecamp" to the nearest peak they have yet to climb. It encourages local adventures up peaks that may be overlooked. Note: a member’s home basecamp must be specified to calculate their Completion Radius.</div>
                </div>
                <div class="row">
                    <div class="col-md-12" style="text-align: center;">
                        <img src="{% static 'img/about-completion-radius.png' %}">
                        <p>&nbsp;</p>
                        <p style="font-size: 11px; color: #999999;">Example: a member who has climbed all peaks within 6 miles of Calgary, Alberta</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="about-vert-distance-modal modal fade" id="about-vert-distance" tabindex="-1" role="dialog" aria-labelledby="about-vert-distance-label">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
              <h4 class="modal-title" id="about-vert-distance-label">A note on <span style="color: #f24100;">vertical gain, distance, and difficulty</span></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12" style="margin-bottom: 20px; font-size: 15px; line-height: 32px;">
                        <p style="line-height: 32px;">peakery calculates your <strong>vertical gain</strong> and <strong>distance</strong> for various timeframes based on the GPS tracks you add to your summit logs. Only logs with GPS tracks are included in your totals. Add more of your GPS tracks to see these stats grow.</p>
                        <p style="line-height: 32px;">peakery also creates a <strong>difficulty breakdown</strong> of all of the peaks you’ve climbed based on the routes you took. For your climbs to be included in this breakdown, you must specify the routes you took in your summit logs AND these routes must have difficulty ratings in peakery. If a route is missing a difficulty rating, you can add it by going to the ‘edit’ page for that route. For now, peakery uses the Yosemite Decimal System (YDS) Class rating for all peaks; we plan to add more international rating systems in the future.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="avatar-modal modal fade" id="avatar-modal" tabindex="-1" role="dialog" aria-labelledby="avatar-label">
    <div class="modal-dialog avatar-modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
              <h4 class="modal-title" id="avatar-label">{{ bagger }}</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <img class="big-avatar-img" src="{{ MEDIA_URL }}{{ avatar.big_avatar_url|urlencode }}">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="followers-following-modal modal fade" id="followers-following" tabindex="-1" role="dialog" aria-labelledby="followers-following-label">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
              <h4 class="modal-title" id="followers-following-label"><span style="font-weight: 500;" class="hidden-xs">{{ bagger }}</span> <span id="modal-show-followers" style="cursor: pointer; color: #f24100; margin-left: 20px; font-size: 15px;"><span class="followers-count"></span> Followers</span><span id="modal-show-followings" style="cursor: pointer; color: #999999; margin-left: 20px; font-size: 15px;"><span class="followings-count"></span> Following</span></h4>
            </div>
            <div class="modal-body" style="padding-top: 0px; padding-bottom: 0px;">
                <div id="modal-followers"></div>
                <div id="modal-followings" style="display: none;"></div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">

    var map;
    var circle;
    var marker;
    var topo;
    var center = null;
    var map_bounds;
    var latLng = null;
    var init = false;
    var photos_displayed = 0;
    var photos_page = 1;
    var photos = [];
    var circle_bounds;
    var pageX, pageY, mapX, mapY;
    var iconstyle;
    var memberId = {{ bagger.id }};

    function openUrl(url) {
        window.location.href = url;
    }

    function numberWithCommas(x) {
        return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    function loadPeaks(peak_id) {

        //get map bounds
        var bounds = map.getBounds();

        var counter = 0;

        var params = '';
        params = params + '&q=';
        params = params + '&n=';
        params = params + '&elev_min=0';
        params = params + '&elev_max=29500';
        params = params + '&prom_min=0';
        params = params + '&prom_max=29500';
        params = params + '&summits_min=0';
        params = params + '&summits_max=500';
        params = params + '&difficulty_min=1';
        params = params + '&difficulty_max=5';
        params = params + '&lat=';
        params = params + '&lng=';
        params = params + '&bounds=' + bounds.getSouthWest().lat + ',' + bounds.getSouthWest().lng + ',' + bounds.getNorthEast().lat + ',' + bounds.getNorthEast().lng;

        //update hidden parameters
        map_bounds = bounds.getSouthWest().lat + ',' + bounds.getSouthWest().lng + ',' + bounds.getNorthEast().lat + ',' + bounds.getNorthEast().lng;

        var LatLngList = [];

        if (params.length > 0) params = '?' + params.slice(-1*(params.length-1));

        $.getJSON('{% url "peaks_map" %}' + params , function(data) {
            $.each( data, function( key, val ) {
                var currentRequest = true;
                if (key=='parameters') {
                    $.each( val, function( parameterkey, parameterval ) {
                        if (parameterval.bounds != map_bounds) currentRequest = false;
                    });
                }

                if (!currentRequest) {
                    return false;
                }

                if (key=='peaks') {

                    var havePeaks = false;

                    $.each( val, function( peakkey, peakval ) {

                        if (!havePeaks) {

                            //first time through, delete highest peak marker and remove any markers not on map
                            deletehighest();
                            //delete markers out of margins
                            delete_old_markers(val);

                        }

                        havePeaks = true;

                        //build country string
                        var country = '';
                        $.each( peakval.country, function( countrykey, countryval ) {
                            country = country + '<div><a href="' + countryval.country_slug + '/">' + countryval.country_name + '</a></div>';
                        });
                        if (country == '') {
                            country = 'Unknown Country';
                        }

                        //build region string
                        var region = '';
                        var mobile_region = '';
                        var region_bull_class = 'hidden-lg hidden-md hidden-sm hidden-xs';
                        $.each( peakval.region, function( regionkey, regionval ) {
                            region_bull_class = '';
                            region = region + '<div style="margin-bottom: 5px;"><a class="truncate" href="' + regionval.region_slug + '/">' + regionval.region_name + ', ' + regionval.country_name + '</a></div>';
                            mobile_region = '<div style="margin-bottom: 5px;"><a class="truncate" href="' + regionval.region_slug + '/">' + regionval.country_name + '</a></div>';
                        });
                        if (region == '') {
                            region = country;
                            mobile_region = country;
                        }

                        //build challenges string
                        var challenges = '';
                        var challenge_count = peakval.challenge_count;
                        if (challenge_count > 0) {
                            challenges = ' &bull; <span>' + challenge_count + ' <i class="fa fa-trophy"></i></span>';
                        }

                        //build summits string
                        var summits, tooltip_your_summits;
                        if (peakval.summit_count > 0) {
                            summits = '<a href="/' + peakval.slug + '/summits/">' + peakval.summit_count + '</a>';
                        } else {
                            summits = '<a href="/' + peakval.slug + '/summits/">0</a><br /><a href="/' + peakval.slug + '/">Be the first!</a>';
                        }
                        if (peakval.your_summits > 0) {
                            summits = summits + '<br /><span style="color: #3fc663; font-weight: 500;">' + peakval.your_summits + 'x by you</span>';
                            tooltip_your_summits = '&nbsp;<span style="color: #3fc663; font-weight: 500;">(You ' + peakval.your_summits + 'x)</span>';
                        } else {
                            tooltip_your_summits = '';
                        }

                        //build tooltip string
                        var tooltip_html, tooltip_width, tooltip_height, marker_top, marker_left;
                        if (peakval.thumbnail_url.indexOf('default.png.350x245_q95_crop.jpg') < 0) {
                            tooltip_html = '<img class="map-tooltip-img hover-photos-hover" src="{{ MEDIA_URL }}' + peakval.thumbnail_url + '"><div class="map-tooltip-info"><div class="map-tooltip-peak-name ellipsis">' + peakval.name + '</div><div class="map-tooltip-peak-stats"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + tooltip_your_summits + challenges + '</div></div>';
                            tooltip_width = 220;
                            tooltip_height = 165;
                        } else {
                            tooltip_html = '<div class="map-tooltip-info"><div class="map-tooltip-peak-name ellipsis">' + peakval.name + '</div><div class="map-tooltip-peak-stats"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + tooltip_your_summits + challenges + '</div></div>';
                            tooltip_width = 220;
                            tooltip_height = 50;
                        }
                        var tooltip_url = '/' + peakval.slug;

                        var latLng = new mapboxgl.LngLat(peakval.lng, peakval.lat);

                        if( circle_bounds.getNorthWest().lon <= peakval.lng && peakval.lng <= circle_bounds.getSouthEast().lat && circle_bounds.getNorthWest().lat <= peakval.lat && peakval.lat <= circle_bounds.getSouthEast().lon ) {
                            iconstyle = 'marker_icon_green';
                        } else {
                            iconstyle = 'marker_icon';
                        }

                        var is_draggable = false;

                        //check if already exist so don't put again
                        var exists = false;
                        for (i = markersArray.length-1; i>=0; i--){
                            if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng)){
                                exists = true ;
                                //if the highest is in the actual viewport, not as the highest, delete it
                                if (iconstyle=='marker_icon_red' || iconstyle=='marker_icon_redgreen' || iconstyle=='marker_icon_green') {
                                    markersArray[i].remove();
                                    markersArray.splice(i,1);
                                    exists = false;
                                }
                            }
                        }

                        //if we are only showing one peak_id, assume other peaks already exist so don't show them
                        if (peak_id == null) {
                            //do nothing for now
                        } else {
                            if (peakval.id != peak_id) {
                                exists = true ;
                            }
                        }

                        if (!exists) {
                            var latLng = [peakval.lng, peakval.lat];
                            //add marker
                            //create an HTML element for the marker
                            var el = document.createElement('div');
                            el.className = iconstyle;

                            el.addEventListener('click', function(e) {
                                if (isTouchDevice()) {

                                    var bottom = $('#map-canvas').height();
                                    var right = $('#map-canvas').width();

                                    if (mapY < (bottom/2)) {
                                        marker_top = mapY;
                                    } else {
                                        marker_top = mapY - tooltip_height - 45;
                                    }

                                    if (mapX < (right/3)) {
                                        marker_left = mapX;
                                    } else if (mapX >= (right/3) && mapX < ((right/3)*2)) {
                                        marker_left = mapX - (tooltip_width/2) - 15;
                                    } else {
                                        marker_left = mapX - tooltip_width - 30;
                                    }

                                    $('#marker-tooltip').html(marker.properties.tooltipContent).css({
                                        'left': marker_left,
                                        'top': marker_top,
                                        'width': tooltip_width,
                                        'height': tooltip_height
                                    }).show();
                                    $('#marker-tooltip').data('url',marker.properties.tooltipUrl);
                                } else {
                                    //console.log(peakval.slug);
                                    location = '/' + peakval.slug + '/';;
                                }
                            });

                            el.addEventListener('mouseover', function(e) {

                                var bottom = $('#map-canvas').height();
                                var right = $('#map-canvas').width();

                                if (mapY < (bottom/2)) {
                                    marker_top = mapY;
                                } else {
                                    marker_top = mapY - tooltip_height - 45;
                                }

                                if (mapX < (right/3)) {
                                    marker_left = mapX;
                                } else if (mapX >= (right/3) && mapX < ((right/3)*2)) {
                                    marker_left = mapX - (tooltip_width/2) - 15;
                                } else {
                                    marker_left = mapX - tooltip_width - 30;
                                }

                                $('#marker-tooltip').html(marker.properties.tooltipContent).css({
                                    'left': marker_left,
                                    'top': marker_top,
                                    'width': tooltip_width,
                                    'height': tooltip_height
                                }).show();
                                $('#marker-tooltip').data('url',marker.properties.tooltipUrl);

                            });

                            el.addEventListener('mouseout', function(e) {
                                if (isTouchDevice()) {
                                    //$('#marker-tooltip').hide();
                                } else {
                                    $('#marker-tooltip').hide();
                                }
                            });

                            var marker = new mapboxgl.Marker(el)
                                .setLngLat(latLng)
                                .setOffset([-5, -10])
                                .setDraggable(is_draggable);

                            marker.properties = {};
                            marker.properties.tooltipContent = tooltip_html;
                            marker.properties.tooltipUrl = tooltip_url;
                            marker.properties.iconstyle = iconstyle;
                            marker.properties.peakid = peakval.id;

                            markersArray.push(marker);
                            LatLngList.push(latLng);

                        }

                        counter ++;
                    });

                    //add markers to map in reverse order so highest on top
                    for (var i = markersArray.length - 1; i >= 0; --i) {
                        markersArray[i].addTo(map);
                    }

                    if (!havePeaks) {
                        //didn't have any peaks, so remove all markers
                        delete_old_markers(val);
                    }
                }
            });
        });

        init = true;

    }

    function initPeaks() {

        if (!loadedPeaks) {
            loadPeaks();
            loadedPeaks = true;
        }

    }

    var markersArray = [];
    var loadedPeaks = false;

    function initialize() {

        var mapDiv = document.getElementById('map-canvas');
        var latLng = new mapboxgl.LngLat({{ profile_stats.basecamp_y }}, {{ profile_stats.basecamp_x }});
        circle_bounds = new mapboxgl.LngLatBounds();

        var initZoom = 16;

        if (isTouchDevice()) {
            map = new mapboxgl.Map({
                container: mapDiv, // HTML container id
                style: mapStyle, // style URL
                center: latLng, // starting position as [lng, lat]
                zoom: initZoom,
                scrollZoom: false
            });
        } else {
            map = new mapboxgl.Map({
                container: mapDiv, // HTML container id
                style: mapStyle, // style URL
                center: latLng, // starting position as [lng, lat]
                zoom: initZoom,
                scrollZoom: false
            });
        }

        function calculateCenter() {
          center = map.getCenter();
        }

        map.on('resize', function(e) {
            if (!circle_bounds.isEmpty()) {
                center = map.getCenter();
                map.setCenter(center);
                map.fitBounds(circle_bounds, {padding: 50, duration: 0});
            }
        });

        map.on('load', function () {
            var createGeoJSONCircle = function(center, radius, points) {
                if(!points) points = 64;

                var coords = {
                    latitude: center[1],
                    longitude: center[0]
                };

                var km = radius/1000;

                var ret = [];
                var distanceX = km/(111.320*Math.cos(coords.latitude*Math.PI/180));
                var distanceY = km/110.574;

                var theta, x, y;
                for(var i=0; i<points; i++) {
                    theta = (i/points)*(2*Math.PI);
                    x = distanceX*Math.cos(theta);
                    y = distanceY*Math.sin(theta);

                    ret.push([coords.longitude+x, coords.latitude+y]);
                    var p = new mapboxgl.LngLat(coords.longitude+x, coords.latitude+y);
                    circle_bounds.extend(p);
                }
                ret.push(ret[0]);

                return {
                    "type": "geojson",
                    "data": {
                        "type": "FeatureCollection",
                        "features": [{
                            "type": "Feature",
                            "geometry": {
                                "type": "Polygon",
                                "coordinates": [ret]
                            }
                        }]
                    }
                };
            };

            map.addSource("polygon", createGeoJSONCircle([{{ profile_stats.basecamp_y }}, {{ profile_stats.basecamp_x }}], {{ completion_radius }}));

            map.addLayer({
                "id": "polygon",
                "type": "fill",
                "source": "polygon",
                "layout": {},
                "paint": {
                    "fill-color": "#b2b2b2",
                    "fill-opacity": 0.6
                }
            });
            map.fitBounds(circle_bounds, {padding: 50, duration: 0});
            calculateCenter();
        });

        map.on('click', function () {
            openUrl('/map/#lat={{ profile_stats.basecamp_x }}&lng={{ profile_stats.basecamp_y }}&n={{ profile.location_name|urlencode }}');
        });

        map.on('zoomend', function () {
            initPeaks();
        });

    }

    function loadFollowers() {
        $.getJSON('{% url "user_followers_list" %}?user={{ bagger.id }}', function(data) {
            var follower_html = '';
            var following_html = '';
            $.each( data, function( key, val ) {
                if (key=='following') {
                    if (val=='True') {
                        $('.followButton').hide();
                        $('.unfollowButton').show();
                    } else {
                        $('.unfollowButton').hide();
                        $('.followButton').show();
                    }
                }
                if (key=='followers_count') {
                    $('.followers-count').html(val);
                    var followers_count = parseInt(val);
                    if (followers_count > 0) {
                        $('.has-no-followers').hide();
                        $('.has-followers').show();
                    }
                }
                if (key=='followings_count') {
                    $('.followings-count').html(val);
                    var followings_count = parseInt(val);
                    if (followings_count > 0) {
                        $('.has-no-followers').hide();
                        $('.has-followers').show();
                    }
                }
                if (key=='followers') {

                    $.each( val, function( userkey, userval ) {

                        //build follower string
                        follower_html = follower_html + '<div onclick="openUrl(\'/members/' + userval.username + '\');" class="row striped-row hover-cell" style="cursor: pointer; height: 80px; border-bottom: 1px solid #cccccc;">';
                        follower_html = follower_html + '<div class="col-md-12" style="margin-top: 15px;">';
                        follower_html = follower_html + '<div style="float: left;"><img src="' + userval.avatar + '" style="width: 50px;"></div>';
                        follower_html = follower_html + '<div class="hidden-sm hidden-md hidden-lg" style="float: left; margin-left: 10px; margin-top: -2px;">';
                        follower_html = follower_html + '<a style="line-height: 24px;" href="/members/' + userval.username + '">' + userval.username + '</a>';
                        follower_html = follower_html + '<div style="font-size: 10px; line-height: 10px; color: #666666;">' + userval.summit_count + ' peaks</div>';
                        if (userval.location_name!='') {
                            follower_html = follower_html + '<div class="ellipsis" style="font-size: 10px; color: #666666; width: 124px;">' + userval.location_name + '</div>';
                        }
                        follower_html = follower_html + '</div>';
                        follower_html = follower_html + '<div class="hidden-xs" style="float: left; margin-left: 10px; line-height: 50px;">';
                        follower_html = follower_html + '<a href="/members/' + userval.username + '">' + userval.username + '</a>';
                        follower_html = follower_html + '</div>';
                        follower_html = follower_html + '<div class="hidden-xs" style="float: left; margin-left: 20px; font-size: 11px; color: #666666; line-height: 50px;">' + userval.summit_count + ' peaks';
                        if (userval.location_name!='') {
                            follower_html = follower_html + '&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + userval.location_name;
                        }
                        follower_html = follower_html + '</div>';
                        follower_html = follower_html + '</div>';
                        follower_html = follower_html + '</div>';

                    });

                }
                if (key=='followings') {

                    $.each( val, function( userkey, userval ) {

                        //build following string
                        following_html = following_html + '<div onclick="openUrl(\'/members/' + userval.username + '\');" class="row striped-row hover-cell" style="cursor: pointer; height: 80px; border-bottom: 1px solid #cccccc;">';
                        following_html = following_html + '<div class="col-md-12" style="margin-top: 15px;">';
                        following_html = following_html + '<div style="float: left;"><img src="' + userval.avatar + '" style="width: 50px;"></div>';
                        following_html = following_html + '<div class="hidden-sm hidden-md hidden-lg" style="float: left; margin-left: 10px; margin-top: -2px;">';
                        following_html = following_html + '<a style="line-height: 24px;" href="/members/' + userval.username + '">' + userval.username + '</a>';
                        following_html = following_html + '<div style="font-size: 10px; line-height: 10px; color: #666666;">' + userval.summit_count + ' peaks</div>';
                        if (userval.location_name!='') {
                            following_html = following_html + '<div class="ellipsis" style="font-size: 10px; color: #666666; width: 124px;">' + userval.location_name + '</div>';
                        }
                        following_html = following_html + '</div>';
                        following_html = following_html + '<div class="hidden-xs" style="float: left; margin-left: 10px; line-height: 50px;">';
                        following_html = following_html + '<a href="/members/' + userval.username + '">' + userval.username + '</a>';
                        following_html = following_html + '</div>';
                        following_html = following_html + '<div class="hidden-xs" style="float: left; margin-left: 20px; font-size: 11px; color: #666666; line-height: 50px;">' + userval.summit_count + ' peaks';
                        if (userval.location_name!='') {
                            following_html = following_html + '&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + userval.location_name;
                        }
                        following_html = following_html + '</div>';
                        following_html = following_html + '</div>';
                        following_html = following_html + '</div>';

                    });

                }
            });
            $('#modal-followers').html(follower_html);
            $('#modal-followings').html(following_html);
        });
    }

    function loadContributorStats() {

        $.getJSON('{% url "user_contributor_stats" %}?user={{ bagger.id }}', function(data) {
            var temp;

            var peak_info_corrections_count = parseInt(data.peak_info_corrections_count);
            $('#peak-info-corrections-count').html(peak_info_corrections_count);
            if (peak_info_corrections_count > 0) {
                temp = '#' + data.peak_info_corrections_rank + ' contributor'
                $('#peak-info-corrections-rank').html(temp);
                $('#peak-info-corrections-rank').show();
            }

            var peak_photos_count = parseInt(data.peak_photos_count);
            $('#peak-photos-count').html(peak_photos_count);
            if (peak_photos_count > 0) {
                temp = '#' + data.peak_photos_rank + ' contributor'
                $('#peak-photos-rank').html(temp);
                $('#peak-photos-rank').show();
            }

            var peak_count = parseInt(data.peak_count);
            $('#peak-count').html(peak_count);
            if (peak_count > 0) {
                temp = '#' + data.peak_rank + ' contributor'
                $('#peak-rank').html(temp);
                $('#peak-rank').show();
            }

            var summit_routes_count = parseInt(data.summit_routes_count);
            $('#summit-routes-count').html(summit_routes_count);
            if (summit_routes_count > 0) {
                temp = '#' + data.summit_routes_rank + ' contributor'
                $('#summit-routes-rank').html(temp);
                $('#summit-routes-rank').show();
            }
        });

    }

    $(document).ready(function() {

        //get awards counts
        $.getJSON('{% url "user_awards_list" %}?user={{ bagger.id }}', function(data) {
            var temp;

            var kom_count = parseInt(data.kom_count);
            temp = data.kom_count + ' peak' + ((kom_count>1)?'s':'');
            $('#kom_count').html(temp);

            var first_ascents_count = parseInt(data.first_ascents_count);
            temp = data.first_ascents_count + ' peak' + ((first_ascents_count>1)?'s':'');
            $('#first_ascents_count').html(temp);

            var summit_stewards_count = parseInt(data.summit_stewards_count);
            temp = data.summit_stewards_count + ' peak' + ((summit_stewards_count>1)?'s':'');
            $('#summit_stewards_count').html(temp);

            var peaks_bagged_rank_all = parseInt(data.peaks_bagged_rank_all);
            var page_number = parseInt(peaks_bagged_rank_all / 20);
            page_number = page_number + 1;
            temp = '#' + data.peaks_bagged_rank_all + ' all-time';
            $('#peaks-bagged-rank').html(temp);
            var newUrl = '/members/#since=all&order=new_peaks&page=' + page_number;
            $('#peaks-bagged-rank').attr("href", newUrl);

            //days since last climb
            var days_since_last_summit = parseInt(data.days_since_last_summit);
            if (days_since_last_summit >= 0) {
                $("#days-since-last-summit").html(days_since_last_summit);
                $("#last-summit-link").attr('href', data.last_summit_url);
                $("#last-summit-link").html(data.last_summit_name);
                $("#last-summit-div").css("cursor", "pointer");
                $("#last-summit-div").click(function(){
                    openUrl(data.last_summit_url);
                });
            } else {
                $("#days-since-last-summit").hide();
                $("#last-summit-link-div").hide();
                $("#last-summit-missing").show();
            }

            //show/hide member links
            var request_user_id = data.request_user_id;
            if (request_user_id > 0) {
                if (request_user_id == memberId) {
                    //show member links, remove nonmember and nonlogin links
                    $(".nonmember-links").remove();
                    $(".nonlogin-links").remove();
                    $(".member-links").removeClass("member-links");
                    //show contributor stats
                    $("#contributor-stats").show();
                    loadContributorStats();
                } else {
                    //show nonmember links, remove member and nonlogin links
                    $(".member-links").remove();
                    $(".nonlogin-links").remove();
                    $(".nonmember-links").removeClass("nonmember-links");
                    if (data.request_admin_user) {
                        //show contributor stats
                        $("#contributor-stats").show();
                        loadContributorStats();
                    }
                }
            } else {
                //show nonlogin links, remove member and nonmember links
                $(".member-links").remove();
                $(".nonmember-links").remove();
                $(".nonlogin-links").removeClass("nonlogin-links");
            }
        });

        //load followers
        loadFollowers();

        $("time.timeago").timeago();

        $('#map-canvas').mousemove(function(e) {
            var offset = $(this).offset();
            pageX = e.pageX;
            pageY = e.pageY;
            mapX = (e.pageX - offset.left);
            mapY = (e.pageY - offset.top);
        });

        $('#map-canvas').on('touchstart', function(e) {
            var offset = $(this).offset();
            pageX = e.originalEvent.touches[0].pageX;
            pageY = e.originalEvent.touches[0].pageY;
            mapX = (pageX - offset.left);
            mapY = (pageY - offset.top);
        });

        $('.profile-about-completion-radius').on('click', function(e) {
            $('#about-completion-radius').modal('show');
            e.stopPropagation();
        });

        $('#modal-show-followers').on('click', function() {
            if ($('#modal-followings').is(':visible')) {
                $('#modal-show-followers').css('color','#f24100');
                $('#modal-show-followings').css('color','#999999');
                $('#modal-followings').fadeOut(300);
                $('#modal-followers').fadeIn(300);
            }
        });

        $('#modal-show-followings').on('click', function() {
            if ($('#modal-followers').is(':visible')) {
                $('#modal-show-followings').css('color','#f24100');
                $('#modal-show-followers').css('color','#999999');
                $('#modal-followers').fadeOut(300);
                $('#modal-followings').fadeIn(300);
            }
        });

        $('#followers-following-stat').on('click', function() {
            $('#followers-following').modal('show');
        });

        $('.more-about-bagger').on('click', function() {
            $('#about-bagger-teaser-mobile').hide();
            $('#about-bagger-teaser').hide();
            $('#about-bagger-social').css('position','relative');
            $('#about-bagger-complete').fadeIn(300);
            $(this).parents('.peakimg-responsive').css('height','auto');
        });

        $('.less-about-bagger').on('click', function() {
            $('#about-bagger-complete').hide();
            $('#about-bagger-social').css('position','absolute');
            $('#about-bagger-teaser-mobile').fadeIn(300);
            $('#about-bagger-teaser').fadeIn(300);
            $(this).parents('.peakimg-responsive').css('height','');
        });

        $('#unfollow-{{ bagger.id }}').hover(
        function(){
            $(this).removeClass('unfollowButton').addClass('unfollowButtonHoverProfile').text('Unfollow').css('background-color','#f24100').css('border-color','#f24100');
        },
        function(){
            $(this).removeClass('unfollowButtonHoverProfile').addClass('unfollowButton').text('You are following').css('background-color','#00b330').css('border-color','#00b330');
        });

        $("#unfollow-{{ bagger.id }}").on('click',function (){
            var btn = $("#unfollow-{{ bagger.id }}");
            var follow_btn = $("#follow-{{ bagger.id }}");
            btn.html('<i class="fa fa-spinner fa-spin"></i>');
            $.post('/accounts/unfollow/{{ bagger.id }}/',function(data){
                if (data == "True"){
                    follow_btn.html('Follow');
                    $("#unfollow-{{ bagger.id }}").hide();
                    $("#follow-{{ bagger.id }}").show();
                    $("#mobile-unfollow-{{ bagger.id }}").hide();
                    $("#mobile-follow-{{ bagger.id }}").show();
                    loadFollowers();
                }
            });
        });

        $("#follow-{{ bagger.id }}").on('click',function (){
            var btn = $("#follow-{{ bagger.id }}");
            var unfollow_btn = $("#unfollow-{{ bagger.id }}");
            btn.html('<i class="fa fa-spinner fa-spin"></i>');
            $.post('/accounts/follow/{{ bagger.id }}/',function(data){
                if (data == "True"){
                    unfollow_btn.html('You are following');
                    $("#follow-{{ bagger.id }}").hide();
                    $("#unfollow-{{ bagger.id }}").show();
                    $("#mobile-follow-{{ bagger.id }}").hide();
                    $("#mobile-unfollow-{{ bagger.id }}").show();
                    loadFollowers();
                }
            });
        });

        $("#mobile-unfollow-{{ bagger.id }}").on('click',function (){
            var btn = $("#mobile-unfollow-{{ bagger.id }}");
            var follow_btn = $("#mobile-follow-{{ bagger.id }}");
            btn.html('<i class="fa fa-spinner fa-spin"></i>');
            $.post('/accounts/unfollow/{{ bagger.id }}/',function(data){
                if (data == "True"){
                    follow_btn.html('follow');
                    $("#mobile-unfollow-{{ bagger.id }}").hide();
                    $("#mobile-follow-{{ bagger.id }}").show();
                    $("#unfollow-{{ bagger.id }}").hide();
                    $("#follow-{{ bagger.id }}").show();
                    loadFollowers();
                }
            });
        });

        $("#mobile-follow-{{ bagger.id }}").on('click',function (){
            var btn = $("#mobile-follow-{{ bagger.id }}");
            var unfollow_btn = $("#mobile-unfollow-{{ bagger.id }}");
            btn.html('<i class="fa fa-spinner fa-spin"></i>');
            $.post('/accounts/follow/{{ bagger.id }}/',function(data){
                if (data == "True"){
                    unfollow_btn.html('following');
                    $("#mobile-follow-{{ bagger.id }}").hide();
                    $("#mobile-unfollow-{{ bagger.id }}").show();
                    $("#follow-{{ bagger.id }}").hide();
                    $("#unfollow-{{ bagger.id }}").show();
                    loadFollowers();
                }
            });
        });

        {% if profile_stats.basecamp_x != 0 %}
        initialize();
        resize_map();
        {% endif %}

        {% if profile_stats.basecamp_x != 0 %}
        $(window).resize(function() {
            resize_map();
        });
        {% endif %}

    });

    $.cssHooks.backgroundColor = {
        get: function(elem) {
            if (elem.currentStyle)
                var bg = elem.currentStyle["backgroundColor"];
            else if (window.getComputedStyle)
                var bg = document.defaultView.getComputedStyle(elem,
                    null).getPropertyValue("background-color");
            if (bg.search("rgb") == -1)
                return bg;
            else {
                bg = bg.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
                function hex(x) {
                    return ("0" + parseInt(x).toString(16)).slice(-2);
                }
                return "#" + hex(bg[1]) + hex(bg[2]) + hex(bg[3]);
            }
        }
    }

    function resize_map() {
        var mapHeight = $('#nearest-to-home-goal-div').height();
        var mapWidth = $('#nearest-to-home-goal-div').width();
        $("#map-canvas").height(mapHeight);
        $("#map-canvas").width(mapWidth);
        map.resize();
    }

    function fix_item_location(id,point){
        $.post('{% url "fix_item_location" %}', {id:id, lat:point.lat(), long:point.lng()},
                function(data) {
                    var output = $("#message_map");
                    var output_div = $("#message_map_div");
                    output.html(data);
                    output_div.show();
                    setTimeout(function() {
                        $("#message_map_div").hide();
                    }, 10000)
                }
        );
    }

    function delete_peak_from_map (id){
        $.post('{% url "delete_peak_from_map" %}', {id:id},
                function(data) {
                    var output = $("#message_map");
                    var output_div = $("#message_map_div");
                    output.html(data);
                    output_div.show();
                    setTimeout(function() {
                        $("#message_map_div").hide();
                    }, 10000)
                }
        );
    }

    function check_is_in(marker){
        return map.getBounds().contains(marker.getPosition());
    }

    function delete_out_markers(){
        if (markersArray){
            for (i in markersArray){
                if (!check_is_in(markersArray[i])){
                    markersArray[i].remove();
                    markersArray.splice(i,1);
                }
            }
        }
    }

    function deletehighest(){
        if (markersArray){
            for (i in markersArray){
                if (markersArray[i].properties.iconstyle == 'marker_icon_redgreen' || markersArray[i].properties.iconstyle == 'marker_icon_red'){
                    markersArray[i].remove();
                    markersArray.splice(i,1);
                }
            }
        }
    }

    function limit_number_of_markers(limit){
        if (markersArray.length > limit){
            for (i = markersArray.length-1; i>=limit; i--){
                markersArray[i].remove();
                markersArray.splice(i,1);
            }
        }
    }

    function elevation_range(data){
        if (markersArray){
            for (i = markersArray.length-1; i>=0; i--){
                var inrange = false;
                $.each(data, function(k, v){
                    var latLng1 = new mapboxgl.LngLat(v.lng, v.lat);
                    if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng1)){
                        inrange = true;
                    }
                });
                if (!inrange){
                    markersArray[i].remove();
                    markersArray.splice(i,1);
                }
            }
        }
    }

    function delete_old_markers(data){
        if (markersArray){
            for (i = markersArray.length-1; i>=0; i--){
                var inrange = false;
                $.each(data, function(k, v){
                    var latLng1 = new mapboxgl.LngLat(v.lng, v.lat);
                    if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng1)){
                        inrange = true;
                    }
                });
                if (!inrange){
                    markersArray[i].remove();
                    markersArray.splice(i,1);
                }
            }
        }
    }

    function fromLatLngToString(latLng) {
        return latLng.lat + ',' + latLng.lng;
    }

</script>


{% endblock %}

{% block gallery %}
<div id="blueimp-gallery" class="blueimp-gallery blueimp-gallery-controls" data-use-bootstrap-modal="false" data-slideshow-interval="4000">
    <!-- The container for the modal slides -->
    <div class="slides"></div>
    <!-- Controls for the borderless lightbox -->
    <h3 class="title"></h3>
    <div class="description">
        <div class="description-text">
            <div class="description-text-caption"></div>
            <div class="description-text-user"></div>
        </div>
    </div>
    <a class="prev">‹</a>
    <a class="next">›</a>
    <a class="close">×</a>
    <a class="play-pause"></a>
    <ol class="indicator"></ol>
    <!-- The modal dialog, which will be used to wrap the lightbox content -->
    <div class="modal fade">
        <div class="modal-dialog" style="width: 80%;">
            <div class="modal-content">
                <div class="modal-header">
                    <button style="color: #fff;" type="button" class="close" aria-hidden="true">&times;</button>
                    <h4 class="modal-title"></h4>
                </div>
                <div class="modal-body next"></div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default pull-left prev">
                        <i class="glyphicon glyphicon-chevron-left"></i>
                        Previous
                    </button>
                    <button type="button" class="btn btn-primary next">
                        Next
                        <i class="glyphicon glyphicon-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="loading" style="display: none;">Loading&#8230;</div>
{% endblock %}