{% load avatar_tags follow_tags %}
<li>
    <span class="followerWrapper clearfix">
        <div class="leftCol">
            {% if following.get_object != request.user  %}
                <a href="{{ following.get_object.get_absolute_url }}">{% avatar following.get_object 45 %}</a>
            {% else %}
                {% avatar following.get_object 45 %}
            {% endif %}
        </div><!-- /.leftCol -->
        <div class="rightCol">
            <h3>{% if following.get_object != request.user  %}<a href="{{ following.get_object.get_absolute_url }}">{{ following.get_object.username }}</a>{% else %}You{% endif %}</h3>
            <p>
                {% if following.get_object.get_profile.peaks_bagged_count %}
                    {{ following.get_object.get_profile.peaks_bagged_count }}
                {% else %}
                    0
                {% endif %} peaks
                {% if following.get_object.get_profile.location_name %}
                    &bull; {{ following.get_object.get_profile.location_name }}
                {% endif %}
            </p>
            {% if following.get_object != request.user %}
                {% if request.user == profile %}
                    {% is_following request.user following.get_object %}
                    {% if is_following %}
                        <a id="follow-{{ following.get_object.id }}" href="#" onclick="followUser('{% url "follow_user" following.get_object.id %}', '{{ following.get_object.id }}')" class="peakeryFollowButton followButton" style="display: none;">Follow</a>
                        <a id="unfollow-{{ following.get_object.id }}" href="#" onclick="unfollowUser('{% url "unfollow_user" following.get_object.id %}', '{{ following.get_object.id }}')" class="peakeryFollowButton unfollowButton">You are following</a>
                    {% else %}
                        <a id="follow-{{ following.get_object.id }}" href="#" onclick="followUser('{% url "follow_user" following.get_object.id %}', '{{ following.get_object.id }}')" class="peakeryFollowButton followButton">Follow</a>
                        <a id="unfollow-{{ following.get_object.id }}" href="#" onclick="unfollowUser('{% url "unfollow_user" following.get_object.id %}', '{{ following.get_object.id }}')" class="peakeryFollowButton unfollowButton" style="display: none;">You are following</a>
                    {% endif %}
                    {% else %}
                    {% is_following request.user following.get_object %}
                    {% if is_following %}
                        <a id="follow-{{ following.get_object.id }}" href="#" onclick="followUser('{% url "follow_user" following.get_object.id %}', '{{ following.get_object.id }}')" class="peakeryFollowButton followButton" style="display: none;">Follow</a>
                        <a id="unfollow-{{ following.get_object.id }}" href="#" onclick="unfollowUser('{% url "unfollow_user" following.get_object.id %}', '{{ following.get_object.id }}')" class="peakeryFollowButton unfollowButton">You are following</a>
                    {% else %}
                        <a id="follow-{{ following.get_object.id }}" href="#" onclick="followUser('{% url "follow_user" following.get_object.id %}', '{{ following.get_object.id }}')" class="peakeryFollowButton followButton">Follow</a>
                        <a id="unfollow-{{ following.get_object.id }}" href="#" onclick="unfollowUser('{% url "unfollow_user" following.get_object.id %}', '{{ following.get_object.id }}')" class="peakeryFollowButton unfollowButton" style="display: none;">You are following</a>
                    {% endif %}
                {% endif %}
            {% endif %}
        </div><!-- /.rightCol -->
    </span><!-- /.followerWrapper -->
</li>