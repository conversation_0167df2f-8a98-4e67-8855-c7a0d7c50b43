{% extends "base_no_header_footer.html" %}
{% load static %}
{% load avatar_tags %}
{% load item_tags %}
{% load cache %}
{% load humanize %}

{% block title %}Edit info for {{ bagger }}{% endblock %}
{% block titlemeta %}Edit info for {{ bagger }}{% endblock %}
{% block description %}Edit info for {{ bagger }}{% endblock %}

{% block mapbox %}
{% include "mapbox/mapbox.html" %}
{% endblock %}

{% block fixed_page_header %}
    <form class="useredit_form" method="POST">
    <div class="row main-header-row" style="border-bottom: none;">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 form-header-bar">
            <span style="font-size: 24px; font-weight: 600; color: #fff;">
            <div id="breadcrumbs" style="margin-left: 15px;">
                <div class="pull-right cancel-x-button-div">
                    <a class="cancel-x-button" onclick="cancelEdit();"><i class="fa fa-times"></i></a>
                </div>
                <div class="pull-right save-changes-button-div">
                    <button class="btn btn-secondary set2 input save-changes-button" id="edit_user" disabled>Save changes</button>
                </div>
                <div>
                    <div class="form-header-title ellipsis"><span>Edit your info - Updates can take some minutes to impact the system</span></div>
                </div>
            </div>
            </span>
        </div>
    </div>
{% endblock %}

{% block content %}

<script src="{% static 'vendor/s3.fine-uploader/s3.fine-uploader.js' %}"></script>

<style>


    /* Geocoder styles */

        .geocoder {
            background-color: transparent;
        }

        .mapboxgl-ctrl-geocoder--icon-search {
            display: none;
        }

        .mapboxgl-ctrl-geocoder--input {
            width: 100%;
            height: 46px;
            border: none !important;
        }

        .mapboxgl-ctrl-geocoder--input:focus {
            outline: none !important;
        }

        .mapboxgl-ctrl-geocoder--input::placeholder {
            font-size: 12px;
        }

        .mapboxgl-ctrl-geocoder .suggestions {
            width: 215px !important;
        }

        .mapboxgl-ctrl-geocoder .suggestions > li {
            width: 100% !important;
        }

        .mapboxgl-ctrl-geocoder--pin-right {
            display: none;
        }


    /* Geocoder styles end */

    body.modal-open {
        overflow: visible;
    }

    .remove-photo {
        position: absolute;
        top: 10px;
        right: 10px;
        color: #fff;
        font-size: 24px;
    }

    .remove-photo:hover {
        color: #ccc;
    }

    .basecamp-hint {
        cursor: pointer;
    }

   @media screen and (max-width: 767px) and (min-width: 1px) {
       .useredit_form {
           height: 70px;
       }
        #content-body {
           margin-top: -10px;
           padding-bottom: 0px;
        }
        html, body {
            letter-spacing: .03125em;
        }
        .content-pane {
           margin-top: 30px;
        }
        ul#user-files textarea {
            font-size: 14px;
        }
        .row-full-width .col-xs-12 {
            padding-left: 10px;
            padding-right: 10px;
        }
        .field-title {
            font-size: 16px;
            font-weight: 700;
        }
        .form-header-title {
            font-size: 14px;
            font-weight: 500;
        }
        input {
            font-size: 16px;
            font-weight: 300;
        }
        form textarea {
            font-size: 16px;
            font-weight: 300;
        }
        .route-info-section {
            margin-top: 20px;
        }
        .field-title-spacer {
            height: 0px;
        }
        .header-peak-name {
            color: #fff;
        }
        .header-peak {
            font-size: 18px;
            font-weight: 500;
        }
        .close-route-edit {
            margin-right: 0px;
        }
        .header-help {
            color: #999;
            font-size: 11px;
            font-weight: 300;
        }
        ::-webkit-input-placeholder { font-size: 16px; }
        ::-moz-placeholder { font-size: 16px; } /* firefox 19+ */
        :-ms-input-placeholder { font-size: 16px; } /* ie */
        input:-moz-placeholder { font-size: 16px; }
        input { font-size: 16px; }
        #label-facebook-page {
            left: 10px;
            top: 43px;
        }
        #label-twitter-username {
            left: 17px;
            top: 112px;
        }
        #label-instagram-username {
            left: 16px;
            top: 182px;
        }
        #label-flicker-page {
            left: 16px;
            top: 251px;
        }
        #label-website {
            left: 22px;
            top: 323px;
        }
    }
    @media screen and (min-width: 768px) {
        .useredit_form {
           height: 80px;
       }
        #content-body {
           margin-top: 30px;
        }
        .content-pane {
           margin-top: 0px;
        }
        input {
            font-size: 18px;
            font-weight: 300;
        }
        form textarea {
            font-size: 18px;
            font-weight: 300;
        }
        .route-info-section {
            margin-top: 60px;
        }
        .field-title-spacer {
            height: 10px;
        }
        .save-changes-div {
            float: right;
        }
    }
    @media screen and (min-width: 768px) and (max-width: 1023px) {
        ul#peakroute-files textarea {
            font-size: 16px;
        }
        .header-peak-name {
            color: #fff;
        }
        .header-peak {
            font-size: 18px;
            font-weight: 500;
        }
        .close-route-edit {
            margin-right: 0px;
        }
        .header-help {
            color: #999;
            font-size: 12px;
            font-weight: 300;
        }
        .field-title {
            font-size: 18px;
            font-weight: 700;
        }
        .form-header-title {
            font-size: 18px;
            font-weight: 500;
        }
        ::-webkit-input-placeholder { font-size: 16px; }
        ::-moz-placeholder { font-size: 16px; } /* firefox 19+ */
        :-ms-input-placeholder { font-size: 16px; } /* ie */
        input:-moz-placeholder { font-size: 16px; }
        input { font-size: 16px; }
        #label-facebook-page {
            left: 10px;
            top: 48px;
        }
        #label-twitter-username {
            left: 17px;
            top: 117px;
        }
        #label-instagram-username {
            left: 16px;
            top: 186px;
        }
        #label-flicker-page {
            left: 16px;
            top: 255px;
        }
        #label-website {
            left: 22px;
            top: 327px;
        }
    }
    @media screen and (min-width: 1024px) {
        ul#user-files textarea {
            font-size: 18px;
        }
        .header-peak-name {
            color: #f24100;
        }
        .header-peak {
            font-size: 20px;
            font-weight: 300;
        }
        .close-route-edit {
            margin-right: 20px;
        }
        .header-help {
            color: #999;
            font-size: 14px;
            font-weight: 300;
        }
        .field-title {
            font-size: 21px;
            font-weight: 700;
        }
        .form-header-title {
            font-size: 20px;
            font-weight: 300;
        }
        ::-webkit-input-placeholder { font-size: 18px; }
        ::-moz-placeholder { font-size: 18px; } /* firefox 19+ */
        :-ms-input-placeholder { font-size: 18px; } /* ie */
        input:-moz-placeholder { font-size: 18px; }
        input { font-size: 18px; }
        #label-facebook-page {
            left: 10px;
            top: 53px;
        }
        #label-twitter-username {
            left: 17px;
            top: 123px;
        }
        #label-instagram-username {
            left: 16px;
            top: 193px;
        }
        #label-flicker-page {
            left: 16px;
            top: 261px;
        }
        #label-website {
            left: 22px;
            top: 333px;
        }
        #gm-custom-mapunits {
            right: 142px;
        }
    }
    @media screen and (min-width: 1px) and (max-width: 1279px) {
         .no-header-container {
           padding-left: 0px;
        }
    }
    ::-webkit-input-placeholder { font-weight: 300; }
    ::-moz-placeholder { font-weight: 300; } /* firefox 19+ */
    :-ms-input-placeholder { font-weight: 300; } /* ie */
    input:-moz-placeholder { font-weight: 300; }
    input { font-weight: 300; }

    .gm-style-mtc {
        opacity: .8;
    }

    .qq-upload-list {
        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        box-shadow: none;
    }

    .qq-upload-list li.qq-upload-success {
        background-color: #f6f6f6;
        color: #424242;
        border-bottom: none;
        border-top: none;
    }

    .qq-upload-list li.qq-in-progress {
        background-color: #f6f6f6;
        color: #424242;
        border-bottom: none;
        border-top: none;
    }

    @media screen and (min-width: 1px) and (max-width: 479px) {
        .qq-thumbnail-wrapper, .qq-thumbnail-selector {
            width: 200px;
            max-width: 200px;
            min-height: 133px;
        }
        .remove-photo {
            width: 200px;
            max-width: 200px;
            right: 6px;
            top: 11px;
            vertical-align: top;
            position: absolute;
            display: table-cell;
            text-align: right;
            cursor: pointer;
        }
        #edit_user {
            width: 145px;
        }
    }
    @media screen and (min-width: 480px) and (max-width: 767px) {
        .qq-thumbnail-wrapper, .qq-thumbnail-selector {
            width: 300px;
            max-width: 300px;
            min-height: 200px;
        }
        .remove-photo {
            width: 300px;
            max-width: 300px;
            right: 6px;
            top: 11px;
            vertical-align: top;
            position: absolute;
            display: table-cell;
            text-align: right;
            cursor: pointer;
        }
        #edit_user {
            width: 145px;
        }
    }
    @media screen and (min-width: 768px) {
        .qq-thumbnail-wrapper, .qq-thumbnail-selector {
            width: 300px;
            max-width: 300px;
            min-height: 200px;
        }
        .remove-photo {
            width: 300px;
            max-width: 300px;
            right: 6px;
            top: 11px;
            vertical-align: top;
            position: absolute;
            display: table-cell;
            text-align: right;
            cursor: pointer;
        }
        #edit_user {
            width: 190px;
        }
    }

</style>

    <script type="text/template" id="qq-image-template">
        <div class="qq-uploader-selector qq-uploader" qq-drop-area-text="" style="max-height: none; background-color: #f6f6f6; border: none; padding: 0px; min-height: 0px;">
            <div class="qq-upload-drop-area-selector qq-upload-drop-area" qq-hide-dropzone>
                <span class="qq-upload-drop-area-text-selector"></span>
            </div>
            <ul class="qq-upload-list-selector qq-upload-list" aria-live="polite" aria-relevant="additions removals" style="max-height: none;">
                <li style="padding: 5px;">
                    <div style="display: flex; display: -webkit-flex;">
                        <div class="qq-thumbnail-wrapper" style="position: relative; align-content: stretch;">
                            <div>
                                <img class="qq-thumbnail-selector" qq-max-size="300" qq-server-scale style="margin-right: 0px;">
                            </div>
                            <div id="file-1-remove-photo" class="remove-photo"><i style="text-shadow: 0px 0px 5px #ccc;" class="fa fa-times" aria-hidden="true"></i></div>
                        </div>
                        <div style="position: absolute;">
                            <button type="button" class="qq-upload-retry-selector qq-upload-retry btn btn-secondary">Retry</button>
                        </div>
                    </div>
                </li>
            </ul>
            <div class="qq-upload-button-selector btn btn-secondary" style="width: 245px; height: 55px; font-size: 18px; text-align: center; background-color: #33c1f5; margin: 5px; z-index: 1; {% if avatar.avatar_url %}display: none;{% endif %}">
                <div>Choose profile pic (jpg)</div>
            </div>
            <span class="qq-drop-processing-selector qq-drop-processing">
                <span>Processing dropped files...</span>
                <span class="qq-drop-processing-spinner-selector qq-drop-processing-spinner"></span>
            </span>

            <dialog class="qq-alert-dialog-selector">
                <div class="qq-dialog-message-selector"></div>
                <div class="qq-dialog-buttons">
                    <button type="button" class="qq-cancel-button-selector btn btn-secondary">Close</button>
                </div>
            </dialog>

            <dialog class="qq-confirm-dialog-selector">
                <div class="qq-dialog-message-selector"></div>
                <div class="qq-dialog-buttons">
                    <button type="button" class="qq-cancel-button-selector btn btn-secondary">No</button>
                    <button type="button" class="qq-ok-button-selector btn btn-secondary">Yes</button>
                </div>
            </dialog>

            <dialog class="qq-prompt-dialog-selector">
                <div class="qq-dialog-message-selector"></div>
                <input type="text">
                <div class="qq-dialog-buttons">
                    <button type="button" class="qq-cancel-button-selector btn btn-secondary">Cancel</button>
                    <button type="button" class="qq-ok-button-selector btn btn-secondary">Ok</button>
                </div>
            </dialog>
        </div>
    </script>

    <div class="container" style="padding-left: 0px; padding-right: 0px;">

        <div class="content-pane" style="background-color: #f6f6f6; padding-top: 40px;">

            <input type="hidden" name="user_id" value="{{ bagger.id }}">
            <input type="hidden" name="location_name" id="location_name" value="{{ profile.location_name }}">
            <input type="hidden" name="location_city" id="location_city" value="">
            <input type="hidden" name="location_state" id="location_state" value="">
            <input type="hidden" name="location_country" id="location_country" value="">
            <input type="hidden" name="location_area_1" id="location_area_1" value="">
            <input type="hidden" name="location_area_2" id="location_area_2" value="">
            <input type="hidden" name="avatar_image" id="avatar_image" value="">
            {% csrf_token %}

            <div class="row row-full-width" style="padding-bottom: 60px;">
                <div class="col-md-12">
                    <span class="field-title" style="margin-top: 20px;">Your photo</span>
                    <div id="uploader"></div>
                </div>
            </div>

            <div class="row row-full-width" style="padding-bottom: 60px;">
                <div class="col-md-12">
                    <span class="field-title" style="margin-top: 20px;">About you</span>
                    <fieldset class="userName">
                        <textarea onkeyup="textAreaAdjust();" class="route-step-text" name="about-user" id="about-user" style="display: block; padding-right: 30px; height: 100px; resize: none; width: 100%; border: 1px solid #CCC; padding: 8px 10px; overflow: hidden;" placeholder="about you...">{% if profile.about_me %}{{ profile.about_me }}{% endif %}</textarea>
                    </fieldset>
                </div>
            </div>

            <div class="row row-full-width" style="padding-bottom: 20px;">
                <div class="col-md-12">
                    <span class="field-title" style="margin-top: 20px;">Your home basecamp <span class="header-help hidden-xs" style="margin-top: 16px;">ex: <span class="basecamp-hint" style="color: #00b1f2;">Denver, CO</span> or <span class="basecamp-hint" style="color: #00b1f2;">Calgary, AB</span> or <span class="basecamp-hint" style="color: #00b1f2;">Chamonix, France</span></span></span>
                    <fieldset>
                        <div id="geocoder" class="geocoder"></div>
                    </fieldset>
                </div>
            </div>

            <div class="row row-full-width" style="padding-bottom: 60px;">
                <div class="col-md-12">
                    <div id="peak-map-col" class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="padding-right: 0px; padding-left: 0px; background-image: url('{% static 'img/default.png' %}'); background-size: cover; border-bottom: solid 1px #e0e0e0;">
                        <div id="map-canvas" style="width: 100%; height: 100%;">
                            <div id="gm-custom-mapunits" class="gmnoprint gm-style-mtc" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 100px; top: 0px;">
                                <div id="gm-custom-mapunitsbutton" draggable="false" title="Change map units" style="cursor: pointer; direction: ltr; overflow: hidden; text-align: center; position: relative; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 11px; -webkit-background-clip: padding-box; background-clip: padding-box; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; font-weight: 500;">
                                    <span id="gm-custom-mapunitsbutton-label-feet" class="gm-custom-mapunits-selected">Feet</span> | <span id="gm-custom-mapunitsbutton-label-meters" class="gm-custom-mapunits-unselected">Meters</span>
                                </div>
                            </div>
                            <div id="gm-custom-maptype" class="gmnoprint gm-style-mtc" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 104px; right: 0px; top: 0px;">
                                <div id="gm-custom-mapbutton" draggable="false" title="Change map style" style="cursor: pointer; direction: ltr; overflow: hidden; text-align: left; position: relative; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 11px; -webkit-background-clip: padding-box; background-clip: padding-box; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; font-weight: 500;">
                                    <span id="gm-custom-mapbutton-label">Terrain</span><img src="https://maps.gstatic.com/mapfiles/arrow-down.png" draggable="false" style="-webkit-user-select: none; border: 0px; padding: 0px; margin: -2px 0px 0px; position: absolute; right: 6px; top: 50%; width: 7px; height: 4px;">
                                </div>
                                <div id="gm-custom-mapdropdown" style="background-color: white; z-index: -1; padding-left: 2px; padding-right: 2px; border-bottom-left-radius: 2px; border-bottom-right-radius: 2px; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; position: absolute; top: 100%; left: 0px; right: 0px; text-align: left; display: none;">
                                    <div id="gm-custom-mapoption-terrain" draggable="false" title="Show street map with terrain" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                        Terrain
                                    </div>
                                    <div id="gm-custom-mapoption-natatl" draggable="false" title="Show natural atlas" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                        Natural Atlas (US)
                                    </div>
                                    <div id="gm-custom-mapoption-outdoors" draggable="false" title="Show open topo map" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                        OpenTopoMap
                                    </div>
                                    <div id="gm-custom-mapoption-topo" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                        Topo Govt (as avail)
                                    </div>
                                    <div id="gm-custom-mapoption-satstreets" draggable="false" title="Show satellite imagery with street map" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                        Satellite
                                    </div>
                                    <div id="gm-custom-mapoption-sat" draggable="false" title="Show satellite imagery" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                        Satellite Topo
                                    </div>
                                </div>
                            </div>

                            <div id="message_map_div" class="gmnoprint gm-style-mtc" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 104px; right: 0px; top: 0px; display: none;">
                                <div id="message_map" draggable="false" style="direction: ltr; overflow: hidden; text-align: left; position: relative; color: rgb(86, 86, 86); font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 11px; border-bottom-left-radius: 2px; border-top-left-radius: 2px; border-bottom-right-radius: 2px; border-top-right-radius: 2px; -webkit-background-clip: padding-box; background-clip: padding-box; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; font-weight: 500;">

                                </div>
                            </div>

                        </div>
                        <div id="marker-tooltip" data-url="" data-index="" style="cursor: pointer; display: none; position:absolute; width: 200px; height: 150px; background: rgba(0, 0, 0, 0.3); margin: 15px;"></div>
                        <input type="hidden" name="basecamp-lat" id="basecamp-lat" value="{{ profile_stats.basecamp_x }}">
                        <input type="hidden" name="basecamp-lng" id="basecamp-lng" value="{{ profile_stats.basecamp_y }}">
                    </div>
                </div>
            </div>

            <div class="row row-full-width" style="padding-bottom: 60px;">
                <div class="col-md-12">
                    <span class="field-title" style="margin-top: 20px;">Favorite peak</span>
                    <fieldset>
                        <input id="id_favorite_item" style="width: 100%; min-width: 340px; max-width: 640px; padding: 10px;" maxlength="200" name="favorite_item" placeholder="URL of peak page on peakery..." type="url" value="{% if profile.favorite_item %}{{ profile.favorite_item }}{% endif %}">
                    </fieldset>
                </div>
            </div>

            <div class="row row-full-width" style="padding-bottom: 60px;">
                <div class="col-md-12">
                    <span class="field-title" style="margin-top: 20px;">Next summit goal</span>
                    <fieldset>
                        <input id="id_next_item_goal" style="width: 100%; min-width: 340px; max-width: 640px; padding: 10px;" maxlength="200" name="next_item_goal" placeholder="URL of peak page on peakery..." type="url" value="{% if profile.next_item_goal %}{{ profile.next_item_goal }}{% endif %}">
                    </fieldset>
                </div>
            </div>

            <div class="row row-full-width" style="padding-bottom: 60px;">
                <div class="col-md-12">
                    <span class="field-title" style="margin-top: 20px;">Email <span class="header-help">not shown publicly</span></span>
                    <fieldset>
                        <input id="id_email" style="width: 100%; min-width: 340px; max-width: 640px; padding: 10px;" maxlength="75" name="email" type="email" value="{% if bagger.email %}{{ bagger.email }}{% endif %}">
                    </fieldset>
                </div>
            </div>

            <div class="row row-full-width" style="padding-bottom: 20px;">
                <div class="col-md-12">
                    <span class="field-title" style="margin-top: 20px;">Your other pages</span>
                    <fieldset>
                        <label id="label-facebook-page" style="position: absolute; width: 40px; text-align: right;"><i style="color: #30bef2; margin-left: 5px;" class="fab fa-facebook fa-2x"></i></label>
                        <input id="id_facebook_page" style="margin-bottom: 20px; width: 100%; min-width: 340px; max-width: 640px; padding-left: 55px; padding-top: 10px; padding-bottom: 10px; padding-right: 10px;" maxlength="200" name="facebook_page" placeholder="Facebook page (full URL)..." type="url" value="{% if profile.facebook_page %}{{ profile.facebook_page }}{% endif %}">
                    </fieldset>
                    <fieldset>
                        <label id="label-twitter-username" style="position: absolute; width: 40px; text-align: right;"><i style="color: #30bef2; margin-left: 5px;" class="fab fa-twitter fa-2x"></i></label>
                        <input id="id_twitter_username" style="margin-bottom: 20px; width: 100%; min-width: 340px; max-width: 640px; padding-left: 55px; padding-top: 10px; padding-bottom: 10px; padding-right: 10px;" maxlength="50" name="twitter_username" placeholder="Twitter username..." type="text" value="{% if profile.twitter_username %}{{ profile.twitter_username }}{% endif %}">
                    </fieldset>
                    <fieldset>
                        <label id="label-instagram-username" style="position: absolute; width: 40px; text-align: right;"><i style="color: #30bef2; margin-left: 5px;" class="fab fa-instagram fa-2x"></i></label>
                        <input id="id_instagram_username" style="margin-bottom: 20px; width: 100%; min-width: 340px; max-width: 640px; padding-left: 55px; padding-top: 10px; padding-bottom: 10px; padding-right: 10px;" maxlength="50" name="instagram_username" placeholder="Instagram username..." type="text" value="{% if profile.instagram_username %}{{ profile.instagram_username }}{% endif %}">
                    </fieldset>
                    <fieldset>
                        <label id="label-flicker-page" style="position: absolute; width: 40px; text-align: right;"><i style="color: #30bef2; margin-left: 5px;" class="fab fa-flickr fa-2x"></i></label>
                        <input id="id_flicker_page" style="margin-bottom: 20px; width: 100%; min-width: 340px; max-width: 640px; padding-left: 55px; padding-top: 10px; padding-bottom: 10px; padding-right: 10px;" maxlength="200" name="flicker_page" placeholder="Flickr page (full URL)..." type="url" value="{% if profile.flicker_page %}{{ profile.flicker_page }}{% endif %}">
                    </fieldset>
                    <fieldset>
                        <label id="label-website" style="position: absolute; width: 40px; text-align: right;"><i style="color: #30bef2; margin-left: 5px;" class="fa fa-desktop fa-2x"></i></label>
                        <input id="id_website" style="margin-bottom: 20px; width: 100%; min-width: 340px; max-width: 640px; padding-left: 55px; padding-top: 10px; padding-bottom: 10px; padding-right: 10px;" maxlength="200" name="website" placeholder="your blog or other page..." type="url" value="{% if profile.website %}{{ profile.website }}{% endif %}">
                    </fieldset>
                </div>
            </div>

            <div class="row row-full-width" style="padding-bottom: 20px;">
                <div class="col-md-12">
                    <span class="field-title" style="margin-top: 20px;">Change username</span>
                    <fieldset>
                        <input id="id_new_username" style="margin-bottom: 20px; width: 100%; min-width: 340px; max-width: 640px; padding-top: 10px; padding-bottom: 10px; padding-right: 10px;" maxlength="40" name="new_username" placeholder="New username..." type="text">
                    </fieldset>
                </div>
            </div>

        </div>

    </div>

    </form>

<style type="text/css">
    .ui-autocomplete {
        max-height: 100px;
        overflow-y: auto;
        /* prevent horizontal scrollbar */
        overflow-x: hidden;
        /* add padding to account for vertical scrollbar */
        padding-right: 20px;
    }
        /* IE 6 doesn't support max-height
       * we use height instead, but this forces the menu to always be this tall
       */
    * html .ui-autocomplete {
        height: 100px;
    }
</style>

    <div class="message-modal modal fade" id="message-modal" tabindex="-1" role="dialog" aria-labelledby="message-modal-label">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                  <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                  <h4 class="modal-title" id="message-modal-label"></h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div id="message-modal-body" class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="margin-bottom: 5px; font-size: 16px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="confirm-modal modal fade" id="confirm-modal" tabindex="-1" role="dialog" aria-labelledby="confirm-modal-label">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                  <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                  <h4 class="modal-title" id="confirm-modal-label"></h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div id="confirm-modal-body" class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="margin-bottom: 5px; font-size: 16px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script type="text/javascript">

$('#map-canvas').height(500);

var uploaderIdle = true;
var formSubmitted = false;
var formCancelled = false;
var map;
var viewer;
var topo;
var outdoors;
var center = null;
var map_bounds;
var init = false;
var photos_displayed = 0;
var photos_page = 1;
var photos = [];
var iconstyle;
var geocoder;

var markersArray = [];

function loadMarker() {

    var latLng = [{{ profile_stats.basecamp_y }}, {{ profile_stats.basecamp_x }}];
    var is_draggable = false;
    iconstyle = 'marker_icon_basecamp_flag';

    //add marker
    //create an HTML element for the marker
    var el = document.createElement('div');
    el.className = iconstyle;

    var marker = new mapboxgl.Marker(el)
        .setLngLat(latLng)
        .setOffset([-5, -10])
        .setDraggable(is_draggable)
        .addTo(map);

    markersArray.push(marker);

}

function initialize() {

    var window_width = $(window).width();
    if (window_width < 768) {
        $("div#map-canvas").height(300);
    } else {
        $("div#map-canvas").height(500);
    }

    var mapDiv = document.getElementById('map-canvas');
    var latLng = [{{ profile_stats.basecamp_y }}, {{ profile_stats.basecamp_x }}];
    var LatLngList = [];

    var mapZoom = 10;

    if (isTouchDevice()) {
        map = new mapboxgl.Map({
            container: mapDiv, // HTML container id
            style: mapStyle, // style URL
            center: latLng, // starting position as [lng, lat]
            zoom: mapZoom,
            scrollZoom: false
        });
    } else {
        map = new mapboxgl.Map({
            container: mapDiv, // HTML container id
            style: mapStyle, // style URL
            center: latLng, // starting position as [lng, lat]
            zoom: mapZoom,
            scrollZoom: false
        });
        scale = new mapboxgl.ScaleControl({maxWidth: 120, unit: 'imperial'});
        map.addControl(scale, 'bottom-right');
        var nav = new mapboxgl.NavigationControl({showCompass: false});
        map.addControl(nav, 'bottom-right');
        // disable map rotation using right click + drag
        map.dragRotate.disable();
        // disable map rotation using touch rotation gesture
        map.touchZoomRotate.disableRotation();
    }

    function calculateCenter() {
      center = map.getCenter();
    }

    map.on('move', function(e) {
        $('#edit_user').prop('disabled', false);
        //recenter main peak marker
        var mapCenter = map.getCenter();
        var latitude = mapCenter.lat;
        var longitude = mapCenter.lng;
        $('#basecamp-lat').val(latitude);
        $('#basecamp-lng').val(longitude);
        markersArray[0].setLngLat(mapCenter);
    });

    map.on('load', function () {
        calculateCenter();
        var mapUnits = readCookie('map_units');
        if (mapUnits != '') {
            toggleMapUnits(mapUnits);
        }
        setMapControls();
    });

    map.on('click', function () {
        map.scrollZoom.enable();
    });

    loadMarker();



    const coordinatesGeocoder = function (query) {
        // Match anything which looks like
        // decimal degrees coordinate pair.
        const matches = query.match(
            /^[ ]*(?:Lat: )?(-?\d+\.?\d*)[, ]+(?:Lng: )?(-?\d+\.?\d*)[ ]*$/i
        );
        if (!matches) {
            return null;
        }

        function coordinateFeature(lng, lat) {
            return {
                center: [lng, lat],
                geometry: {
                    type: 'Point',
                    coordinates: [lng, lat]
                },
                place_name: 'Lat: ' + lat + ', Long: ' + lng,
                place_type: ['coordinate'],
                properties: {},
                type: 'Feature'
            };
        }

        const coord1 = Number(matches[1]);
        const coord2 = Number(matches[2]);
        const geocodes = [];

        geocodes.push(coordinateFeature(coord1, coord2));

        return geocodes;
    };


    geocoder = new MapboxGeocoder({
        accessToken: mapboxgl.accessToken,
        mapboxgl: mapboxgl,
        types: 'country, region, place',
        reverseGeocode: true,
        localGeocoder: coordinatesGeocoder,
        flyTo: {duration: 0},
        limit: 10,
        marker: false,
        render: function (item) {
            // extract the item's maki icon or use a default
            const maki = item.properties.maki || 'marker';
            return `<div class='geocoder-dropdown-item'>
        <span class='geocoder-dropdown-text'>
        ${item.place_name.replace(', United States', '')}
        </span>
        </div>`;
        }
    });

    document.getElementById('geocoder').appendChild(geocoder.onAdd(map));

    geocoder.on('result', e => {
        $('#location_name').val(e.result.text);
        $('#edit_user').prop('disabled', false);
    });
}

function setMapControls() {
    var check = checkIfMapboxStyleIsLoaded();
    if (!check) {
        // It's not safe to manipulate layers yet, so wait 200ms and then check again
        setTimeout(function() {
            setMapControls();
        }, 200);
        return;
    }
    // Whew, now it's safe to manipulate layers!
    var mapUnits = readCookie('map_units');
    if (mapUnits == 'meters') {
        toggleMapUnits(mapUnits);
    }
}

function addExtraMapLayers() {
    var check = checkIfMapboxStyleIsLoaded();
    if (!check) {
        setTimeout(function() {
            addExtraMapLayers();
        }, 200);
        return;
    }
}

function checkIfNewUsernameAlreadyExists(newUsername) {
    return new Promise((resolve, reject) => {
        $.getJSON(`/members/${encodeURIComponent(newUsername)}/exists/`, function (data) {
            resolve(data.exists === "true");
        }).fail(function () {
            $("#username-feedback").text("Error checking username. Please try again.").css("color", "orange");
            reject(false);
        });
    });
}

$(document).ready(function(){

    initialize();

    //switch map units
    $("#gm-custom-mapunits").click(function(){
        if ($('#gm-custom-mapunitsbutton-label-meters').hasClass('gm-custom-mapunits-selected')) {
            toggleMapUnits('feet');
            scale.setUnit('imperial');
        } else {
            toggleMapUnits('meters');
            scale.setUnit('metric');
        }
    });

    //Disable scrollZoom
    $('#map-canvas').on('mouseleave', function() {
        map.scrollZoom.disable();
    });

    //Custom Google Map type stuff

    $('#gm-custom-mapbutton').on('mouseenter', function(){
       $('#gm-custom-mapdropdown').show();
    });

    $('#gm-custom-mapbutton').on('mouseleave', function(){
       $('#gm-custom-mapdropdown').hide();
    });

    $('#gm-custom-mapbutton').on('touchstart', function() {
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-satstreets').on('click', function() {
        toggleMapType('satellite');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-satstreets').on('touchstart', function() {
        toggleMapType('satellite');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-satstreets').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
    });

    $('#gm-custom-mapoption-satstreets').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
    });

    $('#gm-custom-mapoption-topo').on('click', function() {
        toggleMapType('caltopo');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-topo').on('touchstart', function() {
        toggleMapType('caltopo');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-topo').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
    });

    $('#gm-custom-mapoption-topo').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
    });

    $('#gm-custom-mapoption-sat').on('click', function() {
        toggleMapType('sat_topo');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-sat').on('touchstart', function() {
        toggleMapType('sat_topo');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-sat').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
    });

    $('#gm-custom-mapoption-sat').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
    });

    $('#gm-custom-mapoption-terrain').on('click', function() {
        toggleMapType('terrain');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-terrain').on('touchstart', function() {
        toggleMapType('terrain');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-terrain').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
    });

    $('#gm-custom-mapoption-terrain').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
    });

    $('#gm-custom-mapoption-outdoors').on('click', function() {
        toggleMapType('outdoors');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-outdoors').on('touchstart', function() {
        toggleMapType('outdoors');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-outdoors').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
    });

    $('#gm-custom-mapoption-outdoors').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
    });

    //Natural atlas stuff
    $('#gm-custom-mapoption-natatl').on('click', function() {
        toggleMapType('natural_atlas');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-natatl').on('touchstart', function() {
        toggleMapType('natural_atlas');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-natatl').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
    });

    $('#gm-custom-mapoption-natatl').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
    });

    $('#about-user').on('input', function() {
        $('#edit_user').prop('disabled', false);
    });

    $('#user-name').on('input', function() {
        $('#edit_user').prop('disabled', false);
    });

    $('#id_favorite_item').on('input', function() {
        $('#edit_user').prop('disabled', false);
    });

    $('#id_next_item_goal').on('input', function() {
        $('#edit_user').prop('disabled', false);
    });

    $('#id_email').on('input', function() {
        $('#edit_user').prop('disabled', false);
    });

    $('#id_facebook_page').on('input', function() {
        $('#edit_user').prop('disabled', false);
    });

    $('#id_twitter_username').on('input', function() {
        $('#edit_user').prop('disabled', false);
    });

    $('#id_instagram_username').on('input', function() {
        $('#edit_user').prop('disabled', false);
    });

    $('#id_new_username').on('input', function() {
        $('#edit_user').prop('disabled', false);
    });

    $('#id_flicker_page').on('input', function() {
        $('#edit_user').prop('disabled', false);
    });

    $('#id_website').on('input', function() {
        $('#edit_user').prop('disabled', false);
    });

$('#edit_user').on('click', async function (e) {
    e.preventDefault();
    const newUsername = $('#id_new_username').val().trim();

    if (newUsername !== '') {
        const exists = await checkIfNewUsernameAlreadyExists(newUsername);
        if (exists) {
            alert("The desired username is already taken. Please try again.");
            return;
        }
    }

    formSubmitted = true;
    $('#edit_user').html('<i class="fa fa-spinner fa-spin"></i>');
    if (uploaderIdle) {
        reverseGeoCode();
    }
});

    $(document).on("keydown", ":input:not(textarea)", function(event) {
        if (event.keyCode == 13) {
            return false;
        } else {
            return event.keyCode;
        }
    });

    textAreaAdjust();

    var allowedExtensions = ['jpg', 'jpeg'];
    var uploader = new qq.s3.FineUploader({
        debug: false,
        element: document.getElementById('uploader'),
        template: 'qq-image-template',
        request: {
            endpoint: 'https://peakery-media.s3.amazonaws.com',
            accessKey: 'AKIAJPWBM4YZXFHWBTPQ'
        },
        signature: {
            endpoint: '{% url "s3signature" %}'
        },
        uploadSuccess: {
            endpoint: '{% url "s3_profile_photo_upload" %}',
            params: {
                'user_id': '{{ bagger.id }}'
            }
        },
        {% if avatar.avatar_url %}
        session: {
            endpoint: '{% url "s3_profile_photo_init" %}',
            params: {
                'user_id': '{{ user.id }}'
            }
        },
        {% endif %}
        iframeSupport: {
            localBlankPagePath: '/api/s3blank/'
        },
        retry: {
           enableAuto: false // defaults to false
        },
        validation: {
            acceptFiles: ['image/jpg, image/jpeg, .jpg, .jpeg'],
            allowedExtensions: allowedExtensions,
            sizeLimit: 10000000,
            image: {
                minHeight: 1000,
                minWidth: 1000
            }
        },
        messages: {
            typeError: 'Sorry, must be a JPG file.',
            sizeError: 'Sorry, this file is too big. Must be under 10 MB.',
            minHeightImageError: 'Sorry, this photo is too small. Must be at least 1000 x 1000 pixels.',
            minWidthImageError: 'Sorry, this photo is too small. Must be at least 1000 x 1000 pixels.'
        },
        showMessage: function (message) {
            $('#message-modal-label').html('Error');
            $('#message-modal-body').html(message);
            $('#message-modal').modal('show');
        },
        text: {
            fileInputTitle: 'Choose file(s)'
        },
        callbacks: {
            onSubmit: function(id, name) {},
            onSubmitted: function(id, name) {
                uploaderIdle = false;
                $('#edit_user').prop('disabled', false);
                $('.qq-upload-button-selector').hide();
                $('.qq-upload-list > li').each(function () {
                    $(this).find('.remove-photo').on('click', function() {
                        confirmRemovePhoto();
                    });
                });
            },
            onComplete: function(id, name, responseJSON, maybeXhr) {
                $('#avatar_image').val(responseJSON.image_filename);
            },
            onAllComplete: function(successful, failed) {
                uploaderIdle = true;
                if (formSubmitted) {
                    reverseGeoCode();
                }
                if (formCancelled) {
                    {% if avatar.avatar_url %}
                    $('#avatar_image').val('{{ avatar.avatar_url }}');
                    {% endif %}
                    window.location.href = '/members/{{ bagger }}';
                }
            },
            onCancel: function(id, name) {},
            onUpload: function(id, name) {},
            onUploadChunk: function(id, name, chunkData) {},
            onUploadChunkSuccess: function(id, chunkData, responseJSON, xhr) {},
            onResume: function(id, fileName, chunkData) {},
            onProgress: function(id, name, loaded, total) {},
            onTotalProgress: function(loaded, total) {},
            onError: function(id, name, reason, maybeXhrOrXdr) {
                $('#message-modal-label').html('Error');
                $('#message-modal-body').html('Error uploading ' + name + '. Hit the Retry button on the photo to try again.');
                $('#message-modal').modal('show');
            },
            onAutoRetry: function(id, name, attemptNumber) {},
            onManualRetry: function(id, name) {},
            onValidateBatch: function(fileOrBlobData) {},
            onValidate: function(fileOrBlobData) {},
            onSubmitDelete: function(id) {},
            onDelete: function(id) {},
            onDeleteComplete: function(id, xhrOrXdr, isError) {},
            onPasteReceived: function(blob) {},
            onStatusChange: function(id, oldStatus, newStatus) {},
            onSessionRequestComplete: function(response, success, xhrOrXdr) {
                if (success) {
                    for ( var i = 0; i < response.length; i++) {
                        var obj = response[i];
                        $('.qq-thumbnail-selector').attr('src', obj.thumbnail_url);
                        $('.remove-photo').on('click', function() {
                            confirmRemovePhoto();
                        });
                        $('#avatar_image').val(obj.name);
                    }
                }
            }
        },
        objectProperties: {
            acl: 'public-read',
            key: function (fileId) {

                var filename = uploader.getName(fileId);
                var uuid = uploader.getUuid(fileId);
                var ext = filename.substr(filename.lastIndexOf('.') + 1).toLowerCase();

                return  'avatars/{{ request.user.username }}/{{ request.user.id }}_' + uuid + '.' + ext;

            }
        }
    });

});

function reset_textareas(selector){
    var value = $(selector).val();
    if (value=='full details of your trip...' || value=='write a caption...'){$(selector).val("")}
    $(selector).removeClass("blur");
}

function jailai(selector){
    $("textarea#"+selector).effect("highlight", {}, 3000);
}

function reset_spinner(selector){
    $(selector).find('div.qq-upload-button').removeClass('qq-upload-loading');
    $(selector).find('div.qq-upload-button').children('div').hide();
};

function cancelEdit() {
    if ($('#edit_user').prop('disabled') == true) {
        formCancelled = true;
        {% if avatar.avatar_url %}
        $('#avatar_image').val('{{ avatar.avatar_url }}');
        {% endif %}
        if (uploaderIdle) {
            window.location.href = '/members/{{ bagger }}';
        }
    } else {
        $('#confirm-modal-label').html('Discard your changes?');
        $('#confirm-modal-body').html('<div style="margin-top: 20px; text-align: center;"><button id="confirm-cancel-edit" onclick="confirmCancelEdit(); return false;" class="btn btn-primary" style="width: 100px;">Discard</button><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">Cancel</button></div>');
        $('#confirm-modal').modal('show');
    }
}

function confirmCancelEdit() {
    formCancelled = true;
    $('#confirm-cancel-edit').html('<i class="fa fa-spinner fa-spin"></i>');
    {% if avatar.avatar_url %}
    $('#avatar_image').val('{{ avatar.avatar_url }}');
    {% endif %}
    if (uploaderIdle) {
        window.location.href = '/members/{{ bagger }}';
    }
}

function confirmRemovePhoto() {
    $('#confirm-modal-label').html('Are you sure?');
    $('#confirm-modal-body').html('<div style="margin-top: 20px; text-align: center;"><button onclick="removePhoto(); return false;" class="btn btn-primary" style="width: 100px;">Discard</button><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">Cancel</button></div>');
    $('#confirm-modal').modal('show');
}

function removePhoto() {
    $('#confirm-modal').modal('hide');
    $('#avatar_image').val('');
    $('.qq-upload-button-selector').show();
    $('.qq-file-id-0').remove();
    $('#edit_user').prop('disabled', false);
}

function fromLatLngToString(latLng) {
    return latLng.lat + ',' + latLng.lng;
}

function numberWithCommas(x) {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

function textAreaAdjust() {
    $('#about-user').height(1);
    var scrollHeight = document.getElementById("about-user").scrollHeight;
    $('#about-user').height(25+scrollHeight);
}

function reverseGeoCode() {
    var latlng = map.getCenter();
    var latitude = latlng.lat;
    var longitude = latlng.lng;


    $('#basecamp-lat').val(latitude);
    $('#basecamp-lng').val(longitude);

    $("#geocoder").hide()
    let result = geocoder.query(latitude + "," + longitude);

    function processReverseGeocodeResult(result) {
        let country = "";

        try {
            let data = result._typeahead.selected;
            country = data.place_name;
        } catch (e) {
        }

        $('#location_name').val(country);
        //save form
        $('.useredit_form').attr('action', '/members/{{ bagger }}/edit/');
        $('.useredit_form').submit();
    }

    setTimeout(function () {
        processReverseGeocodeResult(result);
    }, 300);

}

    function gmapsCallback() {
        // Empty function needed for the google maps callback
    }

</script>

{% load item_tags %}{% get_gmaps_lib %}
{% block gmaps_lib %}{% endblock %}

{% endblock %}

{% block end_full_height_form %}
</form>
{% endblock %}