{% load widget_tweaks %}
<div id="findFriendsContainer" class="signUpWrapper">
{#    <h1>Welcome</h1>#}
    <h2>Login to your Gmail to find friends already using peakery</h2>

    <form method="post" id="find_mail_friends" action="{% url "find_mail_friends" %}" class="peakeryForm" style="display: block; text-align: center;">
        <div class="clearfix" style="margin: 0px auto; width: 225px;">
            <span class="a">
                {{ form.email|attr:"placeholder:Email"|attr:'class:email' }}
            </span>
            {% if form.email.errors %}
                <span class="formError">{{ form.email.errors.0 }}</span>
            {% endif %}
            <span class="a">
                {{ form.password|attr:"placeholder:Password" }}
            </span>
            {% if form.password.errors %}
                <span class="formError">{{ form.password.errors.0 }}</span>
            {% endif %}
            {% if login_failed %}
                <div id="loginFailed">
                    <h3><PERSON><PERSON> failed.</h3>
                    <p>Please check the email and password you have provided and try again.</p>
                </div>
            {% endif %}
            <span>

            <span class="spinner" style="z-index:1000;display: none; background: url({{ MEDIA_URL }}img/misc/loading.gif) no-repeat center center; position: absolute; top: -3px; left: 7px;width: 100%; height: 100%;"></span>
            <input type="submit" value="Find friends" class="btn" style="margin-top: 20px;">
                </span>
        </div>
    </form>
</div>

<script language="javascript">
    $(document).ready(function(){
        $('form#find_mail_friends').submit(function() {
            form = $(this);
            $(".spinner").show();
            $(this).ajaxSubmit({
                target: 'div#findFriendsContainer',
                success:    function(e) {
                    $(".spinner").hide();
                }
            });
            return false;
        });
    });
</script>