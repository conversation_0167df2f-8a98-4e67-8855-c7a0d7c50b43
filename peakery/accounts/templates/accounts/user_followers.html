{% load avatar_tags follow_tags %}
{% block javascript %}
    <script type="text/javascript">
        $(document).ready(function() {
            var ini = parseInt({{ max_per_page }});


            $(document).bind("close.facebox",function(){
                var url = $("#ssi_follower").attr("lang");
                $("#ssi_follower").load(url);
                var url1 = $("#ssi_following").attr("lang");
                $("#ssi_following").load(url1);
            });

            $('a#see-more-followers').click(function() {
                $.get('{% url "get_more_user_followers" profile.id %}?page=' + ini, function(data){
                    $('ul.followersList').append(data);
                    ini += parseInt({{ max_per_page }});

                    if (ini >= parseInt({{ total_followers }})) {
                        $('a#see-more-followers').hide();
                    }
                });
            });
        });

        function followUser(url, id) {
            $.get(url, function(data) {
                if(data == 'True') {
                    $('a#follow-' + id).hide();
                    $('a#unfollow-' + id).show();
                }
            });
        }

        function unfollowUser(url, id) {
            $.get(url, function(data) {
                if(data == 'True') {
                    $('a#follow-' + id).show();
                    $('a#unfollow-' + id).hide();
                }
            });
        }
    // Unfollow button
$(function(){
    $('a.unfollowButton').hover(
            function(){
                $(this).removeClass('unfollowButton').addClass('unfollowButtonHover').text('Unfollow');
            },
            function(){
                $(this).removeClass('unfollowButtonHover').addClass('unfollowButton').text('You are following');
            });
});
    </script>
    <link rel="stylesheet" href="{{ MEDIA_URL }}css/lightboxes/django-follow.css" type="text/css" media="screen" charset="utf-8" />
{% endblock %}

{% block content %}
    <div id="userFollowers" class="djangoFollow">
        {% if request.user == profile %}
            <h3>You have <span>{{ total_followers }}</span> followers</h3>
        {% else %}
            <h3>{{ profile.username }} has <span>{{ total_followers }}</span> followers</h3>
        {% endif %}
        <ul class="followersList">
            {% for follower in followers %}
                {% include 'accounts/user_followers_include.html' %}
            {% endfor %}
        </ul><!-- /.followerList -->
        {% if total_followers > max_per_page %}
            <a id="see-more-followers" href="#seeMore" class="seeMoreButton">see more ...</a>
        {% endif %}
    </div><!-- /#userFollowers -->
{% endblock %}