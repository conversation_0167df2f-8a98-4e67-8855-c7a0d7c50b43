{% extends "base.html" %}

{% load item_tags %}
{% load avatar_tags %}
{% block extrajs %}

{% endblock %}
{% block title %}Peakery Activity{% endblock %}
{% block titlemeta %}Peakery Activity - {% endblock %}
{% block description %}Peakery Activity{% endblock %}
{% block image_rel %}{% endblock %}
{% block activity_active %}active{% endblock %}

{% block content %}
    <script type="text/javascript">
        $(document).ready(function(){
            $("#notificationsCounter").prev().children().addClass('active');
        });
    </script>

    <div class="peakeryNewsMainContainer grayContainer clearfix">
        <div class="nav clearfix">
            <a href="{% url "user_news" %}" id="filter_all" class="btn {% if filter == 'all' %}selected{% endif %}">
                All activity
            </a>
            <a href="{% url "user_news" 'you' %}" id="filter_you" class="btn {% if filter == 'you' %}selected{% endif %}">
                Your activity
            </a>
            <a href="{% url "user_news" 'following' %}" id="filter_following" class="btn {% if filter == 'following' %}selected{% endif %}">
                Members you follow
            </a>
        </div>
        {% include "accounts/ajax/more_news.html" %}
        {% if notices %}
            <div id="render-more-news-data">
                <input type="hidden" id="from" name="from" value="{{ from }}">
                <input type="hidden" id="each" name="each" value="{{ each }}">
                <input type="hidden" id="total" name="total" value="{{ total }}">
            </div>
            <div id="render-more-news">
            </div>
            {% if total > notices|length %}
            <div id="seemore">
                <a id="seemore-news" class="seemore" href="javascript:void(0);">See more…</a>
            </div>
            {% endif %}
        {% endif %}
    </div><!-- end .peakeryNewsMainContainer-->

   <script type="text/javascript">
        //summit log pagination
        $(document).ready(function(){
            $("#seemore-news").click(function(){
                var btn = $(this);
                btn.text("loading...");
                var from = parseInt($("input#from").val());
                var each = parseInt($("input#each").val());
                var total = parseInt($("input#total").val());
                var to = from + each;

                $.get('{% url "user_news" filter %}',
                        {'from':from, 'to':to},
                        function(data){
                            $("div#render-more-news-data").append(data);
                            btn.text("see more");
                            if(to>=total){
                                btn.hide();
                            }
                        });

                $("input#from").val(to);
            });
        });
    </script>


{% endblock %}

