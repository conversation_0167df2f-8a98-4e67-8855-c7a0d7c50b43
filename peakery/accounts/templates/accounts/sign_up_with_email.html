{% load widget_tweaks %}
<div id="registerFaceboxWrapper">
    <div id="register_container" class="signUpWithEmail signUpWrapper" {% if next %}{% endif %}>
        {{ form.non_field_errors }}
        <form method="post" id="register_form{{ generated_id }}" class=" {{ window }}">
            {#<span class="a"><input type="text" placeholder="First name" id="firstName"></span>#}
        <span class="a autofocus">
            {{ form.first_name|attr:"class:userData"|attr:"placeholder:First name" }}
        </span>
        <div id="signup-firstname-icon-div" style="position: absolute;top: 18px;left: 15px;">
            <span id="signup-firstname-icon-span"><i style="color: #999;" class="fa fa-user"></i></span>
        </div>
            {% if form.first_name.errors %}
                <span class="formError">
                {{ form.first_name.errors }}
            </span>
            {% endif %}
            {#<span class="a"><input type="text" placeholder="Last name" id="lastName"></span>#}
        <span class="a">
            {{ form.last_name|attr:"class:userData"|attr:"placeholder:Last name" }}
        </span>
        <div id="signup-lastname-icon-div" style="position: absolute;top: 84px;left: 15px;">
            <span id="signup-lastname-icon-span"><i style="color: #999;" class="fa fa-user"></i></span>
        </div>
            {% if form.last_name.errors %}
                <span class="formError">
                {{ form.last_name.errors }}
            </span>
            {% endif %}
            {#<span class="a"><input type="email" placeholder="Email address" id="email"></span>#}
        <span class="a">
            {{ form.email|attr:"class:email"|attr:"type:email"|attr:"placeholder:Email" }}
        </span>
        <div id="signup-email-icon-div" style="position: absolute;top: 148px;left: 15px;">
            <span id="signup-email-icon-span"><i style="color: #999;" class="fa fa-envelope"></i></span>
        </div>
            {% if form.email.errors %}
                <span class="formError">
                <ul class="errorlist">
                    <li>{{ form.email.errors }}</li>
                </ul>
            </span>
            {% endif %}
            {#<span class="a"><input type="password" placeholder="Password" id="password"></span>#}
        <span class="a" id="password_secret_span">
            {{ form.password1|attr:"class:password"|attr:"placeholder:Password" }}
        </span>
        <span class="a" style="display: none" id="password_inblank_span">
            <input class="password" id="id_password_in_blank">
        </span>
        <div id="signup-password-icon-div" style="position: absolute;top: 212px;left: 15px;">
            <span id="signup-password-icon-span"><i style="color: #999;" class="fa fa-lock"></i></span>
        </div>
        <div id="signup-showpassword-icon-div" style="position: absolute;top: 213px;left: 253px;">
            <span id="showPassword"><i style="color: #999; cursor: pointer;" class="fa fa-eye"></i></span>
        </div>

            {% if form.password1.errors %}
                <span class="formError">
                {{ form.password1.errors }}
            </span>
            {% endif %}

            <span style="display:none">
                {{ form.password2|attr:"placeholder:Password"|attr:"class:password" }}
            </span>

            <span id="showPasswordWrapper" style="display: none;">
            <input type="checkbox" id="showPasswordCheckbox"><label for="showPassword" style="color: #666;">show password</label>
            <input type="hidden" id="is_android" name="is_android" value="">
        </span>
            <p class="terms">By signing up you accept the <a href="/terms/" target="_blank" style="text-decoration: none;">Terms</a> </p>
            <div class="clearfix btnWrapper">
                <input type="submit" class="btn" value="Sign Up">
                <a id="next" href="{{ next }}" rel="facebox"></a>
            </div>
            <div style="text-align: center;height: 50px;line-height: 60px;">
                Already a member? <a style="font-weight: 500; cursor: pointer;" id="already-a-member">Login</a>
            </div>
        </form>
    </div>


<script type="text/javascript">

    $(document).ready(function(){


        // $('a#next').facebox();


        $('#showPassword').click(function()
        {
            $('#showPasswordCheckbox').click();
            //checked
            var checked = $('#showPasswordCheckbox:checked').val() != undefined;
            console.log(checked);
            if (checked) {
                $("#password_secret_span").hide();
                $("#password_inblank_span").show();
                $('#showPassword').html('<i style="color: #999; cursor: pointer;" class="fa fa-eye-slash"></i>');
            } else {
                $("#password_secret_span").show();
                $("#password_inblank_span").hide();
                $('#showPassword').html('<i style="color: #999; cursor: pointer;" class="fa fa-eye"></i>');
            }
        });

        $('#id_password1').keyup(function(){
            $("#id_password_in_blank").val($(this).val());

        });

        $("#id_password_in_blank").keyup(function(){
            $("#id_password1").val($(this).val());

        });

        $('form#register_form{{ generated_id }}').submit(function(e) {
            e.preventDefault();
            form = $(this);
            //debugger;
            $(this).ajaxSubmit({
                target: 'div#registerFaceboxWrapper',
                url: '{% url "register" %}?action={{ action }}{% if next %}&next={{ next }}{% endif %}',
                success:    function(e) {
                    try {
//                        var o = jQuery.parseJSON(e);
                        var o = e;
//                        console.log(o);
                        if (o.sucess) {
                            //$("#signup_menu").hide();
                            $('#accounts-sign-up').modal('hide');
                            if (o.next != false) {
                                //if (cont <2) {
                                    //jQuery.facebox({ ajax: o.next });
                                //}
                                //cont += 1; // using cont from peakery.js
                                $('#accounts-choose-username .modal-body').load(o.next);
                                $('#accounts-choose-username').modal('show');
                            }
                            if (o.action == 'go_to_profile') {
                                $("div#register_container").hide();
                                location = "/members/" + o.value + "/";
                            }
                            if (o.action == 'summit_flow') {
                                $("#step1").trigger('click');
                            }
                        }
                    } catch(except){
                        //console.log(except);
                    }
                }
            });
            return false;
        });

        var currentURL = window.location.pathname;
        if (currentURL.indexOf('about') < 0) {
            $('form#register_form{{ generated_id }} #id_email').select();
        }

        {# copy the passw1 to passw2 to prevent password confirmation errors #}
        $('form#register_form{{ generated_id }} #id_password1').change(function(){
            $('form#register_form{{ generated_id }} #id_password2').val($(this).val());
        });

        if("{{action}}"=="go_to_profile"){
            $("form#register_form{{ generated_id }} input[type='submit']").val("SIGN UP");
            $("form#register_form{{ generated_id }} div").css("width","99%").css("padding","0");
        }

        $("#reg-btn").click(function(){
            $(this).hide();
            $("#reg-btn2").show();
        });



    });
    $(function(){
        $('input, textarea').placeholder();
        $('span.a.autofocus input').autofocus();
    });
</script>
</div>