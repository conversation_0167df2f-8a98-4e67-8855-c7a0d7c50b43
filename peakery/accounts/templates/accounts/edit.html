<div id="settingsContainer">
{% load widget_tweaks %}
{% load avatar_tags %}

{% load i18n %}
{% if show_javascript %}
{% block javascript %}
    <style>
        .ui-autocomplete {
            max-height: 200px;
            overflow-y: auto;
            /* prevent horizontal scrollbar */
            overflow-x: hidden;
            /* add padding to account for vertical scrollbar */
            padding-right: 20px;
        }
            /* IE 6 doesn't support max-height
            * we use height instead, but this forces the menu to always be this tall
            */
        * html .ui-autocomplete {
            height: 200px;
        }

        .ui-autocomplete-loading { background: white url('{{ MEDIA_URL }}img/misc/ajax1.gif') right center no-repeat; }

        form.form_set1 label {
            text-align: left;
            width: 100%;
            font-size: 21px;
            margin-bottom: 10px;
        }
    
        form input[type='text'] {
            font-size: 21px;
            font-weight: bold;
            border-color: #a8a8a8;
            padding: 8px 10px;
            margin-bottom: 20px;
            width: 56%;
        }

        form input[type='email'] {
            font-size: 21px;
            font-weight: bold;
            border-color: #a8a8a8;
            padding: 8px 10px;
            margin-bottom: 20px;
            width: 56%;
        }

        form input[type='url'] {
            font-size: 21px;
            font-weight: bold;
            border-color: #a8a8a8;
            padding: 8px 10px;
            margin-bottom: 20px;
            width: 56%;
        }
    
    </style>

    <script type="text/javascript">
        $(document).ready(function() {

            var modalDiv = $(".edit-profile-modal");
            setModalMaxHeight(modalDiv);

            function setModalMaxHeight(element) {
                this.$element = $(element);
                this.$content = this.$element.find('.modal-content');
                var borderWidth = this.$content.outerHeight() - this.$content.innerHeight();
                var dialogMargin = $(window).width() < 768 ? 20 : 60;
                var contentHeight = $(window).height() - (dialogMargin + borderWidth);
                var headerHeight = this.$element.find('.modal-header').outerHeight() || 0;
                var footerHeight = this.$element.find('.modal-footer').outerHeight() || 0;
                var maxHeight = contentHeight - (headerHeight + footerHeight) - 100;

                this.$content.css({
                    'overflow': 'hidden'
                });

                this.$element
                        .find('.modal-body').css({
                            'max-height': maxHeight,
                            'overflow-y': 'auto'
                        });
            }

            $('.edit-profile-modal').on('show.bs.modal', function () {
                $(this).show();
                setModalMaxHeight(this);
            });

            $(window).resize(function () {
                if ($('.edit-profile-modal.in').length != 0) {
                    setModalMaxHeight($('.edit-profile-modal.in'));
                }
            });

            $('input[title!=""]').hint();

            if ($("#id_about_me").val()=="about you..."){$("#id_about_me").addClass("blur")}
            $('form#edit_profile_form').submit(function() {
                if ($("#id_about_me").val()=="about you..."){$("#id_about_me").val("")}
                $(this).ajaxSubmit({
                    target: 'div#edit-profile-tabs',
                    success:    function(e) {
                        $( "div#edit-profile-tabs" ).tabs();
                        location.reload(true);
                        if(e == "<head></head><body>True</body>" || e == "True"){
                            $("div#edit_form").hide();
                            $("div#edit-profile-tabs").html("Saving...");
                            location.reload(true);
                        }
                    }
                });
                return false;
            });
            $( "#location_city" ).autocomplete({
                source: "{% url "autocomplete_city" %}",
                minLength: 2
            });
            if($("#id_about_me").text()==""){
                $("#id_about_me").text("about you...");
            }

            $("#id_about_me").click(function(){
                if($(this).val()=="about you..."){
                    $(this).select();
                    $(this).removeClass("blur");
                }
            });



            var uploader1 = new qq.FileUploader({
                element: document.getElementById('file-1'),
                action: '{% url "upload_avatar" %}',
                params: {param: 1},
                debug: false,

                onSubmit: function(id, fileName){ },
                onProgress: function(id, fileName, loaded, total){
                    $("#file-1").text("uploading...");
                },
                onComplete: function(id, fileName, responseJSON){ set_image('file-1', responseJSON);},
                onCancel: function(id, fileName){},
                messages: {},
                showMessage: function(message){}
            });






            if("{{ has_avatar }}" == "True"){
                div = $("div.qq-upload-button");
                div.removeClass("qq-upload-button");
                div.addClass("qq-upload-button-virgin");
                div.css("background","url('{% avatar_url user 300 %}')");
                div.css("width","300px");
                div.css("height","240px");
            }

            //$("#change_avatar").click(function(){ $("input[name='file']").trigger("click"); });
        });

        function set_image(where, responseJSON){
            image_url = responseJSON["image"];
            img = "<img src='"+image_url+"' >";
            $("div#"+where).html(img);
            $("textarea#"+where).attr('title',responseJSON["photo_id"]);
        }

        //# sourceURL=/accounts/edit/
    </script>
{% endblock %}
{% endif %}

{% block content %}
<form id="edit_profile_form" class="form_set1" action="{% url "user_edit" %}" method="post">
<div class="modal-header">
    <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
    <h4 class="modal-title" id="edit-profile-label">Edit your profile</h4>
</div>
<div class="modal-body" style="padding-top: 15px; padding-bottom: 0px;">
    <div id="edit_form" class="tab" style="width: 720px;">
        <div class="col_set" style="width: 720px;">
            <div class="col_1">
                <div id="file-1" class="default_pic">

                </div>
                {% if has_avatar %}<div id="change_avatar" style="text-align: center; font-size: 10px">click photo to change</div>{% endif %}
            </div>
            <div class="col_2" id="aboutMeContainer" style="left: 97px; position: relative; width: 420px;">
                {{ person_form.about_me|add_class:'about_me'|attr:'placeholder:About you ...' }}
                {{ person_form.about_me.errors }}
                <script type="text/javascript">
                    $(function(){
                        $('div#aboutMeContainer textarea').placeholder();
                    });
                </script>
            </div>
        </div>
        <fieldset>
            <label>Your peakery username </label>
            {{ user_form.username }}
            {{ user_form.username.errors }}
        </fieldset>

        <fieldset>
            <label>Your home basecamp </label>
            {{ person_form.location_name|attr:"title:ex: San Francisco" }}
            {{ person_form.location_name.errors }}
        </fieldset>

        <fieldset>
            <label>Favorite peak <span style="font-size: 14px; color: #999999;">paste the URL of the peak's page on peakery</span></label>
            {{ person_form.favorite_item|attr:"title:paste URL of the peak’s page on peakery" }}
            {{ person_form.favorite_item.errors }}
        </fieldset>

        <fieldset>
            <label>Next peak goal <span style="font-size: 14px; color: #999999;">paste the URL of the peak's page on peakery</span></label>
            {{ person_form.next_item_goal|attr:"title:paste URL of the peak’s page on peakery" }}
            {{ person_form.next_item_goal.errors }}
        </fieldset>

        <fieldset>
            <label>Email <span style="font-size: 14px; color: #999999;">not public</span></label>
            {{ user_form.email }}
            {{ user_form.email.errors }}
        </fieldset>

        <fieldset>
            <label>Your other pages</label>
        </fieldset>

        <fieldset>
            <label style="width: 10px;"><i style="color: #30bef2; margin-left: 5px;" class="fab fa-facebook"></i></label>
            {{ person_form.facebook_page|attr:"title:Facebook page (full URL)..." }}
            {{ person_form.facebook_page.errors }}
        </fieldset>

        <fieldset>
            <label style="width: 10px;"><i style="color: #30bef2; margin-left: 5px;" class="fa fa-twitter"></i></label>
            {{ person_form.twitter_username|attr:"title:Twitter username..." }}
            {{ person_form.twitter_username.errors }}
        </fieldset>

        <fieldset>
            <label style="width: 10px;"><i style="color: #30bef2; margin-left: 5px;" class="fa fa-flickr"></i></label>
            {{ person_form.flicker_page|attr:"title:Flickr username..."  }}
            {{ person_form.flicker_page.errors }}
        </fieldset>

        <fieldset>
            <label style="width: 10px;"><span style="color: #30bef2; font-size: 12px;">www&nbsp;</span></label>
            {{ person_form.website|attr:"title:your blog or other page..." }}
            {{ person_form.website.errors }}
        </fieldset>

    </div>
</div>
<div class="edit-profile-modal-footer">
    <input type="submit" value="Submit changes" class="btn btn-secondary" />
</div>
</form>

{% endblock %}