{% load widget_tweaks %}

<div id="o"></div>
<div class="containerContainer" style="padding-top:10px;">
    <script type="text/javascript">
        $(function(){
            $('#signup_menu h1.getYourOwnPeakPage').css('display','none');
        });
    </script>
    <div style="text-align:center; margin-bottom: 30px;" id="signUpDropdown">
    </div>

    <div style="width: 254px; text-align: center;">
        <a class="btn btn-secondary sign-up-with-email" style="width: 238px; height: 60px; line-height: 25px; font-size: 14px; margin-top: 10px;"><i class="fas fa-envelope fa-2x" aria-hidden="true"></i><span style="padding-left: 10px; top: -4px; position: relative;">Sign up with email</span></a>
    </div>

    <div style="text-align: center; height: 60px; line-height: 60px; margin-top: 20px;">
        Already a member? <a style="cursor: pointer; font-weight: 500;" id="already-a-member">Login</a>
    </div>
</div>

<script type="text/javascript">
    $(function(){
        $('a#emailSignUp').click(function(){
            $('.signUpWithFb').css('display','none');
            $('.signUpWithEmail').css('display','block');
        });
    });
</script>
<script type="text/javascript">
    $(document).ready(function(){

        $('form input[title!=""]').hint();
        $('a#next').facebox();
        swapField('register_form{{ generated_id }} #id_password1');
        $('form#register_form{{ generated_id }}').submit(function() {
            form = $(this);
            $(this).ajaxSubmit({
                target: 'div#register_container',
                success:    function(e) {
                    var o = jQuery.parseJSON(e);
                    if(o.sucess){
                        $("#signup_menu").hide();
                        if(o.next!=false){
                            jQuery.facebox({ ajax: o.next });
                        }
                        if (o.action=='go_to_profile'){
                            $("div#register_container").hide();
                            location = "/members/"+o.value+"/";
                        }
                        if (o.action=='summit_flow'){
                            $("#step1").trigger('click');
                        }
                    }
                }
            });
            return false;
        });

        var currentURL = window.location.pathname;

        if (currentURL.indexOf('about') < 0) {
            $('form#register_form{{ generated_id }} #id_email').select();
        }

        $('form#register_form{{ generated_id }} #id_password1').change(function(){
            $('form#register_form{{ generated_id }} #id_password2').val($(this).val());
        });

        if("{{action}}"=="go_to_profile"){
            $("form#register_form{{ generated_id }} input[type='submit']").val("SIGN UP");
            $("form#register_form{{ generated_id }} div").css("width","99%").css("padding","0");
        }

        $("#reg-btn").click(function(){
            $(this).hide();
            $("#reg-btn2").show();
        });
    });
</script>