{% load widget_tweaks %}
<script type="text/javascript">
    $(document).ready(function(){
        $('input[title!=""]').hint();
        $('a#next_url').facebox();
        $('form#login_form').ajaxForm({
            target: '#output',
            success:    function(e) {
                if(e=='True'){
                    $("#next_url").trigger('click');
                }else{
                    //error from django
                    $("p#message").html('Incorrect Password. <a href="#resetPassword">Forgot? Click to reset it.</a>');
                    $("input#id_password").val("");
                }
            }
        });
    });
</script>

<div id="peakeryLogin" class="lightbox">
    <div id="output" style="display: none"></div>
    <div id="login-form">
        <p id="message"></p>
        <form action="{% url "login_user_lite" %}" method="POST" id="login_form" class="peakeryForm">
            <input type="hidden" id="csrf_token" value='{"csrfmiddlewaretoken": "{{ csrf_token }}"}'>
            <span class="a">
                {{ form.username|attr:"placeholder:username or email address"|attr:"class:userData" }}
            </span>
            <span class="a">
                <input type="password" id="id_password" name="password" placeholder="Password">
            </span>
            <span class="clearfix" style="display: block; text-align: left; margin: 5px 0 10px;">
                <a href="javascript:void(0);" id="forgotPassword">Forgot?</a>
            </span>
            <input class="btn set2 input" type="submit" value="Login" />
        </form>
        <div class="singup" style="display: block; margin: 10px 0 5px; text-align: right;">
            <span class="notAMember">Not a member? <a rel="facebox" href="{% url "register" %}?next={{ next }}&window=lbox">Sign up</a></span>
        </div>
    </div>

    <a id="next_url" href="{{ next }}"></a>
</div>