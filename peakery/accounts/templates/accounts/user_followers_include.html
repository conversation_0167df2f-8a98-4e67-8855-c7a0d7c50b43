{% load avatar_tags follow_tags %}
<li>
    <span class="followerWrapper clearfix">
        <div class="leftCol">
            {% if follower != request.user  %}
            <a href="{{ follower.get_absolute_url }}" >{% avatar follower 65 %}</a>
            {% else %}
                {% avatar follower 65 %}
            {% endif %}
        </div><!-- /.leftCol -->
        <div class="rightCol">
            <h3>{% if follower != request.user  %}<a href="{{ follower.get_absolute_url }}">{{ follower.username }}</a>{% else %}You{% endif %}</h3>
            <p>
                {% if follower.get_profile.peaks_bagged_count %}
                    {{ follower.get_profile.peaks_bagged_count }}
                {% else %}
                    0
                {% endif %} peaks
                {% if follower.get_profile.location_name %}
                    &bull; {{ follower.get_profile.location_name }}
                {% endif %}
            </p>
            {% if follower != request.user %}
                {% if request.user == profile %}
                    {% is_following request.user follower %}
                    {% if is_following %}
                        <a id="follow-{{ follower.id }}" href="#" onclick="followUser('{% url "follow_user" follower.id %}', '{{ follower.id }}')" class="peakeryFollowButton followButton" style="display: none;">Follow</a>
                        <a id="unfollow-{{ follower.id }}" href="#" onclick="unfollowUser('{% url "unfollow_user" follower.id %}', '{{ follower.id }}')" class="peakeryFollowButton unfollowButton">You are following</a>
                    {% else %}
                        <a id="follow-{{ follower.id }}" href="#" onclick="followUser('{% url "follow_user" follower.id %}', '{{ follower.id }}')" class="peakeryFollowButton followButton">Follow</a>
                        <a id="unfollow-{{ follower.id }}" href="#" onclick="unfollowUser('{% url "unfollow_user" follower.id %}', '{{ follower.id }}')" class="peakeryFollowButton unfollowButton" style="display: none;">You are following</a>
                    {% endif %}
                {% else %}
                    {% is_following request.user follower %}
                    {% if is_following %}
                        <a id="follow-{{ follower.id }}" href="#" onclick="followUser('{% url "follow_user" follower.id %}', '{{ follower.id }}')" class="peakeryFollowButton followButton" style="display: none;">Follow</a>
                        <a id="unfollow-{{ follower.id }}" href="#" onclick="unfollowUser('{% url "unfollow_user" follower.id %}', '{{ follower.id }}')" class="peakeryFollowButton unfollowButton">You are following</a>
                    {% else %}
                        <a id="follow-{{ follower.id }}" href="#" onclick="followUser('{% url "follow_user" follower.id %}', '{{ follower.id }}')" class="peakeryFollowButton followButton">Follow</a>
                        <a id="unfollow-{{ follower.id }}" href="#" onclick="unfollowUser('{% url "unfollow_user" follower.id %}', '{{ follower.id }}')" class="peakeryFollowButton unfollowButton" style="display: none;">You are following</a>
                    {% endif %}
                {% endif %}
            {% endif %}
        </div><!-- /.rightCol -->
    </span><!-- /.followerWrapper -->
</li>
