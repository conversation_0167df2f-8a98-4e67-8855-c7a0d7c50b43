{% extends "base.html" %}
{% load static %}
{% load avatar_tags item_tags favorite_tags truncatechars verbatim %}
{% load humanize %}

{% block css %}
    <link href="{{MEDIA_URL}}css/view-peak.css?v=1" rel="stylesheet" type="text/css" media="screen" />
    <link href="{{MEDIA_URL}}css/lightboxes/django-follow.css" rel="stylesheet" type="text/css" media="screen" />
    <link href="{{MEDIA_URL}}css/jquery.lightbox-0.5.css" rel="stylesheet" type="text/css" media="screen" />
    <link href="{{MEDIA_URL}}css/jquery.lightbox-0.5.css" rel="stylesheet" type="text/css" media="screen" />
    <link href="{{MEDIA_URL}}css/tipsy.css" rel="stylesheet" type="text/css" media="screen" />
{% endblock %}

{% block title %}{{ bagger }}'s badges{% endblock %}
{% block titlemeta %}{{ bagger }}'s badges{% endblock %}
{% block description %}{{ bagger }}'s peak badges & awards{% endblock %}
{% block image_rel %}{% avatar_url bagger 135 %}{% endblock %}

{% block extrajs %}
    <script type="text/javascript" src="{% static 'js/jquery.lightbox-0.5.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/jquery.tipsy.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/handlebars.1.0.0.beta.3.js' %}"></script>
{% endblock %}

{% block profile_active %}{% if user == bagger %}active{% endif %}{% endblock %}

{% block mobile_header_follow %}
{% if user.is_authenticated %}{% if user.username|stringformat:"s" != bagger|stringformat:"s" %}<a href="javascript:void(0);" id="mobile-follow-{{ bagger.id }}" style="font-size: 11px; font-weight: 300; margin-left: 15px; {% if following %}display: none;{% else %}display: inline;{% endif %}">follow</a><a href="javascript:void(0);" id="mobile-unfollow-{{ bagger.id }}" style="font-size: 11px; font-weight: 300; margin-left: 15px; color: #00b330; {% if following %}display: inline;{% else %}display: none;{% endif %}">following</a>{% else %}<a href="/members/{{ bagger }}/edit" style="font-size: 11px; font-weight: 300; margin-left: 15px; {% if following %}display: none;{% else %}display: inline;{% endif %}">edit</a>{% endif %}{% endif %}
{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row hidden-xs">
        <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 65px; border-radius: 12px 12px 0 0;"><span style="font-size: 24px; font-weight: 600;">
            <div id="breadcrumbs" style="margin-left: -5px;">
                <ul>
                    <li style="margin-top: 2px;"><a class="modal-link" data-toggle="modal" data-target="#avatar-modal"><img style="height: 50px; width: 50px; border-radius:10px;" src="{{ MEDIA_URL }}{{ avatar.avatar_url|urlencode }}"></a><div style="float: right; margin-left: 15px; font-weight: 600; font-size: 20px;">{{ bagger }}{% if user.is_authenticated %}{% if user.username|stringformat:"s" == bagger|stringformat:"s" %}<a class="profile-edit-link hidden-xs" style="font-size: 12px; margin-left: 10px;" href="/members/{{ bagger }}/edit">edit</a>{% endif %}{% endif %}</div></li>
                </ul>
                <div class="pull-right hidden-xs" style="margin-top: -5px;">
                    <a style="{{ subnav_info_style }}" class="region-header-sub-links" href="/members/{{ bagger }}/">Info</a><a style="{{ subnav_map_style }}" class="region-header-sub-links" href="/members/{{ bagger }}/map/">Map</a><a style="{{ subnav_badges_style }}" class="region-header-sub-links" href="/members/{{ bagger }}/badges/">Badges</a><a style="{{ subnav_summits_style }}" class="region-header-sub-links" href="/members/{{ bagger }}/summits/">Climbs</a><a style="{{ subnav_challenges_style }}" class="region-header-sub-links" href="/members/{{ bagger }}/challenges/">Challenges</a><a style="{{ subnav_p_style }}" class="region-header-sub-links" href="/members/{{ bagger }}/photos/">Photos</a>{% if user.is_authenticated %}{% if user.username|stringformat:"s" != bagger|stringformat:"s" %}<div class="region-header-sub-links hidden-xs hidden-sm" style="float: right; margin-top: 3px;"><a href="javascript:void(0);" id="follow-{{ bagger.id }}" class="peakeryFollowButton followButton btn btn-secondary" style="font-size: 12px; float: right; width: 165px; {% if following %}display: none;{% else %}display: block;{% endif %}">Follow</a><a href="javascript:void(0);" id="unfollow-{{ bagger.id }}" class="peakeryFollowButton btn btn-secondary unfollowButton" style="font-size: 12px; float: right; width: 165px; background-color: rgb(0, 179, 48); border-color: rgb(0, 179, 48); {% if following %}display: block;{% else %}display: none;{% endif %}">You are following</a></div>{% endif %}{% endif %}
                </div>
            </div>
        </span></div>
    </div>
{% endblock %}

{% block content %}

    <style type="text/css">

        body.modal-open {
            overflow: visible;
        }

        .modal.fade{
          opacity:1;
        }
        .modal.fade .modal-dialog {
           -webkit-transform: translate(0);
           -moz-transform: translate(0);
           transform: translate(0);
        }

        .grow-badge-icons .item_icon_kom {
            height: 40px;
            margin-top: -5px;
        }

        .grow-badge-icons .item_icon_first {
            height: 34px;
            margin-top: -2px;
        }

        .grow-badge-icons .item_icon_steward {
            height: 35px;
            margin-top: -2px;
        }

        .grow-badge-icons .item_icon_text {
            font-weight: 500;
            background-color: #0FC707;
            margin-top: -2px;
        }

        .grow-badge-icons .item_icon_imgspan {
            margin-left: 15px;
        }

        #badges-list {
            padding-bottom: 20px;
        }

        .grow-badge-info {
            background-color: #fff;
            padding-bottom: 6px;
            box-shadow: 0 10px 16px -6px #000000;
            background-image: none;
        }

        .grow-badge-info > .item_name {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            border-bottom-right-radius: 40px;
            border-bottom-left-radius: 40px;
        }

        .grow-badge-info > .item_info {
            color:#666;
        }

        @media screen and (min-width: 1280px) {
            .grow-badge {
                width: 250px !important;
                height: 168px !important;
                border-top-left-radius: 50px !important;
                border-top-right-radius: 50px !important;
            }

            .grow-badge-info {
                width: 250px !important;
                top: 128px !important;
                border-bottom-right-radius: 50px !important;
                border-bottom-left-radius: 50px !important;
            }

            .grow-badge-icons {
                top: 128px;
            }

            .grow-badge-info > .item_name {
                border-bottom-right-radius: 50px !important;
                border-bottom-left-radius: 50px !important;
            }
        }

        @media screen and (max-width: 767px) and (min-width: 1px) {

            .badge-div {
                margin-top: 30px;
            }

           #content-body {
               margin-top: 0px;
           }
           .content-pane {
               margin-top: 99px;
           }
           .mobile-regions-subnav-fixed {
               margin-top: 50px;
           }
           #selectMobileBadgefilter {
               margin-top: 7px;
           }
           .peakeryFollowButton {
                font-size: 10px !important;
                width: 130px !important;
            }
           .filter-bar {
               top: 120px;
           }

           /* splash badges */
            .challenge-badge {
                width: 500px;
                height: 336px;
                box-shadow: 0px 0px 100px #999;
                border: 6px solid #fff;
                border-radius: 100% / 60%;
            }
            .challenge-badge-completed {
                width: 500px;
                height: 336px;
                border: 6px solid #7fff00;
                box-shadow: 0px 0px 100px #7fff00;
            }
            .splash-grow-badge {
                width: 300px;
                height: 200px;
            }
            .splash-grow-badge-info {
                width: 300px;
                height: 60px;
                padding-top: 13px;
                top: 200px;
            }
            .splash-grow-badge-info .item_name {
                font-size: 16px;
                line-height: 16px;
                font-weight: 600;
            }
            .splash-grow-badge-info .item_info {
                font-size: 12px;
            }
            .badge-data {
                text-align: center;
                color: #fff;
                margin-top: 110px;
            }
            .badge-data-count {
                text-align: center;
                color: #fff;
                margin-top: 50px;
            }
            .badge-data-challenge {
                text-align: center;
                color: #fff;
                margin-top: 50px;
            }
            .badge-icon-data {
                text-align: center;
                color: #fff;
                margin-top: -40px;
            }
            .badge-steward-data {
                text-align: center;
                color: #fff;
                margin-top: -20px;
            }
            .badge-data-top {
                font-size: 16px;
                line-height: 22px;
                font-weight: 500;
                padding-left: 15px;
                padding-right: 15px;
                margin-bottom: 10px;
            }
            .badge-data-middle {
                font-size: 24px;
                line-height: 30px;
                font-weight: 600;
                padding-left: 15px;
                padding-right: 15px;
                margin-bottom: 10px;
            }
            .badge-data-bottom {
                color: #999999;
                font-size: 12px;
                line-height: 16px;
                padding-left: 15px;
                padding-right: 15px;
            }
            .badge-icon {
                width: 300px !important;
            }
            .badge-icon img {
                width: 300px;
            }
            .badge-icon-steward {
                width: 250px !important;
            }
            .badge-icon-steward img {
                width: 250px;
            }
            .progress {
                width: 300px;
                position: relative;
                margin-left: -150px;
                left: 50%;
                height: 30px;
            }
            .progress-complete {
                margin-bottom: 20px;
            }
            .progress-bar-blue {
                font-size: 12px;
                line-height: 30px;
            }
            .badge-data-challenge .badge-data-middle {
                margin-bottom: 20px !important;
            }
            .badge-peak-count {
                margin-top: -130px;
                width: 150px;
                height: 50px;
                left: 75px;
                position: relative;
                -webkit-border-radius: 30px;
                -moz-border-radius: 30px;
                border-radius: 10px;
                border: 5px solid #fff;
            }
            .badge-peak-count span {
                color: #fff;
                line-height: 42px;
                font-size: 20px;
            }
            .grow-badge {
                border-top-left-radius: 40px;
                border-top-right-radius: 40px;
            }
            .grow-badge-info > .item_name {
                border-bottom-right-radius: 40px;
                border-bottom-left-radius: 40px;
            }
            .grow-badge-icons .item_icon_imgspan {
                margin-left: 5px !important;
            }

       }
       @media screen and (max-width: 1023px) and (min-width: 768px) {
           #badges-list {
                margin-top: 50px;
            }
           .badge-div {
                margin-top: 20px;
            }
           .content-pane {
               margin-top: 49px;
           }
           #selectMobileBadgefilter {
               margin-top: 7px;
           }
           .mobile-regions-subnav-fixed {
               margin-top: 71px;
           }
           .badge-div {
               margin-top: 20px;
               margin-bottom: 30px;
            }
           .filter-bar {
               top: 141px;
           }
           .regions-subnav-fixed {
                top: 141px;
            }

           /* splash badges */
           .challenge-badge {
                width: 500px;
                height: 336px;
                box-shadow: 0px 0px 100px #999;
                border: 10px solid #fff;
                border-radius: 100% / 60%;
            }
            .challenge-badge-completed {
                width: 500px;
                height: 336px;
                border: 10px solid #7fff00;
                box-shadow: 0px 0px 100px #7fff00;
            }
            .splash-grow-badge {
                width: 480px;
                height: 325px;
            }
            .splash-grow-badge-info {
                width: 480px;
                height: 80px;
                padding-top: 15px;
                top: 285px;
            }
            .splash-grow-badge-info .item_name {
                font-size: 24px;
                line-height: 24px;
                font-weight: 600;
            }
            .splash-grow-badge-info .item_info {
                font-size: 16px;
            }
            .badge-data {
                text-align: center;
                color: #fff;
                margin-top: 90px;
            }
            .badge-data-count {
                text-align: center;
                color: #fff;
                margin-top: 90px;
            }
            .badge-data-challenge {
                text-align: center;
                color: #fff;
                margin-top: 50px;
            }
            .badge-icon-data {
                text-align: center;
                color: #fff;
                margin-top: -40px;
            }
            .badge-steward-data {
                text-align: center;
                color: #fff;
                margin-top: -20px;
            }
            .badge-data-top {
                font-size: 26px;
                line-height: 32px;
                font-weight: 500;
                padding-left: 15px;
                padding-right: 15px;
                margin-bottom: 15px;
            }
            .badge-data-middle {
                font-size: 38px;
                line-height: 44px;
                font-weight: 600;
                padding-left: 15px;
                padding-right: 15px;
                margin-bottom: 15px;
            }
            .badge-data-bottom {
                color: #999999;
                font-size: 14px;
                line-height: 20px;
                padding-left: 15px;
                padding-right: 15px;
            }
            .badge-icon {
                width: 480px !important;
            }
            .badge-icon img {
                width: 480px;
            }
            .badge-icon-steward {
                width: 400px !important;
            }
            .badge-icon-steward img {
                width: 400px;
            }
            .progress {
                width: 600px;
                position: absolute;
                margin-left: -300px;
                margin-top: -55px;
                left: 50%;
                height: 35px;
            }
            .progress-incomplete {
                margin-top: -45px;
            }
            .challenge-show {
                font-size: 14px;
                line-height: 35px;
            }
            .badge-data-challenge .badge-data-middle {
                margin-bottom: 60px !important;
            }
            .badge-peak-count {
                margin-top: -200px;
                width: 240px;
                height: 80px;
                left: 120px;
                position: relative;
                -webkit-border-radius: 30px;
                -moz-border-radius: 30px;
                border-radius: 20px;
                border: 5px solid #fff;
            }
            .badge-peak-count span {
                color: #fff;
                line-height: 70px;
                font-size: 30px;
            }
            .grow-badge {
                border-top-left-radius: 40px;
                border-top-right-radius: 40px;
            }
            .grow-badge-info > .item_name {
                border-bottom-right-radius: 40px;
                border-bottom-left-radius: 40px;
            }

       }
        @media screen and (min-width: 1024px) {
            #badges-list {
                margin-top: 50px;
            }
            .badge-div {
                margin-top: 20px;
            }
            .content-pane {
               margin-top: 49px;
           }
            .mobile-regions-subnav-fixed {
               margin-top: 0px;
           }
            #selectMobileBadgefilter {
               margin-top: 0px;
           }
            .regions-subnav-fixed {
                top: 141px;
            }

            /* splash badges */
            .challenge-badge {
                width: 500px;
                height: 336px;
                box-shadow: 0px 0px 100px #999;
                border: 10px solid #fff;
                border-radius: 100% / 60%;
            }
            .challenge-badge-completed {
                width: 500px;
                height: 336px;
                border: 10px solid #7fff00;
                box-shadow: 0px 0px 100px #7fff00;
            }
            .splash-grow-badge {
                width: 480px;
                height: 325px;
            }
            .splash-grow-badge-info {
                width: 480px;
                height: 80px;
                padding-top: 15px;
                top: 285px;
            }
            .splash-grow-badge-info .item_name {
                font-size: 24px;
                line-height: 24px;
                font-weight: 600;
            }
            .splash-grow-badge-info .item_info {
                font-size: 16px;
            }
            .badge-data {
                text-align: center;
                color: #fff;
                margin-top: 90px;
            }
            .badge-data-count {
                text-align: center;
                color: #fff;
                margin-top: 90px;
            }
            .badge-data-challenge {
                text-align: center;
                color: #fff;
                margin-top: 50px;
            }
            .badge-icon-data {
                text-align: center;
                color: #fff;
                margin-top: -40px;
            }
            .badge-steward-data {
                text-align: center;
                color: #fff;
                margin-top: -20px;
            }
            .badge-data-top {
                font-size: 34px;
                line-height: 40px;
                font-weight: 500;
                padding-left: 15px;
                padding-right: 15px;
            }
            .badge-data-middle {
                font-size: 50px;
                line-height: 56px;
                font-weight: 600;
                padding-left: 15px;
                padding-right: 15px;
                margin-bottom: 20px;
            }
            .badge-data-bottom {
                color: #999999;
                font-size: 16px;
                line-height: 22px;
                padding-left: 15px;
                padding-right: 15px;
                margin-bottom: 20px;
            }
            .badge-icon {
                width: 480px !important;
            }
            .badge-icon img {
                width: 480px;
            }
            .badge-icon-steward {
                width: 400px !important;
            }
            .badge-icon-steward img {
                width: 400px;
            }
            .progress {
                width: 600px;
                position: absolute;
                margin-left: -300px;
                margin-top: -60px;
                left: 50%;
            }
            .badge-data-challenge .badge-data-middle {
                margin-bottom: 70px !important;
            }
            .badge-peak-count {
                margin-top: -200px;
                width: 240px;
                height: 80px;
                left: 120px;
                position: relative;
                -webkit-border-radius: 30px;
                -moz-border-radius: 30px;
                border-radius: 20px;
                border: 5px solid #fff;
            }
            .badge-peak-count span {
                color: #fff;
                line-height: 70px;
                font-size: 30px;
            }
            .grow-badge {
                border-top-left-radius: 40px;
                border-top-right-radius: 40px;
            }
            .grow-badge-info > .item_name {
                border-bottom-right-radius: 40px;
                border-bottom-left-radius: 40px;
            }

        }

    </style>

    <script type="text/javascript">

        var badge_type = 'all';
        var sort_type = 'latest';

    </script>

<div class="container">

    <!-- Mobile header -->
    <div class="row sub-header-row hidden-lg hidden-md hidden-sm" style="height: 50px; position: fixed; width: 100%; z-index: 2; top: 70px;">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999; display: flex; justify-content: space-between;">
            <a style="{{ subnav_info_style }}" class="mobile-header-sub-links" href="/members/{{ bagger }}/">Info</a>{% if IS_MOBILE_APP_ACCESS != 'True' %}<a style="{{ subnav_map_style }}" class="mobile-header-sub-links" href="/members/{{ bagger }}/map/">Map</a>{% endif %}<a style="{{ subnav_badges_style }}" class="mobile-header-sub-links" href="/members/{{ bagger }}/badges/">Badges</a><a style="{{ subnav_summits_style }}" class="mobile-header-sub-links" href="/members/{{ bagger }}/summits/">Summits</a><a style="{{ subnav_challenges_style }}" class="mobile-header-sub-links" href="/members/{{ bagger }}/challenges/">Challenges</a><a style="{{ subnav_p_style }}" class="mobile-header-sub-links" href="/members/{{ bagger }}/photos/">Photos</a>
        </div>
    </div>
    <!-- End mobile header -->

    <div class="row sub-header-row hidden-xs hidden-sm regions-subnav-fixed" style="height: 50px; padding-right: 0px;">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999;">
            <a style="margin-left: 0px;" id="type-all" class="region-header-sort-links ajax-link" onclick="loadBadges('all', sort_type, '1');">All {{ all_badges_count }} Peak Badge{{ all_badges_count|pluralize:"s" }}</a><a style="margin-left: 35px;" id="type-kom" class="region-header-sort-links ajax-link" onclick="loadBadges('kom', sort_type, '1');">{{ kom_count }} King of the Mountain{{ kom_count|pluralize:"s" }}</a><a style="margin-left: 35px;" id="type-first-ascent" class="region-header-sort-links ajax-link" onclick="loadBadges('first', sort_type, '1');">{{ first_ascent_count }} First Ascent{{ first_ascent_count|pluralize:"s" }}</a><a style="margin-left: 35px;" id="type-summit-steward" class="region-header-sort-links ajax-link" onclick="loadBadges('steward', sort_type, '1');">{{ summit_steward_count }} Summit Steward{{ summit_steward_count|pluralize:"s" }}</a>
            <span class="pull-right"><a style="margin-left: 10px; color: rgb(153, 153, 153); font-weight: 300;" id="sort-latest" class="region-header-sort-links ajax-link" onclick="loadBadges(badge_type, 'latest', '1');">Latest</a><a style="margin-left: 35px; color: rgb(153, 153, 153); font-weight: 300;" id="sort-elevation" class="region-header-sort-links ajax-link" onclick="loadBadges(badge_type, 'elevation', '1');">Elevation</a><a style="margin-left: 35px; color: rgb(153, 153, 153); font-weight: 300;" id="sort-prominence" class="region-header-sort-links ajax-link" onclick="loadBadges(badge_type, 'prominence', '1');">Prominence</a><a style="margin-left: 35px; color: rgb(153, 153, 153); font-weight: 300;" id="sort-summits" class="region-header-sort-links ajax-link" onclick="loadBadges(badge_type, 'summits', '1');">Climbs</a></span>
        </div>
    </div>

    <div class="row content-pane">

        <div class="col-md-12">
            <div class="row sub-header-row hidden-md hidden-lg filter-bar" style="height: 50px; padding-right: 0px;">
                <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12" style="height: 50px;line-height: 26px;">
                    <div style="float: left; width: 50%; height: 100%; border-right: solid 1px #cfcfcf;">
                        <div class="select" id="selectMobileBadgefilter">
                            <input type="hidden" id="hdnMobileBadgeFilter" value="">
                            <button class="btn btn-default badge-filter-button" style="border: none; padding-top: 7px; padding-bottom: 7px; margin-left: 1px; color: #00b1f2; font-size: 12px; padding-left: 5px; padding-right: 5px; background-color: #f2f2f2; width: 98%; text-align: left;" data-toggle="dropdown" aria-expanded="false">
                                <span id="mobile-badgefilter-title">All {{ all_badges_count }} Peak Badge{{ all_badges_count|pluralize:"s" }}</span>
                                <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu badgefilter-list" style="cursor: pointer; height: 180px; left: 5px; overflow: auto; top: 50px;">
                                <li style="float: none; height: 30px; margin: 10px 0 0;"><a onclick="loadBadges('all', sort_type, '1');" class="badgefilter-item" data-value="all" href="#">All {{ all_badges_count }} Peak Badge{{ all_badges_count|pluralize:"s" }}</a></li>
                                <li style="float: none; height: 30px; margin: 10px 0 0;"><a onclick="loadBadges('kom', sort_type, '1');" class="badgefilter-item" data-value="kom" href="#">{{ kom_count }} King of the Mountain{{ kom_count|pluralize:"s" }}</a></li>
                                <li style="float: none; height: 30px; margin: 10px 0 0;"><a onclick="loadBadges('first', sort_type, '1');"class="badgefilter-item" data-value="first" href="#">{{ first_ascent_count }} First Ascent{{ first_ascent_count|pluralize:"s" }}</a></li>
                                <li style="float: none; height: 30px; margin: 10px 0 0;"><a onclick="loadBadges('steward', sort_type, '1');"class="badgefilter-item" data-value="steward" href="#">{{ summit_steward_count }} Summit Steward{{ summit_steward_count|pluralize:"s" }}</a></li>
                            </ul>
                        </div>
                    </div>
                    <div style="float: left; margin-left: 10px; width: 45%; height: 100%;">
                        <div class="select" id="selectMobileSortBadges" style="margin-top: 7px;">
                            <button class="btn btn-default mobile-sortbadges-button" style="border: none; padding-top: 7px; padding-bottom: 7px; margin-left: 1px; color: #00b1f2; font-size: 12px; padding-left: 5px; padding-right: 5px; background-color: #f2f2f2; width: 98%; text-align: left;" data-toggle="dropdown">
                                <span id="mobile-sortbadges-title">Latest</span>
                                <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu mobile-sortbadges-list" style="height: 175px;; left: 50%; top: 50px; overflow: auto;">
                                <li style="float: none; height: 30px; margin: 10px 0 0;"><a onclick="loadBadges(badge_type, 'latest', '1');" href="#">Latest</a></li>
                                <li style="float: none; height: 30px; margin: 10px 0 0;"><a onclick="loadBadges(badge_type, 'elevation', '1');" href="#">Elevation</a></li>
                                <li style="float: none; height: 30px; margin: 10px 0 0;"><a onclick="loadBadges(badge_type, 'prominence', '1');" href="#">Prominence</a></li>
                                <li style="float: none; height: 30px; margin: 10px 0 0;"><a onclick="loadBadges(badge_type, 'summits', '1');" href="#">Summits</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-12">
            <div class="row" id="ajax-data-loading" style="display: none;">
              <div class="col-md-12 data-loading" style="text-align: center; color: #666;">
                <i class="fa fa-spinner fa-spin fa-5x"></i>
              </div>
            </div>
            <div class="row" id="badges-list">
            </div>
        </div>
    </div>
    <div class="row dark-background-row" id="pagination" style="border-bottom: none; color: #ccc; margin-top: -1px; height: 80px; margin-left: 0px; display: none;">
        <div class="col-md-12">
            <div class="row" style="padding-left: 15px; margin-left: 5px; text-align: center;">
                <span id="pagination-pages" class="step-links"></span>
            </div>
        </div>
    </div>
    <div class="row hidden-lg hidden-md hidden-sm">
        <div style="height: 178px;"></div>
    </div>

    <div class="row hidden-xs">
        <div style="height: 54px;"></div>
    </div>
</div>

<script type="text/javascript">

    // Hide mobile filter bar on on scroll down
    var didScroll;
    var lastScrollTop = 0;
    var delta = 5;

    $(window).scroll(function(event){
        didScroll = true;
    });

    document.addEventListener("touchstart", ScrollStart, false);

    setInterval(function() {
        if (didScroll) {
            hasScrolled();
            didScroll = false;
        }
    }, 100);

    function ScrollStart() {
        //start of scroll event for iOS
        hasScrolled();
    }

    function hasScrolled() {
        var st = $(this).scrollTop();

        // Make sure they scroll more than delta
        if (Math.abs(lastScrollTop - st) <= delta)
            return;

        // If they scrolled down and are past the filter bar, add class .filter-scrollup.
        // This is necessary so you never see what is "behind" the navbar.
        if (st > lastScrollTop && st > 50) {
            // Scroll Down
            $('.filter-bar').hide();
        } else {
            // Scroll Up
            if (st + $(window).height() < $(document).height()) {
                $('.filter-bar').show();
            }
        }
        lastScrollTop = st;
    }

    function loadBadges(type, sort, page) {

        badge_type = type;
        sort_type = sort;

        $('#pagination').css('display','none');
        $('#badges-list').hide();

        window.scrollTo(0, 0);

        switch(badge_type) {
            case 'all':
                $('#type-all').css('color', '#F24100');
                $('#type-all').css('font-weight', '500');
                $('#type-kom').css('color', '#999');
                $('#type-kom').css('font-weight', '300');
                $('#type-first-ascent').css('color', '#999');
                $('#type-first-ascent').css('font-weight', '300');
                $('#type-summit-steward').css('color', '#999');
                $('#type-summit-steward').css('font-weight', '300');
                $('#mobile-badgefilter-title').html('All {{ all_badges_count }} Peak Badge{{ all_badges_count|pluralize:"s" }}');
                break;
            case 'kom':
                $('#type-all').css('color', '#999');
                $('#type-all').css('font-weight', '300');
                $('#type-kom').css('color', '#F24100');
                $('#type-kom').css('font-weight', '500');
                $('#type-first-ascent').css('color', '#999');
                $('#type-first-ascent').css('font-weight', '300');
                $('#type-summit-steward').css('color', '#999');
                $('#type-summit-steward').css('font-weight', '300');
                $('#mobile-badgefilter-title').html('{{ kom_count }} King of the Mountain{{ kom_count|pluralize:"s" }}');
                break;
            case 'first':
                $('#type-all').css('color', '#999');
                $('#type-all').css('font-weight', '300');
                $('#type-kom').css('color', '#999');
                $('#type-kom').css('font-weight', '300');
                $('#type-first-ascent').css('color', '#F24100');
                $('#type-first-ascent').css('font-weight', '500');
                $('#type-summit-steward').css('color', '#999');
                $('#type-summit-steward').css('font-weight', '300');
                $('#mobile-badgefilter-title').html('{{ first_ascent_count }} First Ascent{{ first_ascent_count|pluralize:"s" }}');
                break;
            case 'steward':
                $('#type-all').css('color', '#999');
                $('#type-all').css('font-weight', '300');
                $('#type-kom').css('color', '#999');
                $('#type-kom').css('font-weight', '300');
                $('#type-first-ascent').css('color', '#999');
                $('#type-first-ascent').css('font-weight', '300');
                $('#type-summit-steward').css('color', '#F24100');
                $('#type-summit-steward').css('font-weight', '500');
                $('#mobile-badgefilter-title').html('{{ summit_steward_count }} Summit Steward{{ summit_steward_count|pluralize:"s" }}');
                break;
            default:
                $('#type-all').css('color', '#F24100');
                $('#type-all').css('font-weight', '500');
                $('#type-kom').css('color', '#999');
                $('#type-kom').css('font-weight', '300');
                $('#type-first-ascent').css('color', '#999');
                $('#type-first-ascent').css('font-weight', '300');
                $('#type-summit-steward').css('color', '#999');
                $('#type-summit-steward').css('font-weight', '300');
                $('#mobile-badgefilter-title').html('All {{ all_badges_count }} Peak Badge{{ all_badges_count|pluralize:"s" }}');
        }

        switch(sort_type) {
            case 'latest':
                $('#sort-latest').css('color', '#F24100');
                $('#sort-latest').css('font-weight', '500');
                $('#sort-elevation').css('color', '#999');
                $('#sort-elevation').css('font-weight', '300');
                $('#sort-prominence').css('color', '#999');
                $('#sort-prominence').css('font-weight', '300');
                $('#sort-summits').css('color', '#999');
                $('#sort-summits').css('font-weight', '300');
                $('#mobile-sortbadges-title').html('Latest');
                break;
            case 'elevation':
                $('#sort-latest').css('color', '#999');
                $('#sort-latest').css('font-weight', '300');
                $('#sort-elevation').css('color', '#F24100');
                $('#sort-elevation').css('font-weight', '500');
                $('#sort-prominence').css('color', '#999');
                $('#sort-prominence').css('font-weight', '300');
                $('#sort-summits').css('color', '#999');
                $('#sort-summits').css('font-weight', '300');
                $('#mobile-sortbadges-title').html('Elevation');
                break;
            case 'prominence':
                $('#sort-latest').css('color', '#999');
                $('#sort-latest').css('font-weight', '300');
                $('#sort-elevation').css('color', '#999');
                $('#sort-elevation').css('font-weight', '300');
                $('#sort-prominence').css('color', '#F24100');
                $('#sort-prominence').css('font-weight', '500');
                $('#sort-summits').css('color', '#999');
                $('#sort-summits').css('font-weight', '300');
                $('#mobile-sortbadges-title').html('Prominence');
                break;
            case 'summits':
                $('#sort-latest').css('color', '#999');
                $('#sort-latest').css('font-weight', '300');
                $('#sort-elevation').css('color', '#999');
                $('#sort-elevation').css('font-weight', '300');
                $('#sort-prominence').css('color', '#999');
                $('#sort-prominence').css('font-weight', '300');
                $('#sort-summits').css('color', '#F24100');
                $('#sort-summits').css('font-weight', '500');
                $('#mobile-sortbadges-title').html('Summits');
                break;
            default:
                $('#sort-latest').css('color', '#F24100');
                $('#sort-latest').css('font-weight', '500');
                $('#sort-elevation').css('color', '#999');
                $('#sort-elevation').css('font-weight', '300');
                $('#sort-prominence').css('color', '#999');
                $('#sort-prominence').css('font-weight', '300');
                $('#sort-summits').css('color', '#999');
                $('#sort-summits').css('font-weight', '300');
                $('#mobile-sortbadges-title').html('Latest');
        }

        window.location.hash = 'type='+badge_type+'&order='+sort_type+'&page='+page;

        $('#badges-list').empty();
        $('#ajax-data-loading').css('display', 'inline');
        var badgeCount;

        $.getJSON('{% url "badges_list" %}?user={{ profile.user_id }}&type='+badge_type+'&sort='+sort_type+'&page='+page , function(data) {
            $.each( data, function( key, val ) {
                if (key=='badge_count') {
                    badgeCount = val;
                    var totalPages = Math.ceil(parseInt(badgeCount)/120);
                    var counter = 1;
                    //show pagination
                    var paginationHtml = '';
                    if (parseInt(page) == totalPages && totalPages > 1) {
                        paginationHtml = '<div style="display: inline-block; height: 55px;"><a onclick="loadBadges(\''+badge_type+'\', \''+sort_type+'\', ' + (parseInt(page)-1).toString() + ');"><img src="{% static 'img/font-awesome_4-7-0_arrow-circle-left_256_0_00b1f2_none.png' %}" style="height: 26px; margin-bottom: 5px;"></a></div><div class="current" style="margin-left: 20px; margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">badges ' + ((parseInt(page)-1)*60+1).toString() + ' - ' + badgeCount + ' of ' + badgeCount + '</div>';
                    } else if (parseInt(page) > 1) {
                        paginationHtml = '<div style="display: inline-block; height: 55px;"><a onclick="loadBadges(\''+badge_type+'\', \''+sort_type+'\', ' + (parseInt(page)-1).toString() + ');"><img src="{% static 'img/font-awesome_4-7-0_arrow-circle-left_256_0_00b1f2_none.png' %}" style="height: 26px; margin-bottom: 5px;"></a></div><div class="current" style="margin-left: 20px; margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">badges ' + ((parseInt(page)-1)*60+1).toString() + ' - ' + ((parseInt(page))*60).toString() + ' of ' + badgeCount + '</div>';
                    } else if (totalPages > 1) {
                        paginationHtml = '<div class="current" style="margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">badges 1 - 120 of ' + badgeCount + '</div>';
                    } else {
                        paginationHtml = '<div class="current" style="margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">badges 1 - ' + badgeCount + ' of ' + badgeCount + '</div>';
                    }
                    if (parseInt(page) < totalPages && badgeCount > 0) {
                        paginationHtml = paginationHtml + '<div style="display: inline-block; height: 55px;"><a onclick="loadBadges(\''+badge_type+'\', \''+sort_type+'\', ' + (parseInt(page)+1).toString() + ');"><img src="{% static 'img/font-awesome_4-7-0_arrow-circle-right_256_0_00b1f2_none.png'%}" style="height: 26px; margin-bottom: 5px;"></i></a></div>';
                        $('#pagination-pages').html(paginationHtml);
                        $('#pagination').css('display', 'inherit');
                    } else if (badgeCount > 0) {
                        $('#pagination-pages').html(paginationHtml);
                        $('#pagination').css('display', 'inherit');
                    }
                }
                if (key=='badges') {
                    $('#ajax-data-loading').css('display', 'none');
                    $.each( val, function( badgekey, badgeval ) {
                        if (badgeval.kom > 0) {
                            kom_img = '<span class="item_icon_imgspan"><img class="item_icon_kom" src="{% static 'img/<EMAIL>' %}"></span>';
                        } else {
                            kom_img = '';
                        }
                        if (badgeval.summit_steward > 0) {
                            summit_steward_img = '<span class="item_icon_imgspan"><img class="item_icon_steward" src="{% static 'img/<EMAIL>' %}"></span>';
                        } else {
                            summit_steward_img = '';
                        }
                        if (badgeval.first_ascent > 0) {
                            first_ascent_img = '<span class="item_icon_imgspan"><img class="item_icon_first" src="{% static 'img/<EMAIL>' %}"></span>';
                        } else {
                            first_ascent_img = '';
                        }
                        if (badgeval.summitlog_count > 1) {
                            summitlog_span = '<span class="item_icon_text">' + badgeval.summitlog_count + 'x</span>';
                        } else {
                            summitlog_span = '';
                        }
                        if (sort_type == 'prominence') {
                            if (badgeval.prominence != '0.0') {
                                prominence = numberWithCommas(Math.floor(badgeval.prominence)) + ' ft / ' + numberWithCommas(Math.floor(badgeval.prominence * .3048)) + ' m prom';
                            } else {
                                prominence = '&nbsp;';
                            }
                            $('#badges-list').append('<div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive badge-div"><div class="summit_badges"><div class="badge-li"><a href="/' + badgeval.slug + '" class="withoutNumber"><div class="thumb grow-badge hover-photos" style="background: url(' + encodeURI(badgeval.thumbnail_url) + ') no-repeat; background-size: cover;"><div class="item_icons grow-badge-icons">' + summitlog_span + kom_img + summit_steward_img + first_ascent_img + '</div><div class="peak grow-badge-info"> <span class="item_name ellipsis">' + badgeval.name + '</span> <span class="item_info">' + prominence + '</span></div></div></a></div></div></div>');
                        } else {
                            $('#badges-list').append('<div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive badge-div"><div class="summit_badges"><div class="badge-li"><a href="/' + badgeval.slug + '" class="withoutNumber"><div class="thumb grow-badge hover-photos" style="background: url(' + encodeURI(badgeval.thumbnail_url) + ') no-repeat; background-size: cover;"><div class="item_icons grow-badge-icons">' + summitlog_span + kom_img + summit_steward_img + first_ascent_img + '</div><div class="peak grow-badge-info"> <span class="item_name ellipsis">' + badgeval.name + '</span> <span class="item_info">' + numberWithCommas(Math.floor(badgeval.elevation)) + ' ft / ' + numberWithCommas(Math.floor(badgeval.elevation * .3048)) + ' m</span></div></div></a></div></div></div>');
                        }
                    });
                    counter ++;
                }
            });
            if (badgeCount > 0) {
                $('#badges-list').show();
            }
        });

    }

    function numberWithCommas(x) {
        return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    $(document).ready(function() {

        {% if summit_badges %}

        $("#modal-0").addClass("md-show");
        $('.md-overlay').show();
        $('#content-holder').css({"filter" : "blur(15px)"});

        $('.md-overlay').on('click', function(e) {
            {% for b in summit_badges %}
            {% if forloop.counter0 > 0 %}else{% endif %} if ($("#modal-{{ forloop.counter }}").hasClass('md-modal') && $("#modal-{{ forloop.counter0 }}").hasClass('md-show')) {
                $("#modal-{{ forloop.counter0 }}").removeClass("md-effect-1").addClass("md-effect-11").removeClass("md-show");
                setTimeout(function () {
                    $("#modal-{{ forloop.counter }}").addClass("md-show");
                }, 300);
            }
            {% endfor %}
        });

        $('.md-modal').on('click', function(e) {
            {% for b in summit_badges %}
            {% if forloop.counter0 > 0 %}else{% endif %} if ($("#modal-{{ forloop.counter }}").hasClass('md-modal') && $("#modal-{{ forloop.counter0 }}").hasClass('md-show')) {
                $("#modal-{{ forloop.counter0 }}").removeClass("md-effect-1").addClass("md-effect-11").removeClass("md-show");
                setTimeout(function () {
                    $("#modal-{{ forloop.counter }}").addClass("md-show");
                }, 300);
            }
            {% endfor %}
        });

        $('#modal-{{ last_badge_index }}').on('click', function(e) {
            $(".md-modal").removeClass("md-show");
            $('.md-overlay').hide();
            $('#content-holder').css({"filter" : "none"});
        });

        $('.md-overlay').on('click', function(e) {
            if ($('#modal-{{ last_badge_index }}').hasClass('md-show')) {
                $(".md-modal").removeClass("md-show");
                $('.md-overlay').hide();
                $('#content-holder').css({"filter" : "none"});
            }
        });

        {% endif %}

        $('.slide').click(function(){
            var pos = splash_gallery.getIndex();
            var count = splash_gallery.getNumber();
            if (pos == count-1) {
                splash_gallery.close();
            } else {
                splash_gallery.next();
            }
            return false;
        });

        var viewport_height = $(window).height();
        var viewport_width = $(window).width();
        var footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
        var data_loading_margin = (viewport_height - footer_height) / 3;
        $('.data-loading').css('margin-top', data_loading_margin);
        $(window).resize(function() {
            viewport_height = $(window).height();
            viewport_width = $(window).width();
            footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
            data_loading_margin = (viewport_height - footer_height) / 3;
            $('.data-loading').css('margin-top', data_loading_margin);
        });

        var vars = [], hash, type, sort, page;
        var q = document.URL.split('#')[1];
        if(q != undefined){
            q = q.split('&');
            for(var i = 0; i < q.length; i++){
                hash = q[i].split('=');
                vars.push(hash[1]);
                vars[hash[0]] = hash[1];
            }
        }

        if (vars['type'] != undefined) {
            type = vars['type'];
        } else {
            type = 'all';
        }

        if (vars['order'] != undefined) {
            sort = vars['order'];
        } else {
            sort = 'latest';
        }

        loadBadges(type, sort, '1');

        $('#badges-list').on('mouseenter', '.badge-li', function() {
            var window_width = $(window).width();
            if (window_width >= 1024) {
                $(this).find('.grow-badge').css('-webkit-transform', 'scale(1.2)');
                $(this).find('.grow-badge').css('transform', 'scale(1.2)');
                $(this).find('.grow-badge').css('transition', '0.3s ease');
            }
        });

        $('#badges-list').on('mouseleave', '.badge-li', function() {
            $(this).find('.grow-badge').css('-webkit-transform','scale(1.0)');
            $(this).find('.grow-badge').css('transform','scale(1.0)');
            $(this).find('.grow-badge').css('transition','0.3s ease');
        });

        $('#unfollow-{{ bagger.id }}').hover(
        function(){
            $(this).removeClass('unfollowButton').addClass('unfollowButtonHoverProfile').text('Unfollow').css('background-color','#f24100').css('border-color','#f24100');
        },
        function(){
            $(this).removeClass('unfollowButtonHoverProfile').addClass('unfollowButton').text('You are following').css('background-color','#00b330').css('border-color','#00b330');
        });

        $("#unfollow-{{ bagger.id }}").on('click',function (){
            var btn = $("#unfollow-{{ bagger.id }}");
            var follow_btn = $("#follow-{{ bagger.id }}");
            btn.html('<i class="fa fa-spinner fa-spin"></i>');
            $.post('/accounts/unfollow/{{ bagger.id }}/',function(data){
                if (data == "True"){
                    follow_btn.html('Follow');
                    $("#unfollow-{{ bagger.id }}").hide();
                    $("#follow-{{ bagger.id }}").show();
                    $("#mobile-unfollow-{{ bagger.id }}").hide();
                    $("#mobile-follow-{{ bagger.id }}").show();
                }
            });
        });

        $("#follow-{{ bagger.id }}").on('click',function (){
            var btn = $("#follow-{{ bagger.id }}");
            var unfollow_btn = $("#unfollow-{{ bagger.id }}");
            btn.html('<i class="fa fa-spinner fa-spin"></i>');
            $.post('/accounts/follow/{{ bagger.id }}/',function(data){
                if (data == "True"){
                    unfollow_btn.html('You are following');
                    $("#follow-{{ bagger.id }}").hide();
                    $("#unfollow-{{ bagger.id }}").show();
                    $("#mobile-follow-{{ bagger.id }}").hide();
                    $("#mobile-unfollow-{{ bagger.id }}").show();
                }
            });
        });

        $("#mobile-unfollow-{{ bagger.id }}").on('click',function (){
            var btn = $("#mobile-unfollow-{{ bagger.id }}");
            var follow_btn = $("#mobile-follow-{{ bagger.id }}");
            btn.html('<i class="fa fa-spinner fa-spin"></i>');
            $.post('/accounts/unfollow/{{ bagger.id }}/',function(data){
                if (data == "True"){
                    follow_btn.html('follow');
                    $("#mobile-unfollow-{{ bagger.id }}").hide();
                    $("#mobile-follow-{{ bagger.id }}").show();
                    $("#unfollow-{{ bagger.id }}").hide();
                    $("#follow-{{ bagger.id }}").show();
                }
            });
        });

        $("#mobile-follow-{{ bagger.id }}").on('click',function (){
            var btn = $("#mobile-follow-{{ bagger.id }}");
            var unfollow_btn = $("#mobile-unfollow-{{ bagger.id }}");
            btn.html('<i class="fa fa-spinner fa-spin"></i>');
            $.post('/accounts/follow/{{ bagger.id }}/',function(data){
                if (data == "True"){
                    unfollow_btn.html('following');
                    $("#mobile-follow-{{ bagger.id }}").hide();
                    $("#mobile-unfollow-{{ bagger.id }}").show();
                    $("#follow-{{ bagger.id }}").hide();
                    $("#unfollow-{{ bagger.id }}").show();
                }
            });
        });

    });

</script>

{% endblock %}

{% block gallery %}
{% for b in summit_badges %}
    {% if b.type == 'summary' %}
        <div class="md-modal md-effect-1" id="modal-{{ forloop.counter0 }}">
            <div class="md-content">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="summit_badges">
                        <div class="badge-li">
                            <div class="thumb splash-grow-badge hover-photos" style="background: url({{ b.peak_thumbnail }}) 0% 0% / cover no-repeat; transform: scale(1); transition: 0.4s ease;">
                                <div class="peak splash-grow-badge-info">
                                    <span class="item_name ellipsis">{{ b.peak_name }}</span>
                                    <span class="item_info">{{ b.peak_elevation }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="badge-data">
                        <div class="badge-data-top">Your <span style="color: #f24100; font-weight: 500;">{{ b.summit_count|ordinal }}</span> summit of</div>
                        <div class="badge-data-middle">{{ b.peak_name }}</div>
                        {% if b.summits_this_year_count > 0 %}
                        <div class="badge-data-bottom"><span style="color: #f24100; font-weight: 500;">{{ b.summits_this_year_count|ordinal }}</span> member to summit in {{ b.summit_date|date:"Y" }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    {% elif b.type == 'first_ascent' %}
        <div class="md-modal md-effect-1" id="modal-{{ forloop.counter0 }}">
            <div class="md-content">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <ul class="summit_badges" style="text-align: center;">
                        <li class="badge-icon" style="float: none; display: inline-block;">
                            <img src="{% static 'img/splash_badge_first_ascent.png' %}">
                        </li>
                    </ul>
                    <div class="badge-icon-data">
                        <div class="badge-data-top">You snagged the {{ b.peak_name }}</div>
                        <div class="badge-data-middle"><span style="color: #ff0000;">First Ascent</span></div>
                        <div class="badge-data-bottom">You're the first to log this summit. Now it will forever bear your name.</div>
                    </div>
                </div>
            </div>
        </div>
    {% elif b.type == 'summit_steward' %}
        <div class="md-modal md-effect-1" id="modal-{{ forloop.counter0 }}">
            <div class="md-content">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <ul class="summit_badges" style="text-align: center;">
                        <li class="badge-icon-steward" style="float: none; display: inline-block;">
                            <img src="{% static 'img/summit-steward-badge.png' %}">
                        </li>
                    </ul>
                    <div class="badge-steward-data">
                        <div class="badge-data-top">You're now {{ b.peak_name|an }} {{ b.peak_name }}</div>
                        <div class="badge-data-middle"><span style="color: #00b1f2;">Summit Steward</span></div>
                        <div class="badge-data-bottom">Your <span style="color: #00b1f2; font-weight: 500;">5 summits</span> make you the {{ b.steward_count|ordinal }} Summit Steward of {{ b.peak_name }}</div>
                    </div>
                </div>
            </div>
        </div>
    {% elif b.type == 'king_of_the_mountain' %}
        <div class="md-modal md-effect-1" id="modal-{{ forloop.counter0 }}">
            <div class="md-content">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <ul class="summit_badges" style="text-align: center;">
                        <li class="badge-icon" style="float: none; display: inline-block">
                            <img src="{% static 'img/splash_badge_kom.png' %}">
                        </li>
                    </ul>
                    <div class="badge-icon-data">
                        <div class="badge-data-top">You're now the {{ b.peak_name }}</div>
                        <div class="badge-data-middle"><span style="color: #CFB53B;">King of the Mountain</span></div>
                        <div class="badge-data-bottom">You've summited this peak more times than any other member.</div>
                    </div>
                </div>
            </div>
        </div>
    {% elif b.type == '50_peaks' %}
        <div class="md-modal md-effect-1" id="modal-{{ forloop.counter0 }}">
            <div class="md-content">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <ul class="summit_badges" style="text-align: center;">
                        <li class="badge-icon" style="float: none; display: inline-block;">
                            <img src="{% static 'img/big-peak.png' %}">
                            <div class="badge-peak-count" style="background-color:#CD7F32;">
                                <span>50 peaks</span>
                            </div>
                        </li>
                    </ul>
                    <div class="badge-data-count">
                        <div class="badge-data-top">Nice, you've now climbed</div>
                        <div class="badge-data-middle">50 peaks</div>
                        <div class="badge-data-bottom">This puts you in the <span style="color: #f24100; font-weight: 500;">top 7%</span> of peakery members.</div>
                    </div>
                </div>
            </div>
        </div>
    {% elif b.type == '100_peaks' %}
        <div class="md-modal md-effect-1" id="modal-{{ forloop.counter0 }}">
            <div class="md-content">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <ul class="summit_badges" style="text-align: center;">
                        <li class="badge-icon" style="float: none; display: inline-block;">
                            <img src="{% static 'img/big-peak.png' %}">
                            <div class="badge-peak-count" style="background-color:#C0C0C0;">
                                <span>100 peaks</span>
                            </div>
                        </li>
                    </ul>
                    <div class="badge-data-count">
                        <div class="badge-data-top">Great work, you've now climbed</div>
                        <div class="badge-data-middle">100 peaks</div>
                        <div class="badge-data-bottom">This puts you in the <span style="color: #f24100; font-weight: 500;">top 3%</span> of peakery members.</div>
                    </div>
                </div>
            </div>
        </div>
    {% elif b.type == '200_peaks' %}
        <div class="md-modal md-effect-1" id="modal-{{ forloop.counter0 }}">
            <div class="md-content">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <ul class="summit_badges" style="text-align: center;">
                        <li class="badge-icon" style="float: none; display: inline-block;">
                            <img src="{% static 'img/big-peak.png' %}">
                            <div class="badge-peak-count" style="background-color:#CFB53B;">
                                <span>200 peaks</span>
                            </div>
                        </li>
                    </ul>
                    <div class="badge-data-count">
                        <div class="badge-data-top">Whoa, you've now climbed</div>
                        <div class="badge-data-middle">200 peaks</div>
                        <div class="badge-data-bottom">This puts you in the <span style="color: #00b1f2; font-weight: 500;">top 2%</span> of peakery members.</div>
                    </div>
                </div>
            </div>
        </div>
    {% elif b.type == 'elevation_pr' %}
        <div class="md-modal md-effect-1" id="modal-{{ forloop.counter0 }}">
            <div class="md-content">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="summit_badges">
                        <div class="badge-li">
                            <div class="thumb splash-grow-badge hover-photos" style="background: url({{ b.peak_thumbnail }}) 0% 0% / cover no-repeat; transform: scale(1); transition: 0.4s ease;">
                                <div class="peak splash-grow-badge-info">
                                    <span class="item_name ellipsis">{{ b.peak_name }}</span>
                                    <span class="item_info">{{ b.peak_elevation }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="badge-data">
                        <div class="badge-data-top">You set a personal record for</div>
                        <div class="badge-data-middle">Max Elevation</div>
                        <div class="badge-data-bottom">Higher than your old record <span style="color: #f24100; font-weight: 500;">{{ b.previous_record_peak }}</span> by <span style="color: #f24100; font-weight: 500;">{{ b.new_record_delta }} ft / {{ b.new_record_delta_meters }} m</span></div>
                    </div>
                </div>
            </div>
        </div>
    {% elif b.type == 'challenge' %}
        <div class="md-modal md-effect-1" id="modal-{{ forloop.counter0 }}">
            <div class="md-content">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    {% if b.summited_peaks == b.total_peaks %}
                    <div class="summit_badges">
                        <div class="badge-li">
                            <div class="thumb splash-grow-badge hover-photos challenge-badge challenge-badge-completed" style="background: url({{ MEDIA_URL }}{{ b.thumbnail }}) 0% 0% / cover no-repeat; transform: scale(1); transition: 0.4s ease;"></div>
                        </div>
                    </div>
                    <div class="badge-data-challenge">
                        <div class="badge-data-top">Congrats! You finished the</div>
                        <div class="badge-data-middle">{{ b.name }}</div>
                        <div class="progress progress-complete">
                            <div class="progress-bar progress-bar-blue challenge-badge-completed-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: {{ b.completion_pct }}%;">
                                <span class="challenge-show">{{ b.summited_peaks }} out of {{ b.total_peaks }} peaks ({{ b.completion_pct|floatformat:"0" }}%)</span>
                            </div>
                        </div>
                        <div class="badge-data-bottom">You're now one of <span class="challenge-badge-completed-text">{{ b.finisher_count }} members</span> who've completed this challenge</div>
                    </div>
                    {% else %}
                    <div class="summit_badges">
                        <div class="badge-li">
                            <div class="thumb splash-grow-badge hover-photos challenge-badge" style="background: url({{ MEDIA_URL }}{{ b.thumbnail }}) 0% 0% / cover no-repeat; transform: scale(1); transition: 0.4s ease;"></div>
                        </div>
                    </div>
                    <div class="badge-data-challenge">
                        <div class="badge-data-top">Your <span style="color: #f24100; font-weight: 500;">{{ b.summited_peaks|ordinal }}</span> peak in the</div>
                        <div class="badge-data-middle">{{ b.name }}</div>
                        <div class="progress progress-incomplete">
                            <div class="progress-bar progress-bar-blue" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: {{ b.completion_pct }}%;">
                                <span class="challenge-show">{{ b.summited_peaks }} out of {{ b.total_peaks }} peaks ({{ b.completion_pct|floatformat:"0" }}%)</span>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    {% endif %}
{% endfor %}

{% endblock %}
