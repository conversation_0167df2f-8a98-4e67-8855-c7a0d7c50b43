<script type="text/javascript">
    $(document).ready(function() {
        $('form#change_password').submit(function() {
                $(this).ajaxSubmit({
                    target: 'div#change_password',
                    success:    function(e) {
                        if(e=='True'){
                            $("div#change_password").hide();
                            location.reload(true);
                        }
                    }
                });
                return false;
        });
    });
</script>

<div id="change_password" style="width: 400px">
    <h1>Reset Password</h1>
    <form  id="change_password" class="form_set1"  method="post" action="{% url "change_password" %}">
        <fieldset>
            <label>Current password: </label>
            {{ password_form.old_password }}
            {{ password_form.old_password.errors }}
        </fieldset>

        <fieldset>
            <label>New password: </label>
            {{ password_form.new_password1 }}
            {{ password_form.new_password1.errors }}
        </fieldset>

        <fieldset>
            <label>Retype Password: </label>
            {{ password_form.new_password2 }}
            {{ password_form.new_password2.errors }}
        </fieldset>
        
        <input type="submit" value="Save" class="btn set2 input righted" />
    </form>
</div>
