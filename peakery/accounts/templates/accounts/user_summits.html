{% extends "base.html" %}
{% load static %}
{% load avatar_tags item_tags favorite_tags truncatechars verbatim %}
{% load humanize %}

{% block css %}
    <link href="{{MEDIA_URL}}css/view-peak.css?v=1" rel="stylesheet" type="text/css" media="screen" />
    <link href="{{MEDIA_URL}}css/lightboxes/django-follow.css" rel="stylesheet" type="text/css" media="screen" />
    <link href="{{MEDIA_URL}}css/jquery.lightbox-0.5.css" rel="stylesheet" type="text/css" media="screen" />
    <link href="{{MEDIA_URL}}css/jquery.lightbox-0.5.css" rel="stylesheet" type="text/css" media="screen" />
    <link href="{{MEDIA_URL}}css/tipsy.css" rel="stylesheet" type="text/css" media="screen" />
{% endblock %}

{% block title %}{{ bagger }}'s climbs{% endblock %}
{% block titlemeta %}{{ bagger }}'s climbs{% endblock %}
{% block description %}{{ bagger }}'s {{ summitlogs|length }} mountain trip report{{ summitlogs|length|pluralize:"s" }}{% endblock %}
{% block image_rel %}{% avatar_url bagger 135 %}{% endblock %}

{% block extrajs %}
    <script type="text/javascript" src="{% static 'js/jquery.lightbox-0.5.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/jquery.tipsy.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/handlebars.1.0.0.beta.3.js' %}"></script>
    <script type="text/javascript">
    {% include "mapbox/polyline.js" %}
    </script>
    <script src="{% static 'js/GPXParser.js'%}"></script>
{% endblock %}

{% block profile_active %}{% if user == bagger %}active{% endif %}{% endblock %}

{% block mobile_header_follow %}
{% if user.is_authenticated %}{% if user.username|stringformat:"s" != bagger|stringformat:"s" %}<a id="mobile-follow-{{ bagger.id }}" style="font-size: 11px; font-weight: 300; margin-left: 15px; {% if following %}display: none;{% else %}display: inline;{% endif %}">follow</a><a id="mobile-unfollow-{{ bagger.id }}" style="font-size: 11px; font-weight: 300; margin-left: 15px; color: #00b330; {% if following %}display: inline;{% else %}display: none;{% endif %}">following</a>{% else %}<a href="/members/{{ bagger }}/edit/" style="font-size: 11px; font-weight: 300; margin-left: 15px; {% if following %}display: none;{% else %}display: inline;{% endif %}">edit</a>{% endif %}{% endif %}
{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row hidden-xs">
        <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 65px; border-radius: 12px 12px 0 0;"><span style="font-size: 24px; font-weight: 600;">
            <div id="breadcrumbs" style="margin-left: -5px;">
                <ul>
                    <li style="margin-top: 2px;"><a class="modal-link" data-toggle="modal" data-target="#avatar-modal"><img style="height: 50px; width: 50px; border-radius:10px;" src="{{ MEDIA_URL }}{{ avatar.avatar_url|urlencode }}"></a><div style="float: right; margin-left: 15px; font-weight: 600; font-size: 20px;">{{ bagger }}{% if user.is_authenticated %}{% if user.username|stringformat:"s" == bagger|stringformat:"s" %}<a class="profile-edit-link hidden-xs" style="font-size: 12px; margin-left: 10px;" href="/members/{{ bagger }}/edit/">edit</a>{% endif %}{% endif %}</div></li>
                </ul>
                <div class="pull-right hidden-xs" style="margin-top: -5px;">
                    <a style="{{ subnav_info_style }}" class="region-header-sub-links" href="/members/{{ bagger }}/">Info</a><a style="{{ subnav_map_style }}" class="region-header-sub-links" href="/members/{{ bagger }}/map/">Map</a><a style="{{ subnav_badges_style }}" class="region-header-sub-links" href="/members/{{ bagger }}/badges/">Badges</a><a style="{{ subnav_summits_style }}" class="region-header-sub-links" href="/members/{{ bagger }}/summits/">Climbs</a><a style="{{ subnav_challenges_style }}" class="region-header-sub-links" href="/members/{{ bagger }}/challenges/">Challenges</a><a style="{{ subnav_p_style }}" class="region-header-sub-links" href="/members/{{ bagger }}/photos/">Photos</a>{% if user.is_authenticated %}{% if user.username|stringformat:"s" != bagger|stringformat:"s" %}<div class="region-header-sub-links hidden-xs hidden-sm" style="float: right; margin-top: 3px;"><a id="follow-{{ bagger.id }}" class="peakeryFollowButton followButton btn btn-secondary" style="font-size: 12px; float: right; width: 165px; {% if following %}display: none;{% else %}display: block;{% endif %}">Follow</a><a id="unfollow-{{ bagger.id }}" class="peakeryFollowButton btn btn-secondary unfollowButton" style="font-size: 12px; float: right; width: 165px; background-color: rgb(0, 179, 48); border-color: rgb(0, 179, 48); {% if following %}display: block;{% else %}display: none;{% endif %}">You are following</a></div>{% endif %}{% endif %}
                </div>
            </div>
        </span></div>
    </div>
{% endblock %}

{% block content %}

    <style type="text/css">

        .splash-grow-badge-info {
            background-image: none;
            background-color: #fff;

        }

.tooltip-inner {
        white-space:pre-wrap;
    }
        @media screen and (min-width: 600px) {
            .splash-grow-badge-info {
                border-bottom-right-radius: 60px;
                border-bottom-left-radius: 60px;
                box-shadow: 0 20px 20px 0px #000000;
            }
            .splash-grow-badge {
                border-top-left-radius: 60px;
                border-top-right-radius: 60px;
            }
        }

        .splash-grow-badge-info .item_name {
            color: #333;
        }

        .splash-grow-badge-info .item_info {
            color: #666;
        }

        .summit-card-header {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }

        body.modal-open {
            overflow: visible;
        }

        .modal.fade{
          opacity:1;
        }
        .modal.fade .modal-dialog {
           -webkit-transform: translate(0);
           -moz-transform: translate(0);
           transform: translate(0);
        }

        @media screen and (max-width: 767px) and (min-width: 1px) {
           #content-body {
               margin-top: 0px;
           }
           .content-pane {
               margin-top: 119px;
           }
           .mobile-regions-subnav-fixed {
               margin-top: 50px;
           }
           .peakeryFollowButton {
                font-size: 10px !important;
                width: 130px !important;
            }
           .summit-card-peak-title {
                position: absolute;
                top: 0px;
                left: 16px;
                font-size: 13px !important;
                color: #333;
            }
            .summit-card-mobile-stats {
                position: absolute;
                top: 40px;
                left: 10px;
                margin-right: 10px;
                font-size: 12px !important;
                line-height: 20px;
            }
            .filter-bar {
               top: 120px;
           }

            /* splash badges */
            .challenge-badge {
                width: 500px;
                height: 336px;
                box-shadow: 0px 0px 100px #999;
                border: 6px solid #fff;
                border-radius: 100% / 60%;
            }
            .challenge-badge-completed {
                width: 500px;
                height: 336px;
                border: 6px solid #7fff00;
                box-shadow: 0px 0px 100px #7fff00;
            }
            .splash-grow-badge {
                width: 300px;
                height: 200px;
            }
            .splash-grow-badge-info {
                width: 300px;
                height: 60px;
                padding-top: 13px;
                top: 200px;
            }
            .splash-grow-badge-info .item_name {
                font-size: 16px;
                line-height: 16px;
                font-weight: 600;
            }
            .splash-grow-badge-info .item_info {
                font-size: 12px;
            }
            .badge-data {
                text-align: center;
                color: #fff;
                margin-top: 110px;
            }
            .badge-data-count {
                text-align: center;
                color: #fff;
                margin-top: 50px;
            }
            .badge-data-challenge {
                text-align: center;
                color: #fff;
                margin-top: 50px;
            }
            .badge-icon-data {
                text-align: center;
                color: #fff;
                margin-top: -40px;
            }
            .badge-steward-data {
                text-align: center;
                color: #fff;
                margin-top: -20px;
            }
            .badge-data-top {
                font-size: 16px;
                line-height: 22px;
                font-weight: 500;
                padding-left: 15px;
                padding-right: 15px;
                margin-bottom: 10px;
            }
            .badge-data-middle {
                font-size: 24px;
                line-height: 30px;
                font-weight: 600;
                padding-left: 15px;
                padding-right: 15px;
                margin-bottom: 10px;
            }
            .badge-data-bottom {
                color: #999999;
                font-size: 12px;
                line-height: 16px;
                padding-left: 15px;
                padding-right: 15px;
            }
            .badge-icon {
                width: 300px !important;
            }
            .badge-icon img {
                width: 300px;
            }
            .badge-icon-steward {
                width: 250px !important;
            }
            .badge-icon-steward img {
                width: 250px;
            }
            .progress {
                width: 300px;
                position: relative;
                margin-left: -150px;
                left: 50%;
                height: 30px;
            }
            .progress-complete {
                margin-bottom: 20px;
            }
            .progress-bar-blue {
                font-size: 12px;
                line-height: 30px;
            }
            .badge-data-challenge .badge-data-middle {
                margin-bottom: 20px !important;
            }
            .badge-peak-count {
                margin-top: -130px;
                width: 150px;
                height: 50px;
                left: 75px;
                position: relative;
                -webkit-border-radius: 30px;
                -moz-border-radius: 30px;
                border-radius: 10px;
                border: 5px solid #fff;
            }
            .badge-peak-count span {
                color: #fff;
                line-height: 42px;
                font-size: 20px;
            }

       }
       @media screen and (max-width: 1023px) and (min-width: 768px) {
           .content-pane {
               margin-top: 49px;
           }
           .mobile-regions-subnav-fixed {
               margin-top: 0px;
           }
           .filter-bar {
               top: 141px;
           }

           /* splash badges */
           .challenge-badge {
                width: 500px;
                height: 336px;
                box-shadow: 0px 0px 100px #999;
                border: 10px solid #fff;
                border-radius: 100% / 60%;
            }
            .challenge-badge-completed {
                width: 500px;
                height: 336px;
                border: 10px solid #7fff00;
                box-shadow: 0px 0px 100px #7fff00;
            }
            .splash-grow-badge {
                width: 480px;
                height: 325px;
            }
            .splash-grow-badge-info {
                width: 480px;
                height: 90px;
                padding-top: 20px;
                top: 325px;
            }
            .splash-grow-badge-info .item_name {
                font-size: 24px;
                line-height: 24px;
                font-weight: 600;
            }
            .splash-grow-badge-info .item_info {
                font-size: 14px;
            }
            .badge-data {
                text-align: center;
                color: #fff;
                margin-top: 120px;
            }
            .badge-data-count {
                text-align: center;
                color: #fff;
                margin-top: 90px;
            }
            .badge-data-challenge {
                text-align: center;
                color: #fff;
                margin-top: 50px;
            }
            .badge-icon-data {
                text-align: center;
                color: #fff;
                margin-top: -40px;
            }
            .badge-steward-data {
                text-align: center;
                color: #fff;
                margin-top: -20px;
            }
            .badge-data-top {
                font-size: 26px;
                line-height: 32px;
                font-weight: 500;
                padding-left: 15px;
                padding-right: 15px;
                margin-bottom: 15px;
            }
            .badge-data-middle {
                font-size: 38px;
                line-height: 44px;
                font-weight: 600;
                padding-left: 15px;
                padding-right: 15px;
                margin-bottom: 15px;
            }
            .badge-data-bottom {
                color: #999999;
                font-size: 14px;
                line-height: 20px;
                padding-left: 15px;
                padding-right: 15px;
            }
            .badge-icon {
                width: 480px !important;
            }
            .badge-icon img {
                width: 480px;
            }
            .badge-icon-steward {
                width: 400px !important;
            }
            .badge-icon-steward img {
                width: 400px;
            }
            .progress {
                width: 600px;
                position: absolute;
                margin-left: -300px;
                margin-top: -55px;
                left: 50%;
                height: 35px;
            }
            .progress-incomplete {
                margin-top: -45px;
            }
            .challenge-show {
                font-size: 14px;
                line-height: 35px;
            }
            .badge-data-challenge .badge-data-middle {
                margin-bottom: 60px !important;
            }
            .badge-peak-count {
                margin-top: -200px;
                width: 240px;
                height: 80px;
                left: 120px;
                position: relative;
                -webkit-border-radius: 30px;
                -moz-border-radius: 30px;
                border-radius: 20px;
                border: 5px solid #fff;
            }
            .badge-peak-count span {
                color: #fff;
                line-height: 70px;
                font-size: 30px;
            }

       }
        @media screen and (min-width: 1024px) {
            .content-pane {
               margin-top: 49px;
           }
            .mobile-regions-subnav-fixed {
               margin-top: 0px;
           }
            .regions-subnav-fixed {
                top: 141px;
            }

            /* splash badges */
            .challenge-badge {
                width: 500px;
                height: 336px;
                box-shadow: 0px 0px 100px #999;
                border: 10px solid #fff;
                border-radius: 100% / 60%;
            }
            .challenge-badge-completed {
                width: 500px;
                height: 336px;
                border: 10px solid #7fff00;
                box-shadow: 0px 0px 100px #7fff00;
            }
            .splash-grow-badge {
                width: 480px;
                height: 325px;
            }
            .splash-grow-badge-info {
                width: 480px;
                top: 325px;
                height: 90px;
                padding-top: 20px;
            }
            .splash-grow-badge-info .item_name {
                font-size: 24px;
                line-height: 24px;
                font-weight: 600;
            }
            .splash-grow-badge-info .item_info {
                font-size: 14px;
            }
            .badge-data {
                text-align: center;
                color: #fff;
                margin-top: 120px;
            }
            .badge-data-count {
                text-align: center;
                color: #fff;
                margin-top: 90px;
            }
            .badge-data-challenge {
                text-align: center;
                color: #fff;
                margin-top: 50px;
            }
            .badge-icon-data {
                text-align: center;
                color: #fff;
                margin-top: -40px;
            }
            .badge-steward-data {
                text-align: center;
                color: #fff;
                margin-top: -20px;
            }
            .badge-data-top {
                font-size: 34px;
                line-height: 40px;
                font-weight: 500;
                padding-left: 15px;
                padding-right: 15px;
            }
            .badge-data-middle {
                font-size: 50px;
                line-height: 56px;
                font-weight: 600;
                padding-left: 15px;
                padding-right: 15px;
                margin-bottom: 20px;
            }
            .badge-data-bottom {
                color: #999999;
                font-size: 16px;
                line-height: 22px;
                padding-left: 15px;
                padding-right: 15px;
                margin-bottom: 20px;
            }
            .badge-icon {
                width: 480px !important;
            }
            .badge-icon img {
                width: 480px;
            }
            .badge-icon-steward {
                width: 400px !important;
            }
            .badge-icon-steward img {
                width: 400px;
            }
            .progress {
                width: 600px;
                position: absolute;
                margin-left: -300px;
                margin-top: -60px;
                left: 50%;
            }
            .badge-data-challenge .badge-data-middle {
                margin-bottom: 70px !important;
            }
            .badge-peak-count {
                margin-top: -200px;
                width: 240px;
                height: 80px;
                left: 120px;
                position: relative;
                -webkit-border-radius: 30px;
                -moz-border-radius: 30px;
                border-radius: 20px;
                border: 5px solid #fff;
            }
            .badge-peak-count span {
                color: #fff;
                line-height: 70px;
                font-size: 30px;
            }

        }

    </style>

    <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>

<div class="container">
    <!-- Mobile header -->
    <div class="row sub-header-row hidden-lg hidden-md hidden-sm" style="height: 50px; position: fixed; width: 100%; z-index: 2; top: 70px;">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999; display: flex; justify-content: space-between;">
            <a style="{{ subnav_info_style }}" class="mobile-header-sub-links" href="/members/{{ bagger }}/">Info</a>{% if IS_MOBILE_APP_ACCESS != 'True' %}<a style="{{ subnav_map_style }}" class="mobile-header-sub-links" href="/members/{{ bagger }}/map/">Map</a>{% endif %}<a style="{{ subnav_badges_style }}" class="mobile-header-sub-links" href="/members/{{ bagger }}/badges/">Badges</a><a style="{{ subnav_summits_style }}" class="mobile-header-sub-links" href="/members/{{ bagger }}/summits/">Climbs</a><a style="{{ subnav_challenges_style }}" class="mobile-header-sub-links" href="/members/{{ bagger }}/challenges/">Challenges</a><a style="{{ subnav_p_style }}" class="mobile-header-sub-links" href="/members/{{ bagger }}/photos/">Photos</a>
        </div>
    </div>
    <!-- End mobile header -->

    <div class="row sub-header-row hidden-xs hidden-sm regions-subnav-fixed" style="height: 50px; padding-right: 0px;">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999;">
            <span style="font-size: 14px; float: left;"><span id="summit-count" style="color: #f24100;">{{ summitlogs|length }}</span> <span id="summit-count-desc">climb{{ summitlogs|length|pluralize:"s" }}</span></span>
            <span style="font-size: 14px; float: left;">&nbsp;in</span>
            <span class="select" id="selectTimefilter" style="float: left; margin-top: -3px;">
                <button class="btn btn-default year-filter-button" style="margin-bottom: 0px; background-color: #f2f2f2; border-color: #f2f2f2; padding: 8px 8px; font-size: 15px;" data-toggle="dropdown">
                    <span id="timefilter-title">all years</span>
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu timefilter-list" style="height: auto; left: inherit; overflow: scroll; max-height: 70vh;">
                    <li style="float: none;"><a id="timefilter-item-0" class="timefilter-item" data-value="" href="#">all years</a></li>
                    {% for y in years %}
                    <li style="float: none;"><a id="timefilter-item-{{ y.year }}" class="timefilter-item" data-value="{{ y.year }}" href="#">{{ y.year }}</a></li>
                    {% endfor %}
                </ul>
            </span>
            <span style="font-size: 14px; float: left;">in</span>
            <span class="select" id="selectRegionFilter" style="float: left; margin-top: 4px;">
                <button class="btn btn-default region-filter-button" style="margin-bottom: 0px; background-color: #f2f2f2; border-color: #f2f2f2; padding: 8px 8px; font-size: 15px;" data-toggle="dropdown">
                    <span id="regionfilter-title" style="color: #00B1F2;">all regions</span>
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu regionfilter-list" style="height: auto; left: inherit; overflow: scroll; max-height: 70vh;">
                    <li style="float: none;"><a class="regionfilter-item" data-value="" href="#">all regions</a></li>
                    {% for r in regions %}
                    <li style="float: none;"><a class="regionfilter-item" data-value="{{ r.id }}" href="#">{{ r.region_name }} &bull; {{ r.summit_count }} climb{{ r.summit_count|pluralize:"s" }}</a></li>
                    {% endfor %}
                </ul>
            </span>
            <span class="pull-right"><a style="margin-left: 10px; color: rgb(153, 153, 153); font-weight: 300;" id="type-all" class="region-header-sub-links ajax-link" onclick="loadSummits(summitYear, summitRegion, 'all', '1');">All</a><a style="margin-left: 35px; color: rgb(153, 153, 153); font-weight: 300;" id="type-successes" class="region-header-sub-links ajax-link" onclick="loadSummits(summitYear, summitRegion, 'successes', '1');">Summits</a><a style="margin-left: 35px; color: rgb(153, 153, 153); font-weight: 300;" id="type-attempts" class="region-header-sub-links ajax-link" onclick="loadSummits(summitYear, summitRegion, 'attempts', '1');">Attempts</a></span>
        </div>
    </div>

    <div class="row content-pane">

        <div class="col-md-12">
            <div class="row sub-header-row hidden-md hidden-lg filter-bar" style="height: 50px; padding-right: 0px;">
                <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12" style="height: 50px;line-height: 26px;">
                    <div style="float: left; width: 50%; height: 100%; border-right: solid 1px #cfcfcf;">
                        <div class="select" id="selectMobileTimefilter" style="margin-top: 11px; margin-left: 5px;">
                            <select id="mobileTimefilterSelect" style="width: 96%; -webkit-appearance: none; border: 0; background: none; color: #00b1f2;">
                                <option value="all">All years</option>
                                {% for y in years %}
                                <option value="{{ y.year }}">{{ y.year }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div style="float: left; margin-left: 10px; width: 45%;">
                        <div class="select" id="selectMobileRegionfilter" style="margin-top: 11px; margin-left: 5px;">
                            <select id="mobileRegionfilterSelect" style="width: 96%; -webkit-appearance: none; border: 0; background: none; color: #00b1f2;">
                                <option value="">All regions</option>
                                {% for r in regions %}
                                <option value="{{ r.id }}">{{ r.region_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-12">
            <div class="row" id="ajax-data-loading" style="display: none;">
              <div class="col-md-12 data-loading" style="text-align: center; color: #666;">
                <i class="fa fa-spinner fa-spin fa-5x"></i>
              </div>
            </div>
            <div class="hidden-xs">
                <div id="chart-container" class="row sub-header-row" style="cursor: pointer; height: 205px; background-color: #fff; display: none; border-radius: 0 0 12px 12px;">
                    <div class="col-md-12" style="height: 205px;">
                        <div id="chart_div"></div>
                    </div>
                </div>
            </div>
            <div id="chart-spacer" style="display: none;" class="row dark-background-row hidden-xs"><div class="sp-60"></div></div>
            <div class="row" id="summits-list">
                <div class="col-md-12">

                </div>
            </div>
        </div>
    </div>
    <div class="row dark-background-row" id="pagination" style="border-bottom: none; color: #ccc; margin-top: -1px; height: 80px; margin-left: 0px; display: none;">
        <div class="col-md-12">
            <div class="row" style="padding-left: 15px; margin-left: 5px; text-align: center;">
                <span id="pagination-pages" class="step-links"></span>
            </div>
        </div>
    </div>
    <div class="row dark-header-row hidden-lg hidden-md hidden-sm bottom-spacer">
        <div class="row">
            <div style="height: 28px;"></div>
        </div>
    </div>
    <div class="row dark-header-row hidden-xs bottom-spacer">
        <div class="row">
            <div style="height: 53px;"></div>
        </div>
    </div>

</div>

<script type="text/javascript">

    // Hide mobile filter bar on on scroll down
    var didScroll;
    var lastScrollTop = 0;
    var delta = 5;

    $(window).scroll(function(event){
        didScroll = true;
    });

    document.addEventListener("touchstart", ScrollStart, false);

    setInterval(function() {
        if (didScroll) {
            hasScrolled();
            didScroll = false;
        }
    }, 100);

    function ScrollStart() {
        //start of scroll event for iOS
        hasScrolled();
    }

    function hasScrolled() {
        var st = $(this).scrollTop();

        // Make sure they scroll more than delta
        if (Math.abs(lastScrollTop - st) <= delta)
            return;

        // If they scrolled down and are past the filter bar, add class .filter-scrollup.
        // This is necessary so you never see what is "behind" the navbar.
        if (st > lastScrollTop && st > 50) {
            // Scroll Down
            $('.filter-bar').hide();
        } else {
            // Scroll Up
            if (st + $(window).height() < $(document).height()) {
                $('.filter-bar').show();
            }
        }
        lastScrollTop = st;
    }

    var summitSort;
    var summitYear;
    var summitRegion;
    var chartData = [];
    var loading = false;

    var regionData = {};
    {% for r in regions %}
    regionData[{{ r.id }}] = {{ r.summit_count }};
    {% endfor %}

    var yearData = {};
    {% for y in years %}
    yearData['{{ y.year }}'] = {{ y.summits_count }};
    {% endfor %}

    google.load('visualization', '1', {packages: ['corechart', 'bar']});
    google.setOnLoadCallback(init);

    function drawAnnotations(chartdata) {

        try {
          var data;
          data = google.visualization.arrayToDataTable(chartdata);
        {% if years|length > 30 %}
          var hAxisFontSize = 8;
        {% elif years|length > 20 %}
          var hAxisFontSize = 10;
        {% else %}
          var hAxisFontSize = 12;
        {% endif %}

          var options = {
            backgroundColor: '#ffffff',
            legend: 'none',
            vAxis:{
             baselineColor: '#ffffff',
             gridlineColor: '#ffffff',
             textPosition: 'none'
            },
            hAxis:{
                textStyle: {color: '#00B1F2', fontSize: hAxisFontSize}
                //direction:-1,
                //slantedText:true,
                //slantedTextAngle:90
            },
            animation: {
                startup: true,
                duration: 1000,
                easing: 'out'
            },
            annotations: {
              alwaysOutside: true,
              stemLength: 1,
              stemColor: '#ffffff',
              textStyle: {
                fontSize: 14,
                color: '#000000',
                auraColor: 'none'
              }
            },
            chartArea: {
                left: "0",
                top: "5",
                height: "175",
                width: "100%"
            },
            width: '100%',
            height: '205',
            'tooltip' : {
              trigger: 'none'
            }
          };

          var clickHandler = function(e) {
            var parts = e.targetID.split('#');
            var selectedIndex;
            var reload = false;
            if (parts[0] == 'hAxis') {
                selectedIndex = parts[3];
                reload = true;
            } else if (parts[0] == 'bar') {
                selectedIndex = parts[2];
                reload = true;
            } else if (parts[0] == 'annotationtext') {
                selectedIndex = parts[2];
                reload = true;
            }
            if (reload) {
                selectedIndex = parseInt(selectedIndex) + 1;
                if (chartData[selectedIndex][0] == '????') {
                    summitYear = 'Unknown';
                } else {
                    summitYear = chartData[selectedIndex][0];
                }
                $("#timefilter-title").html(summitYear);
                $('#mobile-timefilter-title').html(summitYear);
                loadSummits(summitYear, summitRegion, summitSort, '1');
            }
          };

          var chart = new google.visualization.ColumnChart(document.getElementById('chart_div'));

          // Listen for the 'select' event, and call my function selectHandler() when
          // the user selects something on the chart.
          //google.visualization.events.addListener(chart, 'select', selectHandler);
          google.visualization.events.addListener(chart, 'click', clickHandler);

          $('#chart-container').show();
          $('#chart-spacer').show();

          chart.draw(data, options);
        }
        catch {
            //pass
        }
    }

    function loadSummits(year, region, type, page) {

        loading = true;

        if (year == '????') {
            year = 'Unknown';
        }

        if (year != '') {
            $('#mobileTimefilterSelect').val(year);
        }

        summitSort = type;
        summitYear = year;
        summitRegion = region;
        $('#pagination').css('display','none');
        $('#chart-container').css('display','none');
        $('#chart-spacer').css('display','none');
        $('.bottom-spacer').show();
        window.scrollTo(0, 0);

        switch(type) {
            case 'all':
                $('#type-all').css('color', '#F24100');
                $('#type-all').css('font-weight', '500');
                $('#type-successes').css('color', '#999');
                $('#type-successes').css('font-weight', '300');
                $('#type-attempts').css('color', '#999');
                $('#type-attempts').css('font-weight', '300');
                $('#ranked-by-title').html('All');
                break;
            case 'successes':
                $('#type-all').css('color', '#999');
                $('#type-all').css('font-weight', '300');
                $('#type-successes').css('color', '#F24100');
                $('#type-successes').css('font-weight', '500');
                $('#type-attempts').css('color', '#999');
                $('#type-attempts').css('font-weight', '300');
                $('#ranked-by-title').html('Successful summits');
                break;
            case 'attempts':
                $('#type-all').css('color', '#999');
                $('#type-all').css('font-weight', '300');
                $('#type-successes').css('color', '#999');
                $('#type-successes').css('font-weight', '300');
                $('#type-attempts').css('color', '#F24100');
                $('#type-attempts').css('font-weight', '500');
                $('#ranked-by-title').html('Attempts');
                break;
            default:
                $('#type-all').css('color', '#F24100');
                $('#type-all').css('font-weight', '500');
                $('#type-successes').css('color', '#999');
                $('#type-successes').css('font-weight', '300');
                $('#type-attempts').css('color', '#999');
                $('#type-attempts').css('font-weight', '300');
                $('#ranked-by-title').html('All');
        }

        window.location.hash = 'year='+year+'&region='+region+'&order='+type+'&page='+page;

        $('#summits-list').empty();
        $('#ajax-data-loading').css('display', 'inline');
        var totalPages, totalSummits;
        var counter = 1;

        chartData = [];
        chartData.push(['Year', 'Total Summits', {role: 'style'}, {role: 'annotation'}]);
        var chartDataYears = {};

        {% for y in years %}
            chartDataYears['{{ y.year }}'] = {'total_summits': {{ y.summits_count }}, 'region_summits': 0, 'style': 'stroke-color: #FFFFFF; stroke-width: 2; color: #00b1f2;', 'annotation': '{{ y.summits_count }}'};
        {% endfor %}

        $.getJSON('{% url "user_summits_list" %}?user={{ profile.user_id }}&year='+year+'&region='+region+'&type='+type+'&page='+page, function(data) {
            $.each( data, function( key, val ) {
                if (key=='summit_years') {

                    var total_count = 0;
                    $.each( val, function( yearkey, yearval ) {

                        if (region != '' || type == 'attempts' || type == 'successes') {
                            chartDataYears[yearval.year].region_summits = yearval.summits_count;
                        }

                        if (yearval.year == year) {
                            chartDataYears[yearval.year].style = 'stroke-color: #FFFFFF; stroke-width: 2; color: #f24100;';
                        }

                        if (year != '') {
                            if (parseInt(yearval.year) == parseInt(year)) {
                                total_count = total_count + parseInt(yearval.summits_count);
                            } else if (yearval.year == 'Unknown' && year == 'Unknown') {
                                total_count = total_count + parseInt(yearval.summits_count);
                            }
                        } else {
                            total_count = total_count + parseInt(yearval.summits_count);
                        }
                    });
                    if (region != '' && year == '' && type != 'attempts') {
                        total_count = regionData[region];
                    }
                    $('#summit-count').html(total_count);
                    totalSummits = parseInt(total_count);
                    if (totalSummits != 1) {
                        $('#summit-count-desc').html('climbs');
                    } else {
                        $('#summit-count-desc').html('climb');
                    }
                    totalPages = Math.ceil(parseInt(total_count)/25);

                    $.each(chartDataYears, function( key, val ) {
                        var display_year = key;
                        if (key == 'Unknown') {
                            display_year = '????';
                        }
                        if (region != '' || type == 'attempts' || type == 'successes') {
                            chartData.push([display_year, parseInt(val.region_summits), val.style, val.region_summits]);
                        } else {
                            chartData.push([display_year, parseInt(val.total_summits), val.style, val.annotation]);
                        }
                    });

                    if (total_count > 0) {
                        drawAnnotations(chartData);
                    } else {
                        $('.bottom-spacer').hide();
                    }
                }
                if (key=='summits') {
                    $.each( val, function( summitkey, summitval ) {
                        var summitdate = new Date(summitval.summitlog_date);
                        var today = new Date();
                        var timeDiff = Math.abs(today.getTime() - summitdate.getTime());
                        var diffDays = Math.ceil(timeDiff / (1000 * 3600 * 24));
                        var photo_arr = summitval.photos;
                        var avatar;
                        var isUsa = false;
                        if (summitval.avatar_url != 'None') {
                            avatar = '<img src="{{MEDIA_URL}}' + summitval.avatar_url + '" class="summit-card-user-avatar">&nbsp;&nbsp;';
                        } else {
                            avatar = '';
                        }

                        //build country string
                        var country = '';
                        $.each( summitval.country, function( countrykey, countryval ) {
                            country = country + countryval.country_name + ' / ';
                            if (countryval.country_name == 'United States') {
                                isUsa = true;
                            }
                        });
                        country = country.substr(0, country.length-3);
                        if (country == '') {
                            country = 'Unknown Country';
                        }

                        //build region string
                        var region = '';
                        $.each( summitval.region, function( regionkey, regionval ) {
                            region = region + regionval.region_name + ', ' + regionval.country_name + ' / ';
                        });
                        region = region.substr(0, region.length-3);
                        if (region == '') {
                            region = country;
                        }

                        var dividerDiv = '';
                        if (counter > 1) {
                            dividerDiv = '<div class="row dark-background-row"><div class="sp-60"></div></div>';
                        }

                        if (summitval.date_entered == 'False') {
                            var desktop_date = 'date unknown';
                            var mobile_date = 'date unknown';
                        } else {
                            var display_date = $.format.date(summitval.summitlog_date + 'T00:00:00', 'MMM d, yyyy');
                            var desktop_date = display_date + '&nbsp;/&nbsp;<time class="timeago" datetime="' + summitval.summitlog_date + 'T00:00:00">' + summitval.summitlog_date + '</time>';
                            var mobile_date = '<time class="timeago" datetime="' + summitval.summitlog_date + 'T00:00:00">' + summitval.summitlog_date + '</time>';
                        }

                        var header_border = '';
                        if (photo_arr.length == 0 && (summitval.gpx_file == 'None' || summitval.gpx_file == '') && summitval.log != null) {
                            header_border = 'border-bottom: 1px solid #c0c0c0; ';
                        }

                        var attempt = '';
                        var mobile_stats = '';

                        var max_username_length = 15;
                        var max_region_length = 27;
                        var trimmed_username = summitval.username.length > max_username_length ? summitval.username.substring(0, max_username_length - 3) + "..." : summitval.username;
                        var trimmed_region = region.length > max_region_length ? region.substring(0, max_region_length - 3) + "..." : region;

                        if (summitval.attempt == 'True') {
                            attempt = '<span class="summit-attempt">Attempt</span>&nbsp;&nbsp;&bull;&nbsp;&nbsp;';
                            mobile_stats = '<span class="summit-attempt">Attempt</span>&nbsp;&bull;&nbsp;'  + '<a href="/members/' + summitval.username + '/">' + trimmed_username + '</a>&nbsp;&bull;&nbsp;' + mobile_date;
                        } else {
                            //if summit doesn't have a peak
                            if (summitval.peak_id == '{{ NO_PEAK_ID }}') {
                                mobile_stats = '<a href="/members/' + summitval.username + '/">' + trimmed_username + '</a>&nbsp;&bull;&nbsp;' + mobile_date;
                            } else {
                                mobile_stats = trimmed_region + '&nbsp;&bull;&nbsp;<a href="/members/' + summitval.username + '/">' + trimmed_username + '</a>&nbsp;&bull;&nbsp;' + mobile_date;
                            }
                        }

                        var additional_summits = '';
                        if (summitval.num_summits_in_group > 1) {
                            var num_additional_summits = parseInt(summitval.num_summits_in_group) - 1;
                            var additional_peak_names = '';
                            $.each(summitval.addl_summits, function (addlsummitkey, addlsummitval) {
                                additional_peak_names = additional_peak_names + addlsummitval.peak_name + '\n';
                            });
                            additional_summits = '<span class="summit-list-stats addl-summits-stats" style="font-weight: 300; color: #0ae;" data-toggle="tooltip" data-placement="top" data-original-title="' + additional_peak_names + '">&nbsp;&nbsp;+ '  + num_additional_summits + ' other peak' + ((num_additional_summits != 1) ? 's' : '') + '</span>';
                        }

                        //if summit doesn't have a peak
                        if (summitval.peak_id == '{{ NO_PEAK_ID }}') {
                            $('#summits-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">' + dividerDiv + '<div class="row" style="' + header_border + 'cursor: pointer;" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');"><div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 summit-card-header" style="background-color: #fff; border-right: 1px solid #c0c0c0; letter-spacing: 1.0px;"><div class="summit-list-stats summit-card-user-stats hidden-xs hidden-sm" style="float: right;">' + avatar + '<a href="/members/' + summitval.username + '/">' + summitval.username + '</a>&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + desktop_date + '</div><div class="summit-list-stats summit-card-user-stats hidden-lg hidden-md hidden-xs" style="float: right;"><a href="/members/' + summitval.username + '/">' + summitval.username + '</a>&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + mobile_date + '</div><div class="summit-list-stats summit-card-peak-stats hidden-lg hidden-md hidden-xs" style="float: right;"></div><div class="stats-header summit-card-peak-title" style="height: 70px; margin-top: 0px; margin-bottom: 0px;"><div class="summit-list-stats summit-card-peak-stats hidden-xs hidden-sm" style="float: right;"></div><div class="ellipsis" style="overflow: hidden;">' + summitval.peak_name + additional_summits + '</div></div><div class="summit-list-stats summit-card-mobile-stats hidden-lg hidden-md hidden-sm">' + mobile_stats + '</div></div></div>');
                        } else {
                            $('#summits-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">' + dividerDiv + '<div class="row" style="' + header_border + 'cursor: pointer;" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');"><div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 summit-card-header" style="background-color: #fff; border-right: 1px solid #c0c0c0; letter-spacing: 1.0px;"><div class="summit-list-stats summit-card-user-stats hidden-xs hidden-sm" style="float: right;">&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + avatar + '<a href="/members/' + summitval.username + '/">' + summitval.username + '</a>&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + desktop_date + '</div><div class="summit-list-stats summit-card-user-stats hidden-lg hidden-md hidden-xs" style="float: right;">&nbsp;&nbsp;&bull;&nbsp;&nbsp;<a href="/members/' + summitval.username + '/">' + summitval.username + '</a>&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + mobile_date + '</div><div class="summit-list-stats summit-card-peak-stats hidden-lg hidden-md hidden-xs" style="float: right;">' + attempt + region + '</div><div class="stats-header summit-card-peak-title" style="height: 70px; margin-top: 0px; margin-bottom: 0px;"><div class="summit-list-stats summit-card-peak-stats hidden-xs hidden-sm" style="float: right;">' + attempt + numberWithCommas(Math.floor(summitval.elevation)) + ' ft / ' + numberWithCommas(Math.floor(summitval.elevation * .3048)) + ' m&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + region + '</div><div class="ellipsis" style="overflow: hidden;"><a style="color: #000000;" href="/' + summitval.peak_slug + '/">' + summitval.peak_name + '</a>' + additional_summits + '</div></div><div class="summit-list-stats summit-card-mobile-stats hidden-lg hidden-md hidden-sm">' + mobile_stats + '</div></div></div>');
                        }

                        //get favorites count
                        var favorites_arr = summitval.favorites;
                        var favorites_count = favorites_arr.length;
                        if (favorites_count == '0') {
                            favorites_count = '&nbsp;';
                        }

                        //get comments count
                        var comments_arr = summitval.comments;
                        var comments_count = comments_arr.length;
                        if (comments_count == '0') {
                            comments_count = '&nbsp;';
                        }

                        var staticMapZoom = '/' + summitval.peak_long + ',' + summitval.peak_lat + ',10,0.00,0.00';
                        var twoPhotoMapClass = 'col-lg-6 col-md-6 col-sm-4 hidden-xs map-card-web-2 map-card-tablet-2 map-card-mobile-2';
                        //gpx file?
                        if (summitval.gpx_file != 'None' && summitval.gpx_file != '') {
                            staticMapZoom = '/auto';
                            twoPhotoMapClass = 'col-lg-6 col-md-6 col-sm-4 col-xs-12 map-card-web-2 map-card-tablet-2 map-card-mobile-2';
                        }

                        //add photos
                        var photo_index = 0;
                        var photo_arr = summitval.photos;
                        var photos_count = photo_arr.length;
                        var photos_style = '';
                        if (photos_count == '0') {
                            photos_count = '&nbsp;';
                            photos_style = 'display: none;'
                        }
                        var staticMapUrl = summitval.map_mapbox_thumbnail;

                        // if we have a gpx file
                        if (summitval.gpx_file != 'None' && summitval.gpx_file != '' && summitval.gpx_mapbox_thumbnail != '') {

                            //build stats overlay
                            var statsOverlayText = '';
                            var mobileStatsOverlayText = '';
                            var gpxStatsOverlay = '';
                            var mobileStatsOverlay = '';
                            var divStyle = '';
                            if (summitval.total_distance > 0 || summitval.total_trip_time > 0 || summitval.elevation_gain > 0) {
                                if (isUsa) {
                                    if (summitval.total_distance > 0) {
                                        gpxStatsOverlay = gpxStatsOverlay + parseFloat(summitval.total_distance).toFixed(1) + ' mi &bull; ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="fas fa-arrows-alt-h" style="margin-right: 5px;"></i>' + parseFloat(summitval.total_distance).toFixed(1) + ' mi</span><div style="width: 1px; height: 30px; background-color: #ccc; margin-top: 10px;"></div> ';
                                    }
                                    if (summitval.elevation_gain > 0) {
                                        gpxStatsOverlay = gpxStatsOverlay + parseInt(summitval.elevation_gain).toLocaleString() + ' ft gain &bull; ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="fas fa-long-arrow-alt-up fa-rotate-45" style="margin-right: 5px;"></i>' + parseInt(summitval.elevation_gain).toLocaleString() + ' ft gain</span><div style="width: 1px; height: 30px; background-color: #ccc; margin-top: 10px;"></div> ';
                                    }
                                    if (summitval.total_trip_time > 0) {
                                        totalSeconds = parseFloat(summitval.total_trip_time);
                                        hours = Math.floor(totalSeconds / 3600);
                                        totalSeconds %= 3600;
                                        minutes = Math.floor(totalSeconds / 60);
                                        gpxStatsOverlay = gpxStatsOverlay + hours + 'h ' + minutes + 'm ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="far fa-clock" style="margin-right: 5px;"></i>' + hours + ' hr ' + minutes + ' min </span></div>';
                                    }
                                } else {
                                    if (summitval.total_distance > 0) {
                                        km = parseFloat(summitval.total_distance) * 1.609344
                                        gpxStatsOverlay = gpxStatsOverlay + km.toFixed(1) + ' km &bull; ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="fas fa-arrows-alt-h" style="margin-right: 5px;"></i>' + km.toFixed(1) + ' km</span><div style="width: 1px; height: 30px; background-color: #ccc; margin-top: 10px;"></div> ';
                                    }
                                    if (summitval.elevation_gain > 0) {
                                        m = parseFloat(summitval.elevation_gain) * 0.3048
                                        gpxStatsOverlay = gpxStatsOverlay + parseInt(m).toLocaleString() + ' m gain &bull; ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="fas fa-long-arrow-alt-up" style="margin-right: 5px;"></i>' + parseInt(m).toLocaleString() + ' m gain</span><div style="width: 1px; height: 30px; background-color: #ccc; margin-top: 10px;"></div> ';
                                    }
                                    if (summitval.total_trip_time > 0) {
                                        totalSeconds = parseFloat(summitval.total_trip_time);
                                        hours = Math.floor(totalSeconds / 3600);
                                        totalSeconds %= 3600;
                                        minutes = Math.floor(totalSeconds / 60);
                                        gpxStatsOverlay = gpxStatsOverlay + hours + 'h ' + minutes + 'm ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="far fa-clock" style="margin-right: 5px;"></i>' + hours + ' hr ' + minutes + ' min </span>';
                                    }
                                }
                            }

                            statsOverlayText = '<div class="user-photo-info hidden-xs" style="display: block; height: 60px;"><span class="data photo-caption" style="position: absolute; bottom: -5px; right: 10px; color: #fff;"><p class="bagger" style="font-size: 16px; margin: 0 10px 15px 0;">' + gpxStatsOverlay + '</p></span></div>';
                            mobileStatsOverlayText = '<div class="hidden-lg hidden-md hidden-sm col-xs-12" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; background-color: #fff; border-bottom: 1px solid #c0c0c0; margin-top: -1px; padding-left: 0px; padding-right: 0px;"><div class="col-md-12" style="height: 50px;line-height: 50px;font-size: 12px;color: #999;display: flex;justify-content: space-between;background-color: #f2f2f2;padding-left: 0px;padding-right: 1px;">' + mobileStatsOverlay + '</div></div>';

                            //if log doesn't have a peak, an Activity, so will have GPX
                            if (summitval.peak_id == '{{ NO_PEAK_ID }}') {

                                if (photo_arr.length == 0) {
                                    $('#summits-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 map-card-web-0 map-card-tablet-0 map-card-mobile-0" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" id="map-' + summitval.id + '" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="img-responsive peakeryPhoto photography peakimg-responsive" src="{% static 'img/spacer.png' %}"></div>' + statsOverlayText + '</div>');
                                } else if (photo_arr.length == 1) {
                                    $.each(summitval.photos, function (photokey, photoval) {
                                        photo_index++;
                                        $('#summits-list').append('<div class="summit-card-photo-' + photo_index + ' col-lg-3 col-md-3 col-sm-4 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                    });
                                    $('#summits-list').append('<div class="col-lg-9 col-md-9 col-sm-8 col-xs-6 map-card-web-1 map-card-tablet-1 map-card-mobile-1" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png' %}"></div>' + statsOverlayText + '</div>');
                                } else if (photo_arr.length == 2) {
                                    $.each(summitval.photos, function (photokey, photoval) {
                                        photo_index++;
                                        $('#summits-list').append('<div class="summit-card-photo-' + photo_index + ' col-lg-3 col-md-3 col-sm-4 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                    });
                                    $('#summits-list').append('<div class="' + twoPhotoMapClass + '" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png' %}"></div>' + statsOverlayText + '</div>');
                                } else {
                                    $.each(summitval.photos, function (photokey, photoval) {
                                        photo_index++;
                                        $('#summits-list').append('<div class="summit-card-photo-' + photo_index + ' col-lg-3 col-md-3 col-sm-3 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                        if (photo_index > 3) {
                                            return false;
                                        }
                                    });
                                    if (photo_index > 3) {
                                        $('#summits-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 map-card-web-4 map-card-tablet-4 map-card-mobile-4" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png' %}"></div>' + statsOverlayText + '</div>');
                                    } else {
                                        $('#summits-list').append('<div class="col-lg-3 col-md-3 col-sm-3 col-xs-6 map-card-web-4 map-card-tablet-4 map-card-mobile-4" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png' %}"></div>' + statsOverlayText + '</div>');
                                    }
                                }
                            //if log has peak AND GPX
                            } else {

                                if (photo_arr.length == 0) {
                                    $('#summits-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" id="map-' + summitval.id + '" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden; aspect-ratio:16/3;"><div><img class="img-responsive peakeryPhoto photography peakimg-responsive" src="{% static 'img/spacer.png' %}"></div>' + statsOverlayText + '</div>');
                                } else if (photo_arr.length == 1) {
                                    $.each(summitval.photos, function (photokey, photoval) {
                                        photo_index++;
                                        $('#summits-list').append('<div class="summit-card-photo-' + photo_index + ' col-lg-3 col-md-3 col-sm-4 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                    });
                                    $('#summits-list').append('<div class="col-lg-9 col-md-9 col-sm-8 col-xs-6 map-card-web-1 map-card-tablet-1 map-card-mobile-1" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png' %}"></div>' + statsOverlayText + '</div>');
                                } else if (photo_arr.length == 2) {
                                    $.each(summitval.photos, function (photokey, photoval) {
                                        photo_index++;
                                        $('#summits-list').append('<div class="summit-card-photo-' + photo_index + ' col-lg-3 col-md-3 col-sm-4 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                    });
                                    $('#summits-list').append('<div class="' + twoPhotoMapClass + '" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png' %}"></div>' + statsOverlayText + '</div>');
                                } else {
                                    $.each(summitval.photos, function (photokey, photoval) {
                                        photo_index++;
                                        $('#summits-list').append('<div class="summit-card-photo-' + photo_index + ' col-lg-3 col-md-3 col-sm-3 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                        if (photo_index > 3) {
                                            return false;
                                        }
                                    });
                                    if (photo_arr.length == 3) {
                                        $('#summits-list').append('<div class="col-lg-3 col-md-3 col-sm-3 col-xs-6" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png' %}"></div>' + statsOverlayText + '</div>');
                                    } else {
                                    //if 4 or more photos
                                        $('#summits-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 map-card-web-4 map-card-mobile-4 map-card-tablet-4" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png' %}"></div>' + statsOverlayText + '</div>');
                                    }
                                }

                            }
                            $('#summits-list').append(mobileStatsOverlayText);
                        //if log has peak but no GPX
                        }
                        else {
                            if (photo_arr.length == 1) {
                                $.each(summitval.photos, function (photokey, photoval) {
                                    photo_index++;
                                    $('#summits-list').append('<div class="summit-card-photo-' + photo_index + ' col-lg-3 col-md-3 col-sm-4 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                });
                                $('#summits-list').append('<div class="col-lg-9 col-md-9 col-sm-8 col-xs-6 map-card-web-1 map-card-mobile-1 map-card-tablet-1" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png' %}"></div></div>');
                            } else if (photo_arr.length == 2) {
                                $.each(summitval.photos, function (photokey, photoval) {
                                    photo_index++;
                                    $('#summits-list').append('<div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                });
                                $('#summits-list').append('<div class="' + twoPhotoMapClass + '" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png' %}"></div></div>');
                            } else if (photo_arr.length >= 3) {
                                $.each(summitval.photos, function (photokey, photoval) {
                                    photo_index++;
                                    $('#summits-list').append('<div class="summit-card-photo-' + photo_index + ' col-lg-3 col-md-3 col-sm-3 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                    if (photo_index > 3) {
                                        return false;
                                    }
                                });
                                if (staticMapUrl != '') {
                                    if (photo_arr.length == 3) {
                                        $('#summits-list').append('<div class="col-lg-3 col-md-3 col-sm-3 col-xs-6" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png' %}"></div></div>');
                                    } else {
                                        // 4 or more photos
                                        $('#summits-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 map-card-web-4 map-card-mobile-4 map-card-tablet-4" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png' %}"></div></div>');
                                    }
                                }
                            }
                        }

                        //add log
                        if (summitval.log != null) {
                            var log_text = summitval.log.replace(/(?:\r\n|\r|\n)/g, '<br>');
                            if (log_text.length > 9999) {
                                log_text = summitval.log.substring(0, 700)+'...';
                            }
                            $('#summits-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; background-color: #fff; border-bottom: 1px solid #c0c0c0;"><div><p class="summit-log-summary">' + log_text + '</p></div></div>');
                        }
                        //footer
                        if (summitval.liked == '1') {
                            classLiked = 'liked';
                        } else {
                            classLiked = '';
                        }
                        if (summitval.log != null || (summitval.gpx_file != 'None' && summitval.gpx_file != '') || photo_arr.length > 0 || summitval.peak_thumbnail_url != 'images/img/cache/default.png.350x245_q95_crop.jpg') {
                            $('#summits-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');"><div class="row"><div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="height: 70px; background-color: #fff; z-index: 1; border-bottom-left-radius: 8px; border-bottom-right-radius: 8px; box-shadow: 0px 10px 10px rgba(0, 0, 0, 1);"><div><p><span class="summit-list-footer" style="font-weight: 600; line-height: 70px;"><a href="/' + summitval.peak_slug + '/summits/' + summitval.id + '/"><button style="padding-left: 13px; width: 100%; height: 100%; text-align: left; border: none; color: #00B1F2; font-weight: 600; position: absolute; left: -2px; top: -2px; z-index: 1; border-bottom-left-radius: 8px; border-bottom-right-radius: 8px;" class="btn btn-default btn-see-full-log">SEE FULL LOG</button></a></span><span class="summit-list-footer pull-right" style="margin-top: 8px; position: absolute; right: 30px; z-index: 2;"><span style="margin-left: 40px;"><a href="javascript:like_summit(' + summitval.id + ');" class="like_summit{{ is_authenticated }}" id="summitlog-with-' + summitval.id + '"><span class="' + classLiked + '" id="summitlog-like-' + summitval.id + '" style="width: 32px; height: 32px; display: inline-block; color: #ffffff; line-height: 32px; text-align: center; padding-left: 2px; font-size: 10px; font-weight: bold; background-size: 100%; background-image: url({% static 'img/fa-heart_256_0_00b1f2_none.png'%});">' + favorites_count + '</span></a></span><span style="margin-left: 40px;"><a href="/' + summitval.peak_slug + '/summits/' + summitval.id + '/"><span style="width: 32px; height: 32px; display: inline-block; color: #ffffff; line-height: 30px; text-align: center; padding-left: 3px; font-size: 10px; font-weight: bold; background-size: 100%; background-image: url({% static 'img/fa-comment_256_0_00b1f2_none.png'%});">' + comments_count + '</span></a></span><span style="margin-left: 40px; ' + photos_style + '"><a href="/' + summitval.peak_slug + '/summits/' + summitval.id + '/"><span style="width: 32px; height: 32px; display: inline-block; color: #ffffff; line-height: 36px; text-align: center; padding-left: 2px; font-size: 10px; font-weight: bold; background-size: 100%; background-image: url({% static 'img/fa-camera_256_0_00b1f2_none.png'%});">' + photos_count + '</span></a></span></span></p><span class="peak-list-highlights"><p></p></span></div></div></div>');
                        }
                        $('#summits-list').append('</div>');

                        if (summitval.gpx_mapbox_thumbnail != '') {
                            $("#map-"+summitval.id).css("background-image", "url('" + summitval.gpx_mapbox_thumbnail + "')");
                        }

                        //tooltips
                        $('.addl-summits-stats').tooltip();

                        counter++;

                    });
                    //show pagination
                    var paginationHtml = '';
                    if (parseInt(page) == totalPages && totalPages > 1) {
                        paginationHtml = '<div style="display: inline-block; height: 55px;"><a onclick="loadSummits(\''+summitYear+'\', \''+summitRegion+'\', \''+summitSort+'\', ' + (parseInt(page)-1).toString() + ');"><img src="{% static 'img/font-awesome_4-7-0_arrow-circle-left_256_0_00b1f2_none.png' %}" style="height: 26px; margin-bottom: 5px;"></a></div><div class="current" style="margin-left: 20px; margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">summits ' + ((parseInt(page)-1)*25+1).toString() + ' - ' + numberWithCommas(totalSummits) + ' of ' + numberWithCommas(totalSummits) + '</div>';
                    } else if (parseInt(page) > 1) {
                        paginationHtml = '<div style="display: inline-block; height: 55px;"><a onclick="loadSummits(\''+summitYear+'\', \''+summitRegion+'\', \''+summitSort+'\', ' + (parseInt(page)-1).toString() + ');"><img src="{% static 'img/font-awesome_4-7-0_arrow-circle-left_256_0_00b1f2_none.png' %}" style="height: 26px; margin-bottom: 5px;"></a></div><div class="current" style="margin-left: 20px; margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">summits ' + ((parseInt(page)-1)*25+1).toString() + ' - ' + ((parseInt(page))*25).toString() + ' of ' + numberWithCommas(totalSummits) + '</div>';
                    } else if (totalPages > 1) {
                        paginationHtml = '<div class="current" style="margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">summits 1 - 25 of ' + numberWithCommas(totalSummits) + '</div>';
                    } else {
                        paginationHtml = '<div class="current" style="margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">summits 1 - ' + numberWithCommas(totalSummits) + ' of ' + numberWithCommas(totalSummits) + '</div>';
                    }
                    if (parseInt(page) < totalPages) {
                        paginationHtml = paginationHtml + '<div style="display: inline-block; height: 55px;"><a onclick="loadSummits(\''+summitYear+'\', \''+summitRegion+'\', \''+summitSort+'\', ' + (parseInt(page)+1).toString() + ');"><img src="{% static 'img/font-awesome_4-7-0_arrow-circle-right_256_0_00b1f2_none.png'%}" style="height: 26px; margin-bottom: 5px;"></i></a></div>';
                        $('#pagination-pages').html(paginationHtml);
                        $('#pagination').css('display', 'inherit');
                    } else {
                        $('#pagination-pages').html(paginationHtml);
                        $('#pagination').css('display', 'inherit');
                    }

                }
            });

            $("time.timeago").timeago();
            $('#ajax-data-loading').css('display', 'none');
            loading = false;
        });

    }

    function numberWithCommas(x) {
        return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    function change_like_count(id,count){
        $('#summitlog-like-'+id).html(count+" like");
    }

    {% if user.is_authenticated %}
    function like_summit(id) {
        var likeButton = $('#summitlog-like-'+id);
        var summitID = id;
        var summitlogID = '18';
        if (likeButton.hasClass("login")){
            $.facebox( {ajax:'/accounts/login/?next=/accounts/login_reload/login/'});
        } else {
            if (likeButton.hasClass("liked")) {
                likeButton.removeClass("liked");
                likeButton.addClass("liking");
                var count = $('#summitlog-like-'+summitID).html();
                count = parseInt(count)-1;
                if (count > 0) {
                    $('#summitlog-like-'+summitID).html(count);
                } else {
                    $('#summitlog-like-'+summitID).html('&nbsp;');
                }
                var request = $.ajax({
                  method: "POST",
                  url: "/favorites/remove/",
                  dataType: "json",
                  data: {object_id: summitID, content_type_id: summitlogID}
                });
                request.done(function( data ) {
                  //$('#summitlog-like-'+summitID).html(data.count);
                  likeButton.removeClass("liking");
                });
            } else {
                likeButton.addClass("liked");
                likeButton.addClass("liking");
                var count = $('#summitlog-like-'+summitID).html();
                if (count == '&nbsp;') {
                    count = 1;
                } else {
                    count = parseInt(count)+1;
                }
                $('#summitlog-like-'+summitID).html(count);
                var request = $.ajax({
                  method: "POST",
                  url: "/favorites/add/",
                  dataType: "json",
                  data: {object_id: summitID, content_type_id: summitlogID}
                });
                request.done(function( data ) {
                  //$('#summitlog-like-'+summitID).html(data.count);
                  likeButton.removeClass("liking");
                });
            }
        }
        return false;
    }
    {% else %}
    function like_summit(id) {
        $('#accounts-login').modal('show');
    }
    {% endif %}

    function get_gpx_distance(lat1, lon1, lat2, lon2) {
        var radlat1 = Math.PI * lat1/180
        var radlat2 = Math.PI * lat2/180
        var theta = lon1-lon2
        var radtheta = Math.PI * theta/180
        var dist = Math.sin(radlat1) * Math.sin(radlat2) + Math.cos(radlat1) * Math.cos(radlat2) * Math.cos(radtheta);
        dist = Math.acos(dist)
        dist = dist * 180/Math.PI
        dist = dist * 60 * 1.1515
        return dist
    }

    function viewSummit(url) {
        window.location.href = url;
    }

    function highlightPhotos(summitid) {
        $('.summitimg-'+summitid).addClass('hover-photos-hover');
    }

    function unhighlightPhotos(summitid) {
        $('.summitimg-'+summitid).removeClass('hover-photos-hover');
    }

    function init() {

        var vars = [], hash, year, region, sort, page;
        var q = document.URL.split('#')[1];
        if(q != undefined){
            q = q.split('&');
            for(var i = 0; i < q.length; i++){
                hash = q[i].split('=');
                vars.push(hash[1]);
                vars[hash[0]] = hash[1];
            }
        }

        if (vars['year'] != undefined) {
            year = vars['year'];
            if (year != '') {
                $("#timefilter-title").html(year);
                $("#mobile-timefilter-title").html(year);
            }
        } else {
            year = '';
        }

        if (vars['region'] != undefined) {
            region = vars['region'];
            if (region != '') {
                var region_html = $('.regionfilter-item[data-value='+region+']').html();
                var region_name = region_html.split('\u2022');
                $("#regionfilter-title").html(region_name[0]);
                $("#mobile-regionfilter-title").html(region_name[0]);
            }
        } else {
            region = '';
        }

        if (vars['order'] != undefined) {
            sort = vars['order'];
        } else {
            sort = 'all';
        }

        if (vars['page'] != undefined) {
            page = vars['page'];
        } else {
            page = '1';
        }

        loadSummits(year, region, sort, page);

    }

    $(document).ready(function() {

        {% if summit_badges %}

        $("#modal-0").addClass("md-show");
        $('.md-overlay').show();
        $('#content-holder').css({"filter" : "blur(15px)"});

        $('.md-overlay').on('click', function(e) {
            {% for b in summit_badges %}
            {% if forloop.counter0 > 0 %}else{% endif %} if ($("#modal-{{ forloop.counter }}").hasClass('md-modal') && $("#modal-{{ forloop.counter0 }}").hasClass('md-show')) {
                $("#modal-{{ forloop.counter0 }}").removeClass("md-effect-1").addClass("md-effect-11").removeClass("md-show");
                setTimeout(function () {
                    $("#modal-{{ forloop.counter }}").addClass("md-show");
                }, 300);
            }
            {% endfor %}
        });

        $('.md-modal').on('click', function(e) {
            {% for b in summit_badges %}
            {% if forloop.counter0 > 0 %}else{% endif %} if ($("#modal-{{ forloop.counter }}").hasClass('md-modal') && $("#modal-{{ forloop.counter0 }}").hasClass('md-show')) {
                $("#modal-{{ forloop.counter0 }}").removeClass("md-effect-1").addClass("md-effect-11").removeClass("md-show");
                setTimeout(function () {
                    $("#modal-{{ forloop.counter }}").addClass("md-show");
                }, 300);
            }
            {% endfor %}
        });

        $('#modal-{{ last_badge_index }}').on('click', function(e) {
            $(".md-modal").removeClass("md-show");
            $('.md-overlay').hide();
            $('#content-holder').css({"filter" : "none"});
        });

        $('.md-overlay').on('click', function(e) {
            if ($('#modal-{{ last_badge_index }}').hasClass('md-show')) {
                $(".md-modal").removeClass("md-show");
                $('.md-overlay').hide();
                $('#content-holder').css({"filter" : "none"});
            }
        });

        {% endif %}

        $('.slide').click(function(){
            var pos = splash_gallery.getIndex();
            var count = splash_gallery.getNumber();
            if (pos == count-1) {
                splash_gallery.close();
            } else {
                splash_gallery.next();
            }
            return false;
        });

        $('#mobileTimefilterSelect option:selected').text($('#mobileTimefilterSelect option:selected').text()+' \u25BE');
        $('#mobileRegionfilterSelect option:selected').text($('#mobileRegionfilterSelect option:selected').text()+' \u25BE');

        var viewport_height = $(window).height();
        var viewport_width = $(window).width();
        var footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
        var data_loading_margin = (viewport_height - footer_height) / 3;
        $('.data-loading').css('margin-top', data_loading_margin);
        $(window).resize(function() {
            viewport_height = $(window).height();
            viewport_width = $(window).width();
            footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
            data_loading_margin = (viewport_height - footer_height) / 3;
            $('.data-loading').css('margin-top', data_loading_margin);
        });

        $('.timefilter-list').on('click', '.timefilter-item', function(e) {

            e.preventDefault();
            $("#timefilter-title").html($(this).html());
            $("#mobile-timefilter-title").html($(this).html());
            summitYear = $(this).data('value');
            loadSummits(summitYear, summitRegion, summitSort, '1');

        });

        $('.mobile-timefilter-list').on('click', '.timefilter-item', function(e) {

            e.preventDefault();
            $("#timefilter-title").html($(this).html());
            $("#mobile-timefilter-title").html($(this).html());
            summitYear = $(this).data('value');
            loadSummits(summitYear, summitRegion, summitSort, '1');

        });

        $('#mobileTimefilterSelect').on('change', function() {

            $("#timefilter-title").html($(this).val());
            $('#mobileTimefilterSelect option').each( function( index, element ){
                $(this).text($(this).text().replace(' \u25BE',''));
            });
            $('#mobileTimefilterSelect option:selected').text($('#mobileTimefilterSelect option:selected').text()+' \u25BE');
            summitYear = $(this).val();
            loadSummits(summitYear, summitRegion, summitSort, '1');

        });

        $('.regionfilter-item').on('click', function(e) {

            e.preventDefault();
            var region_html = $(this).html();
            var region_name = region_html.split('\u2022');
            $("#regionfilter-title").html(region_name[0]);
            $("#mobile-regionfilter-title").html(region_name[0]);
            summitRegion = $(this).data('value');
            //reset years filter
            $("#timefilter-title").html('all years');
            $("#mobile-timefilter-title").html('All years');
            summitYear = '';
            loadSummits(summitYear, summitRegion, summitSort, '1');

        });

        $('.mobile-regionfilter-item').on('click', function(e) {

            e.preventDefault();
            var region_html = $(this).html();
            var region_name = region_html.split('\u2022');
            $("#regionfilter-title").html(region_name[0]);
            $("#mobile-regionfilter-title").html(region_name[0]);
            summitRegion = $(this).data('value');
            //reset years filter
            $("#timefilter-title").html('all years');
            $("#mobile-timefilter-title").html('All years');
            summitYear = '';
            loadSummits(summitYear, summitRegion, summitSort, '1');

        });

        $('#mobileRegionfilterSelect').on('change', function() {

            $("#regionfilter-title").html($( "#mobileRegionfilterSelect option:selected" ).text());
            $('#mobileRegionfilterSelect option').each( function( index, element ){
                $(this).text($(this).text().replace(' \u25BE',''));
            });
            $('#mobileRegionfilterSelect option:selected').text($('#mobileRegionfilterSelect option:selected').text()+' \u25BE');
            summitRegion = $( "#mobileRegionfilterSelect option:selected").val();
            //reset years filter
            $("#timefilter-title").html('all years');
            $("#mobile-timefilter-title").html('All years');
            $('#mobileTimefilterSelect').val('all');
            summitYear = '';
            loadSummits(summitYear, summitRegion, summitSort, '1');

        });

        $('#unfollow-{{ bagger.id }}').hover(
        function(){
            $(this).removeClass('unfollowButton').addClass('unfollowButtonHoverProfile').text('Unfollow').css('background-color','#f24100').css('border-color','#f24100');
        },
        function(){
            $(this).removeClass('unfollowButtonHoverProfile').addClass('unfollowButton').text('You are following').css('background-color','#00b330').css('border-color','#00b330');
        });

        $("#unfollow-{{ bagger.id }}").on('click',function (){
            var btn = $("#unfollow-{{ bagger.id }}");
            var follow_btn = $("#follow-{{ bagger.id }}");
            btn.html('<i class="fa fa-spinner fa-spin"></i>');
            $.post('/accounts/unfollow/{{ bagger.id }}/',function(data){
                if (data == "True"){
                    follow_btn.html('Follow');
                    $("#unfollow-{{ bagger.id }}").hide();
                    $("#follow-{{ bagger.id }}").show();
                    $("#mobile-unfollow-{{ bagger.id }}").hide();
                    $("#mobile-follow-{{ bagger.id }}").show();
                }
            });
        });

        $("#follow-{{ bagger.id }}").on('click',function (){
            var btn = $("#follow-{{ bagger.id }}");
            var unfollow_btn = $("#unfollow-{{ bagger.id }}");
            btn.html('<i class="fa fa-spinner fa-spin"></i>');
            $.post('/accounts/follow/{{ bagger.id }}/',function(data){
                if (data == "True"){
                    unfollow_btn.html('You are following');
                    $("#follow-{{ bagger.id }}").hide();
                    $("#unfollow-{{ bagger.id }}").show();
                    $("#mobile-follow-{{ bagger.id }}").hide();
                    $("#mobile-unfollow-{{ bagger.id }}").show();
                }
            });
        });

        $("#mobile-unfollow-{{ bagger.id }}").on('click',function (){
            var btn = $("#mobile-unfollow-{{ bagger.id }}");
            var follow_btn = $("#mobile-follow-{{ bagger.id }}");
            btn.html('<i class="fa fa-spinner fa-spin"></i>');
            $.post('/accounts/unfollow/{{ bagger.id }}/',function(data){
                if (data == "True"){
                    follow_btn.html('follow');
                    $("#mobile-unfollow-{{ bagger.id }}").hide();
                    $("#mobile-follow-{{ bagger.id }}").show();
                    $("#unfollow-{{ bagger.id }}").hide();
                    $("#follow-{{ bagger.id }}").show();
                }
            });
        });

        $("#mobile-follow-{{ bagger.id }}").on('click',function (){
            var btn = $("#mobile-follow-{{ bagger.id }}");
            var unfollow_btn = $("#mobile-unfollow-{{ bagger.id }}");
            btn.html('<i class="fa fa-spinner fa-spin"></i>');
            $.post('/accounts/follow/{{ bagger.id }}/',function(data){
                if (data == "True"){
                    unfollow_btn.html('following');
                    $("#mobile-follow-{{ bagger.id }}").hide();
                    $("#mobile-unfollow-{{ bagger.id }}").show();
                    $("#follow-{{ bagger.id }}").hide();
                    $("#unfollow-{{ bagger.id }}").show();
                }
            });
        });

        //create trigger to resizeEnd event
        $(window).resize(function() {
            if(this.resizeTO) clearTimeout(this.resizeTO);
            this.resizeTO = setTimeout(function() {
                $(this).trigger('resizeEnd');
            }, 500);
        });

        //redraw graph when window resize is completed
        $(window).on('resizeEnd', function() {
            if (loading == false) {
                drawAnnotations(chartData);
            }
        });

    });

</script>

{% endblock %}

{% block gallery %}
{% for b in summit_badges %}
    {% if b.type == 'summary' %}
        <div class="md-modal md-effect-1" id="modal-{{ forloop.counter0 }}">
            <div class="md-content">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="summit_badges">
                        <div class="badge-li">
                            <div class="thumb splash-grow-badge hover-photos" style="background: url({{ b.peak_thumbnail }}) 0% 0% / cover no-repeat; transform: scale(1); transition: 0.4s ease;">
                                <div class="peak splash-grow-badge-info">
                                    <span class="item_name ellipsis">{{ b.peak_name }}</span>
                                    <span class="item_info">{{ b.peak_elevation }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="badge-data">
                        <div class="badge-data-top">Your <span style="color: #f24100; font-weight: 500;">{{ b.summit_count|ordinal }}</span> summit of</div>
                        <div class="badge-data-middle">{{ b.peak_name }}</div>
                        {% if b.summits_this_year_count > 0 %}
                        <div class="badge-data-bottom"><span style="color: #f24100; font-weight: 500;">{{ b.summits_this_year_count|ordinal }}</span> member to summit in {{ b.summit_date|date:"Y" }}</div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    {% elif b.type == 'first_ascent' %}
        <div class="md-modal md-effect-1" id="modal-{{ forloop.counter0 }}">
            <div class="md-content">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <ul class="summit_badges" style="text-align: center;">
                        <li class="badge-icon" style="float: none; display: inline-block;">
                            <img src="{% static 'img/splash_badge_first_ascent.png' %}">
                        </li>
                    </ul>
                    <div class="badge-icon-data">
                        <div class="badge-data-top">You snagged the {{ b.peak_name }}</div>
                        <div class="badge-data-middle"><span style="color: #ff0000;">First Ascent</span></div>
                        <div class="badge-data-bottom">You're the first to log this summit. Now it will forever bear your name.</div>
                    </div>
                </div>
            </div>
        </div>
    {% elif b.type == 'summit_steward' %}
        <div class="md-modal md-effect-1" id="modal-{{ forloop.counter0 }}">
            <div class="md-content">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <ul class="summit_badges" style="text-align: center;">
                        <li class="badge-icon-steward" style="float: none; display: inline-block;">
                            <img src="{% static 'img/summit-steward-badge.png' %}">
                        </li>
                    </ul>
                    <div class="badge-steward-data">
                        <div class="badge-data-top">You're now {{ b.peak_name|an }} {{ b.peak_name }}</div>
                        <div class="badge-data-middle"><span style="color: #00b1f2;">Summit Steward</span></div>
                        <div class="badge-data-bottom">Your <span style="color: #00b1f2; font-weight: 500;">5 summits</span> make you the {{ b.steward_count|ordinal }} Summit Steward of {{ b.peak_name }}</div>
                    </div>
                </div>
            </div>
        </div>
    {% elif b.type == 'king_of_the_mountain' %}
        <div class="md-modal md-effect-1" id="modal-{{ forloop.counter0 }}">
            <div class="md-content">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <ul class="summit_badges" style="text-align: center;">
                        <li class="badge-icon" style="float: none; display: inline-block">
                            <img src="{% static 'img/splash_badge_kom.png' %}">
                        </li>
                    </ul>
                    <div class="badge-icon-data">
                        <div class="badge-data-top">You're now the {{ b.peak_name }}</div>
                        <div class="badge-data-middle"><span style="color: #CFB53B;">King of the Mountain</span></div>
                        <div class="badge-data-bottom">You've summited this peak more times than any other member.</div>
                    </div>
                </div>
            </div>
        </div>
    {% elif b.type == '50_peaks' %}
        <div class="md-modal md-effect-1" id="modal-{{ forloop.counter0 }}">
            <div class="md-content">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <ul class="summit_badges" style="text-align: center;">
                        <li class="badge-icon" style="float: none; display: inline-block;">
                            <img src="{% static 'img/big-peak.png' %}">
                            <div class="badge-peak-count" style="background-color:#CD7F32;">
                                <span>50 peaks</span>
                            </div>
                        </li>
                    </ul>
                    <div class="badge-data-count">
                        <div class="badge-data-top">Nice, you've now climbed</div>
                        <div class="badge-data-middle">50 peaks</div>
                        <div class="badge-data-bottom">This puts you in the <span style="color: #f24100; font-weight: 500;">top 7%</span> of peakery members.</div>
                    </div>
                </div>
            </div>
        </div>
    {% elif b.type == '100_peaks' %}
        <div class="md-modal md-effect-1" id="modal-{{ forloop.counter0 }}">
            <div class="md-content">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <ul class="summit_badges" style="text-align: center;">
                        <li class="badge-icon" style="float: none; display: inline-block;">
                            <img src="{% static 'img/big-peak.png' %}">
                            <div class="badge-peak-count" style="background-color:#C0C0C0;">
                                <span>100 peaks</span>
                            </div>
                        </li>
                    </ul>
                    <div class="badge-data-count">
                        <div class="badge-data-top">Great work, you've now climbed</div>
                        <div class="badge-data-middle">100 peaks</div>
                        <div class="badge-data-bottom">This puts you in the <span style="color: #f24100; font-weight: 500;">top 3%</span> of peakery members.</div>
                    </div>
                </div>
            </div>
        </div>
    {% elif b.type == '200_peaks' %}
        <div class="md-modal md-effect-1" id="modal-{{ forloop.counter0 }}">
            <div class="md-content">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <ul class="summit_badges" style="text-align: center;">
                        <li class="badge-icon" style="float: none; display: inline-block;">
                            <img src="{% static 'img/big-peak.png' %}">
                            <div class="badge-peak-count" style="background-color:#CFB53B;">
                                <span>200 peaks</span>
                            </div>
                        </li>
                    </ul>
                    <div class="badge-data-count">
                        <div class="badge-data-top">Whoa, you've now climbed</div>
                        <div class="badge-data-middle">200 peaks</div>
                        <div class="badge-data-bottom">This puts you in the <span style="color: #00b1f2; font-weight: 500;">top 2%</span> of peakery members.</div>
                    </div>
                </div>
            </div>
        </div>
    {% elif b.type == 'elevation_pr' %}
        <div class="md-modal md-effect-1" id="modal-{{ forloop.counter0 }}">
            <div class="md-content">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <div class="summit_badges">
                        <div class="badge-li">
                            <div class="thumb splash-grow-badge hover-photos" style="background: url({{ b.peak_thumbnail }}) 0% 0% / cover no-repeat; transform: scale(1); transition: 0.4s ease;">
                                <div class="peak splash-grow-badge-info">
                                    <span class="item_name ellipsis">{{ b.peak_name }}</span>
                                    <span class="item_info">{{ b.peak_elevation }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="badge-data">
                        <div class="badge-data-top">You set a personal record for</div>
                        <div class="badge-data-middle">Max Elevation</div>
                        <div class="badge-data-bottom">Higher than your old record <span style="color: #f24100; font-weight: 500;">{{ b.previous_record_peak }}</span> by <span style="color: #f24100; font-weight: 500;">{{ b.new_record_delta }} ft / {{ b.new_record_delta_meters }} m</span></div>
                    </div>
                </div>
            </div>
        </div>
    {% elif b.type == 'challenge' %}
        <div class="md-modal md-effect-1" id="modal-{{ forloop.counter0 }}">
            <div class="md-content">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    {% if b.summited_peaks == b.total_peaks %}
                    <div class="summit_badges">
                        <div class="badge-li">
                            <div class="thumb splash-grow-badge hover-photos challenge-badge challenge-badge-completed" style="background: url({{ MEDIA_URL }}{{ b.thumbnail }}) 0% 0% / cover no-repeat; transform: scale(1); transition: 0.4s ease;"></div>
                        </div>
                    </div>
                    <div class="badge-data-challenge">
                        <div class="badge-data-top">Congrats! You finished the</div>
                        <div class="badge-data-middle">{{ b.name }}</div>
                        <div class="progress progress-complete">
                            <div class="progress-bar progress-bar-blue challenge-badge-completed-bar" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: {{ b.completion_pct }}%;">
                                <span class="challenge-show">{{ b.summited_peaks }} out of {{ b.total_peaks }} peaks ({{ b.completion_pct|floatformat:"0" }}%)</span>
                            </div>
                        </div>
                        <div class="badge-data-bottom">You're now one of <span class="challenge-badge-completed-text">{{ b.finisher_count }} members</span> who've completed this challenge</div>
                    </div>
                    {% else %}
                    <div class="summit_badges">
                        <div class="badge-li">
                            <div class="thumb splash-grow-badge hover-photos challenge-badge" style="background: url({{ MEDIA_URL }}{{ b.thumbnail }}) 0% 0% / cover no-repeat; transform: scale(1); transition: 0.4s ease;"></div>
                        </div>
                    </div>
                    <div class="badge-data-challenge">
                        <div class="badge-data-top">Your <span style="color: #f24100; font-weight: 500;">{{ b.summited_peaks|ordinal }}</span> peak in the</div>
                        <div class="badge-data-middle">{{ b.name }}</div>
                        <div class="progress progress-incomplete">
                            <div class="progress-bar progress-bar-blue" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: {{ b.completion_pct }}%;">
                                <span class="challenge-show">{{ b.summited_peaks }} out of {{ b.total_peaks }} peaks ({{ b.completion_pct|floatformat:"0" }}%)</span>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    {% endif %}
{% endfor %}

{% endblock %}