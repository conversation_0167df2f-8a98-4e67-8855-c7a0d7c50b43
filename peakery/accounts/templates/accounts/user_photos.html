{% extends "base.html" %}
{% load static %}
{% load avatar_tags %}
{% load item_tags %}
{% load humanize %}

{% block title %}{{ bagger }}'s photos{% endblock %}
{% block titlemeta %}{{ bagger }}'s photos{% endblock %}
{% block description %}Collection of {{ bagger }}'s {{ peak_photo_count }} mountain photo{{ peak_photo_count|pluralize:"s" }}{% endblock %}

{% block css %}
    <link href="{{MEDIA_URL}}css/jquery.lightbox-0.5.css" rel="stylesheet" type="text/css" media="screen" />
{% endblock %}

{% block mobile_header_follow %}
{% if user.is_authenticated %}{% if user.username|stringformat:"s" != bagger|stringformat:"s" %}<a href="javascript:void(0);" id="mobile-follow-{{ bagger.id }}" style="font-size: 11px; font-weight: 300; margin-left: 15px; {% if following %}display: none;{% else %}display: inline;{% endif %}">follow</a><a href="javascript:void(0);" id="mobile-unfollow-{{ bagger.id }}" style="font-size: 11px; font-weight: 300; margin-left: 15px; color: #00b330; {% if following %}display: inline;{% else %}display: none;{% endif %}">following</a>{% else %}<a href="/members/{{ bagger }}/edit" style="font-size: 11px; font-weight: 300; margin-left: 15px; {% if following %}display: none;{% else %}display: inline;{% endif %}">edit</a>{% endif %}{% endif %}
{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row hidden-xs">
        <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 65px; border-radius: 12px 12px 0 0;"><span style="font-size: 24px; font-weight: 600;">
            <div id="breadcrumbs" style="margin-left: -5px;">
                <ul>
                    <li style="margin-top: 2px;"><a class="modal-link" data-toggle="modal" data-target="#avatar-modal"><img style="height: 50px; width: 50px; border-radius:10px;" src="{{ MEDIA_URL }}{{ avatar.avatar_url|urlencode }}"></a><div style="float: right; margin-left: 15px; font-weight: 600; font-size: 20px;">{{ bagger }}{% if user.is_authenticated %}{% if user.username|stringformat:"s" == bagger|stringformat:"s" %}<a class="profile-edit-link hidden-xs" style="font-size: 12px; margin-left: 10px;" href="/members/{{ bagger }}/edit">edit</a>{% endif %}{% endif %}</div></li>
                </ul>
                <div class="pull-right hidden-xs" style="margin-top: -5px;">
                    <a style="{{ subnav_info_style }}" class="region-header-sub-links" href="/members/{{ bagger }}/">Info</a><a style="{{ subnav_map_style }}" class="region-header-sub-links" href="/members/{{ bagger }}/map/">Map</a><a style="{{ subnav_badges_style }}" class="region-header-sub-links" href="/members/{{ bagger }}/badges/">Badges</a><a style="{{ subnav_summits_style }}" class="region-header-sub-links" href="/members/{{ bagger }}/summits/">Climbs</a><a style="{{ subnav_challenges_style }}" class="region-header-sub-links" href="/members/{{ bagger }}/challenges/">Challenges</a><a style="{{ subnav_photos_style }}" class="region-header-sub-links" href="/members/{{ bagger }}/photos/">Photos</a>{% if user.is_authenticated %}{% if user.username|stringformat:"s" != bagger|stringformat:"s" %}<div class="region-header-sub-links hidden-xs hidden-sm" style="float: right; margin-top: 3px;"><a href="javascript:void(0);" id="follow-{{ bagger.id }}" class="peakeryFollowButton followButton btn btn-secondary" style="font-size: 12px; float: right; width: 165px; {% if following %}display: none;{% else %}display: block;{% endif %}">Follow</a><a href="javascript:void(0);" id="unfollow-{{ bagger.id }}" class="peakeryFollowButton btn btn-secondary unfollowButton" style="font-size: 12px; float: right; width: 165px; background-color: rgb(0, 179, 48); border-color: rgb(0, 179, 48); {% if following %}display: block;{% else %}display: none;{% endif %}">You are following</a></div>{% endif %}{% endif %}
                </div>
            </div>
        </span></div>
    </div>
{% endblock %}

{% block content %}
    <style type="text/css">

        @media screen and (max-width: 767px) and (min-width: 1px) {
           #content-body {
               margin-top: 20px;
           }
           .content-pane {
               margin-top: 99px;
           }
           .filter-bar {
               top: 120px;
           }
           .mobile-regions-subnav-fixed {
               margin-top: 50px;
           }
           .peakeryFollowButton {
                font-size: 10px !important;
                width: 130px !important;
            }
           .hero-photo-left {
                border-bottom: solid 2px #e0e0e0;
            }
            .hero-photo-right {
                border-bottom: solid 2px #e0e0e0;
            }
       }
       @media screen and (max-width: 1023px) and (min-width: 768px) {
           .content-pane {
               margin-top: 49px;
           }
           .mobile-regions-subnav-fixed {
               margin-top: 0px;
           }
           .hero-photo-left {
                border-bottom: solid 2px #e0e0e0;
            }
            .hero-photo-right {
                border-bottom: solid 2px #e0e0e0;
            }
            .filter-bar {
                top: 141px;
            }
       }
        @media screen and (min-width: 1024px) {
            .content-pane {
               margin-top: 49px;
           }
            .mobile-regions-subnav-fixed {
               margin-top: 0px;
           }
            .regions-subnav-fixed {
                top: 141px;
            }
            .hero-photo-left {
                border-bottom: solid 2px #e0e0e0;
                border-right: solid 1px #e0e0e0;
            }
            .hero-photo-right {
                border-bottom: solid 2px #e0e0e0;
                border-left: solid 1px #e0e0e0;
            }
        }

        .blueimp-gallery > .description {
          position: absolute;
          bottom: 0px;
          width: 100%;
          text-align: center;
          color: #fff;
          margin-bottom: 2%;
          height: auto;
          display: none;
        }
        .blueimp-gallery-controls > .description {
          display: block;
        }
        .blueimp-gallery-controls > .description > .description-text {
            padding: 10px;
            background: rgba(0, 0, 0, 0.5);
        }
        .content-pane {
            border-bottom: none;
        }
    </style>

<div class="container">

    <!-- Mobile header -->
    <div class="row sub-header-row hidden-lg hidden-md hidden-sm" style="height: 50px; position: fixed; width: 100%; z-index: 2; top: 70px;">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999; display: flex; justify-content: space-between;">
            <a style="{{ subnav_info_style }}" class="mobile-header-sub-links" href="/members/{{ bagger }}/">Info</a>{% if IS_MOBILE_APP_ACCESS != 'True' %}<a style="{{ subnav_map_style }}" class="mobile-header-sub-links" href="/members/{{ bagger }}/map/">Map</a>{% endif %}<a style="{{ subnav_badges_style }}" class="mobile-header-sub-links" href="/members/{{ bagger }}/badges/">Badges</a><a style="{{ subnav_summits_style }}" class="mobile-header-sub-links" href="/members/{{ bagger }}/summits/">Climbs</a><a style="{{ subnav_challenges_style }}" class="mobile-header-sub-links" href="/members/{{ bagger }}/challenges/">Challenges</a><a style="{{ subnav_photos_style }}" class="mobile-header-sub-links" href="/members/{{ bagger }}/photos/">Photos</a>
        </div>
    </div>
    <!-- End mobile header -->

    <div class="row sub-header-row hidden-xs hidden-sm regions-subnav-fixed" style="height: 50px; padding-right: 0px;">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999;">
            <span style="font-size: 14px; float: left;"><span id="photo-count" style="color: #f24100;"></span><span id="photo-count-desc"></span></span>
            <span style="font-size: 14px; float: left;">&nbsp;in</span>
            <span class="select" id="selectTimefilter" style="float: left; margin-top: -3px;">
                <button class="btn btn-default year-filter-button" style="margin-bottom: 0px; background-color: #f2f2f2; border-color: #f2f2f2; padding: 8px 8px; font-size: 15px;" data-toggle="dropdown">
                    <span id="timefilter-title">all years</span>
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu timefilter-list" style="cursor: pointer; height: auto; left: inherit; overflow: scroll; max-height: 70vh;">
                    <li style="float: none;"><a id="timefilter-item-0" class="timefilter-item" data-value="all" data-count="{{ peak_photo_count }}">all years</a></li>
                    {% for y in years %}
                    <li style="float: none;"><a id="timefilter-item-{{ y.year }}" class="timefilter-item" data-value="{{ y.year }}" data-count="{{ y.photos_count }}">{{ y.year }} &bull; {{ y.photos_count }} photo{{ y.photos_count|pluralize:"s" }}</a></li>
                    {% endfor %}
                </ul>
            </span>
        </div>
    </div>

    <div class="row content-pane">

        <div class="col-md-12">
            <div class="row sub-header-row hidden-md hidden-lg filter-bar" style="height: 50px; padding-right: 0px;">
                <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12" style="height: 50px;line-height: 26px;">
                    <div style="float: left; width: 100%; height: 100%;">
                        <div class="select" id="selectMobileTimefilter" style="margin-top: 11px; margin-left: 5px;">
                            <select id="mobileTimefilterSelect" style="width: 96%; -webkit-appearance: none; border: 0; background: none; color: #00b1f2;">
                                <option value="all">All years &bull; {{ peak_photo_count }} photo{{ peak_photo_count|pluralize:"s" }}</option>
                                {% for y in years %}
                                <option value="{{ y.year }}">{{ y.year }} &bull; {{ y.photos_count }} photo{{ y.photos_count|pluralize:"s" }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-12">
            <div class="row" id="ajax-data-loading" style="display: none;">
              <div class="col-md-12 data-loading" style="text-align: center; color: #666;">
                <i class="fa fa-spinner fa-spin fa-5x"></i>
              </div>
            </div>
            <div id="photos-list" class="row"></div>
            <div class="row" id="more-photos-list"></div>
            <div class="row" id="more-ajax-data-loading" style="display: none;">
              <div class="col-md-12" style="text-align: center; color: #666; margin-top: 20px; margin-bottom: 20px;">
                <i class="fa fa-spinner fa-spin fa-5x"></i>
              </div>
            </div>
        </div>
    </div>

    <div class="row" id="photos-footer" style="display: none;">
        <div class="row dark-background-row">
            <div id="see-more-photos-div" style="height: 120px; text-align: center; margin-top: 60px;">
                <button id="see-more-photos" class="btn btn-secondary" style="color: #fff;">See more photos by {{ bagger }}</button>
            </div>
        </div>
    </div>

    <div class="row dark-background-row hidden-lg hidden-md hidden-sm">
        <div class="row bottom-padding">
            <div style="height: 178px;"></div>
        </div>
    </div>
    <div class="row dark-background-row hidden-xs">
        <div class="row bottom-padding">
            <div style="height: 53px;"></div>
        </div>
    </div>

</div>

<script type="text/javascript">

    // Hide mobile filter bar on on scroll down
    var didScroll;
    var lastScrollTop = 0;
    var delta = 5;

    $(window).scroll(function(event){
        didScroll = true;
    });

    document.addEventListener("touchstart", ScrollStart, false);

    setInterval(function() {
        if (didScroll) {
            hasScrolled();
            didScroll = false;
        }
    }, 100);

    function ScrollStart() {
        //start of scroll event for iOS
        hasScrolled();
    }

    function hasScrolled() {
        var st = $(this).scrollTop();

        // Make sure they scroll more than delta
        if (Math.abs(lastScrollTop - st) <= delta)
            return;

        // If they scrolled down and are past the filter bar, add class .filter-scrollup.
        // This is necessary so you never see what is "behind" the navbar.
        if (st > lastScrollTop && st > 50) {
            // Scroll Down
            $('.filter-bar').hide();
        } else {
            // Scroll Up
            if (st + $(window).height() < $(document).height()) {
                $('.filter-bar').show();
            }
        }
        lastScrollTop = st;
    }

    var photos_displayed = 0;
    var photos_page = 1;
    var photos_year = '';
    var photos = [];
    var photoYears = {};
    photoYears["all"] = "{{ peak_photo_count }}";
    {% for y in years %}
    photoYears["{{ y.year }}"] = "{{ y.photos_count }}";
    {% endfor %}

    function htmlentities(str) {
        return String(str).replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;');
    }

    function loadPhotos(year, page) {

        photos_year = year;
        window.location.hash = 'year='+year;

        $('#photos-footer').hide();

        if (page == 1) {
            $('#ajax-data-loading').css('display', 'inline');
            $('#photos-list').empty();
            $('#more-photos-list').empty();
        } else {
            $('#more-ajax-data-loading').css('display', 'inline');
        }
        var photoCount = photoYears[year];
        $('#photo-count').html(photoCount);
        $('#photo-count-desc').html(' photo' + ((photoCount != 1) ? 's' : ''));
        var totalPages = Math.ceil(parseInt(photoCount)/48);
        var photoIndex = 1;

        //$('#mobileTimefilterSelect').find('option').text(function(i,t){
        //    return this.selected ? '&#xf0d7; ' + t.replace('&#xf0d7; ', '') : t.replace('&#xf0d7; ', '');
        //});

        $.getJSON('{% url "user_photos_list" %}?user_id={{ bagger.id }}&year='+year+'&page='+page , function(data) {

            $.each( data, function( key, val ) {
                if (key=='photos') {
                    $.each( val, function( photokey, photoval ) {

                        //Top two hero photos
                        if (photos_displayed < 2) {
                            photoCaptionClass = 'user-photo-info peak-photo-with-caption';

                            //build photos
                            noPhotoSlideshowClass = 'peakimg-responsive';
                            if (photos_displayed == 0) {
                                divClass = 'hero-photo-left col-lg-6 col-md-6 col-sm-12 col-xs-12';
                            } else {
                                divClass = 'hero-photo-right col-lg-6 col-md-6 col-sm-12 col-xs-12 hidden-xs hidden-sm';
                            }
                            photoDivClass = 'top-photos peakimglrg-responsive';

                            //borderClass = 'photo-grid-' + (photoIndex).toString();
                            borderClass = '';

                            slideshowclass = '';
                            hoverclass = 'hover-photos';
                            photoHtml = '<img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive">';

                            var photoCaption = photoval.caption;
                            if (photoCaption == 'None' || photoCaption == null) {
                                photoCaption = '';
                            }

                            //photo date
                            var display_date = $.format.date(photoval.created + 'T00:00:00', 'MMM, d yyyy');

                            $('#photos-list').append('<a class="gallery-link" data-user="' + photoval.username + '" data-credit="" data-summitlogid="' + photoval.summit_log_id + '" data-photodate="' + display_date + '" data-peakname="' + photoval.peak_name + '" data-peakslug="' + photoval.peak_slug + '" data-description="' + htmlentities(photoCaption) + '" data-gallery href="{{ MEDIA_URL }}' + photoval.fullsize_url + '"><div class="' + divClass + ' ' + borderClass + ' hover-photos" style="aspect-ratio:4/3; cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}' + photoval.fullsize_url + '\'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;"><div class="' + photoDivClass + '"><div class="' + hoverclass + '"><div>' + photoHtml + '</div></div><div class="' + photoCaptionClass + '" style="display: none;"><span class="data photo-caption" style="position: absolute; bottom: -5px; left: 10px; color: #fff;"><p class="bagger" style="white-space: normal; font-size: 10px;">' + photoCaption + '</p><p class="bagger" style="font-size: 10px;">' + photoval.peak_name + '&nbsp;&bull;&nbsp;<time class="timeago" datetime="' + photoval.created + 'T00:00:00">' + photoval.created + '</time></p></span></div><div class="static-photo-info" style="display: block;"><span class="data photo-caption" style="position: absolute; bottom: -5px; left: 10px; color: #fff;"><p class="bagger" style="font-size: 10px;">' + photoval.peak_name + '&nbsp;&bull;&nbsp;<time class="timeago" datetime="' + photoval.created + 'T00:00:00">' + photoval.created + '</time></p></span></div></div></div></a>');
                            if (photoIndex == 1) {
                                photoIndex = 4;
                            } else {
                                photoIndex = 1;
                            }
                        } else {

                            photoCaptionClass = 'user-photo-info peak-photo-with-caption';

                            //build photos
                            noPhotoSlideshowClass = 'peakimg-responsive';
                            divClass = 'col-lg-3 col-md-3 col-sm-4 col-xs-6';
                            imgHeight = '240';
                            photoDivClass = 'top-photos';

                            borderClass = 'photo-grid-' + (photoIndex).toString();

                            slideshowclass = '';
                            hoverclass = 'hover-photos';
                            photoHtml = '<img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive">';

                            var photoCaption = photoval.caption;
                            if (photoCaption == 'None' || photoCaption == null) {
                                photoCaption = '';
                            }

                            //photo date
                            var display_date = $.format.date(photoval.created + 'T00:00:00', 'MMM, d yyyy');

                            if (page == 1) {
                                $('#photos-list').append('<a class="gallery-link" data-user="' + photoval.username + '" data-credit="" data-summitlogid="' + photoval.summit_log_id + '" data-photodate="' + display_date + '" data-peakname="' + photoval.peak_name + '" data-peakslug="' + photoval.peak_slug + '" data-description="' + htmlentities(photoCaption) + '" data-gallery href="{{ MEDIA_URL }}' + photoval.fullsize_url + '"><div class="' + divClass + ' ' + borderClass + ' hover-photos" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}' + photoval.thumbnail_url + '\'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;"><div class="' + photoDivClass + '"><div class="' + hoverclass + '"><div>' + photoHtml + '</div></div><div class="' + photoCaptionClass + '" style="display: none;"><span class="data photo-caption" style="position: absolute; bottom: -5px; left: 10px; color: #fff;"><p class="bagger" style="white-space: normal; font-size: 10px;">' + photoCaption + '</p><p class="bagger" style="font-size: 10px;">' + photoval.peak_name + '&nbsp;&bull;&nbsp;<time class="timeago" datetime="' + photoval.created + 'T00:00:00">' + photoval.created + '</time></p></span></div><div class="static-photo-info" style="display: block;"><span class="data photo-caption" style="position: absolute; bottom: -5px; left: 10px; color: #fff;"><p class="bagger" style="font-size: 10px;">' + photoval.peak_name + '&nbsp;&bull;&nbsp;<time class="timeago" datetime="' + photoval.created + 'T00:00:00">' + photoval.created + '</time></p></span></div></div></div></a>');
                            } else {
                                $('#more-photos-list').append('<a class="gallery-link" data-user="' + photoval.username + '" data-credit="" data-summitlogid="' + photoval.summit_log_id + '" data-photodate="' + display_date + '" data-peakname="' + photoval.peak_name + '" data-peakslug="' + photoval.peak_slug + '" data-description="' + htmlentities(photoCaption) + '" data-gallery href="{{ MEDIA_URL }}' + photoval.fullsize_url + '"><div class="' + divClass + ' ' + borderClass + ' hover-photos" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}' + photoval.thumbnail_url + '\'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;"><div class="' + photoDivClass + '"><div class="' + hoverclass + '"><div>' + photoHtml + '</div></div><div class="' + photoCaptionClass + '" style="display: none;"><span class="data photo-caption" style="position: absolute; bottom: -5px; left: 10px; color: #fff;"><p class="bagger" style="white-space: normal; font-size: 10px;">' + photoCaption + '</p><p class="bagger" style="font-size: 10px;">' + photoval.peak_name + '&nbsp;&bull;&nbsp;<time class="timeago" datetime="' + photoval.created + 'T00:00:00">' + photoval.created + '</time></p></span></div><div class="static-photo-info" style="display: block;"><span class="data photo-caption" style="position: absolute; bottom: -5px; left: 10px; color: #fff;"><p class="bagger" style="font-size: 10px;">' + photoval.peak_name + '&nbsp;&bull;&nbsp;<time class="timeago" datetime="' + photoval.created + 'T00:00:00">' + photoval.created + '</time></p></span></div></div></div></a>');
                            }
                            photoIndex++;
                            if (photoIndex == 25) {
                                photoIndex = 1;
                            }
                        }
                        photos_displayed++;
                        photos.push('{{ MEDIA_URL }}' + photoval.fullsize_url);

                    });
                    $("time.timeago").timeago();
                }
            });

            if (page == 1) {
                $('#ajax-data-loading').css('display', 'none');
            } else {
                $('#more-ajax-data-loading').css('display', 'none');
            }

            //show the footer
            $('#photos-footer').show();
            if (photos_displayed < photoCount) {
                $('#see-more-photos-div').show();
                $('#photos-footer').show();
            } else {
                $('#see-more-photos-div').hide();
                $('#photos-footer').hide();
            }

            photos_page++;
        });

    }

    $(document).ready(function() {

        $("body").on("contextmenu", "img", function(e) {
            return false;
        });

        $('#mobileTimefilterSelect option:selected').text($('#mobileTimefilterSelect option:selected').text()+' \u25BE');

        var viewport_height = $(window).height();
        var viewport_width = $(window).width();
        var footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
        var data_loading_margin = (viewport_height - footer_height) / 3;
        $('.data-loading').css('margin-top', data_loading_margin);
        $(window).resize(function() {
            viewport_height = $(window).height();
            viewport_width = $(window).width();
            footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
            data_loading_margin = (viewport_height - footer_height) / 3;
            $('.data-loading').css('margin-top', data_loading_margin);
        });

        $('#blueimp-gallery').on('open', function (event) {
            $('body,html').css('overflow','visible');
        });

        $("#blueimp-gallery").on('slide', function (event, index, slide) {
            var gallery = $('#blueimp-gallery').data('gallery');
            var caption = gallery.list[index].getAttribute('data-description'),
                username = gallery.list[index].getAttribute('data-user'),
                credit = gallery.list[index].getAttribute('data-credit'),
                summit_log_id = gallery.list[index].getAttribute('data-summitlogid'),
                photo_url = gallery.list[index].getAttribute('data-photo-url'),
                photo_date = gallery.list[index].getAttribute('data-photodate'),
                peak_name = gallery.list[index].getAttribute('data-peakname'),
                peak_slug = gallery.list[index].getAttribute('data-peakslug'),
                caption_node = gallery.container.find('.description-text-caption');
                username_node = gallery.container.find('.description-text-user');
            caption_node.empty();
            username_node.empty();
            if (caption) {
                caption_node[0].appendChild(document.createTextNode(caption));
            }
            if (peak_slug) {
                var newdiv = document.createElement('div');
                var photoDateHtml = '';
                if (summit_log_id != '0') {
                    photoDateHtml = '<a style="color: #fff;" href="/' + peak_slug + '/summits/' + summit_log_id + '">' + photo_date + '</a>';
                } else {
                    photoDateHtml = photo_date;
                }
                newdiv.innerHTML = '<a style="color: #fff;" href="/' + peak_slug + '">' + peak_name + '</a>&nbsp;&bull;&nbsp;' + photoDateHtml;
                username_node[0].appendChild(newdiv);
            }

        });

        $('#see-more-photos').on('click', function(){
            loadPhotos(photos_year, photos_page);
        });

        $('.timefilter-list').on('click', '.timefilter-item', function(e) {

            e.preventDefault();
            photos_displayed = 0;
            var year_html = $(this).html();
            var year_name = year_html.split('\u2022');
            $("#timefilter-title").html(year_name[0]);
            $('#mobileTimefilterSelect').val(year_name[0]);
            $('#photo-count').html($(this).data('count'));
            photos_year = $(this).data('value');
            photos_page = 1;
            loadPhotos(photos_year, photos_page);

        });

        $('#mobileTimefilterSelect').on('change', function() {

            $("#timefilter-title").html($(this).val());
            $('#mobileTimefilterSelect option').each( function( index, element ){
                $(this).text($(this).text().replace(' \u25BE',''));
            });
            $('#mobileTimefilterSelect option:selected').text($('#mobileTimefilterSelect option:selected').text()+' \u25BE');
            photos_displayed = 0;
            photos_year = $(this).val();
            photos_page = 1;
            loadPhotos(photos_year, photos_page);

        });

        $("#photos-list").on('mouseenter','div',function () {
            var caption = $(this).parents('a').data('description');
            if (caption != '') {
                $(this).children('.static-photo-info').hide();
                $(this).children('.user-photo-info').show();
            }
        });

        $("#photos-list").on('mouseleave','div',function () {
            $(this).children('.user-photo-info').hide();
            $(this).children('.static-photo-info').show();
        });

        $("#more-photos-list").on('mouseenter','div',function () {
            var caption = $(this).parents('a').data('description');
            if (caption != '') {
                $(this).children('.static-photo-info').hide();
                $(this).children('.user-photo-info').show();
            }
        });

        $("#more-photos-list").on('mouseleave','div',function () {
            $(this).children('.user-photo-info').hide();
            $(this).children('.static-photo-info').show();
        });

        $('#unfollow-{{ bagger.id }}').hover(
        function(){
            $(this).removeClass('unfollowButton').addClass('unfollowButtonHoverProfile').text('Unfollow').css('background-color','#f24100').css('border-color','#f24100');
        },
        function(){
            $(this).removeClass('unfollowButtonHoverProfile').addClass('unfollowButton').text('You are following').css('background-color','#00b330').css('border-color','#00b330');
        });

        $("#unfollow-{{ bagger.id }}").on('click',function (){
            var btn = $("#unfollow-{{ bagger.id }}");
            var follow_btn = $("#follow-{{ bagger.id }}");
            btn.html('<i class="fa fa-spinner fa-spin"></i>');
            $.post('/accounts/unfollow/{{ bagger.id }}/',function(data){
                if (data == "True"){
                    follow_btn.html('Follow');
                    $("#unfollow-{{ bagger.id }}").hide();
                    $("#follow-{{ bagger.id }}").show();
                    $("#mobile-unfollow-{{ bagger.id }}").hide();
                    $("#mobile-follow-{{ bagger.id }}").show();
                }
            });
        });

        $("#follow-{{ bagger.id }}").on('click',function (){
            var btn = $("#follow-{{ bagger.id }}");
            var unfollow_btn = $("#unfollow-{{ bagger.id }}");
            btn.html('<i class="fa fa-spinner fa-spin"></i>');
            $.post('/accounts/follow/{{ bagger.id }}/',function(data){
                if (data == "True"){
                    unfollow_btn.html('You are following');
                    $("#follow-{{ bagger.id }}").hide();
                    $("#unfollow-{{ bagger.id }}").show();
                    $("#mobile-follow-{{ bagger.id }}").hide();
                    $("#mobile-unfollow-{{ bagger.id }}").show();
                }
            });
        });

        $("#mobile-unfollow-{{ bagger.id }}").on('click',function (){
            var btn = $("#mobile-unfollow-{{ bagger.id }}");
            var follow_btn = $("#mobile-follow-{{ bagger.id }}");
            btn.html('<i class="fa fa-spinner fa-spin"></i>');
            $.post('/accounts/unfollow/{{ bagger.id }}/',function(data){
                if (data == "True"){
                    follow_btn.html('follow');
                    $("#mobile-unfollow-{{ bagger.id }}").hide();
                    $("#mobile-follow-{{ bagger.id }}").show();
                    $("#unfollow-{{ bagger.id }}").hide();
                    $("#follow-{{ bagger.id }}").show();
                }
            });
        });

        $("#mobile-follow-{{ bagger.id }}").on('click',function (){
            var btn = $("#mobile-follow-{{ bagger.id }}");
            var unfollow_btn = $("#mobile-unfollow-{{ bagger.id }}");
            btn.html('<i class="fa fa-spinner fa-spin"></i>');
            $.post('/accounts/follow/{{ bagger.id }}/',function(data){
                if (data == "True"){
                    unfollow_btn.html('following');
                    $("#mobile-follow-{{ bagger.id }}").hide();
                    $("#mobile-unfollow-{{ bagger.id }}").show();
                    $("#follow-{{ bagger.id }}").hide();
                    $("#unfollow-{{ bagger.id }}").show();
                }
            });
        });

        //Load some photos
        var vars = [], hash, year;
        var q = document.URL.split('#')[1];
        if(q != undefined){
            q = q.split('&');
            for(var i = 0; i < q.length; i++){
                hash = q[i].split('=');
                vars.push(hash[1]);
                vars[hash[0]] = hash[1];
            }
        }

        if (vars['year'] != undefined) {
            year = vars['year'];
            if (year != '' && year != 'all') {
                $("#timefilter-title").html(year);
                $('#mobileTimefilterSelect').val(year);
            }
        } else {
            year = 'all';
        }

        loadPhotos(year, photos_page);

    });

</script>

{% endblock %}

{% block gallery %}
<div id="blueimp-gallery" class="blueimp-gallery blueimp-gallery-controls" data-use-bootstrap-modal="false" data-slideshow-interval="4000">
    <!-- The container for the modal slides -->
    <div class="slides"></div>
    <!-- Controls for the borderless lightbox -->
    <h3 class="title"></h3>
    <div class="description">
        <div class="description-text">
            <div class="description-text-caption"></div>
            <div class="description-text-user"></div>
        </div>
    </div>
    <a class="prev">‹</a>
    <a class="next">›</a>
    <a class="close">×</a>
    <a class="play-pause"></a>
    <ol class="indicator"></ol>
    <!-- The modal dialog, which will be used to wrap the lightbox content -->
    <div class="modal fade">
        <div class="modal-dialog" style="width: 80%;">
            <div class="modal-content">
                <div class="modal-header">
                    <button style="color: #fff;" type="button" class="close" aria-hidden="true">&times;</button>
                    <h4 class="modal-title"></h4>
                </div>
                <div class="modal-body next"></div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default pull-left prev">
                        <i class="glyphicon glyphicon-chevron-left"></i>
                        Previous
                    </button>
                    <button type="button" class="btn btn-primary next">
                        Next
                        <i class="glyphicon glyphicon-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="loading" style="display: none;">Loading&#8230;</div>
{% endblock %}