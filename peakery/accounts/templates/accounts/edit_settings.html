{% extends "base_no_header_footer.html" %}

{% load avatar_tags %}
{% load item_tags %}
{% load cache %}
{% load humanize %}
{% load i18n %}

{% block title %}Settings{% endblock %}
{% block titlemeta %}Settings{% endblock %}
{% block description %}Settings{% endblock %}

{% block body_form %}<form class="useredit_form" method="POST" action="/settings/">{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row" style="border-bottom: none;">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 form-header-bar">
            <span style="font-size: 24px; font-weight: 600; color: #fff;">
            <div id="breadcrumbs" style="margin-left: 15px;">
                <div class="pull-right cancel-x-button-div">
                    <a class="cancel-x-button" onclick="cancelEdit();"><i class="fa fa-times"></i></a>
                </div>
                <div class="pull-right save-changes-button-div">
                    <button class="btn btn-secondary set2 input save-changes-button" id="edit_user" disabled>Save changes</button>
                </div>
                <div>
                    <div class="form-header-title ellipsis"><span>Edit your settings</span></div>
                </div>
            </div>
            </span>
        </div>
    </div>
{% endblock %}

{% block content %}

<style>

    body.modal-open {
        overflow: visible;
    }

   @media screen and (max-width: 767px) and (min-width: 1px) {
        #content-body {
           margin-top: 0px;
           padding-bottom: 0px;
        }
        html, body {
            letter-spacing: .03125em;
        }
        .content-pane {
           margin-top: 30px;
        }
        ul#user-files textarea {
            font-size: 14px;
        }
        .row-full-width .col-xs-12 {
            padding-left: 10px;
            padding-right: 10px;
        }
        .field-title {
            font-size: 16px;
            font-weight: 700;
        }
        .form-header-title {
            font-size: 14px;
            font-weight: 500;
        }
        input {
            font-size: 16px;
            font-weight: 300;
        }
        form textarea {
            font-size: 16px;
            font-weight: 300;
        }
        .route-info-section {
            margin-top: 20px;
        }
        .field-title-spacer {
            height: 0px;
        }
        .header-peak-name {
            color: #fff;
        }
        .header-peak {
            font-size: 18px;
            font-weight: 500;
        }
        .close-route-edit {
            margin-right: 0px;
        }
        .header-help {
            color: #999;
            font-size: 11px;
            font-weight: 300;
        }
        ::-webkit-input-placeholder { font-size: 16px; }
        ::-moz-placeholder { font-size: 16px; } /* firefox 19+ */
        :-ms-input-placeholder { font-size: 16px; } /* ie */
        input:-moz-placeholder { font-size: 16px; }
        input { font-size: 16px; }
        #label-facebook-page {
            left: 10px;
            top: 43px;
        }
        #label-twitter-username {
            left: 17px;
            top: 112px;
        }
        #label-instagram-username {
            left: 16px;
            top: 182px;
        }
        #label-flicker-page {
            left: 16px;
            top: 251px;
        }
        #label-website {
            left: 22px;
            top: 323px;
        }
    }
    @media screen and (min-width: 768px) {

        #content-body {
           margin-top: 30px;
        }
        .content-pane {
           margin-top: 0px;
        }
        input {
            font-size: 18px;
            font-weight: 300;
        }
        form textarea {
            font-size: 18px;
            font-weight: 300;
        }
        .route-info-section {
            margin-top: 60px;
        }
        .field-title-spacer {
            height: 10px;
        }
        .save-changes-div {
            float: right;
        }
    }
    @media screen and (min-width: 768px) and (max-width: 1023px) {
        ul#peakroute-files textarea {
            font-size: 16px;
        }
        .header-peak-name {
            color: #fff;
        }
        .header-peak {
            font-size: 18px;
            font-weight: 500;
        }
        .close-route-edit {
            margin-right: 0px;
        }
        .header-help {
            color: #999;
            font-size: 12px;
            font-weight: 300;
        }
        .field-title {
            font-size: 18px;
            font-weight: 700;
        }
        .form-header-title {
            font-size: 18px;
            font-weight: 500;
        }
        ::-webkit-input-placeholder { font-size: 16px; }
        ::-moz-placeholder { font-size: 16px; } /* firefox 19+ */
        :-ms-input-placeholder { font-size: 16px; } /* ie */
        input:-moz-placeholder { font-size: 16px; }
        input { font-size: 16px; }
        #label-facebook-page {
            left: 10px;
            top: 48px;
        }
        #label-twitter-username {
            left: 17px;
            top: 117px;
        }
        #label-instagram-username {
            left: 16px;
            top: 186px;
        }
        #label-flicker-page {
            left: 16px;
            top: 255px;
        }
        #label-website {
            left: 22px;
            top: 327px;
        }
    }
    @media screen and (min-width: 1024px) {
        ul#user-files textarea {
            font-size: 18px;
        }
        .header-peak-name {
            color: #f24100;
        }
        .header-peak {
            font-size: 20px;
            font-weight: 300;
        }
        .close-route-edit {
            margin-right: 20px;
        }
        .header-help {
            color: #999;
            font-size: 14px;
            font-weight: 300;
        }
        .field-title {
            font-size: 21px;
            font-weight: 700;
        }
        .form-header-title {
            font-size: 20px;
            font-weight: 300;
        }
        ::-webkit-input-placeholder { font-size: 18px; }
        ::-moz-placeholder { font-size: 18px; } /* firefox 19+ */
        :-ms-input-placeholder { font-size: 18px; } /* ie */
        input:-moz-placeholder { font-size: 18px; }
        input { font-size: 18px; }
        #label-facebook-page {
            left: 10px;
            top: 53px;
        }
        #label-twitter-username {
            left: 17px;
            top: 123px;
        }
        #label-instagram-username {
            left: 16px;
            top: 193px;
        }
        #label-flicker-page {
            left: 16px;
            top: 261px;
        }
        #label-website {
            left: 22px;
            top: 333px;
        }
    }
    @media screen and (min-width: 1px) and (max-width: 1279px) {
         .no-header-container {
           padding-left: 0px;
        }
    }
    ::-webkit-input-placeholder { font-weight: 300; }
    ::-moz-placeholder { font-weight: 300; } /* firefox 19+ */
    :-ms-input-placeholder { font-weight: 300; } /* ie */
    input:-moz-placeholder { font-weight: 300; }
    input { font-weight: 300; }

    .gm-style-mtc {
        opacity: .8;
    }

    input[type=radio].with-font,
    input[type=checkbox].with-font {
        border: 0;
        clip: rect(0 0 0 0);
        height: 1px;
        margin: -1px;
        overflow: hidden;
        padding: 0;
        position: absolute;
        width: 1px;
    }

    input[type=radio].with-font ~ label:before,
    input[type=checkbox].with-font ~ label:before {
        font-family: "Font Awesome 5 Free";
        display: inline-block;
        content: "\f111";
        letter-spacing: 10px;
        font-size: 1.2em;
        color: #535353;
        width: 1.4em;
    }

    input[type=radio].with-font:checked ~ label:before,
    input[type=checkbox].with-font:checked ~ label:before  {
        content: "\f00c";
        font-size: 1.2em;
        color: #00B1F2;
        letter-spacing: 5px;
    }
    input[type=checkbox].with-font ~ label:before {
        content: "\f0c8";
    }
    input[type=checkbox].with-font:checked ~ label:before {
        content: "\f14a";
        color: #00B1F2;
    }
    label {
        font-weight: 300;
    }

</style>

    <div class="container" style="padding-left: 0px; padding-right: 0px;">

        <div class="content-pane" style="background-color: #f6f6f6; padding-top: 40px;">

            <input type="hidden" name="user_id" value="{{ bagger.id }}">
            <input type="hidden" name="previous_page" id="previous_page" value="">
            {% csrf_token %}

            <div class="row row-full-width hidden-sm hidden-md hidden-lg" style="padding-bottom: 60px;">
                <div class="col-md-12">
                    <a href="/accounts/logout/" class="field-title">Log out</a>
                </div>
            </div>

            <div class="row row-full-width" style="padding-bottom: 60px;">
                <div class="col-md-12">
                    <a id="forgot-password" class="field-title">Reset password</a>
                </div>
            </div>


            <div class="row row-full-width" style="padding-bottom: 60px;">
                <div class="col-md-12">
                    <span class="field-title" style="margin-top: 20px;">Email notifications</span>
                    <fieldset>
                        {% for row in notice_settings.rows %}
                            {% with forloop.counter0 as notification_id %}
                                {% for cell in row.cells %}
                                    <div>
                                        <input type="checkbox" class="with-font" id="notification_{{ notification_id }}" name="{{ cell.0 }}" {% if cell.1 %}checked="yes"{% endif %}/>
                                        <label for="notification_{{ notification_id }}">{% trans row.notice_type.display %}</label>
                                    </div>
                                {% endfor %}
                            {% endwith %}
                        {% endfor %}
                    </fieldset>
                </div>
            </div>

            <div class="row row-full-width" style="padding-bottom: 60px;">
                <div class="col-md-12">
                    <p id="otherIssues">Other issues? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
                </div>
            </div>

        </div>

    </div>

    </form>

<style type="text/css">
    .ui-autocomplete {
        max-height: 100px;
        overflow-y: auto;
        /* prevent horizontal scrollbar */
        overflow-x: hidden;
        /* add padding to account for vertical scrollbar */
        padding-right: 20px;
    }
        /* IE 6 doesn't support max-height
       * we use height instead, but this forces the menu to always be this tall
       */
    * html .ui-autocomplete {
        height: 100px;
    }
</style>

    <div class="message-modal modal fade" id="message-modal" tabindex="-1" role="dialog" aria-labelledby="message-modal-label">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                  <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                  <h4 class="modal-title" id="message-modal-label"></h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div id="message-modal-body" class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="margin-bottom: 5px; font-size: 16px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="confirm-modal modal fade" id="confirm-modal" tabindex="-1" role="dialog" aria-labelledby="confirm-modal-label">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                  <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                  <h4 class="modal-title" id="confirm-modal-label"></h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div id="confirm-modal-body" class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="margin-bottom: 5px; font-size: 16px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script type="text/javascript">

$(document).ready(function(){

    $('#previous_page').val(document.referrer);
    if (document.referrer != '' && document.referrer.slice(-8) != 'settings' && document.referrer.slice(-9) != 'settings/') {
        $('#previous_page').val(document.referrer);
    }

    $('#forgot-password').on('click', function(e) {
        $('#accounts-forgot-password').modal('show');
    });

    $('#reset-my-password').on('click', function(e) {
        e.preventDefault();
        var post_url = '/accounts/password_reset/';
        $.ajax({
            type: "POST",
            url: post_url,
            data: $('#reset-password-form').serialize()
        }).done(function (html) {
            if (html == 'success') {
                //console.log('success');
                $('#accounts-forgot-password-title').html('Forgot password email sent');
                $('#reset-password-form').hide();
                $('#password-reset-email').html($('#id_email').val());
                $('#password-reset-success').fadeIn();

            } else {
                $("#password_reset_error").html(html);
                $("#password_reset_error").fadeIn();
            }
        });
    });
    $('#accounts-forgot-password').on('show.bs.modal', function() {
        $('#password-reset-success').hide();
        $('#password_reset_error').html('');
        $('#password_reset_error').hide();
        $('#reset-password-form').show();
    });
    $('#accounts-forgot-password').on('shown.bs.modal', function() {
        $('#id_email').val('');
        $('#id_email').parent().children('.holder').show();
        $('#id_email').focus();
    });

    $('label').on('click', function() {
        $('#edit_user').prop('disabled', false);
    });

    $('#edit_user').on('click', function(e) {
        e.preventDefault();
        $('.useredit_form').attr('action', '/settings/');
        $('.useredit_form').submit();

    });

    $(document).on("keydown", ":input:not(textarea)", function(event) {
        if (event.keyCode == 13) {
            return false;
        } else {
            return event.keyCode;
        }
    });

});

function jailai(selector){
    $("textarea#"+selector).effect("highlight", {}, 3000);
}

function cancelEdit() {
    if ($('#edit_user').prop('disabled') == true) {
        if (document.referrer != '' && document.referrer.slice(-8) != 'settings' && document.referrer.slice(-9) != 'settings/') {
            window.location.href = document.referrer;
        } else {
            window.location.href = '/';
        }

    } else {
        $('#confirm-modal-label').html('Discard your changes?');
        $('#confirm-modal-body').html('<div style="margin-top: 20px; text-align: center;"><a href="' + document.referrer + '" class="btn btn-primary" style="width: 100px;">Discard</a><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">Cancel</button></div>');
        $('#confirm-modal').modal('show');
    }
}

</script>

{% endblock %}

{% block end_full_height_form %}
</form>

<div class="accounts-forgot-password-modal modal fade" id="accounts-forgot-password" tabindex="-1" role="dialog" aria-labelledby="accounts-forgot-password-label">
    <div class="modal-dialog" style="width: 350px;" role="document">
        <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
              <h4 class="modal-title" id="accounts-forgot-password-label"><span id="accounts-forgot-password-title">Forgot password</span></h4>
            </div>
            <div class="modal-body">
                <div class="signUpWithEmail">
                    <script type="text/javascript">
                        $(function() {
                            $('#id_email').on('input', function(e) {
                                if ($(this).val() != "") {
                                    $(this).parent().children('.holder').hide();
                                } else {
                                    $(this).parent().children('.holder').show();
                                }
                            });
                        });
                    </script>
                    <form id="reset-password-form" action="" method="post">
                        <div style="display: none;" id="password_reset_error"></div>
                        <div style="margin-bottom: 20px;">Enter your email and we’ll send password reset instructions:</div>
                        <span class="a">
                            <input class="email" id="id_email" maxlength="100" name="email" type="text">
                            <span class="holder" style="">Email address</span>
                        </span>
                        <div style="text-align: center;height: 50px;line-height: 60px; margin-top: 25px; margin-bottom: 15px;">
                            {% csrf_token %}
                            <input id="reset-my-password" type="submit" value="Reset my password" class="btn set2 input" style="font-size: 14px; height: 50px;">
                        </div>
                    </form>
                </div>
                <div id="password-reset-success" style="display: none;">
                    We just sent an email to <span id="password-reset-email"></span> with password reset instructions.
                </div>
            </div>
        </div>
    </div>
</div>

<div class="confirm-modal modal fade" id="confirm-modal" tabindex="-1" role="dialog" aria-labelledby="confirm-modal-label">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
              <h4 class="modal-title" id="confirm-modal-label"></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div id="confirm-modal-body" class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="margin-bottom: 5px; font-size: 16px;"></div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}