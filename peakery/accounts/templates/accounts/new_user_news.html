{% extends "base.html" %}
{% load static %}
{% load humanize %}

{% block css %}
    <link href="{{MEDIA_URL}}css/view-peak.css?v=1" rel="stylesheet" type="text/css" media="screen" />
    <link href="{{MEDIA_URL}}css/lightboxes/django-follow.css" rel="stylesheet" type="text/css" media="screen" />
    <link href="{{MEDIA_URL}}css/jquery.lightbox-0.5.css" rel="stylesheet" type="text/css" media="screen" />
    <link href="{{MEDIA_URL}}css/jquery.lightbox-0.5.css" rel="stylesheet" type="text/css" media="screen" />
    <link href="{{MEDIA_URL}}css/tipsy.css" rel="stylesheet" type="text/css" media="screen" />
    <script type="text/javascript">
    {% include "mapbox/polyline.js" %}
    </script>
{% endblock %}

{% load avatar_tags %}
{% block title %}News{% endblock %}
{% block titlemeta %}peakery news{% endblock %}
{% block description %}Latest news about peakery members you follow{% endblock %}

{% block extrajs %}
    <script type="text/javascript" src="{% static 'js/jquery.lightbox-0.5.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/jquery.tipsy.js' %}"></script>
    <script type="text/javascript" src="{% static 'js/handlebars.1.0.0.beta.3.js' %}"></script>
{% endblock %}

{% block content %}

    <style type="text/css">

        body.modal-open {
            overflow: visible;
        }

        .regions-subnav-fixed {
            top: 50px;
        }

        #content-body {
           margin-top: 0px;
       }

        @media screen and (max-width: 767px) and (min-width: 1px) {
           .content-pane {
               margin-top: 40px;
           }
           .mobile-regions-subnav-fixed {
               margin-top: 50px;
           }
           .peakeryFollowButton {
                font-size: 10px !important;
                width: 130px !important;
            }
           .summit-list-stats {
                font-size: 12px;
                color: #999;
            }
           .summit-card-mobile-stats {
                position: relative;
                height: 40px;
                left: 0px;
           }
       }
       @media screen and (max-width: 1023px) and (min-width: 768px) {
           .content-pane {
               margin-top: 50px;
           }
           .mobile-regions-subnav-fixed {
               margin-top: 0px;
           }
           .summit-list-stats {
                font-size: 12px;
                color: #999;
            }
            .summit-card-peak-stats {
                margin-left: 0px;
            }
            .photo-grid-4 {
                border-top: none !important;
                border-right: none !important;
                border-bottom: none !important;
                border-left: none !important;
            }
       }

        @media screen and (min-width: 1024px) {
            .content-pane {
               margin-top: 50px;
           }
            .mobile-regions-subnav-fixed {
               margin-top: 0px;
           }
            .summit-list-stats {
                font-size: 14px;
                color: #999;
            }
            .summit-card-peak-stats {
                margin-left: 0px;
            }
        }

    </style>

    <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>

<div class="container">

    <div class="row sub-header-row regions-subnav-fixed" style="height: 50px; padding-right: 0px;">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999;">
            <span><a style="margin-left: 10px; color: rgb(153, 153, 153); font-weight: 300;" id="type-all" class="region-header-sub-links ajax-link" onclick="loadNews('all', '1');">All</a><a style="margin-left: 35px; color: rgb(153, 153, 153); font-weight: 300;" id="type-follow" class="region-header-sub-links ajax-link" onclick="loadNews('follow', '1');">Members you follow</a><a style="margin-left: 35px; color: rgb(153, 153, 153); font-weight: 300;" id="type-you" class="region-header-sub-links ajax-link" onclick="loadNews('you', '1');">You</a></span>
        </div>
    </div>

    <div class="row content-pane">
        <div class="col-md-12">
            <div class="row" id="ajax-data-loading" style="display: none;">
              <div class="col-md-12 data-loading" style="text-align: center; color: #666;">
                <i class="fa fa-spinner fa-spin fa-5x"></i>
              </div>
            </div>
            <div class="row" id="summits-list"></div>
            <div class="row" id="more-summits-list"></div>
            <div class="row" id="more-summits">
              <div class="row dark-background-row">
                <div style="height: 120px; text-align: center; margin-top: 60px;">
                    <button id="more-summits-btn" class="btn btn-secondary" style="width: 160px; color: #fff; display: none;">See more news</button>
                </div>
              </div>
            </div>
        </div>
    </div>

</div>

<script type="text/javascript">

    var newsType;
    var nextPage = 2;
    var loading = false;

    function loadNews(type, page) {

        loading = true;

        newsType = type;
        $('.bottom-spacer').show();

        switch(type) {
            case 'all':
                $('#type-all').css('color', '#F24100');
                $('#type-all').css('font-weight', '500');
                $('#type-follow').css('color', '#999');
                $('#type-follow').css('font-weight', '300');
                $('#type-you').css('color', '#999');
                $('#type-you').css('font-weight', '300');
                break;
            case 'follow':
                $('#type-all').css('color', '#999');
                $('#type-all').css('font-weight', '300');
                $('#type-follow').css('color', '#F24100');
                $('#type-follow').css('font-weight', '500');
                $('#type-you').css('color', '#999');
                $('#type-you').css('font-weight', '300');
                break;
            case 'you':
                $('#type-all').css('color', '#999');
                $('#type-all').css('font-weight', '300');
                $('#type-follow').css('color', '#999');
                $('#type-follow').css('font-weight', '300');
                $('#type-you').css('color', '#F24100');
                $('#type-you').css('font-weight', '500');
                break;
            default:
                $('#type-all').css('color', '#F24100');
                $('#type-all').css('font-weight', '500');
                $('#type-follow').css('color', '#999');
                $('#type-follow').css('font-weight', '300');
                $('#type-you').css('color', '#999');
                $('#type-you').css('font-weight', '300');
        }

        window.location.hash = 'type='+type;

        var totalPages, totalNews, newsContainer;
        var counter = 1;
        var dividerDiv = '';

        if (page == 1) {
            $('#summits-list').empty();
            $('#more-summits-list').empty();
            $('#ajax-data-loading').css('display', 'inline');
            newsContainer = '#summits-list';
            nextPage = 2;
            window.scrollTo(0, 0);
        } else {
            $('#more-summits-btn').html('<i class="fa fa-spinner fa-spin"></i>');
            newsContainer = '#more-summits-list';
        }

        $.getJSON('{% url "user_news_list" %}?user_id={{ user.id }}&type='+type+'&page='+page, function(data) {
            $.each( data, function( key, val ) {

                if (key=='news_count') {
                    totalNews = val;
                    totalPages = Math.ceil(parseInt(totalNews)/20);
                }

                if (key=='summits') {
                    $.each( val, function( summitkey, summitval ) {

                        if (summitval.id != '') {

                            var summitdate = new Date(summitval.summitlog_date);
                            var today = new Date();
                            var timeDiff = Math.abs(today.getTime() - summitdate.getTime());
                            var diffDays = Math.ceil(timeDiff / (1000 * 3600 * 24));
                            var photo_arr = summitval.photos;
                            var staticMapUrl = '';

                            var avatar;
                            if (summitval.avatar_url != 'None') {
                                avatar = '<img src="{{MEDIA_URL}}' + summitval.avatar_url + '" class="summit-card-user-avatar">&nbsp;&nbsp;';
                            } else {
                                avatar = '';
                            }

                            var sender_avatar;
                            if (summitval.sender_avatar_url != 'None') {
                                sender_avatar = '<img src="{{MEDIA_URL}}' + summitval.sender_avatar_url + '" class="summit-card-user-avatar">&nbsp;&nbsp;';
                            } else {
                                sender_avatar = '<img src="{% static 'img/default-user-100x100.png' %}" class="summit-card-user-avatar">&nbsp;&nbsp;';
                            }

                            //build country string
                            var country = '';
                            $.each(summitval.country, function (countrykey, countryval) {
                                country = country + countryval.country_name + ' / ';
                            });
                            country = country.substr(0, country.length - 3);
                            if (country == '') {
                                country = 'Unknown Country';
                            }

                            //build region string
                            var region = '';
                            $.each(summitval.region, function (regionkey, regionval) {
                                region = region + regionval.region_name + ', ' + regionval.country_name + ' / ';
                            });
                            region = region.substr(0, region.length - 3);
                            if (region == '') {
                                region = country;
                            }

                            dividerDiv = '<div class="row dark-background-row"><div class="sp-60"></div></div>';

                            if (summitval.date_entered == 'False') {
                                var desktop_date = 'date unknown';
                                var mobile_date = 'date unknown';
                            } else {
                                var display_date = $.format.date(summitval.summitlog_date + 'T00:00:00', 'MMM d, yyyy');
                                var desktop_date = display_date + '&nbsp;/&nbsp;<time class="timeago" datetime="' + summitval.summitlog_date + 'T00:00:00">' + summitval.summitlog_date + '</time>';
                                var mobile_date = '<time class="timeago" datetime="' + summitval.summitlog_date + 'T00:00:00">' + summitval.summitlog_date + '</time>';
                            }

                            var header_border = '';
                            if (photo_arr.length == 0 && summitval.gpx_file == 'None' && summitval.log != null) {
                                header_border = 'border-bottom: 1px solid #c0c0c0; ';
                            }
                            if (summitval.notice_type == 'new_comment_following_summitlog' || summitval.notice_type == 'new_comment_summitlog' || summitval.notice_type == 'non_following_comment_summitlog') {
                                header_border = 'border-bottom: 1px solid #c0c0c0; ';
                            }
                            var activity = '';
                            if (summitval.notice_type == 'new_like_following_summitlog') {
                                activity = ' <span style="font-weight: 300;">liked</span> <a href="/members/' + summitval.username + '/">' + summitval.username + '\'s</a> ';
                                if (summitval.attempt == 'True') {
                                    activity = activity + 'attempt of ';
                                } else {
                                    activity = activity + 'summit of ';
                                }
                            } else if (summitval.notice_type == 'new_comment_following_summitlog') {
                                activity = ' <span style="font-weight: 300;">commented on</span> <a href="/members/' + summitval.username + '/">' + summitval.username + '\'s</a> ';
                                if (summitval.attempt == 'True') {
                                    activity = activity + 'attempt of ';
                                } else {
                                    activity = activity + 'summit of ';
                                }
                            } else if (summitval.notice_type == 'non_following_comment_summitlog') {
                                activity = ' <span style="font-weight: 300;">commented after you on</span> <a href="/members/' + summitval.username + '/">' + summitval.username + '\'s</a> ';
                                if (summitval.attempt == 'True') {
                                    activity = activity + 'attempt of ';
                                } else {
                                    activity = activity + 'summit of ';
                                }
                            } else if (summitval.notice_type == 'new_like_summitlog') {
                                activity = ' <span style="font-weight: 300;">liked your ';
                                if (summitval.attempt == 'True') {
                                    activity = activity + 'attempt of</span> ';
                                } else {
                                    activity = activity + 'summit of</span> ';
                                }
                            } else if (summitval.notice_type =='new_comment_summitlog') {
                                activity = ' <span style="font-weight: 300;">commented on your ';
                                if (summitval.attempt == 'True') {
                                    activity = activity + 'attempt of</span> ';
                                } else {
                                    activity = activity + 'summit of</span> ';
                                }
                            } else if (summitval.notice_type == 'user_added_to_summit_log') {
                                activity = ' <span style="font-weight: 300;">added you to their ';
                                if (summitval.attempt == 'True') {
                                    activity = activity + 'attempt of</span> ';
                                } else {
                                    activity = activity + 'summit of</span> ';
                                }
                            } else if (summitval.notice_type == 'following_new_first_ascent') {
                                activity = ' <span style="font-weight: 300;">snagged the</span> <img src="{% static 'img/award_flag.png' %}" style="height: 25px;"> <span style="font-weight: 300;">of</span> ';
                            } else if (summitval.notice_type == 'following_new_kom') {
                                activity = ' <span style="font-weight: 300;">is now</span> <img src="{% static 'img/badge_crown.png' %}" style="height: 25px; margin-bottom: 5px;"> <span style="font-weight: 300;">of</span> ';
                            } else if (summitval.notice_type == 'following_new_steward') {
                                activity = ' <span style="font-weight: 300;">is now a</span> <img src="{% static 'img/award_shield.png' %}" style="height: 25px;"> <span style="font-weight: 300;">of</span> ';
                            } else {
                                if (summitval.attempt == 'True') {
                                    activity = ' <span style="font-weight: 300;">attempted</span> ';
                                } else {
                                    activity = ' <span style="font-weight: 300;">summited</span> ';
                                }
                            }
                            $(newsContainer).append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">' + dividerDiv + '<div class="row" style="' + header_border + 'cursor: pointer;" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');"><div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 summit-card-header" style="background-color: #fff; letter-spacing: 1.0px;"><div class="summit-list-stats summit-card-user-stats hidden-xs hidden-sm" style="float: right;">&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + desktop_date + '</div><div class="summit-list-stats summit-card-user-stats hidden-lg hidden-md hidden-xs" style="float: right;">&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + mobile_date + '</div><div class="summit-list-stats summit-card-peak-stats hidden-lg hidden-md hidden-xs" style="float: right;">' + region + '</div><div class="summit-list-stats summit-card-peak-stats hidden-xs" style="height: 70px; margin-top: 0px; margin-bottom: 0px;"><div class="summit-list-stats summit-card-peak-stats hidden-xs hidden-sm" style="float: right;">' + numberWithCommas(Math.floor(summitval.elevation)) + ' ft / ' + numberWithCommas(Math.floor(summitval.elevation * .3048)) + ' m&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + region + '</div><div class="ellipsis" style="overflow: hidden;">' + sender_avatar + '<a href="/members/' + summitval.sender + '/">' + summitval.sender + '</a>' + activity + '<a style="color: #333;" href="/' + summitval.peak_slug + '/">' + summitval.peak_name + '</a></div></div><div class="summit-list-stats summit-card-mobile-stats hidden-lg hidden-md hidden-sm"><div style="position: absolute; height: 40px; display: table;"><p style="display: table-cell; vertical-align: middle; text-align: left;"><a href="/members/' + summitval.sender + '">' + summitval.sender + '</a>' + activity + '<a style="color: #333;" href="/' + summitval.peak_slug + '/">' + summitval.peak_name + '</a>&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + mobile_date + '</p></div></div></div></div>');

                            //get favorites count
                            var favorites_arr = summitval.favorites;
                            var favorites_count = favorites_arr.length;
                            if (favorites_count == '0') {
                                favorites_count = '&nbsp;';
                            }

                            //get comments count
                            var comments_arr = summitval.comments;
                            var comments_count = comments_arr.length;
                            if (comments_count == '0') {
                                comments_count = '&nbsp;';
                            }

                            //add photos
                            var photo_index = 0;
                            var photos_count = photo_arr.length;
                            var photos_style = '';
                            if (photos_count == '0') {
                                photos_count = '&nbsp;';
                                photos_style = 'display: none;'
                            }

                            //For comment news story, just show the comments
                            if (summitval.notice_type == 'new_comment_following_summitlog' || summitval.notice_type == 'new_comment_summitlog' || summitval.notice_type == 'non_following_comment_summitlog') {

                                //add comment
                                if (summitval.comment != null) {
                                    have_middle_section = true;
                                    var comment_text = summitval.comment;
                                    if (comment_text.length > 700) {
                                        comment_text = summitval.comment.substring(0, 700) + '...';
                                    }
                                    $(newsContainer).append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="background-color: #fff; border-bottom: 1px solid #c0c0c0;"><div><p>&nbsp;</p><p style="font-size: 16px; line-height: 26px;">' + comment_text + '</p><p>&nbsp;</p></div></div>');
                                }

                            } else {

                                var borderCss = '';
                                var log_top_border = '';
                                var have_middle_section = false;
                                //we're not going to show details on a log with no photos anymore, unless there is a gpx file - RLS 10/26/16
                                if (photo_arr.length == 0) {
                                    if (summitval.peak_thumbnail_url == '' && summitval.gpx_file != '') {
                                        have_middle_section = true;
                                        staticMapUrl = 'https://api.mapbox.com/styles/v1/peakery/cjjkkx6qa63mn2rthodesuu8m/static/url-https%3A%2F%2Fs3-us-west-1.amazonaws.com%2Fpeakery-static%2Fimg%2Fmarker-orange-on.png(' + summitval.peak_long + ',' + summitval.peak_lat + ')/' + summitval.peak_long + ',' + summitval.peak_lat + ',11,0.00,0.00/1280x240?access_token=pk.eyJ1IjoicGVha2VyeSIsImEiOiJjampra3Z0bnAxeTVnM3FteHlybHY3b2p1In0.7a5dEa5-995VUv8ceHHNmw';
                                        $(newsContainer).append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" id="map-' + summitval.id + '" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="img-responsive peakeryPhoto photography peakimg-responsive" src="{% static 'img/spacer.png' %}"></div></div>');
                                    } else if (summitval.peak_thumbnail_url != '' && summitval.peak_thumbnail_url.indexOf('default.png.350x245_q95_crop.jpg') < 0) {
                                        have_middle_section = true;
                                        staticMapUrl = 'https://api.mapbox.com/styles/v1/peakery/cjjkkx6qa63mn2rthodesuu8m/static/url-https%3A%2F%2Fs3-us-west-1.amazonaws.com%2Fpeakery-static%2Fimg%2Fmarker-orange-on.png(' + summitval.peak_long + ',' + summitval.peak_lat + ')/' + summitval.peak_long + ',' + summitval.peak_lat + ',11,0.00,0.00/960x240?access_token=pk.eyJ1IjoicGVha2VyeSIsImEiOiJjampra3Z0bnAxeTVnM3FteHlybHY3b2p1In0.7a5dEa5-995VUv8ceHHNmw';
                                        $(newsContainer).append('<div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}' + summitval.peak_thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                        $(newsContainer).append('<div class="col-lg-9 col-md-9 col-sm-8 col-xs-6 map-' + summitval.id + '" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="img-responsive peakimg-responsive" src="{% static 'img/spacer.png' %}"></div></div>');
                                    } else {
                                        log_top_border = 'border-top: 1px solid #c0c0c0;';
                                    }
                                } else if (photo_arr.length == 1) {
                                    have_middle_section = true;
                                    staticMapUrl = 'https://api.mapbox.com/styles/v1/peakery/cjjkkx6qa63mn2rthodesuu8m/static/url-https%3A%2F%2Fs3-us-west-1.amazonaws.com%2Fpeakery-static%2Fimg%2Fmarker-orange-on.png(' + summitval.peak_long + ',' + summitval.peak_lat + ')/' + summitval.peak_long + ',' + summitval.peak_lat + ',11,0.00,0.00/960x240?access_token=pk.eyJ1IjoicGVha2VyeSIsImEiOiJjampra3Z0bnAxeTVnM3FteHlybHY3b2p1In0.7a5dEa5-995VUv8ceHHNmw';
                                    $.each(summitval.photos, function (photokey, photoval) {
                                        $(newsContainer).append('<div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                        photo_index++;
                                        if (photo_index > 3) {
                                            return false;
                                        }
                                    });
                                    $(newsContainer).append('<div class="col-lg-9 col-md-9 col-sm-8 col-xs-6 map-' + summitval.id + '" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png' %}"></div></div>');
                                } else if (photo_arr.length == 2) {
                                    have_middle_section = true;
                                    staticMapUrl = 'https://api.mapbox.com/styles/v1/peakery/cjjkkx6qa63mn2rthodesuu8m/static/<EMAIL>(' + summitval.peak_long + ',' + summitval.peak_lat + ')/' + summitval.peak_long + ',' + summitval.peak_lat + ',11,0.00,0.00/640x240@2x?access_token=pk.eyJ1IjoicGVha2VyeSIsImEiOiJjampra3Z0bnAxeTVnM3FteHlybHY3b2p1In0.7a5dEa5-995VUv8ceHHNmw';
                                    $.each(summitval.photos, function (photokey, photoval) {
                                        $(newsContainer).append('<div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                        photo_index++;
                                        if (photo_index > 3) {
                                            return false;
                                        }
                                    });
                                    $(newsContainer).append('<div class="col-lg-6 col-md-6 col-sm-4 hidden-xs map-' + summitval.id + '" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png' %}"></div></div>');
                                } else if (photo_arr.length == 3) {
                                    have_middle_section = true;
                                    staticMapUrl = 'https://api.mapbox.com/styles/v1/peakery/cjjkkx6qa63mn2rthodesuu8m/static/<EMAIL>(' + summitval.peak_long + ',' + summitval.peak_lat + ')/' + summitval.peak_long + ',' + summitval.peak_lat + ',11,0.00,0.00/320x240@2x?access_token=pk.eyJ1IjoicGVha2VyeSIsImEiOiJjampra3Z0bnAxeTVnM3FteHlybHY3b2p1In0.7a5dEa5-995VUv8ceHHNmw';
                                    $.each(summitval.photos, function (photokey, photoval) {
                                        if (photo_index > 1) {
                                            divClass = 'col-lg-3 col-md-3 col-sm-4 hidden-xs';
                                        } else {
                                            divClass = 'col-lg-3 col-md-3 col-sm-4 col-xs-6';
                                        }
                                        $(newsContainer).append('<div class="' + divClass + ' summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                        photo_index++;
                                        if (photo_index > 3) {
                                            return false;
                                        }
                                    });
                                    $(newsContainer).append('<div class="col-lg-3 col-md-3 hidden-sm hidden-xs map-' + summitval.id + '" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png' %}"></div></div>');
                                } else {
                                    have_middle_section = true;
                                    staticMapUrl = '';
                                    $.each(summitval.photos, function (photokey, photoval) {
                                        if (photo_index < 3) {
                                            borderCss = 'border-right: solid 1px #e0e0e0;';
                                        } else {
                                            borderCss = '';
                                        }
                                        if (photo_index <= 1) {
                                            divClass = 'col-lg-3 col-md-3 col-sm-4 col-xs-6';
                                        } else if (photo_index == 2) {
                                            divClass = 'col-lg-3 col-md-3 col-sm-4 hidden-xs';
                                        } else {
                                            divClass = 'col-lg-3 col-md-3 hidden-sm hidden-xs';
                                        }
                                        if (photo_index < 3) {
                                            $(newsContainer).append('<div class="' + divClass + ' summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; ' + borderCss + ' overflow: hidden;"><div><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                        } else if (photo_index == 3) {
                                            if (summitval.gpx_file != 'None' && summitval.gpx_file != '') {
                                                staticMapUrl = 'https://api.mapbox.com/styles/v1/peakery/cjjkkx6qa63mn2rthodesuu8m/static/<EMAIL>(' + summitval.peak_long + ',' + summitval.peak_lat + ')/' + summitval.peak_long + ',' + summitval.peak_lat + ',11,0.00,0.00/320x240@2x?access_token=pk.eyJ1IjoicGVha2VyeSIsImEiOiJjampra3Z0bnAxeTVnM3FteHlybHY3b2p1In0.7a5dEa5-995VUv8ceHHNmw';
                                                $(newsContainer).append('<div class="col-lg-3 col-md-3 hidden-sm hidden-xs map-' + summitval.id + '" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png' %}"></div></div>');
                                            } else {
                                                $(newsContainer).append('<div class="' + divClass + ' summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; ' + borderCss + ' overflow: hidden;"><div><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                            }
                                        } else {
                                            return false;
                                        }
                                        photo_index++;
                                    });
                                }

                                //add log
                                if (summitval.log != null) {
                                    have_middle_section = true;
                                    var log_text = summitval.log.replace(/(?:\r\n|\r|\n)/g, '<br>');
                                    if (log_text.length > 9999) {
                                        log_text = summitval.log.substring(0, 700) + '...';
                                    }
                                    $(newsContainer).append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; background-color: #fff; border-bottom: 1px solid #c0c0c0; ' + log_top_border + '"><div><p class="summit-log-summary">' + log_text + '</p></div></div>');
                                }

                            }

                            //footer
                            if (summitval.liked == '1') {
                                classLiked = 'liked';
                            } else {
                                classLiked = '';
                            }
                            //if (summitval.log != null || photo_arr.length > 0) {
                            if (have_middle_section) {
                                $(newsContainer).append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');"><div class="row"><div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="height: 70px; background-color: #fff; z-index: 1;"><div><p><span class="summit-list-footer" style="font-weight: 600; line-height: 70px;"><a href="/' + summitval.peak_slug + '/summits/' + summitval.id + '/"><button style="padding-left: 13px; width: 100%; height: 100%; text-align: left; border: none; color: #00B1F2; font-weight: 600; position: absolute; left: -2px; top: -2px; z-index: 1;" class="btn btn-default btn-see-full-log">SEE FULL LOG</button></a></span><span class="summit-list-footer pull-right" style="margin-top: 8px; position: absolute; right: 30px; z-index: 2;"><span style="margin-left: 40px;"><a onclick="like_summit(' + summitval.id + ');" class="like_summit{{ is_authenticated }}" id="summitlog-with-' + summitval.id + '"><span class="' + classLiked + '" id="summitlog-like-' + summitval.id + '" style="width: 32px; height: 32px; display: inline-block; color: #ffffff; line-height: 32px; text-align: center; padding-left: 2px; font-size: 10px; font-weight: bold; background-size: 100%; background-image: url({% static 'img/fa-heart_256_0_00b1f2_none.png'%});">' + favorites_count + '</span></a></span><span style="margin-left: 40px;"><a href="/' + summitval.peak_slug + '/summits/' + summitval.id + '/"><span style="width: 32px; height: 32px; display: inline-block; color: #ffffff; line-height: 30px; text-align: center; padding-left: 3px; font-size: 10px; font-weight: bold; background-size: 100%; background-image: url({% static 'img/fa-comment_256_0_00b1f2_none.png'%});">' + comments_count + '</span></a></span><span style="margin-left: 40px; ' + photos_style + '"><a href="/' + summitval.peak_slug + '/summits/' + summitval.id + '/"><span style="width: 32px; height: 32px; display: inline-block; color: #ffffff; line-height: 36px; text-align: center; padding-left: 2px; font-size: 10px; font-weight: bold; background-size: 100%; background-image: url({% static 'img/fa-camera_256_0_00b1f2_none.png'%});">' + photos_count + '</span></a></span></span></p><span class="peak-list-highlights"><p></p></span></div></div></div></div>');
                            }

                            //gpx file?
                            if (summitval.gpx_file != 'None' && summitval.gpx_file != '' && staticMapUrl.length > 0) {
                                get_gpx_path('{{ S3_MEDIA_URL }}' + summitval.gpx_file, staticMapUrl, 'map-' + summitval.id, summitval.peak_lat, summitval.peak_long);
                            }

                        } else {

                            //this news is not a summit
                            if (summitval.notice_type == 'new_follower') {

                                var sender_avatar, sender_avatar_big;
                                if (summitval.sender_avatar_url != 'None' && summitval.sender_avatar_url != '') {
                                    sender_avatar = '<img src="{{MEDIA_URL}}' + summitval.sender_avatar_url + '" class="summit-card-user-avatar">&nbsp;&nbsp;';
                                    sender_avatar_big = '{{MEDIA_URL}}' + summitval.sender_avatar_url_big;
                                } else {
                                    sender_avatar = '<img src="{% static 'img/default-user-100x100.png' %}" class="summit-card-user-avatar">&nbsp;&nbsp;';
                                    sender_avatar_big = '{% static 'img/default-user.png' %}';
                                }

                                var receiver_avatar, receiver_avatar_big;
                                if (summitval.receiver_avatar_url != 'None' && summitval.receiver_avatar_url != '') {
                                    receiver_avatar = '<img src="{{MEDIA_URL}}' + summitval.receiver_avatar_url + '" class="summit-card-user-avatar">&nbsp;&nbsp;';
                                    receiver_avatar_big = '{{MEDIA_URL}}' + summitval.receiver_avatar_url_big;
                                } else {
                                    receiver_avatar = '';
                                    receiver_avatar_big = '{% static 'img/default-user.png' %}';
                                }

                                dividerDiv = '<div class="row dark-background-row"><div class="sp-60"></div></div>';

                                var header_border = 'border-bottom: 1px solid #c0c0c0; ';

                                $(newsContainer).append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">' + dividerDiv + '<div class="row" style="' + header_border + 'cursor: pointer;" onmouseover="highlightNews(' + summitval.notice_id + ');" onmouseout="unhighlightNews(' + summitval.notice_id + ');" onclick="viewMember(\'/members/' + summitval.sender + '\');"><div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 summit-card-header" style="background-color: #fff; letter-spacing: 1.0px;"><div class="summit-list-stats summit-card-user-stats hidden-xs hidden-sm" style="float: left;">' + sender_avatar + '<a href="/members/' + summitval.sender + '">' + summitval.sender + '</a> started following you</div><div class="summit-list-stats summit-card-user-stats hidden-lg hidden-md hidden-xs" style="float: left;"><a href="/members/' + summitval.sender + '/">' + summitval.sender + '</a> started following you</div><div class="summit-list-stats news-card-mobile-stats hidden-lg hidden-md hidden-sm"><a href="/members/' + summitval.sender + '">' + summitval.sender + '</a> started following you</div></div></div>');
                                $(newsContainer).append('<div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 hover-photos newsimg-' + summitval.notice_id + '" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + sender_avatar_big + '\'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;"><div class="top-photos"><div><div><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div></div></div>');
                                $(newsContainer).append('<div onclick="openUrl(\'/members/' + summitval.sender + '/badges/\');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-2 peakimg-responsive peakimgdata-responsive hover-cell" style="cursor: pointer; border-bottom: 1px solid #c0c0c0;"><div><div class="profile-header-with-giant-stats">Peaks summited</div><p class="profile-giant-red"><a style="color: ' + summitval.sender_peaks_bagged_color + ';" href="/members/' + summitval.sender + '/badges/">' + summitval.sender_peaks_bagged + '</a></p></div></div>');
                                $(newsContainer).append('<div onclick="openUrl(\'/members/' + summitval.sender + '/badges/\');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-3 hidden-xs hidden-sm peakimg-responsive peakimgdata-responsive hover-cell" style="cursor: pointer; border-bottom: 1px solid #c0c0c0;"><div><div class="profile-header-with-giant-stats">Total summits</div><p class="profile-giant-red"><a style="color: #00B1F2;" href="/members/' + summitval.sender + '/badges/">' + summitval.sender_total_summits + '</a></p></div></div>');
                                if (summitval.sender_days_since_last_summit >= 0) {
                                    $(newsContainer).append('<div onclick="openUrl(\'/members/' + summitval.sender + '/badges/\');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-4 hidden-xs peakimg-responsive peakimgdata-responsive hover-cell" style="cursor: pointer; border-bottom: 1px solid #c0c0c0;"><div><div class="profile-header-with-giant-stats">Days since last summit</div><p class="profile-giant-red"><a style="color: #00B1F2;" href="/members/' + summitval.sender + '/badges/">' + summitval.sender_days_since_last_summit + '</a></p></div></div>');
                                } else {
                                    $(newsContainer).append('<div onclick="openUrl(\'/members/' + summitval.sender + '/badges/\');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-4 hidden-xs peakimg-responsive peakimgdata-responsive hover-cell" style="cursor: pointer; border-bottom: 1px solid #c0c0c0;"><div><div class="profile-header-with-giant-stats">Days since last summit</div><p class="profile-data-missing">no info yet</p></div></div>');
                                }

                                //footer
                                $(newsContainer).append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"><div class="row"><div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="border-top: 1px solid #c0c0c0; height: 70px; background-color: #fff; z-index: 1;"><div><p><span class="summit-list-footer" style="font-weight: 600; line-height: 70px;"><a href="/members/' + summitval.sender + '/"><button style="padding-left: 13px; width: 100%; height: 100%; text-align: left; border: none; color: #00B1F2; font-weight: 600; position: absolute; left: -2px; top: -2px; z-index: 1;" class="btn btn-default btn-see-full-log">SEE PROFILE</button></a></span></div></div></div>');
                                $(newsContainer).append('</div>');

                            } else if (summitval.notice_type == 'new_following_follows') {

                                var sender_avatar, sender_avatar_big;
                                if (summitval.sender_avatar_url != 'None' && summitval.sender_avatar_url != '') {
                                    sender_avatar = '<img src="{{MEDIA_URL}}' + summitval.sender_avatar_url + '" class="summit-card-user-avatar">&nbsp;&nbsp;';
                                    sender_avatar_big = '{{MEDIA_URL}}' + summitval.sender_avatar_url_big;
                                } else {
                                    sender_avatar = '<img src="{% static 'img/default-user-100x100.png' %}" class="summit-card-user-avatar">&nbsp;&nbsp;';
                                    sender_avatar_big = '{% static 'img/default-user.png' %}';
                                }

                                var receiver_avatar, receiver_avatar_big;
                                if (summitval.receiver_avatar_url != 'None' && summitval.receiver_avatar_url != '') {
                                    receiver_avatar = '<img src="{{MEDIA_URL}}' + summitval.receiver_avatar_url + '" class="summit-card-user-avatar">&nbsp;&nbsp;';
                                    receiver_avatar_big = '{{MEDIA_URL}}' + summitval.receiver_avatar_url_big;
                                } else {
                                    receiver_avatar = '';
                                    receiver_avatar_big = '{% static 'img/default-user.png' %}';
                                }

                                dividerDiv = '<div class="row dark-background-row"><div class="sp-60"></div></div>';

                                var header_border = 'border-bottom: 1px solid #c0c0c0; ';

                                $(newsContainer).append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">' + dividerDiv + '<div class="row" style="' + header_border + 'cursor: pointer;" onmouseover="highlightNews(' + summitval.notice_id + ');" onmouseout="unhighlightNews(' + summitval.notice_id + ');" onclick="viewMember(\'/members/' + summitval.sender + '\');"><div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 summit-card-header" style="background-color: #fff; letter-spacing: 1.0px;"><div class="summit-list-stats summit-card-user-stats hidden-xs hidden-sm" style="float: left;">' + sender_avatar + '<a href="/members/' + summitval.sender + '/">' + summitval.sender + '</a> started following <a href="/members/' + summitval.receiver + '/">' + summitval.receiver + '</a></div><div class="summit-list-stats summit-card-user-stats hidden-lg hidden-md hidden-xs" style="float: left;"><a href="/members/' + summitval.sender + '">' + summitval.sender + '</a> started following <a href="/members/' + summitval.receiver + '">' + summitval.receiver + '</a></div><div class="summit-list-stats news-card-mobile-stats hidden-lg hidden-md hidden-sm"><a href="/members/' + summitval.sender + '">' + summitval.sender + '</a> started following <a href="/members/' + summitval.receiver + '">' + summitval.receiver + '</a></div></div></div>');
                                $(newsContainer).append('<div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 hover-photos newsimg-' + summitval.notice_id + '" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + receiver_avatar_big + '\'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;"><div class="top-photos"><div><div><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div></div></div>');
                                $(newsContainer).append('<div onclick="openUrl(\'/members/' + summitval.receiver + '/badges/\');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-2 peakimg-responsive peakimgdata-responsive hover-cell" style="cursor: pointer; border-bottom: 1px solid #c0c0c0;"><div><div class="profile-header-with-giant-stats">Peaks summited</div><p class="profile-giant-red"><a style="color: ' + summitval.receiver_peaks_bagged_color + ';" href="/members/' + summitval.receiver + '/badges/">' + summitval.receiver_peaks_bagged + '</a></p></div></div>');
                                $(newsContainer).append('<div onclick="openUrl(\'/members/' + summitval.receiver + '/badges/\');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-3 hidden-xs hidden-sm peakimg-responsive peakimgdata-responsive hover-cell" style="cursor: pointer; border-bottom: 1px solid #c0c0c0;"><div><div class="profile-header-with-giant-stats">Total summits</div><p class="profile-giant-red"><a style="color: #00B1F2;" href="/members/' + summitval.receiver + '/badges/">' + summitval.receiver_total_summits + '</a></p></div></div>');
                                if (summitval.receiver_days_since_last_summit >= 0) {
                                    $(newsContainer).append('<div onclick="openUrl(\'/members/' + summitval.receiver + '/badges/\');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-4 hidden-xs peakimg-responsive peakimgdata-responsive hover-cell" style="cursor: pointer; border-bottom: 1px solid #c0c0c0;"><div><div class="profile-header-with-giant-stats">Days since last summit</div><p class="profile-giant-red"><a style="color: #00B1F2;" href="/members/' + summitval.receiver + '/badges/">' + summitval.receiver_days_since_last_summit + '</a></p></div></div>');
                                } else {
                                    $(newsContainer).append('<div onclick="openUrl(\'/members/' + summitval.receiver + '/badges/\');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-4 hidden-xs peakimg-responsive peakimgdata-responsive hover-cell" style="cursor: pointer; border-bottom: 1px solid #c0c0c0;"><div><div class="profile-header-with-giant-stats">Days since last summit</div><p class="profile-data-missing">no info yet</p></div></div>');
                                }

                                //footer
                                $(newsContainer).append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"><div class="row"><div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="border-top: 1px solid #c0c0c0; height: 70px; background-color: #fff; z-index: 1;"><div><p><span class="summit-list-footer" style="font-weight: 600; line-height: 70px;"><a href="/members/' + summitval.receiver + '"><button style="padding-left: 13px; width: 100%; height: 100%; text-align: left; border: none; color: #00B1F2; font-weight: 600; position: absolute; left: -2px; top: -2px; z-index: 1;" class="btn btn-default btn-see-full-log">SEE PROFILE</button></a></span></div></div></div>');
                                $(newsContainer).append('</div>');

                            }

                        }

                        counter++;

                    });
                    //show pagination
                    if (parseInt(page) == totalPages) {
                        $('#more-summits-btn').hide();
                    } else {
                        $('#more-summits-btn').show();
                    }
                }
            });

            $("time.timeago").timeago();
            if (page == 1) {
                $('#ajax-data-loading').css('display', 'none');
            } else {
                $('#more-summits-btn').html('See more news');
            }
            loading = false;
        });

    }

    function numberWithCommas(x) {
        return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    function change_like_count(id,count){
        $('#summitlog-like-'+id).html(count+" like");
    }

    {% if user.is_authenticated %}
    function like_summit(id) {
        var likeButton = $('#summitlog-like-'+id);
        var summitID = id;
        var summitlogID = '18';
        if (likeButton.hasClass("login")){
            $.facebox( {ajax:'/accounts/login/?next=/accounts/login_reload/login/'});
        } else {
            if (likeButton.hasClass("liked")) {
                likeButton.removeClass("liked");
                likeButton.addClass("liking");
                var count = $('#summitlog-like-'+summitID).html();
                count = parseInt(count)-1;
                if (count > 0) {
                    $('#summitlog-like-'+summitID).html(count);
                } else {
                    $('#summitlog-like-'+summitID).html('&nbsp;');
                }
                var request = $.ajax({
                  method: "POST",
                  url: "/favorites/remove/",
                  dataType: "json",
                  data: {object_id: summitID, content_type_id: summitlogID}
                });
                request.done(function( data ) {
                  //$('#summitlog-like-'+summitID).html(data.count);
                  likeButton.removeClass("liking");
                });
            } else {
                likeButton.addClass("liked");
                likeButton.addClass("liking");
                var count = $('#summitlog-like-'+summitID).html();
                if (count == '&nbsp;') {
                    count = 1;
                } else {
                    count = parseInt(count)+1;
                }
                $('#summitlog-like-'+summitID).html(count);
                var request = $.ajax({
                  method: "POST",
                  url: "/favorites/add/",
                  dataType: "json",
                  data: {object_id: summitID, content_type_id: summitlogID}
                });
                request.done(function( data ) {
                  //$('#summitlog-like-'+summitID).html(data.count);
                  likeButton.removeClass("liking");
                });
            }
        }
        return false;
    }
    {% else %}
    function like_summit(id) {
        $('#accounts-login').modal('show');
    }
    {% endif %}

    function get_gpx_path(gpx_url, map_url, map_div, peak_lat, peak_lon){
        var gpx_path = '';
        var pointIndex = 1;
        var dist = 0;
        var max_dist = 0;
        var points = [];
        var temp_lat, temp_lng;
        $.ajax({
            type: "GET",
            url: gpx_url,
            dataType: "xml",
            success: function(xml) {
                gpx_path = 'path-4+FC202E-0.5(';
                $(xml).find("trkpt").each(function() {
                    if (pointIndex % 10 == 0) {
                        temp_lng = parseFloat($(this).attr("lon"));
                        temp_lat = parseFloat($(this).attr("lat"));
                        points.push([temp_lat, temp_lng]);
                    }
                    pointIndex++;
                });
                $(xml).find("rtept").each(function() {
                    if (pointIndex % 10 == 0) {
                        temp_lng = parseFloat($(this).attr("lon"));
                        temp_lat = parseFloat($(this).attr("lat"));
                        points.push([temp_lat, temp_lng]);
                    }
                    pointIndex++;
                });
                var encoded_polyline = encode_polyline(points);
                gpx_path = gpx_path + encodeURIComponent(encoded_polyline) + '),';
                $("."+map_div).css("background-image", "url('" + map_url.replace("/static/url","/static/"+gpx_path+"url") + "')");
            }
        });
    }

    function get_gpx_distance(lat1, lon1, lat2, lon2) {
        var radlat1 = Math.PI * lat1/180
        var radlat2 = Math.PI * lat2/180
        var theta = lon1-lon2
        var radtheta = Math.PI * theta/180
        var dist = Math.sin(radlat1) * Math.sin(radlat2) + Math.cos(radlat1) * Math.cos(radlat2) * Math.cos(radtheta);
        dist = Math.acos(dist)
        dist = dist * 180/Math.PI
        dist = dist * 60 * 1.1515
        return dist
    }

    function viewSummit(url) {
        window.location.href = url;
    }

    function highlightPhotos(summitid) {
        $('.summitimg-'+summitid).addClass('hover-photos-hover');
    }

    function unhighlightPhotos(summitid) {
        $('.summitimg-'+summitid).removeClass('hover-photos-hover');
    }

    function highlightNews(noticeid) {
        $('.newsimg-'+noticeid).addClass('hover-photos-hover');
    }

    function unhighlightNews(noticeid) {
        $('.newsimg-'+noticeid).removeClass('hover-photos-hover');
    }

    function init() {

        var vars = [], hash, type;
        var q = document.URL.split('#')[1];
        if(q != undefined){
            q = q.split('&');
            for(var i = 0; i < q.length; i++){
                hash = q[i].split('=');
                vars.push(hash[1]);
                vars[hash[0]] = hash[1];
            }
        }

        if (vars['type'] != undefined) {
            type = vars['type'];
        } else {
            type = 'all';
        }

        loadNews(type, 1, 'summits-list');

    }

    $(document).ready(function() {

        init();

        $('#more-summits-btn').on('click', function() {
            loadNews(newsType, nextPage, 'more-summits-list');
            nextPage++;
        });

        var viewport_height = $(window).height();
        var viewport_width = $(window).width();
        var footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
        var data_loading_margin = (viewport_height - footer_height) / 3;
        $('.data-loading').css('margin-top', data_loading_margin);
        $(window).resize(function() {
            viewport_height = $(window).height();
            viewport_width = $(window).width();
            footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
            data_loading_margin = (viewport_height - footer_height) / 3;
            $('.data-loading').css('margin-top', data_loading_margin);
        });

    });

</script>

{% endblock %}