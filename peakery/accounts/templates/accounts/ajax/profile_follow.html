{% if not user.is_authenticated %}
    <a href="{% url "login_and_reload" bagger.username %}" id = "follow-{{ bagger.id }}" rel="facebox" class="btn" style="float: right; padding:  8px 16px;">Follow</a>
    {% else %}
    {% if user != bagger %}
        {% if not following %}
            <a href="javascript:void(0);" id = "follow-{{ bagger.id }}" class="peakeryFollowButton followButton btn btn-secondary" style="font-size: 12px; float: right; width: 165px;">Follow</a>
            <a href="javascript:void(0);" id = "unfollow-{{ bagger.id }}" class="peakeryFollowButton unfollowButton btn btn-secondary" style="font-size: 12px; display: none; float: right; width: 165px;">You are following</a>
        {% else %}
            <a href="javascript:void(0);" id = "follow-{{ bagger.id }}" class="peakeryFollowButton followButton btn btn-secondary" style="font-size: 12px; display:none; float: right; width: 165px;">Follow</a>
            <a href="javascript:void(0);" id = "unfollow-{{ bagger.id }}" class="peakeryFollowButton unfollowButton btn btn-secondary" style="font-size: 12px; float: right; width: 165px;">You are following</a>
        {% endif %}
        <script type="text/javascript">
            $(function(){
                $('#unfollow-{{ bagger.id }}').hover(
                        function(){
                            $(this).removeClass('unfollowButton').addClass('unfollowButtonHoverProfile').text('Unfollow').css('background-color','#f24100').css('border-color','#f24100');
                        },
                        function(){
                            $(this).removeClass('unfollowButtonHoverProfile').addClass('unfollowButton').text('You are following').css('background-color','#00b330').css('border-color','#00b330');
                        });
            });
        </script>
        <script type="text/javascript">
            $(document).ready(function(){

                $("#unfollow-{{ bagger.id }}").on('click',function (){
                    var btn = $("#unfollow-{{ bagger.id }}");
                    btn.html('<i class="fa fa-spinner fa-spin"></i>');
                    $.post('{% url "unfollow_user" bagger.id %}',function(data){
                        if (data == "True"){
                            btn.html('Follow');
                            var url = $("#ssi_follower").attr("lang");
                            $("#ssi_follower").load(url);
                            $("#unfollow-{{ bagger.id }}").toggle();
                            $("#follow-{{ bagger.id }}").toggle();
                        }
                    });
                });

                $("#follow-{{ bagger.id }}").on('click',function (){
                    var btn = $("#follow-{{ bagger.id }}");
                    btn.html('<i class="fa fa-spinner fa-spin"></i>');
                    $.post('{% url "follow_user" bagger.id %}',function(data){
                        if (data == "True"){
                            btn.html('You are following');
                            var url = $("#ssi_follower").attr("lang");
                            $("#ssi_follower").load(url);
                            $("#unfollow-{{ bagger.id }}").toggle();
                            $("#follow-{{ bagger.id }}").toggle();
                        }
                    });
                });

            });
            //# sourceURL=/accounts/profile_follow/{{ bagger.id }}/
        </script>
    {% endif %}
{% endif %}
