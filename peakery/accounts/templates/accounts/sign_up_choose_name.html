{% load widget_tweaks %}
<div id="o"></div>
<div id="registerSuperWrapper">
    <div id="register_container" class="signUpWithEmail signUpWrapper" {% if next %}{% endif %}>
        {{ form.non_field_errors }}
        <form action="{% url "sign_up_choose_name" step %}?action={{ action }}{% if next %}&next={{ next }}{% endif %}" method="post" id="register_form{{ generated_id }}" class=" {{ window }}" style="text-align: center; margin-bottom: 0;">
            <span class="a" style="margin: 25px auto 20px auto;">
                {{ form.username|attr:"placeholder:username"|attr:"class:userData" }}
            </span>
            {% if form.username.errors %}
                <span class="formError"> {{ form.username.errors }}</span>
            {% endif %}
            <div class="clearfix btnWrapper">
                <input type="submit" class="btn" value="Save">
                <a id="next" href="{{ next }}" rel="facebox"></a>
            </div>
        </form>
    </div>
</div>

<a id="find_friends" href="{% url "sign_up_welcome" %}" rel="facebox"></a>


<script language="javascript">
    $(document).ready(function(){

        $('a#next').facebox();

        $('form#register_form{{ generated_id }}').submit(function() {
            form = $(this);
            $(this).ajaxSubmit({
                target: 'div#registerSuperWrapper',
                success:    function(e) {
                    try {
                        if (e == 'Welcome to peakery!') {
                            setTimeout(function(){
                              $('#accounts-choose-username').modal('hide');
                                window.location.reload(true);
                            }, 1000);
                        }
                    } catch(except){
                        //console.log(except);
                    }
                }
            });
            return false;
        });
    });
</script>