# Generated by Django 4.2.11 on 2024-07-29 16:11

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('accounts', '0005_alter_usercontributorstats_user_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='usercontributorstats',
            name='user',
            field=models.ForeignKey(editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='contributor_stats', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='userlocationstats',
            name='user',
            field=models.ForeignKey(editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='loc_stats', to=settings.AUTH_USER_MODEL),
        ),
        migrations.Alter<PERSON>ield(
            model_name='userrelation',
            name='from_user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='userrelation',
            name='to_user',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='relations', to=settings.AUTH_USER_MODEL),
        ),
    ]
