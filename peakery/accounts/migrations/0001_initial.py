# Generated by Django 3.2 on 2024-01-30 11:04

import datetime
from django.conf import settings
import django.contrib.gis.db.models.fields
from django.db import migrations, models
import django.db.models.deletion
import peakery.django_extensions.db.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('cities', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SocialCredentials',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('site_id', models.CharField(max_length=255)),
                ('site_screen_name', models.CharField(blank=True, max_length=255, null=True)),
                ('site_token', models.CharField(blank=True, max_length=255, null=True)),
                ('site_token_secret', models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                'verbose_name_plural': 'social credentials',
            },
        ),
        migrations.CreateModel(
            name='TwitterCredentials',
            fields=[
                ('socialcredentials_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='accounts.socialcredentials')),
            ],
            options={
                'verbose_name_plural': 'Twitter credentials',
            },
            bases=('accounts.socialcredentials',),
        ),
        migrations.CreateModel(
            name='UserRelation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('to_uid', models.CharField(blank=True, max_length=255, null=True)),
                ('first_name', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name', models.CharField(blank=True, max_length=100, null=True)),
                ('email_db', models.EmailField(blank=True, max_length=254, null=True)),
                ('source', models.SmallIntegerField(choices=[(0, 'Facebook'), (1, 'Gmail'), (2, 'Peakery'), (3, 'Twitter'), (4, 'Input')])),
                ('active', models.BooleanField(default=True)),
                ('from_user', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to=settings.AUTH_USER_MODEL)),
                ('to_user', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='relations', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='UserPdfReport',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.IntegerField(blank=True, null=True)),
                ('file', models.URLField(null=True)),
                ('created', peakery.django_extensions.db.fields.CreationDateTimeField(blank=True, default=datetime.datetime.now, editable=False)),
                ('date_created', models.DateField(blank=True, null=True)),
                ('user', models.ForeignKey(editable=False, on_delete=django.db.models.deletion.DO_NOTHING, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='UserLocationStats',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('count_all', models.IntegerField()),
                ('count_365', models.IntegerField()),
                ('count_30', models.IntegerField()),
                ('count_all_unique', models.IntegerField()),
                ('count_365_unique', models.IntegerField()),
                ('count_30_unique', models.IntegerField()),
                ('country', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='cities.country')),
                ('region', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='cities.region')),
                ('user', models.ForeignKey(editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='loc_stats', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='UserContributorStats',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('peak_info_corrections_count', models.IntegerField()),
                ('peak_info_corrections_rank', models.IntegerField()),
                ('peak_photos_count', models.IntegerField()),
                ('peak_photos_rank', models.IntegerField()),
                ('peak_count', models.IntegerField()),
                ('peak_rank', models.IntegerField()),
                ('summit_routes_count', models.IntegerField()),
                ('summit_routes_rank', models.IntegerField()),
                ('date_updated', peakery.django_extensions.db.fields.ModificationDateTimeField(blank=True, default=datetime.datetime.now, editable=False)),
                ('user', models.ForeignKey(editable=False, on_delete=django.db.models.deletion.DO_NOTHING, related_name='contributor_stats', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Person',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('about_me', models.TextField(blank=True, null=True)),
                ('website', models.URLField(blank=True, null=True)),
                ('location', django.contrib.gis.db.models.fields.PointField(blank=True, default=None, null=True, srid=4326)),
                ('location_name', models.CharField(blank=True, max_length=255, null=True)),
                ('favorite_item', models.URLField(blank=True, null=True)),
                ('next_item_goal', models.URLField(blank=True, null=True)),
                ('last_summit_date', models.DateField(blank=True, null=True)),
                ('ranking_all', models.IntegerField(blank=True, null=True)),
                ('ranking_365', models.IntegerField(blank=True, null=True)),
                ('ranking_30', models.IntegerField(blank=True, null=True)),
                ('facebook_page', models.URLField(blank=True, null=True)),
                ('twitter_username', models.CharField(blank=True, max_length=50, null=True)),
                ('flicker_page', models.URLField(blank=True, null=True)),
                ('instagram_username', models.CharField(blank=True, max_length=50, null=True)),
                ('location_area_1', models.CharField(blank=True, max_length=255, null=True)),
                ('location_area_2', models.CharField(blank=True, max_length=255, null=True)),
                ('was_edited_info', models.BooleanField(default=False)),
                ('latest_contribution', models.DateTimeField(blank=True, null=True)),
                ('old_id', models.IntegerField(blank=True, null=True)),
                ('pdf_created', models.BooleanField(default=False)),
                ('social_auth_uuid', models.CharField(blank=True, max_length=255, null=True)),
                ('created', peakery.django_extensions.db.fields.CreationDateTimeField(blank=True, default=datetime.datetime.now, editable=False)),
                ('modified', peakery.django_extensions.db.fields.ModificationDateTimeField(blank=True, default=datetime.datetime.now, editable=False)),
                ('signup_source', models.CharField(blank=True, max_length=255, null=True)),
                ('firebase_uid', models.CharField(blank=True, max_length=50, null=True)),
                ('location_city', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='people_city', to='cities.city')),
                ('location_country', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='people_country', to='cities.country')),
                ('location_region', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='people_region', to='cities.region')),
                ('pdf', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='accounts.userpdfreport')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.DO_NOTHING, to=settings.AUTH_USER_MODEL)),
                ('twitter_credentials', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.DO_NOTHING, to='accounts.twittercredentials')),
            ],
            options={
                'verbose_name_plural': 'people',
            },
        ),
    ]
