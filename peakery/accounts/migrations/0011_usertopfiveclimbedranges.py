# Generated by Django 4.2.11 on 2025-03-15 09:27

import datetime
from django.db import migrations, models
import peakery.django_extensions.db.fields


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0010_usertopfiveclimbedregions'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserTopFiveClimbedRanges',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_id', models.IntegerField(db_index=True)),
                ('range', models.CharField(max_length=50)),
                ('peak_count', models.IntegerField()),
                ('created', peakery.django_extensions.db.fields.CreationDateTimeField(blank=True, default=datetime.datetime.now, editable=False)),
                ('modified', peakery.django_extensions.db.fields.ModificationDateTimeField(blank=True, default=datetime.datetime.now, editable=False)),
            ],
        ),
    ]
