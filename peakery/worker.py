import os


from redis import Redis
from rq import Worker, Queue, Connection
from urllib.parse import urlparse

import django
django.setup()

# Define queues in priority order (first has highest priority)
listen = ['photos', 'default']

redis_url = urlparse(os.environ.get('REDISQUEUE_URL'))


if __name__ == '__main__':
    with Connection(connection=Redis(redis_url.hostname, redis_url.port, password=redis_url.password)):
        worker = Worker(queues=[Queue(name) for name in listen])
        worker.work()
