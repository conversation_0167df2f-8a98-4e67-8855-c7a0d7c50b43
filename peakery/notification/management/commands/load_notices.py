
import logging
from django.conf import settings
from django.utils.translation import gettext_noop as _
from django.core.management.base import NoArgsCommand
from django.db.models import signals

from notification.engine import send_all

class Command(NoArgsCommand):
    help = "Emit queued notices."
    
    def handle_noargs(self, **options):

        print "Creating..."
        if "notification" in settings.INSTALLED_APPS:
            from notification import models as notification

            #notifications
            notification.create_notice_type("new_private_message","I'm sent a private message","A new private message has been received")
            # notifications in user objects
            notification.create_notice_type("new_comment_summitlog","Someone comments on my summit log","A new comment in your summit log")
            notification.create_notice_type("new_like_summitlog","Someone likes my summit log","A new like in your summit log")
            notification.create_notice_type("new_follower","I'm followed by someone new","A new follower")
            # notification in following objects
            notification.create_notice_type("new_comment_following_summitlog","New comment in other user summit log","New comment in other user summit log")
            notification.create_notice_type("new_like_following_summitlog","New Like in other user summit log","New Like in other user summit log")
            notification.create_notice_type("new_following_follows","a following is new follower","a following is new follower")
            notification.create_notice_type("new_following_summitlog","a following user summited a peak","a following user summited")
            #notification for own user
            notification.create_notice_type("new_comment_summitlog_me","New comment in other user summit log","New comment in other user summit log")
            notification.create_notice_type("new_comment_me_summitlog_me","New comment from me in my own summit log","New comment from me in my own summit log")
            notification.create_notice_type("new_like_summitlog_me","New Like in other user summit log","New Like in other user summit log")
            notification.create_notice_type("new_follower_me","I am following someone new","I am following someone new")
            #notification for fellows on summit logs
            notification.create_notice_type("user_added_to_summit_log","I am added to a summit log","Somebody added me to a summit log")
            notification.create_notice_type("user_anonymous_added_to_summit_log","I am added to a summit log (anonymous users)","Somebody added me to a summit log (anonymous users)")
            notification.create_notice_type("user_contact_added_to_summit_log","I'm added to a summit log","Somebody added me to a summit log (user contacts users)")
            #notification for own summitlog
            notification.create_notice_type("new_like_own_summitlog","I liked my own summit log","I liked my own summit log")
        else:
            print "Skipping creation of NoticeTypes as notification app not found"
        print "Finished."
    