{% extends "notification/base.html" %}

{% load i18n %}
{#  {% load mediaurl %}  #}

{% block extrahead %}
    <link rel="stylesheet" href="{{ MEDIA_URL }}css/notices/notices.css" type="text/css" media="screen" charset="utf-8" />
{% endblock %}

{% block head_title %}{% trans "Notification Settings" %}{% endblock %}

{% block content %}

    <div class="noticeSettings">
        <h1>{% trans "Change password settings" %}</h1>
    <p><a href="{% url "change_password" %}">Change Password</a></p>

    <br>
    <br>
        <h1>{% trans "Notification Settings" %}</h1>

        {% url "acct_email" as email_url %}
        {% if user.email %}
            <p>
                {% trans "Primary email" %}:<br />
                <b>{{ user.email }}</b><br />
            </p>
        {% else %}
            <p>
                <span class="warning">{% trans "Note" %}</span>:
                {% blocktrans %}You do not have a verified email address to which notices can be sent. You can add one by going to <a href="{{ email_url }}">Account</a>.{% endblocktrans %}
            </p>
        {% endif %}



        <form method="POST" action=""> {# doubt this easy to do in uni-form #}
            {% csrf_token %}
            <div class="row title">
                <div class="col">{% trans "Notification Type" %}</div>
                {% for header in notice_settings.column_headers %}
                    <div class="col">{{ header }}</div>
                {% endfor %}
            </div><!-- end row -->
            {% for row in notice_settings.rows %}
                <div class="row">
                    <div class="col col_title">
                        <p>{% trans row.notice_type.display %}</p>
                        {% if row.notice_type.description %}<span>{% trans row.notice_type.description %}</span>{% endif %}
                    </div><!-- end col -->
                    {% for cell in row.cells %}
                        <div class="col">
                            <input type="checkbox" class="checkbox" name="{{ cell.0 }}" {% if cell.1 %}checked="yes"{% endif %}/>
                        </div><!-- end col -->
                    {% endfor %}
                </div><!-- end row -->
            {% endfor %}
            <div class="row">
                <td><input type="submit" value="{% trans "Change" %}" /></td>
            </div><!-- end row -->
        </form>
        
        <script type="text/javascript">
            $(function(){
                $('.col_title').click(function(){
                    var $checkbox = $(this).next(".col").children('input.checkbox');
                    if($checkbox.is(':checked')){
                        $checkbox.attr('checked', false);
                    }
                    else{
                        $checkbox.attr('checked', true);
                    }
                });
            });// end jQuery function
        </script>
        
    </div>
{% endblock %}