{% load avatar_tags %}
{% load thumbnail %}
<div class="newWrapper clearfix">
    <div class="leftCol">
        <a href="{{ user.get_profile.get_absolute_url }}" class="avatarLink" style="text-decoration:none;">
            {% avatar user 80 %}
        </a>
    </div>
    <div class="centerCol wComment">
        <span>
            <a href="{{ user.get_profile.get_absolute_url }}" class="username">You</a> like <a href="{{ user_like.get_profile.get_absolute_url }}" class="itemName">{{ user_like.username }}'s</a> summit log for <a href="{{ item.get_absolute_url }}" class="itemName">{{ item.name }}</a>
        </span>
        <div class="comment">
            {% if summit.log %}
                <p>{{ summit.log|truncatewords:35 }}</p>
            {% endif %}
        </div>
    </div>
    <div class="rightCol">
        {% with summit.summit_photos.all as summit_photos %}
            {% if summit_photos %}
                {% for p in summit_photos %}
                    <a href="{{ summit.get_absolute_url }}"><img src="{% thumbnail p.image 120x80 crop="-0,0" %}"/></a>
                {% endfor %}
            {% else %}
                <a href="{{ item.get_absolute_url }}"><img src="{% thumbnail item.get_thumbnail 120x80 crop="-0,0" %}" /></a>
            {% endif %}
        {% endwith %}
    </div>

</div>