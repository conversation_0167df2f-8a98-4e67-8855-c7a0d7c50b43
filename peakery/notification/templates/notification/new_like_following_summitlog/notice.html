{% load avatar_tags %}
<br/>
<div class="newWrapper clearfix">
    <div class="leftCol">
        <a href="{{ user.profile.get_absolute_url }}" class="avatarLink" style="text-decoration:none;">
            {% avatar user 80 %}
        </a>
    </div>
    <div class="centerCol wComment">
        <span>
            <a href="{{ user.profile.get_absolute_url }}" class="username">{{ user.username }}</a> liked <a href="{{ user_like.profile.get_absolute_url }}" class="username">{{ user_like.username }}'s</a> summit log for <a href="{{ item.get_absolute_url }}" class="itemName">{{ item.name }}</a>
        </span>
        <div class="comment">
            {% if summit.log %}
                <p>{{ summit.log|truncatewords:35 }}</p>
            {% endif %}
        </div>
    </div>
    <div class="rightCol">
        {% with summit.summit_photos.all as summit_photos %}
        {% if summit_photos %}
            {% for p in summit_photos %}
                <a href="{{ summit.get_absolute_url }}"><img src="{{ p.image.thumb_320 }}"/></a>
            {% endfor %}
        {% else %}
            <a href="{{ item.get_absolute_url }}"><img src="{{ item.get_thumbnail_320 }}" /></a>
        {% endif %}
        {% endwith %}
    </div>
</div>
<br/>