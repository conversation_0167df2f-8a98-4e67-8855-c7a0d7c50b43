{% extends "notification/base.html" %}

{% load humanize i18n timezone_filters mediaurl gravatar un_or_sn thumbnail %}

{% block extrahead %}
    <link rel="stylesheet" href="{% media_url_prefix %}css/notices/notices.css" type="text/css" media="screen" charset="utf-8" />
{% endblock %}

{% block head_title %}{% trans "Notices" %}{% endblock %}

{% block content %}

    <h1>{% trans "Notices" %}</h1>

    {% autopaginate notices %}

    {% if notices %}

    {# TODO: get timezone support working with regroup #}


    <div class="noticeList">
        {% for notice in notices %}
        {% with notice.is_unseen as is_unseen %}
            <div id="notice_{{ notice.id }}" class="noticeContainer {% if is_unseen %}unreadNotice{% endif %}">

                <div class="wrapper">

                    {% if not is_unseen %}
                        {% if notice.notice_type.label == 'new_request_restricted' %}
                            <a href="{% url "edit_settings" %}#project-members">
                        {% endif %}
                        {% if notice.notice_type.label == 'new_request_relating_project' %}
                            <a href="{% url "edit_settings" %}#linked-projects">
                        {% endif %}
                    {% endif %}

                    <span>
                        <div class="leftCol">
                            
                        </div><!-- end leftCol -->
                        <div class="centerCol">
                            {{ notice.message|safe }}
                            <span class="date">{{ notice.added|date:"D d M Y" }}</span>
                        </div><!-- end centerCol -->
                        {% if is_unseen %}
                            <div class="rightCol">
                                <img src="{% media_url_prefix %}images/notices/green-alert.png" />
                            </div><!-- end rightCol -->
                        {% endif %}
                    </span>

                    {% if not is_unseen %}
                        {% if notice.notice_type.label == 'new_request_restricted' %}

                            </a>

                        {% endif %}
                    {% endif %}


                </div>
            </div><!-- end noticeContainer -->
            {% endwith %}
        {% endfor %}
    </div><!-- end noticeList -->
    
    {% paginate %}
    
    {% else %}
        <p>{% trans "No notices." %}</p>
    {% endif %}

{% endblock %}