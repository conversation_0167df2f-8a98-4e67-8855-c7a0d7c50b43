from django.urls import reverse
from django.shortcuts import render, get_object_or_404
from django.http import HttpResponseRedirect, Http404, HttpResponse
from django.template import RequestContext
from django.contrib.auth.decorators import login_required
from django.contrib.syndication.views import Feed, FeedDoesNotExist
from django.core.exceptions import ObjectDoesNotExist
from peakery.notification.models import *
from peakery.notification.decorators import basic_auth_required, simple_basic_auth_callback
from peakery.notification.feeds import NoticeUserFeed
import warnings

class LegacyFeed(Feed):
    """Provided for backwards compatibility."""
    def __init__(self, slug, request):
        import warnings
        warnings.warn('The syndication feeds.Feed class is deprecated. Please '
                      'use the new class based view API.',
                      category=DeprecationWarning)

        self.slug = slug
        self.request = request
        self.feed_url = getattr(self, 'feed_url', None) or request.path
        self.title_template = self.title_template or ('feeds/%s_title.html' % slug)
        self.description_template = self.description_template or ('feeds/%s_description.html' % slug)

    def get_object(self, bits):
        return None

    def get_feed(self, url=None):
        """
        Returns a feedgenerator.DefaultFeed object, fully populated, for
        this feed. Raises FeedDoesNotExist for invalid parameters.
        """
        if url:
            bits = url.split('/')
        else:
            bits = []
        try:
            obj = self.get_object(bits)
        except ObjectDoesNotExist:
            raise FeedDoesNotExist
        return super(Feed, self).get_feed(obj, self.request)

def feed(request, url, feed_dict=None):
    """Provided for backwards compatibility."""
    warnings.warn('The syndication feed() view is deprecated. Please use the '
                  'new class based view API.',
                  category=DeprecationWarning)

    if not feed_dict:
        raise Http404("No feeds are registered.")

    try:
        slug, param = url.split('/', 1)
    except ValueError:
        slug, param = url, ''

    try:
        f = feed_dict[slug]
    except KeyError:
        raise Http404("Slug %r isn't registered." % slug)

    # Backwards compatibility within the backwards compatibility;
    # Feeds can be updated to be class-based, but still be deployed
    # using the legacy feed view. This only works if the feed takes
    # no arguments (i.e., get_object returns None). Refs #14176.
    if not issubclass(f, LegacyFeed):
        instance = f()
        instance.feed_url = getattr(f, 'feed_url', None) or request.path
        instance.title_template = f.title_template or ('feeds/%s_title.html' % slug)
        instance.description_template = f.description_template or ('feeds/%s_description.html' % slug)

        return instance(request)

    try:
        feedgen = f(slug, request).get_feed(param)
    except FeedDoesNotExist:
        raise Http404("Invalid feed parameters. Slug %r is valid, but other parameters, or lack thereof, are not." % slug)

    response = HttpResponse(mimetype=feedgen.mime_type)
    feedgen.write(response, 'utf-8')
    return response

@basic_auth_required(realm='Notices Feed', callback_func=simple_basic_auth_callback)
def feed_for_user(request):
    """
    An atom feed for all unarchived :model:`notification.Notice`s for a user.
    """
    url = "feed/%s" % request.user.username
    return feed(request, url, {
        "feed": NoticeUserFeed,
    })


@login_required
def notices(request):
    """
    The main notices index view.

    Template: :template:`notification/notices.html`

    Context:

        notices
            A list of :model:`notification.Notice` objects that are not archived
            and to be displayed on the site.
    """
    notices = Notice.objects.notices_for(request.user, on_site=True)

    return render_to_response("notification/notices.html", {
        "notices": notices,
        }, context_instance=RequestContext(request))


@login_required
def notice_settings(request):
    """
    The notice settings view.

    Template: :template:`notification/notice_settings.html`

    Context:

        notice_types
            A list of all :model:`notification.NoticeType` objects.

        notice_settings
            A dictionary containing ``column_headers`` for each ``NOTICE_MEDIA``
            and ``rows`` containing a list of dictionaries: ``notice_type``, a
            :model:`notification.NoticeType` object and ``cells``, a list of
            tuples whose first value is suitable for use in forms and the second
            value is ``True`` or ``False`` depending on a ``request.POST``
            variable called ``form_label``, whose valid value is ``on``.
    """
    notice_types = NoticeType.objects.all()
    for exclude_notification in NOTIFICATIONS_CONFIG['exclude']:
        notice_types = notice_types.exclude(label = exclude_notification)
    settings_table = []
    for notice_type in notice_types:
        settings_row = []
        for medium_id, medium_display in NOTICE_MEDIA:
            form_label = "%s_%s" % (notice_type.label, medium_id)
            setting = get_notification_setting(request.user, notice_type, medium_id)
            if request.method == "POST":
                if request.POST.get(form_label) == "on":
                    if not setting.send:
                        setting.send = True
                        setting.save()
                else:
                    if setting.send:
                        setting.send = False
                        setting.save()
            settings_row.append((form_label, setting.send))
        settings_table.append({"notice_type": notice_type, "cells": settings_row})

    notice_settings = {
        "column_headers": [medium_display for medium_id, medium_display in NOTICE_MEDIA],
        "rows": settings_table,
        }

    return render_to_response("notification/notice_settings.html", {
        "notice_types": notice_types,
        "notice_settings": notice_settings,
        }, context_instance=RequestContext(request))


@login_required
def single(request, id, mark_seen=True):
    """
    Detail view for a single :model:`notification.Notice`.
    
    Template: :template:`notification/single.html`
    
    Context:
    
        notice
            The :model:`notification.Notice` being viewed
    
    Optional arguments:
    
        mark_seen
            If ``True``, mark the notice as seen if it isn't
            already.  Do nothing if ``False``.  Default: ``True``.
    """
    notice = get_object_or_404(Notice, id=id)
    if request.user == notice.recipient:
        if mark_seen and notice.unseen:
            notice.unseen = False
            notice.save()
        return render_to_response("notification/single.html", {
            "notice": notice,
        }, context_instance=RequestContext(request))
    raise Http404


@login_required
def archive(request, noticeid=None, next_page=None):
    """
    Archive a :model:`notices.Notice` if the requesting user is the
    recipient or if the user is a superuser.  Returns a
    ``HttpResponseRedirect`` when complete.
    
    Optional arguments:
    
        noticeid
            The ID of the :model:`notices.Notice` to be archived.
        
        next_page
            The page to redirect to when done.
    """
    if noticeid:
        try:
            notice = Notice.objects.get(id=noticeid)
            if request.user == notice.recipient or request.user.is_superuser:
                notice.archive()
            else:   # you can archive other users' notices
                    # only if you are superuser.
                return HttpResponseRedirect(next_page)
        except Notice.DoesNotExist:
            return HttpResponseRedirect(next_page)
    return HttpResponseRedirect(next_page)


@login_required
def delete(request, noticeid=None, next_page=None):
    """
    Delete a :model:`notices.Notice` if the requesting user is the recipient
    or if the user is a superuser.  Returns a ``HttpResponseRedirect`` when
    complete.
    
    Optional arguments:
    
        noticeid
            The ID of the :model:`notices.Notice` to be archived.
        
        next_page
            The page to redirect to when done.
    """
    if noticeid:
        try:
            notice = Notice.objects.get(id=noticeid)
            if request.user == notice.recipient or request.user.is_superuser:
                notice.delete()
            else:   # you can delete other users' notices
                    # only if you are superuser.
                return HttpResponseRedirect(next_page)
        except Notice.DoesNotExist:
            return HttpResponseRedirect(next_page)
    return HttpResponseRedirect(next_page)


@login_required
def mark_all_seen(request):
    """
    Mark all unseen notices for the requesting user as seen.  Returns a
    ``HttpResponseRedirect`` when complete. 
    """
    
    for notice in Notice.objects.notices_for(request.user, unseen=True):
        notice.unseen = False
        notice.save()
    return HttpResponseRedirect(reverse("notification_notices"))
