from peakery.notification.models import Notice



from django import template
register = template.Library()

def unseen_notifications_count(user):
    counter = Notice.objects.filter(recipient=user, unseen=True).count()
    if counter == 0:
        return 'Notifications'
    elif counter == 1:
        return 'Notifications (%i)' % counter
    elif counter > 100:
        return 'Notifications (100+)'
    else:
        return 'Notifications (%i)' % counter

register.simple_tag(unseen_notifications_count)


""" einztein specific """
def inbox_count(user):

    notices = Notice.objects.filter(recipient=user, unseen=True).count()

    sum = notices
    if sum == 0:
        return ''
    else:
        return " (%s)"%sum

register.simple_tag(inbox_count)