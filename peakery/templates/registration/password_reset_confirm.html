{% extends "base.html" %}

{% block title %}
Enter new password
{% endblock %}

{% block content %}

    <style type="text/css">

        #content-body {
            margin-top: 0px;
        }
        #content-holder {
            height: 100%;
        }

        form span.a {
            margin-bottom: 20px;
        }

        ul.errorlist {
            color: #ff0000;
            text-align: left !important;
        }

    </style>

<div class="container" style="background-color: #f6f6f6; color: #333; margin-bottom: 55px; height: 100%; padding-top: 20px;">

{% if validlink %}

<h2 style="font-size: 32px;">Reset your password</h2>

<form action="" method="post">
    {% csrf_token %}
    {{ form.new_password1.errors }}
    <span class="a" id="password1_secret_span" style="background-color: #f6f6f6;">
    <input class="password" id="id_new_password1" name="new_password1" type="password" style="background-color: #fff;">
    <span class="holder" id="id_new_password1_holder" style="top: 11px;">New Password</span>
    </span>
    <span class="a" style="display: none; background-color: #f6f6f6;" id="password1_inblank_span">
    <input class="password" id="id_password1_in_blank" style="background-color: #fff;">
    <span class="holder" id="id_password1_in_blank_holder" style="top: 11px;">New Password</span>
    </span>
    {{ form.new_password2.errors }}
    <span class="a" id="password2_secret_span" style="background-color: #f6f6f6;">
    <input class="password" id="id_new_password2" name="new_password2" type="password" style="background-color: #fff;">
    <span class="holder" id="id_new_password2_holder" style="top: 11px;">Confirm Password</span>
    </span>
    <span class="a" style="display: none; background-color: #f6f6f6;" id="password2_inblank_span">
    <input class="password" id="id_password2_in_blank" style="background-color: #fff;">
    <span class="holder" id="id_password2_in_blank_holder" style="top: 11px;">Confirm Password</span>
    </span>
    <span id="showPasswordWrapper">
    <input type="checkbox" id="showPassword"><label for="showPassword" style="color: #666; margin-left: 10px;">show password</label>
    </span>
    <br>
    <p><input type="submit" value="Change my password" class="btn set2 input" style="margin-top: 20px;"></p>
</form>

<script type="text/javascript">
    $(function(){
        $('span.a input').each(function(){
            if ($(this).val() != ""){
                $(this).parent().children('.holder').hide();
            }

        });
        $('span.a span.holder').click(function(){
            $(this).parent().find('input').focus();
        });
        $('#id_new_password1').keydown(function(){
            $('#id_new_password1_holder').hide(100);
            $('#id_password1_in_blank_holder').hide(100);
        });
        $('#id_new_password1').keyup(function(){
            if($(this).val() == ""){
                $('#id_new_password1_holder').show(100);
                $('#id_password1_in_blank_holder').show(100);
            }
        });
        $('#id_new_password1').blur(function(){
            if($(this).val() == ""){
                $('#id_new_password1_holder').show(100);
                $('#id_password1_in_blank_holder').show(100);
            }
        });
        $('#id_new_password2').keydown(function(){
            $('#id_new_password2_holder').hide(100);
            $('#id_password2_in_blank_holder').hide(100);
        });
        $('#id_new_password2').keyup(function(){
            if($(this).val() == ""){
                $('#id_new_password2_holder').show(100);
                $('#id_password2_in_blank_holder').show(100);
            }
        });
        $('#id_new_password2').blur(function(){
            if($(this).val() == ""){
                $('#id_new_password2_holder').show(100);
                $('#id_password2_in_blank_holder').show(100);
            }
        });
        $('#id_password1_in_blank').keydown(function(){
            $('#id_new_password1_holder').hide(100);
            $('#id_password1_in_blank_holder').hide(100);
        });
        $('#id_password1_in_blank').keyup(function(){
            if($(this).val() == ""){
                $('#id_new_password1_holder').show(100);
                $('#id_password1_in_blank_holder').show(100);
            }
        });
        $('#id_password1_in_blank').blur(function(){
            if($(this).val() == ""){
                $('#id_new_password1_holder').show(100);
                $('#id_password1_in_blank_holder').show(100);
            }
        });
        $('#id_password2_in_blank').keydown(function(){
            $('#id_new_password2_holder').hide(100);
            $('#id_password2_in_blank_holder').hide(100);
        });
        $('#id_password2_in_blank').keyup(function(){
            if($(this).val() == ""){
                $('#id_new_password2_holder').show(100);
                $('#id_password2_in_blank_holder').show(100);
            }
        });
        $('#id_password2_in_blank').blur(function(){
            if($(this).val() == ""){
                $('#id_new_password2_holder').show(100);
                $('#id_password2_in_blank_holder').show(100);
            }
        });
    });

    $(document).ready(function(){

        $('#showPassword').click(function()
        {
            //checked
            var checked = $('#showPassword:checked').val() != undefined;
            if (checked){
                $("#password1_secret_span").hide();
                $("#password1_inblank_span").show();
                $("#password2_secret_span").hide();
                $("#password2_inblank_span").show();
            }else{
                $("#password1_secret_span").show();
                $("#password1_inblank_span").hide();
                $("#password2_secret_span").show();
                $("#password2_inblank_span").hide();
            }
        });

        $('#id_new_password1').keyup(function(){
            $("#id_password1_in_blank").val($(this).val());
        });

        $("#id_password1_in_blank").keyup(function(){
            $("#id_new_password1").val($(this).val());
        });

        $('#id_new_password2').keyup(function(){
            $("#id_password2_in_blank").val($(this).val());
        });

        $("#id_password2_in_blank").keyup(function(){
            $("#id_new_password2").val($(this).val());
        });

    });

</script>

{% else %}

<h2 style="font-size: 32px;">Password reset unsuccessful</h2>

<p>The password reset link was invalid, possibly because it has already been used.
    Please request a new password reset.</p>

{% endif %}

</div>

{% endblock %}

