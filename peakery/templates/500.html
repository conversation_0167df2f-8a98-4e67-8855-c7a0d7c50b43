{% load static %}
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" xml:lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>

    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /><script type="text/javascript">(window.NREUM||(NREUM={})).loader_config={licenseKey:"fbd14c050b",applicationID:"144587088"};window.NREUM||(NREUM={}),__nr_require=function(n,e,t){function r(t){if(!e[t]){var i=e[t]={exports:{}};n[t][0].call(i.exports,function(e){var i=n[t][1][e];return r(i||e)},i,i.exports)}return e[t].exports}if("function"==typeof __nr_require)return __nr_require;for(var i=0;i<t.length;i++)r(t[i]);return r}({1:[function(n,e,t){function r(){}function i(n,e,t){return function(){return o(n,[u.now()].concat(f(arguments)),e?null:this,t),e?void 0:this}}var o=n("handle"),a=n(4),f=n(5),c=n("ee").get("tracer"),u=n("loader"),s=NREUM;"undefined"==typeof window.newrelic&&(newrelic=s);var p=["setPageViewName","setCustomAttribute","setErrorHandler","finished","addToTrace","inlineHit","addRelease"],d="api-",l=d+"ixn-";a(p,function(n,e){s[e]=i(d+e,!0,"api")}),s.addPageAction=i(d+"addPageAction",!0),s.setCurrentRouteName=i(d+"routeName",!0),e.exports=newrelic,s.interaction=function(){return(new r).get()};var m=r.prototype={createTracer:function(n,e){var t={},r=this,i="function"==typeof e;return o(l+"tracer",[u.now(),n,t],r),function(){if(c.emit((i?"":"no-")+"fn-start",[u.now(),r,i],t),i)try{return e.apply(this,arguments)}catch(n){throw c.emit("fn-err",[arguments,this,n],t),n}finally{c.emit("fn-end",[u.now()],t)}}}};a("actionText,setName,setAttribute,save,ignore,onEnd,getContext,end,get".split(","),function(n,e){m[e]=i(l+e)}),newrelic.noticeError=function(n,e){"string"==typeof n&&(n=new Error(n)),o("err",[n,u.now(),!1,e])}},{}],2:[function(n,e,t){function r(n,e){var t=n.getEntries();t.forEach(function(n){"first-paint"===n.name?a("timing",["fp",Math.floor(n.startTime)]):"first-contentful-paint"===n.name&&a("timing",["fcp",Math.floor(n.startTime)])})}function i(n){if(n instanceof c&&!s){var e,t=Math.round(n.timeStamp);e=t>1e12?Date.now()-t:f.now()-t,s=!0,a("timing",["fi",t,{type:n.type,fid:e}])}}if(!("init"in NREUM&&"page_view_timing"in NREUM.init&&"enabled"in NREUM.init.page_view_timing&&NREUM.init.page_view_timing.enabled===!1)){var o,a=n("handle"),f=n("loader"),c=NREUM.o.EV;if("PerformanceObserver"in window&&"function"==typeof window.PerformanceObserver){o=new PerformanceObserver(r);try{o.observe({entryTypes:["paint"]})}catch(u){}}if("addEventListener"in document){var s=!1,p=["click","keydown","mousedown","pointerdown","touchstart"];p.forEach(function(n){document.addEventListener(n,i,!1)})}}},{}],3:[function(n,e,t){function r(n,e){if(!i)return!1;if(n!==i)return!1;if(!e)return!0;if(!o)return!1;for(var t=o.split("."),r=e.split("."),a=0;a<r.length;a++)if(r[a]!==t[a])return!1;return!0}var i=null,o=null,a=/Version\/(\S+)\s+Safari/;if(navigator.userAgent){var f=navigator.userAgent,c=f.match(a);c&&f.indexOf("Chrome")===-1&&f.indexOf("Chromium")===-1&&(i="Safari",o=c[1])}e.exports={agent:i,version:o,match:r}},{}],4:[function(n,e,t){function r(n,e){var t=[],r="",o=0;for(r in n)i.call(n,r)&&(t[o]=e(r,n[r]),o+=1);return t}var i=Object.prototype.hasOwnProperty;e.exports=r},{}],5:[function(n,e,t){function r(n,e,t){e||(e=0),"undefined"==typeof t&&(t=n?n.length:0);for(var r=-1,i=t-e||0,o=Array(i<0?0:i);++r<i;)o[r]=n[e+r];return o}e.exports=r},{}],6:[function(n,e,t){e.exports={exists:"undefined"!=typeof window.performance&&window.performance.timing&&"undefined"!=typeof window.performance.timing.navigationStart}},{}],ee:[function(n,e,t){function r(){}function i(n){function e(n){return n&&n instanceof r?n:n?c(n,f,o):o()}function t(t,r,i,o){if(!d.aborted||o){n&&n(t,r,i);for(var a=e(i),f=v(t),c=f.length,u=0;u<c;u++)f[u].apply(a,r);var p=s[y[t]];return p&&p.push([b,t,r,a]),a}}function l(n,e){h[n]=v(n).concat(e)}function m(n,e){var t=h[n];if(t)for(var r=0;r<t.length;r++)t[r]===e&&t.splice(r,1)}function v(n){return h[n]||[]}function g(n){return p[n]=p[n]||i(t)}function w(n,e){u(n,function(n,t){e=e||"feature",y[t]=e,e in s||(s[e]=[])})}var h={},y={},b={on:l,addEventListener:l,removeEventListener:m,emit:t,get:g,listeners:v,context:e,buffer:w,abort:a,aborted:!1};return b}function o(){return new r}function a(){(s.api||s.feature)&&(d.aborted=!0,s=d.backlog={})}var f="nr@context",c=n("gos"),u=n(4),s={},p={},d=e.exports=i();d.backlog=s},{}],gos:[function(n,e,t){function r(n,e,t){if(i.call(n,e))return n[e];var r=t();if(Object.defineProperty&&Object.keys)try{return Object.defineProperty(n,e,{value:r,writable:!0,enumerable:!1}),r}catch(o){}return n[e]=r,r}var i=Object.prototype.hasOwnProperty;e.exports=r},{}],handle:[function(n,e,t){function r(n,e,t,r){i.buffer([n],r),i.emit(n,e,t)}var i=n("ee").get("handle");e.exports=r,r.ee=i},{}],id:[function(n,e,t){function r(n){var e=typeof n;return!n||"object"!==e&&"function"!==e?-1:n===window?0:a(n,o,function(){return i++})}var i=1,o="nr@id",a=n("gos");e.exports=r},{}],loader:[function(n,e,t){function r(){if(!x++){var n=E.info=NREUM.info,e=l.getElementsByTagName("script")[0];if(setTimeout(s.abort,3e4),!(n&&n.licenseKey&&n.applicationID&&e))return s.abort();u(y,function(e,t){n[e]||(n[e]=t)}),c("mark",["onload",a()+E.offset],null,"api");var t=l.createElement("script");t.src="https://"+n.agent,e.parentNode.insertBefore(t,e)}}function i(){"complete"===l.readyState&&o()}function o(){c("mark",["domContent",a()+E.offset],null,"api")}function a(){return O.exists&&performance.now?Math.round(performance.now()):(f=Math.max((new Date).getTime(),f))-E.offset}var f=(new Date).getTime(),c=n("handle"),u=n(4),s=n("ee"),p=n(3),d=window,l=d.document,m="addEventListener",v="attachEvent",g=d.XMLHttpRequest,w=g&&g.prototype;NREUM.o={ST:setTimeout,SI:d.setImmediate,CT:clearTimeout,XHR:g,REQ:d.Request,EV:d.Event,PR:d.Promise,MO:d.MutationObserver};var h=""+location,y={beacon:"bam.nr-data.net",errorBeacon:"bam.nr-data.net",agent:"js-agent.newrelic.com/nr-1153.min.js"},b=g&&w&&w[m]&&!/CriOS/.test(navigator.userAgent),E=e.exports={offset:f,now:a,origin:h,features:{},xhrWrappable:b,userAgent:p};n(1),n(2),l[m]?(l[m]("DOMContentLoaded",o,!1),d[m]("load",r,!1)):(l[v]("onreadystatechange",i),d[v]("onload",r)),c("mark",["firstbyte",f],null,"api");var x=0,O=n(6)},{}],"wrap-function":[function(n,e,t){function r(n){return!(n&&n instanceof Function&&n.apply&&!n[a])}var i=n("ee"),o=n(5),a="nr@original",f=Object.prototype.hasOwnProperty,c=!1;e.exports=function(n,e){function t(n,e,t,i){function nrWrapper(){var r,a,f,c;try{a=this,r=o(arguments),f="function"==typeof t?t(r,a):t||{}}catch(u){d([u,"",[r,a,i],f])}s(e+"start",[r,a,i],f);try{return c=n.apply(a,r)}catch(p){throw s(e+"err",[r,a,p],f),p}finally{s(e+"end",[r,a,c],f)}}return r(n)?n:(e||(e=""),nrWrapper[a]=n,p(n,nrWrapper),nrWrapper)}function u(n,e,i,o){i||(i="");var a,f,c,u="-"===i.charAt(0);for(c=0;c<e.length;c++)f=e[c],a=n[f],r(a)||(n[f]=t(a,u?f+i:i,o,f))}function s(t,r,i){if(!c||e){var o=c;c=!0;try{n.emit(t,r,i,e)}catch(a){d([a,t,r,i])}c=o}}function p(n,e){if(Object.defineProperty&&Object.keys)try{var t=Object.keys(n);return t.forEach(function(t){Object.defineProperty(e,t,{get:function(){return n[t]},set:function(e){return n[t]=e,e}})}),e}catch(r){d([r])}for(var i in n)f.call(n,i)&&(e[i]=n[i]);return e}function d(e){try{n.emit("internal-error",e)}catch(t){}}return n||(n=i),t.inPlace=u,t.flag=a,t}},{}]},{},["loader"]);</script><script type="text/javascript">window.NREUM||(NREUM={});NREUM.info={"applicationTime":61,"errorBeacon":"bam.nr-data.net","beacon":"bam.nr-data.net","agent":"","transactionName":"NAdabRBXXBVXV0BYXQ1NfkwMVUYPWVobWEYGD0sXFF9XEUUOQlhXFA==","queueTime":3,"licenseKey":"fbd14c050b","applicationID":"144587088"}</script>
    <meta name="google" content="notranslate">
    <meta http-equiv="Content-Language" content="en">

    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0">
    <meta name="title" content=" | peakery" />
    <meta name="description" content="Your basecamp for the world’s mountains. Find great peaks to climb, tackle Peak Challenges, track your summits, and follow the mountain adventures of friends." />
    <link rel="image_src" href="">
    <link rel="shortcut icon" href="https://s3-us-west-1.amazonaws.com/peakery-media/img/icn/favicon.ico" />
    <link rel="apple-touch-icon" sizes="76x76" href="{% static '' %}img/apple-touch-icon-76x76.png">
    <link rel="apple-touch-icon" sizes="120x120" href="{% static '' %}img/apple-touch-icon-120x120.png">
    <link rel="apple-touch-icon" sizes="152x152" href="{% static '' %}img/apple-touch-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="180x180" href="{% static '' %}img/apple-touch-icon-180x180.png">
    <meta name="apple-mobile-web-app-title" content="Peakery">



    <title>500 System Error | peakery</title>

    <script type="text/javascript">

        var _gaq = _gaq || [];
        _gaq.push(['_setCustomVar',
            1,             // This custom var is set to slot #1.  Required parameter.
            'User Type',   // The name of the custom variable.  Required parameter.

            'Visitor',      // Sets the value of "User Type" to "Member" or "Visitor" depending on status.  Required parameter.

            2             // Sets the scope to session-level.  Optional parameter.
        ]);
        _gaq.push(['_setAccount', 'UA-********-1']);
        _gaq.push(['_trackPageview']);

        (function() {
            var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
            ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
            var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
        })();

    </script>


    <link rel="stylesheet" href="{% static '' %}css/0f2533e978d5-2.01.css" type="text/css" media="screen" />
    <link href="{% static '' %}css/explore.min.css" rel="stylesheet" type="text/css" media="screen" />
    <link href="{% static '' %}css/bootstrap.min.css" rel="stylesheet" type="text/css" media="screen" />
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.3.1/css/all.css" integrity="sha384-mzrmE5qonljUremFsqc01SB46JvROS7bZs3IO2EmfFsd15uHvIt+Y8vEf7N7fWAU" crossorigin="anonymous">
    <link href="{% static '' %}css/bootstrap-toggle.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{% static '' %}css/blueimp-gallery.min.css">
    <link rel="stylesheet" href="{% static '' %}css/bootstrap-image-gallery.css">
    <link rel="stylesheet" href="{% static '' %}css/nouislider.min.css">
    <link rel="stylesheet" href="{% static '' %}css/drawer.min.css">
    <link rel="stylesheet" href="{% static '' %}css/formValidation/formValidation.min.css">
    <link rel="stylesheet" href="{% static '' %}css/jquery-ui.min.css">

    <link rel="stylesheet" type="text/css" href="{% static '' %}vendor/ModalWindowEffects/css/default.min.css" />
    <link rel="stylesheet" type="text/css" href="{% static '' %}vendor/ModalWindowEffects/css/component.min.css" />
    
    <link href="{% static '' %}vendor/s3.fine-uploader/fine-uploader-new.min.css" rel="stylesheet">

    <link href="{% static '' %}css/peakery-custom-3.09.css" rel="stylesheet" type="text/css" media="screen" />
    <link href="{% static '' %}css/peakery-custom-3.10-base-extras.css" rel="stylesheet" type="text/css" media="screen" />

    <style type="text/css">

        .mobile-logo-title {left: 50px;}

    </style>
    
    <script src="{% static '' %}vendor/s3.fine-uploader/s3.fine-uploader.min.js"></script>
    
    <script type="text/template" id="qq-gpx-template">
        <div class="qq-uploader-selector qq-uploader" qq-drop-area-text="" style="max-height: 40px; background-color: transparent; border: none; padding: 0px; min-height: 0px; margin-top: -10px; overflow-x: hidden;">
        <div class="qq-upload-drop-area-selector qq-upload-drop-area" qq-hide-dropzone>
        <span class="qq-upload-drop-area-text-selector"></span>
        </div>
        <ul class="qq-upload-list-selector qq-upload-list" aria-live="polite" aria-relevant="additions removals" style="max-height: none; display: none;">
        <li style="padding: 5px;">
        <div style="display: flex; display: -webkit-flex;">
        <div class="qq-thumbnail-wrapper" style="position: relative; align-content: stretch;">
        <div>
        <img class="qq-thumbnail-selector" qq-max-size="300" qq-server-scale style="margin-right: 0px;">
        </div>
        </div>
        </div>
        </li>
        </ul>
        <div id="gpx-upload-button" class="qq-upload-button-selector btn btn-secondary" style="height: 45px; line-height: 25px; font-size: 14px; margin-top: 0px; padding-top: 10px; background-color: transparent; border: none; color: rgb(204, 204, 204); position: relative; overflow: hidden; direction: ltr;">
        <div id="gpx-upload-button-div"><div class="log-climb-dropdown-icon-div"><i class="fas fa-file-upload"></i></div><div class="log-climb-dropdown-label-div" style="margin-top: -2px; letter-spacing: 0.5px;">Upload GPX file</div></div>
        </div>
        <span class="qq-drop-processing-selector qq-drop-processing">
        <span>Processing dropped files...</span>
        <span class="qq-drop-processing-spinner-selector qq-drop-processing-spinner"></span>
        </span>
        
        <dialog class="qq-alert-dialog-selector">
        <div class="qq-dialog-message-selector"></div>
        <div class="qq-dialog-buttons">
        <button type="button" class="qq-cancel-button-selector btn btn-secondary">Close</button>
        </div>
        </dialog>
        
        <dialog class="qq-confirm-dialog-selector">
        <div class="qq-dialog-message-selector"></div>
        <div class="qq-dialog-buttons">
        <button type="button" class="qq-cancel-button-selector btn btn-secondary">No</button>
        <button type="button" class="qq-ok-button-selector btn btn-secondary">Yes</button>
        </div>
        </dialog>
        
        <dialog class="qq-prompt-dialog-selector">
        <div class="qq-dialog-message-selector"></div>
        <input type="text">
        <div class="qq-dialog-buttons">
        <button type="button" class="qq-cancel-button-selector btn btn-secondary">Cancel</button>
        <button type="button" class="qq-ok-button-selector btn btn-secondary">Ok</button>
        </div>
        </dialog>
        </div>
        </script>

    <script src="{% static '' %}js/jquery.min.js"></script>
    <script src="{% static '' %}js/jquery-ui.min.js"></script>
    <script src="{% static '' %}js/jquery.browser.min.js"></script>
    <script src="{% static '' %}js/jquery.autocomplete.min.js"></script>
    <script type="text/javascript" src="{% static '' %}js/bootstrap.min.js"></script>
    <script src="{% static '' %}js/jquery.timeago.min.js"></script>
    <script src="{% static '' %}js/bootstrap-toggle.min.js"></script>
    <script src="{% static '' %}js/blueimp-gallery.min.js"></script>
    <script src="{% static '' %}js/jquery.blueimp-gallery.min.js"></script>
    <script src="{% static '' %}js/bootstrap-image-gallery.min.js"></script>
    <script src="{% static '' %}js/jquery-dateFormat.min.js"></script>
    <script src="{% static '' %}js/trunk8.min.js"></script>
    <script src="{% static '' %}js/truncate.min.js"></script>
    <script src="{% static '' %}js/nouislider.min.js"></script>
    <script src="{% static '' %}js/autosize.min.js"></script>
    <script src="{% static '' %}js/formValidation/formValidation.min.js"></script>
    <script src="{% static '' %}js/formValidation/framework/bootstrap.min.js"></script>
    <script src="{% static '' %}js/drawer.min.js"></script>
    <script src="{% static '' %}js/jquery.floatThead.min.js"></script>

    <script src="{% static '' %}vendor/ModalWindowEffects/js/modalEffects.js"></script>
    <script src="{% static '' %}vendor/ModalWindowEffects/js/modernizr.custom.js"></script>
    <script src="{% static '' %}vendor/ModalWindowEffects/js/classie.min.js"></script>
    <script src="{% static '' %}vendor/ModalWindowEffects/js/cssParser.min.js"></script>

    <script type="text/javascript" src="{% static '' %}js/jquery.form.min.js"></script>
    <script type="text/javascript" src="{% static '' %}js/facebox.min.js?v=0.1"></script>
    <script type="text/javascript" src="{% static '' %}js/peakery.min.js?v=7"></script>
    <script type="text/javascript" src="{% static '' %}js/jquery.popup.min.js?v=7"></script>
    <script type="text/javascript" charset="utf-8">
        $(document).ready(function() {
            $("span.ssi").each(function(k,v){
                var url = $(v).attr("lang");
                $(v).load(url);
            });
        });
    </script>
    <script type="text/javascript" src="{% static '' %}js/jquery.placeholder.min.js?v=7"></script>
    <script type="text/javascript">
        $(function(){
            $('input, textarea').placeholder();
        });
    </script>
    <script type="text/javascript" src="{% static '' %}js/jquery.elastic.source.min.js?v=7"></script>
    <script type="text/javascript" src="{% static '' %}js/jquery.OnEnter.min.js?v=7"></script>
    <script type="text/javascript" src="{% static '' %}js/jquery.scrollTo-1.4.2-min.js"></script>

    <script type="text/javascript" src="{% static '' %}js/peakery-shasta-3.05-base-extras.min.js"></script>
    <script type="text/javascript" src="{% static '' %}js/peakery-shasta-3.05.min.js"></script>
    <script type="text/javascript" src="{% static '' %}js/peakery-shasta-3.05-base-admin.min.js"></script>



</head>

<body class="drawer drawer--right">

<div id="fb-root"></div>
<script>
    window.fbAsyncInit = function() {
        FB.init({appId: '1044997928847258', status: true, cookie: true,
            xfbml: true, oauth: true});
    };
    (function() {
        var e = document.createElement('script'); e.async = true;
        e.src = 'https://connect.facebook.net/en_US/all.js';
        document.getElementById('fb-root').appendChild(e);
    }());
</script>



<nav class="drawer-nav" role="navigation">
    <ul class="drawer-menu drawer-menu--right">
        
        <a class="navbar-primary" id="gpx-file-1" style="padding-left: 0px; display: none;"><div class="log-climb-dropdown-icon-div"><i class="fas fa-file-upload"></i></div><div class="log-climb-dropdown-label-div">Log with GPX</div></a>

        <li id="slideout-nav-peakery-li" class="slideout-li" style="margin-top: -8px; text-align: center;">
            <img style="margin-top: -3px;" src="{% static '' %}img/navbar-logo-inactive.png" width="24" alt="peakery" title="peakery" /><span style="font-size: 20px; color: #FFFFFF; letter-spacing: 0.54px; padding-left: 10px;">peakery</span>
        </li>

        <li id="slideout-nav-join-login-li" class="nav-hidden slideout-li logged-out-nav">
            <div style="width: 50%; padding-right: 10px; float: left; margin-top: -3px;">
                <a id="slideout-nav-log-climb-btn" class="btn btn-primary" data-toggle="modal" data-target="#accounts-login">Log a climb</a>
            </div>
            <div style="width: 50%; padding-left: 10px; float: left; margin-top: -3px;">
                <a id="slideout-nav-login-btn" class="btn btn-secondary join-peakery">Join/Login</a>
            </div>
        </li>

        <li id="slideout-nav-log-climb-alerts-li" class="nav-hidden slideout-li logged-in-nav">
            <div style="width: 50%; padding-right: 10px; float: left; margin-top: -3px;">
                <a id="slideout-nav-log-a-climb-btn" class="btn btn-primary" href="https://peakery.com/peaks/log_climb/">Log a climb</a>
            </div>
            <div style="width: 50%; padding-left: 10px; float: left; margin-top: -3px;">
                <a id="slideout-nav-alerts-btn" class="btn btn-secondary"><i style="margin-right: 5px;" class="fa fa-bell"></i><span class="nav-hidden nav-member-news-count"></span></a></a>
            </div>
        </li>

        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" href="https://peakery.com/latest/">
                <div class="slideout-links-icon-div"><i class="far fa-clock"></i></div><div class="slideout-links-label-div">Latest</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" href="https://peakery.com/map/">
                <div class="slideout-links-icon-div"><i class="fa fa-map"></i></div><div class="slideout-links-label-div">Peak Map</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" href="https://peakery.com/peaks/">
                <div class="slideout-links-icon-div"><i class="fas fa-list"></i></div><div class="slideout-links-label-div">Peak List</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" href="https://peakery.com/world-mountains/">
                <div class="slideout-links-icon-div"><i class="fa fa-globe-americas"></i></div><div class="slideout-links-label-div">Peaks by Region</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" href="https://peakery.com/challenges/">
                <div class="slideout-links-icon-div"><i class="fa fa-trophy"></i></div><div class="slideout-links-label-div">Peak Challenges</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" href="https://peakery.com/members/">
                <div class="slideout-links-icon-div"><i class="fa fa-users"></i></div><div class="slideout-links-label-div">Members</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" href="https://peakery.com/top-contributors/">
                <div class="slideout-links-icon-div"><i class="fa fa-newspaper"></i></div><div class="slideout-links-label-div">Contributors</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <div class="slideout-links-icon-div">&nbsp;</div>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" href="https://peakery.com/about/">
                <div class="slideout-links-icon-div"><i class="fa fa-award"></i></div><div class="slideout-links-label-div">About</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" target="_blank" onclick="event.stopPropagation();" href="http://blog.peakery.com/">
                <div class="slideout-links-icon-div"><i class="fa fa-info-circle"></i></div><div class="slideout-links-label-div">Blog</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" target="_blank" onclick="event.stopPropagation();" href="http://www.facebook.com/peakery">
                <div class="slideout-links-icon-div"><i class="fab fa-facebook"></i></div><div class="slideout-links-label-div">Facebook</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" target="_blank" onclick="event.stopPropagation();" href="http://www.instagram.com/peakerycom">
                <div class="slideout-links-icon-div"><i class="fab fa-instagram"></i></div><div class="slideout-links-label-div">Instagram</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" target="_blank" onclick="event.stopPropagation();" href="http://twitter.com/peakery">
                <div class="slideout-links-icon-div"><i class="fab fa-twitter-square"></i></div><div class="slideout-links-label-div">Twitter</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" href="https://peakery.com/terms/">
                <div class="slideout-links-icon-div"><i class="fa fa-align-left"></i></div><div class="slideout-links-label-div">Terms</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" href="https://peakery.com/privacy/">
                <div class="slideout-links-icon-div"><i class="fa fa-user-lock"></i></div><div class="slideout-links-label-div">Privacy</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" href="mailto:<EMAIL>">
                <div class="slideout-links-icon-div"><i class="fa fa-envelope"></i></div><div class="slideout-links-label-div">Contact us</div>
            </a>
        </li>

    </ul>
</nav>

<main id="main-content" role="main">

    <div id="darkness"></div>
    <div class="md-overlay"></div>

    <div id="content-holder" style="background-color:#333;">

        <!-- Fixed navbar -->
        <nav class="navbar navbar-inverse navbar-fixed-top">
            <div class="container">
                <div class="navbar-header">
                    <div class="hidden-sm hidden-md hidden-lg" style="right: 60px; position: absolute;">
                        <a style="display: none;" id="mobile-nav-alerts" class="dropdown-toggle btn btn-primary">
                            <div class="mobile-nav-alerts-bell-div bell-with-alerts"><i id="mobile-nav-alerts-bell" class="fa fa-bell"></i></div>
                            <div class="mobile-nav-alerts-count-div nav-hidden news_icon_text nav-member-news-count bell-with-alerts" id="mobile-nav-alerts-count"></div>
                        </a>
                    </div>
                    <button style="color: #fff; font-size: 22px;" type="button" class="drawer-toggle drawer-hamburger hidden-sm hidden-md hidden-lg">
                        <i class="fa fa-bars" aria-hidden="true" ></i>
                    </button>
                    <div class="logo mobile-logo hidden-sm hidden-md hidden-lg" style="position: fixed; top: 0px; left: 0px; z-index: 3;">
                        <a class="navbar-brand" href="https://peakery.com/">
                            <img id="mobile-nav-logo"  src="{% static '' %}img/navbar-logo-inactive.png" width="24" alt="peakery" title="peakery" />
                        </a>
                    </div>
                    <div class="logo hidden-xs">
                        <a class="navbar-brand" href="https://peakery.com/">
                            <img id="main-nav-logo" src="{% static '' %}img/navbar-logo-inactive.png" width="24" alt="peakery" title="peakery" />
                        </a>
                        <a class="navbar-brand-latest" href="https://peakery.com/latest/">
                            <div id="nav-logo-tagline">Latest</div>
                        </a>
                    </div>
                    <div class="mobile-logo-title" style="color: #fff; position: absolute; top: 0px; height: 50px; z-index: 3;">
                        <h1 class="visible-xs mobile-logo-tagline">peakery</h1>
                    </div>
                </div>
                <div id="navbar" class="collapse navbar-collapse">
                    <ul style="cursor: default;" class="nav nav-justified">
                        <li id="navbar-link-map-onnav">
                            <a id="navbar-map-link" class="navbar-primary" href="https://peakery.com/map/">Map</a>
                        </li>
                        <li class="dropdown peaks-dropdown hidden-xs" id="navbar-link-peaks">
                            <a id="nav-peaks-dropdown-link" class="dropdown-toggle btn btn-primary">Peaks <i class="fa fa-angle-down" style="font-size: 12px;"></i></a>
                            <ul style="display: none;" class="dropdown-menu nav-peaks-dropdown">
                                <li class="navbar-li-link" id="navbar-link-map"><div class="nav-peaks-dropdown-tab"></div><a class="navbar-primary" href="https://peakery.com/map/"><div class="nav-peaks-dropdown-icon-div"><i class="fa fa-map"></i></div><div class="nav-peaks-dropdown-label-div">Map</div></a></li>
                                <li class="navbar-li-link" id="navbar-link-peaks"><div class="nav-peaks-dropdown-tab"></div><a class="navbar-primary" href="https://peakery.com/peaks/"><div class="nav-peaks-dropdown-icon-div"><i class="fa fa-list"></i></div><div class="nav-peaks-dropdown-label-div">List</div></a></li>
                                <li class="navbar-li-link" id="navbar-link-regions"><a class="navbar-primary" href="https://peakery.com/world-mountains/"><div class="nav-peaks-dropdown-icon-div"><i class="fa fa-globe-americas"></i></div><div class="nav-peaks-dropdown-label-div">Regions</div></a></li>
                                <li class="navbar-li-link" id="navbar-link-challenges"><a class="navbar-primary" href="https://peakery.com/challenges/"><div class="nav-peaks-dropdown-icon-div"><i class="fa fa-trophy"></i></div><div class="nav-peaks-dropdown-label-div">Challenges</div></a></li>
                                <li class="nav-hidden logged-in-nav navbar-li-link" id="navbar-link-add-peak"><a class="navbar-primary" href="https://peakery.com/peaks/add/"><div class="nav-peaks-dropdown-icon-div"><i class="fa fa-plus"></i></div><div class="nav-peaks-dropdown-label-div">Add missing peak</div></a></li>
                                <li class="nav-hidden logged-out-nav navbar-li-link" id="navbar-link-fake-add-peak"><a class="navbar-primary join-peakery"><div class="nav-peaks-dropdown-icon-div"><i class="fa fa-plus"></i></div><div class="nav-peaks-dropdown-label-div">Add missing peak</div></a></li>
                            </ul>
                        </li>
                        <li id="navbar-link-challenges-onnav">
                            <a id="navbar-challenges-link" class="navbar-primary" href="https://peakery.com/challenges/">Challenges</a>
                        </li>
                        <li class="dropdown more-dropdown hidden-xs" id="navbar-link-more">
                            <a id="nav-more-dropdown-link" class="dropdown-toggle btn btn-primary">More <i class="fa fa-angle-down" style="font-size: 12px;"></i></a>
                            <ul style="display: none;" class="dropdown-menu nav-more-dropdown">
                                <li class="navbar-li-link" id="navbar-link-members"><div class="nav-more-dropdown-tab"></div><a class="nav-member-more-link" href="https://peakery.com/members/"><div class="nav-more-dropdown-icon-div"><i class="fa fa-users"></i></div><div class="nav-more-dropdown-label-div">Members</div></a></li>
                                <li class="navbar-li-link" id="navbar-link-contributors"><a class="nav-member-more-link" href="https://peakery.com/top-contributors/"><div class="nav-more-dropdown-icon-div"><i class="fa fa-award"></i></div><div class="nav-more-dropdown-label-div">Contributors</div></a></li>
                                <li class="navbar-li-link" id="navbar-link-about"><a class="nav-member-more-link" href="https://peakery.com/about/"><div class="nav-more-dropdown-icon-div"><i class="fa fa-info-circle"></i></div><div class="nav-more-dropdown-label-div">About</div></a></li>
                                <li class="navbar-li-link" id="navbar-link-blog"><a class="nav-member-more-link" target="_blank" onclick="event.stopPropagation();" href="http://blog.peakery.com/"><div class="nav-more-dropdown-icon-div"><i class="fa fa-newspaper"></i></div><div class="nav-more-dropdown-label-div">Blog</div></a></li>
                                <li class="navbar-li-link" id="navbar-link-facebook"><a class="nav-member-more-link" target="_blank" onclick="event.stopPropagation();" href="http://www.facebook.com/peakery"><div class="nav-more-dropdown-icon-div"><i class="fab fa-facebook-square"></i></div><div class="nav-more-dropdown-label-div">Facebook</div></a></li>
                                <li class="navbar-li-link" id="navbar-link-twitter"><a class="nav-member-more-link" target="_blank" onclick="event.stopPropagation();" href="http://twitter.com/peakery"><div class="nav-more-dropdown-icon-div"><i class="fab fa-twitter-square"></i></div><div class="nav-more-dropdown-label-div">Twitter</div></a></li>
                                <li class="navbar-li-link" id="navbar-link-instagram"><a class="nav-member-more-link" target="_blank" onclick="event.stopPropagation();" href="http://www.instagram.com/peakerycom"><div class="nav-more-dropdown-icon-div"><i class="fab fa-instagram"></i></div><div class="nav-more-dropdown-label-div">Instagram</div></a></li>
                                <li class="navbar-li-link" id="navbar-link-terms"><a class="nav-member-more-link" href="https://peakery.com/terms/"><div class="nav-more-dropdown-icon-div"><i class="fa fa-align-left"></i></div><div class="nav-more-dropdown-label-div">Terms</div></a></li>
                                <li class="navbar-li-link" id="navbar-link-privacy"><a class="nav-member-more-link" href="https://peakery.com/privacy/"><div class="nav-more-dropdown-icon-div"><i class="fa fa-user-lock"></i></div><div class="nav-more-dropdown-label-div">Privacy</div></a></li>
                                <li class="navbar-li-link" id="navbar-link-contact"><a class="nav-member-more-link" href="mailto:<EMAIL>"><div class="nav-more-dropdown-icon-div"><i class="fa fa-envelope"></i></div><div class="nav-more-dropdown-label-div">Contact us</div></a></li>
                            </ul>
                        </li>
                        <li class="nav-hidden logged-out-nav" id="navbar-link-login">
                            <a id="navbar-login-link" class="navbar-primary" data-toggle="modal" data-target="#accounts-login">Login</a>
                        </li>
                        <li class="nav-hidden logged-out-nav" id="navbar-link-join">
                            <a id="navbar-join-link" class="navbar-secondary join-peakery">Join</a>
                        </li>
                        <li class="nav-hidden logged-out-nav hidden-xs navbar-log-climb-li">
                            <a id="fake-log-your-climb" class="btn btn-primary join-peakery">Log a climb</a>
                        </li>
                        <li class="nav-hidden logged-in-nav hidden-xs navbar-log-climb-li">
                            <a id="nav-log-your-climb" class="dropdown-toggle btn btn-primary">Log a climb</a>
                            <ul style="display: none;" class="dropdown-menu log-climb-dropdown">
                                <li class="navbar-li-link" id="log-climb-log-manually"><a class="navbar-primary" href="https://peakery.com/peaks/log_climb/"><div class="log-climb-dropdown-icon-div"><i class="fas fa-pen-square"></i></div><div class="log-climb-dropdown-label-div">Log manually</div></a></li>
                            </ul>
                        </li>
                    </ul>
                </div><!--/.nav-collapse -->

            </div>
        </nav>

        <!-- Begin page content -->
        <div id="content-body" class="">

            <div id="main">


                <style type="text/css">

                    #content-body {
                        margin-top: 0px;
                    }

                </style>

                <div class="container" style="color: #fff; margin-bottom: 55px;" id="flatpage">
                    <h1>500 System Error</h1>
                    <h3>Something went wrong in our server. Please email us details about what you were trying to do and we’ll investigate a fix. <EMAIL></h3>
                </div>

            </div>



        </div> <!-- /content-body -->

    </div> <!-- /content-holder -->

    <div class="message-modal modal fade" id="message-modal" tabindex="-1" role="dialog" aria-labelledby="message-modal-label">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="message-modal-label"></h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div id="message-modal-body" class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="margin-bottom: 5px; font-size: 16px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div style="padding-left: 0px; z-index: 99999;" class="mobile-alerts-modal modal fade" id="mobile-alerts-modal" tabindex="-1" role="dialog" aria-labelledby="mobile-alerts-modal-label">
        <div class="modal-dialog" style="width: 100%; height: 100%; margin-top: 0px;" role="document">
            <div class="modal-content" style="height: 100%;">
                <div class="modal-header">
                    <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="mobile-alerts-modal-label"><span id="mobile-alerts-modal-title">Notifications</span></h4>
                </div>
                <div id="mobile-alerts-modal-body" class="modal-body" style="height: 100%; padding: 0px;">
                    <div id="mobile-alerts-container" style="overflow-y: scroll; height: 580px; width: 100%;">
                        <ul id="mobile-alerts-ul" class="dropdown-menu nav-alerts-dropdown" style="display: block; border-radius: 0px; position: relative; top: 0px; width: 100%; margin-top: 0px;"></ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="accounts-sign-up-modal modal fade" id="accounts-sign-up" tabindex="-1" role="dialog" aria-labelledby="accounts-sign-up-label">
        <div class="modal-dialog" style="width: 350px;" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="accounts-sign-up-label"><span id="accounts-sign-up-title">Join peakery</span></h4>
                </div>
                <div class="modal-body"></div>
            </div>
        </div>
    </div>

    <div class="accounts-choose-username-modal modal fade" id="accounts-choose-username" tabindex="-1" role="dialog" aria-labelledby="accounts-choose-username-label">
        <div class="modal-dialog" style="width: 350px;" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="accounts-choose-username-label"><span id="accounts-choose-username-title">Choose your peakery username</span></h4>
                </div>
                <div class="modal-body"></div>
            </div>
        </div>
    </div>

    <div class="accounts-login-modal modal fade" id="accounts-login" tabindex="-1" role="dialog" aria-labelledby="accounts-login-label">
        <div class="modal-dialog" style="width: 350px;" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="accounts-login-label"><span id="accounts-login-title">Login</span></h4>
                </div>
                <div class="modal-body">
                    <div id="ajax_account_output" style="display: none"></div>
                    <div style="display: none; margin-bottom: 15px;" id="login_error">Incorrect password.<br>Try again.</div>
                    <div id="facebookButtonContainer" style="text-align:center;">




                        <form class="connect-button" name="login" method="post" action="/social/facebook/login/">
                            <input type='hidden' name='csrfmiddlewaretoken' value='C0xDekJV4d7U6ai2H90SiJOetRuQTE9K' />

                            <button style="font-size: 14px;" class="btn btn-info" onclick="facebookConnect(this.form);return false;"><i class="fab fa-facebook fa-2x" aria-hidden="true"></i> <span style="padding-left: 5px; top: -4px; position: relative;">Log in with Facebook</span></button>
                        </form>
                        <script type="text/javascript" >
                            function facebookConnect(form){
                                function handleResponse(response){
                                    form.submit();
                                }
                                FB.login(handleResponse, {scope: 'email, public_profile, user_friends' }  );
                            }
                        </script>

                        <h3 style="font-size: 18px; margin: 25px 0;">OR</h3>
                    </div>
                    <script type="text/javascript">
                        $(function(){
                            $('#facebookButtonContainer input').attr('src','https://s3-us-west-1.amazonaws.com/peakery-media/img/login_with_facebook.png');
                        });
                    </script>
                    <form id="signin" method="post" action="/accounts/login-user-lite/" style="text-align: center;">
                        <input class="form-control nav-login-input" placeholder="username or email" type="text" id="username" name="username" style="border: 1px solid #ccc; height: 50px; color: #333;">
                        <input class="form-control nav-login-input" placeholder="password" type="password" id="password" name="password" style="border: 1px solid #ccc; height: 50px; color: #333;">
                        <button id="signin-submit" type="submit" class="btn btn-secondary nav-login-submit" style="font-size: 14px; margin-left: 10px; line-height: 20px;">Login</button>
                        <div style="text-align: center;height: 50px;line-height: 60px;">
                            <a id="forgot-password" style="cursor: pointer; color: #999;">Forgot password</a>
                        </div>
                        <div style="text-align: center;height: 50px;line-height: 60px;">
                            Not a member yet? <a style="font-weight: 500; cursor: pointer;" id="not-a-member">Sign up</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="accounts-forgot-password-modal modal fade" id="accounts-forgot-password" tabindex="-1" role="dialog" aria-labelledby="accounts-forgot-password-label">
        <div class="modal-dialog" style="width: 350px;" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="accounts-forgot-password-label"><span id="accounts-forgot-password-title">Forgot password</span></h4>
                </div>
                <div class="modal-body">
                    <div class="signUpWithEmail">
                        <script type="text/javascript">
                            $(function() {
                                $('#id_email').on('input', function(e) {
                                    if ($(this).val() != "") {
                                        $(this).parent().children('.holder').hide();
                                    } else {
                                        $(this).parent().children('.holder').show();
                                    }
                                });
                            });
                        </script>
                        <form id="reset-password-form" action="" method="post">
                            <div style="display: none;" id="password_reset_error"></div>
                            <div style="margin-bottom: 20px;">Enter your email and we’ll send password reset instructions:</div>
                            <span class="a">
                            <input class="email" id="id_email" maxlength="100" name="email" type="text">
                            <span class="holder" style="">Email address</span>
                        </span>
                            <div style="text-align: center;height: 50px;line-height: 60px; margin-top: 25px; margin-bottom: 15px;">
                                <input id="reset-my-password" type="submit" value="Reset my password" class="btn set2 input" style="font-size: 14px; height: 50px;">
                            </div>
                        </form>
                    </div>
                    <div id="password-reset-success" style="display: none;">
                        We just sent an email to <span id="password-reset-email"></span> with password reset instructions.
                    </div>
                </div>
            </div>
        </div>
    </div>




</main>



</body>
</html>