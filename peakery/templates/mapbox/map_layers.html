{% load static %}
<script type="text/javascript">

    $(document).ready(function() {

        //new MapBox 2.0 3D stuff
        var flag = false;
        $('#gm-custom-mapoption-3d').on('touchstart click', function (e) {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#fff");
                $('#gm-custom-mapoption-topo').css("color", "#333");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#0ae");
                $('#gm-custom-mapoption-3d').css("color", "#fff");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('satellite_3d');
                $('#gm-custom-mapdropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">3D Map</div>');
            }
            return false
        });

        $('#gm-custom-mapoption-3d').on('mouseenter', function () {
            $('#gm-custom-mapdropdown').show();
            $('#gm-custom-mapbutton').css('border-bottom-right-radius', '0px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius', '0px');
        });

        $('#gm-custom-mapoption-3d').on('mouseleave', function () {
            $('#gm-custom-mapdropdown').hide();
            $('#gm-custom-mapregiondropdown').hide();
            $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
        });

        var flag = false;
        $('#gm-custom-mapoption-satstreets').on('touchstart click', function (e) {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#0ae");
                $('#gm-custom-mapoption-satstreets').css("color", "#fff");
                $('#gm-custom-mapoption-topo').css("background-color", "#fff");
                $('#gm-custom-mapoption-topo').css("color", "#333");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('satellite');
                $('#gm-custom-mapdropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite</div>');
            }
            return false
        });

        $('#gm-custom-mapoption-satstreets').on('mouseenter', function () {
            $('#gm-custom-mapdropdown').show();
            $('#gm-custom-mapbutton').css('border-bottom-right-radius', '0px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius', '0px');
        });

        $('#gm-custom-mapoption-satstreets').on('mouseleave', function () {
            $('#gm-custom-mapdropdown').hide();
            $('#gm-custom-mapregiondropdown').hide();
            $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
        });


        $('#gm-custom-mapoption-topo').on('mouseenter', function () {
            $('#gm-custom-mapdropdown').show();
            $('#gm-custom-mapregiondropdown').show();
            $('#gm-custom-mapbutton').css('border-bottom-right-radius', '0px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius', '0px');
        });

        $('#gm-custom-mapoption-topo').on('mouseleave', function () {
            $('#gm-custom-mapdropdown').hide();
            $('#gm-custom-mapregiondropdown').hide();
            $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
        });

        $('#gm-custom-mapoption-topo').on('touchstart click', function () {
            $('#gm-custom-mapdropdown').show();
            $('#gm-custom-mapregiondropdown').show();
            $('#gm-custom-mapbutton').css('border-bottom-right-radius', '0px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius', '0px');
            stopPropagation();
        });

        /* Country specific map types */
        //Argentina (IGN - 50K)
        var flag = false;
        $('#gm-custom-mapoption-argentina-ign-50k').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('argentina-ign-50k');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/ar.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Argentina (IGN - 50K)</div>');
            }
            return false
        });

        //Argentina (IGN - 100K)
        var flag = false;
        $('#gm-custom-mapoption-argentina-ign-100k').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('argentina-ign-100k');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/ar.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Argentina (IGN - 100K)</div>');
            }
            return false
        });

        //Australia - SA
        var flag = false;
        $('#gm-custom-mapoption-australia-sa').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('australia-sa');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/au.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Australia - SA</div>');
            }
            return false
        });

        //Australia - NSW (SIX)
        var flag = false;
        $('#gm-custom-mapoption-australia-nsw').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('australia-nsw');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/au.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Australia - NSW (SIX)</div>');
            }
            return false
        });

        //Australia - QLD (QTopo)
        var flag = false;
        $('#gm-custom-mapoption-australia-qld').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('australia-qld');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/au.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Australia - QLD (QTopo)</div>');
            }
            return false
        });

        //Australia - TAS (LIST)
        var flag = false;
        $('#gm-custom-mapoption-australia-ts').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('australia-ts');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/au.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Australia - TAS (LIST)</div>');
            }
            return false
        });

        //Australia - VIC (VicMap)
        var flag = false;
        $('#gm-custom-mapoption-australia-vic').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('australia-vic');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/au.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Australia - VIC (VicMap)</div>');
            }
            return false
        });

        //Austria (BergFex)
        var flag = false;
        $('#gm-custom-mapoption-austria-bergfex').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('austria-bergfex');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/at.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Austria (BergFex)</div>');
            }
            return false
        });

        //Austria (BEV)
        var flag = false;
        $('#gm-custom-mapoption-austria-bev').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('austria-bev');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/at.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Austria (BEV)</div>');
            }
            return false
        });

 //Brazil IBGE
        var flag = false;
        $('#gm-custom-mapoption-brazil').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('brazil');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/br.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Brazil (IBGE)</div>');
            }
            return false
        });



        //Belgium (NGI)
        var flag = false;
        $('#gm-custom-mapoption-belgium-ngi').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('belgium-ngi');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/be.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Belgium (NGI)</div>');
            }
            return false
        });

        //Canada (Caltopo)
        var flag = false;
        $('#gm-custom-mapoption-canada-caltopo').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('canada-caltopo');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/ca.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Canada (NRCAN)</div>');
            }
            return false
        });

        //Canada - BC (Basemap)
        var flag = false;
        $('#gm-custom-mapoption-canada-bc').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('canada-bc');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/ca.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Canada - BC (Basemap)</div>');
            }
            return false
        });

        //Canada - ON (OBM)
        var flag = false;
        $('#gm-custom-mapoption-canada-on').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('canada-on');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/ca.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Canada - ON (OBM)</div>');
            }
            return false
        });

        //Canada - QC (MERN)
        var flag = false;
        $('#gm-custom-mapoption-canada-qc').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('canada-qc');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/ca.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Canada - QC (MERN)</div>');
            }
            return false
        });

        //Croatia
        var flag = false;
        $('#gm-custom-mapoption-croatia-dgu').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('croatia-dgu');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/hr.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Croatia (DGU)</div>');
            }
            return false
        });

        //Czechia
        var flag = false;
        $('#gm-custom-mapoption-czechia-cuzk').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('czechia-cuzk');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/cz.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Czechia (ČÚZK)</div>');
            }
            return false
        });

        //Finland (NLS)
        var flag = false;
        $('#gm-custom-mapoption-finland-nls').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('finland-nls');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/fi.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Finland (NLS)</div>');
            }
            return false
        });

        //France (IGN)
        var flag = false;
        $('#gm-custom-mapoption-france-ign').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('france-ign');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/fr.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">France (IGN)</div>');
            }
            return false
        });

        //Germany (OutdoorActive)
        var flag = false;
        $('#gm-custom-mapoption-germany-oa').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('germany-oa');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/de.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Germany (OutdoorActive)</div>');
            }
            return false
        });

        //Hong Kong (LandsD)
        var flag = false;
        $('#gm-custom-mapoption-hongkong-landsd').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('hongkong-landsd');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/hk.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Hong Kong (LandsD)</div>');
            }
            return false
        });

        //Iceland - Caltopo
        var flag = false;
        $('#gm-custom-mapoption-iceland-caltopo').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('iceland-caltopo');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/is.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Iceland (CalTopo)</div>');
            }
            return false
        });

//Iceland - New
        var flag = false;
        $('#gm-custom-mapoption-iceland-new').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('iceland-new');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/is.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Iceland (Landmælingar)</div>');
            }
            return false
        });

        //Israel (Hiking OSM)
        var flag = false;
        $('#gm-custom-mapoption-israel-hikingosm').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('israel-hikingosm');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/il.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Israel (Hiking OSM)</div>');
            }
            return false
        });

        //Japan (GSI)
        var flag = false;
        $('#gm-custom-mapoption-japan-gsi').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('japan-gsi');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/jp.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Japan (GSI)</div>');
            }
            return false
        });

        //Luxembourg
        var flag = false;
        $('#gm-custom-mapoption-luxembourg').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('luxembourg');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/lu.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Luxembourg (ACT)</div>');
            }
            return false
        });

        //Mexico (INEGI)
        var flag = false;
        $('#gm-custom-mapoption-mexico-inegi').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('mexico-inegi');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/mx.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Mexico (INEGI)</div>');
            }
            return false
        });

        //New Zealand (LINZ)
        var flag = false;
        $('#gm-custom-mapoption-newzealand-linz').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('newzealand-linz');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/nz.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">New Zealand (LINZ)</div>');
            }
            return false
        });

//Norway (Kartverket)
        var flag = false;
        $('#gm-custom-mapoption-norway-new').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('norway-new');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/no.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Norway (Kartverket New)</div>');
            }
            return false
        });


        //Norway (Old)
        var flag = false;
        $('#gm-custom-mapoption-norway-kartverket').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('norway-kartverket');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/no.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Norway (Kartvertek)</div>');
            }
            return false
        });

        //Norway - Jan Mayen (NPI)
        var flag = false;
        $('#gm-custom-mapoption-norway-janmayen').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('norway-janmayen');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/no.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Norway - Jan Mayen (NPI)</div>');
            }
            return false
        });

        //Norway - Svalbard (NPI)
        var flag = false;
        $('#gm-custom-mapoption-norway-svalbard').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('norway-svalbard');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/no.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Norway - Svalbard (NPI)</div>');
            }
            return false
        });

        //Philippines (NAMRIA)
        var flag = false;
        $('#gm-custom-mapoption-philippines-namria').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('philippines-namria');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/ph.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Philippines (NAMRIA)</div>');
            }
            return false
        });

        //Poland (Geoportal)
        var flag = false;
        $('#gm-custom-mapoption-poland-geoportal').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('poland-geoportal');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/pl.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Poland (Geoportal)</div>');
            }
            return false
        });

        //Slovakia (DGU)
        var flag = false;
        $('#gm-custom-mapoption-slovakia-dgu').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('slovakia-dgu');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/sk.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Slovakia (DGU)</div>');
            }
            return false
        });

        //Slovenia (ProStor)
        var flag = false;
        $('#gm-custom-mapoption-slovenia-prostor').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('slovenia-prostor');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/si.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Slovenia (ProStor)</div>');
            }
            return false
        });

        //South Africa NGI 50K
        var flag = false;
        $('#gm-custom-mapoption-southafrica-ngi-50k').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('southafrica-ngi-50k');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/za.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">South Africa NGI 50K</div>');
            }
            return false
        });

        //Spain (IGN)
        var flag = false;
        $('#gm-custom-mapoption-spain-ign').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('spain-ign');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/es.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Spain (IGN)</div>');
            }
            return false
        });

        //Spain - Cataluña (ICGC)
        var flag = false;
        $('#gm-custom-mapoption-spain-cataluna').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('spain-cataluna');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/es.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Spain - Cataluña (ICGC)</div>');
            }
            return false
        });

        //Sweden (SGU)
        var flag = false;
        $('#gm-custom-mapoption-sweden-sgu').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('sweden-sgu');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/se.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Sweden (SGU)</div>');
            }
            return false
        });

        //Switzerland (swisstopo)
        var flag = false;
        $('#gm-custom-mapoption-switzerland-swisstopo').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('switzerland-swisstopo');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/ch.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Switzerland (swisstopo)</div>');
            }
            return false
        });

        //Taiwan (NLSC)
        var flag = false;
        $('#gm-custom-mapoption-taiwan-nlsc').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('taiwan-nlsc');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/tw.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Taiwan (NLSC)</div>');
            }
            return false
        });

        //United Kindom (OS)
        var flag = false;
        $('#gm-custom-mapoption-uk-os').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('uk-os');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/gb.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">United Kingdom (OS)</div>');
            }
            return false
        });

        //United States (Caltopo)
        var flag = false;
        $('#gm-custom-mapoption-us-caltopo').on('click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                $('#gm-custom-mapoption-topo').css("color", "#fff");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('caltopo');
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapregiondropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/us.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">United States (USGS)</div>');
            }
            return false
        });

        //region hover overs
        $('.gm-custom-mapoption-region').on('mouseenter', function () {
            $('#gm-custom-mapdropdown').show();
            $('#gm-custom-mapregiondropdown').show();
            $('#gm-custom-mapbutton').css('border-bottom-right-radius', '0px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius', '0px');
        });

        $('.gm-custom-mapoption-region').on('mouseleave', function () {
            $('#gm-custom-mapdropdown').hide();
            $('#gm-custom-mapregiondropdown').hide();
            $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
        });

        var flag = false;
        $('#gm-custom-mapoption-sat').on('touchstart click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#fff");
                $('#gm-custom-mapoption-topo').css("color", "#333");
                $('#gm-custom-mapoption-sat').css("background-color", "#0ae");
                $('#gm-custom-mapoption-sat').css("color", "#fff");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('sat_topo');
                $('#gm-custom-mapdropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite Topo</div>');
            }
            return false
        });

        $('#gm-custom-mapoption-sat').on('mouseenter', function () {
            $('#gm-custom-mapdropdown').show();
            $('#gm-custom-mapbutton').css('border-bottom-right-radius', '0px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius', '0px');
        });

        $('#gm-custom-mapoption-sat').on('mouseleave', function () {
            $('#gm-custom-mapdropdown').hide();
            $('#gm-custom-mapregiondropdown').hide();
            $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
        });

        var flag = false;
        $('#gm-custom-mapoption-terrain').on('touchstart click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#fff");
                $('#gm-custom-mapoption-topo').css("color", "#333");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#0ae");
                $('#gm-custom-mapoption-terrain').css("color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('terrain');
                $('#gm-custom-mapdropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Terrain</div>');
            }
            return false
        });

        $('#gm-custom-mapoption-terrain').on('mouseenter', function () {
            $('#gm-custom-mapdropdown').show();
            $('#gm-custom-mapbutton').css('border-bottom-right-radius', '0px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius', '0px');
        });

        $('#gm-custom-mapoption-terrain').on('mouseleave', function () {
            $('#gm-custom-mapdropdown').hide();
            $('#gm-custom-mapregiondropdown').hide();
            $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
        });

        var flag = false;
        $('#gm-custom-mapoption-outdoors').on('touchstart click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#fff");
                $('#gm-custom-mapoption-topo').css("color", "#333");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#0ae");
                $('#gm-custom-mapoption-outdoors').css("color", "#fff");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('outdoors');
                $('#gm-custom-mapdropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">OpenTopoMap</div>');
            }
            return false
        });

        $('#gm-custom-mapoption-outdoors').on('mouseenter', function () {
            $('#gm-custom-mapdropdown').show();
            $('#gm-custom-mapbutton').css('border-bottom-right-radius', '0px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius', '0px');
        });

        $('#gm-custom-mapoption-outdoors').on('mouseleave', function () {
            $('#gm-custom-mapdropdown').hide();
            $('#gm-custom-mapregiondropdown').hide();
            $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
        });

        var flag = false;
        $('#gm-custom-mapoption-streets').on('touchstart click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#fff");
                $('#gm-custom-mapoption-topo').css("color", "#333");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#0ae");
                $('#gm-custom-mapoption-streets').css("color", "#fff");
                $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                $('#gm-custom-mapoption-natatl').css("color", "#333");

                toggleMapType('streets');
                $('#gm-custom-mapdropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Streets</div>');
            }
            return false
        });

        $('#gm-custom-mapoption-streets').on('mouseenter', function () {
            $('#gm-custom-mapdropdown').show();
            $('#gm-custom-mapbutton').css('border-bottom-right-radius', '0px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius', '0px');
        });

        $('#gm-custom-mapoption-streets').on('mouseleave', function () {
            $('#gm-custom-mapdropdown').hide();
            $('#gm-custom-mapregiondropdown').hide();
            $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
        });

        //Natural atlas stuff
        var flag = false;
        $('#gm-custom-mapoption-natatl').on('touchstart click', function () {
            if (!flag) {
                flag = true;
                setTimeout(function () {
                    flag = false;
                }, 500);
                $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                $('#gm-custom-mapoption-satstreets').css("color", "#333");
                $('#gm-custom-mapoption-topo').css("background-color", "#fff");
                $('#gm-custom-mapoption-topo').css("color", "#333");
                $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                $('#gm-custom-mapoption-sat').css("color", "#333");
                $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                $('#gm-custom-mapoption-3d').css("color", "#333");
                $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                $('#gm-custom-mapoption-terrain').css("color", "#333");
                $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                $('#gm-custom-mapoption-outdoors').css("color", "#333");
                $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                $('#gm-custom-mapoption-streets').css("color", "#333");
                $('#gm-custom-mapoption-natatl').css("background-color", "#0ae");
                $('#gm-custom-mapoption-natatl').css("color", "#fff");

                toggleMapType('natural_atlas');
                $('#gm-custom-mapdropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();

                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Natural Atlas <span style="font-size: 10px;">(US)</span>/div>');
            }
            return false
        });

        $('#gm-custom-mapoption-natatl').on('mouseenter', function () {
            $('#gm-custom-mapdropdown').show();
            $('#gm-custom-mapbutton').css('border-bottom-right-radius', '0px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius', '0px');
        });

        $('#gm-custom-mapoption-natatl').on('mouseleave', function () {
            $('#gm-custom-mapdropdown').hide();
            $('#gm-custom-mapregiondropdown').hide();
            $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
        });

    });

</script>