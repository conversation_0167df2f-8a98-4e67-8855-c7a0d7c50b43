{% load static %}
<script src='https://api.mapbox.com/mapbox-gl-js/v3.4.0/mapbox-gl.js'></script>
<link href='https://api.mapbox.com/mapbox-gl-js/v3.4.0/mapbox-gl.css' rel='stylesheet' />
<!-- Load the `mapbox-gl-geocoder` plugin. -->
<script src="https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-geocoder/v5.0.0/mapbox-gl-geocoder.min.js"></script>
<link rel="stylesheet" href="https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-geocoder/v5.0.0/mapbox-gl-geocoder.css" type="text/css">

<style>

    .gm-style-mtc {
        opacity: .8;
    }

    .route_marker_pulse_00b1f2 {
        background-image: url('{% static 'img/route-marker-pulse-00b1f2.png' %}');
        background-size: cover;
        width: 21px;
        height: 21px;
        cursor: pointer;
    }

    .route_marker_00b1f2 {
        background-image: url('{% static 'img/route-marker-00b1f2.png' %}');
        background-size: cover;
        width: 21px;
        height: 21px;
        cursor: pointer;
    }

    .route_marker_pulse_00b330 {
        background-image: url('{% static 'img/route-marker-pulse-00b330.png' %}');
        background-size: cover;
        width: 21px;
        height: 21px;
        cursor: pointer;
    }

    .route_marker_00b330 {
        background-image: url('{% static 'img/route-marker-00b330.png' %}');
        background-size: cover;
        width: 21px;
        height: 21px;
        cursor: pointer;
    }

    .route_marker_pulse_8d00f2 {
        background-image: url('{% static 'img/route-marker-pulse-8d00f2.png' %}');
        background-size: cover;
        width: 21px;
        height: 21px;
        cursor: pointer;
    }

    .route_marker_8d00f2 {
        background-image: url('{% static 'img/route-marker-8d00f2.png' %}');
        background-size: cover;
        width: 21px;
        height: 21px;
        cursor: pointer;
    }

    .route_marker_pulse_f0b800 {
        background-image: url('{% static 'img/route-marker-pulse-f0b800.png' %}');
        background-size: cover;
        width: 21px;
        height: 21px;
        cursor: pointer;
    }

    .route_marker_f0b800 {
        background-image: url('{% static 'img/route-marker-f0b800.png' %}');
        background-size: cover;
        width: 21px;
        height: 21px;
        cursor: pointer;
    }

    .route_marker_pulse_f2ca00 {
        background-image: url('{% static 'img/route-marker-pulse-f2ca00.png' %}');
        background-size: cover;
        width: 21px;
        height: 21px;
        cursor: pointer;
    }

    .route_marker_f2ca00 {
        background-image: url('{% static 'img/route-marker-f2ca00.png' %}');
        background-size: cover;
        width: 21px;
        height: 21px;
        cursor: pointer;
    }

    .route_marker_pulse_f200f2 {
        background-image: url('{% static 'img/route-marker-pulse-f200f2.png' %}');
        background-size: cover;
        width: 21px;
        height: 21px;
        cursor: pointer;
    }

    .route_marker_f200f2 {
        background-image: url('{% static 'img/route-marker-f200f2.png' %}');
        background-size: cover;
        width: 21px;
        height: 21px;
        cursor: pointer;
    }

    .route_marker_pulse_f28300 {
        background-image: url('{% static 'img/route-marker-pulse-f28300.png' %}');
        background-size: cover;
        width: 21px;
        height: 21px;
        cursor: pointer;
    }

    .route_marker_f28300 {
        background-image: url('{% static 'img/route-marker-f28300.png' %}');
        background-size: cover;
        width: 21px;
        height: 21px;
        cursor: pointer;
    }

    .route_marker_pulse_fc202e {
        background-image: url('{% static 'img/route-marker-pulse-fc202e.png' %}');
        background-size: cover;
        width: 21px;
        height: 21px;
        cursor: pointer;
    }

    .route_marker_fc202e {
        background-image: url('{% static 'img/route-marker-fc202e.png' %}');
        background-size: cover;
        width: 21px;
        height: 21px;
        cursor: pointer;
    }

    .marker_icon {
        background-image: url('{% static 'img/<EMAIL>' %}');
        background-size: cover;
        width: 24px;
        height: 24px;
        cursor: pointer;
    }

    .marker_icon:hover {
        background-image: url('{% static 'img/<EMAIL>' %}');
    }

    .marker_icon_red {
        background-image: url('{% static 'img/<EMAIL>' %}');
        background-size: cover;
        width: 24px;
        height: 24px;
        cursor: pointer;
    }

    .marker_icon_red:hover {
        background-image: url('{% static 'img/<EMAIL>' %}');
    }

    .marker_icon_green {
        background-image: url('{% static 'img/<EMAIL>' %}');
        background-size: cover;
        width: 24px;
        height: 24px;
        cursor: pointer;
    }

    .marker_icon_green:hover {
        background-image: url('{% static 'img/<EMAIL>' %}');
    }

    .marker_icon_redgreen {
        background-image: url('{% static 'img/<EMAIL>' %}');
        background-size: cover;
        width: 24px;
        height: 24px;
        cursor: pointer;
    }

    .marker_icon_redgreen:hover {
        background-image: url('{% static 'img/<EMAIL>' %}');
    }

    .marker_icon_yellow {
        background-image: url('{% static 'img/<EMAIL>' %}');
        background-size: cover;
        width: 24px;
        height: 24px;
        cursor: pointer;
    }

    .marker_icon_yellow:hover {
        background-image: url('{% static 'img/<EMAIL>' %}');
    }

    .marker_icon_purple {
        background-image: url('{% static 'img/<EMAIL>' %}');
        background-size: cover;
        width: 24px;
        height: 24px;
        cursor: pointer;
    }

    .marker_icon_purple:hover {
        background-image: url('{% static 'img/<EMAIL>' %}');
    }

    .marker_icon_peak {
        background-image: url('{% static 'img/<EMAIL>' %}');
        background-size: cover;
        width: 24px;
        height: 24px;
        cursor: pointer;
    }

    .marker_icon_peak:hover {
        background-image: url('{% static 'img/<EMAIL>' %}');
    }

    .marker_icon_basecamp_flag {
        background-image: url('{% static 'img/home_basecamp_flag.png' %}');
        background-size: cover;
        width: 25px;
        height: 31px;
        cursor: pointer;
    }

    #gm-custom-mapunits:hover {
        background-color: rgb(235, 235, 235) !important;
        color: rgb(0, 0, 0) !important;
    }

    .gm-custom-mapunits-selected {
        color: #333;
    }

    .gm-custom-mapunits-unselected {
        color: #999;
    }

    #gm-custom-mapoption-natatl:hover {
        background-color: #ebebeb!important;
        color: #000!important;
    }

    #gm-custom-mapoption-3d:hover {
        background-color: #ebebeb!important;
        color: #000!important;
    }

</style>

<script type="text/javascript">

    mapboxgl.accessToken = 'pk.eyJ1IjoicGVha2VyeSIsImEiOiJjampra3Z0bnAxeTVnM3FteHlybHY3b2p1In0.7a5dEa5-995VUv8ceHHNmw';

    var mapStyleName;
    var mapMinZoom = 0;
    var mapMinZoom = 0;
    var mapMaxZoom = 22;

    var mapStyleTerrain = 'mapbox://styles/peakery/cl45xqr4r000015pdrcq97oe5';
    var mapStyleTerrainMeters = 'mapbox://styles/peakery/cl4513r11000315qxv4hoj449';
    var mapStyleSatellite = 'mapbox://styles/peakery/cjjkmis2h63yj2ss1dhq1a3z6';
    var mapStyleSatTopo = 'mapbox://styles/peakery/cjjkme1dj1hz02rpb9ggw2zjd';
    var mapStyleSatTopoMeters = 'mapbox://styles/peakery/cl83mxp6u000f14pzxbp88ph6';
    var mapStyleStreets = 'mapbox://styles/peakery/ckfrckk0g0wts19li8x5o9lxq';

    var mapStyleSatellite3D = {
        "version": 8,
        "sources": {
            "caltopo": {
                "type": "raster",
                "tiles": [
                    "https://s3-us-west-1.amazonaws.com/caltopo/topo/{z}/{x}/{y}.png"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 14
            },
            "mapbox://mapbox.satellite": {
                "url": "mapbox://mapbox.satellite",
                "type": "raster",
                "tileSize": 256
            },
            "composite": {
                "url": "mapbox://mapbox.mapbox-streets-v8",
                "type": "vector"
            }
        },
        "sprite": "mapbox://sprites/peakery/ckithy19a1xtf19rc4mqzk2yr/dkzh8zrw5qm35lsvg7bkvm616",
        "glyphs": "mapbox://fonts/peakery/{fontstack}/{range}.pbf",
        "layers": [
            {
                "id": "caltopo",
                "type": "raster",
                "source": "caltopo",
                "minzoom": 0,
                "maxzoom": 20
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            },
		    {
                "id": "background",
                "type": "background",
                "metadata": {
                    "mapbox:featureComponent": "satellite",
                    "mapbox:group": "Satellite imagery, land"
                },
                "layout": {},
                "paint": {"background-color": "hsl(222, 56%, 4%)"}
            },
            {
                "id": "satellite",
                "type": "raster",
                "metadata": {
                    "mapbox:featureComponent": "satellite",
                    "mapbox:group": "Satellite imagery, land"
                },
                "source": "mapbox://mapbox.satellite",
                "layout": {},
                "paint": {}
            },
            {
                "id": "tunnel-street-minor-low",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, tunnels-case"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 13,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "tunnel"],
                    [
                        "step",
                        ["zoom"],
                        [
                            "match",
                            ["get", "class"],
                            ["street", "street_limited", "primary_link"],
                            true,
                            false
                        ],
                        14,
                        [
                            "match",
                            ["get", "class"],
                            [
                                "street",
                                "street_limited",
                                "primary_link",
                                "secondary_link",
                                "tertiary_link"
                            ],
                            true,
                            false
                        ]
                    ],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        12,
                        0.5,
                        14,
                        [
                            "match",
                            ["get", "class"],
                            ["street", "street_limited", "primary_link"],
                            2,
                            "track",
                            1,
                            0.5
                        ],
                        18,
                        [
                            "match",
                            ["get", "class"],
                            ["street", "street_limited", "primary_link"],
                            18,
                            12
                        ]
                    ],
                    "line-color": "hsla(0, 0%, 90%, 0.5)",
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        1,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "tunnel-street-minor-case",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, tunnels-case"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 13,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "tunnel"],
                    [
                        "step",
                        ["zoom"],
                        [
                            "match",
                            ["get", "class"],
                            ["street", "street_limited", "primary_link"],
                            true,
                            false
                        ],
                        14,
                        [
                            "match",
                            ["get", "class"],
                            [
                                "street",
                                "street_limited",
                                "primary_link",
                                "secondary_link",
                                "tertiary_link"
                            ],
                            true,
                            false
                        ]
                    ],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        12,
                        0.75,
                        20,
                        2
                    ],
                    "line-color": "hsla(0, 0%, 0%, 0.5)",
                    "line-gap-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        12,
                        0.5,
                        14,
                        [
                            "match",
                            ["get", "class"],
                            ["street", "street_limited", "primary_link"],
                            2,
                            "track",
                            1,
                            0.5
                        ],
                        18,
                        [
                            "match",
                            ["get", "class"],
                            ["street", "street_limited", "primary_link"],
                            18,
                            12
                        ]
                    ],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        1,
                        15,
                        0
                    ],
                    "line-dasharray": [3, 3]
                }
            },
            {
                "id": "tunnel-primary-secondary-tertiary-case",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, tunnels-case"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 13,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "tunnel"],
                    [
                        "match",
                        ["get", "class"],
                        ["primary", "secondary", "tertiary"],
                        true,
                        false
                    ],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        10,
                        ["match", ["get", "class"], "primary", 1, 0.75],
                        18,
                        2
                    ],
                    "line-color": "hsla(0, 0%, 0%, 0.5)",
                    "line-gap-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        5,
                        ["match", ["get", "class"], "primary", 0.75, 0.1],
                        18,
                        ["match", ["get", "class"], "primary", 32, 26]
                    ],
                    "line-dasharray": [3, 3],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        0.3,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "tunnel-major-link-case",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, tunnels-case"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 13,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "tunnel"],
                    [
                        "match",
                        ["get", "class"],
                        ["motorway_link", "trunk_link"],
                        true,
                        false
                    ],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        12,
                        0.75,
                        20,
                        2
                    ],
                    "line-color": "hsla(0, 0%, 90%, 0.5)",
                    "line-gap-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        12,
                        0.5,
                        14,
                        2,
                        18,
                        18
                    ],
                    "line-dasharray": [3, 3],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        1,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "tunnel-motorway-trunk-case",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, tunnels-case"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 13,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "tunnel"],
                    ["match", ["get", "class"], ["motorway", "trunk"], true, false],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        10,
                        1,
                        18,
                        2
                    ],
                    "line-color": "hsla(0, 1%, 22%, 0.5)",
                    "line-gap-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        5,
                        0.75,
                        18,
                        32
                    ],
                    "line-dasharray": [3, 3],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        1,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "tunnel-path",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "walking-cycling",
                    "mapbox:group": "Walking, cycling, etc., tunnels"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 14,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "tunnel"],
                    ["==", ["get", "class"], "path"],
                    ["!=", ["get", "type"], "steps"],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        15,
                        1,
                        18,
                        4
                    ],
                    "line-color": "hsl(0, 0%, 10%)",
                    "line-dasharray": [
                        "step",
                        ["zoom"],
                        ["literal", [1, 0]],
                        15,
                        ["literal", [1.75, 1]],
                        16,
                        ["literal", [1, 0.75]],
                        17,
                        ["literal", [1, 0.5]]
                    ]
                }
            },
            {
                "id": "tunnel-steps",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "walking-cycling",
                    "mapbox:group": "Walking, cycling, etc., tunnels"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 14,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "tunnel"],
                    ["==", ["get", "type"], "steps"],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        15,
                        1,
                        16,
                        1.6,
                        18,
                        6
                    ],
                    "line-color": "hsl(0, 0%, 10%)",
                    "line-dasharray": [
                        "step",
                        ["zoom"],
                        ["literal", [1, 0]],
                        15,
                        ["literal", [1.75, 1]],
                        16,
                        ["literal", [1, 0.75]],
                        17,
                        ["literal", [0.3, 0.3]]
                    ]
                }
            },
            {
                "id": "tunnel-pedestrian",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "walking-cycling",
                    "mapbox:group": "Walking, cycling, etc., tunnels"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 13,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "tunnel"],
                    ["==", ["get", "class"], "pedestrian"],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        14,
                        0.5,
                        18,
                        12
                    ],
                    "line-color": "hsl(0, 0%, 10%)",
                    "line-dasharray": [
                        "step",
                        ["zoom"],
                        ["literal", [1, 0]],
                        15,
                        ["literal", [1.5, 0.4]],
                        16,
                        ["literal", [1, 0.2]]
                    ]
                }
            },
            {
                "id": "tunnel-major-link",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, tunnels"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 13,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "tunnel"],
                    [
                        "match",
                        ["get", "class"],
                        ["motorway_link", "trunk_link"],
                        true,
                        false
                    ],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        12,
                        0.5,
                        14,
                        2,
                        18,
                        18
                    ],
                    "line-color": [
                        "match",
                        ["get", "class"],
                        "motorway_link",
                        "hsla(26, 100%, 78%, 0.7)",
                        "hsla(46, 77%, 78%, 0.7)"
                    ],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        1,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "tunnel-street-minor",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, tunnels"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 13,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "tunnel"],
                    [
                        "step",
                        ["zoom"],
                        [
                            "match",
                            ["get", "class"],
                            ["street", "street_limited", "primary_link"],
                            true,
                            false
                        ],
                        14,
                        [
                            "match",
                            ["get", "class"],
                            [
                                "street",
                                "street_limited",
                                "primary_link",
                                "secondary_link",
                                "tertiary_link"
                            ],
                            true,
                            false
                        ]
                    ],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        12,
                        0.5,
                        14,
                        [
                            "match",
                            ["get", "class"],
                            ["street", "street_limited", "primary_link"],
                            2,
                            "track",
                            1,
                            0.5
                        ],
                        18,
                        [
                            "match",
                            ["get", "class"],
                            ["street", "street_limited", "primary_link"],
                            18,
                            12
                        ]
                    ],
                    "line-color": [
                        "match",
                        ["get", "class"],
                        "street_limited",
                        "hsl(0, 2%, 22%)",
                        "hsla(0, 0%, 90%, 0.5)"
                    ],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        1,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "tunnel-primary-secondary-tertiary",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, tunnels"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 13,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "tunnel"],
                    [
                        "match",
                        ["get", "class"],
                        ["primary", "secondary", "tertiary"],
                        true,
                        false
                    ],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        5,
                        ["match", ["get", "class"], "primary", 0.75, 0.1],
                        18,
                        ["match", ["get", "class"], "primary", 32, 26]
                    ],
                    "line-color": "hsla(0, 0%, 90%, 0.5)",
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        0.3,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "tunnel-oneway-arrow-blue",
                "type": "symbol",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, tunnels"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 15,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "tunnel"],
                    ["==", ["get", "oneway"], "true"],
                    [
                        "step",
                        ["zoom"],
                        [
                            "match",
                            ["get", "class"],
                            [
                                "primary",
                                "secondary",
                                "street",
                                "street_limited",
                                "tertiary"
                            ],
                            true,
                            false
                        ],
                        16,
                        [
                            "match",
                            ["get", "class"],
                            [
                                "primary",
                                "secondary",
                                "tertiary",
                                "street",
                                "street_limited",
                                "primary_link",
                                "secondary_link",
                                "tertiary_link"
                            ],
                            true,
                            false
                        ]
                    ]
                ],
                "layout": {
                    "symbol-placement": "line",
                    "icon-image": [
                        "step",
                        ["zoom"],
                        "oneway-small",
                        17,
                        [
                            "match",
                            ["get", "class"],
                            [
                                "primary",
                                "secondary",
                                "tertiary",
                                "street",
                                "street_limited"
                            ],
                            "oneway-large",
                            "oneway-small"
                        ],
                        18,
                        "oneway-large"
                    ],
                    "symbol-spacing": 200,
                    "icon-rotation-alignment": "map"
                },
                "paint": {}
            },
            {
                "id": "tunnel-motorway-trunk",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, tunnels"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 13,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "tunnel"],
                    ["match", ["get", "class"], ["motorway", "trunk"], true, false],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        5,
                        0.75,
                        18,
                        32
                    ],
                    "line-color": [
                        "match",
                        ["get", "class"],
                        "motorway",
                        "hsla(26, 100%, 78%, 0.7)",
                        "hsla(46, 77%, 78%, 0.7)"
                    ],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        1,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "tunnel-oneway-arrow-white",
                "type": "symbol",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, tunnels"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 16,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "tunnel"],
                    [
                        "match",
                        ["get", "class"],
                        ["motorway", "motorway_link", "trunk", "trunk_link"],
                        true,
                        false
                    ],
                    ["==", ["get", "oneway"], "true"]
                ],
                "layout": {
                    "symbol-placement": "line",
                    "icon-image": [
                        "step",
                        ["zoom"],
                        "oneway-white-small",
                        17,
                        "oneway-white-large"
                    ],
                    "symbol-spacing": 200
                },
                "paint": {}
            },
            {
                "id": "road-path",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "walking-cycling",
                    "mapbox:group": "Walking, cycling, etc., surface"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 12,
                "filter": [
                    "all",
                    ["==", ["get", "class"], "path"],
                    [
                        "step",
                        ["zoom"],
                        [
                            "!",
                            [
                                "match",
                                ["get", "type"],
                                ["steps", "sidewalk", "crossing"],
                                true,
                                false
                            ]
                        ],
                        16,
                        ["!=", ["get", "type"], "steps"]
                    ],
                    ["match", ["get", "structure"], ["none", "ford"], true, false],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {"line-join": ["step", ["zoom"], "miter", 14, "round"]},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        13,
                        0.5,
                        14,
                        1,
                        15,
                        1,
                        18,
                        4
                    ],
                    "line-color": "#FFA500",
                    "line-dasharray": [
                        "step",
                        ["zoom"],
                        ["literal", [4, 0.3]],
                        15,
                        ["literal", [1.75, 0.3]],
                        16,
                        ["literal", [1, 0.3]],
                        17,
                        ["literal", [1, 0.25]]
                    ]
                }
            },
            {
                "id": "road-steps",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "walking-cycling",
                    "mapbox:group": "Walking, cycling, etc., surface"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 14,
                "filter": [
                    "all",
                    ["==", ["get", "type"], "steps"],
                    ["match", ["get", "structure"], ["none", "ford"], true, false],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {"line-join": "round"},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        15,
                        1,
                        16,
                        1.6,
                        18,
                        6
                    ],
                    "line-color": "hsl(0, 0%, 22%)",
                    "line-dasharray": [
                        "step",
                        ["zoom"],
                        ["literal", [1, 0]],
                        15,
                        ["literal", [1.75, 1]],
                        16,
                        ["literal", [1, 0.75]],
                        17,
                        ["literal", [0.3, 0.3]]
                    ]
                }
            },
            {
                "id": "road-pedestrian",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "walking-cycling",
                    "mapbox:group": "Walking, cycling, etc., surface"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 12,
                "filter": [
                    "all",
                    ["==", ["get", "class"], "pedestrian"],
                    ["match", ["get", "structure"], ["none", "ford"], true, false],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {"line-join": ["step", ["zoom"], "miter", 14, "round"]},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        14,
                        0.5,
                        18,
                        12
                    ],
                    "line-color": "hsl(0, 0%, 22%)",
                    "line-dasharray": [
                        "step",
                        ["zoom"],
                        ["literal", [1, 0]],
                        15,
                        ["literal", [1.5, 0.4]],
                        16,
                        ["literal", [1, 0.2]]
                    ]
                }
            },
            {
                "id": "road-minor-low",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, surface"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 13,
                "filter": [
                    "all",
                    [
                        "step",
                        ["zoom"],
                        false,
                        14,
                        [
                            "match",
                            ["get", "class"],
                            ["secondary_link", "tertiary_link"],
                            true,
                            false
                        ]
                    ],
                    ["match", ["get", "structure"], ["none", "ford"], true, false],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {
                    "line-cap": ["step", ["zoom"], "butt", 14, "round"],
                    "line-join": ["step", ["zoom"], "miter", 14, "round"]
                },
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        14,
                        ["match", ["get", "class"], "track", 1, 0.5],
                        18,
                        12
                    ],
                    "line-color": "hsla(0, 0%, 90%, 0.5)",
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        1,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "road-minor-case",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, surface"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 13,
                "filter": [
                    "all",
                    [
                        "step",
                        ["zoom"],
                        false,
                        14,
                        [
                            "match",
                            ["get", "class"],
                            ["secondary_link", "tertiary_link"],
                            true,
                            false
                        ]
                    ],
                    ["match", ["get", "structure"], ["none", "ford"], true, false],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {
                    "line-cap": ["step", ["zoom"], "butt", 14, "round"],
                    "line-join": ["step", ["zoom"], "miter", 14, "round"]
                },
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        12,
                        0.75,
                        20,
                        2
                    ],
                    "line-color": "hsla(0, 1%, 10%, 0.5)",
                    "line-gap-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        14,
                        ["match", ["get", "class"], "track", 1, 0.5],
                        18,
                        12
                    ],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        1,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "road-street-low",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, surface"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 11,
                "filter": [
                    "all",
                    [
                        "match",
                        ["get", "class"],
                        ["street", "street_limited", "primary_link"],
                        true,
                        false
                    ],
                    ["match", ["get", "structure"], ["none", "ford"], true, false],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {
                    "line-cap": ["step", ["zoom"], "butt", 14, "round"],
                    "line-join": ["step", ["zoom"], "miter", 14, "round"]
                },
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        12,
                        0.5,
                        14,
                        2,
                        18,
                        18
                    ],
                    "line-color": "hsla(0, 0%, 90%, 0.5)",
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        0.3,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "road-street-case",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, surface"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 11,
                "filter": [
                    "all",
                    [
                        "match",
                        ["get", "class"],
                        ["street", "street_limited", "primary_link"],
                        true,
                        false
                    ],
                    ["match", ["get", "structure"], ["none", "ford"], true, false],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {
                    "line-cap": ["step", ["zoom"], "butt", 14, "round"],
                    "line-join": ["step", ["zoom"], "miter", 14, "round"]
                },
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        12,
                        0.75,
                        20,
                        2
                    ],
                    "line-color": "hsla(0, 1%, 10%, 0.5)",
                    "line-gap-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        12,
                        0.5,
                        14,
                        2,
                        18,
                        18
                    ],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        0.3,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "road-secondary-tertiary-case",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, surface"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 8,
                "filter": [
                    "all",
                    [
                        "match",
                        ["get", "class"],
                        ["secondary", "tertiary"],
                        true,
                        false
                    ],
                    ["match", ["get", "structure"], ["none", "ford"], true, false],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {
                    "line-cap": ["step", ["zoom"], "butt", 14, "round"],
                    "line-join": ["step", ["zoom"], "miter", 14, "round"]
                },
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        10,
                        0.75,
                        18,
                        2
                    ],
                    "line-color": "hsla(0, 1%, 10%, 0.5)",
                    "line-gap-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        5,
                        0.1,
                        18,
                        26
                    ],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        0.3,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "road-primary-case",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, surface"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 7,
                "filter": [
                    "all",
                    ["==", ["get", "class"], "primary"],
                    ["match", ["get", "structure"], ["none", "ford"], true, false],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {
                    "line-cap": ["step", ["zoom"], "butt", 14, "round"],
                    "line-join": ["step", ["zoom"], "miter", 14, "round"]
                },
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        10,
                        1,
                        18,
                        2
                    ],
                    "line-color": "hsla(0, 1%, 10%, 0.5)",
                    "line-gap-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        5,
                        0.75,
                        18,
                        32
                    ],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        1,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "road-major-link-case",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, surface"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 10,
                "filter": [
                    "all",
                    [
                        "match",
                        ["get", "class"],
                        ["motorway_link", "trunk_link"],
                        true,
                        false
                    ],
                    ["match", ["get", "structure"], ["none", "ford"], true, false],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {
                    "line-cap": ["step", ["zoom"], "butt", 14, "round"],
                    "line-join": ["step", ["zoom"], "miter", 14, "round"]
                },
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        12,
                        0.75,
                        20,
                        2
                    ],
                    "line-color": "hsla(0, 1%, 22%, 0.5)",
                    "line-gap-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        12,
                        0.5,
                        14,
                        2,
                        18,
                        18
                    ],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        1,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "road-motorway-trunk-case",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, surface"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 5,
                "filter": [
                    "all",
                    ["match", ["get", "class"], ["motorway", "trunk"], true, false],
                    ["match", ["get", "structure"], ["none", "ford"], true, false],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {
                    "line-cap": ["step", ["zoom"], "butt", 14, "round"],
                    "line-join": ["step", ["zoom"], "miter", 14, "round"]
                },
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        10,
                        1,
                        18,
                        2
                    ],
                    "line-color": "hsla(0, 1%, 22%, 0.5)",
                    "line-gap-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        5,
                        0.75,
                        18,
                        32
                    ],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        1,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "road-major-link",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, surface"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 10,
                "filter": [
                    "all",
                    [
                        "match",
                        ["get", "class"],
                        ["motorway_link", "trunk_link"],
                        true,
                        false
                    ],
                    ["match", ["get", "structure"], ["none", "ford"], true, false],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {
                    "line-cap": ["step", ["zoom"], "butt", 13, "round"],
                    "line-join": ["step", ["zoom"], "miter", 13, "round"]
                },
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        12,
                        0.5,
                        14,
                        2,
                        18,
                        18
                    ],
                    "line-color": [
                        "match",
                        ["get", "class"],
                        "motorway_link",
                        "hsla(26, 100%, 68%, 0.7)",
                        "hsla(46, 87%, 68%, 0.7)"
                    ],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        1,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "road-minor",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, surface"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 13,
                "filter": [
                    "all",
                    [
                        "step",
                        ["zoom"],
                        false,
                        14,
                        [
                            "match",
                            ["get", "class"],
                            ["secondary_link", "tertiary_link"],
                            true,
                            false
                        ]
                    ],
                    ["match", ["get", "structure"], ["none", "ford"], true, false],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {
                    "line-cap": ["step", ["zoom"], "butt", 14, "round"],
                    "line-join": ["step", ["zoom"], "miter", 14, "round"]
                },
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        14,
                        ["match", ["get", "class"], "track", 1, 0.5],
                        18,
                        12
                    ],
                    "line-color": "hsla(0, 0%, 90%, 0.5)",
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        1,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "road-street",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, surface"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 11,
                "filter": [
                    "all",
                    [
                        "match",
                        ["get", "class"],
                        ["street", "street_limited", "primary_link"],
                        true,
                        false
                    ],
                    ["match", ["get", "structure"], ["none", "ford"], true, false],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {
                    "line-cap": ["step", ["zoom"], "butt", 14, "round"],
                    "line-join": ["step", ["zoom"], "miter", 14, "round"]
                },
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        12,
                        0.5,
                        14,
                        2,
                        18,
                        18
                    ],
                    "line-color": [
                        "match",
                        ["get", "class"],
                        "street_limited",
                        "hsl(0, 2%, 22%)",
                        "hsla(0, 0%, 90%, 0.5)"
                    ],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        0.3,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "road-secondary-tertiary",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, surface"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 8,
                "filter": [
                    "all",
                    [
                        "match",
                        ["get", "class"],
                        ["secondary", "tertiary"],
                        true,
                        false
                    ],
                    ["match", ["get", "structure"], ["none", "ford"], true, false],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {
                    "line-cap": ["step", ["zoom"], "butt", 14, "round"],
                    "line-join": ["step", ["zoom"], "miter", 14, "round"]
                },
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        5,
                        0.1,
                        18,
                        26
                    ],
                    "line-color": "hsla(0, 0%, 90%, 0.5)",
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        0.3,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "road-primary",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, surface"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 6,
                "filter": [
                    "all",
                    ["==", ["get", "class"], "primary"],
                    ["match", ["get", "structure"], ["none", "ford"], true, false],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {
                    "line-cap": ["step", ["zoom"], "butt", 14, "round"],
                    "line-join": ["step", ["zoom"], "miter", 14, "round"]
                },
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        5,
                        0.75,
                        18,
                        32
                    ],
                    "line-color": "hsla(0, 0%, 90%, 0.5)",
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        1,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "road-oneway-arrow-blue",
                "type": "symbol",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, surface"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 15,
                "filter": [
                    "all",
                    ["==", ["get", "oneway"], "true"],
                    [
                        "step",
                        ["zoom"],
                        [
                            "match",
                            ["get", "class"],
                            [
                                "primary",
                                "secondary",
                                "tertiary",
                                "street",
                                "street_limited"
                            ],
                            true,
                            false
                        ],
                        16,
                        [
                            "match",
                            ["get", "class"],
                            [
                                "primary",
                                "secondary",
                                "tertiary",
                                "street",
                                "street_limited",
                                "primary_link",
                                "secondary_link",
                                "tertiary_link"
                            ],
                            true,
                            false
                        ]
                    ],
                    ["match", ["get", "structure"], ["none", "ford"], true, false]
                ],
                "layout": {
                    "symbol-placement": "line",
                    "icon-image": [
                        "step",
                        ["zoom"],
                        "oneway-small",
                        17,
                        [
                            "match",
                            ["get", "class"],
                            [
                                "primary",
                                "secondary",
                                "tertiary",
                                "street",
                                "street_limited"
                            ],
                            "oneway-large",
                            "oneway-small"
                        ],
                        18,
                        "oneway-large"
                    ],
                    "symbol-spacing": 200,
                    "icon-rotation-alignment": "map"
                },
                "paint": {}
            },
            {
                "id": "road-motorway-trunk",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, surface"
                },
                "source": "composite",
                "source-layer": "road",
                "filter": [
                    "all",
                    ["match", ["get", "class"], ["motorway", "trunk"], true, false],
                    ["match", ["get", "structure"], ["none", "ford"], true, false],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {
                    "line-cap": ["step", ["zoom"], "butt", 13, "round"],
                    "line-join": ["step", ["zoom"], "miter", 13, "round"]
                },
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        5,
                        0.75,
                        18,
                        32
                    ],
                    "line-color": [
                        "step",
                        ["zoom"],
                        [
                            "match",
                            ["get", "class"],
                            "motorway",
                            "hsla(26, 87%, 62%, 0.7)",
                            "hsla(0, 0%, 90%, 0.5)"
                        ],
                        6,
                        [
                            "match",
                            ["get", "class"],
                            "motorway",
                            "hsla(26, 87%, 62%, 0.7)",
                            "hsla(46, 80%, 60%, 0.7)"
                        ],
                        9,
                        [
                            "match",
                            ["get", "class"],
                            "motorway",
                            "hsla(26, 100%, 68%, 0.7)",
                            "hsla(46, 87%, 68%, 0.7)"
                        ]
                    ],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        1,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "road-oneway-arrow-white",
                "type": "symbol",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, surface-icons"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 16,
                "filter": [
                    "all",
                    ["==", ["get", "oneway"], "true"],
                    [
                        "match",
                        ["get", "class"],
                        ["motorway", "trunk", "motorway_link", "trunk_link"],
                        true,
                        false
                    ],
                    ["match", ["get", "structure"], ["none", "ford"], true, false]
                ],
                "layout": {
                    "symbol-placement": "line",
                    "icon-image": [
                        "step",
                        ["zoom"],
                        "oneway-white-small",
                        17,
                        "oneway-white-large"
                    ],
                    "symbol-spacing": 200
                },
                "paint": {}
            },
            {
                "id": "bridge-path",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "walking-cycling",
                    "mapbox:group": "Walking, cycling, etc., barriers-bridges"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 14,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "bridge"],
                    ["==", ["get", "class"], "path"],
                    ["==", ["geometry-type"], "LineString"],
                    ["!=", ["get", "type"], "steps"]
                ],
                "layout": {"line-join": "round"},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        15,
                        1,
                        18,
                        4
                    ],
                    "line-color": "hsl(0, 0%, 22%)",
                    "line-dasharray": [
                        "step",
                        ["zoom"],
                        ["literal", [4, 0.3]],
                        15,
                        ["literal", [1.75, 0.3]],
                        16,
                        ["literal", [1, 0.3]],
                        17,
                        ["literal", [1, 0.25]]
                    ]
                }
            },
            {
                "id": "bridge-steps",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "walking-cycling",
                    "mapbox:group": "Walking, cycling, etc., barriers-bridges"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 14,
                "filter": [
                    "all",
                    ["==", ["get", "type"], "steps"],
                    ["==", ["get", "structure"], "bridge"],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {"line-join": "round"},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        15,
                        1,
                        16,
                        1.6,
                        18,
                        6
                    ],
                    "line-color": "hsl(0, 0%, 22%)",
                    "line-dasharray": [
                        "step",
                        ["zoom"],
                        ["literal", [1, 0]],
                        15,
                        ["literal", [1.75, 1]],
                        16,
                        ["literal", [1, 0.75]],
                        17,
                        ["literal", [0.3, 0.3]]
                    ]
                }
            },
            {
                "id": "bridge-pedestrian",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "walking-cycling",
                    "mapbox:group": "Walking, cycling, etc., barriers-bridges"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 13,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "bridge"],
                    ["==", ["get", "class"], "pedestrian"],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {"line-join": "round"},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        14,
                        0.5,
                        18,
                        12
                    ],
                    "line-color": "hsl(0, 0%, 22%)",
                    "line-dasharray": [
                        "step",
                        ["zoom"],
                        ["literal", [1, 0]],
                        15,
                        ["literal", [1.5, 0.4]],
                        16,
                        ["literal", [1, 0.2]]
                    ]
                }
            },
            {
                "id": "bridge-street-minor-low",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, bridges"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 13,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "bridge"],
                    [
                        "step",
                        ["zoom"],
                        [
                            "match",
                            ["get", "class"],
                            ["street", "street_limited", "primary_link"],
                            true,
                            false
                        ],
                        14,
                        [
                            "match",
                            ["get", "class"],
                            [
                                "street",
                                "street_limited",
                                "primary_link",
                                "secondary_link",
                                "tertiary_link"
                            ],
                            true,
                            false
                        ]
                    ],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {
                    "line-cap": ["step", ["zoom"], "butt", 14, "round"],
                    "line-join": ["step", ["zoom"], "miter", 14, "round"]
                },
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        12,
                        0.5,
                        14,
                        [
                            "match",
                            ["get", "class"],
                            ["street", "street_limited", "primary_link"],
                            2,
                            "track",
                            1,
                            0.5
                        ],
                        18,
                        [
                            "match",
                            ["get", "class"],
                            ["street", "street_limited", "primary_link"],
                            18,
                            12
                        ]
                    ],
                    "line-color": "hsla(0, 0%, 90%, 0.5)",
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        1,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "bridge-street-minor-case",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, bridges"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 13,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "bridge"],
                    [
                        "step",
                        ["zoom"],
                        [
                            "match",
                            ["get", "class"],
                            ["street", "street_limited", "primary_link"],
                            true,
                            false
                        ],
                        14,
                        [
                            "match",
                            ["get", "class"],
                            [
                                "street",
                                "street_limited",
                                "primary_link",
                                "secondary_link",
                                "tertiary_link"
                            ],
                            true,
                            false
                        ]
                    ],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {"line-join": "round"},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        12,
                        0.75,
                        20,
                        2
                    ],
                    "line-color": "hsla(0, 1%, 10%, 0.5)",
                    "line-gap-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        12,
                        0.5,
                        14,
                        [
                            "match",
                            ["get", "class"],
                            ["street", "street_limited", "primary_link"],
                            2,
                            "track",
                            1,
                            0.5
                        ],
                        18,
                        [
                            "match",
                            ["get", "class"],
                            ["street", "street_limited", "primary_link"],
                            18,
                            12
                        ]
                    ],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        1,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "bridge-primary-secondary-tertiary-case",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, bridges"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 13,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "bridge"],
                    [
                        "match",
                        ["get", "class"],
                        ["primary", "secondary", "tertiary"],
                        true,
                        false
                    ],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {"line-join": "round"},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        10,
                        ["match", ["get", "class"], "primary", 1, 0.75],
                        18,
                        2
                    ],
                    "line-color": "hsla(0, 1%, 10%, 0.5)",
                    "line-gap-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        5,
                        ["match", ["get", "class"], "primary", 0.75, 0.1],
                        18,
                        ["match", ["get", "class"], "primary", 32, 26]
                    ],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        0.3,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "bridge-major-link-case",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, bridges"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 13,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "bridge"],
                    [
                        "match",
                        ["get", "class"],
                        ["motorway_link", "trunk_link"],
                        true,
                        false
                    ],
                    ["<=", ["get", "layer"], 1],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {"line-join": "round"},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        12,
                        0.75,
                        20,
                        2
                    ],
                    "line-color": "hsla(0, 1%, 22%, 0.5)",
                    "line-gap-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        12,
                        0.5,
                        14,
                        2,
                        18,
                        18
                    ],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        1,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "bridge-motorway-trunk-case",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, bridges"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 13,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "bridge"],
                    ["match", ["get", "class"], ["motorway", "trunk"], true, false],
                    ["<=", ["get", "layer"], 1],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {"line-join": "round"},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        10,
                        1,
                        18,
                        2
                    ],
                    "line-color": "hsla(0, 1%, 22%, 0.5)",
                    "line-gap-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        5,
                        0.75,
                        18,
                        32
                    ],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        1,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "bridge-major-link",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, bridges"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 13,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "bridge"],
                    [
                        "match",
                        ["get", "class"],
                        ["motorway_link", "trunk_link"],
                        true,
                        false
                    ],
                    ["<=", ["get", "layer"], 1],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {"line-cap": "round", "line-join": "round"},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        12,
                        0.5,
                        14,
                        2,
                        18,
                        18
                    ],
                    "line-color": [
                        "match",
                        ["get", "class"],
                        "motorway_link",
                        "hsla(26, 100%, 68%, 0.7)",
                        "hsla(46, 87%, 68%, 0.7)"
                    ],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        1,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "bridge-street-minor",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, bridges"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 13,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "bridge"],
                    [
                        "step",
                        ["zoom"],
                        [
                            "match",
                            ["get", "class"],
                            ["street", "street_limited", "primary_link"],
                            true,
                            false
                        ],
                        14,
                        [
                            "match",
                            ["get", "class"],
                            [
                                "street",
                                "street_limited",
                                "primary_link",
                                "secondary_link",
                                "tertiary_link"
                            ],
                            true,
                            false
                        ]
                    ],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {
                    "line-cap": ["step", ["zoom"], "butt", 14, "round"],
                    "line-join": ["step", ["zoom"], "miter", 14, "round"]
                },
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        12,
                        0.5,
                        14,
                        [
                            "match",
                            ["get", "class"],
                            ["street", "street_limited", "primary_link"],
                            2,
                            "track",
                            1,
                            0.5
                        ],
                        18,
                        [
                            "match",
                            ["get", "class"],
                            ["street", "street_limited", "primary_link"],
                            18,
                            12
                        ]
                    ],
                    "line-color": [
                        "match",
                        ["get", "class"],
                        "street_limited",
                        "hsl(0, 2%, 22%)",
                        "hsla(0, 0%, 90%, 0.5)"
                    ],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        1,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "bridge-primary-secondary-tertiary",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, bridges"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 13,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "bridge"],
                    [
                        "match",
                        ["get", "class"],
                        ["primary", "secondary", "tertiary"],
                        true,
                        false
                    ],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {
                    "line-cap": ["step", ["zoom"], "butt", 14, "round"],
                    "line-join": ["step", ["zoom"], "miter", 14, "round"]
                },
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        5,
                        ["match", ["get", "class"], "primary", 0.75, 0.1],
                        18,
                        ["match", ["get", "class"], "primary", 32, 26]
                    ],
                    "line-color": "hsla(0, 0%, 90%, 0.5)",
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        0.3,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "bridge-oneway-arrow-blue",
                "type": "symbol",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, bridges"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 15,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "bridge"],
                    ["==", ["get", "oneway"], "true"],
                    [
                        "step",
                        ["zoom"],
                        [
                            "match",
                            ["get", "class"],
                            [
                                "primary",
                                "secondary",
                                "tertiary",
                                "street",
                                "street_limited"
                            ],
                            true,
                            false
                        ],
                        16,
                        [
                            "match",
                            ["get", "class"],
                            [
                                "primary",
                                "secondary",
                                "tertiary",
                                "street",
                                "street_limited",
                                "primary_link",
                                "secondary_link",
                                "tertiary_link"
                            ],
                            true,
                            false
                        ]
                    ]
                ],
                "layout": {
                    "symbol-placement": "line",
                    "icon-image": [
                        "step",
                        ["zoom"],
                        "oneway-small",
                        17,
                        [
                            "match",
                            ["get", "class"],
                            [
                                "primary",
                                "secondary",
                                "tertiary",
                                "street",
                                "street_limited"
                            ],
                            "oneway-large",
                            "oneway-small"
                        ],
                        18,
                        "oneway-large"
                    ],
                    "symbol-spacing": 200,
                    "icon-rotation-alignment": "map"
                },
                "paint": {}
            },
            {
                "id": "bridge-motorway-trunk",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, bridges"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 13,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "bridge"],
                    ["match", ["get", "class"], ["motorway", "trunk"], true, false],
                    ["<=", ["get", "layer"], 1],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {"line-cap": "round", "line-join": "round"},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        5,
                        0.75,
                        18,
                        32
                    ],
                    "line-color": [
                        "match",
                        ["get", "class"],
                        "motorway",
                        "hsla(26, 100%, 68%, 0.7)",
                        "hsla(46, 87%, 68%, 0.7)"
                    ],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        1,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "bridge-major-link-2-case",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, bridges"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 13,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "bridge"],
                    [">=", ["get", "layer"], 2],
                    [
                        "match",
                        ["get", "class"],
                        ["motorway_link", "trunk_link"],
                        true,
                        false
                    ],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {"line-join": "round"},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        12,
                        0.75,
                        20,
                        2
                    ],
                    "line-color": "hsla(0, 1%, 22%, 0.5)",
                    "line-gap-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        12,
                        0.5,
                        14,
                        2,
                        18,
                        18
                    ],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        1,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "bridge-motorway-trunk-2-case",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, bridges"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 13,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "bridge"],
                    [">=", ["get", "layer"], 2],
                    ["match", ["get", "class"], ["motorway", "trunk"], true, false],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {"line-join": "round"},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        10,
                        1,
                        18,
                        2
                    ],
                    "line-color": "hsla(0, 1%, 22%, 0.5)",
                    "line-gap-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        5,
                        0.75,
                        18,
                        32
                    ],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        1,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "bridge-major-link-2",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, bridges"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 13,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "bridge"],
                    [">=", ["get", "layer"], 2],
                    [
                        "match",
                        ["get", "class"],
                        ["motorway_link", "trunk_link"],
                        true,
                        false
                    ],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {"line-cap": "round", "line-join": "round"},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        12,
                        0.5,
                        14,
                        2,
                        18,
                        18
                    ],
                    "line-color": [
                        "match",
                        ["get", "class"],
                        "motorway_link",
                        "hsla(26, 100%, 68%, 0.7)",
                        "hsla(46, 87%, 68%, 0.7)"
                    ],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        1,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "bridge-motorway-trunk-2",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, bridges"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 13,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "bridge"],
                    [">=", ["get", "layer"], 2],
                    ["match", ["get", "class"], ["motorway", "trunk"], true, false],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {
                    "line-cap": ["step", ["zoom"], "butt", 14, "round"],
                    "line-join": ["step", ["zoom"], "miter", 14, "round"]
                },
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        5,
                        0.75,
                        18,
                        32
                    ],
                    "line-color": [
                        "match",
                        ["get", "class"],
                        "motorway",
                        "hsla(26, 100%, 68%, 0.7)",
                        "hsla(46, 87%, 68%, 0.7)"
                    ],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        1,
                        15,
                        0
                    ]
                }
            },
            {
                "id": "bridge-oneway-arrow-white",
                "type": "symbol",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, bridges"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 16,
                "filter": [
                    "all",
                    ["==", ["get", "structure"], "bridge"],
                    [
                        "match",
                        ["get", "class"],
                        ["motorway", "trunk", "motorway_link", "trunk_link"],
                        true,
                        false
                    ],
                    ["==", ["get", "oneway"], "true"]
                ],
                "layout": {
                    "symbol-placement": "line",
                    "icon-image": [
                        "step",
                        ["zoom"],
                        "oneway-white-small",
                        17,
                        "oneway-white-large"
                    ],
                    "symbol-spacing": 200
                },
                "paint": {}
            },
            {
                "id": "aerialway",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "transit",
                    "mapbox:group": "Transit, elevated"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 12,
                "filter": ["==", ["get", "class"], "aerialway"],
                "layout": {"line-join": "round"},
                "paint": {
                    "line-color": "hsla(0, 0%, 25%, 0.5)",
                    "line-width": [
                        "interpolate",
                        ["exponential", 1.5],
                        ["zoom"],
                        14,
                        0.5,
                        20,
                        1
                    ]
                }
            },
            {
                "id": "admin-1-boundary-bg",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "admin-boundaries",
                    "mapbox:group": "Administrative boundaries, admin"
                },
                "source": "composite",
                "source-layer": "admin",
                "minzoom": 7,
                "filter": [
                    "all",
                    ["==", ["get", "admin_level"], 1],
                    ["==", ["get", "maritime"], "false"],
                    ["match", ["get", "worldview"], ["all", "US"], true, false]
                ],
                "layout": {"line-join": "bevel"},
                "paint": {
                    "line-color": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        8,
                        "hsl(0, 0%, 14%)",
                        16,
                        "hsl(0, 0%, 30%)"
                    ],
                    "line-width": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        7,
                        3.75,
                        12,
                        5.5
                    ],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        7,
                        0,
                        8,
                        0.75
                    ],
                    "line-dasharray": [1, 0],
                    "line-blur": ["interpolate", ["linear"], ["zoom"], 3, 0, 8, 3]
                }
            },
            {
                "id": "admin-0-boundary-bg",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "admin-boundaries",
                    "mapbox:group": "Administrative boundaries, admin"
                },
                "source": "composite",
                "source-layer": "admin",
                "minzoom": 1,
                "filter": [
                    "all",
                    ["==", ["get", "admin_level"], 0],
                    ["==", ["get", "maritime"], "false"],
                    ["match", ["get", "worldview"], ["all", "US"], true, false]
                ],
                "layout": {},
                "paint": {
                    "line-width": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        3,
                        5.25,
                        10,
                        12
                    ],
                    "line-color": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        6,
                        "hsl(0, 0%, 14%)",
                        8,
                        "hsl(0, 0%, 30%)"
                    ],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        3,
                        0,
                        4,
                        0.5
                    ],
                    "line-blur": ["interpolate", ["linear"], ["zoom"], 3, 0, 10, 3]
                }
            },
            {
                "id": "admin-1-boundary",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "admin-boundaries",
                    "mapbox:group": "Administrative boundaries, admin"
                },
                "source": "composite",
                "source-layer": "admin",
                "minzoom": 2,
                "filter": [
                    "all",
                    ["==", ["get", "admin_level"], 1],
                    ["==", ["get", "maritime"], "false"],
                    ["match", ["get", "worldview"], ["all", "US"], true, false]
                ],
                "layout": {"line-join": "round", "line-cap": "round"},
                "paint": {
                    "line-dasharray": [
                        "step",
                        ["zoom"],
                        ["literal", [2, 0]],
                        7,
                        ["literal", [2, 2, 6, 2]]
                    ],
                    "line-width": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        7,
                        0.75,
                        12,
                        1.5
                    ],
                    "line-opacity": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        2,
                        0,
                        3,
                        1
                    ],
                    "line-color": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        3,
                        "hsl(0, 0%, 0%)",
                        7,
                        "hsl(0, 0%, 5%)"
                    ]
                }
            },
            {
                "id": "admin-0-boundary",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "admin-boundaries",
                    "mapbox:group": "Administrative boundaries, admin"
                },
                "source": "composite",
                "source-layer": "admin",
                "minzoom": 1,
                "filter": [
                    "all",
                    ["==", ["get", "admin_level"], 0],
                    ["==", ["get", "disputed"], "false"],
                    ["==", ["get", "maritime"], "false"],
                    ["match", ["get", "worldview"], ["all", "US"], true, false]
                ],
                "layout": {"line-join": "round", "line-cap": "round"},
                "paint": {
                    "line-color": "hsl(0, 0%, 0%)",
                    "line-width": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        3,
                        0.75,
                        10,
                        3
                    ],
                    "line-dasharray": [10, 0]
                }
            },
            {
                "id": "admin-0-boundary-disputed",
                "type": "line",
                "metadata": {
                    "mapbox:featureComponent": "admin-boundaries",
                    "mapbox:group": "Administrative boundaries, admin"
                },
                "source": "composite",
                "source-layer": "admin",
                "minzoom": 1,
                "filter": [
                    "all",
                    ["==", ["get", "disputed"], "true"],
                    ["==", ["get", "admin_level"], 0],
                    ["==", ["get", "maritime"], "false"],
                    ["match", ["get", "worldview"], ["all", "US"], true, false]
                ],
                "layout": {"line-join": "round"},
                "paint": {
                    "line-color": "hsl(0, 0%, 0%)",
                    "line-width": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        3,
                        0.75,
                        10,
                        3
                    ],
                    "line-dasharray": [
                        "step",
                        ["zoom"],
                        ["literal", [3.25, 3.25]],
                        6,
                        ["literal", [2.5, 2.5]],
                        7,
                        ["literal", [2, 2.25]],
                        8,
                        ["literal", [1.75, 2]]
                    ]
                }
            },
            {
                "id": "road-label",
                "type": "symbol",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, road-labels"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 10,
                "filter": [
                    "step",
                    ["zoom"],
                    [
                        "match",
                        ["get", "class"],
                        ["motorway", "trunk", "primary", "secondary", "tertiary"],
                        true,
                        false
                    ],
                    12,
                    [
                        "match",
                        ["get", "class"],
                        [
                            "motorway",
                            "trunk",
                            "primary",
                            "secondary",
                            "tertiary",
                            "street",
                            "street_limited"
                        ],
                        true,
                        false
                    ],
                    15,
                    [
                        "match",
                        ["get", "class"],
                        [
                            "path",
                            "pedestrian",
                            "golf",
                            "ferry",
                            "aerialway",
                            "track",
                            "service"
                        ],
                        true,
                        false
                    ]
                ],
                "layout": {
                    "text-size": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        10,
                        [
                            "match",
                            ["get", "class"],
                            [
                                "motorway",
                                "trunk",
                                "primary",
                                "secondary",
                                "tertiary"
                            ],
                            10,
                            [
                                "motorway_link",
                                "trunk_link",
                                "primary_link",
                                "secondary_link",
                                "tertiary_link",
                                "street",
                                "street_limited"
                            ],
                            9,
                            6.5
                        ],
                        18,
                        [
                            "match",
                            ["get", "class"],
                            [
                                "motorway",
                                "trunk",
                                "primary",
                                "secondary",
                                "tertiary"
                            ],
                            16,
                            [
                                "motorway_link",
                                "trunk_link",
                                "primary_link",
                                "secondary_link",
                                "tertiary_link",
                                "street",
                                "street_limited"
                            ],
                            14,
                            13
                        ]
                    ],
                    "text-max-angle": 30,
                    "text-font": ["DIN Pro Medium", "Arial Unicode MS Regular"],
                    "symbol-placement": "line",
                    "text-padding": 1,
                    "text-rotation-alignment": "map",
                    "text-pitch-alignment": "viewport",
                    "text-field": ["coalesce", ["get", "name_en"], ["get", "name"]],
                    "text-letter-spacing": 0.01
                },
                "paint": {
                    "text-color": "hsl(0, 0%, 100%)",
                    "text-halo-color": [
                        "match",
                        ["get", "class"],
                        ["motorway", "trunk"],
                        "hsla(0, 5%, 0%, 0.75)",
                        "hsl(0, 5%, 0%)"
                    ],
                    "text-halo-width": 1,
                    "text-halo-blur": 1
                }
            },
            {
                "id": "road-intersection",
                "type": "symbol",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, road-labels"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 15,
                "filter": [
                    "all",
                    ["==", ["get", "class"], "intersection"],
                    ["has", "name"]
                ],
                "layout": {
                    "text-field": ["coalesce", ["get", "name_en"], ["get", "name"]],
                    "icon-image": "intersection",
                    "icon-text-fit": "both",
                    "icon-text-fit-padding": [1, 2, 1, 2],
                    "text-size": [
                        "interpolate",
                        ["exponential", 1.2],
                        ["zoom"],
                        15,
                        9,
                        18,
                        12
                    ],
                    "text-font": ["DIN Pro Bold", "Arial Unicode MS Bold"]
                },
                "paint": {"text-color": "hsl(230, 57%, 64%)"}
            },
            {
                "id": "road-number-shield",
                "type": "symbol",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, road-labels"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 6,
                "filter": [
                    "all",
                    ["has", "reflen"],
                    ["<=", ["get", "reflen"], 6],
                    [
                        "step",
                        ["zoom"],
                        ["==", ["geometry-type"], "Point"],
                        11,
                        [">", ["get", "len"], 5000],
                        12,
                        [">", ["get", "len"], 2500],
                        13,
                        [">", ["get", "len"], 1000],
                        14,
                        true
                    ]
                ],
                "layout": {
                    "text-size": 9,
                    "icon-image": [
                        "concat",
                        ["get", "shield"],
                        "-",
                        ["to-string", ["get", "reflen"]]
                    ],
                    "icon-rotation-alignment": "viewport",
                    "text-max-angle": 38,
                    "symbol-spacing": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        11,
                        150,
                        14,
                        200
                    ],
                    "text-font": ["DIN Pro Bold", "Arial Unicode MS Bold"],
                    "symbol-placement": ["step", ["zoom"], "point", 11, "line"],
                    "text-rotation-alignment": "viewport",
                    "text-field": ["get", "ref"],
                    "text-letter-spacing": 0.05
                },
                "paint": {
                    "text-color": [
                        "match",
                        ["get", "shield_text_color"],
                        "white",
                        "hsl(0, 0%, 100%)",
                        "yellow",
                        "hsl(50, 100%, 70%)",
                        "orange",
                        "hsl(25, 100%, 75%)",
                        "blue",
                        "hsl(230, 57%, 44%)",
                        "hsl(230, 18%, 13%)"
                    ]
                }
            },
            {
                "id": "road-exit-shield",
                "type": "symbol",
                "metadata": {
                    "mapbox:featureComponent": "road-network",
                    "mapbox:group": "Road network, road-labels"
                },
                "source": "composite",
                "source-layer": "motorway_junction",
                "minzoom": 14,
                "filter": ["all", ["has", "reflen"], ["<=", ["get", "reflen"], 9]],
                "layout": {
                    "text-field": ["get", "ref"],
                    "text-size": 9,
                    "icon-image": [
                        "concat",
                        "motorway-exit-",
                        ["to-string", ["get", "reflen"]]
                    ],
                    "text-font": ["DIN Pro Bold", "Arial Unicode MS Bold"]
                },
                "paint": {
                    "text-color": "hsl(0, 0%, 100%)",
                    "text-translate": [0, 0]
                }
            },
            {
                "id": "ferry-aerialway-label",
                "type": "symbol",
                "metadata": {
                    "mapbox:featureComponent": "transit",
                    "mapbox:group": "Transit, ferry-aerialway-labels"
                },
                "source": "composite",
                "source-layer": "road",
                "minzoom": 15,
                "filter": ["match", ["get", "class"], "aerialway", true, false],
                "layout": {
                    "text-size": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        10,
                        6.5,
                        18,
                        13
                    ],
                    "text-max-angle": 30,
                    "text-font": ["DIN Pro Medium", "Arial Unicode MS Regular"],
                    "symbol-placement": "line",
                    "text-padding": 1,
                    "text-rotation-alignment": "map",
                    "text-pitch-alignment": "viewport",
                    "text-field": ["coalesce", ["get", "name_en"], ["get", "name"]],
                    "text-letter-spacing": 0.01
                },
                "paint": {
                    "text-color": [
                        "match",
                        ["get", "class"],
                        "ferry",
                        "hsl(196, 18%, 86%)",
                        "hsl(0, 0%, 100%)"
                    ],
                    "text-halo-color": [
                        "match",
                        ["get", "class"],
                        "ferry",
                        "hsl(196, 50%, 50%)",
                        "hsl(0, 5%, 0%)"
                    ],
                    "text-halo-width": 1,
                    "text-halo-blur": 1
                }
            },
            {
                "id": "waterway-label",
                "type": "symbol",
                "metadata": {
                    "mapbox:featureComponent": "natural-features",
                    "mapbox:group": "Natural features, natural-labels"
                },
                "source": "composite",
                "source-layer": "natural_label",
                "minzoom": 13,
                "filter": [
                    "all",
                    [
                        "match",
                        ["get", "class"],
                        ["canal", "river", "stream"],
                        ["match", ["get", "worldview"], ["all", "US"], true, false],
                        ["disputed_canal", "disputed_river", "disputed_stream"],
                        [
                            "all",
                            ["==", ["get", "disputed"], "true"],
                            [
                                "match",
                                ["get", "worldview"],
                                ["all", "US"],
                                true,
                                false
                            ]
                        ],
                        false
                    ],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {
                    "text-font": ["DIN Pro Italic", "Arial Unicode MS Regular"],
                    "text-max-angle": 30,
                    "symbol-spacing": [
                        "interpolate",
                        ["linear", 1],
                        ["zoom"],
                        15,
                        250,
                        17,
                        400
                    ],
                    "text-size": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        13,
                        12,
                        18,
                        16
                    ],
                    "symbol-placement": "line",
                    "text-pitch-alignment": "viewport",
                    "text-field": ["coalesce", ["get", "name_en"], ["get", "name"]]
                },
                "paint": {"text-color": "hsl(196, 18%, 90%)"}
            },
            {
                "id": "natural-line-label",
                "type": "symbol",
                "source": "composite",
                "source-layer": "natural_label",
                "minzoom": 4,
                "filter": [
                    "all",
                    [
                        "match",
                        ["get", "class"],
                        ["glacier", "landform"],
                        true,
                        false
                    ],
                    ["==", ["geometry-type"], "LineString"],
                    ["<=", ["get", "filterrank"], 4]
                ],
                "layout": {
                    "text-size": [
                        "step",
                        ["zoom"],
                        ["step", ["get", "sizerank"], 18, 5, 12],
                        17,
                        ["step", ["get", "sizerank"], 18, 13, 12]
                    ],
                    "text-max-angle": 30,
                    "text-field": ["coalesce", ["get", "name_en"], ["get", "name"]],
                    "text-font": [
                        "DIN Offc Pro Medium",
                        "Arial Unicode MS Regular"
                    ],
                    "symbol-placement": "line-center",
                    "text-pitch-alignment": "viewport"
                },
                "paint": {
                    "text-halo-width": 0.5,
                    "text-halo-color": "hsl(0, 0%, 100%)",
                    "text-halo-blur": 0.5,
                    "text-color": [
                        "step",
                        ["zoom"],
                        [
                            "step",
                            ["get", "sizerank"],
                            "hsl(26, 20%, 42%)",
                            5,
                            "hsl(26, 25%, 32%)"
                        ],
                        17,
                        [
                            "step",
                            ["get", "sizerank"],
                            "hsl(26, 20%, 42%)",
                            13,
                            "hsl(26, 25%, 32%)"
                        ]
                    ]
                }
            },
            {
                "id": "natural-point-label",
                "type": "symbol",
                "source": "composite",
                "source-layer": "natural_label",
                "minzoom": 4,
                "filter": [
                    "all",
                    [
                        "match",
                        ["get", "class"],
                        ["dock", "glacier", "water_feature", "wetland"],
                        true,
                        false
                    ],
                    ["==", ["geometry-type"], "Point"],
                    ["<=", ["get", "filterrank"], 4]
                ],
                "layout": {
                    "text-size": [
                        "step",
                        ["zoom"],
                        ["step", ["get", "sizerank"], 18, 5, 12],
                        17,
                        ["step", ["get", "sizerank"], 18, 13, 12]
                    ],
                    "icon-image": [
                        "step",
                        ["zoom"],
                        ["concat", ["get", "maki"], "-11"],
                        15,
                        ["concat", ["get", "maki"], "-15"]
                    ],
                    "text-font": [
                        "DIN Offc Pro Medium",
                        "Arial Unicode MS Regular"
                    ],
                    "text-offset": [
                        "step",
                        ["zoom"],
                        [
                            "step",
                            ["get", "sizerank"],
                            ["literal", [0, 0]],
                            5,
                            ["literal", [0, 0.75]]
                        ],
                        17,
                        [
                            "step",
                            ["get", "sizerank"],
                            ["literal", [0, 0]],
                            13,
                            ["literal", [0, 0.75]]
                        ]
                    ],
                    "text-anchor": [
                        "step",
                        ["zoom"],
                        ["step", ["get", "sizerank"], "center", 5, "top"],
                        17,
                        ["step", ["get", "sizerank"], "center", 13, "top"]
                    ],
                    "text-field": ["coalesce", ["get", "name_en"], ["get", "name"]]
                },
                "paint": {
                    "icon-opacity": [
                        "step",
                        ["zoom"],
                        ["step", ["get", "sizerank"], 0, 5, 1],
                        17,
                        ["step", ["get", "sizerank"], 0, 13, 1]
                    ],
                    "text-halo-color": "hsl(0, 0%, 100%)",
                    "text-halo-width": 0.5,
                    "text-halo-blur": 0.5,
                    "text-color": [
                        "step",
                        ["zoom"],
                        [
                            "step",
                            ["get", "sizerank"],
                            "hsl(26, 20%, 42%)",
                            5,
                            "hsl(26, 25%, 32%)"
                        ],
                        17,
                        [
                            "step",
                            ["get", "sizerank"],
                            "hsl(26, 20%, 42%)",
                            13,
                            "hsl(26, 25%, 32%)"
                        ]
                    ]
                }
            },
            {
                "id": "water-line-label",
                "type": "symbol",
                "metadata": {
                    "mapbox:featureComponent": "natural-features",
                    "mapbox:group": "Natural features, natural-labels"
                },
                "source": "composite",
                "source-layer": "natural_label",
                "filter": [
                    "all",
                    [
                        "match",
                        ["get", "class"],
                        ["bay", "ocean", "reservoir", "sea", "water"],
                        ["match", ["get", "worldview"], ["all", "US"], true, false],
                        [
                            "disputed_bay",
                            "disputed_ocean",
                            "disputed_reservoir",
                            "disputed_sea",
                            "disputed_water"
                        ],
                        [
                            "all",
                            ["==", ["get", "disputed"], "true"],
                            [
                                "match",
                                ["get", "worldview"],
                                ["all", "US"],
                                true,
                                false
                            ]
                        ],
                        false
                    ],
                    ["==", ["geometry-type"], "LineString"]
                ],
                "layout": {
                    "text-size": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        7,
                        ["step", ["get", "sizerank"], 20, 6, 18, 12, 12],
                        10,
                        ["step", ["get", "sizerank"], 15, 9, 12],
                        18,
                        ["step", ["get", "sizerank"], 15, 9, 14]
                    ],
                    "text-max-angle": 30,
                    "text-letter-spacing": [
                        "match",
                        ["get", "class"],
                        "ocean",
                        0.25,
                        ["sea", "bay"],
                        0.15,
                        0
                    ],
                    "text-font": ["DIN Pro Italic", "Arial Unicode MS Regular"],
                    "symbol-placement": "line-center",
                    "text-pitch-alignment": "viewport",
                    "text-field": ["coalesce", ["get", "name_en"], ["get", "name"]]
                },
                "paint": {
                    "text-color": [
                        "match",
                        ["get", "class"],
                        ["bay", "ocean", "sea"],
                        "hsl(196, 46%, 70%)",
                        "hsl(196, 18%, 90%)"
                    ]
                }
            },
            {
                "id": "water-point-label",
                "type": "symbol",
                "metadata": {
                    "mapbox:featureComponent": "natural-features",
                    "mapbox:group": "Natural features, natural-labels"
                },
                "source": "composite",
                "source-layer": "natural_label",
                "filter": [
                    "all",
                    [
                        "match",
                        ["get", "class"],
                        ["bay", "ocean", "reservoir", "sea", "water"],
                        ["match", ["get", "worldview"], ["all", "US"], true, false],
                        [
                            "disputed_bay",
                            "disputed_ocean",
                            "disputed_reservoir",
                            "disputed_sea",
                            "disputed_water"
                        ],
                        [
                            "all",
                            ["==", ["get", "disputed"], "true"],
                            [
                                "match",
                                ["get", "worldview"],
                                ["all", "US"],
                                true,
                                false
                            ]
                        ],
                        false
                    ],
                    ["==", ["geometry-type"], "Point"]
                ],
                "layout": {
                    "text-line-height": 1.3,
                    "text-size": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        7,
                        ["step", ["get", "sizerank"], 20, 6, 15, 12, 12],
                        10,
                        ["step", ["get", "sizerank"], 15, 9, 12]
                    ],
                    "text-font": ["DIN Pro Italic", "Arial Unicode MS Regular"],
                    "text-field": ["coalesce", ["get", "name_en"], ["get", "name"]],
                    "text-letter-spacing": [
                        "match",
                        ["get", "class"],
                        "ocean",
                        0.25,
                        ["bay", "sea"],
                        0.15,
                        0.01
                    ],
                    "text-max-width": [
                        "match",
                        ["get", "class"],
                        "ocean",
                        4,
                        "sea",
                        5,
                        ["bay", "water"],
                        7,
                        10
                    ]
                },
                "paint": {
                    "text-color": [
                        "match",
                        ["get", "class"],
                        ["bay", "ocean", "sea"],
                        "hsl(196, 46%, 70%)",
                        "hsl(196, 18%, 90%)"
                    ]
                }
            },
            {
                "id": "poi-label",
                "type": "symbol",
                "metadata": {
                    "mapbox:featureComponent": "point-of-interest-labels",
                    "mapbox:group": "Point of interest labels, poi-labels"
                },
                "source": "composite",
                "source-layer": "poi_label",
                "minzoom": 6,
                "filter": [
                    "<=",
                    ["get", "filterrank"],
                    ["+", ["step", ["zoom"], 0, 16, 1, 17, 2], 3]
                ],
                "layout": {
                    "text-size": [
                        "step",
                        ["zoom"],
                        ["step", ["get", "sizerank"], 18, 5, 12],
                        17,
                        ["step", ["get", "sizerank"], 18, 13, 12]
                    ],
                    "icon-image": [
                        "step",
                        ["zoom"],
                        [
                            "case",
                            ["has", "maki_beta"],
                            ["image", ["concat", ["get", "maki_beta"], "-11"]],
                            ["image", ["concat", ["get", "maki"], "-11"]]
                        ],
                        15,
                        [
                            "case",
                            ["has", "maki_beta"],
                            ["image", ["concat", ["get", "maki_beta"], "-15"]],
                            ["image", ["concat", ["get", "maki"], "-15"]]
                        ]
                    ],
                    "text-font": ["DIN Pro Bold", "Arial Unicode MS Bold"],
                    "text-offset": [
                        "step",
                        ["zoom"],
                        [
                            "step",
                            ["get", "sizerank"],
                            ["literal", [0, 0]],
                            5,
                            ["literal", [0, 0.75]]
                        ],
                        17,
                        [
                            "step",
                            ["get", "sizerank"],
                            ["literal", [0, 0]],
                            13,
                            ["literal", [0, 0.75]]
                        ]
                    ],
                    "text-anchor": [
                        "step",
                        ["zoom"],
                        ["step", ["get", "sizerank"], "center", 5, "top"],
                        17,
                        ["step", ["get", "sizerank"], "center", 13, "top"]
                    ],
                    "text-field": ["coalesce", ["get", "name_en"], ["get", "name"]]
                },
                "paint": {
                    "icon-opacity": [
                        "step",
                        ["zoom"],
                        ["step", ["get", "sizerank"], 0, 5, 1],
                        17,
                        ["step", ["get", "sizerank"], 0, 13, 1]
                    ],
                    "text-halo-color": [
                        "match",
                        ["get", "class"],
                        "park_like",
                        "hsl(94, 100%, 0%)",
                        "medical",
                        "hsl(0, 5%, 3%)",
                        "hsl(0, 5%, 0%)"
                    ],
                    "text-halo-width": 0.5,
                    "text-halo-blur": 0.5,
                    "text-color": [
                        "step",
                        ["zoom"],
                        [
                            "step",
                            ["get", "sizerank"],
                            [
                                "match",
                                ["get", "class"],
                                "food_and_drink",
                                "hsl(356, 0%, 100%)",
                                "park_like",
                                "hsl(94, 100%, 84%)",
                                "hsl(0, 0%, 100%)"
                            ],
                            5,
                            [
                                "match",
                                ["get", "class"],
                                "food_and_drink",
                                "hsl(356, 0%, 100%)",
                                "park_like",
                                "hsl(94, 100%, 73%)",
                                "hsl(0, 0%, 100%)"
                            ]
                        ],
                        17,
                        [
                            "step",
                            ["get", "sizerank"],
                            [
                                "match",
                                ["get", "class"],
                                "food_and_drink",
                                "hsl(356, 0%, 100%)",
                                "park_like",
                                "hsl(94, 100%, 84%)",
                                "hsl(0, 0%, 100%)"
                            ],
                            13,
                            [
                                "match",
                                ["get", "class"],
                                "food_and_drink",
                                "hsl(356, 0%, 100%)",
                                "park_like",
                                "hsl(94, 100%, 73%)",
                                "hsl(0, 0%, 100%)"
                            ]
                        ]
                    ]
                }
            },
            {
                "id": "transit-label",
                "type": "symbol",
                "metadata": {
                    "mapbox:featureComponent": "transit",
                    "mapbox:group": "Transit, transit-labels"
                },
                "source": "composite",
                "source-layer": "transit_stop_label",
                "minzoom": 12,
                "filter": [
                    "step",
                    ["zoom"],
                    [
                        "all",
                        [
                            "match",
                            ["get", "mode"],
                            "rail",
                            true,
                            "metro_rail",
                            true,
                            false
                        ],
                        ["!=", ["get", "stop_type"], "entrance"]
                    ],
                    15,
                    [
                        "all",
                        [
                            "match",
                            ["get", "mode"],
                            "rail",
                            true,
                            "metro_rail",
                            true,
                            "light_rail",
                            true,
                            false
                        ],
                        ["!=", ["get", "stop_type"], "entrance"]
                    ],
                    16,
                    [
                        "all",
                        [
                            "match",
                            ["get", "mode"],
                            "ferry",
                            false,
                            "bus",
                            false,
                            true
                        ],
                        ["!=", ["get", "stop_type"], "entrance"]
                    ],
                    17,
                    [
                        "all",
                        ["match", ["get", "mode"], "ferry", false, true],
                        ["!=", ["get", "stop_type"], "entrance"]
                    ],
                    19,
                    ["match", ["get", "mode"], "ferry", false, true]
                ],
                "layout": {
                    "text-size": 12,
                    "icon-image": ["get", "network"],
                    "text-font": ["DIN Pro Bold", "Arial Unicode MS Bold"],
                    "text-justify": [
                        "match",
                        ["get", "stop_type"],
                        "entrance",
                        "left",
                        "center"
                    ],
                    "text-offset": [
                        "match",
                        ["get", "stop_type"],
                        "entrance",
                        ["literal", [1, 0]],
                        ["literal", [0, 0.8]]
                    ],
                    "text-anchor": [
                        "match",
                        ["get", "stop_type"],
                        "entrance",
                        "left",
                        "top"
                    ],
                    "text-field": [
                        "step",
                        ["zoom"],
                        "",
                        14,
                        [
                            "match",
                            ["get", "mode"],
                            ["rail", "metro_rail"],
                            ["coalesce", ["get", "name_en"], ["get", "name"]],
                            ""
                        ],
                        16,
                        [
                            "match",
                            ["get", "mode"],
                            ["bus", "bicycle"],
                            "",
                            ["coalesce", ["get", "name_en"], ["get", "name"]]
                        ],
                        18,
                        ["coalesce", ["get", "name_en"], ["get", "name"]]
                    ],
                    "text-letter-spacing": 0.01,
                    "text-max-width": [
                        "match",
                        ["get", "stop_type"],
                        "entrance",
                        15,
                        9
                    ]
                },
                "paint": {
                    "text-halo-color": "hsl(0, 5%, 0%)",
                    "text-color": "hsl(0, 0%, 100%)",
                    "text-halo-blur": 0.5,
                    "text-halo-width": 0.5
                }
            },
            {
                "id": "airport-label",
                "type": "symbol",
                "metadata": {
                    "mapbox:featureComponent": "transit",
                    "mapbox:group": "Transit, transit-labels"
                },
                "source": "composite",
                "source-layer": "airport_label",
                "minzoom": 8,
                "filter": [
                    "match",
                    ["get", "class"],
                    ["military", "civil"],
                    ["match", ["get", "worldview"], ["all", "US"], true, false],
                    ["disputed_military", "disputed_civil"],
                    [
                        "all",
                        ["==", ["get", "disputed"], "true"],
                        ["match", ["get", "worldview"], ["all", "US"], true, false]
                    ],
                    false
                ],
                "layout": {
                    "text-line-height": 1.1,
                    "text-size": ["step", ["get", "sizerank"], 18, 9, 12],
                    "icon-image": [
                        "step",
                        ["get", "sizerank"],
                        ["concat", ["get", "maki"], "-15"],
                        9,
                        ["concat", ["get", "maki"], "-11"]
                    ],
                    "text-font": ["DIN Pro Bold", "Arial Unicode MS Bold"],
                    "text-offset": [0, 0.75],
                    "text-rotation-alignment": "viewport",
                    "text-anchor": "top",
                    "text-field": [
                        "step",
                        ["get", "sizerank"],
                        ["coalesce", ["get", "name_en"], ["get", "name"]],
                        15,
                        ["get", "ref"]
                    ],
                    "text-letter-spacing": 0.01,
                    "text-max-width": 9
                },
                "paint": {
                    "text-color": "hsl(0, 0%, 100%)",
                    "text-halo-color": "hsl(0, 20%, 3%)",
                    "text-halo-width": 1
                }
            },
            {
                "id": "settlement-subdivision-label",
                "type": "symbol",
                "metadata": {
                    "mapbox:featureComponent": "place-labels",
                    "mapbox:group": "Place labels, place-labels"
                },
                "source": "composite",
                "source-layer": "place_label",
                "minzoom": 10,
                "maxzoom": 15,
                "filter": [
                    "all",
                    [
                        "match",
                        ["get", "class"],
                        "settlement_subdivision",
                        ["match", ["get", "worldview"], ["all", "US"], true, false],
                        "disputed_settlement_subdivision",
                        [
                            "all",
                            ["==", ["get", "disputed"], "true"],
                            [
                                "match",
                                ["get", "worldview"],
                                ["all", "US"],
                                true,
                                false
                            ]
                        ],
                        false
                    ],
                    ["<=", ["get", "filterrank"], 4]
                ],
                "layout": {
                    "text-field": ["coalesce", ["get", "name_en"], ["get", "name"]],
                    "text-transform": "uppercase",
                    "text-font": ["DIN Pro Medium", "Arial Unicode MS Regular"],
                    "text-letter-spacing": [
                        "match",
                        ["get", "type"],
                        "suburb",
                        0.15,
                        0.1
                    ],
                    "text-max-width": 7,
                    "text-padding": 3,
                    "text-size": [
                        "interpolate",
                        ["cubic-bezier", 0.5, 0, 1, 1],
                        ["zoom"],
                        11,
                        ["match", ["get", "type"], "suburb", 11, 10.5],
                        15,
                        ["match", ["get", "type"], "suburb", 15, 14]
                    ]
                },
                "paint": {
                    "text-halo-color": "hsla(0, 5%, 0%, 0.75)",
                    "text-halo-width": 1,
                    "text-color": "hsl(0, 0%, 100%)",
                    "text-halo-blur": 0.5
                }
            },
            {
                "id": "settlement-minor-label",
                "type": "symbol",
                "metadata": {
                    "mapbox:featureComponent": "place-labels",
                    "mapbox:group": "Place labels, place-labels"
                },
                "source": "composite",
                "source-layer": "place_label",
                "maxzoom": 15,
                "filter": [
                    "all",
                    ["<=", ["get", "filterrank"], 3],
                    [
                        "match",
                        ["get", "class"],
                        "settlement",
                        ["match", ["get", "worldview"], ["all", "US"], true, false],
                        "disputed_settlement",
                        [
                            "all",
                            ["==", ["get", "disputed"], "true"],
                            [
                                "match",
                                ["get", "worldview"],
                                ["all", "US"],
                                true,
                                false
                            ]
                        ],
                        false
                    ],
                    [
                        "step",
                        ["zoom"],
                        true,
                        8,
                        [">=", ["get", "symbolrank"], 11],
                        10,
                        [">=", ["get", "symbolrank"], 12],
                        11,
                        [">=", ["get", "symbolrank"], 13],
                        12,
                        [">=", ["get", "symbolrank"], 15],
                        13,
                        [">=", ["get", "symbolrank"], 11],
                        14,
                        [">=", ["get", "symbolrank"], 13]
                    ]
                ],
                "layout": {
                    "text-line-height": 1.1,
                    "text-size": [
                        "interpolate",
                        ["cubic-bezier", 0.2, 0, 0.9, 1],
                        ["zoom"],
                        3,
                        [
                            "step",
                            ["get", "symbolrank"],
                            12,
                            9,
                            11,
                            10,
                            10.5,
                            12,
                            9.5,
                            14,
                            8.5,
                            16,
                            6.5,
                            17,
                            4
                        ],
                        13,
                        [
                            "step",
                            ["get", "symbolrank"],
                            23,
                            9,
                            21,
                            10,
                            19,
                            11,
                            17,
                            12,
                            16,
                            13,
                            15,
                            15,
                            13
                        ]
                    ],
                    "icon-image": [
                        "step",
                        ["zoom"],
                        [
                            "case",
                            ["==", ["get", "capital"], 2],
                            "border-dot-13",
                            [
                                "step",
                                ["get", "symbolrank"],
                                "dot-11",
                                9,
                                "dot-10",
                                11,
                                "dot-9"
                            ]
                        ],
                        8,
                        ""
                    ],
                    "text-font": ["DIN Pro Medium", "Arial Unicode MS Regular"],
                    "text-justify": [
                        "step",
                        ["zoom"],
                        [
                            "match",
                            ["get", "text_anchor"],
                            ["left", "bottom-left", "top-left"],
                            "left",
                            ["right", "bottom-right", "top-right"],
                            "right",
                            "center"
                        ],
                        8,
                        "center"
                    ],
                    "text-offset": [
                        "step",
                        ["zoom"],
                        [
                            "match",
                            ["get", "capital"],
                            2,
                            [
                                "match",
                                ["get", "text_anchor"],
                                "bottom",
                                ["literal", [0, -0.3]],
                                "bottom-left",
                                ["literal", [0.3, -0.1]],
                                "left",
                                ["literal", [0.45, 0.1]],
                                "top-left",
                                ["literal", [0.3, 0.1]],
                                "top",
                                ["literal", [0, 0.3]],
                                "top-right",
                                ["literal", [-0.3, 0.1]],
                                "right",
                                ["literal", [-0.45, 0]],
                                "bottom-right",
                                ["literal", [-0.3, -0.1]],
                                ["literal", [0, -0.3]]
                            ],
                            [
                                "match",
                                ["get", "text_anchor"],
                                "bottom",
                                ["literal", [0, -0.25]],
                                "bottom-left",
                                ["literal", [0.2, -0.05]],
                                "left",
                                ["literal", [0.4, 0.05]],
                                "top-left",
                                ["literal", [0.2, 0.05]],
                                "top",
                                ["literal", [0, 0.25]],
                                "top-right",
                                ["literal", [-0.2, 0.05]],
                                "right",
                                ["literal", [-0.4, 0.05]],
                                "bottom-right",
                                ["literal", [-0.2, -0.05]],
                                ["literal", [0, -0.25]]
                            ]
                        ],
                        8,
                        ["literal", [0, 0]]
                    ],
                    "text-anchor": [
                        "step",
                        ["zoom"],
                        ["get", "text_anchor"],
                        8,
                        "center"
                    ],
                    "text-field": ["coalesce", ["get", "name_en"], ["get", "name"]],
                    "text-max-width": 7
                },
                "paint": {
                    "text-color": "hsl(0, 0%, 95%)",
                    "text-halo-color": "hsl(0, 5%, 0%)",
                    "text-halo-width": 1,
                    "text-halo-blur": 1
                }
            },
            {
                "id": "settlement-major-label",
                "type": "symbol",
                "metadata": {
                    "mapbox:featureComponent": "place-labels",
                    "mapbox:group": "Place labels, place-labels"
                },
                "source": "composite",
                "source-layer": "place_label",
                "maxzoom": 15,
                "filter": [
                    "all",
                    ["<=", ["get", "filterrank"], 3],
                    [
                        "match",
                        ["get", "class"],
                        "settlement",
                        ["match", ["get", "worldview"], ["all", "US"], true, false],
                        "disputed_settlement",
                        [
                            "all",
                            ["==", ["get", "disputed"], "true"],
                            [
                                "match",
                                ["get", "worldview"],
                                ["all", "US"],
                                true,
                                false
                            ]
                        ],
                        false
                    ],
                    [
                        "step",
                        ["zoom"],
                        false,
                        8,
                        ["<", ["get", "symbolrank"], 11],
                        10,
                        ["<", ["get", "symbolrank"], 12],
                        11,
                        ["<", ["get", "symbolrank"], 13],
                        12,
                        ["<", ["get", "symbolrank"], 15],
                        13,
                        [">=", ["get", "symbolrank"], 11],
                        14,
                        [">=", ["get", "symbolrank"], 13]
                    ]
                ],
                "layout": {
                    "text-line-height": 1.1,
                    "text-size": [
                        "interpolate",
                        ["cubic-bezier", 0.2, 0, 0.9, 1],
                        ["zoom"],
                        8,
                        ["step", ["get", "symbolrank"], 18, 9, 17, 10, 15],
                        15,
                        [
                            "step",
                            ["get", "symbolrank"],
                            23,
                            9,
                            22,
                            10,
                            20,
                            11,
                            18,
                            12,
                            16,
                            13,
                            15,
                            15,
                            13
                        ]
                    ],
                    "icon-image": [
                        "step",
                        ["zoom"],
                        [
                            "case",
                            ["==", ["get", "capital"], 2],
                            "border-dot-13",
                            [
                                "step",
                                ["get", "symbolrank"],
                                "dot-11",
                                9,
                                "dot-10",
                                11,
                                "dot-9"
                            ]
                        ],
                        8,
                        ""
                    ],
                    "text-font": ["DIN Pro Medium", "Arial Unicode MS Regular"],
                    "text-justify": [
                        "step",
                        ["zoom"],
                        [
                            "match",
                            ["get", "text_anchor"],
                            ["left", "bottom-left", "top-left"],
                            "left",
                            ["right", "bottom-right", "top-right"],
                            "right",
                            "center"
                        ],
                        8,
                        "center"
                    ],
                    "text-offset": [
                        "step",
                        ["zoom"],
                        [
                            "match",
                            ["get", "capital"],
                            2,
                            [
                                "match",
                                ["get", "text_anchor"],
                                "bottom",
                                ["literal", [0, -0.3]],
                                "bottom-left",
                                ["literal", [0.3, -0.1]],
                                "left",
                                ["literal", [0.45, 0.1]],
                                "top-left",
                                ["literal", [0.3, 0.1]],
                                "top",
                                ["literal", [0, 0.3]],
                                "top-right",
                                ["literal", [-0.3, 0.1]],
                                "right",
                                ["literal", [-0.45, 0]],
                                "bottom-right",
                                ["literal", [-0.3, -0.1]],
                                ["literal", [0, -0.3]]
                            ],
                            [
                                "match",
                                ["get", "text_anchor"],
                                "bottom",
                                ["literal", [0, -0.25]],
                                "bottom-left",
                                ["literal", [0.2, -0.05]],
                                "left",
                                ["literal", [0.4, 0.05]],
                                "top-left",
                                ["literal", [0.2, 0.05]],
                                "top",
                                ["literal", [0, 0.25]],
                                "top-right",
                                ["literal", [-0.2, 0.05]],
                                "right",
                                ["literal", [-0.4, 0.05]],
                                "bottom-right",
                                ["literal", [-0.2, -0.05]],
                                ["literal", [0, -0.25]]
                            ]
                        ],
                        8,
                        ["literal", [0, 0]]
                    ],
                    "text-anchor": [
                        "step",
                        ["zoom"],
                        ["get", "text_anchor"],
                        8,
                        "center"
                    ],
                    "text-field": ["coalesce", ["get", "name_en"], ["get", "name"]],
                    "text-max-width": 7
                },
                "paint": {
                    "text-color": "hsl(0, 0%, 95%)",
                    "text-halo-color": "hsl(0, 5%, 0%)",
                    "text-halo-width": 1,
                    "text-halo-blur": 1
                }
            },
            {
                "id": "state-label",
                "type": "symbol",
                "metadata": {
                    "mapbox:featureComponent": "place-labels",
                    "mapbox:group": "Place labels, place-labels"
                },
                "source": "composite",
                "source-layer": "place_label",
                "minzoom": 3,
                "maxzoom": 9,
                "filter": [
                    "match",
                    ["get", "class"],
                    "state",
                    ["match", ["get", "worldview"], ["all", "US"], true, false],
                    "disputed_state",
                    [
                        "all",
                        ["==", ["get", "disputed"], "true"],
                        ["match", ["get", "worldview"], ["all", "US"], true, false]
                    ],
                    false
                ],
                "layout": {
                    "text-size": [
                        "interpolate",
                        ["cubic-bezier", 0.85, 0.7, 0.65, 1],
                        ["zoom"],
                        4,
                        ["step", ["get", "symbolrank"], 10, 6, 9.5, 7, 9],
                        9,
                        ["step", ["get", "symbolrank"], 21, 6, 16, 7, 13]
                    ],
                    "text-transform": "uppercase",
                    "text-font": ["DIN Pro Bold", "Arial Unicode MS Bold"],
                    "text-field": [
                        "step",
                        ["zoom"],
                        [
                            "step",
                            ["get", "symbolrank"],
                            ["coalesce", ["get", "name_en"], ["get", "name"]],
                            5,
                            [
                                "coalesce",
                                ["get", "abbr"],
                                ["get", "name_en"],
                                ["get", "name"]
                            ]
                        ],
                        5,
                        ["coalesce", ["get", "name_en"], ["get", "name"]]
                    ],
                    "text-letter-spacing": 0.15,
                    "text-max-width": 6
                },
                "paint": {
                    "text-color": "hsl(0, 0%, 95%)",
                    "text-halo-color": "hsl(0, 5%, 0%)",
                    "text-halo-width": 1
                }
            },
            {
                "id": "country-label",
                "type": "symbol",
                "metadata": {
                    "mapbox:featureComponent": "place-labels",
                    "mapbox:group": "Place labels, place-labels"
                },
                "source": "composite",
                "source-layer": "place_label",
                "minzoom": 1,
                "maxzoom": 10,
                "filter": [
                    "match",
                    ["get", "class"],
                    "country",
                    ["match", ["get", "worldview"], ["all", "US"], true, false],
                    "disputed_country",
                    [
                        "all",
                        ["==", ["get", "disputed"], "true"],
                        ["match", ["get", "worldview"], ["all", "US"], true, false]
                    ],
                    false
                ],
                "layout": {
                    "icon-image": "",
                    "text-field": ["coalesce", ["get", "name_en"], ["get", "name"]],
                    "text-line-height": 1.1,
                    "text-max-width": 6,
                    "text-font": ["DIN Pro Medium", "Arial Unicode MS Regular"],
                    "text-offset": ["literal", [0, 0]],
                    "text-justify": [
                        "step",
                        ["zoom"],
                        [
                            "match",
                            ["get", "text_anchor"],
                            ["left", "bottom-left", "top-left"],
                            "left",
                            ["right", "bottom-right", "top-right"],
                            "right",
                            "center"
                        ],
                        7,
                        "center"
                    ],
                    "text-size": [
                        "interpolate",
                        ["cubic-bezier", 0.2, 0, 0.7, 1],
                        ["zoom"],
                        1,
                        ["step", ["get", "symbolrank"], 11, 4, 9, 5, 8],
                        9,
                        ["step", ["get", "symbolrank"], 22, 4, 19, 5, 17]
                    ]
                },
                "paint": {
                    "icon-opacity": [
                        "step",
                        ["zoom"],
                        ["case", ["has", "text_anchor"], 1, 0],
                        7,
                        0
                    ],
                    "text-color": "hsl(0, 0%, 95%)",
                    "text-halo-color": [
                        "interpolate",
                        ["linear"],
                        ["zoom"],
                        2,
                        "hsla(0, 5%, 0%, 0.75)",
                        3,
                        "hsl(0, 5%, 0%)"
                    ],
                    "text-halo-width": 1.25
                }
            }
        ]
    };

    var mapStyleCalTopo = {
        "version": 8,
        "sources": {
            "caltopo": {
                "type": "raster",
                "tiles": [
                    "https://s3-us-west-1.amazonaws.com/caltopo/topo/{z}/{x}/{y}.png"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 22
            }
        },
        "layers": [
            {
                "id": "caltopo",
                "type": "raster",
                "source": "caltopo",
                "minzoom": 0,
                "maxzoom": 22
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleArgentinaIgn50k = {
        "version": 8,
        "sources": {
            "argentina-ign-50k": {
                "type": "raster",
                "tiles": [
                    "https://imagenes.ign.gob.ar/geoserver/cartas_mosaicos/gwc/service/wmts?&service=WMTS&request=GetTile&version=1.0.0&layer=cartas_50k&style=raster&format=image/png&tileMatrixSet=EPSG:3857&tileMatrix=EPSG:3857:{z}&TileRow={y}&TileCol={x}"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 15
            }
        },
        "layers": [
            {
                "id": "argentina-ign-50k",
                "type": "raster",
                "source": "argentina-ign-50k",
                "minzoom": 4,
                "maxzoom": 15
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleArgentinaIgn100k = {
        "version": 8,
        "sources": {
            "argentina-ign-100k": {
                "type": "raster",
                "tiles": [
                    "https://imagenes.ign.gob.ar/geoserver/cartas_mosaicos/gwc/service/wmts?&service=WMTS&request=GetTile&version=1.0.0&layer=cartas_100k&style=raster&format=image/png&tileMatrixSet=EPSG:3857&tileMatrix=EPSG:3857:{z}&TileRow={y}&TileCol={x}"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 15
            }
        },
        "layers": [
            {
                "id": "argentina-ign-100k",
                "type": "raster",
                "source": "argentina-ign-100k",
                "minzoom": 4,
                "maxzoom": 15
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleAustraliaNsw = {
        "version": 8,
        "sources": {
            "australia-nsw": {
                "type": "raster",
                "tiles": [
                    "https://maps.six.nsw.gov.au/arcgis/services/public/NSW_Base_Map/MapServer/WmsServer?&service=WMS&request=GetMap&layers=LPIMap_PlacePoint&styles=&format=image%2Fjpeg&transparent=false&version=1.1.1&width=256&height=256&srs=EPSG%3A3857&bbox={bbox-epsg-3857}"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 18
            }
        },
        "layers": [
            {
                "id": "australia-nsw",
                "type": "raster",
                "source": "australia-nsw",
                "minzoom": 4,
                "maxzoom": 18
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleAustraliaQld = {
        "version": 8,
        "sources": {
            "australia-qld": {
                "type": "raster",
                "tiles": [
                    "https://gisservices2.information.qld.gov.au/arcgis/services/QTopo/QTopoBase_WebM/MapServer/WmsServer?&service=WMS&request=GetMap&layers=Queensland%20Topographic%20Map%20Cache&styles=&format=image%2Fjpeg&transparent=false&version=1.1.1&width=256&height=256&srs=EPSG%3A3857&bbox={bbox-epsg-3857}"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 19
            }
        },
        "layers": [
            {
                "id": "australia-qld",
                "type": "raster",
                "source": "australia-qld",
                "minzoom": 3,
                "maxzoom": 19
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleAustraliaSa = {
        "version": 8,
        "sources": {
            "australia-sa": {
                "type": "raster",
                "tiles": [
                    "https://basemap.geohub.sa.gov.au/server/rest/services/BaseMaps/Topographic_wmas/MapServer/tile/{z}/{y}/{x}"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 19
            }
        },
        "layers": [
            {
                "id": "australia-sa",
                "type": "raster",
                "source": "australia-sa",
                "minzoom": 3,
                "maxzoom": 19
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleAustraliaTs = {
        "version": 8,
        "sources": {
            "australia-ts": {
                "type": "raster",
                "tiles": [
                    "https://services.thelist.tas.gov.au/arcgis/rest/services/Basemaps/TasmapRaster/MapServer/WMTS/tile/1.0.0/Basemaps_TasmapRaster/default/default028mm/{z}/{y}/{x}.png"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 19
            }
        },
        "layers": [
            {
                "id": "australia-ts",
                "type": "raster",
                "source": "australia-ts",
                "minzoom": 3,
                "maxzoom": 19
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleAustraliaVic = {
        "version": 8,
        "sources": {
            "australia-vic": {
                "type": "raster",
                "tiles": [
                    "https://services.land.vic.gov.au/catalogue/publicproxy/guest/dv_geoserver/datavic/ows?SERVICE=WMS&&service=WMS&request=GetMap&layers=FORESTS_FCOV500_87_PRESENT%2CVMELEV_EL_CONTOUR%2CVMELEV_EL_GRND_SURFACE_POINT%2CVMELEV_EL_GRND_TYPE_POINT%2CVMHYDRO_HY_WATERCOURSE%2CVMHYDRO_HY_WATER_AREA_POLYGON&styles=&format=image%2Fjpeg&transparent=false&version=1.1.1&width=256&height=256&srs=EPSG%3A3857&bbox={bbox-epsg-3857}"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 19
            }
        },
        "layers": [
            {
                "id": "australia-vic",
                "type": "raster",
                "source": "australia-vic",
                "minzoom": 6,
                "maxzoom": 19
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleAustriaBergFex = {
        "version": 8,
        "sources": {
            "austria-bergfex": {
                "type": "raster",
                "tiles": [
                    "https://tiles.bergfex.at/styles/bergfex-osm/{z}/{x}/{y}@2x.jpg"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 23
            }
        },
        "layers": [
            {
                "id": "austria-bergfex",
                "type": "raster",
                "source": "austria-bergfex",
                "minzoom": 0,
                "maxzoom": 23
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleAustriaBev = {
        "version": 8,
        "sources": {
            "austria-bev": {
                "type": "raster",
                "tiles": [
                    "https://tiles.bergfex.at/data/oek50-512/{z}/{x}/{y}.jpg70"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 20
            }
        },
        "layers": [
            {
                "id": "austria-bev",
                "type": "raster",
                "source": "austria-bev",
                "minzoom": 5,
                "maxzoom": 20
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

var mapStyleBrazil = {
        "version": 8,
        "sources": {
            "brazil": {
                "type": "raster",
                "tiles": [
                    "https://bdgex.eb.mil.br/mapcache?SERVICE=WMS&VERSION=1.1.1&service=WMS&request=GetMap&layers=ctmmultiescalas_mercator&styles=&format=image%2Fjpeg&transparent=false&version=1.1.1&width=256&height=256&srs=EPSG%3A3857&bbox={bbox-epsg-3857}"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 19
            }
        },
        "layers": [
            {
                "id": "brazil",
                "type": "raster",
                "source": "brazil",
                "minzoom": 7,
                "maxzoom": 19
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleBelgiumNgi = {
        "version": 8,
        "sources": {
            "belgium-ngi": {
                "type": "raster",
                "tiles": [
                    "https://cartoweb.wmts.ngi.be/1.0.0/topo/default/3857/{z}/{y}/{x}.png"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 19
            }
        },
        "layers": [
            {
                "id": "belgium-ngi",
                "type": "raster",
                "source": "belgium-ngi",
                "minzoom": 5,
                "maxzoom": 19
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleCanadaCalTopo = {
        "version": 8,
        "sources": {
            "canada-caltopo": {
                "type": "raster",
                "tiles": [
                    "https://s3-us-west-1.amazonaws.com/caltopo/topo/{z}/{x}/{y}.png"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 23
            }
        },
        "layers": [
            {
                "id": "canada-caltopo",
                "type": "raster",
                "source": "canada-caltopo",
                "minzoom": 0,
                "maxzoom": 23
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleCanadaBc = {
        "version": 8,
        "sources": {
            "canada-bc": {
                "type": "raster",
                "tiles": [
                    "https://maps.gov.bc.ca/arcserver/services/province/web_mercator_cache/MapServer/WMSServer?&service=WMS&request=GetMap&layers=0&styles=&format=image%2Fpng&transparent=false&version=1.3.0&width=256&height=256&crs=EPSG%3A3857&bbox={bbox-epsg-3857}"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 15
            }
        },
        "layers": [
            {
                "id": "canada-bc",
                "type": "raster",
                "source": "canada-bc",
                "minzoom": 0,
                "maxzoom": 15
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleCanadaOn = {
        "version": 8,
        "sources": {
            "canada-on": {
                "type": "raster",
                "tiles": [
                    "https://ws.lioservices.lrc.gov.on.ca/arcgis1061a/rest/services/LIO_Cartographic/LIO_Topographic/MapServer/tile/{z}/{y}/{x}"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 19
            }
        },
        "layers": [
            {
                "id": "canada-on",
                "type": "raster",
                "source": "canada-on",
                "minzoom": 5,
                "maxzoom": 19
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleCanadaQc = {
        "version": 8,
        "sources": {
            "canada-qc": {
                "type": "raster",
                "tiles": [
                    "https://servicesmatriciels.mern.gouv.qc.ca/erdas-iws/ogc/wmts/Cartes_Images?&service=WMTS&request=GetTile&version=1.0.0&layer=BDTQ-20K&style=default&format=image/jpeg&tileMatrixSet=GoogleMapsCompatibleExt2:epsg:3857&tileMatrix={z}&TileRow={y}&TileCol={x}"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 19
            }
        },
        "layers": [
            {
                "id": "canada-qc",
                "type": "raster",
                "source": "canada-qc",
                "minzoom": 5,
                "maxzoom": 19
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleCroatiaDgu = {
        "version": 8,
        "sources": {
            "croatia-dgu": {
                "type": "raster",
                "tiles": [
                    "https://geoportal.dgu.hr/ows?SERVICE=WMS&amp;&service=WMS&request=GetMap&layers=TK25&styles=&format=image%2Fjpeg&transparent=false&version=1.1.1&width=256&height=256&srs=EPSG%3A3857&bbox={bbox-epsg-3857}"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 17
            }
        },
        "layers": [
            {
                "id": "croatia-dgu",
                "type": "raster",
                "source": "croatia-dgu",
                "minzoom": 5,
                "maxzoom": 17
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleCzechiaCuzk = {
        "version": 8,
        "sources": {
            "czechia-cuzk": {
                "type": "raster",
                "tiles": [
                    "https://mapserver.mapy.cz/turist-m/{z}-{x}-{y}"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 18
            }
        },
        "layers": [
            {
                "id": "czechia-cuzk",
                "type": "raster",
                "source": "czechia-cuzk",
                "minzoom": 0,
                "maxzoom": 18
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleFinlandNls = {
        "version": 8,
        "sources": {
            "finland-nls": {
                "type": "raster",
                "tiles": [
                    "https://s3-us-west-1.amazonaws.com/caltopo/topo/{z}/{x}/{y}.png"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 23
            }
        },
        "layers": [
            {
                "id": "finland-nls",
                "type": "raster",
                "source": "finland-nls",
                "minzoom": 0,
                "maxzoom": 23
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleFranceIgn = {
        "version": 8,
        "sources": {
            "france-ign": {
                "type": "raster",
                "tiles": [
                    "https://data.geopf.fr/private/wmts?apikey=ign_scan_ws&layer=GEOGRAPHICALGRIDSYSTEMS.MAPS&style=normal&tilematrixset=PM_0_18&Service=WMTS&Request=GetTile&Version=1.0.0&Format=image/jpeg&TileMatrix={z}&TileCol={x}&TileRow={y}"
                ],
                "tileSize": 256,
                "minzoom": 0,
                "maxzoom": 18
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 256,
                "minzoom": 0,
                "maxzoom": 18
            }
        },
        "layers": [
            {
                "id": "france-ign",
                "type": "raster",
                "source": "france-ign",
                "minzoom": 0,
                "maxzoom": 18
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleGermanyOa = {
        "version": 8,
        "sources": {
            "germany-oa": {
                "type": "raster",
                "tiles": [
                    "https://w3.oastatic.com/map/v1/raster/topo_bkg/{z}/{x}/{y}/.png"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 19
            }
        },
        "layers": [
            {
                "id": "germany-oa",
                "type": "raster",
                "source": "germany-oa",
                "minzoom": 7,
                "maxzoom": 19
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleHongKongLandsD = {
        "version": 8,
        "sources": {
            "hongkong-landsd": {
                "type": "raster",
                "tiles": [
                    "https://mapapi.geodata.gov.hk/gs/api/v1.0.0/xyz/basemap/WGS84/{z}/{x}/{y}.png"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 19
            }
        },
        "layers": [
            {
                "id": "hongkong-landsd",
                "type": "raster",
                "source": "hongkong-landsd",
                "minzoom": 7,
                "maxzoom": 19
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleIcelandCalTopo = {
        "version": 8,
        "sources": {
            "iceland-caltopo": {
                "type": "raster",
                "tiles": [
                    "https://s3-us-west-1.amazonaws.com/caltopo/topo/{z}/{x}/{y}.png"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 23
            }
        },
        "layers": [
            {
                "id": "iceland-caltopo",
                "type": "raster",
                "source": "iceland-caltopo",
                "minzoom": 0,
                "maxzoom": 23
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleIcelandNew = {
        "version": 8,
        "sources": {
            "iceland-new": {
                "type": "raster",
                "tiles": [
                    "https://luk.vedur.is/arcgis/rest/services/grunnkort/grunnkort_cache_wmerc84/MapServer/tile/{z}/{y}/{x}"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 23
            }
        },
        "layers": [
            {
                "id": "iceland-new",
                "type": "raster",
                "source": "iceland-new",
                "minzoom": 0,
                "maxzoom": 23
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleIsraelHikingOsm = {
        "version": 8,
        "sources": {
            "israel-hikingosm": {
                "type": "raster",
                "tiles": [
                    "https://israelhiking.osm.org.il/English/Tiles/{z}/{x}/{y}.png"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 19
            }
        },
        "layers": [
            {
                "id": "israel-hikingosm",
                "type": "raster",
                "source": "israel-hikingosm",
                "minzoom": 5,
                "maxzoom": 19
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleJapanGsi = {
        "version": 8,
        "sources": {
            "japan-gsi": {
                "type": "raster",
                "tiles": [
                    "https://cyberjapandata.gsi.go.jp/xyz/std/{z}/{x}/{y}.png"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 23
            }
        },
        "layers": [
            {
                "id": "japan-gsi",
                "type": "raster",
                "source": "japan-gsi",
                "minzoom": 0,
                "maxzoom": 23
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleLuxembourg = {
        "version": 8,
        "sources": {
            "luxembourg": {
                "type": "raster",
                "tiles": [
                    "https://wmts1.geoportail.lu/opendata/wmts/topo/GLOBAL_WEBMERCATOR_4_V3/{z}/{x}/{y}.png"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 19
            }
        },
        "layers": [
            {
                "id": "luxembourg",
                "type": "raster",
                "source": "luxembourg",
                "minzoom": 7,
                "maxzoom": 19
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleMexicoInegi = {
        "version": 8,
        "sources": {
            "mexico-inegi": {
                "type": "raster",
                "tiles": [
                    "https://s3-us-west-1.amazonaws.com/caltopo/topo/{z}/{x}/{y}.png"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 23
            }
        },
        "layers": [
            {
                "id": "mexico-inegi",
                "type": "raster",
                "source": "mexico-inegi",
                "minzoom": 0,
                "maxzoom": 23
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleNewZealandLinz = {
        "version": 8,
        "sources": {
            "newzealand-linz": {
                "type": "raster",
                "tiles": [
                    "https://s3-us-west-1.amazonaws.com/caltopo/topo/{z}/{x}/{y}.png"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 23
            }
        },
        "layers": [
            {
                "id": "newzealand-linz",
                "type": "raster",
                "source": "newzealand-linz",
                "minzoom": 0,
                "maxzoom": 23
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

var mapStyleNorwayNew = {
        "version": 8,
        "sources": {
            "norway-new": {
                "type": "raster",
                "tiles": [
                    "https://cache.kartverket.no/v1/wmts/1.0.0/topo/default/webmercator/{z}/{y}/{x}.png"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 20
            }
        },
        "layers": [
            {
                "id": "norway-new",
                "type": "raster",
                "source": "norway-new",
                "minzoom": 5,
                "maxzoom": 20
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };


    var mapStyleNorwayKartverket = {
        "version": 8,
        "sources": {
            "norway-kartverket": {
                "type": "raster",
                "tiles": [
                    "https://s3-us-west-1.amazonaws.com/caltopo/topo/{z}/{x}/{y}.png"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 23
            }
        },
        "layers": [
            {
                "id": "norway-kartverket",
                "type": "raster",
                "source": "norway-kartverket",
                "minzoom": 0,
                "maxzoom": 23
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleNorwayJanMayen = {
        "version": 8,
        "sources": {
            "norway-janmayen": {
                "type": "raster",
                "tiles": [
                    "https://geodata.npolar.no/arcgis/services/Basisdata/NP_Basiskart_JanMayen_WMS/MapServer/WmsServer?&service=WMS&request=GetMap&layers=1%2C2%2C3%2C4%2C5%2C6%2C7%2C8%2C9%2C10%2C11%2C12%2C13%2C14%2C15%2C16%2C17%2C18%2C19%2C20%2C21%2C22%2C23%2C24%2C25%2C26&styles=&format=image%2Fjpeg&transparent=false&version=1.1.1&width=256&height=256&srs=EPSG%3A3857&bbox={bbox-epsg-3857}"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 19
            }
        },
        "layers": [
            {
                "id": "norway-janmayen",
                "type": "raster",
                "source": "norway-janmayen",
                "minzoom": 7,
                "maxzoom": 19
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleNorwaySvalbard = {
        "version": 8,
        "sources": {
            "norway-svalbard": {
                "type": "raster",
                "tiles": [
                    "https://geodata.npolar.no/arcgis/services/Basisdata/NP_Basiskart_Svalbard_WMS/MapServer/WmsServer?&service=WMS&request=GetMap&layers=1%2C2%2C3%2C4%2C5%2C6%2C7%2C8%2C9%2C10%2C11%2C12%2C13%2C14%2C15%2C16%2C17%2C18%2C19%2C20%2C21%2C22%2C23%2C24%2C25%2C26%2C27%2C28%2C29%2C30%2C31%2C32%2C33%2C34%2C35%2C36%2C37%2C38%2C39%2C40%2C41%2C42%2C43%2C44%2C45&styles=&format=image%2Fjpeg&transparent=false&version=1.1.1&width=256&height=256&srs=EPSG%3A3857&bbox={bbox-epsg-3857}"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 19
            }
        },
        "layers": [
            {
                "id": "norway-svalbard",
                "type": "raster",
                "source": "norway-svalbard",
                "minzoom": 4,
                "maxzoom": 19
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStylePhilippinesNamria = {
        "version": 8,
        "sources": {
            "philippines-namria": {
                "type": "raster",
                "tiles": [
                    "https://basemapserver.geoportal.gov.ph/tiles/v2/PGP/{z}/{x}/{y}.png"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 19
            }
        },
        "layers": [
            {
                "id": "philippines-namria",
                "type": "raster",
                "source": "philippines-namria",
                "minzoom": 0,
                "maxzoom": 19
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStylePolandGeoportal = {
        "version": 8,
        "sources": {
            "poland-geoportal": {
                "type": "raster",
                "tiles": [
                    "https://mapy.geoportal.gov.pl/wss/service/img/guest/TOPO/MapServer/WMSServer?&service=WMS&request=GetMap&layers=Raster&styles=&format=image%2Fjpeg&transparent=false&version=1.1.1&width=256&height=256&srs=EPSG%3A3857&bbox={bbox-epsg-3857}"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 19
            }
        },
        "layers": [
            {
                "id": "poland-geoportal",
                "type": "raster",
                "source": "poland-geoportal",
                "minzoom": 6,
                "maxzoom": 19
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleSlovakiaDgu = {
        "version": 8,
        "sources": {
            "slovakia-dgu": {
                "type": "raster",
                "tiles": [
                    "https://tile.freemap.sk/T/{z}/{x}/{y}.jpeg"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 15
            }
        },
        "layers": [
            {
                "id": "slovakia-dgu",
                "type": "raster",
                "source": "slovakia-dgu",
                "minzoom": 6,
                "maxzoom": 15
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleSloveniaProstor = {
        "version": 8,
        "sources": {
            "slovenia-prostor": {
                "type": "raster",
                "tiles": [
                    "https://prostor.zgs.gov.si/geoserver/wms?&service=WMS&request=GetMap&layers=zemljevid_group&styles=&format=image%2Fpng&transparent=false&version=1.3.0&width=256&height=256&srs=EPSG%3A3857&bbox={bbox-epsg-3857}"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 19
            }
        },
        "layers": [
            {
                "id": "slovenia-prostor",
                "type": "raster",
                "source": "slovenia-prostor",
                "minzoom": 7,
                "maxzoom": 19
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleSpainIgn = {
        "version": 8,
        "sources": {
            "spain-ign": {
                "type": "raster",
                "tiles": [
                    "https://ign.es/wmts/mapa-raster?service=WMTS&request=GetTile&version=1.0.0&Format=image/jpeg&layer=MTN&style=default&tilematrixset=GoogleMapsCompatible&TileMatrix={z}&TileRow={y}&TileCol={x}"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 19
            }
        },
        "layers": [
            {
                "id": "spain-ign",
                "type": "raster",
                "source": "spain-ign",
                "minzoom": 0,
                "maxzoom": 19
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleSpainCataluna = {
        "version": 8,
        "sources": {
            "spain-cataluna": {
                "type": "raster",
                "tiles": [
                    "https://geoserveis.icgc.cat/icc_mapesmultibase/noutm/wms/service?SERVICE=WMS&VERSION=1.1.1&service=WMS&request=GetMap&layers=topo&styles=&format=image%2Fjpeg&transparent=false&version=1.1.1&width=256&height=256&srs=EPSG%3A3857&bbox={bbox-epsg-3857}"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 19
            }
        },
        "layers": [
            {
                "id": "spain-cataluna",
                "type": "raster",
                "source": "spain-cataluna",
                "minzoom": 7,
                "maxzoom": 19
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleSwedenSgu = {
        "version": 8,
        "sources": {
            "sweden-sgu": {
                "type": "raster",
                "tiles": [
                    "https://s3-us-west-1.amazonaws.com/caltopo/topo/{z}/{x}/{y}.png"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 23
            }
        },
        "layers": [
            {
                "id": "sweden-sgu",
                "type": "raster",
                "source": "sweden-sgu",
                "minzoom": 0,
                "maxzoom": 23
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleSwitzerlandSwisstopo = {
        "version": 8,
        "sources": {
            "switzerland-swisstopo": {
                "type": "raster",
                "tiles": [
                    "https://s3-us-west-1.amazonaws.com/caltopo/topo/{z}/{x}/{y}.png"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 23
            }
        },
        "layers": [
            {
                "id": "switzerland-swisstopo",
                "type": "raster",
                "source": "switzerland-swisstopo",
                "minzoom": 0,
                "maxzoom": 23
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleTaiwanNlsc = {
        "version": 8,
        "sources": {
            "taiwan-nlsc": {
                "type": "raster",
                "tiles": [
                    "https://wmts.nlsc.gov.tw/wmts/EMAP5_OPENDATA/default/EPSG:3857/{z}/{y}/{x}"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 20
            }
        },
        "layers": [
            {
                "id": "taiwan-nlsc",
                "type": "raster",
                "source": "taiwan-nlsc",
                "minzoom": 0,
                "maxzoom": 20
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };



    var mapStyleUkOs = {
        "version": 8,
        "sources": {
            "uk-os": {
                "type": "raster",
                "tiles": [
                    "https://api.os.uk/maps/raster/v1/zxy/Outdoor_3857/{z}/{x}/{y}.png?key=HNxQAA07v4dfvBcWuD0G2ZCdaTn7nclG"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 19
            }
        },
        "layers": [
            {
                "id": "uk-os",
                "type": "raster",
                "source": "uk-os",
                "minzoom": 5,
                "maxzoom": 19
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyleOutdoors = {
        "version": 8,
        "sources": {
            "outdoors": {
                "type": "raster",
                // point to our third-party tiles. Note that some examples
                // show a "url" property. This only applies to tilesets with
                // corresponding TileJSON (such as mapbox tiles).
                "tiles": [
                    "https://a.tile.opentopomap.org/{z}/{x}/{y}.png",
                    "https://b.tile.opentopomap.org/{z}/{x}/{y}.png",
                    "https://c.tile.opentopomap.org/{z}/{x}/{y}.png",
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 14
            }
        },
        "layers": [
            {
                "id": "outdoors",
                "type": "raster",
                "source": "outdoors",
                "minzoom": 0,
                "maxzoom": 22
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    //Natural atlas
    var mapStyleNaturalAtlas = {
        "version": 8,
        "sources": {
            "natural-atlas": {
                "type": "raster",
                // point to our third-party tiles. Note that some examples
                // show a "url" property. This only applies to tilesets with
                // corresponding TileJSON (such as mapbox tiles).
                "tiles": [
                    "https://a-naturalatlas-tiles.global.ssl.fastly.net/topo/{z}/{x}/{y}/<EMAIL>"
                ],
                "tileSize": 256
            },
            "mapbox-dem": {
                "type": "raster-dem",
                "url": "mapbox://mapbox.mapbox-terrain-dem-v1",
                "tileSize": 512,
                "maxzoom": 14
            }
        },
        "layers": [
            {
                "id": "natural-atlas",
                "type": "raster",
                "source": "natural-atlas",
                "minzoom": 0,
                "maxzoom": 15
            },
            {
                "id": "sky",
                "type": "sky",
                "paint": {
                    "sky-type": "atmosphere",
                    "sky-atmosphere-sun": [0.0, 0.0],
                    "sky-atmosphere-sun-intensity": 15
                }
            }
        ]
    };

    var mapStyle = mapStyleTerrain;

    function toggleMapUnits(units) {
        var mapButton = $('#gm-custom-mapbutton').html();

        if (units == 'meters') {
            $('#peak-elevation-m').click();
            $('#peak-elevation').attr('placeholder', 'in meters...');
            $('#elevation-units').html('meters');
            $('#peak-prominence-m').click();
            $('#peak-prominence').attr('placeholder', 'in meters...');
            $('#prominence-units').html('meters');
        } else {
            $('#peak-elevation-ft').click();
            $('#peak-elevation').attr('placeholder', 'in feet...');
            $('#elevation-units').html('feet');
            $('#peak-prominence-ft').click();
            $('#peak-prominence').attr('placeholder', 'in feet...');
            $('#prominence-units').html('feet');
        }

        if (units != '' && units != null && mapButton != null) {
            //only toggle contours on Terrain and Satellite Topo styles
            //if ($('#gm-custom-mapbutton-label').html() == 'Terrain') {
            if (mapButton.includes('Terrain')) {
                if (units == 'feet') {
                    createCookie('map_units', 'feet', 365);
                    map.removeLayer("contour-label");
                    map.addLayer({
                        "id": "contour-label",
                        "type": "symbol",
                        "source": "composite",
                        "source-layer": "contour",
                        "minzoom": 11,
                        "filter": [
                            "in",
                            "index",
                            10,
                            5
                        ],
                        "layout": {
                            "text-field": ["concat", ["to-string", ["ceil", ["*", ["get", "ele"], 3.28]]], " ft"],
                            "symbol-placement": "line",
                            "text-pitch-alignment": "viewport",
                            "text-max-angle": 25,
                            "text-padding": 5,
                            "text-font": [
                                "DIN Offc Pro Medium",
                                "Arial Unicode MS Regular"
                            ],
                            "text-size": {
                                "base": 1,
                                "stops": [
                                    [
                                        15,
                                        9.5
                                    ],
                                    [
                                        20,
                                        12
                                    ]
                                ]
                            }
                        },
                        "paint": {
                            "text-color": "hsl(100, 60%, 28%)",
                            "text-halo-width": 1,
                            "text-halo-blur": 0,
                            "text-halo-color": "hsla(0, 0%, 100%, 0.5)"
                        }
                    });
                    $('#gm-custom-mapunitsbutton-label-meters').removeClass('gm-custom-mapunits-selected');
                    $('#gm-custom-mapunitsbutton-label-meters').addClass('gm-custom-mapunits-unselected');
                    $('#gm-custom-mapunitsbutton-label-feet').addClass('gm-custom-mapunits-selected');
                    $('#gm-custom-mapunitsbutton-label-feet').removeClass('gm-custom-mapunits-unselected');
                } else {
                    createCookie('map_units', 'meters', 365);
                    map.removeLayer("contour-label");
                    map.addLayer({
                        "id": "contour-label",
                        "type": "symbol",
                        "source": "composite",
                        "source-layer": "contour",
                        "minzoom": 11,
                        "filter": [
                            "in",
                            "index",
                            10,
                            5
                        ],
                        "layout": {
                            "text-field": ["concat", ["to-string", ["*", ["get", "ele"], 1]], " m"],
                            "symbol-placement": "line",
                            "text-pitch-alignment": "viewport",
                            "text-max-angle": 25,
                            "text-padding": 5,
                            "text-font": [
                                "DIN Offc Pro Medium",
                                "Arial Unicode MS Regular"
                            ],
                            "text-size": {
                                "base": 1,
                                "stops": [
                                    [
                                        15,
                                        9.5
                                    ],
                                    [
                                        20,
                                        12
                                    ]
                                ]
                            }
                        },
                        "paint": {
                            "text-color": "hsl(100, 60%, 28%)",
                            "text-halo-width": 1,
                            "text-halo-blur": 0,
                            "text-halo-color": "hsla(0, 0%, 100%, 0.5)"
                        }
                    });
                    $('#gm-custom-mapunitsbutton-label-feet').removeClass('gm-custom-mapunits-selected');
                    $('#gm-custom-mapunitsbutton-label-feet').addClass('gm-custom-mapunits-unselected');
                    $('#gm-custom-mapunitsbutton-label-meters').addClass('gm-custom-mapunits-selected');
                    $('#gm-custom-mapunitsbutton-label-meters').removeClass('gm-custom-mapunits-unselected');
                }
            //} else if ($('#gm-custom-mapbutton-label').html() == 'Satellite Topo') {
            } else if (mapButton.includes('Satellite Topo')) {
                if (units == 'feet') {
                    createCookie('map_units', 'feet', 365);
                    map.removeLayer("contour-label");
                    map.addLayer({
                        "id": "contour-label",
                        "type": "symbol",
                        "metadata": {"mapbox:group": "********************************"},
                        "source": "composite",
                        "source-layer": "contour",
                        "minzoom": 11,
                        "filter": [
                            "in",
                            "index",
                            10,
                            5
                        ],
                        "layout": {
                            "text-field": [
                                "concat",
                                ["to-string", ["round", ["*", ["get", "ele"], 3.28084]]],
                                " ft"
                            ],
                            "symbol-placement": "line",
                            "text-pitch-alignment": "viewport",
                            "text-max-angle": 25,
                            "text-padding": 5,
                            "text-font": ["DIN Offc Pro Bold", "Arial Unicode MS Regular"],
                            "text-size": [
                                "interpolate",
                                ["linear"],
                                ["zoom"],
                                12,
                                8,
                                13,
                                10,
                                18,
                                12
                            ]
                        },
                        "paint": {
                            "text-color": "#D7FDC4",
                            "text-halo-width": 1.25,
                            "text-halo-color": "hsla(0, 0%, 18%, 0.8)"
                        }
                    });
                    $('#gm-custom-mapunitsbutton-label-meters').removeClass('gm-custom-mapunits-selected');
                    $('#gm-custom-mapunitsbutton-label-meters').addClass('gm-custom-mapunits-unselected');
                    $('#gm-custom-mapunitsbutton-label-feet').addClass('gm-custom-mapunits-selected');
                    $('#gm-custom-mapunitsbutton-label-feet').removeClass('gm-custom-mapunits-unselected');
                } else {
                    createCookie('map_units', 'meters', 365);
                    map.removeLayer("contour-label");
                    map.addLayer({
                        "id": "contour-label",
                        "type": "symbol",
                        "metadata": {"mapbox:group": "********************************"},
                        "source": "composite",
                        "source-layer": "contour",
                        "minzoom": 11,
                        "filter": [
                            "in",
                            "index",
                            10,
                            5
                        ],
                        "layout": {
                            "text-field": [
                                "concat",
                                ["to-string", ["round", ["get", "ele"]]],
                                " m"
                            ],
                            "symbol-placement": "line",
                            "text-pitch-alignment": "viewport",
                            "text-max-angle": 25,
                            "text-padding": 5,
                            "text-font": ["DIN Offc Pro Bold", "Arial Unicode MS Regular"],
                            "text-size": [
                                "interpolate",
                                ["linear"],
                                ["zoom"],
                                12,
                                8,
                                13,
                                10,
                                18,
                                12
                            ]
                        },
                        "paint": {
                            "text-color": "#D7FDC4",
                            "text-halo-width": 1.25,
                            "text-halo-color": "hsla(0, 0%, 18%, 0.8)"
                        }
                    });
                    $('#gm-custom-mapunitsbutton-label-feet').removeClass('gm-custom-mapunits-selected');
                    $('#gm-custom-mapunitsbutton-label-feet').addClass('gm-custom-mapunits-unselected');
                    $('#gm-custom-mapunitsbutton-label-meters').addClass('gm-custom-mapunits-selected');
                    $('#gm-custom-mapunitsbutton-label-meters').removeClass('gm-custom-mapunits-unselected');
                }
            } else {
                if (units == 'feet') {
                    createCookie('map_units', 'feet', 365);
                    $('#gm-custom-mapunitsbutton-label-meters').removeClass('gm-custom-mapunits-selected');
                    $('#gm-custom-mapunitsbutton-label-meters').addClass('gm-custom-mapunits-unselected');
                    $('#gm-custom-mapunitsbutton-label-feet').addClass('gm-custom-mapunits-selected');
                    $('#gm-custom-mapunitsbutton-label-feet').removeClass('gm-custom-mapunits-unselected');
                } else {
                    createCookie('map_units', 'meters', 365);
                    $('#gm-custom-mapunitsbutton-label-feet').removeClass('gm-custom-mapunits-selected');
                    $('#gm-custom-mapunitsbutton-label-feet').addClass('gm-custom-mapunits-unselected');
                    $('#gm-custom-mapunitsbutton-label-meters').addClass('gm-custom-mapunits-selected');
                    $('#gm-custom-mapunitsbutton-label-meters').removeClass('gm-custom-mapunits-unselected');
                }
            }
        }
    }

    function toggleMapType(type) {
        var mapUnits = readCookie('map_units');
        var mapCountry = '';
        if (type=='satellite') {
            createCookie('map_type','satellite',365);
            mapStyleName = 'satellite';
            mapMinZoom = 0;
            mapMaxZoom = 22;
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite</div>');
            map.setStyle(mapStyleSatellite);
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='caltopo') {
            createCookie('map_type','caltopo',365);
            mapStyleName = 'caltopo';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleCalTopo);
            mapMinZoom = 0;
            mapMaxZoom = 19;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        /* Country specific map types */
        } else if (type=='argentina-ign-50k') {
            createCookie('map_type','argentina-ign-50k',365);
            mapStyleName = 'argentina-ign-50k';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleArgentinaIgn50k);
            mapMinZoom = 4;
            mapMaxZoom = 14;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='argentina-ign-100k') {
            createCookie('map_type','argentina-ign-100k',365);
            mapStyleName = 'argentina-ign-100k';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleArgentinaIgn100k);
            mapMinZoom = 4;
            mapMaxZoom = 14;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='australia-nsw') {
            createCookie('map_type','australia-nsw',365);
            mapStyleName = 'australia-nsw';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleAustraliaNsw);
            mapMinZoom = 4;
            mapMaxZoom = 17;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='australia-qld') {
            createCookie('map_type','australia-qld',365);
            mapStyleName = 'australia-qld';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleAustraliaQld);
            mapMinZoom = 3;
            mapMaxZoom = 18;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='australia-sa') {
            createCookie('map_type','australia-sa',365);
            mapStyleName = 'australia-sa';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleAustraliaSa);
            mapMinZoom = 3;
            mapMaxZoom = 18;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='australia-ts') {
            createCookie('map_type','australia-ts',365);
            mapStyleName = 'australia-ts';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleAustraliaTs);
            mapMinZoom = 3;
            mapMaxZoom = 18;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='australia-vic') {
            createCookie('map_type','australia-vic',365);
            mapStyleName = 'australia-vic';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleAustraliaVic);
            mapMinZoom = 6;
            mapMaxZoom = 18;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='austria-bergfex') {
            createCookie('map_type','austria-bergfex',365);
            mapStyleName = 'austria-bergfex';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleAustriaBergFex);
            mapMinZoom = 0;
            mapMaxZoom = 22;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='austria-bev') {
            createCookie('map_type','austria-bev',365);
            mapStyleName = 'austria-bev';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleAustriaBev);
            mapMinZoom = 5;
            mapMaxZoom = 19;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

 } else if (type=='brazil') {
            createCookie('map_type','brazil',365);
            mapStyleName = 'brazil';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleBrazil);
            mapMinZoom = 7;
            mapMaxZoom = 18;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }



        } else if (type=='belgium-ngi') {
            createCookie('map_type','belgium-ngi',365);
            mapStyleName = 'belgium-ngi';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleBelgiumNgi);
            mapMinZoom = 5;
            mapMaxZoom = 18;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='canada-caltopo') {
            createCookie('map_type','canada-caltopo',365);
            mapStyleName = 'canada-caltopo';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleCanadaCalTopo);
            mapMinZoom = 0;
            mapMaxZoom = 22;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='canada-bc') {
            createCookie('map_type','canada-bc',365);
            mapStyleName = 'canada-bc';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleCanadaBc);
            mapMinZoom = 0;
            mapMaxZoom = 19;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='canada-on') {
            createCookie('map_type','canada-on',365);
            mapStyleName = 'canada-on';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleCanadaOn);
            mapMinZoom = 5;
            mapMaxZoom = 18;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='canada-qc') {
            createCookie('map_type','canada-qc',365);
            mapStyleName = 'canada-qc';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleCanadaQc);
            mapMinZoom = 5;
            mapMaxZoom = 18;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='croatia-dgu') {
            createCookie('map_type','croatia-dgu',365);
            mapStyleName = 'croatia-dgu';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleCroatiaDgu);
            mapMinZoom = 5;
            mapMaxZoom = 16;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='czechia-cuzk') {
            createCookie('map_type','czechia-cuzk',365);
            mapStyleName = 'czechia-cuzk';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleCzechiaCuzk);
            mapMinZoom = 0;
            mapMaxZoom = 17;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='finland-nls') {
            createCookie('map_type','finland-nls',365);
            mapStyleName = 'finland-nls';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleFinlandNls);
            mapMinZoom = 0;
            mapMaxZoom = 22;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='france-ign') {
            createCookie('map_type','france-ign',365);
            mapStyleName = 'france-ign';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleFranceIgn);
            mapMinZoom = 0;
            mapMaxZoom = 18;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);


            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='germany-oa') {
            createCookie('map_type','germany-oa',365);
            mapStyleName = 'germany-oa';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleGermanyOa);
            mapMinZoom = 7;
            mapMaxZoom = 18;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='hongkong-landsd') {
            createCookie('map_type','hongkong-landsd',365);
            mapStyleName = 'hongkong-landsd';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleHongKongLandsD);
            mapMinZoom = 7;
            mapMaxZoom = 18;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='iceland-caltopo') {
            createCookie('map_type','iceland-caltopo',365);
            mapStyleName = 'iceland-caltopo';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleIcelandCalTopo);
            mapMinZoom = 0;
            mapMaxZoom = 22;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

            } else if (type=='iceland-new') {
            createCookie('map_type','iceland-new',365);
            mapStyleName = 'iceland-new';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleIcelandNew);
            mapMinZoom = 0;
            mapMaxZoom = 22;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='israel-hikingosm') {
            createCookie('map_type','israel-hikingosm',365);
            mapStyleName = 'israel-hikingosm';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleIsraelHikingOsm);
            mapMinZoom = 5;
            mapMaxZoom = 18;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='japan-gsi') {
            createCookie('map_type','japan-gsi',365);
            mapStyleName = 'japan-gsi';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleJapanGsi);
            mapMinZoom = 0;
            mapMaxZoom = 22;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='luxembourg') {
            createCookie('map_type','luxembourg',365);
            mapStyleName = 'luxembourg';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleLuxembourg);
            mapMinZoom = 7;
            mapMaxZoom = 18;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='mexico-inegi') {
            createCookie('map_type','mexico-inegi',365);
            mapStyleName = 'mexico-inegi';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleMexicoInegi);
            mapMinZoom = 0;
            mapMaxZoom = 22;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='newzealand-linz') {
            createCookie('map_type','newzealand-linz',365);
            mapStyleName = 'newzealand-linz';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleNewZealandLinz);
            mapMinZoom = 0;
            mapMaxZoom = 22;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

 } else if (type=='norway-new') {
            createCookie('map_type','norway-new',365);
            mapStyleName = 'norway-new';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleNorwayNew);
            mapMinZoom = 5;
            mapMaxZoom = 19;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }


        } else if (type=='norway-kartverket') {
            createCookie('map_type','norway-kartverket',365);
            mapStyleName = 'norway-kartverket';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleNorwayKartverket);
            mapMinZoom = 0;
            mapMaxZoom = 22;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='norway-janmayen') {
            createCookie('map_type','norway-janmayen',365);
            mapStyleName = 'norway-janmayen';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleNorwayJanMayen);
            mapMinZoom = 7;
            mapMaxZoom = 18;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='norway-svalbard') {
            createCookie('map_type','norway-svalbard',365);
            mapStyleName = 'norway-svalbard';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleNorwaySvalbard);
            mapMinZoom = 4;
            mapMaxZoom = 18;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='philippines-namria') {
            createCookie('map_type','philippines-namria',365);
            mapStyleName = 'philippines-namria';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStylePhilippinesNamria);
            mapMinZoom = 0;
            mapMaxZoom = 18;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='poland-geoportal') {
            createCookie('map_type','poland-geoportal',365);
            mapStyleName = 'poland-geoportal';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStylePolandGeoportal);
            mapMinZoom = 6;
            mapMaxZoom = 18;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='slovakia-dgu') {
            createCookie('map_type','slovakia-dgu',365);
            mapStyleName = 'slovakia-dgu';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleSlovakiaDgu);
            mapMinZoom = 6;
            mapMaxZoom = 14;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='slovenia-prostor') {
            createCookie('map_type','slovenia-prostor',365);
            mapStyleName = 'slovenia-prostor';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleSloveniaProstor);
            mapMinZoom = 7;
            mapMaxZoom = 18;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='spain-ign') {
            createCookie('map_type','spain-ign',365);
            mapStyleName = 'spain-ign';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleSpainIgn);
            mapMinZoom = 0;
            mapMaxZoom = 18;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='spain-cataluna') {
            createCookie('map_type','spain-cataluna',365);
            mapStyleName = 'spain-cataluna';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleSpainCataluna);
            mapMinZoom = 7;
            mapMaxZoom = 18;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='sweden-sgu') {
            createCookie('map_type','sweden-sgu',365);
            mapStyleName = 'sweden-sgu';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleSwedenSgu);
            mapMinZoom = 0;
            mapMaxZoom = 22;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='switzerland-swisstopo') {
            createCookie('map_type','switzerland-swisstopo',365);
            mapStyleName = 'switzerland-swisstopo';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleSwitzerlandSwisstopo);
            mapMinZoom = 0;
            mapMaxZoom = 22;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='taiwan-nlsc') {
            createCookie('map_type','taiwan-nlsc',365);
            mapStyleName = 'taiwan-nlsc';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleTaiwanNlsc);
            mapMinZoom = 0;
            mapMaxZoom = 19;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

        } else if (type=='southafrica-ngi-50k') {
            createCookie('map_type','southafrica-ngi-50k',365);
            mapStyleName = 'southafrica-ngi-50k';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');

            // Create a custom style with a raster source using Django proxy
            // The proxy handles TMS coordinate conversion and CORS
            var customStyle = {
                "version": 8,
                "sources": {
                    "southafrica-ngi-50k": {
                        "type": "raster",
                        "tiles": [
                            "/mapbox/proxy/southafrica-ngi/{z}/{x}/{y}.png"
                        ],
                        "tileSize": 256
                    }
                },
                "layers": [
                    {
                        "id": "southafrica-ngi-50k",
                        "type": "raster",
                        "source": "southafrica-ngi-50k"
                    }
                ]
            };

            map.setStyle(customStyle);
            mapMinZoom = 6;
            mapMaxZoom = 16;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='andorra-ideandorra') {
            createCookie('map_type','andorra-ideandorra',365);
            mapStyleName = 'andorra-ideandorra';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');

            // Create a custom style with a raster source using Django proxy for WMS
            // The proxy handles EPSG:4326 to EPSG:3857 conversion and CORS
            var customStyle = {
                "version": 8,
                "sources": {
                    "andorra-ideandorra": {
                        "type": "raster",
                        "tiles": [
                            "/mapbox/proxy/andorra-ideandorra/{z}/{x}/{y}.jpg"
                        ],
                        "tileSize": 256
                    }
                },
                "layers": [
                    {
                        "id": "andorra-ideandorra",
                        "type": "raster",
                        "source": "andorra-ideandorra"
                    }
                ]
            };

            map.setStyle(customStyle);

            // Set map bounds to Andorra region
            map.on('styledata', function() {
                // Center on Andorra (coordinates from the example)
                map.setCenter([1.443665, 42.591796]);
                map.setZoom(12);
            });

            mapMinZoom = 8;
            mapMaxZoom = 18;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        }  else if (type=='uk-os') {
            createCookie('map_type','uk-os',365);
            mapStyleName = 'uk-os';
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            map.setStyle(mapStyleUkOs);
            mapMinZoom = 5;
            mapMaxZoom = 18;
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='sat_topo') {
            createCookie('map_type','sat_topo',365);
            mapStyleName = 'sat_topo';
            mapMinZoom = 0;
            mapMaxZoom = 22;
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite Topo</div>');
            if (mapUnits == 'meters') {
                map.setStyle(mapStyleSatTopoMeters);
            } else {
                map.setStyle(mapStyleSatTopo);
            }
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='terrain') {
            createCookie('map_type','terrain',365);
            mapStyleName = 'terrain';
            mapMinZoom = 0;
            mapMaxZoom = 22;
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Terrain</div>');
            if (mapUnits == 'meters') {
                map.setStyle(mapStyleTerrainMeters);
            } else {
                map.setStyle(mapStyleTerrain);
            }
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='outdoors') {
            createCookie('map_type','outdoors',365);
            mapStyleName = 'outdoors';
            mapMinZoom = 0;
            mapMaxZoom = 21;
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">OpenTopoMap</div>');
            map.setStyle(mapStyleOutdoors);
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='streets') {
            createCookie('map_type','streets',365);
            mapStyleName = 'streets';
            mapMinZoom = 0;
            mapMaxZoom = 22;
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Streets</div>');
            map.setStyle(mapStyleStreets);
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='natural_atlas') {
            createCookie('map_type', 'natural_atlas', 365);
            mapStyleName = 'natural_atlas';
            mapMinZoom = 0;
            mapMaxZoom = 14;
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Natural Atlas <span style="font-size: 10px;">(US)</span></div>');
            map.setStyle(mapStyleNaturalAtlas);
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

            try {
                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                //map.setPitch(0);
                map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);
            }
            catch(err) {
                console.log(err.message);
            }

        } else if (type=='satellite_3d') {
            createCookie('map_type','satellite_3d',365);
            mapStyleName = 'satellite_3d';
            mapMinZoom = 0;
            mapMaxZoom = 22;
            $('#gm-custom-mapbutton-label').html('3D Map');
            map.setStyle(mapStyleSatellite3D);
            addExtraMapLayers(type);
            setMapboxMinZoom(mapMinZoom);
            setMapboxMaxZoom(mapMaxZoom);

        }
        addMapboxAttribution(type);
    }

    function initMapType(type) {

        var mapUnits = readCookie('map_units');

        //first set map style URL or JSON

        if (type=='satellite') {
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite</div>');
            mapStyle = mapStyleSatellite;
            mapStyleName = 'satellite';
            mapMinZoom = 0;
            mapMaxZoom = 22;
        } else if (type=='caltopo') {
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            mapStyle = mapStyleCalTopo;
            mapStyleName = 'caltopo';
            mapMinZoom = 0;
            mapMaxZoom = 19;
        } else if (type=='sat_topo') {
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite Topo</div>');
            mapStyleName = 'sat_topo';
            mapMinZoom = 0;
            mapMaxZoom = 22;
        } else if (type=='terrain') {
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Terrain</div>');
            mapStyleName = 'terrain';
            mapMinZoom = 0;
            mapMaxZoom = 22;
        } else if (type=='outdoors') {
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">OpenTopoMap</div>');
            mapStyle = mapStyleOutdoors;
            mapStyleName = 'outdoors';
            mapMinZoom = 0;
            mapMaxZoom = 21;
        } else if (type=='streets') {
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Streets</div>');
            mapStyle = mapStyleStreets;
            mapStyleName = 'streets';
            mapMinZoom = 0;
            mapMaxZoom = 22;
        } else if (type=='natural_atlas') {
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Natural Atlas <span style="font-size: 10px;">(US)</span></div>');
            mapStyle = mapStyleNaturalAtlas;
            mapStyleName = 'natural_atlas';
            mapMinZoom = 0;
            mapMaxZoom = 14;
        }

        var check = checkIfMapboxIsLoaded();
        if (!check) {
            // It's not safe to manipulate layers yet, so wait 200ms and then check again
            setTimeout(function() {
                initMapType(type);
            }, 200);
            return;
        }

        //now set style as needed
        if (type=='satellite') {
            //pass
        } else if (type=='caltopo') {
            //pass
        } else if (type=='sat_topo') {
            if (mapUnits == 'meters') {
                map.setStyle(mapStyleSatTopoMeters);
            } else {
                map.setStyle(mapStyleSatTopo);
            }
        } else if (type=='terrain') {
            if (mapUnits == 'meters') {
                map.setStyle(mapStyleTerrainMeters);
            } else {
                map.setStyle(mapStyleTerrain);
            }
        } else if (type=='outdoors') {
            //pass
        } else if (type=='streets') {
            //pass
        } else if (type=='natural_atlas') {
            //pass
        }

        addExtraMapLayers();
        setMapControls();
        setMapboxMinZoom(mapMinZoom);
        setMapboxMaxZoom(mapMaxZoom);

        addMapboxAttribution(type);
    }

    function initMapTypeMainMap(type) {

        var mapUnits = readCookie('map_units');

        //first set map style URL or JSON
        if (type=='satellite') {
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite</div>');
            mapStyle = mapStyleSatellite;
            mapStyleName = 'satellite';
            mapMinZoom = 0;
            mapMaxZoom = 22;
        } else if (type=='caltopo') {
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
            mapStyle = mapStyleCalTopo;
            mapStyleName = 'caltopo';
            mapMinZoom = 0;
            mapMaxZoom = 19;
        } else if (type=='sat_topo') {
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite Topo</div>');
            mapStyleName = 'sat_topo';
            mapMinZoom = 0;
            mapMaxZoom = 22;
        } else if (type=='terrain') {
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Terrain</div>');
            mapStyleName = 'terrain';
            mapMinZoom = 0;
            mapMaxZoom = 22;
        } else if (type=='outdoors') {
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">OpenTopoMap</div>');
            mapStyle = mapStyleOutdoors;
            mapStyleName = 'outdoors';
            mapMinZoom = 0;
            mapMaxZoom = 21;
        } else if (type=='streets') {
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Streets</div>');
            mapStyle = mapStyleStreets;
            mapStyleName = 'streets';
            mapMinZoom = 0;
            mapMaxZoom = 22;
        } else if (type=='natural_atlas') {
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Natural Atlas <span style="font-size: 10px;">(US)</span></div>');
            mapStyle = mapStyleNaturalAtlas;
            mapStyleName = 'natural_atlas';
            mapMinZoom = 0;
            mapMaxZoom = 14;
        }

        var check = checkIfMapboxIsLoaded();
        if (!check) {
            // It's not safe to manipulate layers yet, so wait 200ms and then check again
            setTimeout(function() {
                initMapTypeMainMap(type);
            }, 200);
            return;
        }

        //now set style as needed
        if (type=='satellite') {
            //pass
        } else if (type=='caltopo') {
            //pass
        } else if (type=='sat_topo') {
            if (mapUnits == 'meters') {
                map.setStyle(mapStyleSatTopoMeters);
            } else {
                map.setStyle(mapStyleSatTopo);
            }
        } else if (type=='terrain') {
            if (mapUnits == 'meters') {
                map.setStyle(mapStyleTerrainMeters);
            } else {
                map.setStyle(mapStyleTerrain);
            }
        } else if (type=='outdoors') {
            //pass
        } else if (type=='streets') {
            //pass
        } else if (type=='natural_atlas') {
            //pass
        }

        addExtraMapLayers();
        setMapControls();
        setMapboxMinZoom(mapMinZoom);
        setMapboxMaxZoom(mapMaxZoom);
        addMapboxAttribution(type);
    }

    // Check if the Mapbox-GL style is loaded.
    function checkIfMapboxStyleIsLoaded() {
        try {
            if (map.isStyleLoaded()) {
                return true; // When it is safe to manipulate layers
            } else {
                return false; // When it is not safe to manipulate layers
            }
        }
        catch(err) {
            return false;
        }
    }

    // Check if the Mapbox-GL map is loaded.
    function checkIfMapboxIsLoaded() {
        try {
            if (map) {
                return true; // When it is safe to manipulate layers
            } else {
                return false; // When it is not safe to manipulate layers
            }
        }
        catch(err) {
            return false;
        }
    }

    function addMapboxAttribution(type) {
        var check = checkIfMapboxStyleIsLoaded();
        if (!check) {
            // It's not safe to manipulate layers yet, so wait 200ms and then check again
            setTimeout(function() {
                addMapboxAttribution(type);
            }, 200);
            return;
        }
        // Whew, now it's safe to manipulate layers!
        if (type=='natural_atlas') {
            //remove opentopomap image
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://naturalatlas.com/" target="_blank">© Natural Atlas, Roads</a> <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="natatl-ctrl-logo" target="_blank" href="https://naturalatlas.com/" aria-label="Natural Atlas logo" rel="noopener"><img src="{% static '' %}img/na-map-logo.svg"></a>').show();
        } else if (type=='outdoors') {
            //remove natural atlas image
            $('.natatl-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('map data: © <a target="_blank" rel="noopener" href="https://openstreetmap.org/copyright">OpenStreetMap</a> contributors, <a target="_blank" rel="noopener" href="http://viewfinderpanoramas.org">SRTM</a> | map style: © <a target="_blank" rel="noopener" href="https://opentopomap.org">OpenTopoMap</a> (<a target="_blank" rel="noopener" href="https://creativecommons.org/licenses/by-sa/3.0/">CC-BY-SA</a>)').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="opentopomap-ctrl-logo" target="_blank" href="https://opentopomap.org/" aria-label="OpenTopoMap logo" rel="noopener"><img src="{% static '' %}img/OpenTopoMap-logo.png" style="width: 100px;"></a>').show();
        } else if (type=='caltopo' || type=='canada-caltopo' || type=='finland-nls' || type=='mexico-inegi' || type== 'newzealand-linz' || type=='norway-kartverket' || type=='sweden-sgu' || type=='switzerland-swisstopo') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://caltopo.com/" aria-label="CalTopo logo" rel="noopener"><img src="https://s3-us-west-1.amazonaws.com/peakery-media/img/caltopo-logo.png" style="width: 100px;"></a>').show();
        } else if (type=='australia-sa') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://location.sa.gov.au/index.html" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/South-Australia-Location.png" style="width: 100px;"></a>').show();
        } else if (type=='austria-bergfex') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://www.bergfex.com/oesterreich/" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/bergfex-logo.png" style="width: 100px;"></a>').show();
        } else if (type=='brazil') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://www.ibge.gov.br/en/" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/brazil-ibge.png" style="width: 100px;"></a>').show();
        } else if (type=='belgium-ngi') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://www.ngi.be/website/" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/belgium-ngi.png" style="width: 100px;"></a>').show();
        } else if (type=='czechia-cuzk') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://www.cuzk.cz/English/Home.aspx" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/cuzk-logo.png" style="width: 100px;"></a>').show();
        } else if (type=='france-ign') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://www.ign.fr" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/france-ign-logo.png" style="width: 100px;"></a>').show();
        } else if (type=='germany-oa') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://www.outdooractive.com/en/" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/outdooractive-logo.png" style="width: 100px;"></a>').show();
        } else if (type=='hongkong-landsd') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://www.landsd.gov.hk/en/" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/hong-kong-landsd-logo.png" style="width: 100px;"></a>').show();
        } else if (type=='iceland-new') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://www.lmi.is" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/iceland-landmaelingar-islands-logo.png" style="width: 100px;"></a>').show();
        } else if (type=='israel-hikingosm') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://israelhiking.osm.org.il" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/israel-hiking.png" style="width: 100px;"></a>').show();
        } else if (type=='japan-gsi') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://www.gsi.go.jp/ENGLISH/" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/japan-gsi.png" style="width: 100px;"></a>').show();
        } else if (type=='luxembourg') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://act.public.lu/fr/administration-cadastre.html" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/luxembourg-act-logo.png" style="width: 100px;"></a>').show();
        } else if (type=='philippines-namria') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://www.namria.gov.ph" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/240px-National_Mapping_and_Resource_Information_Authority_(NAMRIA).svg.png" style="width: 100px;"></a>').show();
        } else if (type=='spain-ign') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://www.ign.es/csw-inspire/srv/eng/main.home" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/spain-ign-logo.png" style="width: 100px;"></a>').show();
        } else if (type=='taiwan-nlsc') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://maps.nlsc.gov.tw/" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/taiwan-nlsc-logo.png" style="width: 100px;"></a>').show();

        } else if (type=='southafrica-ngi-50k') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://htonl.dev.openstreetmap.org/ngi-tiles/" target="_blank">© NGI</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="mapboxgl-ctrl-logo" target="_blank" href="https://www.mapbox.com/" aria-label="Mapbox logo" rel="noopener"></a>').show();

        } else if (type=='andorra-ideandorra') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://www.ideandorra.ad/" target="_blank">© IDE Andorra</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="andorra-ideandorra-ctrl-logo" target="_blank" href="https://www.ideandorra.ad/" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/andorra-ideandorra-logo.png" style="width: 100px;"></a>').show();

        } else if (type=='uk-os') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://www.ordnancesurvey.co.uk" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/UK-Ordnance-Survey-Logo.png" style="width: 100px;"></a>').show();
        } else if (type=='argentina-ign-50k' || type=='argentina-ign-100k') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://www.ign.gob.ar" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/argentina-ign.png" style="width: 100px;"></a>').show();
        } else if (type=='australia-nsw') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://maps.six.nsw.gov.au" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/Australia-NSW-SixMapsLogo.png" style="width: 100px;"></a>').show();
        } else if (type=='australia-ts') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://nre.tas.gov.au/land-tasmania/the-list/listmap" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/tasmania-list-logo.png" style="width: 100px;"></a>').show();
        } else if (type=='australia-vic') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://services.land.vic.gov.au/landchannel/content/vicmapdata" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/victoria-vicmap.png" style="width: 100px;"></a>').show();
        } else if (type=='austria-bev') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://www.bev.gv.at/portal/page?_pageid=713,3175358&_dad=portal&_schema=PORTAL" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/austria-BEV-logo.png" style="width: 100px;"></a>').show();
        } else if (type=='canada-qc') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://mern.gouv.qc.ca/en/" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/quebec-mern.png" style="width: 100px;"></a>').show();
        } else if (type=='croatia-dgu') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://geoportal.dgu.hr" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/croatia-dgu-logo.png" style="width: 100px;"></a>').show();
        } else if (type=='norway-new') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://kartverket.no" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/norway-kartverket_logo.png" style="width: 100px;"></a>').show();

        } else if (type=='norway-janmayen' || type=='norway-svalbard') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://www.npolar.no/en/maps/" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/Norway-NPI-logo.png" style="width: 100px;"></a>').show();

        } else if (type=='poland-geoportal') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://www.geoportal.gov.pl/en/dane" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/poland-geoportal.png" style="width: 100px;"></a>').show();
        } else if (type=='spain-cataluna') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="caltopo-ctrl-logo" target="_blank" href="https://www.icgc.cat/en/" rel="noopener"><img src="https://peakery-static.s3.us-west-1.amazonaws.com/img/cataluna-icgc-logo.png" style="width: 300px;"></a>').show();
        } else if (type=='slovakia-dgu') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<div style="width: 150px; background-color: #fff; padding-left: 5px; font-weight: 500;">Slovakia DGU</div>').show();
        } else if (type=='australia-qld') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<div style="width: 150px; background-color: #fff; padding-left: 5px; font-weight: 500;">Queensland QTopo</div>').show();
        } else if (type=='canada-on') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<div style="width: 150px; background-color: #fff; padding-left: 5px; font-weight: 500;">Ontario Base Maps</div>').show();
        } else if (type=='slovenia-prostor') {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://caltopo.com/" target="_blank">© CalTopo</a>, <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<div style="width: 150px; background-color: #fff; padding-left: 5px; font-weight: 500;">Slovenia (ProStor)</div>').show();
        } else {
            //remove natural atlas and opentopomap images
            $('.natatl-ctrl-logo').remove();
            $('.opentopomap-ctrl-logo').remove();
            //add attribution
            $('.mapboxgl-ctrl-bottom-right').find('.mapboxgl-ctrl-attrib').html('<a href="https://www.mapbox.com/about/maps/" target="_blank">© Mapbox</a> <a href="http://www.openstreetmap.org/about/" target="_blank">© OpenStreetMap</a>').removeClass('mapboxgl-attrib-empty').show();
            $('.mapboxgl-ctrl-bottom-left').find('.mapboxgl-ctrl').html('<a class="mapboxgl-ctrl-logo" target="_blank" href="https://www.mapbox.com/" aria-label="Mapbox logo" rel="noopener"></a>').show();
        }
    }

    function setMapboxMaxZoom(zoom) {
        var check = checkIfMapboxIsLoaded();
        if (!check) {
            // It's not safe to manipulate layers yet, so wait 200ms and then check again
            setTimeout(function() {
                setMapboxMaxZoom(zoom);
            }, 200);
            return;
        }
        // Whew, now it's safe to manipulate layers!
        map.setMaxZoom(zoom);
        //if map is zoomed more than max, zoom out to max
        var currZoom = map.getZoom();
        if (currZoom > zoom) {
            map.setZoom(zoom);
        }
    }

    function setMapboxMinZoom(zoom) {
        var check = checkIfMapboxIsLoaded();
        if (!check) {
            // It's not safe to manipulate layers yet, so wait 200ms and then check again
            setTimeout(function() {
                setMapboxMinZoom(zoom);
            }, 200);
            return;
        }
        // Whew, now it's safe to manipulate layers!
        map.setMinZoom(zoom);
        //if map is zoomed out more than min, zoom in to min
        var currZoom = map.getZoom();
        if (currZoom < zoom) {
            map.setZoom(zoom);
        }
    }

</script>