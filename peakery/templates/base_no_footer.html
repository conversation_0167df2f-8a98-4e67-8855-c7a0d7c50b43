{% load static %}
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" xml:lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>

    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="google" content="notranslate">
    <meta http-equiv="Content-Language" content="en">

    <meta name="viewport" content="viewport-fit=cover, user-scalable=no, width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#333333">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name=“apple-mobile-web-app-title” content=“peakery”>

    <meta name="title" content="{% block titlemeta_overwrite %}{% block titlemeta %}{% endblock %}{% endblock %} | peakery" />
    <meta name="description" content="{% block description %}Your basecamp for the world’s mountains. Find great peaks to climb, tackle Peak Challenges, track your summits, and follow the mountain adventures of friends.{% endblock %}" />
    <link rel="image_src" href="{% block image_rel %}{% endblock %}">
    <link rel="shortcut icon" href="{{MEDIA_URL}}img/icn/favicon.ico" />

    <link rel="apple-touch-icon" sizes="180x180" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-icon" sizes="120x120" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-icon" sizes="167x167" href="{% static 'img/apple-touch-icon-83,5x83,<EMAIL>' %}">
    <link rel="apple-touch-icon" sizes="152x152" href="{% static 'img/<EMAIL>' %}">

    <link rel="apple-touch-startup-image" media="(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 414px) and (device-height: 736px) and (-webkit-device-pixel-ratio: 3)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3)" href="{% static 'img/<EMAIL>' %}">

    <link rel="apple-touch-startup-image" media="(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 2)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 834px) and (device-height: 1112px) and (-webkit-device-pixel-ratio: 2)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 834px) and (device-height: 1112px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" href="{% static 'img/<EMAIL>' %}">

    <link rel="manifest" href="{% static 'json/manifest.json' %}">
    {% block meta_facebook %}{% endblock %}
    {% block meta_google %}{% endblock %}
    {% block meta_twitter %}{% endblock %}
    <title>{% block title %}{% endblock %} | peakery</title>
    {% block mapbox %}{% endblock %}
    <script type="text/javascript">{% block js_globals %}{% endblock %}</script>
    {% if not debug %}
        {% include "analytics/ga.html" %}
    {% endif %}

        <link rel="stylesheet" href="{% static 'css/0f2533e978d5-2.01.css' %}" type="text/css" media="screen" />
        <link href="{% static 'css/explore.css' %}" rel="stylesheet" type="text/css" media="screen" />
        <link href="{% static 'css/bootstrap.css' %}" rel="stylesheet" type="text/css" media="screen" />
        <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.3.1/css/all.css" integrity="sha384-mzrmE5qonljUremFsqc01SB46JvROS7bZs3IO2EmfFsd15uHvIt+Y8vEf7N7fWAU" crossorigin="anonymous">
        <link href="{% static 'css/bootstrap-toggle.css' %}" rel="stylesheet">
        <link rel="stylesheet" href="{% static 'css/blueimp-gallery.css' %}">
        <link rel="stylesheet" href="{% static 'css/bootstrap-image-gallery.css' %}">
        <link rel="stylesheet" href="{% static 'css/nouislider.css' %}">
        <link rel="stylesheet" href="{% static 'css/drawer.css' %}">
        <link rel="stylesheet" href="{% static 'css/formValidation/formValidation.css' %}">
        <link rel="stylesheet" href="{% static 'css/jquery-ui.css' %}">

        <link rel="stylesheet" type="text/css" href="{% static 'vendor/ModalWindowEffects/css/default.css' %}" />
        <link rel="stylesheet" type="text/css" href="{% static 'vendor/ModalWindowEffects/css/component.css' %}" />

        <link href="{% static 'vendor/s3.fine-uploader/fine-uploader-new.css' %}" rel="stylesheet">

        <link href="{% static 'css/peakery-custom-3.09.css' %}" rel="stylesheet" type="text/css" media="screen" />
        <link href="{% static '' %}css/peakery-custom-3.10-base-extras.css" rel="stylesheet" type="text/css" media="screen" />
        <link href="{% static '' %}css/peakery-custom-3.08-base-nofooter-extras.css" rel="stylesheet" type="text/css" media="screen" />

        <style type="text/css">
            {% if IS_MOBILE_APP_ACCESS == 'True' %}
            .navbar-inverse, .dark-background-row, #main {background-image: none; background-color: #333;}
            .fa-bars {display: none;}
            {% if MOBILE_APP_BACK == 'True' %}
            .mobile-logo-title {left: 50px;}
            {% else %}
            .mobile-logo-title {left: 10px;}
            {% endif %}
            {% else %}
            .mobile-logo-title {left: 50px;}
            {% endif %}
            @media screen and (max-width: 767px) and (min-width: 1px) {
                body {
                    letter-spacing: 0px !important;
                }
            }
            .container {
                max-width: inherit;
                padding-left: 35px;
                padding-right: 35px;
            }

        </style>

        <script src="{% static 'vendor/s3.fine-uploader/s3.fine-uploader.js' %}"></script>

        <script type="text/template" id="qq-gpx-template">
            <div class="qq-uploader-selector qq-uploader" qq-drop-area-text="" style="max-height: 40px; background-color: transparent; border: none; padding: 0px; min-height: 0px; margin-top: -10px; overflow-x: hidden;">
                <div class="qq-upload-drop-area-selector qq-upload-drop-area" qq-hide-dropzone>
                    <span class="qq-upload-drop-area-text-selector"></span>
                </div>
                <ul class="qq-upload-list-selector qq-upload-list" aria-live="polite" aria-relevant="additions removals" style="max-height: none; display: none;">
                    <li style="padding: 5px;">
                        <div style="display: flex; display: -webkit-flex;">
                            <div class="qq-thumbnail-wrapper" style="position: relative; align-content: stretch;">
                                <div>
                                    <img class="qq-thumbnail-selector" qq-max-size="300" qq-server-scale style="margin-right: 0px;">
                                </div>
                            </div>
                        </div>
                    </li>
                </ul>
                <div id="gpx-upload-button" class="qq-upload-button-selector btn btn-secondary" style="height: 45px; line-height: 25px; font-size: 14px; margin-top: 0px; padding-top: 10px; background-color: transparent; border: none; color: rgb(204, 204, 204); position: relative; overflow: hidden; direction: ltr;">
                    <div id="gpx-upload-button-div"><div class="log-climb-dropdown-icon-div"><i class="fas fa-file-upload"></i></div><div class="log-climb-dropdown-label-div" style="margin-top: -2px; letter-spacing: 0.5px;">Log climb from GPX file</div></div>
                </div>
                <span class="qq-drop-processing-selector qq-drop-processing">
                    <span>Processing dropped files...</span>
                    <span class="qq-drop-processing-spinner-selector qq-drop-processing-spinner"></span>
                </span>

                <dialog class="qq-alert-dialog-selector">
                    <div class="qq-dialog-message-selector"></div>
                    <div class="qq-dialog-buttons">
                        <button type="button" class="qq-cancel-button-selector btn btn-secondary">Close</button>
                    </div>
                </dialog>

                <dialog class="qq-confirm-dialog-selector">
                    <div class="qq-dialog-message-selector"></div>
                    <div class="qq-dialog-buttons">
                        <button type="button" class="qq-cancel-button-selector btn btn-secondary">No</button>
                        <button type="button" class="qq-ok-button-selector btn btn-secondary">Yes</button>
                    </div>
                </dialog>

                <dialog class="qq-prompt-dialog-selector">
                    <div class="qq-dialog-message-selector"></div>
                    <input type="text">
                    <div class="qq-dialog-buttons">
                        <button type="button" class="qq-cancel-button-selector btn btn-secondary">Cancel</button>
                        <button type="button" class="qq-ok-button-selector btn btn-secondary">Ok</button>
                    </div>
                </dialog>
            </div>
        </script>

    {% block css_for_includes %}{% endblock %}
        <script src="{% static 'js/jquery.js' %}"></script>
        <script src="{% static 'js/jquery-ui.js' %}"></script>
        <script src="{% static 'js/jquery.browser.js' %}"></script>
        <script src="{% static 'js/jquery.autocomplete.js' %}"></script>
        <script type="text/javascript" src="{% static 'js/bootstrap.js' %}"></script>
        <script src="{% static 'js/jquery.timeago.js' %}"></script>
        <script src="{% static 'js/bootstrap-toggle.js' %}"></script>
        <script src="{% static 'js/blueimp-gallery.js' %}"></script>
        <script src="{% static 'js/jquery.blueimp-gallery.js' %}"></script>
        <script src="{% static 'js/bootstrap-image-gallery.js' %}"></script>
        <script src="{% static 'js/jquery-dateFormat.js' %}"></script>
        <script src="{% static 'js/trunk8.js' %}"></script>
        <script src="{% static 'js/truncate.js' %}"></script>
        <script src="{% static 'js/nouislider.js' %}"></script>
        <script src="{% static 'js/autosize.js' %}"></script>
        <script src="{% static 'js/formValidation/formValidation.js' %}"></script>
        <script src="{% static 'js/formValidation/framework/bootstrap.js' %}"></script>
        <script src="{% static 'js/drawer.js' %}"></script>
        <script src="{% static 'js/jquery.floatThead.js' %}"></script>

        <script src="{% static 'vendor/ModalWindowEffects/js/modalEffects.js' %}"></script>
        <script src="{% static 'vendor/ModalWindowEffects/js/modernizr.custom.js' %}"></script>
        <script src="{% static 'vendor/ModalWindowEffects/js/classie.js' %}"></script>
        <script src="{% static 'vendor/ModalWindowEffects/js/cssParser.js' %}"></script>

        <script type="text/javascript" src="{% static 'js/jquery.form.js' %}"></script>
        <script type="text/javascript" src="{% static 'js/facebox.js' %}"></script>
        <script type="text/javascript" src="{% static 'js/peakery.js' %}"></script>
        <script type="text/javascript" src="{% static 'js/jquery.popup.js' %}"></script>
        <script type="text/javascript" charset="utf-8">
            $(document).ready(function() {
                {% include "main/ssi/ssi.js" %}
            });
        </script>
        <script type="text/javascript" src="{% static 'js/jquery.placeholder.js' %}"></script>
        <script type="text/javascript">
            $(function(){
                $('input, textarea').placeholder();
            });
        </script>
        <script type="text/javascript" src="{% static 'js/jquery.elastic.source.js' %}"></script>
        <script type="text/javascript" src="{% static 'js/jquery.OnEnter.js' %}"></script>
        <script type="text/javascript" src="{% static 'js/jquery.scrollTo-1.4.2.js' %}"></script>

        <script type="text/javascript" src="{% static '' %}js/peakery-shasta-3.19-base-extras.js"></script>
        <script type="text/javascript" src="{% static '' %}js/peakery-shasta-3.07.js"></script>
        <script type="text/javascript" src="{% static '' %}js/peakery-shasta-3.05-base-admin.min.js"></script>

    {% block extrajs %}{% endblock %}
    {% block js_for_includes %}{% endblock %}
</head>

<body class="drawer drawer--right">

<div id="darkness" style="z-index: 999999; background: rgba(0, 0, 0, 0.8);"></div>

{% block body_form %}{% endblock %}

<nav class="drawer-nav" role="navigation">
    <ul class="drawer-menu drawer-menu--right">

        <li id="slideout-nav-peakery-li" class="slideout-li" style="margin-top: -8px; text-align: center;">
            <img style="margin-top: -3px;" src="{% static 'img/navbar-logo-inactive.png' %}" width="24" alt="peakery" title="peakery" /><span style="font-size: 20px; color: #FFFFFF; letter-spacing: 0.54px; padding-left: 10px;">peakery</span>
        </li>

        <li id="slideout-nav-peak-search-li" class="slideout-li">
            <div style="float: left; width: 100%; margin-bottom: 20px;" class="hidden-sm hidden-md hidden-lg">
                <form novalidate method="post" action="javascript: mobileNavSearch();" style="margin-right: 20px; margin-left: 20px;">
                    <input type="search" placeholder="Search peaks..." name="mobileq1" id="mobileq1" class="peak-search-input-mobile" style="border: none;">
                    <div id="mobile-searchbox-icon" style="position: absolute; top: 16px; right: 18px; color: #999; font-size: 22px;">
                        <i class="fa fa-search"></i>
                    </div>
                </form>
            </div>
        </li>

        <li id="slideout-nav-join-login-li" class="nav-hidden slideout-li logged-out-nav">
            <div style="width: 50%; padding-right: 10px; float: left; margin-top: -3px;">
                <a id="slideout-nav-log-climb-btn" class="btn btn-primary" data-toggle="modal" data-target="#accounts-login">Log a climb</a>
            </div>
            <div style="width: 50%; padding-left: 10px; float: left; margin-top: -3px;">
                <a id="slideout-nav-login-btn" class="btn btn-secondary" data-toggle="modal" data-target="#accounts-login">Join/Login</a>
            </div>
        </li>

        <li id="slideout-nav-log-climb-alerts-li" class="nav-hidden slideout-li logged-in-nav">
            <div style="width: 50%; padding-right: 10px; float: left; margin-top: -3px;">
                {% if IS_MOBILE_APP_ACCESS == 'True' %}
                <a id="slideout-nav-log-a-climb-btn" class="btn btn-primary" href="href="javascript: Android.logClimb();">Log a climb</a>
                {% else %}
                <a id="slideout-nav-log-a-climb-btn" class="btn btn-primary" href="/peaks/log_climb/">Log a climb</a>
                {% endif %}
            </div>
            <div style="width: 50%; padding-left: 10px; float: left; margin-top: -3px;">
                <a id="slideout-nav-alerts-btn" class="btn btn-secondary"><i style="margin-right: 5px;" class="fa fa-bell"></i><span class="nav-hidden nav-member-news-count"></span></a></a>
            </div>
        </li>

        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" href="/latest/">
                <div class="slideout-links-icon-div"><i class="far fa-clock"></i></div><div class="slideout-links-label-div">Latest</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" href="/map/">
                <div class="slideout-links-icon-div"><i class="fa fa-map"></i></div><div class="slideout-links-label-div">Peak Map</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" href="/peaks/">
                <div class="slideout-links-icon-div"><i class="fas fa-list"></i></div><div class="slideout-links-label-div">Peak List</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" href="/world-mountains/">
                <div class="slideout-links-icon-div"><i class="fa fa-globe-americas"></i></div><div class="slideout-links-label-div">Peaks by Region</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" href="/challenges/">
                <div class="slideout-links-icon-div"><i class="fa fa-trophy"></i></div><div class="slideout-links-label-div">Peak Challenges</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link nav-hidden logged-in-nav">
            <a class="slideout-links nav-member-profile-link" href="">
                <div class="slideout-links-icon-div"><i class="fa fa-user"></i></div><div class="slideout-links-label-div">My Stats</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link nav-hidden logged-in-nav">
            <a class="slideout-links nav-member-map-link" href="">
                <div class="slideout-links-icon-div"><i class="fa fa-map"></i></div><div class="slideout-links-label-div">My Map</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link nav-hidden logged-in-nav">
            <a class="slideout-links nav-member-badges-link" href="">
                <div class="slideout-links-icon-div"><i class="fa fa-th-large"></i></div><div class="slideout-links-label-div">My Badges</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link nav-hidden logged-in-nav">
            <a class="slideout-links nav-member-summits-link" href="">
                <div class="slideout-links-icon-div"><i class="fa fa-play fa-icon-rotate"></i></div><div class="slideout-links-label-div">My Summits</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link nav-hidden logged-in-nav">
            <a class="slideout-links nav-member-challenges-link" href="">
                <div class="slideout-links-icon-div"><i class="fa fa-trophy"></i></div><div class="slideout-links-label-div">My Challenges</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link nav-hidden logged-in-nav">
            <a class="slideout-links nav-member-photos-link" href="">
                <div class="slideout-links-icon-div"><i class="fa fa-images"></i></div><div class="slideout-links-label-div">My Photos</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" href="/members/">
                <div class="slideout-links-icon-div"><i class="fa fa-users"></i></div><div class="slideout-links-label-div">Members</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" href="/top-contributors/">
                <div class="slideout-links-icon-div"><i class="fa fa-award"></i></div><div class="slideout-links-label-div">Contributors</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link nav-hidden logged-in-nav">
            <a class="slideout-links nav-member-settings-link" href="/settings/">
                <div class="slideout-links-icon-div"><i class="fa fa-cog"></i></div><div class="slideout-links-label-div">Settings</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link nav-member-admin-link nav-hidden admin-user-nav hidden-xs">
            <a class="toggle-admin-mode">
                <div class="slideout-links-icon-div"><i class="fa fa-wrench"></i></div><div class="slideout-links-label-div">Admin</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link nav-hidden logged-in-nav">
            <a class="slideout-links nav-member-logout-link" href="/accounts/logout/">
                <div class="slideout-links-icon-div"><i class="fa fa-power-off"></i></div><div class="slideout-links-label-div">Logout</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <div class="slideout-links-icon-div">&nbsp;</div>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" href="/about/">
                <div class="slideout-links-icon-div"><i class="fa fa-info-circle"></i></div><div class="slideout-links-label-div">About</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" href="/peak-month/">
                <div class="slideout-links-icon-div"><i class="fa fa-calendar"></i></div><div class="slideout-links-label-div">Peak Month</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" target="_blank" onclick="event.stopPropagation();" href="http://blog.peakery.com/">
                <div class="slideout-links-icon-div"><i class="fa fa-newspaper"></i></div><div class="slideout-links-label-div">News</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" href="/changelog/">
                <div class="slideout-links-icon-div"><i class="fa fa-clipboard-list"></i></div><div class="slideout-links-label-div">Changelog</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" target="_blank" onclick="event.stopPropagation();" href="http://www.facebook.com/peakery">
                <div class="slideout-links-icon-div"><i class="fab fa-facebook"></i></div><div class="slideout-links-label-div">Facebook</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" target="_blank" onclick="event.stopPropagation();" href="http://www.instagram.com/peakerycom">
                <div class="slideout-links-icon-div"><i class="fab fa-instagram"></i></div><div class="slideout-links-label-div">Instagram</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" target="_blank" onclick="event.stopPropagation();" href="http://twitter.com/peakery">
                <div class="slideout-links-icon-div"><i class="fab fa-twitter-square"></i></div><div class="slideout-links-label-div">Twitter</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" href="/terms/">
                <div class="slideout-links-icon-div"><i class="fa fa-align-left"></i></div><div class="slideout-links-label-div">Terms</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" href="/privacy/">
                <div class="slideout-links-icon-div"><i class="fa fa-user-lock"></i></div><div class="slideout-links-label-div">Privacy</div>
            </a>
        </li>
        <li class="slideout-li slideout-links-li navbar-li-link">
            <a class="slideout-links" href="mailto:<EMAIL>">
                <div class="slideout-links-icon-div"><i class="fa fa-envelope"></i></div><div class="slideout-links-label-div">Contact us</div>
            </a>
        </li>

    </ul>
</nav>

<main id="main-content" role="main">

<div class="md-overlay"></div>

<div id="content-holder" style="background-color:#333;">

<!-- Fixed navbar -->
<nav class="navbar navbar-inverse navbar-fixed-top">
    <div class="container">
        <div class="navbar-header">
            <div class="hidden-sm hidden-md hidden-lg" style="right: 60px; position: absolute;">
                <a style="display: none;" id="mobile-nav-alerts" class="dropdown-toggle btn btn-primary"><i id="mobile-nav-alerts-bell" class="fa fa-bell"></i><div id="mobile-nav-alerts-count" class="nav-hidden news_icon_text nav-member-news-count"></div></a>
            </div>
            {% if IS_MOBILE_APP_ACCESS != 'True' %}
            <button style="color: #fff; font-size: 22px;" type="button" class="drawer-toggle drawer-hamburger hidden-sm hidden-md hidden-lg">
                <i class="fa fa-bars" aria-hidden="true"></i>
            </button>
            {% endif %}
            <div class="logo mobile-logo hidden-sm hidden-md hidden-lg" style="position: fixed; top: 0px; left: 0px; z-index: 3;">
            <a class="navbar-brand" href="/">
                <img id="mobile-nav-logo" {% if IS_MOBILE_APP_ACCESS == 'True' %}style="display: none;"{% endif %} src="{% static 'img/' %}{% if nav_logo_img %}{{ nav_logo_img }}{% else %}navbar-logo-inactive.png{% endif %}" width="24" alt="peakery" title="peakery" />
            </a>
        </div>
        <div class="logo hidden-xs">
            <a class="navbar-brand" href="/">
                <img id="main-nav-logo" src="{% static 'img/' %}{% if nav_logo_img %}{{ nav_logo_img }}{% else %}navbar-logo-inactive.png{% endif %}" width="24" alt="peakery" title="peakery" />
            </a>
            <a class="navbar-brand-latest" href="/latest/">
                <div id="nav-logo-tagline">Latest</div>
            </a>
        </div>
        <div class="mobile-logo-title" style="color: #fff; position: absolute; top: 0px; height: 50px; z-index: 3;">
            <h1 class="visible-xs mobile-logo-tagline">{% if nav_page_name %}{{ nav_page_name }}{% else %}peakery{% endif %}{% block mobile_header_follow %}{% endblock %}</h1>
        </div>
    </div>
    <div id="navbar" class="collapse navbar-collapse">
        <ul style="cursor: default; display: none;" class="nav nav-justified">
            <li id="navbar-link-map-onnav">
                <a id="navbar-map-link" class="navbar-primary" href="/map/">Map</a>
            </li>
            <li class="dropdown peaks-dropdown hidden-xs" id="navbar-link-peaks">
                <a id="nav-peaks-dropdown-link" class="dropdown-toggle btn btn-primary">Peaks <i class="fa fa-angle-down" style="font-size: 12px;"></i></a>
                <ul style="display: none;" class="dropdown-menu nav-peaks-dropdown">
                <li class="navbar-li-link" id="navbar-link-map"><div class="nav-peaks-dropdown-tab"></div><a class="navbar-primary" href="/map/"><div class="nav-peaks-dropdown-icon-div"><i class="fa fa-map"></i></div><div class="nav-peaks-dropdown-label-div">Map</div></a></li>
                <li class="navbar-li-link" id="navbar-link-peaks"><div class="nav-peaks-dropdown-tab"></div><a class="navbar-primary" href="/peaks/"><div class="nav-peaks-dropdown-icon-div"><i class="fa fa-list"></i></div><div class="nav-peaks-dropdown-label-div">List</div></a></li>
                <li class="navbar-li-link" id="navbar-link-regions"><a class="navbar-primary" href="/world-mountains/"><div class="nav-peaks-dropdown-icon-div"><i class="fa fa-globe-americas"></i></div><div class="nav-peaks-dropdown-label-div">Regions</div></a></li>
                <li class="navbar-li-link" id="navbar-link-challenges"><a class="navbar-primary" href="/challenges/"><div class="nav-peaks-dropdown-icon-div"><i class="fa fa-trophy"></i></div><div class="nav-peaks-dropdown-label-div">Challenges</div></a></li>
                <li class="nav-hidden logged-in-nav navbar-li-link" id="navbar-link-add-peak"><a class="navbar-primary" href="/peaks/add/"><div class="nav-peaks-dropdown-icon-div"><i class="fa fa-plus"></i></div><div class="nav-peaks-dropdown-label-div">Add missing peak</div></a></li>
                <li class="nav-hidden logged-out-nav navbar-li-link" id="navbar-link-fake-add-peak"><a class="navbar-primary join-peakery"><div class="nav-peaks-dropdown-icon-div"><i class="fa fa-plus"></i></div><div class="nav-peaks-dropdown-label-div">Add missing peak</div></a></li>
                </ul>
            </li>
            <li id="navbar-link-challenges-onnav">
                <a id="navbar-challenges-link" class="navbar-primary" href="/challenges/">Challenges</a>
            </li>
            <li class="dropdown more-dropdown hidden-xs" id="navbar-link-more">
                <a id="nav-more-dropdown-link" class="dropdown-toggle btn btn-primary">More <i class="fa fa-angle-down" style="font-size: 12px;"></i></a>
                <ul style="display: none;" class="dropdown-menu nav-more-dropdown">
                <li class="navbar-li-link" id="navbar-link-members"><div class="nav-more-dropdown-tab"></div><a class="nav-member-more-link" href="/members/"><div class="nav-more-dropdown-icon-div"><i class="fa fa-users"></i></div><div class="nav-more-dropdown-label-div">Members</div></a></li>
                <li class="navbar-li-link" id="navbar-link-contributors"><a class="nav-member-more-link" href="/top-contributors/"><div class="nav-more-dropdown-icon-div"><i class="fa fa-award"></i></div><div class="nav-more-dropdown-label-div">Contributors</div></a></li>
                <li class="navbar-li-link" id="navbar-link-about"><a class="nav-member-more-link" href="/about/"><div class="nav-more-dropdown-icon-div"><i class="fa fa-info-circle"></i></div><div class="nav-more-dropdown-label-div">About</div></a></li>
                <li class="navbar-li-link" id="navbar-link-peak-month"><a class="nav-member-more-link" href="/peak-month/"><div class="nav-more-dropdown-icon-div"><i class="fa fa-calendar"></i></div><div class="nav-more-dropdown-label-div">Peak Month</div></a></li>
                <li class="navbar-li-link" id="navbar-link-blog"><a class="nav-member-more-link" target="_blank" onclick="event.stopPropagation();" href="http://blog.peakery.com/"><div class="nav-more-dropdown-icon-div"><i class="fa fa-newspaper"></i></div><div class="nav-more-dropdown-label-div">News</div></a></li>
                <li class="navbar-li-link" id="navbar-link-changelog"><a class="nav-member-more-link" href="/changelog/"><div class="nav-more-dropdown-icon-div"><i class="fa fa-clipboard-list"></i></div><div class="nav-more-dropdown-label-div">Changelog</div></a></li>
                <li class="navbar-li-link" id="navbar-link-facebook"><a class="nav-member-more-link" target="_blank" onclick="event.stopPropagation();" href="http://www.facebook.com/peakery"><div class="nav-more-dropdown-icon-div"><i class="fab fa-facebook-square"></i></div><div class="nav-more-dropdown-label-div">Facebook</div></a></li>
                <li class="navbar-li-link" id="navbar-link-twitter"><a class="nav-member-more-link" target="_blank" onclick="event.stopPropagation();" href="http://twitter.com/peakery"><div class="nav-more-dropdown-icon-div"><i class="fab fa-twitter-square"></i></div><div class="nav-more-dropdown-label-div">Twitter</div></a></li>
                <li class="navbar-li-link" id="navbar-link-instagram"><a class="nav-member-more-link" target="_blank" onclick="event.stopPropagation();" href="http://www.instagram.com/peakerycom"><div class="nav-more-dropdown-icon-div"><i class="fab fa-instagram"></i></div><div class="nav-more-dropdown-label-div">Instagram</div></a></li>
                <li class="navbar-li-link" id="navbar-link-terms"><a class="nav-member-more-link" href="/terms/"><div class="nav-more-dropdown-icon-div"><i class="fa fa-align-left"></i></div><div class="nav-more-dropdown-label-div">Terms</div></a></li>
                <li class="navbar-li-link" id="navbar-link-privacy"><a class="nav-member-more-link" href="/privacy/"><div class="nav-more-dropdown-icon-div"><i class="fa fa-user-lock"></i></div><div class="nav-more-dropdown-label-div">Privacy</div></a></li>
                <li class="navbar-li-link" id="navbar-link-contact"><a class="nav-member-more-link" href="mailto:<EMAIL>"><div class="nav-more-dropdown-icon-div"><i class="fa fa-envelope"></i></div><div class="nav-more-dropdown-label-div">Contact us</div></a></li>
                </ul>
            </li>
            <li class="nav-hidden logged-out-nav" id="navbar-link-login">
                <a id="navbar-login-link" class="navbar-primary" data-toggle="modal" data-target="#accounts-login">Login</a>
            </li>
            <li class="nav-hidden logged-out-nav" id="navbar-link-join">
                <a id="navbar-join-link" class="navbar-secondary join-peakery">Join</a>
            </li>
            <li class="hidden-xs peak-search-input-desktop-li">
                <div class="nav-explore-container">
                    <form novalidate method="post" action="javascript: navSearch();">
                        <input class="peak-search-input-desktop" type="search" placeholder="Search peaks..." name="q" id="q1" onkeyup="buttonUp();" required>
                        <div id="searchbox-icon-div" style="position: relative; top: -2px;">
                            <a id="searchbox-icon" class="searchbox-icon navbar-search" style="top: -23px; right: 7px;"><i class="fa fa-search"></i></a>
                        </div>
                    </form>
                </div>
            </li>
            <li class="nav-hidden logged-out-nav hidden-xs navbar-log-climb-li">
                <a id="fake-log-your-climb" class="btn btn-primary" data-toggle="modal" data-target="#accounts-login">Log a climb</a>
            </li>
            <li class="nav-hidden logged-in-nav hidden-xs navbar-log-climb-li">
                <a id="nav-log-your-climb" class="dropdown-toggle btn btn-primary">Log a climb</a>
                <ul style="display: none;" class="dropdown-menu log-climb-dropdown">
                <li id="log-climb-log-with-gpx"><div class="log-climb-dropdown-tab"></div><a class="navbar-primary" id="gpx-file-1" style="padding-left: 0px;"><div class="log-climb-dropdown-icon-div"><i class="fas fa-file-upload"></i></div><div class="log-climb-dropdown-label-div">Log with GPX</div></a></li>
                <li class="navbar-li-link" id="log-climb-log-this-peak" style="display: none;"><a class="navbar-primary" href=""><div class="log-climb-dropdown-icon-div"><i class="fas fa-pen-square"></i></div><div class="log-climb-dropdown-label-div log-climb-dropdown-this-peak-div ellipsis"></div></a></li>
                <li class="navbar-li-link" id="log-climb-log-manually"><a class="navbar-primary" href="/peaks/log_climb/"><div class="log-climb-dropdown-icon-div"><i class="fas fa-pen-square"></i></div><div class="log-climb-dropdown-label-div">Log climb manually</div></a></li>
                </ul>
            </li>
            <li class="nav-hidden logged-in-nav dropdown you-dropdown navbar-you-li hidden-xs" id="navbar-link-you">
                <a class="dropdown-toggle navbar-you navbar-primary" id="navbar-you-link"><span id="navbar-you-dropdown-toggle"><img src="" id="navbar-you-avatar"></span></a>
                <ul style="display: none;" class="dropdown-menu nav-you-dropdown">
                <li class="navbar-li-link" id="navbar-link-my-stats"><div class="nav-you-dropdown-tab"></div><a class="nav-member-profile-link" href=""><div class="nav-you-dropdown-icon-div"><i class="fa fa-user"></i></div><div class="nav-you-dropdown-label-div">My Stats</div></a></li>
                <li class="navbar-li-link" id="navbar-link-my-map"><a class="nav-member-map-link" href=""><div class="nav-you-dropdown-icon-div"><i class="fa fa-map"></i></div><div class="nav-you-dropdown-label-div">My Map</div></a></li>
                <li class="navbar-li-link" id="navbar-link-my-badges"><a class="nav-member-badges-link" href=""><div class="nav-you-dropdown-icon-div"><i class="fa fa-th-large"></i></div><div class="nav-you-dropdown-label-div">My Badges</div></a></li>
                <li class="navbar-li-link" id="navbar-link-my-summits"><a class="nav-member-summits-link" href=""><div class="nav-you-dropdown-icon-div"><i class="fa fa-play fa-icon-rotate"></i></div><div class="nav-you-dropdown-label-div">My Summits</div></a></li>
                <li class="navbar-li-link" id="navbar-link-my-challenges"><a class="nav-member-challenges-link" href=""><div class="nav-you-dropdown-icon-div"><i class="fa fa-trophy"></i></div><div class="nav-you-dropdown-label-div">My Challenges</div></a></li>
                <li class="navbar-li-link" id="navbar-link-my-photos"><a class="nav-member-photos-link" href=""><div class="nav-you-dropdown-icon-div"><i class="fa fa-images"></i></div><div class="nav-you-dropdown-label-div">My Photos</div></a></li>
                <li class="navbar-li-link" id="navbar-link-my-settings"><a class="nav-member-settings-link" href="/settings/"><div class="nav-you-dropdown-icon-div"><i class="fa fa-cog"></i></div><div class="nav-you-dropdown-label-div">Settings</div></a></li>
                <li class="navbar-li-link nav-hidden admin-user-nav hidden-xs" id="navbar-link-admin"><a class="nav-member-admin-link toggle-admin-mode"><div class="nav-you-dropdown-icon-div"><i class="fa fa-wrench"></i></div><div class="nav-you-dropdown-label-div">Admin</div></a></li>
                <li class="navbar-li-link" id="navbar-link-logout"><a class="nav-member-logout-link" href="/accounts/logout/"><div class="nav-you-dropdown-icon-div"><i class="fa fa-power-off"></i></div><div class="nav-you-dropdown-label-div">Log out</div></a></li>
                </ul>
            </li>
            <li class="nav-hidden logged-in-nav hidden-xs navbar-alerts-li" id="navbar-alerts">
                <a id="nav-alerts" class="dropdown-toggle btn btn-primary"><i id="nav-alerts-bell" class="fa fa-bell"></i><div id="nav-alerts-count" class="nav-hidden news_icon_text nav-member-news-count"></div></a>
                <div style="display: none; position: absolute; background: transparent;" id="nav-alerts-dropdown-div">
                    <div class="nav-alerts-dropdown-tab"></div>
                    <div id="nav-alerts-container" style="overflow-y: scroll; height: 0px;">
                        <ul id="nav-alerts-ul" class="dropdown-menu nav-alerts-dropdown" style="display: block; border: none; position: relative; top: 0px; margin-top: 0px;"></ul>
                    </div>
                </div>
            </li>
        </ul>
    </div><!--/.nav-collapse -->
    {% block fixed_page_header %}{% endblock %}
  </div>
</nav>

<!-- Begin page content -->
<div id="content-body" class="{{ fixed_subnav_class }}">

    <div id="main">
        {% block content %}
        {% endblock %}
    </div>

    {% block footer %}{% endblock %}

</div> <!-- /content-body -->

</div> <!-- /content-holder -->

<div class="message-modal modal fade" id="message-modal" tabindex="-1" role="dialog" aria-labelledby="message-modal-label">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
              <h4 class="modal-title" id="message-modal-label"></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div id="message-modal-body" class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="margin-bottom: 5px; font-size: 16px;"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<div style="padding-left: 0px; z-index: 99999;" class="mobile-alerts-modal modal fade" id="mobile-alerts-modal" tabindex="-1" role="dialog" aria-labelledby="mobile-alerts-modal-label">
    <div class="modal-dialog" style="width: 100%; height: 100%; margin-top: 0px;" role="document">
        <div class="modal-content" style="height: 100%;">
            <div class="modal-header">
              <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
              <h4 class="modal-title" id="mobile-alerts-modal-label"><span id="mobile-alerts-modal-title">Notifications</span></h4>
            </div>
            <div id="mobile-alerts-modal-body" class="modal-body" style="height: 100%; padding: 0px;">
                <div id="mobile-alerts-container" style="overflow-y: scroll; height: 580px; width: 100%;">
                    <ul id="mobile-alerts-ul" class="dropdown-menu nav-alerts-dropdown" style="display: block; border-radius: 0px; position: relative; top: 0px; width: 100%; margin-top: 0px;"></ul>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="accounts-sign-up-modal modal fade" id="accounts-sign-up" tabindex="-1" role="dialog" aria-labelledby="accounts-sign-up-label">
    <div class="modal-dialog" style="width: 350px;" role="document">
        <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
              <h4 class="modal-title" id="accounts-sign-up-label"><span id="accounts-sign-up-title">Join peakery</span></h4>
            </div>
            <div class="modal-body"></div>
        </div>
    </div>
</div>

<div class="accounts-choose-username-modal modal fade" id="accounts-choose-username" tabindex="-1" role="dialog" aria-labelledby="accounts-choose-username-label">
    <div class="modal-dialog" style="width: 350px;" role="document">
        <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
              <h4 class="modal-title" id="accounts-choose-username-label"><span id="accounts-choose-username-title">Choose your peakery username</span></h4>
            </div>
            <div class="modal-body"></div>
        </div>
    </div>
</div>

<div class="accounts-login-modal modal fade" id="accounts-login" tabindex="-1" role="dialog" aria-labelledby="accounts-login-label">
    <div class="modal-dialog" style="width: 350px;" role="document">
        <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
              <h4 class="modal-title" id="accounts-login-label"><span id="accounts-login-title">Login</span></h4>
            </div>
            <div class="modal-body">
                <div id="ajax_account_output" style="display: none"></div>
                <div style="display: none; margin-bottom: 15px;" id="login_error">Incorrect password.<br>Try again.</div>
                <form id="signin" method="post" action="/accounts/login-user-lite/" style="text-align: center;">
                    {% csrf_token %}
                    <input class="form-control nav-login-input" placeholder="username or email" type="text" autocapitalize="none" id="username" name="username" style="border: 1px solid #ccc; height: 50px; color: #333;">
                    <input class="form-control nav-login-input" placeholder="password" type="password" id="password" name="password" style="border: 1px solid #ccc; height: 50px; color: #333;">
                    <button id="signin-submit" type="submit" class="btn btn-secondary nav-login-submit" style="font-size: 14px; margin-left: 10px; line-height: 20px;">Login</button>
                    <div style="text-align: center;height: 50px;line-height: 60px;">
                        <a id="forgot-password" style="cursor: pointer; color: #999;">Forgot password</a>
                    </div>
                    <div style="text-align: center;height: 50px;line-height: 60px;">
                        Not a member yet? <a style="font-weight: 500; cursor: pointer;" id="not-a-member">Sign up</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="accounts-forgot-password-modal modal fade" id="accounts-forgot-password" tabindex="-1" role="dialog" aria-labelledby="accounts-forgot-password-label">
    <div class="modal-dialog" style="width: 350px;" role="document">
        <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
              <h4 class="modal-title" id="accounts-forgot-password-label"><span id="accounts-forgot-password-title">Forgot password</span></h4>
            </div>
            <div class="modal-body">
                <div class="signUpWithEmail">
                    <script type="text/javascript">
                        $(function() {
                            $('#id_email').on('input', function(e) {
                                if ($(this).val() != "") {
                                    $(this).parent().children('.holder').hide();
                                } else {
                                    $(this).parent().children('.holder').show();
                                }
                            });
                        });
                    </script>
                    <form id="reset-password-form" action="" method="post">
                        <div style="display: none;" id="password_reset_error"></div>
                        <div style="margin-bottom: 20px;">Enter your email and we’ll send password reset instructions:</div>
                        <span class="a">
                            <input class="email" id="id_email" maxlength="100" name="email" type="text" autocapitalize="none">
                            <span class="holder" style="">Email address</span>
                        </span>
                        <div style="text-align: center;height: 50px;line-height: 60px; margin-top: 25px; margin-bottom: 15px;">
                            {% csrf_token %}
                            <input id="reset-my-password" type="submit" value="Reset my password" class="btn set2 input" style="font-size: 14px; height: 50px;">
                        </div>
                    </form>
                </div>
                <div id="password-reset-success" style="display: none;">
                    We just sent an email to <span id="password-reset-email"></span> with password reset instructions.
                </div>
            </div>
        </div>
    </div>
</div>

{% block gallery %}
{% endblock %}

</main>

{% block end_body_form %}{% endblock %}

</body>
</html>