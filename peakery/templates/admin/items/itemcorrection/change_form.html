{% extends "admin/change_form.html" %}
{% load static %}
{% block extrahead %}{{ block.super }}

<style>

    #gm-custom-mapfilter, #gm-custom-maplayers, #gm-custom-maplegend {
        -moz-box-shadow: 0 0 2px rgba(0,0,0,.1);
        -webkit-box-shadow: 0 0 2px rgba(0,0,0,.1);
        box-shadow: 0 0 0 2px rgba(0,0,0,.1);
    }

    #peak-search-3d {
        cursor: pointer;
        font-size: 16px;
        color: #333;
        font-weight: 700;
        line-height: 30px;
        text-align: center;
        margin-left: 4px;
    }

    @media screen and (min-width: 1024px) {
        #gm-custom-maplegend {
            bottom: 233px;
        }
        #gm-custom-map3d {
            bottom: 278px;
        }
    }

    @media screen and (min-width: 1px) and (max-width: 1023px) {
        #gm-custom-maplegend {
            top: 100px;
        }
        #gm-custom-map3d {
            top: 145px;
        }
    }

    .mapboxgl-ctrl {
        margin-bottom: 15px !important;
    }

    #gm-custom-mapdropdown, #gm-custom-mapbutton {
        opacity: 1;
        webkit-backdrop-filter: blur(5px);
        backdrop-filter: blur(5px);
    }

    #gm-custom-mapunits:hover {
        background-color: transparent !important;
    }

    #gm-custom-mapbutton, #gm-custom-mapdropdown {
        border: 2px solid rgba(0,0,0,0.15);
    }

    #gm-custom-mapbutton {
        width: 180px;
        margin-left: 90px;
        border-top-right-radius: 8px;
        border-top-left-radius: 8px;
        border-bottom-right-radius: 8px;
        border-bottom-left-radius: 8px;
    }

    #gm-custom-mapbutton:hover {
        border-bottom-right-radius: 0px;
        border-bottom-left-radius: 0px;
    }

    #gm-custom-mapdropdown {
        width: 190px;
        margin-left: 91px;
        border-bottom-right-radius: 8px;
        border-bottom-left-radius: 8px;
    }

    #gm-custom-mapoption-terrain, #gm-custom-mapoption-natatl, #gm-custom-mapoption-outdoors, #gm-custom-mapoption-streets, #gm-custom-mapoption-topo, #gm-custom-mapoption-satstreets, #gm-custom-mapoption-sat {
        width: 178px;
        margin-left: -2px;
        border-bottom: solid 1px #f2f2f2;
    }

    #gm-custom-mapoption-3d {
        width: 178px;
        margin-left: -2px;
        border-bottom: solid 1px #f2f2f2;
    }

    #gm-custom-mapoption-3d {
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
    }

    #gm-custom-mapoption-streets:hover {
        background-color: rgb(235, 235, 235) !important;
        color: rgb(0, 0, 0) !important;
    }

    #search-peaks-btn[disabled] {
        -webkit-text-fill-color: #fff;
        color: #fff;
    }

   @media screen and (max-width: 767px) and (min-width: 1px) {
       #content-body {
           margin-top: 3px;
       }
       #peaks-map {
           top: 0px;
       }
       #gm-custom-maptype {
           right: 46% !important;
       }
       .mapboxgl-ctrl-bottom-right {
            bottom: 20px;
        }
       .mapboxgl-ctrl-geolocate, .mapboxgl-ctrl-fullscreen {
           width: 36px !important;
           height: 36px !important;
       }
       #gm-custom-maplegend, #gm-custom-maplayers, #gm-custom-mapfilter, #gm-custom-map3d {
           width: 36px !important;
           height: 36px !important;
           padding-top: 3px;
           padding-left: 3px;
       }
   }
    @media screen and (min-width: 768px) {
       #content-body {
           margin-top: 3px;
       }
        #peak-search {
            top: 0px;
        }
        #gm-custom-maplegend-dropdown {
            width: 320px !important;
            left: 20px !important;
        }
    }
    @media screen and (min-width: 768px) and (max-width: 1023px) {
        #peaks-map {
            top: 0px;
        }
    }
    @media screen and (min-width: 1024px) {
        #peaks-map {
            top: 0px;
        }
        #gm-custom-mapunits {
            right: 172px;
        }
    }
    #content-holder {
        background-image: none;
    }

    #explore {
        padding-top: 0px;
    }

    .map-tooltip-info {
        background-color: rgba(0,0,0,.6);
        -webkit-backdrop-filter: blur(0px) !important;
    }

    .mapboxgl-ctrl-attrib {display: none !important;}

</style>

<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.8.1/jquery.min.js"></script>
{% include "mapbox/mapbox.html" %}
<script type="text/javascript">
$(document).ready(function(){
    $('.field-location_lat_lng').find('label').html('Location:');

    var change = 0;

    $('input, textarea').keypress(function(){
        change++;
    });

    $('select').change(function(){
        change++;
    });

    $('#approve_btn').bind("click", function(){
        if (change>0){
            if (confirm("You might have unsaved changes, are you sure you want to approve?")){
                location.href = "{% url "approve_item_correction" object_id %}";
            }
        }
        else {
            location.href = "{% url "approve_item_correction" object_id %}";
        }
    });

    $('#save_btn').bind("click", function(){
        $('input[name*="_continue"]').trigger('click');
    });

    {% if original.field == 6 %}
    $('.field-original_photo_image').find('.readonly').html('<img src="https://s3-us-west-1.amazonaws.com/peakery-media/{{ original.original_value }}" width="640">');
    $('.field-new_photo_image').find('.readonly').html('<img src="https://s3-us-west-1.amazonaws.com/peakery-media/{{ original.new_value }}" width="640">');
    {% endif %}

    {% if original.field == 2 or original.field == 11 %}
    initialize();
    {% else %}
    $('.field-location_lat_lng').hide();
    {% endif %}

    //Custom Google Map type stuff

    $('#gm-custom-mapbutton').on('mouseenter', function(){
       $('#gm-custom-mapdropdown').show();
       $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
       $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapbutton').on('mouseleave', function(){
       $('#gm-custom-mapdropdown').hide();
       $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    $('#map-loading').on('mouseenter', function(){
       $('#gm-custom-mapdropdown').show();
       $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
       $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#map-loading').on('mouseleave', function(){
       $('#gm-custom-mapdropdown').hide();
       $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    $('#gm-custom-mapbutton').on('touchstart', function() {
        $('#gm-custom-mapdropdown').toggle();
    });

    //new MapBox 2.0 3D stuff
    var flag = false;
    $('#gm-custom-mapoption-3d').on('touchstart click', function(e) {
        if (!flag) {
            flag = true;
            setTimeout(function(){ flag = false; }, 500);
            $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
            $('#gm-custom-mapoption-satstreets').css("color", "#333");
            $('#gm-custom-mapoption-topo').css("background-color", "#fff");
            $('#gm-custom-mapoption-topo').css("color", "#333");
            $('#gm-custom-mapoption-sat').css("background-color", "#fff");
            $('#gm-custom-mapoption-sat').css("color", "#333");
            $('#gm-custom-mapoption-3d').css("background-color", "#0ae");
            $('#gm-custom-mapoption-3d').css("color", "#fff");
            $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
            $('#gm-custom-mapoption-terrain').css("color", "#333");
            $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
            $('#gm-custom-mapoption-outdoors').css("color", "#333");
            $('#gm-custom-mapoption-streets').css("background-color", "#fff");
            $('#gm-custom-mapoption-streets').css("color", "#333");
            $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
            $('#gm-custom-mapoption-natatl').css("color", "#333");

            toggleMapType('satellite_3d');
            $('#gm-custom-mapdropdown').toggle();

            $('#search-peaks-btn').prop('disabled', false);
            $('#q').prop('disabled', false);
            $('#n').prop('disabled', false);
            $(".mapboxgl-canvas-container").show();
            $('.mapboxgl-ctrl-bottom-left').show();
            $('.mapboxgl-ctrl-bottom-right').show();

            $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">3D Map</div>');
        }
        return false
    });

    $('#gm-custom-mapoption-3d').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapoption-3d').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    var flag = false;
    $('#gm-custom-mapoption-satstreets').on('touchstart click', function(e) {
        if (!flag) {
            flag = true;
            setTimeout(function(){ flag = false; }, 500);
            $('#gm-custom-mapoption-satstreets').css("background-color", "#0ae");
            $('#gm-custom-mapoption-satstreets').css("color", "#fff");
            $('#gm-custom-mapoption-topo').css("background-color", "#fff");
            $('#gm-custom-mapoption-topo').css("color", "#333");
            $('#gm-custom-mapoption-sat').css("background-color", "#fff");
            $('#gm-custom-mapoption-sat').css("color", "#333");
            $('#gm-custom-mapoption-3d').css("background-color", "#fff");
            $('#gm-custom-mapoption-3d').css("color", "#333");
            $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
            $('#gm-custom-mapoption-terrain').css("color", "#333");
            $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
            $('#gm-custom-mapoption-outdoors').css("color", "#333");
            $('#gm-custom-mapoption-streets').css("background-color", "#fff");
            $('#gm-custom-mapoption-streets').css("color", "#333");
            $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
            $('#gm-custom-mapoption-natatl').css("color", "#333");

            toggleMapType('satellite');
            $('#gm-custom-mapdropdown').toggle();

            $('#search-peaks-btn').prop('disabled', false);
            $('#q').prop('disabled', false);
            $('#n').prop('disabled', false);
            $(".mapboxgl-canvas-container").show();
            $('.mapboxgl-ctrl-bottom-left').show();
            $('.mapboxgl-ctrl-bottom-right').show();

            $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite</div>');
        }
        return false
    });

    $('#gm-custom-mapoption-satstreets').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapoption-satstreets').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    var flag = false;
    $('#gm-custom-mapoption-topo').on('touchstart click', function() {
        if (!flag) {
            flag = true;
            setTimeout(function(){ flag = false; }, 500);
            $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
            $('#gm-custom-mapoption-satstreets').css("color", "#333");
            $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
            $('#gm-custom-mapoption-topo').css("color", "#fff");
            $('#gm-custom-mapoption-sat').css("background-color", "#fff");
            $('#gm-custom-mapoption-sat').css("color", "#333");
            $('#gm-custom-mapoption-3d').css("background-color", "#fff");
            $('#gm-custom-mapoption-3d').css("color", "#333");
            $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
            $('#gm-custom-mapoption-terrain').css("color", "#333");
            $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
            $('#gm-custom-mapoption-outdoors').css("color", "#333");
            $('#gm-custom-mapoption-streets').css("background-color", "#fff");
            $('#gm-custom-mapoption-streets').css("color", "#333");
            $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
            $('#gm-custom-mapoption-natatl').css("color", "#333");

            toggleMapType('caltopo');
            $('#gm-custom-mapdropdown').toggle();

            $('#search-peaks-btn').prop('disabled', false);
            $('#q').prop('disabled', false);
            $('#n').prop('disabled', false);
            $(".mapboxgl-canvas-container").show();
            $('.mapboxgl-ctrl-bottom-left').show();
            $('.mapboxgl-ctrl-bottom-right').show();

            $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
        }
        return false
    });

    $('#gm-custom-mapoption-topo').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapoption-topo').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    var flag = false;
    $('#gm-custom-mapoption-sat').on('touchstart click', function() {
        if (!flag) {
            flag = true;
            setTimeout(function(){ flag = false; }, 500);
            $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
            $('#gm-custom-mapoption-satstreets').css("color", "#333");
            $('#gm-custom-mapoption-topo').css("background-color", "#fff");
            $('#gm-custom-mapoption-topo').css("color", "#333");
            $('#gm-custom-mapoption-sat').css("background-color", "#0ae");
            $('#gm-custom-mapoption-sat').css("color", "#fff");
            $('#gm-custom-mapoption-3d').css("background-color", "#fff");
            $('#gm-custom-mapoption-3d').css("color", "#333");
            $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
            $('#gm-custom-mapoption-terrain').css("color", "#333");
            $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
            $('#gm-custom-mapoption-outdoors').css("color", "#333");
            $('#gm-custom-mapoption-streets').css("background-color", "#fff");
            $('#gm-custom-mapoption-streets').css("color", "#333");
            $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
            $('#gm-custom-mapoption-natatl').css("color", "#333");

            toggleMapType('sat_topo');
            $('#gm-custom-mapdropdown').toggle();

            $('#search-peaks-btn').prop('disabled', false);
            $('#q').prop('disabled', false);
            $('#n').prop('disabled', false);
            $(".mapboxgl-canvas-container").show();
            $('.mapboxgl-ctrl-bottom-left').show();
            $('.mapboxgl-ctrl-bottom-right').show();

            $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite Topo</div>');
        }
        return false
    });

    $('#gm-custom-mapoption-sat').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapoption-sat').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    var flag = false;
    $('#gm-custom-mapoption-terrain').on('touchstart click', function() {
        if (!flag) {
            flag = true;
            setTimeout(function(){ flag = false; }, 500);
            $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
            $('#gm-custom-mapoption-satstreets').css("color", "#333");
            $('#gm-custom-mapoption-topo').css("background-color", "#fff");
            $('#gm-custom-mapoption-topo').css("color", "#333");
            $('#gm-custom-mapoption-sat').css("background-color", "#fff");
            $('#gm-custom-mapoption-sat').css("color", "#333");
            $('#gm-custom-mapoption-3d').css("background-color", "#fff");
            $('#gm-custom-mapoption-3d').css("color", "#333");
            $('#gm-custom-mapoption-terrain').css("background-color", "#0ae");
            $('#gm-custom-mapoption-terrain').css("color", "#fff");
            $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
            $('#gm-custom-mapoption-outdoors').css("color", "#333");
            $('#gm-custom-mapoption-streets').css("background-color", "#fff");
            $('#gm-custom-mapoption-streets').css("color", "#333");
            $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
            $('#gm-custom-mapoption-natatl').css("color", "#333");

            toggleMapType('terrain');
            $('#gm-custom-mapdropdown').toggle();

            $('#search-peaks-btn').prop('disabled', false);
            $('#q').prop('disabled', false);
            $('#n').prop('disabled', false);
            $(".mapboxgl-canvas-container").show();
            $('.mapboxgl-ctrl-bottom-left').show();
            $('.mapboxgl-ctrl-bottom-right').show();

            $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Terrain</div>');
        }
        return false
    });

    $('#gm-custom-mapoption-terrain').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapoption-terrain').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    var flag = false;
    $('#gm-custom-mapoption-outdoors').on('touchstart click', function() {
        if (!flag) {
            flag = true;
            setTimeout(function(){ flag = false; }, 500);
            $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
            $('#gm-custom-mapoption-satstreets').css("color", "#333");
            $('#gm-custom-mapoption-topo').css("background-color", "#fff");
            $('#gm-custom-mapoption-topo').css("color", "#333");
            $('#gm-custom-mapoption-sat').css("background-color", "#fff");
            $('#gm-custom-mapoption-sat').css("color", "#333");
            $('#gm-custom-mapoption-3d').css("background-color", "#fff");
            $('#gm-custom-mapoption-3d').css("color", "#333");
            $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
            $('#gm-custom-mapoption-terrain').css("color", "#333");
            $('#gm-custom-mapoption-outdoors').css("background-color", "#0ae");
            $('#gm-custom-mapoption-outdoors').css("color", "#fff");
            $('#gm-custom-mapoption-streets').css("background-color", "#fff");
            $('#gm-custom-mapoption-streets').css("color", "#333");
            $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
            $('#gm-custom-mapoption-natatl').css("color", "#333");

            toggleMapType('outdoors');
            $('#gm-custom-mapdropdown').toggle();

            $('#search-peaks-btn').prop('disabled', false);
            $('#q').prop('disabled', false);
            $('#n').prop('disabled', false);
            $(".mapboxgl-canvas-container").show();
            $('.mapboxgl-ctrl-bottom-left').show();
            $('.mapboxgl-ctrl-bottom-right').show();

            $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">OpenTopoMap</div>');
        }
        return false
    });

    $('#gm-custom-mapoption-outdoors').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapoption-outdoors').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    var flag = false;
    $('#gm-custom-mapoption-streets').on('touchstart click', function() {
        if (!flag) {
            flag = true;
            setTimeout(function(){ flag = false; }, 500);
            $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
            $('#gm-custom-mapoption-satstreets').css("color", "#333");
            $('#gm-custom-mapoption-topo').css("background-color", "#fff");
            $('#gm-custom-mapoption-topo').css("color", "#333");
            $('#gm-custom-mapoption-sat').css("background-color", "#fff");
            $('#gm-custom-mapoption-sat').css("color", "#333");
            $('#gm-custom-mapoption-3d').css("background-color", "#fff");
            $('#gm-custom-mapoption-3d').css("color", "#333");
            $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
            $('#gm-custom-mapoption-terrain').css("color", "#333");
            $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
            $('#gm-custom-mapoption-outdoors').css("color", "#333");
            $('#gm-custom-mapoption-streets').css("background-color", "#0ae");
            $('#gm-custom-mapoption-streets').css("color", "#fff");
            $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
            $('#gm-custom-mapoption-natatl').css("color", "#333");

            toggleMapType('streets');
            $('#gm-custom-mapdropdown').toggle();

            $('#search-peaks-btn').prop('disabled', false);
            $('#q').prop('disabled', false);
            $('#n').prop('disabled', false);
            $(".mapboxgl-canvas-container").show();
            $('.mapboxgl-ctrl-bottom-left').show();
            $('.mapboxgl-ctrl-bottom-right').show();

            $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Streets</div>');
        }
        return false
    });

    $('#gm-custom-mapoption-streets').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapoption-streets').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    //Natural atlas stuff
    var flag = false;
    $('#gm-custom-mapoption-natatl').on('touchstart click', function() {
        if (!flag) {
            flag = true;
            setTimeout(function(){ flag = false; }, 500);
            $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
            $('#gm-custom-mapoption-satstreets').css("color", "#333");
            $('#gm-custom-mapoption-topo').css("background-color", "#fff");
            $('#gm-custom-mapoption-topo').css("color", "#333");
            $('#gm-custom-mapoption-sat').css("background-color", "#fff");
            $('#gm-custom-mapoption-sat').css("color", "#333");
            $('#gm-custom-mapoption-3d').css("background-color", "#fff");
            $('#gm-custom-mapoption-3d').css("color", "#333");
            $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
            $('#gm-custom-mapoption-terrain').css("color", "#333");
            $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
            $('#gm-custom-mapoption-outdoors').css("color", "#333");
            $('#gm-custom-mapoption-streets').css("background-color", "#fff");
            $('#gm-custom-mapoption-streets').css("color", "#333");
            $('#gm-custom-mapoption-natatl').css("background-color", "#0ae");
            $('#gm-custom-mapoption-natatl').css("color", "#fff");

            toggleMapType('natural_atlas');
            $('#gm-custom-mapdropdown').toggle();

            $('#search-peaks-btn').prop('disabled', false);
            $('#q').prop('disabled', false);
            $('#n').prop('disabled', false);
            $(".mapboxgl-canvas-container").show();
            $('.mapboxgl-ctrl-bottom-left').show();
            $('.mapboxgl-ctrl-bottom-right').show();

            $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Natural Atlas <span style="font-size: 10px;">(US)</span></div>');
        }
        return false
    });

    $('#gm-custom-mapoption-natatl').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapoption-natatl').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

});
</script>

{% if original.field == 2 or original.field == 1 or original.field == 11 %}
{% include "mapbox/mapbox.html" %}
<script type="text/javascript">

    function createCookie(name, value, days) {
        var expires;

        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = "; expires=" + date.toGMTString();
        } else {
            expires = "";
        }
        document.cookie = encodeURIComponent(name) + "=" + encodeURIComponent(value) + expires + "; path=/";
    }

    function readCookie(name) {
        var nameEQ = encodeURIComponent(name) + "=";
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return decodeURIComponent(c.substring(nameEQ.length, c.length));
        }
        return null;
    }

    function eraseCookie(name) {
        createCookie(name, "", -1);
    }

    function initialize() {

        var mapDiv = document.getElementById('map-canvas');
        {% if original.field == 2 %}
            var latLng = new mapboxgl.LngLat({{ original.location.x }}, {{ original.location.y }});
        {% else %}
            var latLng = new mapboxgl.LngLat({{ original.item.location.x }}, {{ original.item.location.y }});
        {% endif %}
        var LatLngList = [];

        {% if peak.is_usa_but_not_alaska %}
        var mapType = 'natural_atlas';
        {% else %}
        var mapType = 'outdoors';
        {% endif %}
        initMapType(mapType);

        var initZoom = 14;

        map = new mapboxgl.Map({
            container: mapDiv, // HTML container id
            style: 'mapbox://styles/peakery/cjjkkx6qa63mn2rthodesuu8m', // style URL
            center: latLng, // starting position as [lng, lat]
            zoom: initZoom,
            interactive: true
        });
        scale = new mapboxgl.ScaleControl({maxWidth: 120, unit: 'imperial'});
        map.addControl(scale, 'bottom-right');
        var nav = new mapboxgl.NavigationControl({showCompass: false});
        map.addControl(nav, 'bottom-right');

        map.on('load', function () {

            {% if original.field == 2 %}
            var latLng1 = new mapboxgl.LngLat({{ original.location.x }}, {{ original.location.y }});
            var marker = new mapboxgl.Marker({
                    draggable: false,
                    color: '#ff0000'
                })
                .setLngLat([{{ original.location.x }}, {{ original.location.y }}])
                .addTo(map);
            LatLngList.push(latLng1);
            {% endif %}

            var latLng2 = new mapboxgl.LngLat({{ original.item.location.x }}, {{ original.item.location.y }});
            var marker2 = new mapboxgl.Marker({
                    draggable: false,
                    color: '#0000ff'
                })
                .setLngLat([{{ original.item.location.x }}, {{ original.item.location.y }}])
                .addTo(map);
            LatLngList.push(latLng2);

            var bounds = new mapboxgl.LngLatBounds();
            for (var i = 0, LtLgLen = LatLngList.length; i < LtLgLen; i++) {
                bounds.extend(LatLngList[i]);
            }
            toggleMapUnits('feet');
            toggleMapType('terrain');

            $('#map-canvas').insertAfter($('#id_location_lat_lng'));
            $('#id_location_lat_lng').hide();
            $('#map-canvas').show();
            map.resize();
        });

    }

    function setMapControls() {
        var check = checkIfMapboxStyleIsLoaded();
        if (!check) {
            // It's not safe to manipulate layers yet, so wait 200ms and then check again
            setTimeout(function() {
                setMapControls();
            }, 200);
            return;
        }
        // Whew, now it's safe to manipulate layers!
        var mapUnits = readCookie('map_units');
        if (mapUnits == 'meters') {
            toggleMapUnits(mapUnits);
        }
    }

    function addExtraMapLayers() {
        var check = checkIfMapboxStyleIsLoaded();
        if (!check) {
            // It's not safe to manipulate layers yet, so wait 200ms and then check again
            setTimeout(function() {
                addExtraMapLayers();
            }, 200);
            return;
        }
        // Whew, now it's safe to manipulate layers!
        // No extra map layers necessary
    }

</script>
{% else %}
 <style type="text/css">
    .form-row.field-location {
        display: none;
     }
</style>
{% endif %}

{% if original.field == 6 %}
{% else %}
 <style type="text/css">
    .form-row.field-original_photo_image {
        display: none;
     }
    .form-row.field-new_photo_image {
        display: none;
     }
</style>
{% endif %}

<div id="map-canvas" style="display: none; width: 800px; height: 500px;">
    <div id="gm-custom-maptype" class="gmnoprint" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 180px; right: 50%; top: 0px;">
        <div id="map-loading" style="display: none; position: absolute; top: 4px; left: 94px; height: 46px; width: 172px; background-color: #fff; z-index: 2; text-align: center; font-size: 20px;"><i style="line-height: 46px;" class="fa fa-spinner fa-spin"></i></div>
        <div id="gm-custom-mapbutton" class="hidden-xs hidden-sm" draggable="false" style="cursor: pointer; direction: ltr; overflow: hidden; text-align: left; position: relative; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); height: 34px; padding: 8px; -webkit-background-clip: padding-box; background-clip: padding-box; font-weight: 500;">
            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Terrain</div>
        </div>
        <div id="gm-custom-mapdropdown" style="z-index: 10; padding-left: 2px; padding-right: 2px; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; position: absolute; top: 50px; left: 0px; right: 0px; text-align: left; display: none;">
            <div id="gm-custom-mapoption-terrain" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Terrain</div>
            </div>
            <div id="gm-custom-mapoption-natatl" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Natural Atlas <span style="font-size: 10px;">(US)</span></div>
            </div>
            <div id="gm-custom-mapoption-outdoors" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">OpenTopoMap</div>
            </div>
            <div id="gm-custom-mapoption-streets" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Streets</div>
            </div>
            <div id="gm-custom-mapoption-topo" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>
            </div>
            <div id="gm-custom-mapoption-satstreets" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite</div>
            </div>
            <div id="gm-custom-mapoption-sat" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite Topo</div>
            </div>
            <div class="" id="gm-custom-mapoption-3d" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">3D Map</div>
            </div>
        </div>
        <div id="gm-custom-maplegend-dropdown" style="z-index: 10; border: 2px solid rgba(0,0,0,0.15); position: absolute; width: 270px; top: 52px; left: 40px; right: 0px; text-align: left; border-radius: 8px; display: none;">
            <div id="gm-custom-maplegend-highest" draggable="false" style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500; border-top-left-radius: 8px; border-top-right-radius: 8px;">
                <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Highest peak on map</div>
            </div>
            <div id="gm-custom-maplegend-yoursummits" draggable="false" style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Climbed by you<div style="float: right; display: none;"><label class="switch"><input id="map-summits-toggle" type="checkbox"><span class="slider round"></span></label></div></div>
            </div>
            <div id="gm-custom-maplegend-yourattempts" draggable="false" style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Attempted by you<div style="float: right; display: none;"><label class="switch"><input id="map-attempts-toggle" type="checkbox"><span class="slider round"></span></label></div></div>
            </div>
            <div id="gm-custom-maplegend-unclimbed" draggable="false" style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Unclimbed by you<div style="float: right; display: none;"><label class="switch"><input id="map-unclimbed-toggle" type="checkbox"><span class="slider round"></span></label></div></div>
            </div>
            <div id="gm-custom-maplegend-yourkings" draggable="false" style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Your King of the Mountains<div style="float: right; {% if not request.user.is_authenticated %}display: none;{% endif %}"><label class="switch"><input id="map-kom-toggle" type="checkbox"><span class="slider round"></span></label></div></div>
            </div>
            <div id="gm-custom-maplegend-yourstewards" draggable="false" style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Your Summit Stewards<div style="float: right; {% if not request.user.is_authenticated %}display: none;{% endif %}"><label class="switch"><input id="map-stewards-toggle" type="checkbox"><span class="slider round"></span></label></div></div>
            </div>
            <div id="gm-custom-maplegend-yourfirstascents" draggable="false" style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Your First Ascents<div style="float: right; {% if not request.user.is_authenticated %}display: none;{% endif %}"><label class="switch"><input id="map-firstascents-toggle" type="checkbox"><span class="slider round"></span></label></div></div>
            </div>
            <div id="gm-custom-maplegend-classics" draggable="false" style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Classic peaks<div style="float: right;"><label class="switch"><input id="map-classics-toggle" type="checkbox"><span class="slider round"></span></label></div></div>
            </div>
            <div id="gm-custom-maplegend-challenges" draggable="false" style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Peak Challenges<div style="float: right;"><label class="switch"><input id="map-challenges-toggle" type="checkbox"><span class="slider round"></span></label></div></div>
            </div>
            <div id="gm-custom-maplegend-photos" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500; border-bottom-left-radius: 8px; border-bottom-right-radius: 8px;">
                <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Photos<div style="float: right;"><label class="switch"><input id="map-photos-toggle" type="checkbox"><span class="slider round"></span></label></div></div>
            </div>
        </div>
    </div>
    <div id="message_map_div" class="gmnoprint gm-style-mtc" style="opacity: 1; margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 400px; height: 100px; min-height: 100px; margin-left: -200px; left: 50%; bottom: 0px; display: none;">
        <div id="message_map" draggable="false" style="height: 100px; direction: ltr; overflow: hidden; text-align: left; position: relative; color: rgb(86, 86, 86); font-family: Roboto, Arial, sans-serif; font-size: 11px; background-color: transparent; padding: 11px; border-radius: 8px; font-weight: 500;">

        </div>
    </div>
</div>
<div id="marker-tooltip" data-url="" data-index="" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; cursor: pointer; opacity: 0; position:absolute; width: 200px; height: 150px; background: rgba(0, 0, 0, 0.3); margin: 15px;"></div>

{% endblock %}

{% block object-tools %}
    {% if change %}{% if not is_popup %}
        <ul class="object-tools">
{#            <li><a href="{% url "approve_item_correction" object_id %}">Approve</a></li>#}
            <li><a id='save_btn' href="#">Save changes</a></li>
            {% if original.field == 6 %}
            <li><a id='approve_btn' href="#">Add & Feature</a></li>
            <li><a href="{% url "item_correction_add_photo_only" object_id %}">Only Add</a></li>
            {% else %}
            <li><a id='approve_btn' href="#">Approve</a></li>
            {% endif %}
            <li><a href="{% url "reject_item_correction" object_id %}">Reject</a></li>
            <li style="background: none;"><span style="margin: 0px 15px 0px 10px;color:#ccc">|</span></li>
            <li><a target='_blank' href="{% url "admin_item_correction" object_id %}">go to admin peak</a></li>
            <li><a target='_blank' href="{% url "view_item_correction" object_id %}">go to peak page</a></li>
            <li><a href="history/" class="historylink">History</a></li>

        </ul>
        {% if original.systemMessages %}
            <ul class="messagelist">
                <li class="error">{{ original.systemMessages}}</li>
            </ul>
        {% endif %}
    {% endif %}{% endif %}
{% endblock %}

