{% load i18n %}
<div class="inline-group" id="{{ inline_admin_formset.formset.prefix }}-group">
  <div class="tabular inline-related {% if forloop.last %}last-related{% endif %}">
{{ inline_admin_formset.formset.management_form }}
<fieldset class="module">
   <h2>{{ inline_admin_formset.opts.verbose_name_plural|capfirst }}</h2>
   {{ inline_admin_formset.formset.non_form_errors }}
   <table>
     <thead><tr>
     {% for field in inline_admin_formset.fields %}
       {% if not field.widget.is_hidden %}
         <th{% if forloop.first %} colspan="2"{% endif %}{% if field.required %} class="required"{% endif %}>{{ field.label|capfirst }}</th>
       {% endif %}
     {% endfor %}
     {% if inline_admin_formset.formset.can_delete %}<th>{% trans "Delete?" %}</th>{% endif %}
     </tr></thead>

     <tbody>
     {% for inline_admin_form in inline_admin_formset %}
        {% if inline_admin_form.form.non_field_errors %}
        <tr><td colspan="{{ inline_admin_form.field_count }}">{{ inline_admin_form.form.non_field_errors }}</td></tr>
        {% endif %}
        <tr class="{% cycle "row1" "row2" %} {% if inline_admin_form.original or inline_admin_form.show_url %}has_original{% endif %}{% if forloop.last %} empty-form{% endif %}"
             id="{{ inline_admin_formset.formset.prefix }}-{% if not forloop.last %}{{ forloop.counter0 }}{% else %}empty{% endif %}">
        <td class="original">
          {% if inline_admin_form.original or inline_admin_form.show_url %}<p>
          {% if inline_admin_form.original %} {{ inline_admin_form.original }}{% endif %}
          {% if inline_admin_form.show_url %}<a href="../../../r/{{ inline_admin_form.original_content_type_id }}/{{ inline_admin_form.original.id }}/">{% trans "View on site" %}</a>{% endif %}
            </p>{% endif %}
          {% if inline_admin_form.has_auto_field %}{{ inline_admin_form.pk_field.field }}{% endif %}
          {{ inline_admin_form.fk_field.field }}
          {% spaceless %}
          {% for fieldset in inline_admin_form %}
            {% for line in fieldset %}
              {% for field in line %}
                {% if field.is_hidden %} {{ field.field }} {% endif %}
              {% endfor %}
            {% endfor %}
          {% endfor %}
          {% endspaceless %}
        </td>
        {% for fieldset in inline_admin_form %}
          {% for line in fieldset %}
            {% for field in line %}
              <td class="{{ field.field.name }}">
              {% if field.is_readonly %}
                  <p>{{ field.contents }}</p>
              {% else %}
                  {{ field.field.errors.as_ul }}
                  {{ field.field }}
              {% endif %}
              </td>
            {% endfor %}
          {% endfor %}
        {% endfor %}
        {% if inline_admin_formset.formset.can_delete %}
          <td class="delete">{% if inline_admin_form.original %}{{ inline_admin_form.deletion_field.field }}{% endif %}</td>
        {% endif %}
        </tr>
     {% endfor %}
     </tbody>
   </table>
</fieldset>
  </div>
</div>

<script type="text/javascript">
(function($) {
    $(document).ready(function($) {
        var rows = "#{{ inline_admin_formset.formset.prefix }}-group .tabular.inline-related tbody tr";
        var alternatingRows = function(row) {
            $(rows).not(".add-row").removeClass("row1 row2")
                .filter(":even").addClass("row1").end()
                .filter(rows + ":odd").addClass("row2");
        }
        var reinitDateTimeShortCuts = function() {
            // Reinitialize the calendar and clock widgets by force
            if (typeof DateTimeShortcuts != "undefined") {
                $(".datetimeshortcuts").remove();
                DateTimeShortcuts.init();
            }
        }
        var updateSelectFilter = function() {
            // If any SelectFilter widgets are a part of the new form,
            // instantiate a new SelectFilter instance for it.
            if (typeof SelectFilter != "undefined"){
                $(".selectfilter").each(function(index, value){
                  var namearr = value.name.split('-');
                  SelectFilter.init(value.id, namearr[namearr.length-1], false, "{{ STATIC_URL }}");
                })
                $(".selectfilterstacked").each(function(index, value){
                  var namearr = value.name.split('-');
                  SelectFilter.init(value.id, namearr[namearr.length-1], true, "{{ STATIC_URL }}");
                })
            }
        }
        var initPrepopulatedFields = function(row) {
            row.find('.prepopulated_field').each(function() {
                var field = $(this);
                var input = field.find('input, select, textarea');
                var dependency_list = input.data('dependency_list') || [];
                var dependencies = row.find(dependency_list.join(',')).find('input, select, textarea');
                if (dependencies.length) {
                    input.prepopulate(dependencies, input.attr('maxlength'));
                }
            });
        }
        var initMap = function(row) {
            var id = row.attr('id');
            var number = id.substr(id.lastIndexOf('-') + 1);
            var emptyRow = row.parent().children('.empty-form');
            var map_tds = [];
            row.find('.olMap').each(function() {
            	map_tds.push($(this).closest('td'));
            });
            var empty_map_tds = [];
            emptyRow.find('.olMap').each(function() {
                empty_map_tds.push($(this).closest('td'));
            });
            row.find('.olMapViewport').each(function() {
                $(this).remove();
            });
            var num_maps = map_tds.length;
            for(var i = 0; i < num_maps; i++) {
                var map_td = map_tds[i];
                var empty_map_td = empty_map_tds[i];
                map_td.children('span').children('a').each(function() {
                    $(this).attr('href', $(this).attr('href').replace('__prefix__', number));
                });
                map_td.children('style').each(function() {
                    var styles = this.innerHTML.replace(/__prefix__/g, number).replace(/\n\s*/g,'').split('}');
                    var length = styles.length;
                    for(var i = 0; i < length; i++) {
                        var styletext = styles[i];
                        if(styletext) {
                            $.rule(styletext + '}').appendTo('link');
                        }
                    }
                }).remove();
                empty_map_td.find('script').each(function() {
                    var script = document.createElement("script");
                    script.type = "text/javascript";
                    script.text = this.innerHTML.replace(/__prefix__/g, number);
                    $("head").append(script);
                });
            }
        }
        $(rows).formset({
            prefix: "{{ inline_admin_formset.formset.prefix }}",
            addText: "{% blocktrans with inline_admin_formset.opts.verbose_name|title as verbose_name %}Add another {{ verbose_name }}{% endblocktrans %}",
            formCssClass: "dynamic-{{ inline_admin_formset.formset.prefix }}",
            deleteCssClass: "inline-deletelink",
            deleteText: "{% trans "Remove" %}",
            emptyCssClass: "empty-form",
            removed: alternatingRows,
            added: (function(row) {
                initPrepopulatedFields(row);
                reinitDateTimeShortCuts();
                updateSelectFilter();
                alternatingRows(row);
                initMap(row);
            })
        });
    });
})(django.jQuery);
</script>
