<script type="text/javascript">

  var map = null;
  var center = null;
  var latLng = null;

  function initialize() {
    var p = {};
    p['x'] = {{peak.long}};
    p['y'] = {{peak.lat}};

    var topo = new google.maps.ImageMapType({getTileUrl: function(p,z) { return "https://s3-us-west-1.amazonaws.com/caltopo/topo/" + z + "/" + p.x + "/" + p.y + ".png" }, maxZoom: 16, name: "Topo (US & Canada)", opacity: 1, tileSize: new google.maps.Size(256,256)});
    var outdoors = new google.maps.ImageMapType({getTileUrl: function(p,z) { return "https://a.tile.thunderforest.com/outdoors/" + z + "/" + p.x + "/" + p.y + ".png?apikey=7a86fe002ce040d99784325cf93a1318" }, maxZoom: 22, name: "Outdoors", opacity: 1, tileSize: new google.maps.Size(256,256)});

    var marker_icon = '{{ MEDIA_URL }}img/marker.png';
    var mapDiv = document.getElementById('peak_map_canvas');
    latLng = new google.maps.LatLng({{ peak.lat }}, {{ peak.long }});
    map = new google.maps.Map(mapDiv, {
      center: latLng,
      zoom: 14,
      mapTypeId: google.maps.MapTypeId.TERRAIN,
      mapTypeControl: false,
      streetViewControl: false,
      scrollwheel: false,
      draggable: false,
      panControl: false,
      zoomControl: false,
      navigationControl: false
    });
    map.mapTypes.set('Topo', topo);
    map.mapTypes.set('Outdoors', outdoors);

    var myLatLng = new google.maps.LatLng({{ peak.lat }}, {{ peak.long }});

    var marker = new google.maps.Marker({
      position: latLng,      map: map, title:"{{ peak.name }}",   icon: marker_icon
    });

    function calculateCenter() {
      center = map.getCenter();
    }
    google.maps.event.addDomListener(map, 'idle', function() {
      calculateCenter();
    });
    google.maps.event.addDomListener(window, 'resize', function() {
      map.setCenter(center);
    });

  }

  function set_route_gpx(gpx_url) {
    $.ajax({
        type: "GET",
        url: gpx_url,
        dataType: "xml",
        success: function(xml) {
        bounds = new google.maps.LatLngBounds ();
        $(xml).find("trkpt").each(function() {
            var lat = $(this).attr("lat");
            var lon = $(this).attr("lon");
            var p = new google.maps.LatLng(lat, lon);
            points.push(p);
            bounds.extend(p);
        });

        poly = new google.maps.Polyline({
            // use your own style here
            path: points,
            strokeColor: "#fc202e",
            strokeOpacity: .7,
            strokeWeight: 4
        });

        poly.setMap(map);

        // fit bounds to track
        map.fitBounds(bounds);
        }
    });
  }

  function gmapsCallback() {
      var init_height = $("#peak-photo-col").height();
      var init_map_width = $("#peak-map-col").width();
      $("#peak_map_container").height(init_height);
      initialize();
  }
</script>

<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<script type="text/javascript" src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAjV7dkmG1WYHeHE0EnQvOV6YnPWpRxIzg&libraries=geometry&loading=async&callback=gmapsCallback"></script>
