{% load static %}
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" xml:lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>

    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="google" content="notranslate">
    <meta http-equiv="Content-Language" content="en">

    <meta name="viewport" content="viewport-fit=cover, user-scalable=no, width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#333333">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name=“apple-mobile-web-app-title” content=“peakery”>

    <meta name="title" content="{% block titlemeta_overwrite %}{% block titlemeta %}{% endblock %}{% endblock %} | peakery" />
    <meta name="description" content="{% block description %}Your basecamp for the world’s mountains. Find great peaks to climb, tackle Peak Challenges, track your summits, and follow the mountain adventures of friends.{% endblock %}" />
    <link rel="image_src" href="{% block image_rel %}{% endblock %}">
    <link rel="shortcut icon" href="{{MEDIA_URL}}img/icn/favicon.ico" />

    <link rel="apple-touch-icon" sizes="180x180" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-icon" sizes="120x120" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-icon" sizes="167x167" href="{% static 'img/apple-touch-icon-83,5x83,<EMAIL>' %}">
    <link rel="apple-touch-icon" sizes="152x152" href="{% static 'img/<EMAIL>' %}">

    <link rel="apple-touch-startup-image" media="(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 414px) and (device-height: 736px) and (-webkit-device-pixel-ratio: 3)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3)" href="{% static 'img/<EMAIL>' %}">

    <link rel="apple-touch-startup-image" media="(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 2)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 834px) and (device-height: 1112px) and (-webkit-device-pixel-ratio: 2)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 834px) and (device-height: 1112px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" href="{% static 'img/<EMAIL>' %}">

    <link rel="manifest" href="{% static 'json/manifest.json' %}">
    {% block meta_facebook %}{% endblock %}
    {% block meta_google %}{% endblock %}
    {% block meta_twitter %}{% endblock %}
    <title>{% block title %}{% endblock %} | peakery</title>
    {% block mapbox %}{% endblock %}
    <script type="text/javascript">{% block js_globals %}{% endblock %}</script>
    {% if not debug %}
        {% include "analytics/ga.html" %}
    {% endif %}

        <link rel="stylesheet" href="{% static 'css/0f2533e978d5-2.01.css' %}" type="text/css" media="screen" />
        <link href="{% static 'css/explore.css' %}" rel="stylesheet" type="text/css" media="screen" />
        <link href="{% static 'css/bootstrap.css' %}" rel="stylesheet" type="text/css" media="screen" />
        <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.3.1/css/all.css" integrity="sha384-mzrmE5qonljUremFsqc01SB46JvROS7bZs3IO2EmfFsd15uHvIt+Y8vEf7N7fWAU" crossorigin="anonymous">
        <link href="{% static 'css/bootstrap-toggle.css' %}" rel="stylesheet">
        <link rel="stylesheet" href="{% static 'css/blueimp-gallery.css' %}">
        <link rel="stylesheet" href="{% static 'css/bootstrap-image-gallery.css' %}">
        <link rel="stylesheet" href="{% static 'css/nouislider.css' %}">
        <link rel="stylesheet" href="{% static 'css/drawer.css' %}">
        <link rel="stylesheet" href="{% static 'css/formValidation/formValidation.css' %}">
        <link rel="stylesheet" href="{% static 'css/jquery-ui.css' %}">

        <link rel="stylesheet" type="text/css" href="{% static 'vendor/ModalWindowEffects/css/default.css' %}" />
        <link rel="stylesheet" type="text/css" href="{% static 'vendor/ModalWindowEffects/css/component.css' %}" />

        <link href="{% static 'vendor/s3.fine-uploader/fine-uploader-new.css' %}" rel="stylesheet">

        <link href="{% static 'css/peakery-custom-3.09.css' %}" rel="stylesheet" type="text/css" media="screen" />
        <link href="{% static '' %}css/peakery-custom-3.10-base-extras.css" rel="stylesheet" type="text/css" media="screen" />
        <link href="{% static '' %}css/peakery-custom-3.08-base-nofooter-extras.css" rel="stylesheet" type="text/css" media="screen" />

    {% block css_for_includes %}{% endblock %}
        <script src="{% static 'js/jquery.js' %}"></script>
        <script src="{% static 'js/jquery-ui.js' %}"></script>
        <script src="{% static 'js/jquery.browser.js' %}"></script>
        <script src="{% static 'js/jquery.autocomplete.js' %}"></script>
        <script type="text/javascript" src="{% static 'js/bootstrap.js' %}"></script>
        <script src="{% static 'js/jquery.timeago.js' %}"></script>
        <script src="{% static 'js/bootstrap-toggle.js' %}"></script>
        <script src="{% static 'js/blueimp-gallery.js' %}"></script>
        <script src="{% static 'js/jquery.blueimp-gallery.js' %}"></script>
        <script src="{% static 'js/bootstrap-image-gallery.js' %}"></script>
        <script src="{% static 'js/jquery-dateFormat.js' %}"></script>
        <script src="{% static 'js/trunk8.js' %}"></script>
        <script src="{% static 'js/truncate.js' %}"></script>
        <script src="{% static 'js/nouislider.js' %}"></script>
        <script src="{% static 'js/autosize.js' %}"></script>
        <script src="{% static 'js/formValidation/formValidation.js' %}"></script>
        <script src="{% static 'js/formValidation/framework/bootstrap.js' %}"></script>
        <script src="{% static 'js/drawer.js' %}"></script>
        <script src="{% static 'js/jquery.floatThead.js' %}"></script>

        <script src="{% static 'vendor/ModalWindowEffects/js/modalEffects.js' %}"></script>
        <script src="{% static 'vendor/ModalWindowEffects/js/modernizr.custom.js' %}"></script>
        <script src="{% static 'vendor/ModalWindowEffects/js/classie.js' %}"></script>
        <script src="{% static 'vendor/ModalWindowEffects/js/cssParser.js' %}"></script>

        <script type="text/javascript" src="{% static 'js/jquery.form.js' %}"></script>
        <script type="text/javascript" src="{% static 'js/facebox.js' %}"></script>
        <script type="text/javascript" src="{% static 'js/peakery.js' %}"></script>
        <script type="text/javascript" src="{% static 'js/jquery.popup.js' %}"></script>
        <script type="text/javascript" charset="utf-8">
            $(document).ready(function() {
                {% include "main/ssi/ssi.js" %}
            });
        </script>
        <script type="text/javascript" src="{% static 'js/jquery.placeholder.js' %}"></script>
        <script type="text/javascript">
            $(function(){
                $('input, textarea').placeholder();
            });
        </script>
        <script type="text/javascript" src="{% static 'js/jquery.elastic.source.js' %}"></script>
        <script type="text/javascript" src="{% static 'js/jquery.OnEnter.js' %}"></script>
        <script type="text/javascript" src="{% static 'js/jquery.scrollTo-1.4.2.js' %}"></script>

        <script type="text/javascript" src="{% static '' %}js/peakery-shasta-3.07.js"></script>

    {% block extrajs %}{% endblock %}
    {% block js_for_includes %}{% endblock %}

    <style type="text/css">
        @media screen and (max-width: 767px) and (min-width: 1px) {
            body {
                letter-spacing: 0px !important;
            }
        }
        .modal-dialog {
            width: 100%;
            height: 100%;
            margin: 0;
            padding: 0;
        }

        .modal-content {
            height: auto;
            min-height: 100%;
            border-radius: 0;
            background-color: #f6f6f6;
        }
        .modal-close {
            display: none;
        }
        .modal-header {
            background-color: #333;
        }
    </style>

</head>

<body class="drawer drawer--right">

<div id="darkness" style="z-index: 999999; background: rgba(0, 0, 0, 0.8);"></div>

<script>
    $(function(){
        $('.join-peakery').on('click', function(e) {
            $('#accounts-sign-up .modal-body').load('/accounts/sign-up-content',function(e) {
                $('#accounts-sign-up-title').html('Join peakery');
                $('#accounts-sign-up').modal('show');
            });
        });
        $('#accounts-sign-up').on('click', '.sign-up-with-email', function(e) {
            $('#accounts-sign-up .modal-body').load('/accounts/register/?next=/accounts/sign-up-choose-name/1/',function(e) {
                $('#accounts-sign-up-title').html('<i class="fas fa-envelope fa-2x" aria-hidden="true"></i><span style="padding-left: 10px; top: -4px; position: relative;">Sign up with email</span>');
                $('#accounts-sign-up').modal('show');
            });
        });
        $('#forgot-password').on('click', function(e) {
            $('#accounts-login').modal('hide');
            $('#accounts-forgot-password').modal('show');
        });
        $('#remember-password').on('click', function(e) {
            $('#accounts-forgot-password').modal('hide');
            $('#accounts-login').modal('show');
        });
        $('#not-a-member').on('click', function(e) {
            $('#accounts-login').modal('hide');
            $('#accounts-sign-up .modal-body').load('/accounts/register/?next=/accounts/sign-up-choose-name/1/',function(e) {
                $('#accounts-sign-up-title').html('<i class="fas fa-envelope fa-2x" aria-hidden="true"></i><span style="padding-left: 10px; top: -4px; position: relative;">Sign up with email</span>');
                {% if IS_MOBILE_APP_ACCESS == 'True' %}
                $('#accounts-sign-up .modal-body').find('#is_android').val('true');
                {% endif %}
                $('#accounts-sign-up').modal('show');
            });
        });
        $('#accounts-sign-up').on('click', '#already-a-member', function(e) {
            $('#accounts-sign-up').modal('hide');
            $('#accounts-login').modal('show');
        });
        $('#reset-my-password').on('click', function(e) {
            e.preventDefault();
            var post_url = '/accounts/password_reset/';
            $.ajax({
                type: "POST",
                url: post_url,
                data: $('#reset-password-form').serialize()
            }).done(function (html) {
                if (html == 'success') {
                    //console.log('success');
                    $('#accounts-forgot-password-title').html('Forgot password email sent');
                    $('#reset-password-form').hide();
                    $('#password-reset-email').html($('#id_email').val());
                    $('#password-reset-success').fadeIn();

                } else {
                    $("#password_reset_error").html(html);
                    $("#password_reset_error").fadeIn();
                }
            });
        });
        $('#accounts-forgot-password').on('show.bs.modal', function() {
            $('#password-reset-success').hide();
            $('#password_reset_error').html('');
            $('#password_reset_error').hide();
            $('#reset-password-form').show();
        });
        $('#accounts-forgot-password').on('shown.bs.modal', function() {
            $('#id_email').val('');
            $('#id_email').parent().children('.holder').show();
            $('#id_email').focus();
        });
    });
</script>

{% block body_form %}{% endblock %}

<main id="main-content" role="main">

<div class="md-overlay"></div>

<div id="content-holder" style="background-color:#333;">

<!-- Begin page content -->
<div id="content-body" class="{{ fixed_subnav_class }}">

    <div id="main">
        {% block content %}
        {% endblock %}
    </div>

    {% block footer %}{% endblock %}

</div> <!-- /content-body -->

</div> <!-- /content-holder -->

<div class="message-modal modal fade" id="message-modal" tabindex="-1" role="dialog" aria-labelledby="message-modal-label">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
              <h4 class="modal-title" id="message-modal-label"></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div id="message-modal-body" class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="margin-bottom: 5px; font-size: 16px;"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<div style="padding-left: 0px; z-index: 99999;" class="mobile-alerts-modal modal fade" id="mobile-alerts-modal" tabindex="-1" role="dialog" aria-labelledby="mobile-alerts-modal-label">
    <div class="modal-dialog" style="width: 100%; height: 100%; margin-top: 0px;" role="document">
        <div class="modal-content" style="height: 100%;">
            <div class="modal-header">
              <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
              <h4 class="modal-title" id="mobile-alerts-modal-label"><span id="mobile-alerts-modal-title">Notifications</span></h4>
            </div>
            <div id="mobile-alerts-modal-body" class="modal-body" style="height: 100%; padding: 0px;">
                <div id="mobile-alerts-container" style="overflow-y: scroll; height: 580px; width: 100%;">
                    <ul id="mobile-alerts-ul" class="dropdown-menu nav-alerts-dropdown" style="display: block; border-radius: 0px; position: relative; top: 0px; width: 100%; margin-top: 0px;"></ul>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="accounts-sign-up-modal modal fade" id="accounts-sign-up" tabindex="-1" role="dialog" aria-labelledby="accounts-sign-up-label">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
              <h4 class="modal-title" id="accounts-sign-up-label"><span id="accounts-sign-up-title">Join peakery</span></h4>
            </div>
            <div class="modal-body"></div>
        </div>
    </div>
</div>

<div class="accounts-choose-username-modal modal fade" id="accounts-choose-username" tabindex="-1" role="dialog" aria-labelledby="accounts-choose-username-label">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
              <h4 class="modal-title" id="accounts-choose-username-label"><span id="accounts-choose-username-title">Choose your peakery username</span></h4>
            </div>
            <div class="modal-body"></div>
        </div>
    </div>
</div>

<div class="accounts-login-modal modal fade" id="accounts-login" tabindex="-1" role="dialog" aria-labelledby="accounts-login-label">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
              <h4 class="modal-title" id="accounts-login-label"><span id="accounts-login-title">Login</span></h4>
            </div>
            <div class="modal-body">
                <div id="ajax_account_output" style="display: none"></div>
                <div style="display: none; margin-bottom: 15px;" id="login_error">Incorrect password.<br>Try again.</div>
                <form id="signin" method="post" action="/accounts/login-user-lite/" style="text-align: center;">
                    {% csrf_token %}
                    <input class="form-control nav-login-input" placeholder="username or email" type="text" autocapitalize="none" id="username" name="username" style="border: 1px solid #ccc; height: 50px; color: #333;">
                    <input class="form-control nav-login-input" placeholder="password" type="password" id="password" name="password" style="border: 1px solid #ccc; height: 50px; color: #333;">
                    <button id="signin-submit" type="submit" class="btn btn-secondary nav-login-submit" style="font-size: 14px; margin-left: 10px; line-height: 20px;">Login</button>
                    <div style="text-align: center;height: 50px;line-height: 60px;">
                        <a id="forgot-password" style="cursor: pointer; color: #999;">Forgot password</a>
                    </div>
                    <div style="text-align: center;height: 50px;line-height: 60px;">
                        Not a member yet? <a style="font-weight: 500; cursor: pointer;" id="not-a-member">Sign up</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="accounts-forgot-password-modal modal fade" id="accounts-forgot-password" tabindex="-1" role="dialog" aria-labelledby="accounts-forgot-password-label">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
              <h4 class="modal-title" id="accounts-forgot-password-label"><span id="accounts-forgot-password-title">Forgot password</span></h4>
            </div>
            <div class="modal-body">
                <div class="signUpWithEmail">
                    <script type="text/javascript">
                        $(function() {
                            $('#id_email').on('input', function(e) {
                                if ($(this).val() != "") {
                                    $(this).parent().children('.holder').hide();
                                } else {
                                    $(this).parent().children('.holder').show();
                                }
                            });
                        });
                    </script>
                    <form id="reset-password-form" action="" method="post">
                        <div style="display: none;" id="password_reset_error"></div>
                        <div style="margin-bottom: 20px;">Enter your email and we’ll send password reset instructions:</div>
                        <span class="a">
                            <input class="email" id="id_email" maxlength="100" name="email" type="text" autocapitalize="none">
                            <span class="holder" style="">Email address</span>
                        </span>
                        <div style="text-align: center;height: 50px;line-height: 60px; margin-top: 25px; margin-bottom: 15px;">
                            {% csrf_token %}
                            <input id="reset-my-password" type="submit" value="Reset my password" class="btn set2 input" style="font-size: 14px; height: 50px;">
                        </div>
                    </form>
                </div>
                <div id="password-reset-success" style="display: none;">
                    We just sent an email to <span id="password-reset-email"></span> with password reset instructions.
                </div>
            </div>
        </div>
    </div>
</div>

{% block gallery %}
{% endblock %}

</main>

{% block end_body_form %}{% endblock %}

</body>
</html>