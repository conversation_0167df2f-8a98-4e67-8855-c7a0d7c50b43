{% load static %}
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en" xml:lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>

    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="google" content="notranslate">
    <meta http-equiv="Content-Language" content="en">

    <meta name="viewport" content="viewport-fit=cover, user-scalable=no, width=device-width, initial-scale=1, maximum-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#333333">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <meta name=“apple-mobile-web-app-title” content=“peakery”>

    <meta name="title" content="{% block titlemeta_overwrite %}{% block titlemeta %}{% endblock %}{% endblock %} | peakery" />
    <meta name="description" content="{% block description %}Your basecamp for the world’s mountains. Find great peaks to climb, tackle Peak Challenges, track your summits, and follow the mountain adventures of friends.{% endblock %}" />
    <link rel="image_src" href="{% block image_rel %}{% endblock %}">
    <link rel="shortcut icon" href="{{MEDIA_URL}}img/icn/favicon.ico" />

    <link rel="apple-touch-icon" sizes="180x180" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-icon" sizes="120x120" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-icon" sizes="167x167" href="{% static 'img/apple-touch-icon-83,5x83,<EMAIL>' %}">
    <link rel="apple-touch-icon" sizes="152x152" href="{% static 'img/<EMAIL>' %}">

    <link rel="apple-touch-startup-image" media="(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 414px) and (device-height: 736px) and (-webkit-device-pixel-ratio: 3)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3)" href="{% static 'img/<EMAIL>' %}">

    <link rel="apple-touch-startup-image" media="(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 2)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 834px) and (device-height: 1112px) and (-webkit-device-pixel-ratio: 2)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 1024px) and (device-height: 1366px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 834px) and (device-height: 1194px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 834px) and (device-height: 1112px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" href="{% static 'img/<EMAIL>' %}">
    <link rel="apple-touch-startup-image" media="(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2) and (orientation: landscape)" href="{% static 'img/<EMAIL>' %}">

    <link rel="manifest" href="{% static 'json/manifest.json' %}">
    {% block meta_facebook %}{% endblock %}
    {% block meta_google %}{% endblock %}
    {% block meta_twitter %}{% endblock %}
    <title>{% block title %}{% endblock %} | peakery</title>
    {% block mapbox %}{% endblock %}
    <script type="text/javascript">{% block js_globals %}{% endblock %}</script>

    {% if not debug %}
        {% include "analytics/ga.html" %}
    {% endif %}

        <link rel="stylesheet" href="{% static 'css/0f2533e978d5-2.01.css' %}" type="text/css" media="screen" />
        <link href="{% static 'css/explore.css' %}" rel="stylesheet" type="text/css" media="screen" />
        <link href="{% static 'css/bootstrap.css' %}" rel="stylesheet" type="text/css" media="screen" />
        <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.3.1/css/all.css" integrity="sha384-mzrmE5qonljUremFsqc01SB46JvROS7bZs3IO2EmfFsd15uHvIt+Y8vEf7N7fWAU" crossorigin="anonymous">
        <link href="{% static 'css/bootstrap-toggle.css' %}" rel="stylesheet">
        <link rel="stylesheet" href="{% static 'css/blueimp-gallery.css' %}">
        <link rel="stylesheet" href="{% static 'css/bootstrap-image-gallery.css' %}">
        <link rel="stylesheet" href="{% static 'css/nouislider.css' %}">
        <link rel="stylesheet" href="{% static 'css/drawer.css' %}">
        <link rel="stylesheet" href="{% static 'css/formValidation/formValidation.css' %}">
        <link rel="stylesheet" href="{% static 'css/jquery-ui.css' %}">
        <link rel="stylesheet" type="text/css" href="{% static 'vendor/ModalWindowEffects/css/default.css' %}" />
        <link rel="stylesheet" type="text/css" href="{% static 'vendor/ModalWindowEffects/css/component.css' %}" />
        <link href="{% static 'vendor/s3.fine-uploader/fine-uploader-new.css' %}" rel="stylesheet">
        <link href="{% static 'css/peakery-custom-3.09.css' %}" rel="stylesheet" type="text/css" media="screen" />
        <link href="{% static 'css/peakery-custom-3.08-base-noheaderfooter-extras.css' %}" rel="stylesheet" type="text/css" media="screen" />

        <style type="text/css">
            @media screen and (max-width: 767px) and (min-width: 1px) {
                body {
                    letter-spacing: 0px !important;
                }
            }
            .container, .main-header-row {
                max-width: inherit;
            }

        </style>

    {% block css_for_includes %}{% endblock %}
        <script src="{% static 'js/jquery.js' %}"></script>
        <script src="{% static 'js/jquery-ui.js' %}"></script>
        <script src="{% static 'js/jquery.browser.js' %}"></script>
        <script src="{% static 'js/jquery.autocomplete.js' %}"></script>
        <script type="text/javascript" src="{% static 'js/bootstrap.js' %}"></script>
        <script src="{% static 'js/jquery.timeago.js' %}"></script>
        <script src="{% static 'js/bootstrap-toggle.js' %}"></script>
        <script src="{% static 'js/blueimp-gallery.js' %}"></script>
        <script src="{% static 'js/jquery.blueimp-gallery.js' %}"></script>
        <script src="{% static 'js/bootstrap-image-gallery.js' %}"></script>
        <script src="{% static 'js/jquery-dateFormat.js' %}"></script>
        <script src="{% static 'js/trunk8.js' %}"></script>
        <script src="{% static 'js/truncate.js' %}"></script>
        <script src="{% static 'js/nouislider.js' %}"></script>
        <script src="{% static 'js/autosize.js' %}"></script>
        <script src="{% static 'js/formValidation/formValidation.js' %}"></script>
        <script src="{% static 'js/formValidation/framework/bootstrap.js' %}"></script>

        <script type="text/javascript" src="{% static 'js/jquery.form.js' %}"></script>
        <script type="text/javascript" src="{% static 'js/facebox.js' %}"></script>
        <script type="text/javascript" src="{% static 'js/peakery.js' %}"></script>
        <script type="text/javascript" src="{% static 'js/jquery.popup.js' %}"></script>
        <script type="text/javascript" charset="utf-8">
            $(document).ready(function() {
                {% include "main/ssi/ssi.js" %}
            });
        </script>
        <script type="text/javascript" src="{% static 'js/jquery.placeholder.js' %}"></script>
        <script type="text/javascript">
            $(function(){
                $('input, textarea').placeholder();
            });
        </script>
        <script type="text/javascript" src="{% static 'js/jquery.elastic.source.js' %}"></script>
        <script type="text/javascript" src="{% static 'js/jquery.OnEnter.js' %}"></script>
        <script type="text/javascript" src="{% static 'js/jquery.scrollTo-1.4.2.js' %}"></script>
        <script type="text/javascript">
            $(function(){
                $('textarea.elastic').elastic();
            });
        </script>

        <script type="text/javascript" src="{% static '' %}js/peakery-shasta-3.07.js"></script>
    {% block extrajs %}{% endblock %}
    {% block js_for_includes %}{% endblock %}
</head>

<body class="{% block bodyclass %}{% endblock %}">

{% block body_form %}{% endblock %}

<div id="darkness" style="z-index: 999999; background: rgba(0, 0, 0, 0.8);"></div>

<div id="content-holder" style="background-color:#333;">

{% block full_height_form %}{% endblock %}

<!-- Fixed navbar -->
<nav class="navbar navbar-inverse navbar-fixed-top" style="z-index: 9; border: none;">
  <div class="container no-header-container" style="padding-left: 0px;">
    {% block fixed_page_header %}{% endblock %}
  </div>
</nav>

<!-- Begin page content -->
<div id="content-body" class="{{ fixed_subnav_class }}">

    <div id="main">
        {% block content %}
        {% endblock %}
    </div>

</div> <!-- /content-body -->

{% block end_full_height_form %}{% endblock %}

</div> <!-- /content-holder -->

{% if not user.is_authenticated %}
<div class="accounts-sign-up-modal modal fade" id="accounts-sign-up" tabindex="-1" role="dialog" aria-labelledby="accounts-sign-up-label">
    <div class="modal-dialog" style="width: 450px;" role="document">
        <div class="modal-content">

        </div>
    </div>
</div>
<div class="accounts-login-modal modal fade" id="accounts-login" tabindex="-1" role="dialog" aria-labelledby="accounts-login-label">
    <div class="modal-dialog" style="width: 450px;" role="document">
        <div class="modal-content">

        </div>
    </div>
</div>
{% endif %}

{% block gallery %}
{% endblock %}

{% block end_body_form %}{% endblock %}

</body>
</html>