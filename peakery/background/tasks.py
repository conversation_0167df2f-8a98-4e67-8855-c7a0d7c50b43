import datetime
from django.core.mail import send_mail as django_send_mail
from django.core.mail.message import EmailMessage
from django.conf import settings
from django.db import connection

def regular_send_mail(subject, message, from_email, recipient_list, fail_silently=False, auth_user=None, auth_password=None, connection=None):
    return django_send_mail(subject, message, from_email, recipient_list, fail_silently, auth_user, auth_password, connection)

def email_message(subject, body, to, from_email=settings.DEFAULT_FROM_EMAIL, attachments=None):
    try:
        email = EmailMessage(subject, body, to=to, from_email=from_email)
        email.content_subtype = "html"

        if attachments:
            for a in attachments:
                email.attach_file(a)
        email.send()

        return True
    except Exception as e:
        print(e)
        return False


def task_approve_temp_item(id):
    from peakery.items.models import Item, ItemCountry, ItemRegion
    from peakery.tempitems.models import TempItem

    print('approving item : %s' % id)

    tempitem = TempItem.objects.get(pk=id)

    item = Item(name=tempitem.name,lat=tempitem.lat,long=tempitem.long,elevation=tempitem.elevation,
                prominence=tempitem.prominence,range=tempitem.range,user=tempitem.user,location=tempitem.location,location_edited=True,elevation_edited=True)
    item.quick_save()
    # Include Country and Regions that new Item
    for country in tempitem.country.all():
        itemcountry = ItemCountry(item=item, country=country)
        itemcountry.save()
    for region in tempitem.region.all():
        itemregion  = ItemRegion(item=item, region=region)
        itemregion.save()

    #set the new slug
    item.set_peak_slug()

    #get the new peak
    new_item = Item.objects.get(id=item.id)

    item.send_approved_email(tempitem, new_item)

    tempitem.delete()
    return True

def task_reject_temp_item(id):
    from peakery.tempitems.models import TempItem
    tempitem = TempItem.objects.get(pk=id)
    if tempitem.reject_text.all().count() > 0:
        tempitem.send_rejected_email()
    return True

def task_rename_temp_item(id):
    from peakery.items.models import Item, ItemCountry, ItemRegion
    from peakery.tempitems.models import TempItem
    tempitem = TempItem.objects.get(pk=id)

    new_peak_elevation = tempitem.elevation
    new_peak_elevation_meters = float(tempitem.elevation) * .3048
    if tempitem.is_usa():
        new_peak_name = 'Peak %s ft' % (int(round(float(new_peak_elevation), 0)))
    else:
        new_peak_name = 'Peak %s m' % (int(round(new_peak_elevation_meters, 0)))

    tempitem.name = new_peak_name
    tempitem.save()

    return True

def task_approve_item_correction(correction_id):
    from peakery.items.models import ItemCorrection, STATUS_REJECTED, STATUS_APPROVED,AlternateName,AlternateNameItemCorrection,ItemRegion,RegionItemCorrection,ItemCountry,CountryItemCorrection
    print("using the task for task_approve_item_correction ")
    # We must apply the field
    correction = ItemCorrection.objects.get(pk=correction_id)
    if correction.status != STATUS_APPROVED:
        item = correction.item
        field = correction.get_field_display().strip().lower()
        if field in ['coords']:
            new_value = correction.location
            item.fix_fields_with(field=field,value = new_value)
        elif field in ['alternate names']:
            alternate_name_correction = AlternateNameItemCorrection.objects.filter(item = correction)
            if alternate_name_correction:
                alternate_name_correction = alternate_name_correction[0]
                alternate_name = AlternateName()
                alternate_name.name = alternate_name_correction.name
                alternate_name.item = item
                alternate_name.save()
                #item.save()
        elif field in ['country']:
            country_correction = CountryItemCorrection.objects.filter(item = correction)
            if correction.new_value:
                if country_correction:
                    country_correction = country_correction[0]
                    item_country = ItemCountry()
                    item_country.country = country_correction.country
                    item_country.item = item
                    item_country.save()
                    item.set_peak_slug()
            else:
                if country_correction:
                    country_correction = country_correction[0]
                    sql = "delete from items_item_country where item_id = %s and country_id = %s "
                    with connection.cursor() as cursor:
                        cursor.execute(sql, [item.id, country_correction.country.id])
                    item.set_peak_slug()
        elif field in ['region']:
            region_correction = RegionItemCorrection.objects.filter(item = correction)
            if correction.new_value:
                if region_correction:
                    region_correction = region_correction[0]
                    item_region = ItemRegion()
                    item_region.region = region_correction.region
                    item_region.item = item
                    item_region.save()
                    item.set_peak_slug()
            else:
                if region_correction:
                    region_correction = region_correction[0]
                    sql = "delete from items_item_region where item_id = %s and region_id = %s "
                    with connection.cursor() as cursor:
                        cursor.execute(sql, [item.id, region_correction.region.id])
                    item.set_peak_slug()
        elif field in ['photo']:
            item.thumbnail = correction.new_photo.image
            item.thumbnail_credit = correction.user.username
            item.quick_save()
        elif field in ['delete']:
            item.active = False
            item.quick_save()
        else:
            new_value = correction.new_value
            item.fix_fields_with(field=field,value = new_value)
        correction.status = STATUS_APPROVED
        correction.decision_date = datetime.datetime.now()
        correction.save()
        # Removed by Ticket 824: admin: when an admin user approves Item Corrections, dont send the user emails
        if field in ['delete']:
            pass
        else:
            correction.send_approved_email()
        # clear cache
        from django.core.cache import cache
        key = "ubication_names_%s" % item.id
        if key:
            if cache.get(key):
                cache.set(key, None, 0)

def task_reject_item_correction(correction_id):
    from peakery.items.models import ItemCorrection, STATUS_REJECTED, STATUS_APPROVED
    print("using the task for task_reject_item_correction ")
    correction = ItemCorrection.objects.get(pk=correction_id)
    field = correction.get_field_display().strip().lower()
    correction.status = STATUS_REJECTED
    correction.decision_date = datetime.datetime.now()
    correction.save()
    #item = correction.item
    #item.save()
    if field in ['delete']:
        item = correction.item
        item.active = True
        item.quick_save()

