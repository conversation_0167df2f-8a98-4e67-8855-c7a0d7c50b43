class Activity:
    user_id = None
    ip_address = None
    activity_source = None

    def __init__(self, user_id, ip_address, activity_source):
        self.user_id = user_id
        self.ip_address = ip_address
        self.activity_source = activity_source

    def __hash__(self):
        return hash(self.user_id)

    def __eq__(self, other):
        if not isinstance(other, type(self)):
            return NotImplemented
        return self.user_id == other.user_id
