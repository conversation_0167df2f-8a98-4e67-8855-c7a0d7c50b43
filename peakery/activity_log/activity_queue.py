import queue

from peakery.activity_log.activity import Activity


class ActivityQueue:
    activity_queue = None

    def __init__(self):
        self.activity_queue = queue.Queue()

    def put(self, act: Activity):
        self.activity_queue.put(act)

    def size(self) -> int:
        return self.activity_queue.qsize()

    def drain_queue(self) -> set[Activity]:
        activities = set()
        while not self.activity_queue.empty():
            activities.add(self.activity_queue.get())
        return activities
