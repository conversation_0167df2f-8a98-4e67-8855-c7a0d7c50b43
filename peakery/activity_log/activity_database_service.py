from peakery.activity_log.activity_queue import ActivityQueue
from threading import Thread
from time import sleep
from datetime import datetime
from django.db import connection
from psycopg2.extras import execute_values

PERSIST_INTERVAL_SECS = 60  # The interval where items of the queue will be taken and persisted.
MAX_QUEUE_SIZE = 5000  # The max allowed queue size before items are persisted.


class ActivityDatabaseService:
    last_run = datetime.now()
    activity_queue = None

    def __init__(self, activity_queue: ActivityQueue):
        self.activity_queue = activity_queue

    def persist_activities(self, activities_tuple):
        with connection.cursor() as cursor:
            execute_values(cursor,
                           "INSERT INTO activity_log (user_id, ip_address, activity_date, activity_source) VALUES %s",
                           activities_tuple)

    def consume_and_persist(self):
        while True:
            try:
                current_time = datetime.now()
                elapsed_since_last_run_secs = (current_time - self.last_run).total_seconds()

                if ((elapsed_since_last_run_secs > PERSIST_INTERVAL_SECS and self.activity_queue.size() > 0)
                        or self.activity_queue.size() > MAX_QUEUE_SIZE):
                    activities_set = self.activity_queue.drain_queue()
                    activities_tuple = [(a.user_id, a.ip_address, "now()", a.activity_source) for a in
                                        activities_set]
                    self.persist_activities(activities_tuple)
                    self.last_run = datetime.now()
                else:
                    sleep(5)  # Wait 5 seconds before checking again
            except Exception as e:
                print("Something wrong happened when processing the activity log")
                print(e)

    def initialize_service(self):
        print("Starting activity service...")
        thread = Thread(target=self.consume_and_persist, daemon=True)
        thread.start()