# Generated by Django 4.2.11 on 2024-07-29 15:00

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('items', '0005_item_soft_delete'),
    ]

    operations = [
        migrations.AlterField(
            model_name='companions',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='gpxtrack',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='gpx_track', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='item',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='itemauditlogentry',
            name='action_user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='itemcomment',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='item_comments', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='itemcorrection',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items_corrections', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='itemcorrectionloggroup',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='item_correction_log_group', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='itemduplicate',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='items_duplicates', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='itemerrorreport',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='item_error_reports', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='itemgroup',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='item_groups', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='itemgrouphighlight',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='item_group_highlights', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='itemgrouphighlightloggroup',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='item_group_highlights_log_group', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='itemhighlight',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='item_highlights', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='itemhighlightloggroup',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='item_highlights_log_group', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='itemphoto',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='item_photos', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='peakroute',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='peak_route', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='peakroutearchive',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='peak_route_archive', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='peakroutehighlight',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='peak_route_highlights', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='peakroutehighlightloggroup',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='peak_route_highlights_log_group', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='summitlog',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='summit_log', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='summitlogcomment',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='summit_comments', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='summitloggroup',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='summit_log_group', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='summitroute',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='routes_created', to=settings.AUTH_USER_MODEL),
        ),
    ]
