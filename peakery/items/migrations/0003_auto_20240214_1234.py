# Generated by Django 3.2 on 2024-02-14 12:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('items', '0002_removed_unused_columns_20240130_1536'),
    ]

    operations = [
        migrations.AlterField(
            model_name='item',
            name='country',
            field=models.ManyToManyField(blank=True, db_index=True, help_text='Only if dont know the region', related_name='country_items', through='items.ItemCountry', to='cities.Country'),
        ),
        migrations.AlterField(
            model_name='item',
            name='region',
            field=models.ManyToManyField(blank=True, help_text='If dont know the region just select the country below', related_name='region_items', through='items.ItemRegion', to='cities.Region'),
        ),
    ]
