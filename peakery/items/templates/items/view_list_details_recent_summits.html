{% extends "base.html" %}

{% block extrajs %}
<script type="text/javascript">
    $(document).ready(function(){
        $(".peak_desc1 p").linkify();
    });
</script>
{% endblock %}

{% load thumbnail %}
{% load avatar_tags %}

{% block title %}{{ group }} - Recent Summits{% endblock %}
{% block titlemeta %}{{ group }} - Recent Summits - {% endblock %}

{% block content %}

<div class="peak_lists">

    <div class="peak_section_desc1">
        <h1><a href="{{ group.get_absolute_url }}">{{ group.name }}</a> - Recent Summits <span class="metadata">{{ group.items.count }} peaks</span></h1>
        <img src="{% thumbnail group.get_thumbnail 340x262 crop%}" alt=""/>
        <p>{{ group.description }}</p>
    </div>

<!--RECENT SUMMITS-->

    {% if all_summits %}

    <div class="list_section">
        <h2>Recent Summits</h2>
        {% for rs in all_summits %}
        <ul class="users_lists_all">
            <li>
                <div class="avatar">
                    <a href="{% url "user_profile" rs.0.user.username %}">{% avatar rs.0.user 100 %}</a>
                </div>
                <!--END avatar -->
                <div class="data">
                    <a class="nickname" href="{% url "user_profile" rs.0.user.username %}"> {{ rs.0.user }}</a>
                    <p><a href="{{ rs.0.item.get_absolute_url }}">{{ rs.0.item }}
                        on {% if rs.0.date_entered %}{{ rs.0.date|date:"b j, Y"|title }}{% else %}date unknown{% endif %}</a></p>
                    {% if rs.1.3 < 30 %}
                        <span class="percentContainer">
                                    <span class="percent"
                                          style="width: {{ rs.1.3 }}%; display: inline-block; float: left;"></span>
                                    <p style="display: inline-block; font-size: 10px; color: #000; float: left; line-height: 23px; margin-left: 5px; padding: 0; width: auto;">{{ rs.1.3 }}%</p>
                                </span>
                    {% else %}
                        <span class="percentContainer">
                       <span class="percent" style="width: {{ rs.1.3 }}%;font-size: 10px;font-weight: normal;">{{ rs.1.3 }}%</span>
                    </span>
                {% endif %}
                <p class="completed_list">{{ rs.1.2 }} of {{ rs.1.1 }}</p>
                </div><!--END data -->
            </li>
        </ul>
        {% endfor %}
    </div>
    {% endif %}
</div>

<div id="explore_pagination">
    <div class="results">
        <p style="font-size: 14px">results {{ ini }} - {% if end > all_summits_count %} {{ all_summits_count }}{% else %} {{ end }} {% endif %} of {{ all_summits_count }}</p>
    </div>
    <div class="links">
        <p style="font-size: 16px">
            {% if previous != 0 %}<a href="?page={{ previous }}{{ getvars }}">&laquo; previous</a> {% endif %}
            {% if end < all_summits_count %} <a href="?page={{ next }}{{ getvars }}">next &raquo;</a>{% endif %}
        </p>
    </div>
</div>

{% endblock %}
