<script type="text/javascript">
    //    $(document).ready(function(){min_h=160;$("ul#peak-photos li").each(function(k,v){h=parseInt(($(v).css("height")).replace('px',''));if(h>min_h){min_h=h}});$("ul#peak-photos li").css("height",min_h+"px")});
</script>
{% for p in photos %}
    {% comment %}<li style="background-image: url('{{ p.photo_url }}')">
    <a href="{% url "photo_view" 0 %}?u={{ p.photo_id }}&user={{ p.user_name }}&user_id={{ p.user_id }}&c={{ p.caption }}" title="" class="photoLink lightbox"></a>{% endcomment %}
    {#        <img src="{{ p.photo_url }}" alt="{{ p.caption }}" width="230" height="160" />#}
    {% comment %}{% if p.caption %}
        <span class="">
            {{ p.caption }}
        </span>
    {% endif %}
    <a class="font11px" href="{{ p.user_page }}" target="_blank">
        {{ p.user_name }}
    </a>
    <span class="data">
        <p class="bagger">{{ p.user_name }} &bull; Panoramio</p>
    </span>
</li>{% endcomment %}
    <li class="panoramioImage" style="display: none;">
        <div class="img" style="background-image: url('{{ p.photo_url }}');">
            <a href="">{{ p.user_name }}</a>
        </div>
        <div class="caption">
            {% if p.caption %}
                <p class="caption">{{ p.caption }}</p>
            {% endif %}
        </div>
    </li>
{% endfor %}