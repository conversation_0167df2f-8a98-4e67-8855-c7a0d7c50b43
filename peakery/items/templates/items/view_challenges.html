{% extends "base.html" %}

{% load static %}
{% block title %}Peak challenges{% endblock %}
{% block titlemeta_overwrite %}Peak challenges{% endblock %}
{% block description %}{{ challenges|length }} Peak Challenges around the world. Peak Challenges invite you to climb special collections of mountains ranging from gentle hikes to technical climbs.{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row fixed-page-header">
        <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 70px; border-radius: 12px 12px 0 0;">
            <div class="section-title hidden-xs" style="font-weight: 600; float: left;">Peak Challenges</div>
            <div class="mobile-section-title hidden-sm hidden-md hidden-lg" style="font-weight: 500; float: left;">
                <span id="mobile-summits-count" style="color: #F24100;">{{ challenges|length }}</span> Peak Challenges in the world
            </div>
            <div class="pull-right hidden-xs section-title-stats" style="margin-left: 15px;">
                <span style="font-size: 16px; letter-spacing: 1.0px; font-weight: 400;">
                    <span id="summits-count" style="color: #F24100;">{{ challenges|length }}</span>
                    <span id="summits-count-desc" style="color: #333;">     challenges around the world</span>
                </span>
            </div>
        </div>
    </div>
{% endblock %}

{% block content %}

<style>

   #regions_div {cursor: pointer;}
   #regions_div_tablet {cursor: pointer;}
   #regions_div path[fill="#ffffff"] {cursor: auto}
   #regions_div_tablet path[fill="#ffffff"] {cursor: auto}
   #regions_div path[fill="#f5f5f5"] {cursor: auto}
   #regions_div_tablet path[fill="#f5f5f5"] {cursor: auto}

    @media screen and (max-width: 767px) and (min-width: 1px) {
       .challenge-continent {
            margin-left:20px;
            font-size: 16px;
            font-weight: 500;
            text-transform: uppercase;
            margin-top: 20px;
        }
        .challenge-country {
            margin-left:30px;
            font-size: 14px;
            padding: 5px;
            font-weight: 500;
        }
        .challenge-region {
            margin-left:40px;
            font-size: 14px;
            padding: 5px;
        }
   }
   @media screen and (max-width: 1023px) and (min-width: 768px) {
       .challenge-continent {
            margin-left:20px;
            font-size: 20px;
            font-weight: 500;
            text-transform: uppercase;
            margin-top: 20px;
        }
        .challenge-country {
            margin-left:30px;
            font-size: 16px;
            padding: 5px;
            font-weight: 500;
        }
        .challenge-region {
            margin-left:40px;
            font-size: 16px;
            padding: 5px;
        }
   }
    @media screen and (min-width: 1024px) {
        .challenge-continent {
            margin-left:20px;
            font-size: 20px;
            font-weight: 500;
            text-transform: uppercase;
            margin-top: 20px;
        }
        .challenge-country {
            margin-left:30px;
            font-size: 16px;
            padding: 5px;
            font-weight: 500;
        }
        .challenge-region {
            margin-left:40px;
            font-size: 16px;
            padding: 5px;
        }
    }

</style>

<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<script type="text/javascript">
  google.load("visualization", "1", {packages:["geochart"]});
  google.setOnLoadCallback(drawRegionsMap);

  window.onresize = function(){
        var chartWidth = $('#regions_div').width();
        var chartHeight = chartWidth * 0.59; // 59%
        $('#regions_div').height(chartHeight);
        $('#challenges-overview').height(chartHeight);

        var col1_height = $('#challenges-col1-content').height();
        var col2_height = $('#challenges-col2-content').height();
        var col3_height = $('#challenges-col3-content').height();
        var col4_height = $('#challenges-col4-content').height();
        if (col1_height >= col2_height && col1_height >= col3_height && col1_height >= col4_height) {
            $('#challenges-col1').height($('#challenges-col1-content').height()+30);
            $('#challenges-col2').height($('#challenges-col1-content').height()+30);
            $('#challenges-col3').height($('#challenges-col1-content').height()+30);
            $('#challenges-col4').height($('#challenges-col1-content').height()+30);
        } else if (col2_height >= col1_height && col2_height >= col3_height && col2_height >= col4_height) {
            $('#challenges-col1').height($('#challenges-col2-content').height()+30);
            $('#challenges-col2').height($('#challenges-col2-content').height()+30);
            $('#challenges-col3').height($('#challenges-col2-content').height()+30);
            $('#challenges-col4').height($('#challenges-col2-content').height()+30);
        } else if (col3_height >= col1_height && col3_height >= col2_height && col3_height >= col4_height) {
            $('#challenges-col1').height($('#challenges-col3-content').height()+30);
            $('#challenges-col2').height($('#challenges-col3-content').height()+30);
            $('#challenges-col3').height($('#challenges-col3-content').height()+30);
            $('#challenges-col4').height($('#challenges-col3-content').height()+30);
        } else if (col4_height >= col1_height && col4_height >= col2_height && col4_height >= col3_height) {
            $('#challenges-col1').height($('#challenges-col4-content').height()+30);
            $('#challenges-col2').height($('#challenges-col4-content').height()+30);
            $('#challenges-col3').height($('#challenges-col4-content').height()+30);
            $('#challenges-col4').height($('#challenges-col4-content').height()+30);
        }

        drawRegionsMap();
    };

  function slugify(str) {
      str = str.replace(/^\s+|\s+$/g, ''); // trim
      str = str.toLowerCase();

      // remove accents, swap ñ for n, etc
      var from = "àáäâèéëêìíïîòóöôùúüûñç·/_,:;";
      var to   = "aaaaeeeeiiiioooouuuunc------";
      for (var i=0, l=from.length ; i<l ; i++) {
        str = str.replace(new RegExp(from.charAt(i), 'g'), to.charAt(i));
      }

      str = str.replace(/[^a-z0-9 -]/g, '') // remove invalid chars
        .replace(/\s+/g, '-') // collapse whitespace and replace by -
        .replace(/-+/g, '-'); // collapse dashes

      return str;
    }

  function openUrl(url) {
        window.location.href = url;
    }

  function drawRegionsMap() {

    var countriesArray = [['Country', 'Challenges']];
    var countryItem;
    {% for c in challenge_countries %}
    countryItem = ['{{ c.name }}', parseFloat({{ c.challenge_count }})];
    countriesArray.push(countryItem);
    {% endfor %}

    var data = google.visualization.arrayToDataTable(countriesArray);

    var options = {
        colorAxis: {colors: ['#00B1F2', '#00B1F2']},
        defaultColor: '#00B1F2',
        forceIFrame: false,
        legend: 'none'
    };

    var chart = new google.visualization.GeoChart(document.getElementById('regions_div'));

    google.visualization.events.addListener(chart, 'select', function() {
        var selectionIdx = chart.getSelection()[0].row;
        var countryName = data.getValue(selectionIdx, 0);
        window.location.href = '/region/' + slugify(countryName) + '-mountains/challenges/';
    });

    google.visualization.events.addListener(chart, 'error', function() {
      $('#regions_div').hide();
    });

    google.visualization.events.addListener(chart, 'ready', setChartSize);

    chart.draw(data, options);
  }

  function setChartSize() {
    var chartWidth = $('#regions_div').width();
    var chartHeight = chartWidth * 0.59; // 59%
    $('#regions_div').height(chartHeight);
    $('#challenges-overview').height(chartHeight);
  }

  $(document).ready(function() {

        var chartWidth = $('#regions_div').width();
        var chartHeight = chartWidth * 0.59; // 59%
        $('#regions_div').height(chartHeight);
        $('#challenges-overview').height(chartHeight);

        var col1_height = $('#challenges-col1-content').height();
        var col2_height = $('#challenges-col2-content').height();
        var col3_height = $('#challenges-col3-content').height();
        var col4_height = $('#challenges-col4-content').height();
        if (col1_height >= col2_height && col1_height >= col3_height && col1_height >= col4_height) {
            $('#challenges-col1').height($('#challenges-col1-content').height()+30);
            $('#challenges-col2').height($('#challenges-col1-content').height()+30);
            $('#challenges-col3').height($('#challenges-col1-content').height()+30);
            $('#challenges-col4').height($('#challenges-col1-content').height()+30);
        } else if (col2_height >= col1_height && col2_height >= col3_height && col2_height >= col4_height) {
            $('#challenges-col1').height($('#challenges-col2-content').height()+30);
            $('#challenges-col2').height($('#challenges-col2-content').height()+30);
            $('#challenges-col3').height($('#challenges-col2-content').height()+30);
            $('#challenges-col4').height($('#challenges-col2-content').height()+30);
        } else if (col3_height >= col1_height && col3_height >= col2_height && col3_height >= col4_height) {
            $('#challenges-col1').height($('#challenges-col3-content').height()+30);
            $('#challenges-col2').height($('#challenges-col3-content').height()+30);
            $('#challenges-col3').height($('#challenges-col3-content').height()+30);
            $('#challenges-col4').height($('#challenges-col3-content').height()+30);
        } else if (col4_height >= col1_height && col4_height >= col2_height && col4_height >= col3_height) {
            $('#challenges-col1').height($('#challenges-col4-content').height()+30);
            $('#challenges-col2').height($('#challenges-col4-content').height()+30);
            $('#challenges-col3').height($('#challenges-col4-content').height()+30);
            $('#challenges-col4').height($('#challenges-col4-content').height()+30);
        }
  });

</script>

<div class="container">

    <div id="challenges-div" class="row sub-header-row hidden-sm hidden-xs">
        <div id="challenges-overview" class="col-lg-3 col-md-3 col-sm-12 col-xs-12" style="min-height: 140px;">
            <p style="margin-top: 45px !important;">Get out in the mountains more! Climb special collections of peaks called <i><strong>Peak Challenges</strong></i>.</p>

            <p>peakery has Challenges for everyone: from gentle walks to technical climbs. There's one ready for you to tackle.</p>

            <p style="color: #ff0000; font-size: 12px !important; text-transform: uppercase; font-weight: 500;">Use caution pursuing Challenges. Hazards most definitely exist!</p>

            <div id="add-peak-challenge"><a class="modal-link" style="font-size: 15px;" data-toggle="modal" data-target="#add-challenge">Want to add a Peak Challenge?</a></div>
        </div>
        <div class="col-lg-9 col-md-9" style="background-color: #fff;">
            <div id="regions_div" style="width: 100%; height: 100%;"></div>
        </div>
    </div>
    <div class="row">
        {% for c in top_challenges|slice:"0:1" %}
            <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 hover-photos" onclick="openUrl('/challenges/{{ c.slug }}/');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{% static 'img/default.png' %}'); background-size: cover; border-right: solid 1px #fff;">
                <div>
                    <a href="/challenges/{{ c.slug }}/"><img src="{{ MEDIA_URL }}{{ c.thumbnail }}" class="img-responsive peakeryPhoto photography peakimg-responsive"></a>
                    <div class="user-photo-info">
                        <span class="photo-caption-peakname-only" style="position: absolute; bottom: 5px; left: 10px; color: #fff;">
                            <div class="bagger ellipsis"><a href="/challenges/{{ c.slug }}/" style="color: #fff;">{{ c.name }}</a></div>
                        </span>
                    </div>
                </div>
            </div>
        {% endfor %}
        {% for c in top_challenges|slice:"1:2" %}
            <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 hover-photos" onclick="openUrl('/challenges/{{ c.slug }}/');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{% static 'img/default.png' %}'); background-size: cover;">
                <div>
                    <a href="/challenges/{{ c.slug }}/"><img src="{{ MEDIA_URL }}{{ c.thumbnail }}" class="img-responsive peakeryPhoto photography peakimg-responsive"></a>
                    <div class="user-photo-info">
                        <span class="photo-caption-peakname-only" style="position: absolute; bottom: 5px; left: 10px; color: #fff;">
                            <div class="bagger ellipsis"><a href="/challenges/{{ c.slug }}/" style="color: #fff;">{{ c.name }}</a></div>
                        </span>
                    </div>
                </div>
            </div>
        {% endfor %}
        {% for c in top_challenges|slice:"2:3" %}
            <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 hidden-xs hidden-sm hover-photos" onclick="openUrl('/challenges/{{ c.slug }}/');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{% static 'img/default.png' %}'); background-size: cover; border-left: solid 1px #fff;">
                <div>
                    <a href="/challenges/{{ c.slug }}/"><img src="{{ MEDIA_URL }}{{ c.thumbnail }}" class="img-responsive peakeryPhoto photography peakimg-responsive"></a>
                    <div class="user-photo-info">
                        <span class="photo-caption-peakname-only" style="position: absolute; bottom: 5px; left: 10px; color: #fff;">
                            <div class="bagger ellipsis"><a href="/challenges/{{ c.slug }}/" style="color: #fff;">{{ c.name }}</a></div>
                        </span>
                    </div>
                </div>
            </div>
        {% endfor %}
        {% for c in top_challenges|slice:"3:4" %}
            <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 hidden-xs hover-photos" onclick="openUrl('/challenges/{{ c.slug }}/');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{% static 'img/default.png' %}'); background-size: cover; border-left: solid 1px #fff;">
                <div>
                    <a href="/challenges/{{ c.slug }}/"><img src="{{ MEDIA_URL }}{{ c.thumbnail }}" class="img-responsive peakeryPhoto photography peakimg-responsive"></a>
                    <div class="user-photo-info">
                        <span class="photo-caption-peakname-only" style="position: absolute; bottom: 5px; left: 10px; color: #fff;">
                            <div class="bagger ellipsis"><a href="/challenges/{{ c.slug }}/" style="color: #fff;">{{ c.name }}</a></div>
                        </span>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
    <div class="row sub-header-row" style="border: none;">
        <div id="challenges-col1" class="col-lg-3 col-md-3 col-sm-4 col-xs-6" style="padding-right: 0px; padding-left: 0px; padding-top: 20px;">
        <div id="challenges-col1-content">
        <p class="challenge-continent"><a href="/world-mountains/challenges/">WORLDWIDE</a>&nbsp;&nbsp;<span style="color: #aaa; font-size: 10px; font-weight: 300;">{{ world_challenges|length }}</span></p>
        {% for c in challenge_continents %}
            {% if c.id == 2 %}
                <p class="challenge-continent"><a href="/region/{{ c.slug }}-mountains/challenges/">{{ c.name }}</a>&nbsp;&nbsp;<span style="color: #aaa; font-size: 10px; font-weight: 300;">{{ c.challenge_count }}</span></p>
                {% for d in challenge_countries %}
                    {% if d.continent == c.id %}
                        <p class="challenge-country"><a href="/region/{{ d.slug }}-mountains/challenges/">{{ d.name }}</a>&nbsp;&nbsp;<span style="color: #aaa; font-size: 10px; font-weight: 300;">{{ d.challenge_count }}</span></p>
                        {% for r in challenge_regions %}
                            {% if r.country_id == d.id %}
                                <p class="challenge-region"><a href="/{{ d.slug }}-mountains/{{ r.slug }}/challenges/">{{ r.name }}</a>&nbsp;&nbsp;<span style="color: #aaa; font-size: 10px; font-weight: 300;">{{ r.challenge_count }}</span></p>
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                {% endfor %}
            {% endif %}
        {% endfor %}
        <p style="margin-left:40px; padding: 5px;">&nbsp;</p>
        </div>
        </div>
        <div id="challenges-col2" class="col-lg-3 col-md-3 col-sm-4 col-xs-6" style="border-left: solid 1px #fff; padding-right: 0px; padding-left: 0px; padding-top: 20px;">
        <div id="challenges-col2-content">
        {% for c in challenge_continents %}
            {% if c.id == 4 %}
                <p class="challenge-continent"><a href="/region/{{ c.slug }}-mountains/challenges/">{{ c.name }}</a>&nbsp;&nbsp;<span style="color: #aaa; font-size: 10px; font-weight: 300;">{{ c.challenge_count }}</span></p>
                {% for d in challenge_countries %}
                    {% if d.continent == c.id %}
                        <p class="challenge-country"><a href="/region/{{ d.slug }}-mountains/challenges/">{{ d.name }}</a>&nbsp;&nbsp;<span style="color: #aaa; font-size: 10px; font-weight: 300;">{{ d.challenge_count }}</span></p>
                        {% for r in challenge_regions %}
                            {% if r.country_id == d.id %}
                                <p class="challenge-region"><a href="/{{ d.slug }}-mountains/{{ r.slug }}/challenges/">{{ r.name }}</a>&nbsp;&nbsp;<span style="color: #aaa; font-size: 10px; font-weight: 300;">{{ r.challenge_count }}</span></p>
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                {% endfor %}
            {% endif %}
        {% endfor %}
        <div class="hidden-lg hidden-md hidden-sm" style="border-left: 1px solid rgb(255, 255, 255); margin-top: -9px; margin-left: -1px; padding-top: 9px;">
        {% for c in challenge_continents %}
            {% if c.id == 3 or c.id == 8 or c.id == 7 or c.id == 5 %}
                <p class="challenge-continent"><a href="/region/{{ c.slug }}-mountains/challenges/">{{ c.name }}</a>&nbsp;&nbsp;<span style="color: #aaa; font-size: 10px; font-weight: 300;">{{ c.challenge_count }}</span></p>
                {% for d in challenge_countries %}
                    {% if d.continent == c.id %}
                        <p class="challenge-country"><a href="/region/{{ d.slug }}-mountains/challenges/">{{ d.name }}</a>&nbsp;&nbsp;<span style="color: #aaa; font-size: 10px; font-weight: 300;">{{ d.challenge_count }}</span></p>
                        {% for r in challenge_regions %}
                            {% if r.country_id == d.id %}
                                <p class="challenge-region"><a href="/{{ d.slug }}-mountains/{{ r.slug }}/challenges/">{{ r.name }}</a>&nbsp;&nbsp;<span style="color: #aaa; font-size: 10px; font-weight: 300;">{{ r.challenge_count }}</span></p>
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                {% endfor %}
            {% endif %}
        {% endfor %}
        <p style="margin-left:20px; font-size: 20px; font-weight: 500; text-transform: uppercase; margin-top: 20px;"><a href="/region/Antarctica-mountains/challenges/">Antarctica</a></p>
        <p style="margin-left:30px; padding: 5px; font-weight: 300; color: #aaa; padding-bottom: 20px;">brrrr... seriously?</p>
        </div>
        <p style="margin-left:40px; padding: 5px;">&nbsp;</p>
        </div>
        </div>
        <div id="challenges-col3" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 hidden-xs" style="border-left: solid 1px #fff; padding-right: 0px; padding-left: 0px; padding-top: 20px;">
        <div id="challenges-col3-content">
        {% for c in challenge_continents %}
            {% if c.id == 3 or c.id == 8 %}
                <p class="challenge-continent"><a href="/region/{{ c.slug }}-mountains/challenges/">{{ c.name }}</a>&nbsp;&nbsp;<span style="color: #aaa; font-size: 10px; font-weight: 300;">{{ c.challenge_count }}</span></p>
                {% for d in challenge_countries %}
                    {% if d.continent == c.id %}
                        <p class="challenge-country"><a href="/region/{{ d.slug }}-mountains/challenges/">{{ d.name }}</a>&nbsp;&nbsp;<span style="color: #aaa; font-size: 10px; font-weight: 300;">{{ d.challenge_count }}</span></p>
                        {% for r in challenge_regions %}
                            {% if r.country_id == d.id %}
                                <p class="challenge-region"><a href="/{{ d.slug }}-mountains/{{ r.slug }}/challenges/">{{ r.name }}</a>&nbsp;&nbsp;<span style="color: #aaa; font-size: 10px; font-weight: 300;">{{ r.challenge_count }}</span></p>
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                {% endfor %}
            {% endif %}
        {% endfor %}
        <div class="hidden-lg hidden-md" style="border-left: 1px solid rgb(255, 255, 255); margin-top: -9px; margin-left: -1px; padding-top: 9px;">
        {% for c in challenge_continents %}
            {% if c.id == 7 or c.id == 5 %}
                <p class="challenge-continent"><a href="/region/{{ c.slug }}-mountains/challenges/">{{ c.name }}</a>&nbsp;&nbsp;<span style="color: #aaa; font-size: 10px; font-weight: 300;">{{ c.challenge_count }}</span></p>
                {% for d in challenge_countries %}
                    {% if d.continent == c.id %}
                        <p class="challenge-country"><a href="/region/{{ d.slug }}-mountains/challenges/">{{ d.name }}</a>&nbsp;&nbsp;<span style="color: #aaa; font-size: 10px; font-weight: 300;">{{ d.challenge_count }}</span></p>
                        {% for r in challenge_regions %}
                            {% if r.country_id == d.id %}
                                <p class="challenge-region"><a href="/{{ d.slug }}-mountains/{{ r.slug }}/challenges/">{{ r.name }}</a>&nbsp;&nbsp;<span style="color: #aaa; font-size: 10px; font-weight: 300;">{{ r.challenge_count }}</span></p>
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                {% endfor %}
            {% endif %}
        {% endfor %}
        <p style="margin-left:20px; font-size: 20px; font-weight: 500; text-transform: uppercase; margin-top: 20px;"><a href="/region/Antarctica-mountains/challenges/">Antarctica</a></p>
        <p style="margin-left:30px; padding: 5px; font-weight: 300; color: #aaa;">brrrr... seriously?</p>
        </div>
        <p style="margin-left:40px; padding: 5px;">&nbsp;</p>
        </div>
        </div>
        <div id="challenges-col4" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 hidden-xs hidden-sm" style="border-left: solid 1px #fff; padding-right: 0px; padding-left: 0px; padding-top: 20px;">
        <div id="challenges-col4-content">
        {% for c in challenge_continents %}
            {% if c.id == 7 or c.id == 5 %}
                <p class="challenge-continent"><a href="/region/{{ c.slug }}-mountains/challenges/">{{ c.name }}</a>&nbsp;&nbsp;<span style="color: #aaa; font-size: 10px; font-weight: 300;">{{ c.challenge_count }}</span></p>
                {% for d in challenge_countries %}
                    {% if d.continent == c.id %}
                        <p class="challenge-country"><a href="/region/{{ d.slug }}-mountains/challenges/">{{ d.name }}</a>&nbsp;&nbsp;<span style="color: #aaa; font-size: 10px; font-weight: 300;">{{ d.challenge_count }}</span></p>
                        {% for r in challenge_regions %}
                            {% if r.country_id == d.id %}
                                <p class="challenge-region"><a href="/{{ d.slug }}-mountains/{{ r.slug }}/challenges/">{{ r.name }}</a>&nbsp;&nbsp;<span style="color: #aaa; font-size: 10px; font-weight: 300;">{{ r.challenge_count }}</span></p>
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                {% endfor %}
            {% endif %}
        {% endfor %}
        <p style="margin-left:20px; font-size: 20px; font-weight: 500; text-transform: uppercase; margin-top: 20px;"><a href="/region/Antarctica-mountains/challenges/">Antarctica</a></p>
        <p style="margin-left:30px; padding: 5px; font-weight: 300; color: #aaa;">brrrr... seriously?</p>
        <p style="margin-left:40px; padding: 5px;">&nbsp;</p>
        </div>
        </div>
    </div>
    <div class="row sub-header-row hidden-lg hidden-md hidden-sm" style="border: none;">
        <div class="row">
            <div style="height: 135px;"></div>
        </div>
    </div>
    <div class="row sub-header-row hidden-xs hidden-sm" style="border: none;">
        <div class="row">
            <div style="height: 25px;"></div>
        </div>
    </div>
</div>

<div class="add-challenge-modal modal fade" id="add-challenge" tabindex="-1" role="dialog" aria-labelledby="add-challenge-label">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
              <h4 class="modal-title" id="about-awards-label">A note on <span style="color: #f24100;">Peak Challenges</span></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12" style="margin-bottom: 20px; font-size: 14px;">Peak challenges aim to get more people out in the mountains. We'd like to feature new challenges around the world that are:</div>
                </div>
                <div class="row">
                    <div class="col-md-12" style="font-size: 14px; margin-left: 20px; padding-right: 40px; line-height: 23px;">
                        <ul style="list-style: initial;">
                            <li style="margin-bottom: 10px;"><span style="color: #f24100; font-weight: 500;">Interesting:</span> exceptionally rewarding collections of mountains to climb defined by objective criteria (elevation, prominence, specific region/park/range, distance from a city, etc.) or subjective criteria ("Classics", popularity, etc.).</li>
                            <li style="margin-bottom: 10px;"><span style="color: #f24100; font-weight: 500;">Accessible:</span> strong preference for challenges near cities to attract far more people (e.g., the <a href="/challenges/denver-home-court-peak-challenge/">Denver Home Court Peaks Challenge</a>). Expansive regions are often best broken into multiple peak challenges (e.g., 3 challenges in Yosemite National Park).</li>
                            <li style="margin-bottom: 10px;"><span style="color: #f24100; font-weight: 500;">Achievable:</span> a realistic undertaking for most people. Not requring long-distance travel between peaks. Sweet spot is between 5 to 40 non-technical peaks (i.e., up to Class 3 in US YDS).</li>
                            <li style="margin-bottom: 10px;"><span style="color: #f24100; font-weight: 500;">Progressive:</span> create a series of challenges in mountainous regions to promote continued mountain adventures. Smaller challenges serve as stepping-stones to bigger challenges (e.g., the <a href="/challenges/phoenix-7-summits-challenge/">Phoenix 7 Summits Challenge</a> is a subset of the 23-peak <a href="/challenges/phoenix-city-limits-peak-challenge/">Phoenix City Limits Challenge</a>).</li>
                        </ul>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12" style="margin-bottom: 5px; font-size: 14px;">Have a good peak challenge in mind? <a href="mailto:<EMAIL>">Please let us know!</a></div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}