{% load thumbnail linkify avatar_tags %}

<li id="comment-{{ c.id }}" style="padding: 20px 15px;">
    <div class="edit-and-share-options">
        {% if request.user == profile.user %}
            {% if c.summit_comment %}
                <span class="right-top-edit-button" style="margin-top: -17px; margin-bottom: 5px;"><a rel="facebox" href="{% url "summit_comment_edit" c.id %}" class="edit_icon">edit your comment</a></span>
            {% else %}
                <span class="right-top-edit-button" style="margin-top: -17px; margin-bottom: 5px;"><a rel="facebox" href="{% url "comment_edit" c.id %}" class="edit_icon">edit your comment</a></span>
            {% endif %}
        {% endif %}
        {% if c.summit_comment %}
            <div class="avatar" style="display: block; margin-bottom: 5px; text-align: right; float: right;">{% avatar c.summit_log.user 85 %}</div>
        {% endif %}
    </div>
    <div class="peak-info" style="width: 500px;">
        <a href="{{ comment_item.get_absolute_url }}">
            <img src="{% thumbnail comment_item.get_thumbnail 110x75 crop %}" alt="{{ comment_item }}" title="{{ comment_item }}" />
        </a>
        <div style="display: inline-block; float: right; width: 380px;">
            <h4 style="margin-bottom: 0;">
                {{ comment_item.name }}
                {% if c.summit_comment %} &bull; <span style="font-size: 13px; font-weight: normal;">comment on <strong>{{ c.summit_log.user.username }}'s</strong> summitlog</span>{% endif %}
            </h4>
            <p>
                <span>{{ comment_item.get_elevation }}</span>
                <span>{{ c.created|date:"M d, Y" }}</span>
            </p>
        </div>
    </div>
    <div id="comment-{{c.id}}" class="summit-info lower">
        <p>{% autoescape off %}{{ c.comment|striptags|linkify:"linkify" }}{% endautoescape %}</p>
    </div>
</li>