{% load thumbnail %}

<script type="text/javascript">
    $(document).ready(function(){
        $("a.filter-order").click(function(e){
            e.preventDefault();
            
            $("a.filter-order").removeClass('current');
            $(this).addClass('current');

            var order = $(this).attr('rel');
            var url = $(this).attr('href');
            var div = $("div.summit_badges");

            $.get(url,{order:order}, function(data){
                $('div#see-more').html('');
                $('a#more').remove();
                $("div#summit-bagdes").html(data);
            });
        });

        // START USER PROFILE SCRIPTS
        $("a#more").click(function(e){
            e.preventDefault();
            
            var a = $(this);
            var order = a.attr('rel');
            
            a.text("").addClass('loading1');
            var url = a.attr('href');

            var order = $("ul#view_summit_by a.current").attr('rel');
            $.get(url,{order:order }, function(response){
                $(response).insertBefore("li#summit-bagdes-get-items-here");
                a.remove();
            });
        });
    });
</script>

{% if first_time %}
    <div id="summit-bagdes">
        {% if summits %}
            <div class="centered">
                <ul id="view_summit_by" class="horz view_by" style="padding-top: 20px; display: table;">
                    <li>View by:</li>
                    <li><a class="filter-order {{ order.order_date }}" href="{% url "summit_badges_by_user" user.username %}" rel="-date">Latest summits</a></li>
                    <li><a class="filter-order {{ order.order_badge }}" href="{% url "summit_badges_by_user" user.username %}" rel="-item__elevation">Highest elevation</a></li>
                    <li><a class="filter-order {{ order.order_most_claimed }}" href="{% url "summit_badges_by_user" user.username %}" rel="most-claimed">Most summits</a></li>
                </ul>

                <div id="summit_bagdes_result">
                    <ul class="summit_badges">
                        {% for s in summits %}
                            <li>
                                <a href="{{ s.item.get_absolute_url }}" {% if s.summit_times <= 1 %}class="withoutNumber"{% endif %}>
                                    {% if s.summit_times > 1 %}
                                        <span class="number" style="position: absolute; top: 0; right: 0; width: 30px; height: 30px;">
                                            <p style="-webkit-transform: rotate(45deg); -moz-transform: rotate(45deg); color: #0070C0; font-weight: bold; font-size: 18px; margin: 2px 25px 0 0; ">{{ s.summit_times }}x</p>
                                        </span>
                                    {% endif %}
                                    <div class="thumb" style="background: url({% thumbnail s.item.get_thumbnail 160x110 crop %}) no-repeat center center">
                                    </div>
                                    <span class="peak">
                                        <span class="item_name">{{ s.item.name }}</span>
                                        <span class="item_info">{{ s.item.get_elevation }}</span>
                                    </span>
                                </a>
                            </li>
                        {% endfor %}
                        <li id="summit-bagdes-get-items-here" style="display: none"></li>
                    </ul>
                </div>
            </div>
        {% endif %}
    </div>
    <div id="see-more"></div>
{% else %}
    {% for s in summits %}
        <li>
            <a href="{{ s.item.get_absolute_url }}" {% if s.summit_times <= 1 %}class="withoutNumber"{% endif %}>
                {% if s.summit_times > 1 %}
                    <span class="number" style="position: absolute; top: 0; right: 0; width: 30px; height: 30px;">
                        <p style="-webkit-transform: rotate(45deg); -moz-transform: rotate(45deg); color: #0070C0; font-weight: bold; font-size: 18px; margin: 2px 25px 0 0; ">{{ s.summit_times }}x</p>
                    </span>
                {% endif %}
                <div class="thumb" style="background: url({% thumbnail s.item.get_thumbnail 160x110 crop %}) no-repeat center center">
                </div>
                <span class="peak">
                    <span class="item_name">{{ s.item.name }}</span>
                    <span class="item_info">{{ s.item.get_elevation }}</span>
                </span>
            </a>
        </li>
    {% endfor %}
{% endif %}

{% if available %}
    <a style="clear:both;" id="more" class="seemore" rel="{{ order.order_by }}" tabindex="{{ peaks_bagged_count }}" href="{% url "summit_badges_by_user" user.username %}?ini={{ next_page }}">see more…</a>
{% endif %}