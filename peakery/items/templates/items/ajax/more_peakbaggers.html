    {% load avatar_tags %}
    {% load account_tags %}
    {% load cities_tags %}

    <script type="text/javascript">
    $(document).ready(function(){
        if ("{{ baggers|length }}" != "10") {
            $("#seemore-summits").hide();
        }



    });
    </script>
    <ul id="bagger" class="peakbaggers">
    {% for bagger in baggers %}

        {%  if timefilter == "30" and bagger.loc_stats__count_30__sum > 0 %}
            <li class="clearfix">
                <div class="clearfix wrapper">
                    <div class="avatar">
                        <a href="{% url "user_profile" bagger %}" class="avatarLink" style="text-decoration:none;">
                            {% avatar bagger 180 %}
                            {# <img src="http://placehold.it/250x250" alt="">  #}
                            <div class="overlay" style="position:absolute; top:0; left:0; width:121px; height:121px;">
                                <img src="{{ MEDIA_URL }}img/peakbaggers/overlay.png" alt="">
                                <p id="a" style="position:absolute; top:35px; left:32px; color:#333; font-weight:bold; font-size: 18px;">#{{ forloop.counter|add:from|add:"-10" }}</p>
                            </div>
                        </a>
                    </div>
                    <div class="content clearfix">
                        <h2 class="membername"><a href="{% url "user_profile" bagger %}">{{ bagger }}</a>
                        <span>
{#                            {% if bagger.loc_stats__count_30__sum < bagger.loc_stats__count_30_unique__sum %}#}
{#                                <span class="blue">{{ bagger.loc_stats__count_30_unique__sum }}</span> new peak{{ bagger.loc_stats__count_30_unique__sum|pluralize }} &bull;#}
{#                                <span class="blue">{{ bagger.loc_stats__count_30_unique__sum }}</span> total summit{{ bagger.loc_stats__count_30_unique__sum|pluralize }}#}
{#                            {% else %}#}
                                <span class="blue">{{ bagger.loc_stats__count_30_unique__sum }}</span> new peak{{ bagger.loc_stats__count_30_unique__sum|pluralize }} &bull;
                                <span class="blue">{{ bagger.loc_stats__count_30__sum }}</span> total summit{{ bagger.loc_stats__count_30__sum|pluralize }}
{#                            {% endif %}#}
                        </span>
                        </h2>
                        <ul class="peaks_bagged">
                            {% summits_filter bagger timefilter country region %}
                        </ul><!-- END peaks_bagged -->
                    </div><!-- END content -->
                </div>
            </li>
        {% endif %}

        {% if timefilter == "365" and bagger.loc_stats__count_365__sum > 0  %}
            <li class="clearfix">
                <div class="clearfix wrapper">
                    <div class="avatar">
                        <a href="{% url "user_profile" bagger %}" class="avatarLink" style="text-decoration:none;">
                            {% avatar bagger 180 %}
                            {# <img src="http://placehold.it/250x250" alt="">  #}
                            <div class="overlay" style="position:absolute; top:0; left:0; width:121px; height:121px;">
                                <img src="{{ MEDIA_URL }}img/peakbaggers/overlay.png" alt="">
                                <p id="a" style="position:absolute; top:35px; left:32px; color:#333; font-weight:bold; font-size: 18px;">#{{ forloop.counter|add:from|add:"-10" }}</p>
                            </div>
                        </a>
                    </div>
                    <div class="content clearfix">
                        <h2 class="membername"><a href="{% url "user_profile" bagger %}">{{ bagger }}</a>
                        <span>
{#                            {% if bagger.loc_stats__count_365__sum < bagger.loc_stats__count_365_unique__sum %}#}
{#                                <span class="blue">{{ bagger.loc_stats__count_365_unique__sum }}</span> new peak{{ bagger.loc_stats__count_365_unique__sum|pluralize }} &bull;#}
{#                                <span class="blue">{{ bagger.loc_stats__count_365_unique__sum }}</span> total summit{{ bagger.loc_stats__count_365_unique__sum|pluralize }}#}
{#                            {% else %}#}
                                <span class="blue">{{ bagger.loc_stats__count_365_unique__sum }}</span> new peak{{ bagger.loc_stats__count_365_unique__sum|pluralize }} &bull;
                                <span class="blue">{{ bagger.loc_stats__count_365__sum }}</span> total summit{{ bagger.loc_stats__count_365__sum|pluralize }}
{#                            {% endif %}#}
                        </span>
                        </h2>
                        <ul class="peaks_bagged">
                            {% summits_filter bagger timefilter country region %}
                        </ul><!-- END peaks_bagged -->
                    </div><!-- END content -->
                </div>
            </li>
        {% endif %}

        {% if timefilter == "all" and bagger.loc_stats__count_all__sum > 0 %}
            <li class="clearfix">
                <div class="clearfix wrapper">
                    <div class="avatar">
                        <a href="{% url "user_profile" bagger %}" class="avatarLink" style="text-decoration:none;">
                            {% avatar bagger 180 %}
                            {# <img src="http://placehold.it/250x250" alt="">  #}
                            <div class="overlay" style="position:absolute; top:0; left:0; width:121px; height:121px;">
                                <img src="{{ MEDIA_URL }}img/peakbaggers/overlay.png" alt="">
                                <p id="a" style="position:absolute; top:35px; left:32px; color:#333; font-weight:bold; font-size: 18px;">#{{ forloop.counter|add:from|add:"-10" }}</p>
                            </div>
                        </a>
                    </div>
                    <div class="content clearfix">
                        <h2 class="membername"><a href="{% url "user_profile" bagger %}">{{ bagger }}</a>
                        <span>
{#                            {% if bagger.loc_stats__count_all__sum < bagger.loc_stats__count_all_unique__sum %}#}
{#                                <span class="blue">{{ bagger.loc_stats__count_all_unique__sum }}</span> peak{{ bagger.loc_stats__count_all_unique__sum|pluralize }} &bull;#}
{#                                <span class="blue">{{ bagger.loc_stats__count_all_unique__sum }}</span> total summit{{ bagger.loc_stats__count_all_unique__sum|pluralize }}#}
{#                            {% else %}#}
                                <span class="blue">{{ bagger.loc_stats__count_all_unique__sum }}</span> peak{{ bagger.loc_stats__count_all_unique__sum|pluralize }} &bull;
                                <span class="blue">{{ bagger.loc_stats__count_all__sum }}</span> total summit{{ bagger.loc_stats__count_all__sum|pluralize }}
{#                            {% endif %}#}
                        </span>
                        </h2>
                        <ul class="peaks_bagged">
                            {% summits_filter bagger timefilter country region %}
                        </ul><!-- END peaks_bagged -->
                    </div><!-- END content -->
                </div>
            </li>
        {% endif %}

    {% endfor %}
    </ul>

    {% if not baggers %}
    <div class="notfound-summits" id="notmorefound">No one has summited peaks in {% if region %}{% get_region region %}{% else %}{% if country  %}{% get_country country %}{% endif %}{% endif %}{% if timefilter == 'all' %}.{% else %} in the last {{ timefilter }} days. Get out there!{% endif %}</div>
    {% else %}


    {% endif %}

    <!-- END baggers -->



