{% load thumbnail account_tags %}
<ul class="vert white_boxes_list">
    {% for list in lists|slice:":5" %}
        <li class="block">
            <h3 class="block">
                <a href="{{ list.0.get_absolute_url }}" style="display: inline-block;float: left; margin-right: 10px;">{{ list.0.name }}</a>
                {% if list.3 < 30 %}
                    <span class="percentContainer">
                        <span class="percent" style="width: {{ list.3 }}%; display: inline-block; float: left;"></span>
                        <p style="display: inline-block; font-size: 10px; color: #000; float: left; line-height: 23px; margin-left: 5px; padding: 0; width: auto;">{{ list.3 }}%</p>
                    </span>
                {% else %}
                    <span class="percentContainer">
                        <span class="percent" style="width: {{ list.3 }}%;font-size: 10px;font-weight: normal;">{{ list.3 }}%</span>
                    </span>
                {% endif %}
                <span class="count-bagged" style="display: inline-block; margin-left: 15px; font-size: 12px; font-weight: normal; line-height: 24px;">{{ list.2 }} summited out of {{ list.1 }}</span>
            </h3>
            <ul class="view_by clearfix">
                <li>View:</li>
                <li><a href="#all" class="view-all filter current">All</a></li>
                <li><a href="#claimed" class="view-claimed filter">Summited</a></li>
                <li><a href="#unclaimed" class="view-unclaimed filter">Unsummited</a></li>
            </ul>
            <ul class="horz clearfix peak-list-by-user" id="userProfile_peakList">
                {% for peak in list.0.get_items_order_by_elevation %}
                    <li class="peakListItem {% user_summited profile.user peak.id %}" original-title="<span>{{ peak.name }}</span><span>{{ peak.get_elevation }}</span>">
                        <a id="peak-list-item" style="margin-bottom: -2px; display: block;" class="{% user_summited profile.user peak.id %}" href="{{ peak.get_absolute_url }}">
                            <img src="{% thumbnail peak.get_thumbnail 75x55 crop %}" width="75" height="55" alt="{{ peak.name }}">
                        </a>
                    </li>
                {% endfor %}
            </ul>
        </li>
    {% empty %}
        {% if first_time %}
            {{ profile }} has started no peak lists yet
        {% endif %}
    {% endfor %}
    {% if lists and first_time %}
        <li id="more-block">
            <a id="seemore-lists" class="seemore" name="5" rel="5" tabindex="{{ lists_count }}" href="{% url "peaklists_by_user" username %}">see more…</a>
        </li>
    {% endif %}
    <script type="text/javascript">
        $('li.peakListItem').tipsy({gravity: 's', html: true});
    </script>
</ul>