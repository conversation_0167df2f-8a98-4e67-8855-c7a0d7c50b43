{% extends "base.html" %}

{% block extrajs %}
<script type="text/javascript">
    $(document).ready(function(){
        $(".peak_desc1 p").linkify();
    });
</script>
{% endblock %}

{% load thumbnail %}
{% load avatar_tags %}

{% block title %}{{ group }} - Top Pursuers{% endblock %}
{% block titlemeta %}{{ group }} - Top Pursuers - {% endblock %}

{% block content %}

<div class="peak_lists">

    <div class="peak_section_desc1">
        <h1><a href="{{ group.get_absolute_url }}">{{ group.name }}</a> - Top Pursuers <span class="metadata">{{ group.items.count }} peaks</span></h1>
        <img src="{% thumbnail group.get_thumbnail 340x262 crop%}" alt=""/>
        <p>{{ group.description }}</p>
    </div>

    <!--IN PROGRESS LEADERS-->
            {% if leaders %}
            <div class="list_section">
                <h2>Top Pursuers</h2>
                {% for l in leaders %}
                <ul class="users_lists_all">
                    <li>
                        <div class="avatar">
                            <a href="{% url "user_profile" l.user.username %}">{% avatar l.user 100 %}</a>
                        </div>
                        <div class="data">
                            <a class="nickname" href="{% url "user_profile" l.user.username %}"> {{ l.user }}</a>
                            <p><a href="{{ l.lastest_summit.item.get_absolute_url }}">{{ l.lastest_summit.item }} on {% if l.lastest_summit.date_entered %}{{ l.lastest_summit.date|date:"b j, Y"|title }}{% else %}date unknown{% endif %}</a></p>
                        {% if l.completed.3 < 30 %}
                            <span class="percentContainer">
                                            <span class="percent"
                                                  style="width: {{ l.completed.3 }}%; display: inline-block; float: left;"></span>
                                            <p style="display: inline-block; font-size: 10px; color: #000; float: left; line-height: 23px; margin-left: 5px; padding: 0; width: auto;">{{ l.completed.3 }}%</p>
                                        </span>
                        {% else %}
                            <span class="percentContainer">
                               <span class="percent" style="width: {{ l.completed.3 }}%;font-size: 10px;font-weight: normal;">{{ l.completed.3 }}%</span>
                            </span>
                        {% endif %}
                        <p class="completed_list">{{ l.completed.2 }} of {{ l.completed.1 }}</p>
                        </div><!-- END data -->
                    </li>
                </ul>
                {% endfor %}
            </div>
            {% endif %}
</div>

<div id="explore_pagination">
    <div class="results">
        <p style="font-size: 14px">results {{ ini }} - {% if end > leaders_count %} {{ leaders_count }}{% else %} {{ end }} {% endif %} of {{ leaders_count }}</p>
    </div>
    <div class="links">
        <p style="font-size: 16px">
            {% if previous != 0 %}<a href="?page={{ previous }}{{ getvars }}">&laquo; previous</a> {% endif %}
            {% if end < leaders_count %} <a href="?page={{ next }}{{ getvars }}">next &raquo;</a>{% endif %}
        </p>
    </div>
</div>

{% endblock %}
