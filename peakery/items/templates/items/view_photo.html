{% load thumbnail %}
{% comment %}<script type="text/javascript">
    $(document).ready(function(){
        $("div#photo").click(function(){ jQuery(document).trigger('close.facebox') });
    });
</script>{% endcomment %}
{% comment %}<div id="photo-lb">
    <div class="img" style="background-image: url('{% thumbnail photo.image 650x410 crop %}');">
        <a href="{% url "user_profile" photo.user %}">{{ photo.user }}</a>
    </div>
{#    <img src="{% thumbnail photo.image 650x410 %}" alt="{{ photo.caption }}" title="{{ photo }}" />#}
    <div class="caption">
        <p>{{ photo.caption|default:" " }}</p>
    </div>
    <div class="controls clearfix">
        <a href="javascript:void(0)" id="previous" class="navButton">Previous</a>
        <a href="javascript:void(0)" id="next" class="navButton">Next</a>
    </div>
</div>{% endcomment %}
<!-- SUMMIT PHOTOS LIGHTBOX -->
<div id="photo-lb">
    <div id="lightboxSlider" class="lightboxSlider">
        <input type="hidden" value="{{ peak_photos|length }}" id="item_photos_count_x">
        <ul id="lightboxXXX" style="height: 460px; overflow: hidden;">
            {% for photo in peak_photos|slice:":18" %}
                <li>
                    <div class="img" style="background-image: url('{% thumbnail photo.image 650x410 crop %}');">
                        <a href="{% url "user_profile" photo.summit_log.user %}">{{ photo.summit_log.user }}</a>
                    </div>
                    <div class="caption">
                        {% if photo.caption %}
                            <p class="caption">{{ photo.caption }}</p>
                        {% endif %}
                    </div>
                </li>
            {% endfor %}
        </ul>
    </div>
    {% comment %}<div class="controls clearfix">
        <a href="javascript:void(0)" id="previous" class="navButton">Previous</a>
        <a href="javascript:void(0)" id="next" class="navButton">Next</a>
    </div>{% endcomment %}
</div>
<style type="text/css">
    ul#lightbox li {
        width: 650px;
        height: 500px;
    }
</style>
<!-- END SUMMIT PHOTOS LIGHTBOX -->
<script type="text/javascript">
    $(document).ready(function(){
        var photos = 15 - parseInt($("input#item_photos_count_x").val());
        var currentImage = parseInt({{ photo_id }});
        if (photos > 0) {
            $.get("{% url "get_panoramio_pics" peak.id 1 %}", {q:photos}, function(data) {
                $("ul#lightboxXXX").append(data);
                $('li.panoramioImage').css('display','block');
                $('div.lightboxSlider').easySlider({
                    prevId: 'previous',
                    nextId: 'next',
                    controlsBefore:	'<div class="controls clearfix">',
                    controlsAfter: '</div>',
                    continuous: true,
                    currentImg: currentImage
                });
            });
        } else {
            $('div.lightboxSlider').easySlider({
                prevId: 'previous',
                nextId: 'next',
                controlsBefore:	'<div class="controls clearfix">',
                controlsAfter: '</div>',
                continuous: true,
                currentImg: currentImage
            });
        }
    });
</script>