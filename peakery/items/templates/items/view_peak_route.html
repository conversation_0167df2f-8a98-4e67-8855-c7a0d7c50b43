{% extends "base.html" %}

{% load static %}
{% load avatar_tags item_tags favorite_tags truncatechars verbatim %}
{% load json_filters %}
{% load humanize %}

{% block title %}{{ peak.name }} - {{ peak.get_ubication_names_title }}{% endblock %}
{% block titlemeta %}{{ peak.name }} - {{ peak.get_ubication_names_title }} -  {% endblock %}
{% block description %}{{ peak.meta_description|striptags|truncatewords:160 }}{% endblock %}
{#{% block image_rel %}{% thumbnail peak.thumbnail "120x120" as im %}{{ im.url }}{% endthumbnail %}{% endblock %}#}
{% block image_rel %}thumbnail peak.thumbnail 120x120{% endblock %}

{% block mapbox %}
    {% include "mapbox/mapbox.html" %}
    <script src="{% static 'js/GPXParser.js' %}"></script>
{% endblock %}

{% block meta_facebook %}
    <meta property="og:title" content="{{ peak.name }}"/>
    <meta property="og:site_name" content="peakery.com"/>
    <meta property="og:type" content="article"/>
    <meta property="og:url" content="{{ peak.get_absolute_url }}"/>
    {#    <meta property="og:image" content="{% thumbnail peak.get_thumbnail "120x120" as im %}{% endthumbnail %}"/>#}
    <meta property="og:image" content="thumbnail peak.get_thumbnail 120x120"/>
    <meta property="og:description" content="{{ peak.meta_description|striptags|truncatewords:160 }}"/>
{% endblock %}

{% block js_globals %}
    var peakObject = '{"id": "{{ peak.id }}", "name": {{ peak.name|jsonify|escape_single_quotes }}, "slug": "{{ peak.slug_new_text }}", "is_classic": "{{ peak.is_classic }}", "lat": "{{ peak.lat }}", "lng": "{{ peak.long }}", "summit_count": "{{ peak.summitlog_count }}", "your_summits": "{{ your_summits_count }}", "your_attempts": "{{ your_attempts_count }}", "challenge_count": "{{ challenge_count }}", "elevation": "{{ peak.elevation }}", "prominence": "{{ peak.prominence }}", "thumbnail_url": "{{ peak.get_thumbnail_480 }}", "region": [{% if peak.region %}{% for r in peak.region.all %}{"region_id": "{{ r.id }}", "region_name": "{{ r.name }}", "region_slug": "{{ r.get_absolute_url }}", "country_name": "{{ r.country.name }}"}{% if not forloop.last %},{% endif %}{% endfor %}{% endif %}], "country": [{% if peak.country %}{% for c in peak.country.all %}{"country_id": "{{ c.id }}", "country_name": "{{ c.name }}", "country_slug": "{{ c.get_absolute_url }}"}{% if not forloop.last %},{% endif %}{% endfor %}{% endif %}]}';
{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row hidden-xs">
        <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 65px; border-top-left-radius:8px; border-top-right-radius:8px;"><span
                style="font-size: 24px; font-weight: 600;">
            <div id="breadcrumbs">
                <div class="pull-right hidden-xs">
                    <a class="region-header-sub-links" href="/{{ peak.slug_new_text }}/">Info</a>
                    <a class="region-header-sub-links"
                       href="/{{ peak.slug_new_text }}/map/">Map</a>
                    <a class="region-header-sub-links"
                       href="/{{ peak.slug_new_text }}/summits/">Climbs</a>
                    {% if your_summit_count != 0 %}
                        <a href="/{{ peak.slug_new_text }}/summits/#month=&member=you&route=&sort=most_recent&page=1"
                           class="youve-climbed" style="margin-left: 0px;">(You {{ your_summit_count }}x)</a>
                    {% endif %}
                </div>
                {% if alternate_names_list %}
                    <div class="ellipsis" style="line-height: 55px;overflow: hidden;">
                    <span class="peak-title">{{ peak.name }}<br><div class="ellipsis"
                                                                     style="font-size: 11px;color: #999999;margin-top: -32px;font-weight: 100;">also known as {% for a in alternate_names_list %}
                        {{ a.name }}{% if not forloop.last %}, {% endif %}{% endfor %}</div></span>
                </div>
                {% else %}
                    <div class="ellipsis" style="line-height: 70px; overflow: hidden;">
                     <span class="peak-title">{{ peak.name }}</span>
                </div>
                {% endif %}
            </div>
        </span></div>
    </div>
{% endblock %}

{% block content %}

    <style type="text/css">

        .switch {
            position: relative;
            display: inline-block;
            width: 46px;
            height: 25px;
            margin-bottom: 0px;
            margin-top: 5px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            -webkit-transition: .4s;
            transition: .4s;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 17px;
            width: 17px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            -webkit-transition: .4s;
            transition: .4s;
        }

        input:checked + .slider {
            background-color: #2196F3;
        }

        input:focus + .slider {
            box-shadow: 0 0 1px #2196F3;
        }

        input:checked + .slider:before {
            -webkit-transform: translateX(20px);
            -ms-transform: translateX(20px);
            transform: translateX(20px);
        }

        /* Rounded sliders */
        .slider.round {
            border-radius: 25px;
        }

        .slider.round:before {
            border-radius: 50%;
        }

        .blueimp-gallery > .description {
            position: absolute;
            bottom: 0px;
            width: 100%;
            text-align: center;
            color: #fff;
            margin-bottom: 2%;
            height: auto;
            display: none;
        }

        .blueimp-gallery-controls > .description {
            display: block;
        }

        .blueimp-gallery-controls > .description > .description-text {
            padding: 10px;
            background: rgba(0, 0, 0, 0.5);
        }

        #divSortPeaksElevation:hover {
            background-color: #ccc;
        }

        .marker-photo-icon {
            cursor: pointer;
            box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px 0px;
        }

        .filtered > i {
            color: #f24000;
        }

        .unfiltered > i {
            color: #333;
        }

        #peak-search-mobile > i {
            line-height: 30px;
            margin-left: 9px;
            font-size: 12px;
        }

        #peak-search-layers > i {
            line-height: 30px;
            margin-left: 7px;
            color: #333;
            font-size: 16px;
        }

        #peak-search-legend > i {
            line-height: 30px;
            margin-left: 6px;
            font-size: 18px;
            color: #333;
        }

        #gm-custom-mapfilter, #gm-custom-maplayers, #gm-custom-maplegend, #gpx_download_div {
            -moz-box-shadow: 0 0 2px rgba(0, 0, 0, .1);
            -webkit-box-shadow: 0 0 2px rgba(0, 0, 0, .1);
            box-shadow: 0 0 0 2px rgba(0, 0, 0, .1);
        }

        #peak-search-3d {
            cursor: pointer;
            font-size: 16px;
            color: #333;
            font-weight: 700;
            line-height: 30px;
            text-align: center;
            margin-left: 4px;
        }

        @media screen and (min-width: 1024px) {
            #gm-custom-maplegend {
                bottom: 145px;
            }

            #gm-custom-map3d {
                bottom: 278px;
            }
        }

        @media screen and (min-width: 1px) and (max-width: 1023px) {
            #gm-custom-maplegend {
                top: 100px;
            }

            #gm-custom-map3d {
                top: 145px;
            }
        }

        .mapboxgl-ctrl {
            margin-bottom: 15px !important;
        }

        #gm-custom-mapdropdown, #gm-custom-mapbutton {
            opacity: 1;
            webkit-backdrop-filter: blur(5px);
            backdrop-filter: blur(5px);
        }

        #gm-custom-mapunits:hover {
            background-color: transparent !important;
        }

        #gm-custom-mapbutton, #gm-custom-mapdropdown {
            border: 2px solid rgba(0, 0, 0, 0.15);
        }

        #gm-custom-mapbutton {
            width: 180px;
            margin-left: 90px;
            border-top-right-radius: 8px;
            border-top-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom-left-radius: 8px;
        }

        #gm-custom-mapbutton:hover {
            border-bottom-right-radius: 0px;
            border-bottom-left-radius: 0px;
        }

        #gm-custom-mapdropdown {
            width: 179px;
            margin-left: 91px;
            border-bottom-right-radius: 8px;
            border-bottom-left-radius: 8px;
        }

        #gm-custom-mapoption-terrain, #gm-custom-mapoption-natatl, #gm-custom-mapoption-outdoors, #gm-custom-mapoption-streets, #gm-custom-mapoption-topo, #gm-custom-mapoption-satstreets, #gm-custom-mapoption-sat {
            width: 175px;
            margin-left: -2px;
            border-bottom: solid 1px #f2f2f2;
        }

        #gm-custom-mapoption-3d {
            width: 175px;
            margin-left: -2px;
            border-bottom: solid 1px #f2f2f2;
        }

        #gm-custom-mapoption-3d {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
        }

        #gm-custom-mapoption-streets:hover {
            background-color: rgb(235, 235, 235) !important;
            color: rgb(0, 0, 0) !important;
        }

        #search-peaks-btn[disabled] {
            -webkit-text-fill-color: #fff;
            color: #fff;
        }

        @media screen and (max-width: 767px) and (min-width: 1px) {
            #content-body {
                margin-top: 3px;
            }

            #peaks-map {
                top: 0px;
            }

            #gm-custom-maptype {
                right: 46% !important;
            }

            .mapboxgl-ctrl-bottom-right {
                bottom: 0px;
            }

            .mapboxgl-ctrl-geolocate, .mapboxgl-ctrl-fullscreen {
                width: 36px !important;
                height: 36px !important;
            }

            #gm-custom-maplegend, #gm-custom-maplayers, #gm-custom-mapfilter, #gm-custom-map3d, #gpx_download_div {
                width: 36px !important;
                height: 36px !important;
                padding-top: 3px;
                padding-left: 3px;
            }
        }

        @media screen and (min-width: 768px) {
            #peak-search {
                top: 0px;
            }

            #gm-custom-maplegend-dropdown {
                width: 320px !important;
                left: 20px !important;
            }
        }

        #photos-list {
            background-color: #ccc;
        }

        #navbar-link-peaks > a:hover {
            color: #ffffff;
        }

        @media screen and (min-width: 1280px) and (max-width: 1439px) {
            #route-title-bar {
              /*  max-width: 1280px; */
            }
        }

        @media screen and (max-width: 767px) and (min-width: 1px) {
            #log-your-climb {
                margin-left: 0px;
            }

           #content-body {
               margin-top: 20px;
           }

            .route-name {
                line-height: 18px;
                margin-top: 0px;
                margin-bottom: 0px;
                display: flex;
                justify-content: center;
                flex-direction: column;
                height: 50px;
            }
        }

        @media screen and (max-width: 1023px) and (min-width: 768px) {
            #log-your-climb {
                margin-left: 20px;
                padding: 15px 10px;
                width: 120px;
            }

            .peak-title {
                font-size: 16px;
                font-weight: 600;
            }

            .route-name {
                line-height: 22px;
                margin-top: 0px;
                margin-bottom: 0px;
                display: flex;
                justify-content: center;
                flex-direction: column;
                height: 60px;
            }
        }

        @media screen and (min-width: 1024px) {
            #log-your-climb {
                margin-left: 50px;
                width: 160px;
            }

            .peak-title {
                font-size: 20px;
                font-weight: 600;
            }

            .route-name {
                line-height: 28px;
                margin-top: 0px;
                margin-bottom: 0px;
                display: flex;
                justify-content: center;
                flex-direction: column;
                height: 70px;
            }

            #gm-custom-mapunits {
                right: 142px;
            }
        }

        @media screen and (max-width: 767px) and (min-width: 1px) {


            .content-pane {
                margin-top: 100px;
            }

            .peak-title {
                font-size: 12px;
                font-weight: 500;
            }

            .mobile-regions-subnav-fixed {
                margin-top: 50px;
            }

            .more-info-page-title, .more-info-page-url {
                height: 50px;
            }

            .more-info-image {
                width: 133px;
                height: 100px;
            }

            #selectMobileRoutefilter {
                margin-top: 7px;
            }

            #route-title {
                height: 50px;
                line-height: 50px;
                background-color: #fff;
            }

            #route-title-bar {
                top: 120px;
            }

            #rank-circle {
                margin-top: 12px;
                margin-left: -5px;
                margin-right: 5px;
                padding-right: 10px;
            }

            #edit-route-link-div {
                font-size: 12px;
                margin-left: 10px;
            }

            #distance-to-summit-header {
                border-right: 1px solid #c0c0c0;
                border-bottom: 1px solid #c0c0c0;
            }

            #elevation-start-header {
                border-bottom: 1px solid #c0c0c0;
            }

            #elevation-gain-header {
                border-right: 1px solid #c0c0c0;
                border-bottom: 1px solid #c0c0c0;
            }

            #time-to-summit-header {
                border-bottom: 1px solid #c0c0c0;
            }

            #start-location-header {
                border-right: 1px solid #c0c0c0;
                border-bottom: 1px solid #c0c0c0;
            }

            #difficulty-header {
                border-bottom: 1px solid #c0c0c0;
            }

            #summits-header {
                border-right: 1px solid #c0c0c0;
                border-bottom: 1px solid #c0c0c0;
            }

            #last-summit-header {
                border-bottom: 1px solid #c0c0c0;
            }

            #gm-custom-mapunits {
                left: 114px;
            }
        }

        @media screen and (max-width: 1023px) and (min-width: 768px) {
            .content-pane {
                margin-top: 61px;
            }

            .more-info-page-title, .more-info-page-url {
                height: 75px;
            }

            .more-info-image {
                width: 200px;
                height: 150px;
            }

            #peak-title {
                font-size: 18px;
            }

            #selectMobileRoutefilter {
                margin-top: 7px;
            }

            #route-title {
                height: 60px;
                line-height: 60px;
                background-color: #f2f2f2;
            }

            #rank-circle {
                margin-top: 17px;
                padding-right: 15px;
            }

            #edit-route-link-div {
                font-size: 14px;
                margin-left: 20px;
            }

            #distance-to-summit-header {
                border-right: 1px solid #c0c0c0;
                border-bottom: 1px solid #c0c0c0;
            }

            #elevation-start-header {
                border-right: 1px solid #c0c0c0;
                border-bottom: 1px solid #c0c0c0;
            }

            #elevation-gain-header {
                border-bottom: 1px solid #c0c0c0;
            }

            #time-to-summit-header {
                border-right: 1px solid #c0c0c0;
                border-bottom: 1px solid #c0c0c0;
            }

            #start-location-header {
                border-right: 1px solid #c0c0c0;
                border-bottom: 1px solid #c0c0c0;
            }

            #difficulty-header {
                border-bottom: 1px solid #c0c0c0;
            }

            #summits-header {
                border-right: 1px solid #c0c0c0;
                border-bottom: 1px solid #c0c0c0;
            }

            #last-summit-header {
                border-right: 1px solid #c0c0c0;
                border-bottom: 1px solid #c0c0c0;
            }

            #gm-custom-mapunits {
                left: 114px;
            }
        }

        @media screen and (min-width: 1024px) {
            .content-pane {
                margin-top: 71px;
            }

            .more-info-page-title, .more-info-page-url {
                height: 75px;
            }

            .more-info-image {
                width: 200px;
                height: 150px;
            }

            #peak-title {
                font-size: 22px;
            }

            div.description {
                font-size: 18px;
            }

            .mobile-regions-subnav-fixed {
                margin-top: 0px;
            }

            #selectMobileRoutefilter {
                margin-top: 0px;
            }

            #route-title {
                height: 70px;
                line-height: 70px;
                background-color: #f2f2f2;
            }

            #rank-circle {
                margin-top: 21px;
                padding-right: 20px;
            }

            #edit-route-link-div {
                font-size: 14px;
                margin-left: 20px;
            }

            #distance-to-summit-header {
                border-right: 1px solid #c0c0c0;
                border-bottom: 1px solid #c0c0c0;
            }

            #elevation-start-header {
                border-right: 1px solid #c0c0c0;
                border-bottom: 1px solid #c0c0c0;
            }

            #elevation-gain-header {
                border-right: 1px solid #c0c0c0;
                border-bottom: 1px solid #c0c0c0;
            }

            #time-to-summit-header {
                border-bottom: 1px solid #c0c0c0;
            }

            #start-location-header {
                border-right: 1px solid #c0c0c0;
                border-bottom: 1px solid #c0c0c0;
            }

            #difficulty-header {
                border-right: 1px solid #c0c0c0;
                border-bottom: 1px solid #c0c0c0;
            }

            #summits-header {
                border-right: 1px solid #c0c0c0;
                border-bottom: 1px solid #c0c0c0;
            }

            #last-summit-header {
                border-bottom: 1px solid #c0c0c0;
            }
        }

        #chart_div {
            cursor: pointer;
        }

        .table > thead > tr > th {
            border-bottom: none;
            padding-left: 10px;
            padding-top: 0px;
            padding-bottom: 0px;
            padding-right: 0px;
        }

        .table > tbody > tr > td, .table > tfoot > tr > td {
            border-top: none;
            padding-left: 10px;
            padding-top: 0px;
            padding-bottom: 0px;
            padding-right: 0px;
        }

        .gm-style-mtc {
            opacity: .8;
        }

        .gm-pad-left {
            padding-left: 20px;
            margin-right: 30px !important;
        }

        .table {
            margin-bottom: 0px;
        }

        .google-visualization-tooltip {
            border: solid 2px #ff0000;
        }

        div.peak_elevation_canvas svg g g g g rect {
            stroke-width: 0px;
            fill: red;
        }

        .blueimp-gallery > .description {
            position: absolute;
            bottom: 0px;
            width: 100%;
            text-align: center;
            color: #fff;
            margin-bottom: 2%;
            height: auto;
            display: none;
        }

        .blueimp-gallery-controls > .description {
            display: block;
        }

        .blueimp-gallery-controls > .description > .description-text {
            padding: 10px;
            background: rgba(0, 0, 0, 0.5);
        }

        .map-tooltip-info {
            background-color: rgba(0, 0, 0, .6);
            -webkit-backdrop-filter: blur(0px) !important;
        }

        .toggle-switch {
            height: 40px;
        }

        .toggle-switch label {
            line-height: 40px;
            color: #666;
        }

        .marker_icon:hover, .marker_icon_red:hover, .marker_icon_green:hover, .marker_icon_redgreen:hover, .marker_icon_yellow:hover, .marker_icon_purple:hover, .marker_icon_peak:hover, .marker-icon-hover {
            background-image: url('{% static 'img/<EMAIL>' %}') !important;
            height: 28px;
            width: 28px;
            -webkit-animation-name: markerPulse;
            -webkit-animation-duration: 3s;
            -webkit-animation-iteration-count: infinite;
        }

        .marker_icon_classic {
            background-image: url({% static '' %}img/<EMAIL>);
            height: 28px;
            width: 28px;
        }

        .marker_icon_firstascent {
            background-image: url({% static '' %}img/<EMAIL>);
            height: 28px;
            width: 28px;
        }

        .marker_icon_kom {
            background-image: url({% static '' %}img/<EMAIL>);
            height: 28px;
            width: 28px;
        }

        .marker_icon_steward {
            background-image: url({% static '' %}img/<EMAIL>);
            height: 28px;
            width: 28px;
        }

        @-webkit-keyframes markerPulse {
            from {
                -webkit-filter: brightness(1.2) saturate(1.5);
            }
            50% {
                -webkit-filter: brightness(0.9) saturate(1);
            }
            to {
                -webkit-filter: brightness(1.2) saturate(1.5);
            }
        }

        /*tooltip animations*/
        .scale-in-tl {
            -webkit-animation: scale-in-tl .1s cubic-bezier(0.50, 1.3, .70, 1) both;
            animation: scale-in-tl .1s cubic-bezier(0.50, 1.3, .70, 1) both;
            opacity: 1;
        }

        @-webkit-keyframes scale-in-tl {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
                opacity: 1;
            }
        }

        @keyframes scale-in-tl {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
                opacity: 1;
            }
        }

        .scale-out-tl {
            -webkit-animation: scale-out-tl .1s cubic-bezier(0.3, .05, .5, .1) both;
            animation: scale-out-tl .1s cubic-bezier(0.3, .05, .5, .1) both;
            opacity: 0;
        }

        @-webkit-keyframes scale-out-tl {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
                opacity: 1;
            }
        }

        @keyframes scale-out-tl {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
                opacity: 1;
            }
        }

        .scale-in-tm {
            -webkit-animation: scale-in-tm .1s cubic-bezier(0.50, 1.3, .70, 1) both;
            animation: scale-in-tm .1s cubic-bezier(0.50, 1.3, .70, 1) both;
            opacity: 1;
        }

        @-webkit-keyframes scale-in-tm {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
                opacity: 1;
            }
        }

        @keyframes scale-in-tm {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
                opacity: 1;
            }
        }

        .scale-out-tm {
            -webkit-animation: scale-out-tm .1s cubic-bezier(0.3, .05, .5, .1) both;
            animation: scale-out-tm .1s cubic-bezier(0.3, .05, .5, .1) both;
            opacity: 0;
        }

        @-webkit-keyframes scale-out-tm {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
                opacity: 1;
            }
        }

        @keyframes scale-out-tm {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
                opacity: 1;
            }
        }

        .scale-in-tr {
            -webkit-animation: scale-in-tr .1s cubic-bezier(0.50, 1.3, .70, 1) both;
            animation: scale-in-tr .1s cubic-bezier(0.50, 1.3, .70, 1) both;
            opacity: 1;
        }

        @-webkit-keyframes scale-in-tr {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
                opacity: 1;
            }
        }

        @keyframes scale-in-tr {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
                opacity: 1;
            }
        }

        .scale-out-tr {
            -webkit-animation: scale-out-tr .1s cubic-bezier(0.3, .05, .5, .1) both;
            animation: scale-out-tr .1s cubic-bezier(0.3, .05, .5, .1) both;
            opacity: 0;
        }

        @-webkit-keyframes scale-out-tr {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
                opacity: 1;
            }
        }

        @keyframes scale-out-tr {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
                opacity: 1;
            }
        }

        /*bottom*/
        .scale-in-bl {
            -webkit-animation: scale-in-bl .1s cubic-bezier(0.50, 1.3, .70, 1) both;
            animation: scale-in-bl .1s cubic-bezier(0.50, 1.3, .70, 1) both;
            opacity: 1;
        }

        @-webkit-keyframes scale-in-bl {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
                opacity: 1;
            }
        }

        @keyframes scale-in-bl {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
                opacity: 1;
            }
        }

        .scale-out-bl {
            -webkit-animation: scale-out-bl .1s cubic-bezier(0.3, .05, .5, .1) both;
            animation: scale-out-bl .1s cubic-bezier(0.3, .05, .5, .1) both;
            opacity: 0;
        }

        @-webkit-keyframes scale-out-bl {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
                opacity: 1;
            }
        }

        @keyframes scale-out-bl {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
                opacity: 1;
            }
        }

        .scale-in-bm {
            -webkit-animation: scale-in-bm .1s cubic-bezier(0.50, 1.3, .70, 1) both;
            animation: scale-in-bm .1s cubic-bezier(0.50, 1.3, .70, 1) both;
            opacity: 1;
        }

        @-webkit-keyframes scale-in-bm {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
                opacity: 1;
            }
        }

        @keyframes scale-in-bm {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
                opacity: 1;
            }
        }

        .scale-out-bm {
            -webkit-animation: scale-out-bm .1s cubic-bezier(0.3, .05, .5, .1) both;
            animation: scale-out-bm .1s cubic-bezier(0.3, .05, .5, .1) both;
            opacity: 0;
        }

        @-webkit-keyframes scale-out-bm {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
                opacity: 1;
            }
        }

        @keyframes scale-out-bm {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
                opacity: 1;
            }
        }

        .scale-in-br {
            -webkit-animation: scale-in-br .1s cubic-bezier(0.50, 1.3, .70, 1) both;
            animation: scale-in-br .1s cubic-bezier(0.50, 1.3, .70, 1) both;
            opacity: 1;
        }

        @-webkit-keyframes scale-in-br {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
                opacity: 1;
            }
        }

        @keyframes scale-in-br {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
                opacity: 1;
            }
        }

        .scale-out-br {
            -webkit-animation: scale-out-br .1s cubic-bezier(0.3, .05, .5, .1) both;
            animation: scale-out-br .1s cubic-bezier(0.3, .05, .5, .1) both;
            opacity: 0;
        }

        @-webkit-keyframes scale-out-br {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
                opacity: 1;
            }
        }

        @keyframes scale-out-br {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
                opacity: 1;
            }
        }

        .mapboxgl-ctrl-attrib {
            display: none !important;
        }

    </style>

    <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>

    <div class="container">
        <!-- Mobile header -->
        <div class="row sub-header-row hidden-lg hidden-md hidden-sm"
             style="height: 50px; position: fixed; width: 100%; z-index: 999;">
            <div class="col-md-12"
                 style="height: 50px; line-height: 50px; font-size: 12px; color: #999; display: flex; justify-content: space-between;">
                <span><a class="mobile-header-sub-links"
                         href="/{{ peak.slug_new_text }}/">Info</a></span>
                {% if IS_MOBILE_APP_ACCESS == 'True' %}
                    <span><a id="mobile-app-map-link"
                             class="mobile-header-sub-links">Map</a></span>
                {% else %}
                    <span><a class="mobile-header-sub-links"
                             href="/{{ peak.slug_new_text }}/map/">Map</a></span>
                {% endif %}
                <span><a class="mobile-header-sub-links"
                         href="/{{ peak.slug_new_text }}/summits/">Climbs</a>
                    {% if your_summit_count != 0 %}
                        <a class="mobile-header-sub-links youve-climbed"
                           href="/{{ peak.slug_new_text }}/summits/#type=you"
                           style="margin-left: 0px;">(You {{ your_summit_count }}x)</a></span>
                        {% if IS_MOBILE_APP_ACCESS == 'True' %}
                            <span><a id="log-your-climb" class="mobile-header-sub-links"
                                     href="javascript: Android.logClimb(peakObject);"
                                     style="color: #00b1f2;">Log climb</a></span>
                        {% else %}
                            <span><a id="log-your-climb" class="mobile-header-sub-links"
                                     href="{% url "log_climb_multi_simple" %}?peak={{ peak.id }}"
                                     style="color: #00b1f2;">Log climb</a></span>
                        {% endif %}
                    {% else %}
                        {% if user.is_authenticated %}
                            {% if IS_MOBILE_APP_ACCESS == 'True' %}
                                </span><span><a id="log-your-climb" class="mobile-header-sub-links"
                                                href="javascript: Android.logClimb(peakObject);"
                                                style="color: #00b1f2;">Log climb</a></span>
                            {% else %}
                                </span><span><a id="log-your-climb" class="mobile-header-sub-links"
                                                href="{% url "log_climb_multi_simple" %}?peak={{ peak.id }}"
                                                style="color: #00b1f2;">Log climb</a></span>
                            {% endif %}
                        {% else %}
                            </span><span><a id="log-your-climb" class="mobile-header-sub-links"
                                            style="color: #00b1f2; cursor: pointer;" data-toggle="modal"
                                            data-target="#accounts-login">Log climb</a></span>
                        {% endif %}
                    {% endif %}
            </div>
        </div>
        <!-- End mobile header -->

        <div id="route-title-bar" class="row sub-header-row" style="position: fixed; width: 100%; z-index: 999;">
            <div id="route-title" class="col-md-12">
                <div id="rank-circle" style="float: left;">
                    <span class="memberNumberCircle"
                          style="background-color: {{ rank_color }};"><span>{{ route_rank }}</span></span>
                </div>
                <div id="edit-route-link-div" class="pull-right">
                    {% if user.is_authenticated %}
                        <a id="add-summit-route" class="ajax-link" style="color: #f24100;"
                           href="/peaks/{{ peak.id }}/routes/edit/{{ route.id }}/">edit<span class="hidden-xs"> this route</span></a>
                    {% else %}
                        <a id="add-summit-route" class="ajax-link join-peakery"
                           style="color: #f24100; cursor: pointer;">edit<span class="hidden-xs"> this route</span></a>
                    {% endif %}
                </div>
                <div style="float: left;">
                    <div class="stats-header route-name"><span
                            class="route-name-title">{{ route.name }}</span></div>
                </div>
            </div>
        </div>

        <div class="row sub-header-row content-pane" style="border-bottom: none;">
            <div class="col-md-12">

                <!-- Top photos block -->
                {% if photos %}
                    <div id="photos-list" class="row">
                        {% for p in photos|slice:"0:1" %}
                            <a class="gallery-link" data-user="{{ p.username }}" data-credit=""
                               data-description="{% if p.caption %}{{ p.caption }}{% endif %}" data-gallery
                               href="{{ MEDIA_URL }}{{ p.thumbnail_url }}">
                                <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-1 hover-photos"
                                     style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ MEDIA_URL }}{{ p.thumbnail_url }}'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;">
                                    <div class="top-photos">
                                        <div>
                                            <div>
                                                <img src="{% static 'img/spacer.png' %}"
                                                     class="img-responsive peakeryPhoto photography peakimg-responsive">
                                            </div>
                                        </div>
                                        {% if p.username %}
                                            <div class="user-photo-info">
                                    <span class="data photo-caption"
                                          style="position: absolute; bottom: -5px; left: 10px; color: #fff;">
                                        <p class="bagger" style="font-size: 10px;">{{ p.username }}&nbsp;&bull;&nbsp;<time
                                                class="timeago"
                                                datetime="{{ p.created }}T00:00:00">{{ p.created }}</time></p>
                                    </span>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </a>
                        {% endfor %}
                        {% for p in photos|slice:"1:2" %}
                            <a class="gallery-link" data-user="{{ p.username }}" data-credit=""
                               data-description="{% if p.caption %}{{ p.caption }}{% endif %}" data-gallery
                               href="{{ MEDIA_URL }}{{ p.thumbnail_url }}">
                                <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-2 hover-photos"
                                     style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ MEDIA_URL }}{{ p.thumbnail_url }}'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;">
                                    <div class="top-photos">
                                        <div>
                                            <div>
                                                <img src="{% static 'img/spacer.png' %}"
                                                     class="img-responsive peakeryPhoto photography peakimg-responsive">
                                            </div>
                                        </div>
                                        {% if p.username %}
                                            <div class="user-photo-info">
                                    <span class="data photo-caption"
                                          style="position: absolute; bottom: -5px; left: 10px; color: #fff;">
                                        <p class="bagger" style="font-size: 10px;">{{ p.username }}&nbsp;&bull;&nbsp;<time
                                                class="timeago"
                                                datetime="{{ p.created }}T00:00:00">{{ p.created }}</time></p>
                                    </span>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </a>
                        {% endfor %}
                        {% for p in photos|slice:"2:3" %}
                            <a class="gallery-link" data-user="{{ p.username }}" data-credit=""
                               data-description="{% if p.caption %}{{ p.caption }}{% endif %}" data-gallery
                               href="{{ MEDIA_URL }}{{ p.thumbnail_url }}">
                                <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-3 hidden-xs hover-photos"
                                     style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ MEDIA_URL }}{{ p.thumbnail_url }}'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;">
                                    <div class="top-photos">
                                        <div>
                                            <div>
                                                <img src="{% static 'img/spacer.png' %}"
                                                     class="img-responsive peakeryPhoto photography peakimg-responsive">
                                            </div>
                                        </div>
                                        {% if p.username %}
                                            <div class="user-photo-info">
                                    <span class="data photo-caption"
                                          style="position: absolute; bottom: -5px; left: 10px; color: #fff;">
                                        <p class="bagger" style="font-size: 10px;">{{ p.username }}&nbsp;&bull;&nbsp;<time
                                                class="timeago"
                                                datetime="{{ p.created }}T00:00:00">{{ p.created }}</time></p>
                                    </span>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </a>
                        {% endfor %}
                        {% for p in photos|slice:"3:4" %}
                            <a class="gallery-link" data-user="{{ p.username }}" data-credit=""
                               data-description="{% if p.caption %}{{ p.caption }}{% endif %}" data-gallery
                               href="{{ MEDIA_URL }}{{ p.thumbnail_url }}">
                                <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-4 hidden-xs hidden-sm hover-photos"
                                     style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ MEDIA_URL }}{{ p.thumbnail_url }}'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;">
                                    <div class="top-photos">
                                        <div>
                                            <div>
                                                <img src="{% static 'img/spacer.png' %}"
                                                     class="img-responsive peakeryPhoto photography peakimg-responsive">
                                            </div>
                                        </div>
                                        {% if p.username %}
                                            <div class="user-photo-info">
                                    <span class="data photo-caption"
                                          style="position: absolute; bottom: -5px; left: 10px; color: #fff;">
                                        <p class="bagger" style="font-size: 10px;">{{ p.username }}&nbsp;&bull;&nbsp;<time
                                                class="timeago"
                                                datetime="{{ p.created }}T00:00:00">{{ p.created }}</time></p>
                                    </span>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </a>
                        {% endfor %}
                    </div>
                    <!-- End top photos block -->
                {% endif %}

                <div class="row" style="border-bottom: 1px solid #c0c0c0;">
                    <div id="distance-to-summit-header" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive"
                         style="max-height: 200px; background-color: #fff;">
                        <div>
                            <div class="stats-header">Distance</div>
                            {% if route.distance_to_summit or route.total_distance %}
                                {% if peak.is_usa %}
                                    {% if route.distance_to_summit %}
                                        <p class="stats-data">{% mi_into_mi route.distance_to_summit %} to summit</p>
                                    {% endif %}
                                    {% if route.total_distance %}
                                        <p class="stats-data">{% mi_into_mi route.total_distance %} total</p>
                                    {% endif %}
                                {% else %}
                                    {% if route.distance_to_summit %}
                                        <p class="stats-data">{% mi_into_km route.distance_to_summit %} to summit</p>
                                    {% endif %}
                                    {% if route.total_distance %}
                                        <p class="stats-data">{% mi_into_km route.total_distance %} total</p>
                                    {% endif %}
                                {% endif %}
                            {% else %}
                                <p class="stats-data-missing">no info yet</p>
                            {% endif %}
                        </div>
                    </div>
                    <div id="elevation-start-header" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive"
                         style="max-height: 200px; background-color: #fff;">
                        <div>
                            <div class="stats-header">Elevation</div>
                            {% if route.start_elevation or route.max_elevation %}
                                {% if peak.is_usa %}
                                    {% if route.start_elevation %}
                                        <p class="stats-data elevation-data">{% ft_into_ft route.start_elevation %}
                                            start</p>
                                    {% endif %}
                                    {% if route.max_elevation %}
                                        <p class="stats-data elevation-data">{% ft_into_ft route.max_elevation %}
                                            max</p>
                                    {% endif %}
                                {% else %}
                                    {% if route.start_elevation %}
                                        <p class="stats-data elevation-data">{% ft_into_m route.start_elevation %}
                                            start</p>
                                    {% endif %}
                                    {% if route.max_elevation %}
                                        <p class="stats-data elevation-data">{% ft_into_m route.max_elevation %} max</p>
                                    {% endif %}
                                {% endif %}
                            {% else %}
                                <p class="stats-data-missing">no info yet</p>
                            {% endif %}
                        </div>
                    </div>
                    <div id="elevation-gain-header" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive"
                         style="max-height: 200px; background-color: #fff;">
                        <div>
                            <div class="stats-header">Vertical</div>
                            {% if route.elevation_gain or route.elevation_loss %}
                                {% if peak.is_usa %}
                                    {% if route.elevation_gain %}
                                        <p class="stats-data elevation-data">{% ft_into_ft route.elevation_gain %}
                                            gain</p>
                                    {% endif %}
                                    {% if route.elevation_loss %}
                                        <p class="stats-data elevation-data"
                                           style="color: #ff0000;">{% ft_into_ft route.elevation_loss %} loss</p>
                                    {% endif %}
                                {% else %}
                                    {% if route.elevation_gain %}
                                        <p class="stats-data elevation-data">{% ft_into_m route.elevation_gain %}
                                            gain</p>
                                    {% endif %}
                                    {% if route.elevation_loss %}
                                        <p class="stats-data elevation-data"
                                           style="color: #ff0000;">{% ft_into_m route.elevation_loss %} loss</p>
                                    {% endif %}
                                {% endif %}
                            {% else %}
                                <p class="stats-data-missing">no info yet</p>
                            {% endif %}
                        </div>
                    </div>
                    <div id="time-to-summit-header" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive"
                         style="max-height: 200px; background-color: #fff;">
                        <div>
                            <div class="stats-header">Time</div>
                            {% if summmits_with_trip_time > 0 %}
                                {% if avg_time_to_summit > 0 %}
                                    <p class="stats-data">{% sec_to_hrs_min avg_time_to_summit %} to summit</p>
                                {% endif %}
                                {% if avg_total_trip_time > 0 %}
                                    <p class="stats-data">{% sec_to_hrs_min avg_total_trip_time %} total</p>
                                {% endif %}
                                <p style="color: #999;" class="stats-data-bottom">average based
                                    on {{ summmits_with_trip_time }}
                                    summit{{ summmits_with_trip_time|pluralize:"s" }}</p>
                            {% else %}
                                <p class="stats-data-missing">no info yet</p>
                            {% endif %}
                        </div>
                    </div>
                    <div id="start-location-header" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive"
                         style="max-height: 200px; background-color: #fff;">
                        <div>
                            <div class="stats-header">Getting there</div>
                            {% if route.gpx_file %}
                                <span id="directions-link"></span>
                            {% else %}
                                <p class="stats-data-missing">no info yet</p>
                            {% endif %}
                        </div>
                    </div>
                    <div id="difficulty-header" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive"
                         style="max-height: 200px; background-color: #fff;">
                        <div>
                            <div class="stats-header">Difficulty</div>
                            {% if route.difficulty %}
                                <p class="stats-data">{{ route.difficulty }}</p>
                            {% else %}
                                <p class="stats-data-missing">no info yet</p>
                            {% endif %}
                        </div>
                    </div>
                    <div id="summits-header" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive"
                         style="max-height: 200px; background-color: #fff;">
                        <div>
                            <div class="stats-header">Climbs</div>
                            {% if summits_count %}
                                <p class="stats-data"><a
                                        href="/{{ peak.slug_new_text }}/summits/#month=&member=all&route={{ route.id }}&order=most_recent&page=1">{{ summits_count }}
                                    climb{{ summits_count|pluralize:"s" }}</a></p>
                                <p style="color: #999;" class="stats-data-bottom">#{{ overall_rank }} most climbed
                                    route</p>
                            {% else %}
                                <p class="stats-data-missing">no info yet</p>
                            {% endif %}
                        </div>
                    </div>
                    <div id="last-summit-header" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive"
                         style="max-height: 200px; background-color: #fff;">
                        <div>
                            <div class="stats-header">Last climb</div>
                            {% if summits %}
                                {% for s in summits|slice:"0:1" %}
                                    <p class="stats-data"><a
                                            href="/{{ peak.slug_new_text }}/summits/{{ s.id }}/">{{ s.date|date:"M j, Y"|default:"" }}</a>
                                    </p>
                                    <p class="stats-data">by <a
                                            href="/members/{{ s.username }}/">{{ s.username }}</a></p>
                                {% endfor %}
                            {% else %}
                                <p class="stats-data-missing">no info yet</p>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="row" style="height: 500px; background-color: #fff; border-bottom: solid 1px #cfcfcf;">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" id="peak-map-col"
                         style="padding-right: 0px; padding-left: 0px;">
                        <div id="map-canvas" style="width: 100%; height: 500px;">
                            {% if user.is_authenticated %}
                                {% if IS_MOBILE_APP_ACCESS == 'True' %}
                                    <a href="{{ S3_MEDIA_URL }}{{ route.gpx_file }}">
                                        <div id="gpx_download_div" class="gmnoprint gm-style-mtc"
                                             style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 30px; height: 30px; border-radius: 5px; top: 5px; right: 0px; background-color: #fff; opacity: 1; display: none;">
                                            <div id="gpx_download_link" draggable="false"
                                                 style="direction: ltr; overflow: hidden; text-align: left; position: relative; color: rgb(86, 86, 86); font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 14px; background-color: rgb(255, 255, 255); padding: 8px; border-radius: 5px; -webkit-background-clip: padding-box; background-clip: padding-box; font-weight: 500;">
                                                <i class="fa fa-download" aria-hidden="true"></i>
                                            </div>
                                        </div>
                                    </a>
                                {% else %}
                                    <div id="gpx_download_div"
                                         onclick="openUrl('/peaks/{{ peak.id }}/routes/download/{{ route.id }}');"
                                         class="gmnoprint gm-style-mtc"
                                         style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 30px; height: 30px; border-radius: 5px; top: 5px; right: 0px; background-color: #fff; opacity: 1; display: none;">
                                        <div id="gpx_download_link" draggable="false"
                                             style="direction: ltr; overflow: hidden; text-align: left; position: relative; color: rgb(86, 86, 86); font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 14px; background-color: rgb(255, 255, 255); padding: 8px; border-radius: 5px; -webkit-background-clip: padding-box; background-clip: padding-box; font-weight: 500;">
                                            <i class="fa fa-download" aria-hidden="true"></i>
                                        </div>
                                    </div>
                                {% endif %}
                            {% else %}
                                <div id="gpx_download_div" class="gmnoprint gm-style-mtc" data-toggle="modal"
                                     data-target="#accounts-login"
                                     style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 30px; height: 30px; border-radius: 5px; top: 5px; right: 0px; background-color: #fff; opacity: 1; display: none;">
                                    <div id="gpx_download_link" draggable="false"
                                         style="direction: ltr; overflow: hidden; text-align: left; position: relative; color: rgb(86, 86, 86); font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 14px; background-color: rgb(255, 255, 255); padding: 8px; border-radius: 5px; -webkit-background-clip: padding-box; background-clip: padding-box; font-weight: 500;">
                                        <i class="fa fa-download" aria-hidden="true"></i>
                                    </div>
                                </div>
                            {% endif %}
                            <div id="gm-custom-mapunits" class="gmnoprint gm-style-mtc"
                                 style="display: none; margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 100px; top: 0px;">
                                <div id="gm-custom-mapunitsbutton" draggable="false" title="Change map units"
                                     style="cursor: pointer; direction: ltr; overflow: hidden; text-align: center; position: relative; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 11px; -webkit-background-clip: padding-box; background-clip: padding-box; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; font-weight: 500;">
                                    <span id="gm-custom-mapunitsbutton-label-feet" class="gm-custom-mapunits-selected">Feet</span>
                                    | <span id="gm-custom-mapunitsbutton-label-meters"
                                            class="gm-custom-mapunits-unselected">Meters</span>
                                </div>
                            </div>
                            <div id="gm-custom-map3d" class="gmnoprint gm-style-mtc"
                                 style="display: none; margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 30px; height: 30px; border-radius: 5px; right: 0px; background-color: #fff; opacity: 1;">
                                <a id="peak-search-3d" style="cursor: pointer;">3D</a>
                            </div>
                            <div id="gm-custom-maplegend" class="gmnoprint gm-style-mtc"
                                 style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 30px; height: 30px; border-radius: 5px; right: 0px; background-color: #fff; opacity: 1;">
                                <a id="peak-search-legend" style="cursor: pointer;"><i style="margin-left: 5px;"
                                                                                       class="fas fa-eye"></i></a>
                            </div>
                            <div id="gm-custom-mapfilter" class="gmnoprint gm-style-mtc hidden-lg hidden-md"
                                 style="display: none; margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 30px; height: 30px; border-radius: 5px; top: 5px; right: 0px; background-color: #fff; opacity: 1;">
                                <a id="peak-search-mobile" style="cursor: pointer;"><i class="fa fa-filter"></i></a>
                            </div>
                            <div id="gm-custom-maplayers" class="gmnoprint gm-style-mtc hidden-lg hidden-md"
                                 style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 30px; height: 30px; border-radius: 5px; top: 53px; right: 0px; background-color: #fff; opacity: 1;">
                                <a id="peak-search-layers" style="cursor: pointer;"><i
                                        class="fas fa-layer-group"></i></a>
                            </div>
                            <div id="gm-custom-maptype" class="gmnoprint"
                                 style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 180px; right: 50%; top: 0px;">
                                <div id="gm-custom-mapbutton" class="hidden-xs hidden-sm" draggable="false"
                                     style="cursor: pointer; direction: ltr; overflow: hidden; text-align: left; position: relative; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); height: 54px; padding: 8px; -webkit-background-clip: padding-box; background-clip: padding-box; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/<EMAIL>">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Terrain</div>
                                </div>
                                <div id="gm-custom-mapdropdown"
                                     style="z-index: 10; padding-left: 2px; padding-right: 2px; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; position: absolute; top: 52px; left: 0px; right: 0px; text-align: left; display: none;">
                                    <div id="gm-custom-mapoption-terrain" draggable="false"
                                         style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                        <div style="float: left;"><img
                                                style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                                src="{% static '' %}img/<EMAIL>">
                                        </div>
                                        <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Terrain
                                        </div>
                                    </div>
                                    <div id="gm-custom-mapoption-natatl" draggable="false"
                                         style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                        <div style="float: left;"><img
                                                style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                                src="{% static '' %}img/<EMAIL>">
                                        </div>
                                        <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Natural
                                            Atlas <span style="font-size: 10px;">(US)</span></div>
                                    </div>
                                    <div id="gm-custom-mapoption-outdoors" draggable="false"
                                         style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                        <div style="float: left;"><img
                                                style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                                src="{% static '' %}img/<EMAIL>">
                                        </div>
                                        <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">
                                            OpenTopoMap
                                        </div>
                                    </div>
                                    <div id="gm-custom-mapoption-streets" draggable="false"
                                         style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                        <div style="float: left;"><img
                                                style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                                src="{% static '' %}img/<EMAIL>">
                                        </div>
                                        <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Streets
                                        </div>
                                    </div>
                                    <div id="gm-custom-mapoption-topo" draggable="false"
                                         style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                        <div style="float: left;"><img
                                                style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                                src="{% static '' %}img/<EMAIL>">
                                        </div>
                                        <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo
                                            <span style="font-size: 10px;">(as avail)</span></div>
                                    </div>
                                    <div id="gm-custom-mapoption-satstreets" draggable="false"
                                         style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                        <div style="float: left;"><img
                                                style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                                src="{% static '' %}img/<EMAIL>">
                                        </div>
                                        <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite
                                        </div>
                                    </div>
                                    <div id="gm-custom-mapoption-sat" draggable="false"
                                         style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                        <div style="float: left;"><img
                                                style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                                src="{% static '' %}img/<EMAIL>">
                                        </div>
                                        <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite
                                            Topo
                                        </div>
                                    </div>
                                    <div class="" id="gm-custom-mapoption-3d" draggable="false"
                                         style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                        <div style="float: left;"><img
                                                style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                                src="{% static '' %}img/<EMAIL>">
                                        </div>
                                        <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">3D Map</div>
                                    </div>
                                </div>
                                <div id="gm-custom-maplegend-dropdown"
                                     style="z-index: 10; border: 2px solid rgba(0,0,0,0.15); position: absolute; width: 270px; top: 52px; left: 40px; right: 0px; text-align: left; border-radius: 8px; display: none;">
                                    <div id="gm-custom-maplegend-highest" draggable="false"
                                         style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500; border-top-left-radius: 8px; border-top-right-radius: 8px;">
                                        <div style="float: left;"><img style="width: 34px;"
                                                                       src="{% static '' %}img/<EMAIL>">
                                        </div>
                                        <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Highest peak
                                            on map
                                        </div>
                                    </div>
                                    <div id="gm-custom-maplegend-yoursummits" draggable="false"
                                         style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                        <div style="float: left;"><img style="width: 34px;"
                                                                       src="{% static '' %}img/<EMAIL>">
                                        </div>
                                        <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Climbed by
                                            you
                                            <div style="float: right; display: none;"><label class="switch"><input
                                                    id="map-summits-toggle" type="checkbox"><span
                                                    class="slider round"></span></label></div>
                                        </div>
                                    </div>
                                    <div id="gm-custom-maplegend-yourattempts" draggable="false"
                                         style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                        <div style="float: left;"><img style="width: 34px;"
                                                                       src="{% static '' %}img/<EMAIL>">
                                        </div>
                                        <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Attempted by
                                            you
                                            <div style="float: right; display: none;"><label class="switch"><input
                                                    id="map-attempts-toggle" type="checkbox"><span
                                                    class="slider round"></span></label></div>
                                        </div>
                                    </div>
                                    <div id="gm-custom-maplegend-unclimbed" draggable="false"
                                         style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                        <div style="float: left;"><img style="width: 34px;"
                                                                       src="{% static '' %}img/<EMAIL>">
                                        </div>
                                        <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Unclimbed by
                                            you
                                            <div style="float: right; display: none;"><label class="switch"><input
                                                    id="map-unclimbed-toggle" type="checkbox"><span
                                                    class="slider round"></span></label></div>
                                        </div>
                                    </div>
                                    <div id="gm-custom-maplegend-yourkings" draggable="false"
                                         style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                        <div style="float: left;"><img style="width: 34px;"
                                                                       src="{% static '' %}img/<EMAIL>">
                                        </div>
                                        <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Your King of
                                            the Mountains
                                            <div style="float: right; {% if not request.user.is_authenticated %}display: none;{% endif %}">
                                                <label class="switch"><input id="map-kom-toggle" type="checkbox"><span
                                                        class="slider round"></span></label></div>
                                        </div>
                                    </div>
                                    <div id="gm-custom-maplegend-yourstewards" draggable="false"
                                         style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                        <div style="float: left;"><img style="width: 34px;"
                                                                       src="{% static '' %}img/<EMAIL>">
                                        </div>
                                        <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Your Summit
                                            Stewards
                                            <div style="float: right; {% if not request.user.is_authenticated %}display: none;{% endif %}">
                                                <label class="switch"><input id="map-stewards-toggle"
                                                                             type="checkbox"><span
                                                        class="slider round"></span></label></div>
                                        </div>
                                    </div>
                                    <div id="gm-custom-maplegend-yourfirstascents" draggable="false"
                                         style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                        <div style="float: left;"><img style="width: 34px;"
                                                                       src="{% static '' %}img/<EMAIL>">
                                        </div>
                                        <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Your First
                                            Ascents
                                            <div style="float: right; {% if not request.user.is_authenticated %}display: none;{% endif %}">
                                                <label class="switch"><input id="map-firstascents-toggle"
                                                                             type="checkbox"><span
                                                        class="slider round"></span></label></div>
                                        </div>
                                    </div>
                                    <div id="gm-custom-maplegend-classics" draggable="false"
                                         style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                        <div style="float: left;"><img style="width: 34px;"
                                                                       src="{% static '' %}img/<EMAIL>">
                                        </div>
                                        <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Classic
                                            peaks
                                            <div style="float: right;"><label class="switch"><input
                                                    id="map-classics-toggle" type="checkbox"><span
                                                    class="slider round"></span></label></div>
                                        </div>
                                    </div>
                                    <div id="gm-custom-maplegend-photos" draggable="false"
                                         style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500; border-bottom-left-radius: 8px; border-bottom-right-radius: 8px;">
                                        <div style="float: left;"><img style="width: 34px;"
                                                                       src="{% static '' %}img/<EMAIL>">
                                        </div>
                                        <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Photos
                                            <div style="float: right;"><label class="switch"><input
                                                    id="map-photos-toggle" type="checkbox"><span
                                                    class="slider round"></span></label></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="message_map_div" class="gmnoprint gm-style-mtc"
                                 style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 104px; right: 0px; top: 0px; display: none;">
                                <div id="message_map" draggable="false"
                                     style="direction: ltr; overflow: hidden; text-align: left; position: relative; color: rgb(86, 86, 86); font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 11px; border-bottom-left-radius: 2px; border-top-left-radius: 2px; border-bottom-right-radius: 2px; border-top-right-radius: 2px; -webkit-background-clip: padding-box; background-clip: padding-box; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; font-weight: 500;">

                                </div>
                            </div>

                        </div>
                        <div id="marker-tooltip" data-url="" data-index=""
                             style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; cursor: pointer; opacity: 0; position:absolute; width: 200px; height: 150px; background: rgba(0, 0, 0, 0.3); margin: 15px;"></div>
                        <div id="photo-tooltip" data-url="" data-index=""
                             style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; cursor: pointer; opacity: 0; position:absolute; width: 200px; height: 150px; background: rgba(0, 0, 0, 0.3); margin: 15px;"></div>
                    </div>
                </div>

                {% if route.gpx_file %}

                    <div class="row" style="height: 150px; background-color: #fff; border-bottom: solid 1px #cfcfcf;">
                        <div class="col-md-12" id="peak-elevation-col" style="padding-right: 0px; padding-left: 0px;">
                            <div id="peak_elevation_container" class="peak_elevation_container" style="height: 150px;">
                                <div id="peak_elevation_canvas" class="peak_elevation_canvas" style="height: 150px;">
                                    <div id="elevation_cont" style="position: absolute; top: 10px; z-index: -1">
                                        <div id="elevation-loader"></div>
                                    </div><!-- END elevation_cont -->
                                </div>
                            </div>
                        </div>
                    </div>

                {% endif %}

                <div class="row dark-background-row">
                    <div class="sp-60"></div>
                </div>

                <div style="background-color: #fff; margin-left: -15px; margin-right: -15px;">
                    <div style="display: table; width: 100%; min-height: 240px;">
                        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6"
                             style="min-height: 240px; display: table-cell; float: none; background-color: #fff; border-top: 1px solid #c0c0c0; border-right: 1px solid #c0c0c0;">
                            <div class="stats-header">Route type</div>
                            <p class="stats-data">
                                {% if types %}
                                    {% for t in types %}
                                        {{ t }}{% if not forloop.last %} &bull; {% endif %}
                                    {% endfor %}
                                {% else %}
                                    <p class="stats-data-missing">no info yet</p>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>

                <div class="row dark-background-row">
                    <div class="sp-60"></div>
                </div>

            </div>
        </div>

    </div>

    <script type="text/javascript">

        var map;
        var mapCountry = '';
        var topo;
        var outdoors;
        var center = null;
        var map_bounds;
        var latLng = null;
        var init = false;
        var photos_displayed = 0;
        var photos_page = 1;
        var photos = [];
        var points = [];
        var newPoints = [];
        var linePoints = [];
        var lineData;
        var gpoints = [];
        var poly = null;
        var elevationsArr = [];
        var allElevations = [];
        var marker;
        var startmarker;
        var pageX, pageY, mapX, mapY;
        var iconstyle;
        var fit_bounds = true;

        google.charts.load('current', {'packages': ['corechart']});
        google.charts.setOnLoadCallback(initialize);

        window.onresize = function () {
            if (elevationsArr != []) {
                var chartWidth = $('#peak_elevation_canvas').width();
                drawElevationChart();
            }
        };

        function hideMapTooltip() {
            if ($('#marker-tooltip').hasClass('scale-in-tl')) {
                $('#marker-tooltip').addClass('scale-out-tl').removeClass('scale-in-tl');
            } else if ($('#marker-tooltip').hasClass('scale-in-tm')) {
                $('#marker-tooltip').addClass('scale-out-tm').removeClass('scale-in-tm');
            } else if ($('#marker-tooltip').hasClass('scale-in-tr')) {
                $('#marker-tooltip').addClass('scale-out-tr').removeClass('scale-in-tr');
            } else if ($('#marker-tooltip').hasClass('scale-in-bl')) {
                $('#marker-tooltip').addClass('scale-out-bl').removeClass('scale-in-bl');
            } else if ($('#marker-tooltip').hasClass('scale-in-bm')) {
                $('#marker-tooltip').addClass('scale-out-bm').removeClass('scale-in-bm');
            } else if ($('#marker-tooltip').hasClass('scale-in-br')) {
                $('#marker-tooltip').addClass('scale-out-br').removeClass('scale-in-br');
            }
        }

        function hidePhotoTooltip() {
            if ($('#photo-tooltip').hasClass('scale-in-tl')) {
                $('#photo-tooltip').addClass('scale-out-tl').removeClass('scale-in-tl');
            } else if ($('#photo-tooltip').hasClass('scale-in-tm')) {
                $('#photo-tooltip').addClass('scale-out-tm').removeClass('scale-in-tm');
            } else if ($('#photo-tooltip').hasClass('scale-in-tr')) {
                $('#photo-tooltip').addClass('scale-out-tr').removeClass('scale-in-tr');
            } else if ($('#photo-tooltip').hasClass('scale-in-bl')) {
                $('#photo-tooltip').addClass('scale-out-bl').removeClass('scale-in-bl');
            } else if ($('#photo-tooltip').hasClass('scale-in-bm')) {
                $('#photo-tooltip').addClass('scale-out-bm').removeClass('scale-in-bm');
            } else if ($('#photo-tooltip').hasClass('scale-in-br')) {
                $('#photo-tooltip').addClass('scale-out-br').removeClass('scale-in-br');
            }
        }

        function hideChallengeTooltip() {
            if ($('#challenge-tooltip').hasClass('scale-in-tl')) {
                $('#challenge-tooltip').addClass('scale-out-tl').removeClass('scale-in-tl');
            } else if ($('#challenge-tooltip').hasClass('scale-in-tm')) {
                $('#challenge-tooltip').addClass('scale-out-tm').removeClass('scale-in-tm');
            } else if ($('#challenge-tooltip').hasClass('scale-in-tr')) {
                $('#challenge-tooltip').addClass('scale-out-tr').removeClass('scale-in-tr');
            } else if ($('#challenge-tooltip').hasClass('scale-in-bl')) {
                $('#challenge-tooltip').addClass('scale-out-bl').removeClass('scale-in-bl');
            } else if ($('#challenge-tooltip').hasClass('scale-in-bm')) {
                $('#challenge-tooltip').addClass('scale-out-bm').removeClass('scale-in-bm');
            } else if ($('#challenge-tooltip').hasClass('scale-in-br')) {
                $('#challenge-tooltip').addClass('scale-out-br').removeClass('scale-in-br');
            }
        }

        function drawElevationChart() {

            var data = google.visualization.arrayToDataTable(elevationsArr);

            var chart = new google.visualization.SteppedAreaChart(document.getElementById('peak_elevation_canvas'));

            var options = {
                backgroundColor: '#ffffff',
                colors: ['#f24100'],
                legend: 'none',
                hAxis: {textPosition: 'none'},
                chartArea: {
                    left: "0",
                    top: "5",
                    height: "80%",
                    width: "100%"
                },
                vAxis: {
                    gridlines: {
                        color: '#eee'
                    }
                },
                width: '100%',
                height: '100%',
                tooltip: {isHtml: true}
            };

            chart.draw(data, options);

            google.visualization.events.addListener(chart, 'onmouseover', function (e) {

                var sliceid = e.row;
                //console.log("mouse over"+sliceid);
                updateMarker(newPoints[sliceid]);

            });
        }

        function numberWithCommas(x) {
            return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        }

        function set_route_gpx(gpx_url) {
            var window_width = $(window).width();
            var routeMarkerVisible = true;
            if (window_width < 768) {
                routeMarkerVisible = false;
            }
            elevationsArr = [[{label: 'Distance', id: 'Distance', type: 'number'}, {
                label: 'Elevation',
                id: 'Elevation',
                type: 'number'
            }, {type: 'string', role: 'tooltip', 'p': {'html': true}}]];
            var linePointStep = 1;
            var linePointCounter = 1;
            linePoints = [];
            $.ajax({
                type: "GET",
                url: gpx_url,
                dataType: "xml",
                success: function (xml) {
                    bounds = new mapboxgl.LngLatBounds();
                    var gpx = new gpxParser(); //Create gpxParser Object
                    var xmlString = (new XMLSerializer()).serializeToString(xml);
                    gpx.parse(xmlString); //parse gpx file from string data
                    var totalPoints = gpx.tracks[0].points.length;
                    if (totalPoints <= 7200) {
                        linePointStep = 1;
                    } else if (totalPoints <= 14400) {
                        linePointStep = 4;
                    } else if (totalPoints <= 28800) {
                        linePointStep = 8;
                    } else {
                        linePointStep = 16;
                    }
                    $(xml).find("trkpt").each(function () {
                        if (linePointCounter == linePointStep) {
                            var lat = $(this).attr("lat");
                            var lon = $(this).attr("lon");
                            var p = new mapboxgl.LngLat(lon, lat);
                            var gp = new google.maps.LatLng(lat, lon);
                            points.push(p);
                            gpoints.push(gp);
                            allElevations.push(parseFloat($(this).find("ele").text()));
                            linePointCounter = 1;
                        } else {
                            linePointCounter++;
                        }
                    });
                    $(xml).find("rtept").each(function () {
                        if (linePointCounter == linePointStep) {
                            var lat = {{ peak.lat }};
                            var lon = {{ peak.long }};
                            var p = new mapboxgl.LngLat(lon, lat);
                            var gp = new google.maps.LatLng(lat, lon);
                            points.push(p);
                            gpoints.push(gp);
                            allElevations.push(parseFloat($(this).find("ele").text()));
                            linePointCounter = 1;
                        } else {
                            linePointCounter++;
                        }
                    });

                    var startStep = {{ route.gpx_start_index }};
                    var endStep = {{ route.gpx_end_index }};
                    var totalPoints = points.length;
                    var pointStep = totalPoints / 500;
                    var polylineLength = 0;
                    var elevationGain = 0;
                    var lastElevation = 0;
                    var currentElevation = 0;
                    var addedStartMarker = false;
                    {% if route.total_distance %}
                        var totalPolylineDistance = {{ route.total_distance }};
                    {% else %}
                        var totalPolylineDistance = 0;
                    {% endif %}
                    for (var i = 0; i < totalPoints; i++) {
                        if (i >= (pointStep * startStep) && i <= (pointStep * endStep)) {
                            //add start marker and route marker
                            if (!addedStartMarker) {
                                //create an HTML element for the marker
                                var el = document.createElement('div');
                                el.className = 'route_marker_pulse_' + '{{ rank_color }}'.replace('#', '');

                                marker = new mapboxgl.Marker(el)
                                    .setLngLat(points[i])
                                    .setOffset([-5, -10])
                                    .setDraggable(false)
                                    .addTo(map);

                                //create an HTML element for the marker
                                var el = document.createElement('div');
                                el.className = 'route_marker_' + '{{ rank_color }}'.replace('#', '');

                                startmarker = new mapboxgl.Marker(el)
                                    .setLngLat(points[i])
                                    .setOffset([-5, -10])
                                    .setDraggable(false)
                                    .addTo(map);

                                //add google maps directions link
                                var start_lat = points[i].lat;
                                var start_lon = points[i].lng;
                                var directions_url = 'https://www.google.com/maps/dir//' + start_lat.toString() + ',' + start_lon.toString() + '/@' + start_lat.toString() + ',' + start_lon.toString() + ',17z';
                                $('#directions-link').html('<p style="margin-bottom: 20px;"><a target="_blank" href="' + directions_url + '">Directions on Google Maps</a></p>');
                                addedStartMarker = true;
                            }
                            //calculate elevation gain
                            if (i == 0) {
                                lastElevation = allElevations[i];
                            }
                            currentElevation = allElevations[i];
                            if (currentElevation > lastElevation) {
                                elevationGain = elevationGain + (currentElevation - lastElevation);
                            }
                            newPoints.push(points[i]);
                            linePoints.push([points[i].lng, points[i].lat]);
                            bounds.extend(points[i]);
                            var elevationItem = [(polylineLength).toString(), allElevations[i] * 3.28084, '<div style="margin: 10px;"><span style="white-space: nowrap;">Distance: <span style="color: #ff0000;">' + (polylineLength).toFixed(2).toString() + ' mi / ' + (polylineLength * 1.60934).toFixed(2).toString() + 'km</span></span><br /><span style="white-space: nowrap;">Elevation: <span style="color: #ff0000;">' + numberWithCommas((allElevations[i] * 3.28084).toFixed(0)).toString() + ' ft / ' + numberWithCommas((allElevations[i] * 3.28084 * .3048).toFixed(0)).toString() + 'm</span></span></div>'];
                            elevationsArr.push(elevationItem);
                            if (i > 0) {
                                polylineLength = (totalPolylineDistance * (i / totalPoints));
                            }

                            lastElevation = allElevations[i];

                        }
                    }

                    lineData = {
                        "type": "Feature",
                        "properties": {},
                        "geometry": {
                            "type": "LineString",
                            "coordinates": linePoints
                        }
                    }

                    map.addSource('route-data', {
                        type: 'geojson',
                        data: lineData
                    });

                    map.addLayer({
                        "id": "route-layer",
                        "type": "line",
                        "source": "route-data",
                        "layout": {
                            "line-join": "round",
                            "line-cap": "round"
                        },
                        "paint": {
                            "line-color": "#fc202e",
                            "line-width": 4
                        }
                    });

                    if (fit_bounds) {
                        // fit bounds to track
                        map.fitBounds(bounds, {padding: 50, maxZoom: 14});
                    }

                    drawElevationChart();

                    fit_bounds = false;

                    setMapControls();

                }
            });
        }

        function loadPeaks() {

            //set map center cookie
            var NewMapCenter = map.getCenter();
            var NewMapZoom = map.getZoom();
            createCookie('map_lat', NewMapCenter.lat, 365);
            createCookie('map_lng', NewMapCenter.lng, 365);
            createCookie('map_zoom', NewMapZoom, 365);

            //get 2d map bounds or create 3d map bounds
            var mapButton = $('#gm-custom-mapbutton').html();
            if (mapButton.includes('3D Map')) {
                var camLat = map.getCenter().lat;
                var camLng = map.getCenter().lng;
                var sw = new mapboxgl.LngLat(camLng - .10, camLat - .10);
                var ne = new mapboxgl.LngLat(camLng + .10, camLat + .10);
                var bounds = new mapboxgl.LngLatBounds(sw, ne);
            } else if ($('#peak-search-3d').html() == '2D') {
                var camLat = map.getCenter().lat;
                var camLng = map.getCenter().lng;
                var sw = new mapboxgl.LngLat(camLng - .10, camLat - .10);
                var ne = new mapboxgl.LngLat(camLng + .10, camLat + .10);
                var bounds = new mapboxgl.LngLatBounds(sw, ne);
            } else {
                var bounds = map.getBounds();
            }

            var counter = 0;

            var params = '';
            params = params + '&n=';
            params = params + '&elev_min=0';
            params = params + '&elev_max=29500';
            params = params + '&prom_min=0';
            params = params + '&prom_max=29500';
            params = params + '&summits_min=0';
            params = params + '&summits_max=500';
            params = params + '&difficulty_min=1';
            params = params + '&difficulty_max=5';
            params = params + '&lat=';
            params = params + '&lng=';
            params = params + '&bounds=' + bounds.getSouthWest().lat + ',' + bounds.getSouthWest().lng + ',' + bounds.getNorthEast().lat + ',' + bounds.getNorthEast().lng + '&zoom=' + map.getZoom();

            //update hidden parameters
            map_bounds = bounds.getSouthWest().lat + ',' + bounds.getSouthWest().lng + ',' + bounds.getNorthEast().lat + ',' + bounds.getNorthEast().lng;

            var LatLngList = [];

            if (params.length > 0) params = '?' + params.slice(-1 * (params.length - 1));

            $.getJSON('{% url "peaks_map_get_peaks" %}' + params, function (data) {
                $.each(data, function (key, val) {
                    var currentRequest = true;
                    if (key == 'parameters') {
                        $.each(val, function (parameterkey, parameterval) {
                            if (parameterval.bounds != map_bounds) currentRequest = false;
                        });
                    }

                    if (!currentRequest) {
                        return false;
                    }

                    if (key == 'peaks') {

                        var havePeaks = false;

                        $.each(val, function (peakkey, peakval) {

                            if (!havePeaks) {

                                //first time through, delete highest peak marker and remove any markers not on map
                                deletehighest();
                                //delete markers out of margins
                                delete_old_markers(val);

                            }

                            havePeaks = true;

                            //build country string
                            var country = '';
                            $.each(peakval.country, function (countrykey, countryval) {
                                country = country + '<div><a href="' + countryval.country_slug + '/">' + countryval.country_name + '</a></div>';
                            });
                            if (country == '') {
                                country = 'Unknown Country';
                            }

                            //build region string
                            var region = '';
                            var mobile_region = '';
                            var region_bull_class = 'hidden-lg hidden-md hidden-sm hidden-xs';
                            $.each(peakval.region, function (regionkey, regionval) {
                                region_bull_class = '';
                                region = region + '<div style="margin-bottom: 5px;"><a class="truncate" href="' + regionval.region_slug + '/">' + regionval.region_name + ', ' + regionval.country_name + '</a></div>';
                                mobile_region = '<div style="margin-bottom: 5px;"><a class="truncate" href="' + regionval.region_slug + '/">' + regionval.country_name + '</a></div>';
                            });
                            if (region == '') {
                                region = country;
                                mobile_region = country;
                            }

                            //build challenges string
                            var challenges = '';
                            var challenge_count = peakval.challenge_count;
                            if (challenge_count > 0) {
                                challenges = ' &bull; <span>' + challenge_count + ' <i class="fa fa-trophy"></i></span>';
                            }

                            //build distance string
                            var distance = '';

                            //build summits string
                            var summits, tooltip_your_summits;
                            if (peakval.summit_count > 0) {
                                summits = '<a href="/' + peakval.slug + '/summits/">' + peakval.summit_count + '</a>';
                            } else {
                                summits = '<a href="/' + peakval.slug + '/summits/">0</a><br /><a href="/' + peakval.slug + '/">Be the first!</a>';
                            }
                            if (peakval.your_summits > 0) {
                                summits = summits + '<br /><span style="color: #3fc663; font-weight: 500;">' + peakval.your_summits + 'x by you</span>';
                                tooltip_your_summits = '&nbsp;<span style="color: #3fc663; font-weight: 500;">(You ' + peakval.your_summits + 'x)</span>';
                            } else {
                                tooltip_your_summits = '';
                            }

                            //tooltip vars
                            var tooltip_html, tooltip_width, tooltip_height, marker_top, marker_left,
                                tooltip_total_width, tooltip_total_height;

                            //show tooltip badges?
                            var showClassic = 'display: none;';
                            var showChallenges = 'display: none;';
                            var showYourSummits = 'display: none;';
                            if (peakval.is_classic == 'True') {
                                showClassic = '';
                            }
                            if (peakval.challenge_count > 0) {
                                showChallenges = '';
                            }
                            if (peakval.your_summits > 0) {
                                showYourSummits = '';
                            }

                            peaklist_html = '';

                            //show feet or meters?
                            var showFeet = 'display: none;';
                            var showMeters = 'display: none;';
                            if ($('#bt_showinmeters').hasClass('meters')) {
                                showMeters = '';
                            } else {
                                showFeet = '';
                            }

                            //build tooltip string
                            if (peakval.thumbnail_url.indexOf('default.png.350x245_q95_crop.jpg') < 0) {
                                tooltip_html = '<img class="map-tooltip-img hover-photos-hover" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px;" src="{{ MEDIA_URL }}' + peakval.thumbnail_url + '"><div class="map-tooltip-peak-badges" style="font-weight:500; top: 115px; width: 220px; position: absolute; background-color: transparent; background-image: linear-gradient(to bottom, transparent 0px, rgba(0, 0, 0, 0.7) 100%); padding-left: 5px;"><div id="peak-classic-badge" style="' + showClassic + ' width: 65px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00a7ee; font-size: 8px; color: #fff; float: left; text-align: center; line-height: 20px;"><i class="fa fa-star" style="float: left; margin-left: 5px; line-height: 18px; filter: drop-shadow(0px 0px 1px #333); color: #feeeb9;"></i> CLASSIC</div><div id="peak-challenges-badge" style="' + showChallenges + ' width: 95px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #5c48e0; font-size: 8px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-trophy" style="filter: drop-shadow(0px 0px 1px #333); color: #feeeb9; float: left; margin-left: 5px; line-height: 18px;"></i> ' + peakval.challenge_count + ' CHALLENGE' + ((peakval.challenge_count != 1) ? 'S' : '') + '</div><div id="peak-your-summits-badge" style="' + showYourSummits + ' width: 40px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00b524; font-size: 9px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-check" style="filter: drop-shadow(0px 0px 1px #333); float: left; margin-left: 5px; line-height: 20px;"></i> ' + peakval.your_summits + 'x</div></div><div class="map-tooltip-info" style="border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; bottom: -35px; height: 100px; background-color: transparent !important;"><div style="margin-left: -8px; margin-top: 43px; height: 55px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; box-shadow: 0 5px 8px 0 rgba(0,0,0,0.30);"><div class="map-tooltip-peak-name ellipsis" style="color: #333; text-align: left; padding-left: 10px; font-size: 15px; width: 210px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; padding-left: 10px;"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div></div>';
                                tooltip_width = 220;
                                tooltip_height = 165;
                                tooltip_total_width = 250;
                                tooltip_total_height = 230;
                                peaklist_html = '<a href="/' + peakval.slug + '/"><div id="peak-listitem-' + peakval.id + '" data-peakid="' + peakval.id + '" class="hover-photos" style="float: left; margin-left: 10px; margin-top: 10px; margin-bottom: -60px;"><img class="map-tooltip-img hover-photos-hover" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; position: relative;" src="{{ MEDIA_URL }}' + peakval.thumbnail_url + '"><div class="map-tooltip-peak-badges" style="font-weight:500; top: -52px; width: 220px; position: relative; background-color: transparent; background-image: linear-gradient(to bottom, transparent 0px, rgba(0, 0, 0, 0.7) 100%); padding-left: 5px;"><div id="peak-classic-badge" style="' + showClassic + ' width: 65px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00a7ee; font-size: 8px; color: #fff; float: left; text-align: center; line-height: 20px;"><i class="fa fa-star" style="float: left; margin-left: 5px; line-height: 18px; filter: drop-shadow(0px 0px 1px #333); color: #feeeb9;"></i> CLASSIC</div><div id="peak-challenges-badge" style="' + showChallenges + ' width: 95px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #5c48e0; font-size: 8px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-trophy" style="filter: drop-shadow(0px 0px 1px #333); color: #feeeb9; float: left; margin-left: 5px; line-height: 18px;"></i> ' + peakval.challenge_count + ' CHALLENGE' + ((peakval.challenge_count != 1) ? 'S' : '') + '</div><div id="peak-your-summits-badge" style="' + showYourSummits + ' width: 40px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00b524; font-size: 9px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-check" style="filter: drop-shadow(0px 0px 1px #333); float: left; margin-left: 5px; line-height: 20px;"></i> ' + peakval.your_summits + 'x</div></div><div class="map-tooltip-info" style="border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; position: relative; left: inherit; top: -65px; width: 220px; height: 100px; background-color: transparent !important;"><div class="peak-listitem-footer" style="margin-left: -8px; margin-top: 43px; height: 55px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff;"><div class="map-tooltip-peak-name ellipsis" style="color: #333; text-align: left; padding-left: 10px; font-size: 15px; width: 210px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; padding-left: 10px;"><span style="' + showFeet + '" class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="' + showMeters + '" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div></div></div></a>';
                            } else {
                                if (peakval.is_classic == 'True' || peakval.challenge_count > 0 || peakval.your_summits > 0) {
                                    tooltip_html = '<div class="map-tooltip-peak-badges" style="font-weight:500; top: 4px; z-index: 2; width: 220px; position: absolute; background-color: transparent; padding-left: 5px;"><div id="peak-classic-badge" style="' + showClassic + ' width: 65px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00a7ee; font-size: 8px; color: #fff; float: left; text-align: center; line-height: 20px;"><i class="fa fa-star" style="float: left; margin-left: 5px; line-height: 18px; filter: drop-shadow(0px 0px 1px #333); color: #feeeb9;"></i> CLASSIC</div><div id="peak-challenges-badge" style="' + showChallenges + ' width: 95px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #5c48e0; font-size: 8px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-trophy" style="filter: drop-shadow(0px 0px 1px #333); color: #feeeb9; float: left; margin-left: 5px; line-height: 18px;"></i> ' + peakval.challenge_count + ' CHALLENGE' + ((peakval.challenge_count != 1) ? 'S' : '') + '</div><div id="peak-your-summits-badge" style="' + showYourSummits + ' width: 40px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00b524; font-size: 9px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-check" style="filter: drop-shadow(0px 0px 1px #333); float: left; margin-left: 5px; line-height: 20px;"></i> ' + peakval.your_summits + 'x</div></div><div class="map-tooltip-info" style="height: 75px; border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; box-shadow: 0 5px 8px 0 rgba(0,0,0,0.30);"><div class="map-tooltip-peak-name ellipsis" style="margin-top: 25px; color: #333; text-align: left; font-size: 15px; width: 210px; height: 27px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; margin-bottom: -5px;"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div>';
                                    tooltip_width = 220;
                                    tooltip_height = 75;
                                    tooltip_total_width = 250;
                                    tooltip_total_height = 105;
                                    peaklist_html = '<a href="/' + peakval.slug + '/"><div id="peak-listitem-' + peakval.id + '" data-peakid="' + peakval.id + '" class="hover-photos" style="float: left; margin-left: 10px; margin-top: 10px; margin-bottom: 5px;"><div class="map-tooltip-peak-badges" style="font-weight:500; top: 4px; z-index: 2; width: 220px; position: relative; background-color: transparent; padding-left: 5px;"><div id="peak-classic-badge" style="' + showClassic + ' width: 65px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00a7ee; font-size: 8px; color: #fff; float: left; text-align: center; line-height: 20px;"><i class="fa fa-star" style="float: left; margin-left: 5px; line-height: 18px; filter: drop-shadow(0px 0px 1px #333); color: #feeeb9;"></i> CLASSIC</div><div id="peak-challenges-badge" style="' + showChallenges + ' width: 95px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #5c48e0; font-size: 8px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-trophy" style="filter: drop-shadow(0px 0px 1px #333); color: #feeeb9; float: left; margin-left: 5px; line-height: 18px;"></i> ' + peakval.challenge_count + ' CHALLENGE' + ((peakval.challenge_count != 1) ? 'S' : '') + '</div><div id="peak-your-summits-badge" style="' + showYourSummits + ' width: 40px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00b524; font-size: 9px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-check" style="filter: drop-shadow(0px 0px 1px #333); float: left; margin-left: 5px; line-height: 20px;"></i> ' + peakval.your_summits + 'x</div></div><div class="map-tooltip-info peak-listitem-footer" style="height: 80px; border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; position: relative;"><div class="map-tooltip-peak-name ellipsis" style="margin-top: 25px; color: #333; text-align: left; font-size: 15px; width: 210px; height: 27px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; margin-bottom: -5px;"><span style="' + showFeet + '" class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="' + showMeters + '" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div></div></a>';
                                } else {
                                    tooltip_html = '<div class="map-tooltip-info" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; box-shadow: 0 5px 8px 0 rgba(0,0,0,0.30);"><div class="map-tooltip-peak-name ellipsis" style="color: #333; text-align: left; font-size: 15px; width: 210px; height: 27px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; margin-bottom: -5px;"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div>';
                                    tooltip_width = 220;
                                    tooltip_height = 50;
                                    tooltip_total_width = 250;
                                    tooltip_total_height = 80;
                                    peaklist_html = '<a href="/' + peakval.slug + '/"><div id="peak-listitem-' + peakval.id + '" data-peakid="' + peakval.id + '" class="hover-photos" style="float: left; margin-left: 10px; margin-top: 10px; margin-bottom: 5px;"><div class="map-tooltip-info peak-listitem-footer" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; position: relative; height: 54px;"><div class="map-tooltip-peak-name ellipsis" style="color: #333; text-align: left; font-size: 15px; width: 210px; height: 27px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; margin-bottom: -5px;"><span style="' + showFeet + '" class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="' + showMeters + '" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div></div></a>';
                                }
                            }

                            var tooltip_url = '/' + peakval.slug;

                            var latLng = new mapboxgl.LngLat(peakval.lng, peakval.lat);

                            if (counter == 0) {
                                //highest peak gets red icon
                                iconstyle = 'marker_icon_red';
                            } else if (peakval.your_summits > 0) {
                                //if you have summited then green icon
                                iconstyle = 'marker_icon_green';
                            } else if (peakval.your_attempts > 0) {
                                //if you have attempted then yellow icon
                                iconstyle = 'marker_icon_yellow';
                            } else {
                                iconstyle = 'marker_icon';
                            }

                            if (isTouchDevice()) {
                                var is_draggable = false;
                            } else {
                                {% if request.user.is_staff %}
                                    var is_draggable = true;
                                {% else %}
                                    var is_draggable = false;
                                {% endif %}
                            }

                            //check if already exist so don't put again
                            var exists = false;
                            for (i = markersArray.length - 1; i >= 0; i--) {
                                if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng)) {
                                    exists = true;
                                    //if the highest is in the actual viewport, not as the highest, delete it
                                    if (iconstyle == 'marker_icon_red' || iconstyle == 'marker_icon_redgreen' || iconstyle == 'marker_icon_green') {
                                        markersArray[i].remove();
                                        markersArray.splice(i, 1);
                                        exists = false;
                                    }
                                }
                            }

                            if (!exists) {
                                var latLng = [peakval.lng, peakval.lat];
                                //add marker
                                //create an HTML element for the marker
                                var el = document.createElement('div');
                                el.className = iconstyle;
                                el.setAttribute('id', 'peak-marker-' + peakval.id);

                                if (peakval.is_classic == 'True') {
                                    el.setAttribute('data-classic', 'true');
                                }

                                {% if request.user.is_authenticated %}

                                    if (peakval.kom_user == '{{ request.user.id }}') {
                                        el.setAttribute('data-kom', 'true');
                                    }

                                    if (peakval.first_ascent_user == '{{ request.user.id }}') {
                                        el.setAttribute('data-firstascent', 'true');
                                    }

                                    if (peakval.summit_stewards != '') {
                                        var stewards = $.parseJSON(peakval.summit_stewards.replace(/&quot;/g, '"'));
                                        $.each(stewards, function (key, data) {
                                            if (key == 'summit_stewards') {
                                                for (var i = 0; i < data.length; i++) {
                                                    if (data[i] == '{{ request.user.id }}') {
                                                        el.setAttribute('data-steward', 'true');
                                                    }
                                                }
                                            }
                                        });
                                    }

                                {% endif %}

                                el.addEventListener('click', function (e) {
                                    if (isTouchDevice()) {
                                        hideMapTooltip();
                                        $('#gm-custom-mapdropdown').hide();
                                        $('#gm-custom-maplegend-dropdown').hide();

                                        var bottom = $('#map-canvas').height();
                                        var right = $('#map-canvas').width();
                                        var showClass = 'scale-in-';
                                        var hideClass = 'scale-out-';

                                        var markerX = this.getBoundingClientRect().x + 14;
                                        var mapX = document.getElementById('map-canvas').getBoundingClientRect().x;
                                        var mapY = document.getElementById('map-canvas').getBoundingClientRect().y;

                                        var markerY = this.getBoundingClientRect().y + 14 - mapY;

                                        if (markerY < (bottom / 2)) {
                                            marker_top = markerY;
                                            showClass = showClass + 't';
                                            hideClass = hideClass + 't';
                                        } else {
                                            marker_top = markerY - tooltip_total_height - 15;
                                            showClass = showClass + 'b';
                                            hideClass = hideClass + 'b';
                                        }

                                        if (markerX < (right / 3)) {
                                            marker_left = markerX;
                                            showClass = showClass + 'l';
                                            hideClass = hideClass + 'l';
                                        } else if (markerX >= (right / 3) && markerX < ((right / 3) * 2)) {
                                            marker_left = markerX - (tooltip_total_width / 2);
                                            showClass = showClass + 'm';
                                            hideClass = hideClass + 'm';
                                        } else {
                                            marker_left = markerX - tooltip_total_width;
                                            showClass = showClass + 'r';
                                            hideClass = hideClass + 'r';
                                        }

                                        $('#marker-tooltip').html(marker.properties.tooltipContent).css({
                                            'left': marker_left,
                                            'top': marker_top,
                                            'width': tooltip_width,
                                            'height': tooltip_height
                                        });
                                        $('#marker-tooltip').removeClass('scale-out-tl').removeClass('scale-out-tm').removeClass('scale-out-tr').removeClass('scale-out-bl').removeClass('scale-out-bm').removeClass('scale-out-br');
                                        $('#marker-tooltip').addClass(showClass);
                                        $('#marker-tooltip').data('url', marker.properties.tooltipUrl);
                                        if ($('#bt_showinmeters').hasClass('meters')) {
                                            $('#marker-tooltip').find('.peak-elevation-feet').hide();
                                            $('#marker-tooltip').find('.peak-elevation-meters').show();
                                        } else {
                                            $('#marker-tooltip').find('.peak-elevation-meters').hide();
                                            $('#marker-tooltip').find('.peak-elevation-feet').show();
                                        }
                                    } else {
                                        //console.log(peakval.slug);
                                        location = '/' + peakval.slug + '/';;
                                    }
                                    e.stopPropagation();
                                });

                                el.addEventListener('mouseover', function (e) {

                                    if (!isTouchDevice()) {

                                        var bottom = $('#map-canvas').height();
                                        var right = $('#map-canvas').width();
                                        var showClass = 'scale-in-';
                                        var hideClass = 'scale-out-';

                                        var markerX = this.getBoundingClientRect().x + 14;
                                        var mapX = document.getElementById('map-canvas').getBoundingClientRect().x;
                                        var mapY = document.getElementById('map-canvas').getBoundingClientRect().y;

                                        markerX = markerX - mapX;

                                        var markerY = this.getBoundingClientRect().y + 14 - mapY;

                                        if (markerY < (bottom / 2)) {
                                            marker_top = markerY;
                                            showClass = showClass + 't';
                                            hideClass = hideClass + 't';
                                        } else {
                                            marker_top = markerY - tooltip_total_height - 15;
                                            showClass = showClass + 'b';
                                            hideClass = hideClass + 'b';
                                        }

                                        if (markerX < (right / 3)) {
                                            marker_left = markerX;
                                            showClass = showClass + 'l';
                                            hideClass = hideClass + 'l';
                                        } else if (markerX >= (right / 3) && markerX < ((right / 3) * 2)) {
                                            marker_left = markerX - (tooltip_total_width / 2);
                                            showClass = showClass + 'm';
                                            hideClass = hideClass + 'm';
                                        } else {
                                            marker_left = markerX - tooltip_total_width;
                                            showClass = showClass + 'r';
                                            hideClass = hideClass + 'r';
                                        }

                                        $('#marker-tooltip').html(marker.properties.tooltipContent).css({
                                            'left': marker_left,
                                            'top': marker_top,
                                            'width': tooltip_width,
                                            'height': tooltip_height
                                        }).addClass(showClass).removeClass('scale-out-tl').removeClass('scale-out-tm').removeClass('scale-out-tr').removeClass('scale-out-bl').removeClass('scale-out-bm').removeClass('scale-out-br');
                                        $('#marker-tooltip').data('url', marker.properties.tooltipUrl);
                                        if ($('#bt_showinmeters').hasClass('meters')) {
                                            $('#marker-tooltip').find('.peak-elevation-feet').hide();
                                            $('#marker-tooltip').find('.peak-elevation-meters').show();
                                        } else {
                                            $('#marker-tooltip').find('.peak-elevation-meters').hide();
                                            $('#marker-tooltip').find('.peak-elevation-feet').show();
                                        }
                                    }
                                    e.stopPropagation();

                                });

                                el.addEventListener('mouseout', function (e) {
                                    if (isTouchDevice()) {
                                        //$('#marker-tooltip').hide();
                                    } else {
                                        hideMapTooltip();
                                    }
                                });

                                var marker = new mapboxgl.Marker(el)
                                    .setLngLat(latLng)
                                    .setOffset([-5, -10])
                                    .setDraggable(is_draggable);

                                marker.properties = {};
                                marker.properties.tooltipContent = tooltip_html;
                                marker.properties.tooltipUrl = tooltip_url;
                                marker.properties.iconstyle = iconstyle;
                                marker.properties.peakid = peakval.id;

                                {% if request.user.is_staff %}
                                    //editing functions
                                    marker.on('dragstart', function (e) {
                                        hideMapTooltip();
                                    });
                                    marker.on('dragend', function (e) {
                                        var point = marker.getLngLat();
                                        fix_item_location(peakval.id, point);
                                    });

                                    el.addEventListener('contextmenu', function (e) {
                                        if (confirm("Are you sure you want to delete this peak?")) {
                                            delete_peak_from_map(peakval.id);
                                            hideMapTooltip();
                                            for (i = markersArray.length - 1; i >= 0; i--) {
                                                if (markersArray[i].properties.peakid == peakval.id) {
                                                    markersArray[i].remove();
                                                    markersArray.splice(i, 1);
                                                }
                                            }
                                        }
                                    });
                                {% endif %}

                                markersArray.push(marker);
                                LatLngList.push(latLng);

                            }

                            counter++;
                        });

                        if (!havePeaks) {
                            //didn't have any peaks, so remove all markers
                            delete_old_markers(val);
                        }
                    }

                    //add photo markers
                    if (NewMapZoom >= 13) {
                        for (var i = photoMarkersArray.length - 1; i >= 0; --i) {
                            photoMarkersArray[i].addTo(map);
                        }
                    }

                    //add challenge markers
                    for (var i = challengeMarkersArray.length - 1; i >= 0; --i) {
                        challengeMarkersArray[i].addTo(map);
                    }

                    //add peak markers to map in reverse order so highest on top
                    for (var i = markersArray.length - 1; i >= 0; --i) {
                        markersArray[i].addTo(map);
                    }

                });

                //show classics if needed
                if ($("#map-classics-toggle").is(':checked')) {
                    $('.mapboxgl-marker[data-classic=true]').addClass('marker_icon_classic');
                }

                //show kom if needed
                if ($("#map-kom-toggle").is(':checked')) {
                    $('.mapboxgl-marker[data-kom=true]').addClass('marker_icon_kom');
                }

                //show first ascents if needed
                if ($("#map-firstascents-toggle").is(':checked')) {
                    $('.mapboxgl-marker[data-firstascent=true]').addClass('marker_icon_firstascent');
                }

                //show summit stewards if needed
                if ($("#map-stewards-toggle").is(':checked')) {
                    $('.mapboxgl-marker[data-steward=true]').addClass('marker_icon_steward');
                }

                //hide unclimbed if needed
                if (!$("#map-unclimbed-toggle").is(':checked')) {
                    $('.marker_icon').hide();
                }

                //hide attempts if needed
                if (!$("#map-attempts-toggle").is(':checked')) {
                    $('.marker_icon_yellow').hide();
                }

                //hide summits if needed
                if (!$("#map-summits-toggle").is(':checked')) {
                    $('.marker_icon_green').hide();
                }

            });

            $.getJSON('{% url "peaks_map_get_photos" %}' + params, function (data) {
                $.each(data, function (key, val) {
                    if (key == 'photos') {

                        var havePhotos = false;
                        var aryPeakPhotos = [];
                        delete_all_photo_markers(val);

                        $.each(val, function (photokey, photoval) {

                            if (!havePhotos) {

                                //first time through, delete all photo markers
                                delete_all_photo_markers(val);

                            }

                            havePhotos = true;

                            //tooltip vars
                            var tooltip_html, tooltip_width, tooltip_height, marker_top, marker_left,
                                tooltip_total_width, tooltip_total_height;

                            //build tooltip string
                            tooltip_html = '<div style="width: 100%; height: 100%; border-radius: 8px; background-image: url({{ MEDIA_URL }}' + photoval.thumbnail_url + '); background-size: cover;"></div>';
                            tooltip_width = 165;
                            tooltip_total_width = 205;
                            tooltip_height = 165;
                            tooltip_total_height = 165;

                            var tooltip_url = '/' + photoval.fullsize_url;

                            var latLng = new mapboxgl.LngLat(photoval.photo_lng, photoval.photo_lat);

                            var latLng = [photoval.photo_lng, photoval.photo_lat];
                            //add marker
                            //create an HTML element for the marker
                            var el_div = document.createElement('div');
                            var el = document.createElement('a');

                            el_div.className = 'marker-photo-icon';
                            el.className = 'gallery-link';

                            //hide photos if necessary
                            if ($("#map-photos-toggle").is(':checked')) {
                                el.style.display = 'block';
                            } else {
                                el.style.display = 'none';
                            }

                            el_div.style.backgroundImage = 'url({{ MEDIA_URL }}' + photoval.thumbnail_url + ')';
                            el_div.style.width = '20px';
                            el_div.style.height = '20px';
                            el_div.style.border = '2px solid rgba(255,255,255)';
                            el_div.style.backgroundSize = 'cover';
                            el_div.setAttribute('id', 'photo-marker-' + photoval.id);

                            el.setAttribute('data-user', photoval.username);
                            el.setAttribute('data-gallery', '');
                            el.setAttribute('data-description', encodeDoubleQuotes(photoval.caption));
                            el.setAttribute('data-createdate', photoval.createdate);
                            el.setAttribute('data-peak-slug', photoval.peak_slug);
                            el.setAttribute('data-summit-log-id', photoval.summit_log_id);
                            el.setAttribute('href', '{{ MEDIA_URL }}' + photoval.fullsize_url);

                            el.appendChild(el_div);

                            el.addEventListener('mouseover', function (e) {

                                if (!isTouchDevice()) {

                                    var bottom = $('#map-canvas').height();
                                    var right = $('#map-canvas').width();
                                    var showClass = 'scale-in-';
                                    var hideClass = 'scale-out-';

                                    var markerX = this.getBoundingClientRect().x + 18;

                                    if ($('#peak-search').css('left') == '0px') {
                                        markerX = markerX - 240;
                                    }

                                    var markerY = this.getBoundingClientRect().y + 18 - 50;

                                    if (markerY < (bottom / 2)) {
                                        marker_top = markerY;
                                        showClass = showClass + 't';
                                        hideClass = hideClass + 't';
                                    } else {
                                        marker_top = markerY - tooltip_total_height - 45;
                                        showClass = showClass + 'b';
                                        hideClass = hideClass + 'b';
                                    }

                                    if (markerX < (right / 3)) {
                                        marker_left = markerX;
                                        showClass = showClass + 'l';
                                        hideClass = hideClass + 'l';
                                    } else if (markerX >= (right / 3) && markerX < ((right / 3) * 2)) {
                                        marker_left = markerX - (tooltip_total_width / 2);
                                        showClass = showClass + 'm';
                                        hideClass = hideClass + 'm';
                                    } else {
                                        marker_left = markerX - tooltip_total_width;
                                        showClass = showClass + 'r';
                                        hideClass = hideClass + 'r';
                                    }

                                    $('#photo-tooltip').html(marker.properties.tooltipContent).css({
                                        'left': marker_left,
                                        'top': marker_top,
                                        'width': tooltip_width,
                                        'height': tooltip_height
                                    }).addClass(showClass).removeClass('scale-out-tl').removeClass('scale-out-tm').removeClass('scale-out-tr').removeClass('scale-out-bl').removeClass('scale-out-bm').removeClass('scale-out-br');
                                    $('#photo-tooltip').data('url', marker.properties.tooltipUrl);
                                }
                                e.stopPropagation();

                            });

                            el.addEventListener('mouseout', function (e) {
                                if (isTouchDevice()) {
                                    //$('#marker-tooltip').hide();
                                } else {
                                    hidePhotoTooltip();
                                }
                            });

                            var marker = new mapboxgl.Marker(el)
                                .setLngLat(latLng)
                                .setOffset([-5, -10]);

                            marker.properties = {};
                            marker.properties.tooltipContent = tooltip_html;
                            marker.properties.tooltipUrl = tooltip_url;
                            marker.properties.photoid = photoval.id;
                            marker.properties.lat = photoval.photo_lat;
                            marker.properties.lng = photoval.photo_lng;

                            photoMarkersArray.push(marker);

                        });
                    }
                })
            })

            $.getJSON('{% url "peaks_map_get_challenges" %}' + params, function (data) {
                $.each(data, function (key, val) {
                    if (key == 'challenges') {

                        var haveChallenges = false;
                        var aryPeakChallenges = [];
                        delete_all_challenge_markers();

                        $.each(val, function (challengekey, challengeval) {

                            if (!haveChallenges) {

                                //first time through, delete any markers not on map
                                //delete markers out of margins
                                delete_old_challenge_markers(val);

                            }

                            haveChallenges = true;

                            //tooltip vars
                            var tooltip_html, tooltip_width, tooltip_height, marker_top, marker_left,
                                tooltip_total_width, tooltip_total_height;

                            //build tooltip string
                            tooltip_html = '<img class="map-tooltip-img hover-photos-hover" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px;" src="{{ MEDIA_URL }}images/' + challengeval.thumbnail_url + '"><div class="map-tooltip-info" style="border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; bottom: -35px; height: 100px; background-color: transparent !important;"><div style="margin-left: -8px; margin-top: 43px; height: 55px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; box-shadow: 0 5px 8px 0 rgba(0,0,0,0.30);"><div class="map-tooltip-peak-name ellipsis" style="color: #333; text-align: left; padding-left: 10px; font-size: 15px; width: 210px;">' + challengeval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; padding-left: 10px;">' + challengeval.peak_count + ' peak' + ((challengeval.peak_count != 1) ? 's' : '') + '</div></div></div>';
                            tooltip_width = 220;
                            tooltip_height = 165;
                            tooltip_total_width = 250;
                            tooltip_total_height = 230;

                            var tooltip_url = '/challenges/' + challengeval.slug + '/';

                            var latLng = new mapboxgl.LngLat(challengeval.challenge_lng, challengeval.challenge_lat);

                            var latLng = [challengeval.challenge_lng, challengeval.challenge_lat];
                            //add marker
                            //create an HTML element for the marker
                            var el_div = document.createElement('div');
                            var el = document.createElement('a');

                            el.className = 'marker-challenge-icon';

                            //hide challenges if necessary
                            if ($("#map-challenges-toggle").is(':checked')) {
                                el.style.display = 'block';
                            } else {
                                el.style.display = 'none';
                            }

                            el.style.backgroundImage = 'url({% static '' %}img/<EMAIL>)';
                            el.style.width = '28px';
                            el.style.height = '28px';
                            //el.style.border = '2px solid rgba(255,255,255)';
                            el.style.backgroundSize = 'cover';
                            el.setAttribute('id', 'challenge-marker-' + challengeval.id);

                            el.addEventListener('click', function (e) {
                                if (isTouchDevice()) {
                                    hideChallengeTooltip();

                                    var bottom = $('#map-canvas').height();
                                    var right = $('#map-canvas').width();
                                    var showClass = 'scale-in-';
                                    var hideClass = 'scale-out-';

                                    var markerX = this.getBoundingClientRect().x + 14;
                                    var markerY = this.getBoundingClientRect().y + 14 - 100;

                                    if (markerY < (bottom / 2)) {
                                        marker_top = markerY;
                                        showClass = showClass + 't';
                                        hideClass = hideClass + 't';
                                    } else {
                                        marker_top = markerY - tooltip_total_height - 15;
                                        showClass = showClass + 'b';
                                        hideClass = hideClass + 'b';
                                    }

                                    if (markerX < (right / 3)) {
                                        marker_left = markerX;
                                        showClass = showClass + 'l';
                                        hideClass = hideClass + 'l';
                                    } else if (markerX >= (right / 3) && markerX < ((right / 3) * 2)) {
                                        marker_left = markerX - (tooltip_total_width / 2);
                                        showClass = showClass + 'm';
                                        hideClass = hideClass + 'm';
                                    } else {
                                        marker_left = markerX - tooltip_total_width;
                                        showClass = showClass + 'r';
                                        hideClass = hideClass + 'r';
                                    }

                                    $('#challenge-tooltip').html(marker.properties.tooltipContent).css({
                                        'left': marker_left,
                                        'top': marker_top,
                                        'width': tooltip_width,
                                        'height': tooltip_height
                                    });
                                    $('#challenge-tooltip').removeClass('scale-out-tl').removeClass('scale-out-tm').removeClass('scale-out-tr').removeClass('scale-out-bl').removeClass('scale-out-bm').removeClass('scale-out-br');
                                    $('#challenge-tooltip').addClass(showClass);
                                    $('#challenge-tooltip').data('url', marker.properties.tooltipUrl);
                                } else {
                                    location = '/challenges/' + challengeval.slug;
                                }
                                e.stopPropagation();
                            });

                            el.addEventListener('mouseover', function (e) {

                                if (!isTouchDevice()) {

                                    var bottom = $('#map-canvas').height();
                                    var right = $('#map-canvas').width();
                                    var showClass = 'scale-in-';
                                    var hideClass = 'scale-out-';

                                    var markerX = this.getBoundingClientRect().x + 14;

                                    if ($('#peak-search').css('left') == '0px') {
                                        markerX = markerX - 240;
                                    }

                                    var markerY = this.getBoundingClientRect().y + 14 - 50;

                                    if (markerY < (bottom / 2)) {
                                        marker_top = markerY;
                                        showClass = showClass + 't';
                                        hideClass = hideClass + 't';
                                    } else {
                                        marker_top = markerY - tooltip_total_height - 15;
                                        showClass = showClass + 'b';
                                        hideClass = hideClass + 'b';
                                    }

                                    if (markerX < (right / 3)) {
                                        marker_left = markerX;
                                        showClass = showClass + 'l';
                                        hideClass = hideClass + 'l';
                                    } else if (markerX >= (right / 3) && markerX < ((right / 3) * 2)) {
                                        marker_left = markerX - (tooltip_total_width / 2);
                                        showClass = showClass + 'm';
                                        hideClass = hideClass + 'm';
                                    } else {
                                        marker_left = markerX - tooltip_total_width;
                                        showClass = showClass + 'r';
                                        hideClass = hideClass + 'r';
                                    }

                                    $('#challenge-tooltip').html(marker.properties.tooltipContent).css({
                                        'left': marker_left,
                                        'top': marker_top,
                                        'width': tooltip_width,
                                        'height': tooltip_height
                                    }).addClass(showClass).removeClass('scale-out-tl').removeClass('scale-out-tm').removeClass('scale-out-tr').removeClass('scale-out-bl').removeClass('scale-out-bm').removeClass('scale-out-br');
                                    $('#challenge-tooltip').data('url', marker.properties.tooltipUrl);
                                }
                                e.stopPropagation();

                            });

                            el.addEventListener('mouseout', function (e) {
                                if (isTouchDevice()) {
                                    //$('#marker-tooltip').hide();
                                } else {
                                    hideChallengeTooltip();
                                }
                            });

                            var marker = new mapboxgl.Marker(el)
                                .setLngLat(latLng)
                                .setOffset([-5, -10]);

                            marker.properties = {};
                            marker.properties.tooltipContent = tooltip_html;
                            marker.properties.tooltipUrl = tooltip_url;
                            marker.properties.photoid = challengeval.id;
                            marker.properties.lat = challengeval.challenge_lat;
                            marker.properties.lng = challengeval.challenge_lng;

                            challengeMarkersArray.push(marker);

                        });
                    }
                })
            })

            init = true;

        }

        var markersArray = [];
        var photoMarkersArray = [];
        var challengeMarkersArray = [];

        const throttle = (fn, wait) => {
            let inThrottle, lastFn, lastTime;
            return function () {
                const context = this,
                    args = arguments;
                if (!inThrottle) {
                    fn.apply(context, args);
                    lastTime = Date.now();
                    inThrottle = true;
                } else {
                    clearTimeout(lastFn);
                    lastFn = setTimeout(function () {
                        if (Date.now() - lastTime >= wait) {
                            fn.apply(context, args);
                            lastTime = Date.now();
                        }
                    }, Math.max(wait - (Date.now() - lastTime), 0));
                }
            };
        };

        const throttleLoadPeaks = throttle(() => { loadPeaks(); }, 3000);

        function initialize() {

            var mapDiv = document.getElementById('map-canvas');
            var latLng = new mapboxgl.LngLat({{ peak.long }}, {{ peak.lat }});
            var LatLngList = [];

            {% if peak.is_usa_but_not_alaska %}
                mapStyle = mapStyleNaturalAtlas;
            {% else %}
                mapStyle = mapStyleOutdoors;
            {% endif %}

            var initZoom = 14;

            var mapType = readCookie('map_type');
            if (mapType != '' && mapType != null) {
                initMapType(mapType);
            }

            if (isTouchDevice()) {
                map = new mapboxgl.Map({
                    container: mapDiv, // HTML container id
                    style: mapStyle, // style URL
                    center: latLng, // starting position as [lng, lat]
                    zoom: initZoom,
                    scrollZoom: false
                });
            } else {
                map = new mapboxgl.Map({
                    container: mapDiv, // HTML container id
                    style: mapStyle, // style URL
                    center: latLng, // starting position as [lng, lat]
                    zoom: initZoom,
                    scrollZoom: false
                });
                scale = new mapboxgl.ScaleControl({maxWidth: 120, unit: 'imperial'});
                map.addControl(scale, 'bottom-right');
                var nav = new mapboxgl.NavigationControl();
                map.addControl(nav, 'bottom-right');
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
            }

            function calculateCenter() {
                center = map.getCenter();
            }

            map.on('resize', function (e) {
                map.setCenter(center);
            });

            map.on('moveend', (eventData) => {
                if (eventData.originalEvent !== undefined) { // We make sure an event triggered this callback.
                    throttleLoadPeaks();
                }
            });

            map.on('load', function () {
                loadPeaks();
                calculateCenter();
                var mapUnits = readCookie('map_units');
                if (mapUnits != '') {
                    toggleMapUnits(mapUnits);
                }
                {% if route.gpx_file %}
                    set_route_gpx('{{ S3_MEDIA_URL }}{{ route.gpx_file }}');
                    $('#gpx_download_div').show();
                {% endif %}
                setMapControls();
            });

            map.on('click', function (e) {
                map.scrollZoom.enable();
                if (isTouchDevice()) {
                    hideMapTooltip();
                    hidePhotoTooltip();
                    hideChallengeTooltip();
                    $('#gm-custom-mapdropdown').hide();
                    $('#gm-custom-maplegend-dropdown').hide();
                } else {
                    hideMapTooltip();
                    hidePhotoTooltip();
                    hideChallengeTooltip();
                    $('#gm-custom-mapdropdown').hide();
                    $('#gm-custom-maplegend-dropdown').hide();
                }
            });

            map.on('dragstart', function (e) {
                if (isTouchDevice()) {
                    hideMapTooltip();
                    hidePhotoTooltip();
                    hideChallengeTooltip();
                    $('#gm-custom-mapdropdown').hide();
                    $('#gm-custom-maplegend-dropdown').hide();
                } else {
                    hideMapTooltip();
                    hidePhotoTooltip();
                    hideChallengeTooltip();
                    $('#gm-custom-mapdropdown').hide();
                    $('#gm-custom-maplegend-dropdown').hide();
                }
            });

            map.on('moveend', function () {
                calculateCenter();
            });

            var checking_style_status = false;
            map.on('styledata', function (e) {
                if (checking_style_status) {
                    // If already checking style status, bail out
                    // (important because styledata event may fire multiple times)
                    return;
                } else {
                    checking_style_status = true;
                    check_style_status();
                }
            });

            function check_style_status() {
                if (map.isStyleLoaded()) {
                    checking_style_status = false;
                    map.fire('map_style_finally_loaded');
                } else {
                    // If not yet loaded, repeat check after delay:
                    setTimeout(function () {
                        check_style_status();
                    }, 200);
                    return;
                }
            }

            map.on('map_style_finally_loaded', function (e) {
                addPolyline();
            });

        }

        function setMapControls() {
            var check = checkIfMapboxStyleIsLoaded();
            if (!check) {
                // It's not safe to manipulate layers yet, so wait 200ms and then check again
                setTimeout(function () {
                    setMapControls();
                }, 200);
                return;
            }
            // Whew, now it's safe to manipulate layers!
            var mapUnits = readCookie('map_units');
            if (mapUnits == 'meters') {
                toggleMapUnits(mapUnits);
            }
        }

        function addExtraMapLayers(type = '') {
            var check = checkIfMapboxStyleIsLoaded();
            if (!check) {
                // It's not safe to manipulate layers yet, so wait 200ms and then check again
                setTimeout(function () {
                    addExtraMapLayers(type);
                }, 200);
                return;
            }
            // Whew, now it's safe to manipulate layers!
            if (linePoints.length > 0) {
                addPolyline();
                $('#gpx_download_div').show();
            }
            // No extra map layers necessary

            if (type == 'satellite_3d') {
                try {
                    map.setTerrain(null);
                } catch (e) {
                    //pass
                }
                // add the DEM source as a terrain layer with exaggerated height
                try {
                    map.setTerrain({'source': 'mapbox-dem', 'exaggeration': 1.2});
                } catch (e) {
                    map.addSource('mapbox-dem', {
                        'type': 'raster-dem',
                        'url': 'mapbox://mapbox.mapbox-terrain-dem-v1',
                        'tileSize': 512,
                        'maxzoom': 14
                    });
                    map.setTerrain({'source': 'mapbox-dem', 'exaggeration': 1.2});
                }
                // add the sky layer
                try {
                    map.addLayer({
                        'id': 'sky',
                        'type': 'sky',
                        'paint': {
                            'sky-type': 'atmosphere',
                            'sky-atmosphere-sun': [0.0, 0.0],
                            'sky-atmosphere-sun-intensity': 15
                        }
                    });
                } catch (e) {
                    //pass
                }
                // enable map rotation using right click + drag
                map.dragRotate.enable();
                // enable map rotation using touch rotation gesture
                map.touchZoomRotate.enableRotation();
                // set pitch to 80 degrees
                //map.setPitch(80);
                map.easeTo({pitch: 80});

            } else {

                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                map.setPitch(0);
                //map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);

            }
        }

        function addPolyline() {
            if (linePoints.length > 0) {
                if (map.getSource('route-data') == undefined) {
                    map.addSource('route-data', {
                        type: 'geojson',
                        data: lineData
                    });
                }
                if (map.getLayer('route-layer') == undefined) {
                    map.addLayer({
                        "id": "route-layer",
                        "type": "line",
                        "source": "route-data",
                        "layout": {
                            "line-join": "round",
                            "line-cap": "round"
                        },
                        "paint": {
                            "line-color": "#fc202e",
                            "line-width": 4
                        }
                    });
                }
            }
        }

        function gmapsCallback() {

            var showPhotos = readCookie('map_photos');
            if (showPhotos == 'true') {
                $('#map-photos-toggle').prop("checked", true);
            } else if (showPhotos === null) {
                createCookie('map_photos', true, 365);
                $('#map-photos-toggle').prop("checked", true);
            } else {
                $('#map-photos-toggle').prop("checked", false);
            }

            var showClassics = readCookie('map_classics');
            if (showClassics == 'true') {
                $('#map-classics-toggle').prop("checked", true);
            } else if (showClassics === null) {
                createCookie('map_classics', true, 365);
                $('#map-classics-toggle').prop("checked", true);
            } else {
                $('#map-classics-toggle').prop("checked", false);
            }

            var showChallenges = readCookie('map_challenges');
            if (showChallenges == 'true') {
                $('#map-challenges-toggle').prop("checked", true);
            } else if (showChallenges === null) {
                createCookie('map_challenges', true, 365);
                $('#map-challenges-toggle').prop("checked", true);
            } else {
                $('#map-challenges-toggle').prop("checked", false);
            }

            var showFirstAscents = readCookie('map_firstascents');
            if (showFirstAscents == 'true') {
                $('#map-firstascents-toggle').prop("checked", true);
            } else if (showFirstAscents === null) {
                createCookie('map_firstascents', true, 365);
                $('#map-firstascents-toggle').prop("checked", true);
            } else {
                $('#map-firstascents-toggle').prop("checked", false);
            }

            var showStewards = readCookie('map_stewards');
            if (showStewards == 'true') {
                $('#map-stewards-toggle').prop("checked", true);
            } else if (showStewards === null) {
                createCookie('map_stewards', true, 365);
                $('#map-stewards-toggle').prop("checked", true);
            } else {
                $('#map-stewards-toggle').prop("checked", false);
            }

            var showKOM = readCookie('map_kom');
            if (showKOM == 'true') {
                $('#map-kom-toggle').prop("checked", true);
            } else if (showKOM === null) {
                createCookie('map_kom', true, 365);
                $('#map-kom-toggle').prop("checked", true);
            } else {
                $('#map-kom-toggle').prop("checked", false);
            }

            var showUnclimbed = readCookie('map_unclimbed');
            if (showUnclimbed == 'true') {
                $('#map-unclimbed-toggle').prop("checked", true);
            } else if (showUnclimbed === null) {
                createCookie('map_unclimbed', true, 365);
                $('#map-unclimbed-toggle').prop("checked", true);
            } else {
                $('#map-unclimbed-toggle').prop("checked", false);
            }

            var showAttempts = readCookie('map_attempts');
            if (showAttempts == 'true') {
                $('#map-attempts-toggle').prop("checked", true);
            } else if (showAttempts === null) {
                createCookie('map_attempts', true, 365);
                $('#map-attempts-toggle').prop("checked", true);
            } else {
                $('#map-attempts-toggle').prop("checked", false);
            }

            var showSummits = readCookie('map_summits');
            if (showSummits == 'true') {
                $('#map-summits-toggle').prop("checked", true);
            } else if (showSummits === null) {
                createCookie('map_summits', true, 365);
                $('#map-summits-toggle').prop("checked", true);
            } else {
                $('#map-summits-toggle').prop("checked", false);
            }

            $("#map-photos-toggle").change(function () {
                if ($(this).is(':checked')) {
                    $('.gallery-link').show();
                    createCookie('map_photos', true, 365);
                } else {
                    $('.gallery-link').hide();
                    createCookie('map_photos', false, 365);
                }
            });

            $("#map-classics-toggle").change(function () {
                if ($(this).is(':checked')) {
                    $('.mapboxgl-marker[data-classic=true]').addClass('marker_icon_classic');
                    createCookie('map_classics', true, 365);
                } else {
                    $('.mapboxgl-marker[data-classic=true]').removeClass('marker_icon_classic');
                    createCookie('map_classics', false, 365);
                }
            });

            $("#map-challenges-toggle").change(function () {
                if ($(this).is(':checked')) {
                    $('.marker-challenge-icon').show();
                    createCookie('map_challenges', true, 365);
                } else {
                    $('.marker-challenge-icon').hide();
                    createCookie('map_challenges', false, 365);
                }
            });

            $("#map-stewards-toggle").change(function () {
                if ($(this).is(':checked')) {
                    $('.mapboxgl-marker[data-steward=true]').addClass('marker_icon_steward');
                    createCookie('map_stewards', true, 365);
                } else {
                    $('.mapboxgl-marker[data-steward=true]').removeClass('marker_icon_steward');
                    createCookie('map_stewards', false, 365);
                }
            });

            $("#map-kom-toggle").change(function () {
                if ($(this).is(':checked')) {
                    $('.mapboxgl-marker[data-kom=true]').addClass('marker_icon_kom');
                    createCookie('map_kom', true, 365);
                } else {
                    $('.mapboxgl-marker[data-kom=true]').removeClass('marker_icon_kom');
                    createCookie('map_kom', false, 365);
                }
            });

            $("#map-firstascents-toggle").change(function () {
                if ($(this).is(':checked')) {
                    $('.mapboxgl-marker[data-firstascent=true]').addClass('marker_icon_firstascent');
                    createCookie('map_firstascents', true, 365);
                } else {
                    $('.mapboxgl-marker[data-firstascent=true]').removeClass('marker_icon_firstascent');
                    createCookie('map_firstascents', false, 365);
                }
            });

            $("#map-unclimbed-toggle").change(function () {
                if ($(this).is(':checked')) {
                    $('.marker_icon').show();
                    createCookie('map_unclimbed', true, 365);
                } else {
                    $('.marker_icon').hide();
                    createCookie('map_unclimbed', false, 365);
                }
            });

            $("#map-attempts-toggle").change(function () {
                if ($(this).is(':checked')) {
                    $('.marker_icon_yellow').show();
                    createCookie('map_attempts', true, 365);
                } else {
                    $('.marker_icon_yellow').hide();
                    createCookie('map_attempts', false, 365);
                }
            });

            $("#map-summits-toggle").change(function () {
                if ($(this).is(':checked')) {
                    $('.marker_icon_green').show();
                    createCookie('map_summits', true, 365);
                } else {
                    $('.marker_icon_green').hide();
                    createCookie('map_summits', false, 365);
                }
            });

            $("#mobile-app-map-link").on('click', function () {
                //Android.peakCoordinates('{{ peak.lat }},{{ peak.long }}');
                //Android.peakId('{{ peak.id }}');
                Android.peakInfo(peakObject);
            });

            //add log this climb option to log climb dropdown
            $('#log-climb-log-manually .navbar-primary .log-climb-dropdown-label-div').html('Log another peak');
            $('#log-climb-log-this-peak .navbar-primary').attr('href', '/peaks/log_climb/?peak={{ peak.id }}');
            $('#log-climb-log-this-peak .navbar-primary .log-climb-dropdown-label-div').html('Log {{ peak.name }}');
            $('#log-climb-log-this-peak').show();

            $('#map-canvas').mousemove(function (e) {
                var offset = $(this).offset();
                pageX = e.pageX;
                pageY = e.pageY;
                mapX = (e.pageX - offset.left);
                mapY = (e.pageY - offset.top);
            });

            $('#map-canvas').on('touchstart', function (e) {
                var offset = $(this).offset();
                pageX = e.originalEvent.touches[0].pageX;
                pageY = e.originalEvent.touches[0].pageY;
                mapX = (pageX - offset.left);
                mapY = (pageY - offset.top);
            });

            $("time.timeago").timeago();

            $('.more-info-link').trunk8({lines: 2, tooltip: false});

            $('.route-name-title').trunk8({lines: 3, tooltip: false});

            $('#blueimp-gallery').on('open', function (event) {
                $('body,html').css('overflow', 'visible');
            });

            $("#blueimp-gallery").on('slide', function (event, index, slide) {
                var gallery = $('#blueimp-gallery').data('gallery');
                var caption = gallery.list[index].getAttribute('data-description'),
                    username = gallery.list[index].getAttribute('data-user'),
                    credit = gallery.list[index].getAttribute('data-credit'),
                    photo_url = gallery.list[index].getAttribute('data-photo-url'),
                    caption_node = gallery.container.find('.description-text-caption');
                username_node = gallery.container.find('.description-text-user');
                caption_node.empty();
                username_node.empty();
                if (caption) {
                    caption_node[0].appendChild(document.createTextNode(caption));
                }
                if (username) {
                    var newdiv = document.createElement('div');
                    if (credit) {
                        newdiv.innerHTML = '<a style="color: #fff;" target="_blank" href="' + photo_url + '/"><i style="margin-right: 5px;" class="fa fa-camera" aria-hidden="true"></i> ' + username + ' ' + credit + '</a>';
                    } else {
                        newdiv.innerHTML = '<a style="color: #fff;" href="/members/' + username + '/"><i style="margin-right: 5px;" class="fa fa-camera" aria-hidden="true"></i> ' + username;
                    }
                    username_node[0].appendChild(newdiv);
                }

            });

            //switch map units
            $("#gm-custom-mapunits").click(function () {
                if ($('#gm-custom-mapunitsbutton-label-meters').hasClass('gm-custom-mapunits-selected')) {
                    toggleMapUnits('feet');
                    scale.setUnit('imperial');
                } else {
                    toggleMapUnits('meters');
                    scale.setUnit('metric');
                }
            });

            //Disable scrollZoom
            $('#map-canvas').on('mouseleave', function () {
                map.scrollZoom.disable();
            });

            $('#peak-search-layers').on('click', function (e) {
                var mapX = document.getElementById('map-canvas').getBoundingClientRect().x;
                var mapY = document.getElementById('map-canvas').getBoundingClientRect().y;
                $('#gm-custom-maplegend-dropdown').hide();
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapdropdown').css('border-top-right-radius', '8px');
                $('#gm-custom-mapdropdown').css('border-top-left-radius', '8px');
                $('#gm-custom-mapoption-terrain').css('border-top-right-radius', '8px');
                $('#gm-custom-mapoption-terrain').css('border-top-left-radius', '8px');
                var height_pad = 30;
                $('#gm-custom-mapdropdown').css('top', height_pad);
                e.stopPropagation();
            });

            $('#peak-search-legend').on('click', function (e) {
                $('#gm-custom-maplegend-dropdown').toggle();
                var width = $(window).width();
                var height = $(window).height();
                var height_pad = 15;
                $('#gm-custom-maplegend-dropdown').css('top', height_pad);
                e.stopPropagation();
            });

            $('#gm-custom-maplegend-close').on('click', function (e) {
                $('#gm-custom-maplegend-dropdown').hide();
                e.stopPropagation();
            });

            $('#marker-tooltip').on('click', function () {
                window.location = $(this).data('url');
            });

            $('#challenge-tooltip').on('click', function () {
                window.location = $(this).data('url');
            });

            //Custom Google Map type stuff

            $('#gm-custom-mapbutton').on('mouseenter', function () {
                $('#gm-custom-mapdropdown').show();
                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '0px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '0px');
            });

            $('#gm-custom-mapbutton').on('mouseleave', function () {
                $('#gm-custom-mapdropdown').hide();
                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
            });

            $('#gm-custom-mapbutton').on('touchstart', function () {
                $('#gm-custom-mapdropdown').toggle();
            });

            //new MapBox 2.0 3D stuff
            var flag = false;
            $('#gm-custom-mapoption-3d').on('touchstart click', function (e) {
                if (!flag) {
                    flag = true;
                    setTimeout(function () {
                        flag = false;
                    }, 500);
                    $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                    $('#gm-custom-mapoption-satstreets').css("color", "#333");
                    $('#gm-custom-mapoption-topo').css("background-color", "#fff");
                    $('#gm-custom-mapoption-topo').css("color", "#333");
                    $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                    $('#gm-custom-mapoption-sat').css("color", "#333");
                    $('#gm-custom-mapoption-3d').css("background-color", "#0ae");
                    $('#gm-custom-mapoption-3d').css("color", "#fff");
                    $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                    $('#gm-custom-mapoption-terrain').css("color", "#333");
                    $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                    $('#gm-custom-mapoption-outdoors').css("color", "#333");
                    $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                    $('#gm-custom-mapoption-streets').css("color", "#333");
                    $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                    $('#gm-custom-mapoption-natatl').css("color", "#333");

                    toggleMapType('satellite_3d');
                    $('#gm-custom-mapdropdown').toggle();

                    $('#search-peaks-btn').prop('disabled', false);
                    $('#q').prop('disabled', false);
                    $('#n').prop('disabled', false);
                    $(".mapboxgl-canvas-container").show();
                    $('.mapboxgl-ctrl-bottom-left').show();
                    $('.mapboxgl-ctrl-bottom-right').show();

                    $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                    $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                    $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">3D Map</div>');
                }
                return false
            });

            $('#gm-custom-mapoption-3d').on('mouseenter', function () {
                $('#gm-custom-mapdropdown').show();
                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '0px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '0px');
            });

            $('#gm-custom-mapoption-3d').on('mouseleave', function () {
                $('#gm-custom-mapdropdown').hide();
                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
            });

            var flag = false;
            $('#gm-custom-mapoption-satstreets').on('touchstart click', function (e) {
                if (!flag) {
                    flag = true;
                    setTimeout(function () {
                        flag = false;
                    }, 500);
                    $('#gm-custom-mapoption-satstreets').css("background-color", "#0ae");
                    $('#gm-custom-mapoption-satstreets').css("color", "#fff");
                    $('#gm-custom-mapoption-topo').css("background-color", "#fff");
                    $('#gm-custom-mapoption-topo').css("color", "#333");
                    $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                    $('#gm-custom-mapoption-sat').css("color", "#333");
                    $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                    $('#gm-custom-mapoption-3d').css("color", "#333");
                    $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                    $('#gm-custom-mapoption-terrain').css("color", "#333");
                    $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                    $('#gm-custom-mapoption-outdoors').css("color", "#333");
                    $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                    $('#gm-custom-mapoption-streets').css("color", "#333");
                    $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                    $('#gm-custom-mapoption-natatl').css("color", "#333");

                    toggleMapType('satellite');
                    $('#gm-custom-mapdropdown').toggle();

                    $('#search-peaks-btn').prop('disabled', false);
                    $('#q').prop('disabled', false);
                    $('#n').prop('disabled', false);
                    $(".mapboxgl-canvas-container").show();
                    $('.mapboxgl-ctrl-bottom-left').show();
                    $('.mapboxgl-ctrl-bottom-right').show();

                    $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                    $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                    $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite</div>');
                }
                return false
            });

            $('#gm-custom-mapoption-satstreets').on('mouseenter', function () {
                $('#gm-custom-mapdropdown').show();
                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '0px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '0px');
            });

            $('#gm-custom-mapoption-satstreets').on('mouseleave', function () {
                $('#gm-custom-mapdropdown').hide();
                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
            });

            var flag = false;
            $('#gm-custom-mapoption-topo').on('touchstart click', function () {
                if (!flag) {
                    flag = true;
                    setTimeout(function () {
                        flag = false;
                    }, 500);
                    $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                    $('#gm-custom-mapoption-satstreets').css("color", "#333");
                    $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
                    $('#gm-custom-mapoption-topo').css("color", "#fff");
                    $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                    $('#gm-custom-mapoption-sat').css("color", "#333");
                    $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                    $('#gm-custom-mapoption-3d').css("color", "#333");
                    $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                    $('#gm-custom-mapoption-terrain').css("color", "#333");
                    $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                    $('#gm-custom-mapoption-outdoors').css("color", "#333");
                    $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                    $('#gm-custom-mapoption-streets').css("color", "#333");
                    $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                    $('#gm-custom-mapoption-natatl').css("color", "#333");

                    toggleMapType('caltopo');
                    $('#gm-custom-mapdropdown').toggle();

                    $('#search-peaks-btn').prop('disabled', false);
                    $('#q').prop('disabled', false);
                    $('#n').prop('disabled', false);
                    $(".mapboxgl-canvas-container").show();
                    $('.mapboxgl-ctrl-bottom-left').show();
                    $('.mapboxgl-ctrl-bottom-right').show();

                    $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                    $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                    $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
                }
                return false
            });

            $('#gm-custom-mapoption-topo').on('mouseenter', function () {
                $('#gm-custom-mapdropdown').show();
                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '0px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '0px');
            });

            $('#gm-custom-mapoption-topo').on('mouseleave', function () {
                $('#gm-custom-mapdropdown').hide();
                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
            });

            var flag = false;
            $('#gm-custom-mapoption-sat').on('touchstart click', function () {
                if (!flag) {
                    flag = true;
                    setTimeout(function () {
                        flag = false;
                    }, 500);
                    $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                    $('#gm-custom-mapoption-satstreets').css("color", "#333");
                    $('#gm-custom-mapoption-topo').css("background-color", "#fff");
                    $('#gm-custom-mapoption-topo').css("color", "#333");
                    $('#gm-custom-mapoption-sat').css("background-color", "#0ae");
                    $('#gm-custom-mapoption-sat').css("color", "#fff");
                    $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                    $('#gm-custom-mapoption-3d').css("color", "#333");
                    $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                    $('#gm-custom-mapoption-terrain').css("color", "#333");
                    $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                    $('#gm-custom-mapoption-outdoors').css("color", "#333");
                    $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                    $('#gm-custom-mapoption-streets').css("color", "#333");
                    $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                    $('#gm-custom-mapoption-natatl').css("color", "#333");

                    toggleMapType('sat_topo');
                    $('#gm-custom-mapdropdown').toggle();

                    $('#search-peaks-btn').prop('disabled', false);
                    $('#q').prop('disabled', false);
                    $('#n').prop('disabled', false);
                    $(".mapboxgl-canvas-container").show();
                    $('.mapboxgl-ctrl-bottom-left').show();
                    $('.mapboxgl-ctrl-bottom-right').show();

                    $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                    $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                    $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite Topo</div>');
                }
                return false
            });

            $('#gm-custom-mapoption-sat').on('mouseenter', function () {
                $('#gm-custom-mapdropdown').show();
                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '0px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '0px');
            });

            $('#gm-custom-mapoption-sat').on('mouseleave', function () {
                $('#gm-custom-mapdropdown').hide();
                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
            });

            var flag = false;
            $('#gm-custom-mapoption-terrain').on('touchstart click', function () {
                if (!flag) {
                    flag = true;
                    setTimeout(function () {
                        flag = false;
                    }, 500);
                    $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                    $('#gm-custom-mapoption-satstreets').css("color", "#333");
                    $('#gm-custom-mapoption-topo').css("background-color", "#fff");
                    $('#gm-custom-mapoption-topo').css("color", "#333");
                    $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                    $('#gm-custom-mapoption-sat').css("color", "#333");
                    $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                    $('#gm-custom-mapoption-3d').css("color", "#333");
                    $('#gm-custom-mapoption-terrain').css("background-color", "#0ae");
                    $('#gm-custom-mapoption-terrain').css("color", "#fff");
                    $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                    $('#gm-custom-mapoption-outdoors').css("color", "#333");
                    $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                    $('#gm-custom-mapoption-streets').css("color", "#333");
                    $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                    $('#gm-custom-mapoption-natatl').css("color", "#333");

                    toggleMapType('terrain');
                    $('#gm-custom-mapdropdown').toggle();

                    $('#search-peaks-btn').prop('disabled', false);
                    $('#q').prop('disabled', false);
                    $('#n').prop('disabled', false);
                    $(".mapboxgl-canvas-container").show();
                    $('.mapboxgl-ctrl-bottom-left').show();
                    $('.mapboxgl-ctrl-bottom-right').show();

                    $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                    $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                    $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Terrain</div>');
                }
                return false
            });

            $('#gm-custom-mapoption-terrain').on('mouseenter', function () {
                $('#gm-custom-mapdropdown').show();
                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '0px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '0px');
            });

            $('#gm-custom-mapoption-terrain').on('mouseleave', function () {
                $('#gm-custom-mapdropdown').hide();
                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
            });

            var flag = false;
            $('#gm-custom-mapoption-outdoors').on('touchstart click', function () {
                if (!flag) {
                    flag = true;
                    setTimeout(function () {
                        flag = false;
                    }, 500);
                    $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                    $('#gm-custom-mapoption-satstreets').css("color", "#333");
                    $('#gm-custom-mapoption-topo').css("background-color", "#fff");
                    $('#gm-custom-mapoption-topo').css("color", "#333");
                    $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                    $('#gm-custom-mapoption-sat').css("color", "#333");
                    $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                    $('#gm-custom-mapoption-3d').css("color", "#333");
                    $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                    $('#gm-custom-mapoption-terrain').css("color", "#333");
                    $('#gm-custom-mapoption-outdoors').css("background-color", "#0ae");
                    $('#gm-custom-mapoption-outdoors').css("color", "#fff");
                    $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                    $('#gm-custom-mapoption-streets').css("color", "#333");
                    $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                    $('#gm-custom-mapoption-natatl').css("color", "#333");

                    toggleMapType('outdoors');
                    $('#gm-custom-mapdropdown').toggle();

                    $('#search-peaks-btn').prop('disabled', false);
                    $('#q').prop('disabled', false);
                    $('#n').prop('disabled', false);
                    $(".mapboxgl-canvas-container").show();
                    $('.mapboxgl-ctrl-bottom-left').show();
                    $('.mapboxgl-ctrl-bottom-right').show();

                    $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                    $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                    $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">OpenTopoMap</div>');
                }
                return false
            });

            $('#gm-custom-mapoption-outdoors').on('mouseenter', function () {
                $('#gm-custom-mapdropdown').show();
                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '0px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '0px');
            });

            $('#gm-custom-mapoption-outdoors').on('mouseleave', function () {
                $('#gm-custom-mapdropdown').hide();
                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
            });

            var flag = false;
            $('#gm-custom-mapoption-streets').on('touchstart click', function () {
                if (!flag) {
                    flag = true;
                    setTimeout(function () {
                        flag = false;
                    }, 500);
                    $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                    $('#gm-custom-mapoption-satstreets').css("color", "#333");
                    $('#gm-custom-mapoption-topo').css("background-color", "#fff");
                    $('#gm-custom-mapoption-topo').css("color", "#333");
                    $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                    $('#gm-custom-mapoption-sat').css("color", "#333");
                    $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                    $('#gm-custom-mapoption-3d').css("color", "#333");
                    $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                    $('#gm-custom-mapoption-terrain').css("color", "#333");
                    $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                    $('#gm-custom-mapoption-outdoors').css("color", "#333");
                    $('#gm-custom-mapoption-streets').css("background-color", "#0ae");
                    $('#gm-custom-mapoption-streets').css("color", "#fff");
                    $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
                    $('#gm-custom-mapoption-natatl').css("color", "#333");

                    toggleMapType('streets');
                    $('#gm-custom-mapdropdown').toggle();

                    $('#search-peaks-btn').prop('disabled', false);
                    $('#q').prop('disabled', false);
                    $('#n').prop('disabled', false);
                    $(".mapboxgl-canvas-container").show();
                    $('.mapboxgl-ctrl-bottom-left').show();
                    $('.mapboxgl-ctrl-bottom-right').show();

                    $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                    $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                    $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Streets</div>');
                }
                return false
            });

            $('#gm-custom-mapoption-streets').on('mouseenter', function () {
                $('#gm-custom-mapdropdown').show();
                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '0px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '0px');
            });

            $('#gm-custom-mapoption-streets').on('mouseleave', function () {
                $('#gm-custom-mapdropdown').hide();
                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
            });

            //Natural atlas stuff
            var flag = false;
            $('#gm-custom-mapoption-natatl').on('touchstart click', function () {
                if (!flag) {
                    flag = true;
                    setTimeout(function () {
                        flag = false;
                    }, 500);
                    $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
                    $('#gm-custom-mapoption-satstreets').css("color", "#333");
                    $('#gm-custom-mapoption-topo').css("background-color", "#fff");
                    $('#gm-custom-mapoption-topo').css("color", "#333");
                    $('#gm-custom-mapoption-sat').css("background-color", "#fff");
                    $('#gm-custom-mapoption-sat').css("color", "#333");
                    $('#gm-custom-mapoption-3d').css("background-color", "#fff");
                    $('#gm-custom-mapoption-3d').css("color", "#333");
                    $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
                    $('#gm-custom-mapoption-terrain').css("color", "#333");
                    $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
                    $('#gm-custom-mapoption-outdoors').css("color", "#333");
                    $('#gm-custom-mapoption-streets').css("background-color", "#fff");
                    $('#gm-custom-mapoption-streets').css("color", "#333");
                    $('#gm-custom-mapoption-natatl').css("background-color", "#0ae");
                    $('#gm-custom-mapoption-natatl').css("color", "#fff");

                    toggleMapType('natural_atlas');
                    $('#gm-custom-mapdropdown').toggle();

                    $('#search-peaks-btn').prop('disabled', false);
                    $('#q').prop('disabled', false);
                    $('#n').prop('disabled', false);
                    $(".mapboxgl-canvas-container").show();
                    $('.mapboxgl-ctrl-bottom-left').show();
                    $('.mapboxgl-ctrl-bottom-right').show();

                    $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                    $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
                    $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Natural Atlas <span style="font-size: 10px;">(US)</span>/div>');
                }
                return false
            });

            $('#gm-custom-mapoption-natatl').on('mouseenter', function () {
                $('#gm-custom-mapdropdown').show();
                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '0px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '0px');
            });

            $('#gm-custom-mapoption-natatl').on('mouseleave', function () {
                $('#gm-custom-mapdropdown').hide();
                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
            });

            $(window).resize(function () {
                $('.more-info-link').trunk8({lines: 2, tooltip: false});
                $('.route-name-title').trunk8({lines: 3, tooltip: false});
                var window_width = $(window).width();
                if (window_width < 768) {
                    //hide the elevation chart and marker

                } else {
                    //show the elevation chart and marker

                }
            });

        }

        function openUrl(url) {
            window.location.href = url;
        }

        function openExternalUrl(url) {
            window.open(url, '_blank');
        }

        function fix_item_location(id, point) {
            $.post('{% url "fix_item_location" %}', {id: id, lat: point.lat(), long: point.lng()},
                function (data) {
                    var output = $("#message_map");
                    var output_div = $("#message_map_div");
                    output.html(data);
                    output_div.show();
                    setTimeout(function () {
                        $("#message_map_div").hide();
                    }, 10000)
                }
            );
        }

        function delete_peak_from_map(id) {
            $.post('{% url "delete_peak_from_map" %}', {id: id},
                function (data) {
                    var output = $("#message_map");
                    var output_div = $("#message_map_div");
                    output.html(data);
                    output_div.show();
                    setTimeout(function () {
                        $("#message_map_div").hide();
                    }, 10000)
                }
            );
        }

        function check_is_in(marker) {
            return map.getBounds().contains(marker.getPosition());
        }

        function delete_out_markers() {
            if (markersArray) {
                for (i in markersArray) {
                    if (!check_is_in(markersArray[i])) {
                        markersArray[i].remove();
                        markersArray.splice(i, 1);
                    }
                }
            }
        }

        function deletehighest() {
            if (markersArray) {
                for (i in markersArray) {
                    if (markersArray[i].properties.iconstyle == 'marker_icon_redgreen' || markersArray[i].properties.iconstyle == 'marker_icon_red') {
                        markersArray[i].remove();
                        markersArray.splice(i, 1);
                    }
                }
            }
        }

        function limit_number_of_markers(limit) {
            if (markersArray.length > limit) {
                for (i = markersArray.length - 1; i >= limit; i--) {
                    markersArray[i].remove();
                    markersArray.splice(i, 1);
                }
            }
        }

        function elevation_range(data) {
            if (markersArray) {
                for (i = markersArray.length - 1; i >= 0; i--) {
                    var inrange = false;
                    $.each(data, function (k, v) {
                        var latLng1 = new mapboxgl.LngLat(v.lng, v.lat);
                        if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng1)) {
                            inrange = true;
                        }
                    });
                    if (!inrange) {
                        markersArray[i].remove();
                        markersArray.splice(i, 1);
                    }
                }
            }
        }

        function delete_old_markers(data) {
            if (markersArray) {
                for (i = markersArray.length - 1; i >= 0; i--) {
                    var inrange = false;
                    $.each(data, function (k, v) {
                        var latLng1 = new mapboxgl.LngLat(v.lng, v.lat);
                        if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng1)) {
                            inrange = true;
                        }
                    });
                    if (!inrange) {
                        markersArray[i].remove();
                        markersArray.splice(i, 1);
                    }
                }
            }
        }

        function delete_old_photo_markers(data) {
            if (photoMarkersArray) {
                for (i = photoMarkersArray.length - 1; i >= 0; i--) {
                    var inrange = false;
                    $.each(data, function (k, v) {
                        var latLng1 = new mapboxgl.LngLat(v.photo_lng, v.photo_lat);
                        if (fromLatLngToString(photoMarkersArray[i].getLngLat()) == fromLatLngToString(latLng1)) {
                            inrange = true;
                        }
                    });
                    if (!inrange) {
                        photoMarkersArray[i].remove();
                        photoMarkersArray.splice(i, 1);
                    }
                }
            }
        }

        function delete_all_photo_markers() {
            if (photoMarkersArray) {
                for (i = photoMarkersArray.length - 1; i >= 0; i--) {
                    photoMarkersArray[i].remove();
                    photoMarkersArray.splice(i, 1);
                }
            }
        }

        function delete_old_challenge_markers(data) {
            if (challengeMarkersArray) {
                for (i = challengeMarkersArray.length - 1; i >= 0; i--) {
                    var inrange = false;
                    $.each(data, function (k, v) {
                        var latLng1 = new mapboxgl.LngLat(v.challenge_lng, v.challenge_lat);
                        if (fromLatLngToString(challengeMarkersArray[i].getLngLat()) == fromLatLngToString(latLng1)) {
                            inrange = true;
                        }
                    });
                    if (!inrange) {
                        challengeMarkersArray[i].remove();
                        challengeMarkersArray.splice(i, 1);
                    }
                }
            }
        }

        function delete_all_challenge_markers() {
            if (challengeMarkersArray) {
                for (i = challengeMarkersArray.length - 1; i >= 0; i--) {
                    challengeMarkersArray[i].remove();
                    challengeMarkersArray.splice(i, 1);
                }
            }
        }

        function fromLatLngToString(latLng) {
            return latLng.lat + ',' + latLng.lng;
        }

        //Updates marker position
        function updateMarker(location) {
            marker.setLngLat(location);
        }

    </script>

{% endblock %}

{% block gallery %}
    <div id="blueimp-gallery" class="blueimp-gallery blueimp-gallery-controls" data-use-bootstrap-modal="false"
         data-slideshow-interval="4000">
        <!-- The container for the modal slides -->
        <div class="slides"></div>
        <!-- Controls for the borderless lightbox -->
        <h3 class="title"></h3>
        <div class="description">
            <div class="description-text">
                <div class="description-text-caption"></div>
                <div class="description-text-user"></div>
            </div>
        </div>
        <a class="prev">‹</a>
        <a class="next">›</a>
        <a class="close">×</a>
        <a class="play-pause"></a>
        <ol class="indicator"></ol>
        <!-- The modal dialog, which will be used to wrap the lightbox content -->
        <div class="modal fade">
            <div class="modal-dialog" style="width: 80%;">
                <div class="modal-content">
                    <div class="modal-header">
                        <button style="color: #fff;" type="button" class="close" aria-hidden="true">&times;</button>
                        <h4 class="modal-title"></h4>
                    </div>
                    <div class="modal-body next"></div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default pull-left prev">
                            <i class="glyphicon glyphicon-chevron-left"></i>
                            Previous
                        </button>
                        <button type="button" class="btn btn-primary next">
                            Next
                            <i class="glyphicon glyphicon-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="loading" style="display: none;">Loading&#8230;</div>

    {% load item_tags %}{% get_gmaps_lib %}
    {% block gmaps_lib %}{% endblock %}
{% endblock %}