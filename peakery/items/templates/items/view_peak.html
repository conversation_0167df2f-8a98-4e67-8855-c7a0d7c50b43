{% extends "base.html" %}
{% load static %}
{% load avatar_tags %}
{% load json_filters %}
{% load item_tags %}
{% load cache %}
{% load humanize %}

{% block css %}
    <link href="{{MEDIA_URL}}css/view-peak.css?v=1.1" rel="stylesheet" type="text/css" media="screen" />
    <style type="text/css">
        #remove-main-peak-photo {
            color: red;
            text-decoration: none;
        }
        #remove-main-peak-photo:hover {
            text-decoration: underline;
        }
    </style>
{% endblock %}

{% block title %}{{ peak_data.peakname_title }} - {{ peak_data.peaklocation_title }}{% endblock %}
{% block titlemeta %}{{ peak_data.peakname_title }} - {{ peak_data.peaklocation_title }}{% endblock %}
{% block description %}{{ peak_data.peak_meta_description }}{% endblock %}
{% block image_rel %}{{ peak_data.thumbnail_910 }}{% endblock %}

{% block meta_facebook %}
    <meta property="og:title" content="{{ peak.name }}"/>
    <meta property="og:site_name" content="peakery.com"/>
    <meta property="og:type" content="website"/>
    <meta property="og:url" content="{{ peak_data.absolute_url }}"/>
    <meta property="og:image" content="{{ peak_data.thumbnail_910 }}"/>
    <meta property="og:description" content="{{ peak_data.peak_meta_description }}"/>
{% endblock %}

{% block meta_google %}
<meta itemprop="name" content="{{ peak.name }}">
<meta itemprop="description" content="{{ peak_data.peak_meta_description }}">
<meta itemprop="image" content="{{ peak_data.thumbnail_910 }}">
{% endblock %}

{% block meta_twitter %}
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="{{ peak.name }}">
<meta name="twitter:description" content="{{ peak_data.peak_meta_description }}">
<meta name="twitter:image" content="{{ peak_data.thumbnail_910 }}">
{% endblock %}

{% block js_globals %}
    var peak_id = {{ peak.id }};
    var peakObject = '{"id": "{{ peak.id }}", "name": {{ peak.name|jsonify|escape_single_quotes }}, "slug": "{{ peak.slug_new_text }}", "is_classic": "{{ peak.is_classic }}", "lat": "{{ peak.lat }}", "lng": "{{ peak.long }}", "summit_count": "{{ peak.summitlog_count }}", "your_summits": "{{ your_summits_count }}", "your_attempts": "{{ your_attempts_count }}", "challenge_count": "{{ challenge_count }}", "elevation": "{{ peak.elevation }}", "prominence": "{{ peak.prominence }}", "thumbnail_url": "{{ peak_data.thumbnail_480 }}", "region": [{% if peak_data.regions %}{% for r in peak_data.regions %}{"region_id": "{{ r.id }}", "region_name": "{{ r.name }}", "region_slug": "{{ r.get_absolute_url }}", "country_name": "{{ r.country.name }}"}{% if not forloop.last %},{% endif %}{% endfor %}{% endif %}], "country": [{% if peak_data.countries %}{% for c in peak_data.countries %}{"country_id": "{{ c.id }}", "country_name": "{{ c.name }}", "country_slug": "{{ c.get_absolute_url }}"}{% if not forloop.last %},{% endif %}{% endfor %}{% endif %}]}';
{% endblock %}
{% block extrajs %}
    <script src="{% static 'js/jquery.cycle.lite.js' %}"></script>
    <script type="text/javascript">
    {% include "mapbox/polyline.js" %}
    </script>
    <script src="{% static 'js/GPXParser.js'%}"></script>
    <style>
          body.modal-open {
                overflow: visible;
            }
            .desktop-admin-link {
                color: #f24100;
            }
    </style>
    <script type="text/javascript">
        $(document).ready(function(){
            $("#photos-list").on('mouseenter','div',function () {
                $(this).children('.user-photo-info').fadeIn(200);
            });

            $("#photos-list").on('mouseleave','div',function () {
                $(this).children('.user-photo-info').fadeOut(200);
            });

            $("#more-photos-list").on('mouseenter','div',function () {
                $(this).children('.user-photo-info').fadeIn(200);
            });

            $("#more-photos-list").on('mouseleave','div',function () {
                $(this).children('.user-photo-info').fadeOut(200);
            });

            $(document).bind('beforeReveal.facebox',function(){
                $('.content').show();
                $('.close').show();
            });

            $(document).bind('loading.facebox',function(){
                $('.content').hide();
                $('.close').hide();
            });

        });
    </script>
{% endblock %}

{% block mapbox %}
{% include "mapbox/mapbox.html" %}
{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row hidden-xs">
        <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 65px; border-top-left-radius:8px; border-top-right-radius:8px;">
            <span style="font-size: 24px; font-weight: 600;">
                <div id="breadcrumbs">
                    <div class="pull-right hidden-xs">
                        <a style="{{ subnav_info_style }}" class="region-header-sub-links" href="/{{ peak.slug_new_text }}/">Info</a>
                        <a class="region-header-sub-links" href="/{{ peak.slug_new_text }}/map/">Map</a>
                        <a class="region-header-sub-links" href="/{{ peak.slug_new_text }}/summits/">Climbs</a>
                        {% if your_summit_count != 0 %}
                            <a href="/{{ peak.slug_new_text }}/summits/#month=&member=you&route=&sort=most_recent&page=1" class="youve-climbed" style="margin-left: 0px;">(You {{ your_summit_count }}x)</a>
                        {% endif %}
                        {% if user.is_authenticated %}
                        <a id="edit-peak-info-header-link" class="region-header-sub-links" href="/peaks/{{ peak.id }}/edit/">Edit Peak Info</a>
                        {% else %}
                        <a id="edit-peak-info-header-link" data-toggle="modal" data-target="#accounts-login" class="region-header-sub-links">Edit Peak Info</a>
                        {% endif %}
                    </div>
                    {% if alternate_names_list %}
                    <div class="ellipsis" style="line-height: 55px;overflow: hidden;">
                        <h1 class="peak-title">{{ peak.name }}<br><div class="ellipsis" style="font-size: 11px;color: #666;margin-top: -32px;font-weight: 300;">also known as {% for name in alternate_names_list %}{{ name }}{% if not forloop.last %}, {% endif %}{% endfor %}</div></h1>
                    </div>
                    {% else %}
                    <div class="ellipsis" style="line-height: 70px; overflow: hidden;">
                         <h1 class="peak-title">{{ peak.name }}</h1>
                    </div>
                    {% endif %}
                </div>
            </span>
        </div>
    </div>

{% endblock %}

{% block content %}

    <script src="{% static 'vendor/s3.fine-uploader/s3.fine-uploader.js' %}"></script>

    <style type="text/css">
        @media screen and (min-width: 1920px) {
            #admin-stuff {
                position: absolute;
                top: -60px;
                right: 0px;
                z-index: 2;
            }
        }
        table.gadget tr:first-child,
        table.gadget tr:last-child {
            display: none !important;
        }
        #navbar-link-peaks > a:hover {
            color: #ffffff;
        }
        p.statsleftrank, .stats-data-bottom {
            color: #999;
        }
        @media screen and (max-width: 767px) and (min-width: 1px) {
            #log-your-climb {
                margin-left: 0px;
            }
            .stats-data-bottom, .stats-data-highlight {
                margin-left: -6px;
            }
            .stats-data-highlight {
                margin-bottom: 5px;
                line-height: 20px;
            }
            .stats-data {
                margin-bottom: 5px;
            }
            .section-header {
                float: left;
                font-size: 14px;
                font-weight: 500;
            }
            .route-card-stats {
                font-size: 12px;
                color: #666;
            }
        }
        @media screen and (max-width: 1023px) and (min-width: 768px) {
            #log-your-climb {
                margin-left: 20px;
                padding: 15px 10px;
                width: 120px;
            }
            .peak-title {
                font-size: 16px;
                font-weight: 600;
            }
            .stats-data {
                margin-bottom: 10px;
            }
            .section-header {
                float: left;
                font-size: 16px;
                font-weight: 500;
            }
            .route-card-stats {
                font-size: 14px;
                color: #666;
            }
        }
        @media screen and (min-width: 1024px) {
            #log-your-climb {
                margin-left: 50px;
                width: 160px;
            }
            .peak-title {
                font-size: 20px;
                font-weight: 600;
            }
            .stats-data {
                margin-bottom: 10px;
            }
            .section-header {
                float: left;
                font-size: 18px;
                font-weight: 500;
            }
            div.description {
                font-size: 16px;
                line-height: 28px;
            }
            .route-card-stats {
                font-size: 14px;
                color: #666;
            }
        }
        @media screen and (max-width: 767px) and (min-width: 1px) {
           #content-body {
               margin-top: 20px;
           }
           .content-pane {
               margin-top: 50px;
           }
           .peak-title {
                font-size: 12px;
                font-weight: 500;
            }
           .stats-header {
               font-size: 14px;
               margin-bottom: 10px;
           }
           .hero-photo-caption-username {
                width: 40%;
            }
           .hero-photo-caption-peakname {
                width: 40%;
            }
       }
        @media screen and (min-width: 768px) {
            .content-pane {
               margin-top: 0px;
           }
        }
        .gm-style-mtc {
            opacity: .8;
        }
        #edit-peak-info {
            background-color: #ffece6;
        }
        #edit-peak-info #edit-peak-info-link {
            color: #f13f01;
        }
        #edit-peak-info:hover {
            background-color: #f24100;
        }
        #edit-peak-info:hover #edit-peak-info-link {
            color: #fff;
        }
        .blueimp-gallery > .description {
          position: absolute;
          bottom: 0px;
          width: 100%;
          text-align: center;
          color: #fff;
          margin-bottom: 2%;
          height: auto;
          display: none;
        }
        .blueimp-gallery-controls > .description {
          display: block;
        }
        .blueimp-gallery-controls > .description > .description-text {
            padding: 10px;
            background: rgba(0, 0, 0, 0.5);
        }
        div#stats.newbox {
            margin-top: 0px;
            width: 100%;
        }
        .sub-header-row {
            border-bottom: none;
        }

        .qq-upload-list {
            box-shadow: none;
        }

        .qq-upload-button-selector {
            width: auto;
        }

        .qq-upload-list li.qq-upload-success {
            background-color: transparent;
            border-bottom: none;
            border-top: none;
        }

        .admin-delete-photo {
            color: #fff;
            border:  solid 2px;
            border-radius: 15px;
            position: absolute;
            right: 7px;
            top: 7px;
            width: 25px;
            padding: 2px;
            padding-left: 5px;
            background: #ccc;
            cursor: pointer;
        }

        .peak-seo-card {
            background-color: #fff;
            padding-top: 10px;
            padding-bottom: 10px;
            width:25%;
        }

        .peak-seo-card-header {
            font-size: 18px;
            font-weight: 500;
            float: left;
        }

        .peak-seo-card-subheader {
            float: right;
            color: #999;
            font-size: 12px;
            font-weight: 300;
            line-height: 36px;
        }

        .peak-seo-info-left {
            font-size: 14px;
            float: left;
            line-height: 2.2em;
        }

        .peak-seo-info-right {
            float: right;
            color: #999;
            font-size: 12px;
            line-height: 30px;
            font-weight: 300;
        }

        @media screen and (min-width: 1680px) {
            .peak-seo-info-left {
                width: 300px;
            }
        }

        @media screen and (max-width: 1679px) and (min-width: 1440px) {
            .peak-seo-info-left {
                width: 200px;
            }
        }

        @media screen and (max-width: 1439px) and (min-width: 1280px) {
            .peak-seo-info-left {
                width: 200px;
            }
        }

        @media screen and (max-width: 1279px) and (min-width: 1px) {
            .peak-seo-info-right {
                display: none;
            }
            .peak-seo-info-left {
                width: 200px;
            }
        }

        .hover-cell:hover, .table-hover-cell:hover {
            background-image: linear-gradient(to bottom,#fde1d6,#fde1d6) !important;
        }

        @media screen and (max-width: 767px) and (min-width: 1px) {
            .featured-logs-thumbnail {
                display: none;
            }
        }

        @media screen and (min-width: 768px) {
            .featured-logs-thumbnail {
                float: left;
                width: 100px;
            }
            .featured-logs-description {
                margin-left: 170px;
            }
        }
        #slideshow1 {
            border-bottom-left-radius:0px !important;
        }
    </style>

    <script type="text/template" id="qq-image-template">
        <div class="qq-uploader-selector qq-uploader" qq-drop-area-text="" style="max-height: none; background-color: #f6f6f6; border: none; padding: 0px; min-height: 0px; overflow-y: visible;">
            <div class="qq-upload-drop-area-selector qq-upload-drop-area" qq-hide-dropzone>
                <span class="qq-upload-drop-area-text-selector"></span>
            </div>
            <ul class="qq-upload-list-selector qq-upload-list" aria-live="polite" aria-relevant="additions removals" style="max-height: none;">
                <li style="padding: 0px; margin: 0px;">
                    <div class="qq-thumbnail-wrapper" style="position: relative; align-content: stretch;">
                        <div>
                            <img class="qq-thumbnail-selector" qq-max-size="300" qq-server-scale style="margin-right: 0px;">
                        </div>
                    </div>
                    <div class="qq-upload-retry-container">
                        <button type="button" class="qq-upload-retry-selector qq-upload-retry btn btn-secondary" style="font-size: 12px;">Retry</button>
                    </div>
                </li>
            </ul>
            <div class="qq-upload-button-selector btn btn-secondary">
                <div class="qq-upload-button-text">Add jpg peak photo</div>
            </div>
            <span class="qq-drop-processing-selector qq-drop-processing">
                <span>Processing dropped files...</span>
                <span class="qq-drop-processing-spinner-selector qq-drop-processing-spinner"></span>
            </span>

            <dialog class="qq-alert-dialog-selector">
                <div class="qq-dialog-message-selector"></div>
                <div class="qq-dialog-buttons">
                    <button type="button" class="qq-cancel-button-selector btn btn-secondary">Close</button>
                </div>
            </dialog>

            <dialog class="qq-confirm-dialog-selector">
                <div class="qq-dialog-message-selector"></div>
                <div class="qq-dialog-buttons">
                    <button type="button" class="qq-cancel-button-selector btn btn-secondary">No</button>
                    <button type="button" class="qq-ok-button-selector btn btn-secondary">Yes</button>
                </div>
            </dialog>

            <dialog class="qq-prompt-dialog-selector">
                <div class="qq-dialog-message-selector"></div>
                <input type="text">
                <div class="qq-dialog-buttons">
                    <button type="button" class="qq-cancel-button-selector btn btn-secondary">Cancel</button>
                    <button type="button" class="qq-ok-button-selector btn btn-secondary">Ok</button>
                </div>
            </dialog>
        </div>
    </script>

    <script type="application/ld+json">
        {
            "@context": "http://schema.org",
            "@type": "Mountain",
            "name": "{{ peak.name }}",
            {% if highlights %}
            "description": "{% for h in highlights %}{{ h.highlight }}...  {% endfor %}",
            {% endif %}
            "image": "{{ peak_data.thumbnail_910 }}",
            "additionalProperty": [
                {
                    "@type": "propertyValue",
                    "name": "Elevation",
                    "value": "{{ peak_data.elevation }}"
                },
                {
                    "@type": "propertyValue",
                    "name": "Country",
                    "value": "{{ peak_data.ubicacion_onlycountryname }}"
                }
                {% if peak.range %}
                ,{
                    "@type": "propertyValue",
                    "name": "MountainRange",
                    "value": "{{ peak.range }}"
                }
                {% endif %}
                {% if peak.prominence or peak.prominence == 0 %}
                ,{
                    "@type": "propertyValue",
                    "name": "Prominence",
                    "value": "{{ peak_data.prominence }}"
                }
                {% endif %}
            ],
            "geo": {
                "@type": "GeoCoordinates",
                "latitude": "{{ peak.lat }}",
                "longitude": "{{ peak.long }}"
            }
        }
    </script>

    <div class="container">
        <!-- Mobile header -->
        <div class="row sub-header-row hidden-lg hidden-md hidden-sm" style="height: 50px; position: fixed; width: 100%; z-index: 999;">
            <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999; display: flex; justify-content: space-between;">
                <span><a style="{{ subnav_info_style }}" class="mobile-header-sub-links" href="/{{ peak.slug_new_text }}/">Info</a></span>
                {% if IS_MOBILE_APP_ACCESS == 'True' %}
                <span><a id="mobile-app-map-link" class="mobile-header-sub-links">Map</a></span>
                {% else %}
                <span><a class="mobile-header-sub-links" href="/{{ peak.slug_new_text }}/map/">Map</a></span>
                {% endif %}
                <span><a class="mobile-header-sub-links" href="/{{ peak.slug_new_text }}/summits/">Climbs</a>
                {% if your_summit_count != 0 %}
                    <a class="mobile-header-sub-links youve-climbed" href="/{{ peak.slug_new_text }}/summits/#type=you" style="margin-left: 0px;">(You {{ your_summit_count }}x)</a></span>
                    {% if IS_MOBILE_APP_ACCESS == 'True' %}
                    <span><a id="log-your-climb" class="mobile-header-sub-links" href="javascript: Android.logClimb(peakObject);" style="color: #00b1f2;">Log climb</a></span>
                    {% else %}
                    <span><a id="log-your-climb" class="mobile-header-sub-links" href="{% url "log_climb_multi_simple" %}?peak={{ peak.id }}" style="color: #00b1f2;">Log climb</a></span>
                    {% endif %}
                {% else %}
                    {% if user.is_authenticated %}
                        {% if IS_MOBILE_APP_ACCESS == 'True' %}
                        </span><span><a id="log-your-climb" class="mobile-header-sub-links" href="javascript: Android.logClimb(peakObject);" style="color: #00b1f2;">Log climb</a></span>
                        {% else %}
                        </span><span><a id="log-your-climb" class="mobile-header-sub-links" href="{% url "log_climb_multi_simple" %}?peak={{ peak.id }}" style="color: #00b1f2;">Log climb</a></span>
                        {% endif %}
                    {% else %}
                    </span><span><a id="log-your-climb" class="mobile-header-sub-links" style="color: #00b1f2; cursor: pointer;" data-toggle="modal" data-target="#accounts-login">Log climb</a></span>
                    {% endif %}
                {% endif %}
            </div>
        </div>
        <!-- End mobile header -->

        <div class="row content-pane" style="overflow: hidden; background-color: #c0c0c0;">

            <div id="peak-photo-col" class="col-lg-6 col-md-6 col-sm-6 col-xs-12" style="cursor: pointer; padding-right: 0px; padding-left: 0px; border-bottom: solid 1px #e0e0e0;">
                <div class="peakinfoimg-responsive">
                  {% if default_photo %}
                      <div style="width: 100%; height: 100%;" onclick="openUrl('/{{ peak.slug_new_text }}');">
                          <img style="width: 100%; height: 100%;" src="{% static ''%}{{ default_photo }}">
                      </div>
                      <div class="empty-photo-info" style="z-index: 99;">
                        <span class="data hero-photo-caption-peakname" style="position: absolute; bottom: 0px; left: 10px; color: #fff;">
                            <p class="bagger ellipsis" style="font-size: 14px;">
                            </p>
                        </span>
                        <span class="data hero-photo-caption-username" style="position: absolute; bottom: 0px; right: 10px; color: #fff;">
                            <p class="bagger ellipsis" style="font-size: 14px; text-align: right;">
                                {% if IS_MOBILE_APP_ACCESS == 'True' %}

                                {% else %}
                                {% if user.is_authenticated %}
                                <a class="add-photo" style="cursor: pointer; color: #00B1F2; font-weight: 500; font-size: 14px;">add peak photo</a>
                                {% else %}
                                <a id="add-photo-top-link" class="join-peakery" style="cursor: pointer; color: #00B1F2; font-weight: 500; font-size: 14px;">add peak photo</a>
                                {% endif %}
                                {% endif %}
                            </p>
                        </span>
                      </div>
                  {% else %}
                      <div class="hero-slideshow" id="slideshow1">
                          <div style="width: 100%; height: 100%;">
                              <a data-user="{% if peak.thumbnail_source %}{{ peak.thumbnail_credit }}{% elif peak_data.main_photo and peak_data.main_photo.user %}{{ peak_data.main_photo.user.username }}{% endif %}" data-credit="" data-description="{% if peak_data.main_photo and peak_data.main_photo.caption %}{{ peak_data.main_photo.caption }}{% endif %}" class="gallery-link" data-gallery href="{{ peak_data.thumbnail_1920 }}">
                              <div style="width: 100%; height: 100%;" class="hover-photos">
                                  <div style="width: 100%; height: 100%; cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ peak_data.thumbnail_910 }}'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;"></div>
                              </div>
                              </a>
                              <div class="hero-user-photo-info" style="z-index: 99;">
                                <span class="data hero-photo-caption-peakname" style="position: absolute; bottom: 0px; left: 10px; color: #fff;">
                                    <p class="bagger ellipsis" style="font-size: 14px;">
                                        {% if peak.thumbnail_source %}
                                        <a target="_blank" href="{{ peak.thumbnail_source }}" style="color: #fff;"><i style="margin-right: 5px;" class="fa fa-camera" aria-hidden="true"></i>{{ peak.thumbnail_credit }}</a>
                                        {% elif peak_data.main_photo and peak_data.main_photo.user %}
                                        <a href="/members/{{ peak_data.main_photo.user.username }}/" style="color: #fff;"><i style="margin-right: 5px;" class="fa fa-camera" aria-hidden="true"></i>{{ peak_data.main_photo.user.username }}</a>
                                        {% endif %}
                                    </p>
                                </span>
                                <span class="data hero-photo-caption-username" style="position: absolute; bottom: 0px; right: 10px; color: #fff;">
                                    <p class="bagger ellipsis" style="font-size: 14px; text-align: right;">
                                        {% if IS_MOBILE_APP_ACCESS == 'True' %}

                                        {% else %}
                                        {% if user.is_authenticated %}
                                        <a class="add-photo" style="cursor: pointer; color: #00B1F2; font-weight: 500; font-size: 14px;">add photo</a>
                                        {% if user.is_staff %}
                                            <a id="remove-main-peak-photo" style="cursor: pointer; color: #f24100; font-weight: 500; font-size: 14px;">remove</a>
                                        {% endif %}
                                        {% else %}
                                        <a style="cursor: pointer; color: #00B1F2; font-weight: 500; font-size: 14px;" data-toggle="modal" data-target="#accounts-login">add photo</a>
                                        {% endif %}
                                        {% endif %}
                                    </p>
                                </span>
                              </div>
                          </div>
                          {% if top_four_photos %}
                          {% for p in top_four_photos %}
                          <div onclick="$('#gallery-photo-{{ p.id }}').click();" style="width: 100%; height: 100%;">
                              <div style="width: 100%; height: 100%;" class="hover-photos">
                                  <div style="width: 100%; height: 100%; cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ MEDIA_URL }}{{ p.fullsize_url }}'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;"></div>
                              </div>
                              <div class="hero-user-photo-info" style="z-index: 99;">
                                <span class="data hero-photo-caption-peakname" style="position: absolute; bottom: 0px; left: 10px; color: #fff;">
                                    <p class="bagger ellipsis" style="font-size: 14px;">
                                        <a href="/members/{{ p.username }}/" style="color: #fff;"><i style="margin-right: 5px;" class="fa fa-camera" aria-hidden="true"></i>{{ p.username }}</a>
                                    </p>
                                </span>
                                <span class="data hero-photo-caption-username" style="position: absolute; bottom: 0px; right: 10px; color: #fff;">
                                    <p class="bagger ellipsis" style="font-size: 14px; text-align: right;">
                                        {% if user.is_authenticated %}
                                        <a class="add-photo" style="cursor: pointer; color: #00B1F2; font-weight: 500; font-size: 14px;">add photo</a>
                                        {% if user.is_staff %}
                                            <a id="remove-main-peak-photo" style="cursor: pointer; color: #f24100; font-weight: 500; font-size: 14px;">remove</a>
                                        {% endif %}
                                        {% else %}
                                        <a class="join-peakery" style="cursor: pointer; color: #00B1F2; font-weight: 500; font-size: 14px;">add photo</a>
                                        {% endif %}
                                    </p>
                                </span>
                              </div>
                          </div>
                          {% endfor %}
                          {% endif %}
                      </div>
                  {% endif %}
                </div>
            </div>

            <div id="peak-map-col" onclick="openUrl('/{{ peak.slug_new_text }}/map/');" class="col-lg-6 col-md-6 col-sm-6 hidden-xs" style="padding-right: 0px; padding-left: 0px; background-image: url('{{ peak_thumbnail }}'); background-position: center; background-size: 1260px 945px; border-bottom: solid 1px #e0e0e0; border-left: solid 1px #e0e0e0;">
                <div id="map-canvas" style="width: 100%; height: 100%; cursor: pointer;"></div>
                <div id="center-peak-marker" style="position: absolute; top: 100px; left: 100px;"><img class="marker-pulse" src="{% static 'img/<EMAIL>' %}"></div>
            </div>

        </div>

        <div class="row">
            <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell" style="max-height: 200px; cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
                <div>
                    <h2 class="stats-header">Elevation</h2>
                    <p class="stats-data peak-elevation-formatted">{{ peak_data.elevation }}</p>
                    <p class="edit-peak-info-link"><a style="color: #f13f01;" data-toggle="modal" data-target="#edit-elevation" data-peakid="{{ peak.id }}" data-elevation="{{ peak_data.elevation_in_feet }}" class="admin-edit-elevation desktop-admin-link hidden-xs hidden-sm hidden-md">edit</a></p>
                    <div class="stats-data-bottom">
                    {% for r in region_ranks %}
                        <p class="statsleftrank statsleft {% if forloop.counter > 1 %}hidden-xs{% endif %}">#{{ r.region_elevation_rank|intcomma }} in <a href="/{{ r.country_slug }}-mountains/{{ r.region_slug }}/peaks/#order=elevation&page=1">{{ r.region_name }}</a></p>
                    {% endfor %}
                    {% for c in country_ranks %}
                        <p class="statsleftrank statsleft {% if forloop.counter > 1 %}hidden-xs{% endif %}">#{{ c.country_elevation_rank|intcomma }} in <a href="/region/{{ c.country_slug }}-mountains/peaks/#order=elevation&page=1">{{ c.country_name }}</a></p>
                    {% endfor %}
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell" style="max-height: 200px; cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
                <div>
                    <h2 class="stats-header">Prominence<div class="pull-right"><a class="profile-modal-link" data-toggle="modal" data-target="#about-prominence">about</a></div></h2>
                    {% if peak.prominence or peak.prominence == 0 %}
                    <p class="stats-data peak-prominence-data peak-prominence-formatted">{{ peak_data.prominence }}</p>
                    <p style="display: none;" class="stats-data-missing peak-prominence-missing"><a rel="facebox" href="{% url "peak_edit_info" peak.id  %}" style="color: #BF3929">missing please add!</a></p>
                    <p class="edit-peak-info-link"><a style="color: #f13f01;" data-toggle="modal" data-target="#edit-prominence" data-peakid="{{ peak.id }}" data-prominence="{{ peak.get_prominence_in_feet }}" class="admin-edit-prominence desktop-admin-link hidden-xs hidden-sm hidden-md">edit</a></p>
                    <div class="stats-data-bottom">
                    {% for r in region_ranks %}
                        <p class="statsleftrank statsleft {% if forloop.counter > 1 %}hidden-xs{% endif %}">#{{ r.region_prominence_rank|intcomma }} in <a href="/{{ r.country_slug }}-mountains/{{ r.region_slug }}/peaks/#order=prominence&page=1">{{ r.region_name }}</a></p>
                    {% endfor %}
                    {% for c in country_ranks %}
                        <p class="statsleftrank statsleft {% if forloop.counter > 1 %}hidden-xs{% endif %}">#{{ c.country_prominence_rank|intcomma }} in <a href="/region/{{ c.country_slug }}-mountains/peaks/#order=prominence&page=1">{{ c.country_name }}</a></p>
                    {% endfor %}
                    </div>
                    {% else %}
                    <p style="display: none;" class="stats-data peak-prominence-data peak-prominence-formatted"></p>
                    <p class="stats-data-missing peak-prominence-missing"><a rel="facebox" href="{% url "peak_edit_info" peak.id  %}" style="color: #BF3929">missing please add!</a></p>
                    <p class="edit-peak-info-link"><a style="color: #f13f01;" data-toggle="modal" data-target="#edit-prominence" data-peakid="{{ peak.id }}" data-prominence="{{ peak.get_prominence_in_feet }}" class="admin-edit-prominence desktop-admin-link hidden-xs hidden-sm hidden-md">edit</a></p>
                    {% endif %}
                </div>
            </div>
            {% if peak.range %}
            <div data-url="/peaks/#range={{ peak.range|urlencode }}" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell peak-row peak-range-div" style="max-height: 200px; cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            {% else %}
            <div data-url="" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell peak-row peak-range-div" style="max-height: 200px; cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            {% endif %}
                <div>
                    <h2 class="stats-header">Range</h2>
                    {% if peak.range %}
                        <p class="stats-data peak-range-data"><a class="peak-range-formatted" href="/peaks/#range={{ peak.range|urlencode }}">{{ peak.range }}</a></p>
                        <p style="display: none;" class="stats-data-missing peak-range-missing"><a rel="facebox" href="{% url "peak_edit_info" peak.id  %}" style="color: #BF3929">missing please add!</a></p>
                    {% else %}
                        <p style="display: none;" class="stats-data peak-range-data"><a class="peak-range-formatted" href="/peaks/#range={{ peak.range|urlencode }}">{{ peak.range }}</a></p>
                        <p class="stats-data-missing peak-range-missing"><a rel="facebox" href="{% url "peak_edit_info" peak.id  %}" style="color: #BF3929">missing please add!</a></p>
                    {% endif %}
                    <p class="edit-peak-info-link"><a style="color: #f13f01;" data-toggle="modal" data-target="#edit-range" data-peakid="{{ peak.id }}" data-range="{% if peak.range %}{{ peak.range }}{% endif %}" class="admin-edit-range desktop-admin-link hidden-xs hidden-sm hidden-md">edit</a></p>
                </div>
            </div>
            <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell" style="max-height: 200px; cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
                <div>
                    <h2 class="stats-header">Region</h2>
                    {% with peak.get_ubication_names as ubication_names %}
                        {% if ubication_names %}
                            {% for location in ubication_names %}
                                {% if location.country.slug %}
                                <p class="stats-data-highlight"><a href="/{{ location.country.slug }}-mountains/{{ location.slug }}/">{{ location.get_ubication_onlyname_title }}</a>{% if not forloop.last %}<span style="color: #000;"> / </span>{% endif %}</p>
                                {% else %}
                                <p class="stats-data-highlight"><a href="/region/{{ location.slug }}-mountains/">{{ location.get_ubication_onlyname_title }}</a>{% if not forloop.last %}<span style="color: #000;"> / </span>{% endif %}</p>
                                {% endif %}
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                </div>
            </div>
            <div onclick="openUrl('/{{ peak.slug_new_text }}/summits/');" class="box-blc-web col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell" style="max-height: 200px; cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
                <div>
                    <h2 class="stats-header">Climbs</h2>
                    {% if summits_count > 0 %}
                    <p class="stats-data-highlight"><a href="/{{ peak.slug_new_text }}/summits/">{{ summits_count }} climb{{ summits_count|pluralize:"s" }}</a></p>
                    <div class="stats-data-bottom">
                    {% for r in region_ranks %}
                        <p class="statsleftrank statsleft {% if forloop.counter > 1 %}hidden-xs{% endif %}">#{{ r.region_summits_rank|intcomma }} in <a href="/{{ r.country_slug }}-mountains/{{ r.region_slug }}/peaks/#order=most_summits&page=1">{{ r.region_name }}</a></p>
                    {% endfor %}
                    {% for c in country_ranks %}
                        <p class="statsleftrank statsleft {% if forloop.counter > 1 %}hidden-xs{% endif %}">#{{ c.country_summits_rank|intcomma }} in <a href="/region/{{ c.country_slug }}-mountains/peaks/#order=most_summits&page=1">{{ c.country_name }}</a></p>
                    {% endfor %}
                    </div>
                    {% else %}
                    <p class="stats-data-missing">no climbs yet</p>
                    {% endif %}
                </div>
            </div>
            {% if most_recently_bagged %}
            <div onclick="openUrl('/{{ peak.slug_new_text }}/summits/{{ most_recently_bagged.id }}/');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell" style="max-height: 200px; cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
                <div>
                    <h2 class="stats-header">Last climb</h2>
                    <p class="stats-data-highlight" style="margin-bottom: 0px;"><a href="/{{ peak.slug_new_text }}/summits/{{ most_recently_bagged.id }}/">{{ most_recently_bagged.date|date:"M j, Y" }} / <time class="timeago" datetime="{{ most_recently_bagged.date|date:"M j, Y"|default:"" }}T00:00:00">{{ most_recently_bagged.date|date:"M j, Y"|default:"" }}T00:00:00</time></a></p>
                    <p class="stats-data-highlight"><span style="color: #333;">by </span><a href="/members/{{ most_recently_bagged.user }}/">{{ most_recently_bagged.user }}</a></p>
                </div>
            </div>
            {% else %}
            <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell" style="max-height: 200px; cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
                <div>
                    <h2 class="stats-header">Last climb</h2>
                    <p class="stats-data-missing">no climbs yet</p>
                </div>
            </div>
            {% endif %}
            <div onclick="openUrl('/{{ peak.slug_new_text }}/summits/');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell" style="max-height: 200px; cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
                <div>
                    <h2 class="stats-header">Top climbing months</h2>
                    {% if top_three_months %}
                        {% for m in top_three_months %}
                            <p class="stats-data"><a href="/{{ peak.slug_new_text }}/summits/#month={{ m.month_number }}&member=all&route=&order=most_recent&page=1">{{ m.summitlog_month }}</a><span style="color: #999;">&nbsp;&nbsp;{{ m.pct_total }}%</span></p>
                        {% endfor %}
                    {% else %}
                    <p class="stats-data-missing">no info yet</p>
                    {% endif %}
                </div>
            </div>
            {% with most_popular_route|slice:"0:1" as routes %}
            {% if routes %}
            {% for route in routes %}
            <div onclick="openUrl('/{{ peak.slug_new_text }}/routes/{{ route.id }}/');" class="box-brc-web col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell" style="max-height: 200px; cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            {% endfor %}
            {% else %}
            <div onclick="openUrl('/{{ peak.slug_new_text }}/routes/');" class="box-brc-web col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell" style="max-height: 200px; cursor: pointer; border-right: 1px solid #c0c0c0; border-bottom: 1px solid #c0c0c0;">
            {% endif %}
            {% endwith %}
                <div>
                    <h2 class="stats-header">Most climbed route</h2>
                    {% with most_popular_route|slice:"0:1" as routes %}
                        {% if routes %}
                            {% for route in routes %}
                                <p class="stats-data-highlight"><a href="/{{ peak.slug_new_text }}/routes/{{ route.id }}/">{{ route.name }}</a></p>
                                {% if peak_data.is_usa %}
                                    {% if route.difficulty and route.elevation_gain and route.total_distance %}
                                        <div class="stats-data-bottom">{% mi_into_mi route.total_distance %}&nbsp;&bull;&nbsp;{{ route.elevation_gain|floatformat:"0"|intcomma }} ft gain&nbsp;&bull;&nbsp;{{ route.difficulty }}</div>
                                    {% elif route.difficulty and route.elevation_gain %}
                                        <div class="stats-data-bottom">{{ route.elevation_gain|floatformat:"0"|intcomma }} ft gain&nbsp;&bull;&nbsp;{{ route.difficulty }}</div>
                                    {% elif route.difficulty and route.total_distance %}
                                        <div class="stats-data-bottom">{% mi_into_mi route.total_distance %}&nbsp;&bull;&nbsp;{{ route.difficulty }}</div>
                                    {% elif route.elevation_gain and route.total_distance %}
                                        <div class="stats-data-bottom">{% mi_into_mi route.total_distance %}&nbsp;&bull;&nbsp;{{ route.elevation_gain|floatformat:"0"|intcomma }} ft gain</div>
                                    {% elif route.difficulty %}
                                        <div class="stats-data-bottom">{{ route.difficulty }}</div>
                                    {% elif route.elevation_gain %}
                                        <div class="stats-data-bottom">{{ route.elevation_gain|floatformat:"0"|intcomma }} ft gain</div>
                                    {% elif route.total_distance %}
                                        <div class="stats-data-bottom">{% mi_into_mi route.total_distance %}</div>
                                    {% endif %}
                                {% else %}
                                    {% if route.difficulty and route.elevation_gain and route.total_distance %}
                                        <div class="stats-data-bottom">{% mi_into_km route.total_distance %}&nbsp;&bull;&nbsp;{{ route.elevation_gain_in_m|floatformat:"0"|intcomma }} m gain&nbsp;&bull;&nbsp;{{ route.difficulty }}</div>
                                    {% elif route.difficulty and route.elevation_gain %}
                                        <div class="stats-data-bottom">{{ route.elevation_gain_in_m|floatformat:"0"|intcomma }} m gain&nbsp;&bull;&nbsp;{{ route.difficulty }}</div>
                                    {% elif route.difficulty and route.total_distance %}
                                        <div class="stats-data-bottom">{% mi_into_km route.total_distance %}&nbsp;&bull;&nbsp;{{ route.difficulty }}</div>
                                    {% elif route.elevation_gain and route.total_distance %}
                                        <div class="stats-data-bottom">{% mi_into_km route.total_distance %}&nbsp;&bull;&nbsp;{{ route.elevation_gain_in_m|floatformat:"0"|intcomma }} m gain</div>
                                    {% elif route.difficulty %}
                                        <div class="stats-data-bottom">{{ route.difficulty }}</div>
                                    {% elif route.elevation_gain %}
                                        <div class="stats-data-bottom">{{ route.elevation_gain_in_m|floatformat:"0"|intcomma }} m gain</div>
                                    {% elif route.total_distance %}
                                        <div class="stats-data-bottom">{% mi_into_km route.total_distance %}</div>
                                    {% endif %}
                                {% endif %}
                            {% endfor %}
                        {% else %}
                        <p class="stats-data-missing"><a style="color: #BF3929;" href="/{{ peak.slug_new_text }}/routes/">no info yet</a></p>
                        {% endif %}
                    {% endwith %}
                </div>
            </div>
        </div>
        {% if user.is_authenticated %}
        <div id="edit-peak-info" class="row hidden-sm hidden-md hidden-lg" onclick="openUrl('/peaks/{{ peak.id }}/edit/');" style="cursor: pointer; border-bottom-left-radius:8px; border-bottom-right-radius:8px;">
            <div class="col-md-12" style="height: 70px; text-align: center; padding-top: 20px;">
                <a id="edit-peak-info-link" href="/peaks/{{ peak.id }}/edit/" style="font-weight: 700;">edit peak info</a>
            </div>
        </div>
        {% else %}
        <div id="edit-peak-info" class="row hidden-sm hidden-md hidden-lg" data-toggle="modal" data-target="#accounts-login" style="cursor: pointer;">
            <div class="col-md-12" style="height: 70px; text-align: center; padding-top: 20px;">
                <a id="edit-peak-info-link" style="font-weight: 700;">edit peak info</a>
            </div>
        </div>
        {% endif %}
        {% if user.is_superuser %}
        <div class="row desktop-admin-link hidden-xs hidden-sm hidden-md" style="background-color: #ffece6; padding-top: 20px; padding-bottom: 20px; border-top: 1px solid #c0c0c0;">
            <div class="col-md-12">
                <h2 class="section-header">Edit Challenges for {{ peak.name }}</h2>
                <div class="pull-right"><a target="_blank" href="/admin/items/item/{{ peak.id }}/">full admin</a></div>
            </div>
            <div class="col-md-12">
                <div id="assigned-challenges">
                {% for c in peak_challenges %}
                <div id="admin-edit-challenge-{{ peak.id }}-div" class="admin-edit-challenges desktop-admin-link hidden-xs hidden-sm hidden-md" data-peakid="{{ peak.id }}" data-challengeid="{{ c.id }}"><input data-peakid="{{ peak.id }}" data-challengeid="{{ c.id }}" class="admin-edit-challenge-checkbox" type="checkbox" id="admin-edit-challenge-{{ c.id }}" checked> {{ c.name }}</div>
                {% endfor %}
                </div>
                <div id="latest-challenge-added">
                {% for c in latest_challenge_added %}
                <div id="admin-edit-challenge-{{ peak.id }}-div" class="admin-edit-challenges desktop-admin-link hidden-xs hidden-sm hidden-md" data-peakid="{{ peak.id }}" data-challengeid="{{ c.id }}"><input data-peakid="{{ peak.id }}" data-challengeid="{{ c.id }}" class="admin-edit-challenge-checkbox" type="checkbox" id="admin-edit-challenge-{{ c.id }}"> {{ c.name }}</div>
                {% endfor %}
                </div>
                <div id="more-challenges-link-div" style="padding-top: 10px;">
                <p><a id="see-all-challenges-link">See all challenges...</a><a style="display: none;" id="see-fewer-challenges-link">See fewer challenges...</a></p>
                </div>
                <div style="display: none;" id="more-challenges">
                {% for c in more_challenges %}
                <div id="admin-edit-challenge-{{ peak.id }}-div" class="admin-edit-challenges desktop-admin-link hidden-xs hidden-sm hidden-md" data-peakid="{{ peak.id }}" data-challengeid="{{ c.id }}"><input data-peakid="{{ peak.id }}" data-challengeid="{{ c.id }}" class="admin-edit-challenge-checkbox" type="checkbox" id="admin-edit-challenge-{{ c.id }}"> {{ c.name }}</div>
                {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}

        <div class="row dark-background-row">
            <div class="sp-60"></div>
        </div>

        <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="padding-right: 15px; padding-left: 15px;">
                <div class="row" id="highlights-header">
                    <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 70px; border-top-left-radius:8px; border-top-right-radius:8px;">
                        <h2 class="section-header">Highlights</h2>
                        <div id="edit-highlights-link-div" class="pull-right">
                            {% if user.is_authenticated %}
                                {% if highlights %}
                                    <a id="edit-highlights-link" style="cursor: pointer; color: #00B1F2; font-weight: 500; font-size: 14px;">edit highlights</a>
                                {% else %}
                                    <a id="edit-highlights-link" style="cursor: pointer; color: #00b1f2; font-weight: 500; font-size: 14px;">add a highlight!</a>
                                {% endif %}
                            {% else %}
                                {% if highlights %}
                                    <a data-toggle="modal" data-target="#accounts-login" style="cursor: pointer; color: #00B1F2; font-weight: 500; font-size: 14px;">edit highlights</a>
                                {% else %}
                                    <a data-toggle="modal" data-target="#accounts-login" style="cursor: pointer; color: #00b1f2; font-weight: 500; font-size: 14px;">add a highlight!</a>
                                {% endif %}
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="row" id="highlights-content" style="{% if not highlights %}display: none; {% endif %}background-color: #fff; border-top: 1px solid #c0c0c0; padding-top: 20px; padding-left: 8px; border-bottom-left-radius:8px; border-bottom-right-radius:8px;{% if not highlights %} display: none;{% endif %}">
                    <div class="col-md-12">
                        <div class="peak_desc">
                            <div class="description">
                                <ul id="highlights-list">
                                {% if highlights %}
                                    {% for h in highlights %}
                                        <li style="list-style: initial; margin-bottom: 20px;">{{ h.highlight }}</li>
                                    {% endfor %}
                                {% endif %}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row" id="edit-highlights-header" style="display: none;">
                    <div class="col-md-12" style="height: 70px; background-color: #feece5; line-height: 70px; font-size: 18px; font-weight: 500;">
                        <h2 class="section-header"><span style="color: #f24100;">Highlights</span></h2>
                    </div>
                </div>
                <div class="row" id="edit-highlights-form" style="display: none; background-color: #feece5; border-top: 1px solid #c0c0c0; padding-top: 20px; padding-left: 10px;">
                    <div class="col-md-12">
                        <div class="peak_desc">
                            <div class="description" style="margin-left: -10px;">
                                <form id="edithighlights_form" method="POST" action="/peaks/edit_highlights/{{ peak.id }}/">
                                <fieldset id="peak-highlights-fieldset">
                                {% if highlights %}
                                    {% for h in highlights %}
                                        <div>
                                            <textarea class="peak-highlight-input" name="peak-highlight-{{ forloop.counter }}"  data-index="{{ forloop.counter }}" id="peak-highlight-{{ forloop.counter }}" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ peak.name }}...">{{ h.highlight }}</textarea>
                                        </div>
                                        {% if forloop.last %}
                                        <div>
                                            <textarea class="peak-highlight-input" name="peak-highlight-{{ forloop.counter|add:1 }}" data-index="{{ forloop.counter|add:1 }}" id="peak-highlight-new" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ peak.name }}..."></textarea>
                                        </div>
                                        {% endif %}
                                    {% endfor %}
                                {% else %}
                                    <div>
                                        <textarea class="peak-highlight-input" name="peak-highlight-1" data-index="1" id="peak-highlight-1" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ peak.name }}..."></textarea>
                                    </div>
                                {% endif %}
                                </fieldset>
                                <button style="float: left; width: 170px; height: 50px; font-size: 16px; padding: 0 20px;" class="btn set2 input" id="edit-highlights-save" type="submit">Save highlights</button>&nbsp;<a style="float: left; margin-left: 30px; margin-top: 17px; cursor: pointer; color: #999;" id="edit-highlights-cancel">Cancel</a>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row dark-background-row">
            <div class="sp-60"></div>
        </div>

        {% if routes %}

        <div class="row">
            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="padding-right: 15px; padding-left: 15px;">
                <div class="row" id="featured-routes-header">
                    <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 70px; font-size: 18px; font-weight: 500; border-top-left-radius:8px; border-top-right-radius:8px;">
                        <h2 class="section-header">Routes</h2>
                    </div>
                </div>

                <div class="row sub-header-row" style="background-color: #fff; border-top: 1px solid #c0c0c0;">
                    <div class="col-md-12" style="padding-left: 0px; padding-right: 0px;">
                        <div class="peak_desc" style="padding: 0px;">
                            <div class="description" style="margin-bottom: 0px; padding-top: 0px;">
                                {% for r in routes %}
                                    <div class="hover-cell" onclick="openUrl('/{{ peak.slug_new_text }}/routes/{{ r.id }}/');" style="width: 100%; display: table; padding: 15px; cursor: pointer; background-image: linear-gradient(to bottom,#fff,#f6f6f6);">
                                        <div style="display: table-cell; vertical-align: middle; padding-right: 15px;">
                                            <img id="route-map-{{ r.id }}" style="width: 150px; border-radius:8px;">
                                        </div>
                                        <div style="display: table-cell; vertical-align: middle; width: 100%;">
                                            <div style="margin-bottom: 0px;" class="stats-data-highlight"><a style="font-weight: 500;" href="/{{ peak.slug_new_text }}/routes/{{ r.id }}/">{{ r.name }}</a></div>
                                            <div class="route-card-stats card-subheader">{% if r.summit_count %}{{ r.summit_count }} climb{{ r.summit_count|pluralize:"s" }}{% endif %}{% if r.total_distance %}&nbsp;&bull;&nbsp;{% if peak_data.is_usa %}{% mi_into_mi r.total_distance %}{% else %}{% mi_into_km r.total_distance %}{% endif %}{% endif %}{% if r.elevation_gain %}&nbsp;&bull;&nbsp;{% if peak_data.is_usa %}{% ft_into_ft r.elevation_gain %}{% else %}{% ft_into_m r.elevation_gain %}{% endif %} gain{% endif %}{% if r.avg_trip_time > 0 %}&nbsp;&bull;&nbsp;{% sec_to_hrs_min r.avg_trip_time %}{% endif %}{% if r.difficulty %}&nbsp;&bull;&nbsp;{{ r.difficulty }}{% endif %}</div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row" id="routes-footer">
             <div class="col-md-12" style="height: 8px; background-color: #f6f6f6; border-bottom-left-radius:8px; border-bottom-right-radius:8px;">
             </div>
        </div>

        {% if user.is_superuser %}
        <div class="row desktop-admin-link hidden-xs hidden-sm hidden-md" style="background-color: #ffece6; padding-top: 20px; padding-bottom: 20px; border-top: 1px solid #c0c0c0;">
            <div class="col-md-12">
                <h2 class="section-header">Combine Routes for {{ peak.name }}</h2>
                <div class="pull-right"><a target="_blank" href="/admin/items/item/{{ peak.id }}/">full admin</a></div>
            </div>
            <div class="col-md-12">
                <div id="combine-peak-routes">
                <form method="POST" action="/peaks/{{ peak.id }}/routes/combine/">
                <table>
                <tr>
                    <td style="width: 60px;">From</td>
                    <td style="width: 60px;">To</td>
                    <td style="width: 60px;">GPX</td>
                    <td>Route</td>
                </tr>
                {% for r in routes %}
                <tr>
                    <td><input type="radio" class="combine-from-route" name="rdoFromRoute" id="rdoFromRoute{{ r.id }}" value="{{ r.id }}" style="margin-left: 10px; margin-bottom: 10px;"></td>
                    <td><input disabled type="radio" class="combine-to-route" name="rdoToRoute" id="rdoToRoute{{ r.id }}" value="{{ r.id }}" style="margin-left: 10px; margin-bottom: 10px;"></td>
                    <td><input disabled type="radio" class="combine-to-gpx" name="rdoToGpx" id="rdoToGpx{{ r.id }}" value="{{ r.id }}" style="margin-left: 10px; margin-bottom: 10px;"></td>
                    <td>{{ r.name }} <a style="font-size: 12px;" href="/peaks/{{ peak.id }}/routes/delete/{{ r.id }}/">[delete]</a></td>
                </tr>
                {% endfor %}
                </table>
                <input type="submit" value="Combine" id="btnAdminCombineRoutes" class="btn btn-secondary" style="width: 150px;height: 40px;margin-top: 10px;padding: 10px;">
                </form>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="row dark-background-row">
            <div class="sp-60"></div>
        </div>

        {% endif %}

        {% if featured_logs %}

        <div class="row" id="featured-logs-header">
            <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 70px; font-size: 18px; font-weight: 500; border-top-left-radius:8px; border-top-right-radius:8px;">
                <h2 class="section-header">Latest climbs</h2>
                <div class="pull-right">
                    <a style="cursor: pointer; color: #00B1F2; font-weight: 500; font-size: 14px;" href="/{{ peak.slug_new_text }}/summits/">see all climbs</a>
                </div>
            </div>
        </div>

        <div class="row sub-header-row" style="background-color: #fff; border-top: 1px solid #c0c0c0;">
            <div class="col-md-12" style="padding-left: 0px; padding-right: 0px;">
                <div class="peak_desc" style="padding: 0px;">
                    <div class="description" style="margin-bottom: 0px; padding-top: 0px;">
                        {% for l in featured_logs %}
                            <div class="hover-cell" onclick="openUrl('/{{ peak.slug_new_text }}/summits/{{ l.id }}/');" style="display: inline-block; padding: 15px; cursor: pointer; background-image: linear-gradient(to bottom,#fff,#f6f6f6);">
                                <div class="featured-logs-thumbnail">
                                    <img class="hover-photos" src="{{ MEDIA_URL }}{{ l.thumbnail_url }}" style="width: 150px; aspect-ratio:4/3; border-radius:8px;">
                                </div>
                                <div class="featured-logs-description">"{{ l.log_text }}" &mdash; <i><span style="color: #00b1f2; font-weight: 500;">{{ l.username }} &bull; {{ l.summitlog_date|date:"M j, Y" }}</span></i></div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        <div class="row" id="climbs-footer">
             <div class="col-md-12" style="height: 8px; background-color: #f6f6f6; border-bottom-left-radius:8px; border-bottom-right-radius:8px;">
             </div>
        </div>


        <div class="row dark-background-row">
            <div class="sp-60"></div>
        </div>

        {% endif %}

        <div class="row" id="photos-header">
            <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 70px; font-size: 18px; font-weight: 500; border-top-left-radius:8px; border-top-right-radius:8px;">
                <h2 class="section-header">Photos</h2>
                <div id="add-photo-link-div" class="pull-right">
                    {% if user.is_authenticated %}
                    <a class="add-photo" style="cursor: pointer; color: #00B1F2; font-weight: 500; font-size: 14px;">add a <span class="hidden-xs">{{ peak.name }}</span> photo</a>
                    {% else %}
                    <a class="join-peakery" style="cursor: pointer; color: #00B1F2; font-weight: 500; font-size: 14px;">add a <span class="hidden-xs">{{ peak.name }}</span> photo</a>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="row sub-header-row">
            <div class="col-md-12">
                <div class="row" id="ajax-data-loading" style="display: none;">
                  <div class="col-md-12" style="text-align: center; color: #c0c0c0; margin-top: 20px; margin-bottom: 20px;">
                    <i class="fa fa-spinner fa-spin fa-5x"></i>
                  </div>
                </div>
                <div id="photos-list" class="row"></div>
                <div class="row" id="more-photos-list"></div>
                <div class="row" id="more-ajax-data-loading" style="display: none;">
                  <div class="col-md-12" style="text-align: center; color: #c0c0c0; margin-top: 20px; margin-bottom: 20px;">
                    <i class="fa fa-spinner fa-spin fa-5x"></i>
                  </div>
                </div>
            </div>
        </div>

        <div class="row" id="photos-footer" style="display: none;">
            <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 70px; font-size: 18px; font-weight: 500; border-bottom-left-radius:8px; border-bottom-right-radius:8px;">
                <div id="see-more-photos-div" style="float: left; display: none;">
                    <a id="see-more-photos" style="cursor: pointer; color: #00B1F2; font-weight: 500; font-size: 14px;">See more <span class="hidden-xs">{{ peak.name }}</span> photos</a>
                </div>
            </div>
        </div>

        <div class="row dark-background-row">
            <div class="sp-60"></div>
        </div>

        {% if summits_count > 0 %}
        <div class="row" id="awards-header">
            <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 70px; font-size: 18px; font-weight: 500; border-top-left-radius:8px; border-top-right-radius:8px;">
                <h2 class="section-header">Awards</h2>
                <div id="about-awards-link-div" class="pull-right">
                    <a id="about-awards-link" data-toggle="modal" data-target="#about-awards" style="cursor: pointer; color: #00B1F2; font-weight: 500; font-size: 14px;">about<img style="height: 25px; margin-left: 10px;" src="{% static 'img/award-icons.png' %}"></a>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="row sub-header-row">
            {% for award in king_of_the_mountain %}
                <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-{{ forloop.counter }}" style="padding-right: 0px; padding-left: 0px; background-image: url('{{MEDIA_URL}}{{ award.avatar_url|urlencode }}'); background-size: cover; background-position: center top; overflow: hidden;">
                    <a href="/members/{{ award.username }}" title="{{ award.username }}"><img class="img-responsive peakeryPhoto photography peakimg-responsive" src="{% static 'img/spacer.png' %}"></a>
                    <div style="position: absolute; top: 10px; left: 10px;"><img src="{% static 'img/badge_crown.png' %}" style="height: 25px; margin-right: 8px;"></div>
                    <div class="user-photo-info">
                        <span class="data photo-caption-peakname" style="position: absolute; bottom: -5px; left: 10px; color: #fff;">
                            <p class="bagger ellipsis" style="font-size: 10px;"><a href="/members/{{ award.username }}" style="color: #fff;">{{ award.username }}</a></p>
                        </span>
                        <span class="data hidden-md hidden-sm hidden-xs photo-caption-username" style="position: absolute; bottom: -5px; right: 10px; color: #fff; text-align: right;">
                            <p class="bagger ellipsis" style="font-size: 10px; text-align: right;"><a href="/members/{{ award.username }}" style="color: #fff;">{{ award.summitlog_count }} climb{{ award.summitlog_count|pluralize:"s" }}</a></p>
                        </span>
                    </div>
                </div>
            {% endfor %}
            {% for award in first_ascent %}
                <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-{{ forloop.counter|add:king_of_the_mountain_count }}" style="padding-right: 0px; padding-left: 0px; background-image: url('{{MEDIA_URL}}{{ award.avatar_url|urlencode }}'); background-size: cover; background-position: center top; overflow: hidden;">
                    <a href="/members/{{ award.username }}" title="{{ award.username }}"><img class="img-responsive peakeryPhoto photography peakimg-responsive" src="{% static 'img/spacer.png' %}"></a>
                    <div style="position: absolute; top: 10px; left: 10px;"><img src="{% static 'img/badge_flag.png' %}" style="height: 25px; margin-right: 8px;"></div>
                    <div class="user-photo-info">
                        <span class="data photo-caption-peakname" style="position: absolute; bottom: -5px; left: 10px; color: #fff;">
                            <p class="bagger ellipsis" style="font-size: 10px;"><a href="/members/{{ award.username }}" style="color: #fff;">{{ award.username }}</a></p>
                        </span>
                        <span class="data hidden-md hidden-sm hidden-xs photo-caption-username" style="position: absolute; bottom: -5px; right: 10px; color: #fff; text-align: right;">
                            <p class="bagger ellipsis" style="font-size: 10px; text-align: right;"><a href="/members/{{ award.username }}" style="color: #fff;"><time class="timeago" datetime="{{ award.log_date|date:"M j, Y"|default:"" }}T00:00:00">{{ award.log_date|date:"M j, Y"|default:"" }}T00:00:00</time></a></p>
                        </span>
                    </div>
                </div>
            {% endfor %}
            {% for award in summit_stewards %}
                <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-{{ forloop.counter|add:king_of_the_mountain_count|add:first_ascent_count }}" style="padding-right: 0px; padding-left: 0px; background-image: url('{{MEDIA_URL}}{{ award.avatar_url|urlencode }}'); background-size: cover; background-position: center top; overflow: hidden;">
                    <a href="/members/{{ award.username }}" title="{{ award.username }}"><img class="img-responsive peakeryPhoto photography peakimg-responsive" src="{% static 'img/spacer.png' %}"></a>
                    <div style="position: absolute; top: 10px; left: 10px;"><img src="{% static 'img/badge_shield.png' %}" style="height: 25px; margin-right: 8px;"></div>
                    <div class="user-photo-info">
                        <span class="data photo-caption-peakname" style="position: absolute; bottom: -5px; left: 10px; color: #fff;">
                            <p class="bagger ellipsis" style="font-size: 10px;"><a href="/members/{{ award.username }}" style="color: #fff;">{{ award.username }}</a></p>
                        </span>
                        <span class="data hidden-md hidden-sm hidden-xs photo-caption-username" style="position: absolute; bottom: -5px; right: 10px; color: #fff; text-align: right;">
                            <p class="bagger ellipsis" style="font-size: 10px; text-align: right;"><a href="/members/{{ award.username }}" style="color: #fff;">{{ award.summitlog_count }} climb{{ award.summitlog_count|pluralize:"s" }}</a></p>
                        </span>
                    </div>
                </div>
            {% endfor %}
        </div>

                {% if summits_count > 0 %}
            <div class="row" id="awards-footer">
                <div class="col-md-12" style="height: 70px; background-color: #f6f6f6; border-bottom-left-radius:8px; border-bottom-right-radius:8px;">
                </div>
            </div>

                {% endif %}

        <div class="row dark-background-row">
            <div class="sp-60"></div>
        </div>

        {% if challenges %}

        <div class="row" id="challenges-header">
            <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 70px; font-size: 18px; font-weight: 500; border-top-left-radius:8px; border-top-right-radius:8px;">
                <h2 class="section-header">Challenges<span class="card-subheader hidden-xs" style="margin-left: 15px;">featuring {{ peak.name }}</span></h2>
            </div>
        </div>

        <div class="row sub-header-row">
            {% for c in challenges %}
                <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 hover-photos photo-grid-{{ forloop.counter }}" style="padding-right: 0px; padding-left: 0px; background-image: url('{{ c.get_thumbnail_480_or_default }}'); background-size: cover; background-position: center top; overflow: hidden;">
                    <a href="/challenges/{{ c.slug }}" title="{{ c.name }}"><img class="img-responsive peakeryPhoto photography peakimg-responsive" src="{% static 'img/spacer.png' %}"></a>
                    <div class="user-photo-info">
                        <span class="data photo-caption-peakname-only" style="position: absolute; bottom: -5px; left: 10px; color: #fff;">
                            <p class="bagger ellipsis" style="font-size: 10px;">
                                <a href="/challenges/{{ c.slug }}" style="color: #fff;">{{ c.name }}</a>
                            </p>
                        </span>
                    </div>
                </div>
            {% endfor %}
        </div>
        <div class="row" id="challenges-footer">
                <div class="col-md-12" style="height: 70px; background-color: #f6f6f6; border-bottom-left-radius:8px; border-bottom-right-radius:8px;">
                </div>
            </div>
        <div class="row dark-background-row">
            <div class="sp-60"></div>
        </div>

        {% endif %}


        <div class="row sub-header-row" style="border-radius:8px;">

        {% if most_climbed_within_100 %}

        <div class="peak-seo-card col-md-3 hidden-sm hidden-xs" style="border-right: 1px solid #c0c0c0; aspect-ratio: auto; border-top-left-radius:8px; border-bottom-left-radius:8px; ">
            <div class="peak-seo-card-header"><span class="hidden-md">Most climbed peaks</span><span class="hidden-lg">Popular peaks</span></div>
            <div class="peak-seo-card-subheader">{% if peak_data.is_usa %}within 100 mi{% else %}within 160 km{% endif %}</div>
            {% for p in most_climbed_within_100 %}
            <div class="peak-seo-info-left ellipsis"><a href="/{{ p.slug_new_text }}">{{ p.name }}</a></div>
            <div class="peak-seo-info-right">{{ p.summitlog_count }} summit{{ p.summitlog_count|pluralize:"s" }}</div>
            {% endfor %}
        </div>

        {% endif %}

        {% if nearest_items %}

        <div class="peak-seo-card col-md-3 hidden-sm hidden-xs" style="border-right: 1px solid #c0c0c0; aspect-ratio: auto;">
            <div class="peak-seo-card-header">Nearest peaks</div>
            <div class="peak-seo-card-subheader">distance away</div>
            {% for p in nearest_items %}
            <div class="peak-seo-info-left ellipsis"><a href="/{{ p.slug_new_text }}">{{ p.name }}</a></div>
            <div class="peak-seo-info-right">{% if peak_data.is_usa %}{% mi_into_mi p.distance %}{% else %}{% mi_into_km p.distance %}{% endif %}</div>
            {% endfor %}
        </div>

        {% endif %}

        {% if highest_10_nearby_peaks %}

        <div class="peak-seo-card col-md-3 hidden-sm hidden-xs" style="border-right: 1px solid #c0c0c0; aspect-ratio: auto;">
            <div class="peak-seo-card-header">Highest peaks</div>
            <div class="peak-seo-card-subheader">{% if peak_data.is_usa %}within 100 mi{% else %}within 160 km{% endif %}</div>
            {% for p in highest_10_nearby_peaks %}
            <div class="peak-seo-info-left ellipsis"><a href="/{{ p.slug_new_text }}">{{ p.name }}</a></div>
            <div class="peak-seo-info-right">{% if peak_data.is_usa %}{% ft_into_ft p.elevation %}{% else %}{% ft_into_m p.elevation %}{% endif %}</div>
            {% endfor %}
        </div>

        {% endif %}

        {% if recent_10_climbed_peaks %}

        <div class="peak-seo-card col-md-3 hidden-sm hidden-xs" style="border-right: 1px solid #c0c0c0; aspect-ratio: auto; border-top-right-radius:8px; border-bottom-right-radius:8px;">
            <div class="peak-seo-card-header"><span class="hidden-md">Latest climbed peaks</span><span class="hidden-lg">Latest peaks</span></div>
            <div class="peak-seo-card-subheader">{% if peak_data.is_usa %}within 100 mi{% else %}within 160 km{% endif %}</div>
            {% for p in recent_10_climbed_peaks %}
                <div class="peak-seo-info-left ellipsis"><a href="/{{ p.slug_new_text }}">{{ p.name }}</a></div>
                <div class="peak-seo-info-right"><time class="timeago" datetime="{{ p.last_summit_date|date:"M j, Y"|default:"" }}T00:00:00">{{ p.last_summit_date|date:"M j, Y"|default:"" }}T00:00:00</time></div>
            {% endfor %}
        </div>

        {% endif %}

        </div>

        <div class="row hidden-lg hidden-md hidden-sm">
            <div style="height: 180px;"></div>
        </div>
        <div class="row hidden-xs">
            <div style="height: 56px;"></div>
        </div>

        <div class="about-prominence-modal modal fade" id="about-prominence" tabindex="-1" role="dialog" aria-labelledby="about-prominence-label">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                      <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                      <h4 class="modal-title" id="about-prominence-label">A note on <span style="color: #f24100;">Prominence</span></h4>
                    </div>
                    <div class="modal-body">
                        <p style="line-height: 32px;">A peak’s prominence, also known as topographic prominence or relative height, is a measure of how distinct a peak is from other peaks. It’s defined as the vertical distance between a peak and the lowest contour line surrounding that peak and no higher peak. Prominence is a popular metric for peaks for two reasons: 1) it’s objective and relatively easy to calculate, and 2) higher prominence peaks are more likely to be interesting with higher independence vs. peaks with lower prominence.</p>
                        <p style="line-height: 32px;">Note that prominence is not the same thing as a peak’s vertical drop, which is usually extremely difficult to calculate because a peak’s base elevation can be highly subjective. Instead, peakery shows vertical gain for specific routes up peaks.</p>
                        <p style="line-height: 32px;">Prominence ranks are calculated only for peaks with known prominence. Note that since many peaks in peakery still have unknown prominence, the ranks are subject to change as new prominence info is added.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="about-awards-modal modal fade" id="about-awards" tabindex="-1" role="dialog" aria-labelledby="about-awards-label">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                      <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                      <h4 class="modal-title" id="about-awards-label">About <span style="color: #f24100;">peakery awards</span></h4>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="margin-bottom: 20px; font-size: 21px;">Log climbs on peakery to earn awards:</div>
                        </div>
                        <div class="row">
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2"><img src="{% static 'img/about-awards-badge.png' %}" style="height: 75px;"></div>
                            <div class="col-lg-10 col-md-10 col-sm-10 col-xs-10" style="margin-bottom: 16px; font-size: 15px; margin-top: 10px;"><strong>Peak Badges</strong>: Earn a special badge for each unique peak you summit. See all of your badges on your Badges page.</div>
                        </div>
                        <div class="row">
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2"><img src="{% static 'img/about-awards-flag.png' %}" style="height: 75px;"></div>
                            <div class="col-lg-10 col-md-10 col-sm-10 col-xs-10" style="margin-bottom: 16px; font-size: 15px; margin-top: 10px;"><strong>First Ascent Award</strong>: Only 1 available per peak. Goes to the first peakery member to log a successful summit of a peak. Snag this award and the peak will forever bear your name.</div>
                        </div>
                        <div class="row">
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2"><img src="{% static 'img/about-awards-crown.png' %}" style="height: 75px;"></div>
                            <div class="col-lg-10 col-md-10 col-sm-10 col-xs-10" style="margin-bottom: 16px; font-size: 15px; margin-top: 10px;"><strong>King of the Mountain Award</strong>: Only 1 available per peak. Summit a peak more times than any other member. Beware: this award can be lost!</div>
                        </div>
                        <div class="row">
                            <div class="col-lg-2 col-md-2 col-sm-2 col-xs-2"><img src="{% static 'img/about-awards-shield.png' %}" style="height: 75px;"></div>
                            <div class="col-lg-10 col-md-10 col-sm-10 col-xs-10" style="margin-bottom: 16px; font-size: 15px; margin-top: 10px;"><strong>Summit Steward</strong>: Summit a peak at least 5 times to become one of its Summit Stewards. As Steward of a peak, you’re encouraged to keep that peak’s info up-to-date on peakery and spread goodwill on your future climbs up the peak.</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="add-photo-modal modal fade" id="add-photo" tabindex="-1" role="dialog" aria-labelledby="add-photo-label">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                      <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                      <h4 class="modal-title" id="about-awards-label">Add photo <span class="hidden-xs">for </span><span class="hidden-xs" style="color: #f24100;">{{ peak.name }}</span></h4>
                    </div>
                    <div class="modal-body">
                        <div id="add-photo-form-container" class="hidden-xs">
                            <div class="row">
                                <div class="col-md-12" style="text-align: center; margin-bottom: 20px; font-size: 21px;">Did you take a good photo of this peak?</div>
                            </div>
                            <div class="row">
                                <div class="col-md-12" style="text-align: center; margin-bottom: 20px; font-size: 21px;">
                                    <div id="peak-photo-upload"></div>
                                </div>
                            </div>
                            <div id="add-photo-error-container" class="hidden-xs">
                                <div id="add-photo-error-message-container" class="row">
                                    <div class="col-md-12" style="text-align: center;"><span id="add-photo-error-message" style="color: #ff0000;"></span></div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12" style="text-align: left; margin-bottom: 20px; font-size: 15px; margin-top: 10px;">
                                    <p>peakery features 1 photo of each peak - ideally one that:</p>
                                    <ul style="list-style: inherit; margin-left: 20px;">
                                        <li>shows the mountain as a whole (not just the summit or views)</li>
                                        <li>is landscape orientation (but no panos)</li>
                                        <li>is at least 1920x1440 dimensions and max size 10MB</li>
                                    </ul>
                                    <p>We'll select the best photo to feature. If your photo isn't featured, it may still be added to the full collection of photos for this peak. Thanks for contributing!</p>
                                </div>
                            </div>
                        </div>
                        <div id="mobile-add-photo-error-container" class="hidden-sm hidden-md hidden-lg">
                            <div id="mobile-add-photo-error-message-container" class="row">
                                <div class="col-md-12" style="text-align: center; margin-bottom: 20px; font-size: 21px;"><span id="mobile-add-photo-error-message"></span></div>
                            </div>
                            <div id="mobile-add-photo-error-close-container" class="row">
                                <div class="col-md-12" style="text-align: center; margin-bottom: 20px; font-size: 21px;">
                                    <button id="add-photo-close-error" class="btn btn-secondary" style="width: 120px;">Close</button>
                                </div>
                            </div>
                        </div>
                        <div id="add-photo-success-container" style="display: none;">
                            <div class="row">
                                <div class="col-md-12" style="text-align: center; margin-bottom: 20px; font-size: 21px;">Thanks. We’ll review and let you know when it’s approved.</div>
                            </div>
                            <div class="row">
                                <div id="add-photo-success-photo" class="col-md-12" style="text-align: center; margin-bottom: 20px;"></div>
                            </div>
                            <div class="row">
                                <div class="col-md-12" style="text-align: center; margin-bottom: 20px; font-size: 21px;">
                                    <button id="add-photo-close-success" class="btn btn-secondary" style="width: 120px;">Close</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    {% if request.user.is_superuser %}
    <div class="edit-elevation-modal modal fade" id="edit-elevation" tabindex="-1" role="dialog" aria-labelledby="edit-elevation-label">
        <div class="modal-dialog" role="document" style="max-width: 600px;">
            <div class="modal-content">
                <div class="modal-header">
                  <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                  <h4 class="modal-title" id="edit-elevation-label">Edit Elevation</h4>
                </div>
                <div class="modal-body">
                    <div class="row row-full-width summitlog-section"  style="padding-bottom: 20px;">
                        <div class="col-md-12">
                            <span class="field-title">Elevation</span>
                            <div class="field-title-spacer"></div>
                            <fieldset>
                                <div>
                                    <input value="" type="number" name="peak-elevation" id="peak-elevation" style="width: 125px; padding: 10px;" placeholder="in feet...">
                                    <input type="hidden" id="peak-elevation-id" value="">
                                    <div class="btn-group" data-toggle="buttons" style="margin-left: 15px;">
                                        <label class="btn active">
                                        <input type="radio" name="peak-elevation-units" id="peak-elevation-ft" value="ft" checked><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i> <span>  ft</span>
                                        </label>
                                        <label class="btn" style="margin-left: 5px;">
                                        <input type="radio" name="peak-elevation-units" id="peak-elevation-m" value="m"><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i><span> m</span>
                                        </label>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                    </div>
                    <div class="row row-full-width summitlog-section"  style="padding-bottom: 20px;">
                        <div class="pull-left save-changes-button-div">
                            <button class="btn btn-secondary set2 input save-changes-button" id="save-peak-elevation" style="width: 120px;">Save</button>
                        </div>
                    </div>
                    <div id="edit-elevation-message" class="row row-full-width summitlog-section" style="display: none; padding-bottom: 20px; color: #F24100;"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="edit-prominence-modal modal fade" id="edit-prominence" tabindex="-1" role="dialog" aria-labelledby="edit-prominence-label">
        <div class="modal-dialog" role="document" style="max-width: 600px;">
            <div class="modal-content">
                <div class="modal-header">
                  <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                  <h4 class="modal-title" id="edit-prominence-label">Edit Prominence</h4>
                </div>
                <div class="modal-body">
                    <div class="row row-full-width summitlog-section" style="padding-bottom: 20px;">
                        <div class="col-md-12">
                            <span class="field-title">Prominence</span>
                            <div class="field-title-spacer"></div>
                            <fieldset>
                                <div>
                                    <input value="" type="number" name="peak-prominence" id="peak-prominence" style="width: 125px; padding: 10px;" placeholder="in feet...">
                                    <input type="hidden" id="peak-prominence-id" value="">
                                    <div class="btn-group" data-toggle="buttons" style="margin-left: 15px;">
                                        <label class="btn active">
                                        <input type="radio" name="peak-prominence-units" id="peak-prominence-ft" value="ft" checked><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i> <span>  ft</span>
                                        </label>
                                        <label class="btn" style="margin-left: 5px;">
                                        <input type="radio" name="peak-prominence-units" id="peak-prominence-m" value="m"><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i><span> m</span>
                                        </label>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                    </div>
                    <div class="row row-full-width summitlog-section"  style="padding-bottom: 20px;">
                        <div class="pull-left save-changes-button-div">
                            <button class="btn btn-secondary set2 input save-changes-button" id="save-peak-prominence" style="width: 120px;">Save</button>
                        </div>
                    </div>
                    <div id="edit-prominence-message" class="row row-full-width summitlog-section" style="display: none; padding-bottom: 20px; color: #F24100;"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="edit-range-modal modal fade" id="edit-range" tabindex="-1" role="dialog" aria-labelledby="edit-range-label">
        <div class="modal-dialog" role="document" style="max-width: 600px;">
            <div class="modal-content">
                <div class="modal-header">
                  <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                  <h4 class="modal-title" id="edit-range-label">Edit Range</h4>
                </div>
                <div class="modal-body">
                    <div class="row row-full-width summitlog-section" style="padding-bottom: 20px;">
                        <div class="col-md-12">
                            <span class="field-title">Range</span>
                            <div class="field-title-spacer"></div>
                            <fieldset class="peakRange">
                                <input value="" type="text" name="peak-range" id="peak-range" style="width: 50%; min-width: 340px; padding: 10px;" placeholder="enter mountain range name..."></input>
                                <input type="hidden" id="peak-range-id" value="">
                            </fieldset>
                        </div>
                    </div>
                    <div class="row row-full-width summitlog-section"  style="padding-bottom: 20px;">
                        <div class="pull-left save-changes-button-div">
                            <button class="btn btn-secondary set2 input save-changes-button" id="save-peak-range" style="width: 120px;">Save</button>
                        </div>
                    </div>
                    <div id="edit-range-message" class="row row-full-width summitlog-section" style="display: none; padding-bottom: 20px; color: #F24100;"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="confirm-modal modal fade" id="confirm-modal" tabindex="-1" role="dialog" aria-labelledby="confirm-modal-label">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                  <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                  <h4 class="modal-title" id="confirm-modal-label"></h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div id="confirm-modal-body" class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="margin-bottom: 5px; font-size: 16px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

<script type="text/javascript">

    var initial_highlights = [];
    {% for h in highlights %}
        initial_highlights.push('{{ h.highlight }}');
    {% endfor %}

    var mapHeight = $('#peak-photo-col').height();
    $('#map-canvas').height(mapHeight);

    var uploaderIdle = true;
    var viewer;
    var topo;
    var outdoors;
    var center = null;
    var map_bounds;
    var init = false;
    var photos_displayed = 0;
    var photos_page = 1;
    var photos = [];
    var pageX, pageY, mapX, mapY;
    var iconstyle;
    var combineFromId, combineToId;

    $(function(){

        $("#mobile-app-map-link").on('click', function() {
            //Android.peakCoordinates('{{ peak.lat }},{{ peak.long }}');
            //Android.peakId('{{ peak.id }}');
            Android.peakInfo(peakObject);
        });

        //add log this climb option to log climb dropdown
        $('#log-climb-log-manually .navbar-primary .log-climb-dropdown-label-div').html('Log climb of another peak');
        $('#log-climb-log-this-peak .navbar-primary').attr('href','/peaks/log_climb/?peak={{ peak.id }}');
        $('#log-climb-log-this-peak .navbar-primary .log-climb-dropdown-label-div').html('Log {{ peak.name }}');
        $('#log-climb-log-this-peak').show();

//            Top-left nav button ("find peaks") width to be it same at the width of the leftCol:
        var leftColWidth = $('div#explore .leftCol').width();
        $('li.headlink').css('width', leftColWidth);
//            Peak name input needs to show the remove text icon when the user enter text on it:
        var a = $('a#clear_peak_name');
        var a2 = $('a#clear_near_location');

        var input = $('input#q');
        var input2 = $('input#n');

        if ( input.val() != "" ) {
            a.css('display', 'block');
        }

        if ( input2.val() != "" ) {
            a2.css('display', 'block');
        }

        input.keyup(function(){
            if( $(this).val() != "" ) {
                a.css('display', 'block');
            } else {
                a.css('display', 'none');
            }
        });

        input2.keyup(function(){
            if( $(this).val() != "" ) {
                a2.css('display', 'block');
            } else {
                a2.css('display', 'none');
            }
        });

        a.click(function(){
            input.val('');
            $(this).css('display', 'none');
        });

        a2.click(function(){
            input2.val('');
            $(this).css('display', 'none');
        });

        $('#peak-search').css({left: '0px'});

        $(document).on('click', '.peak-row', function (e) {
            var url = $(this).data('url');
            if (url != '') {
                window.location.href = url;
            }
        });

        //add gpx to route maps
        {% if routes %}
            {% for r in routes %}
                $('#route-map-{{ r.id }}').attr("src", '{{ r.thumbnail }}')
            {% endfor %}
        {% endif %}

        {% if request.user.is_superuser %}
        $(document).on('click', '.admin-edit-elevation', function (e) {
            var peak_id = $(this).data('peakid');
            $('#peak-elevation-id').val(peak_id);
            $('#peak-elevation').val('');
            $("#peak-elevation-ft").click();
            $("#edit-elevation-message").html('');
            $("#edit-elevation-message").hide();
            var elevation = $(this).data('elevation');
            $('#peak-elevation').val(elevation);
            //pre-select meters?
            {% if not peak_data.is_usa %}
            $('#peak-elevation-m').click();
            {% endif %}
            setTimeout(function() {
                $('#peak-elevation').focus().select();
             }, 500);
        });
        $("#peak-elevation-m").on('change', function() {
            setElevationMeters();
        });
        $("#peak-elevation-ft").on('change', function() {
            setElevationFeet();
        });
        $('#save-peak-elevation').on('click', function() {
            if ($('#peak-elevation').val() != '') {
                $('#save-peak-elevation').prop('disabled', true);
                $('#save-peak-elevation').html('<i class="fa fa-spinner fa-spin"></i>');
                admin_update_peak_elevation();
            }
        });

        $(document).on('click', '.admin-edit-prominence', function (e) {
            var peak_id = $(this).data('peakid');
            $('#peak-prominence-id').val(peak_id);
            $('#peak-prominence').val('');
            $("#peak-prominence-ft").click();
            $("#edit-prominence-message").html('');
            $("#edit-prominence-message").hide();
            var prominence = $(this).data('prominence');
            $('#peak-prominence').val(prominence);
            //pre-select meters?
            {% if not peak_data.is_usa %}
            $('#peak-prominence-m').click();
            {% endif %}
            setTimeout(function() {
                $('#peak-prominence').focus().select();
             }, 500);
        });
        $("#peak-prominence-m").on('change', function() {
            setProminenceMeters();
        });
        $("#peak-prominence-ft").on('change', function() {
            setProminenceFeet();
        });
        $('#save-peak-prominence').on('click', function() {
            $('#save-peak-prominence').prop('disabled', true);
            $('#save-peak-prominence').html('<i class="fa fa-spinner fa-spin"></i>');
            admin_update_peak_prominence();
        });

        $(document).on('click', '.admin-edit-range', function (e) {
            var peak_id = $(this).data('peakid');
            $('#peak-range-id').val(peak_id);
            $('#peak-range').val('');
            $("#edit-range-message").html('');
            $("#edit-range-message").hide();
            var range = $(this).data('range');
            $('#peak-range').val(range);
            setTimeout(function() {
                $('#peak-range').focus().select();
             }, 500);
        });
        $('#save-peak-range').on('click', function() {
            $('#save-peak-range').prop('disabled', true);
            $('#save-peak-range').html('<i class="fa fa-spinner fa-spin"></i>');
            admin_update_peak_range();
        });

        $(document).on('click', '.admin-edit-challenge-checkbox', function (e) {
            var peak_id = $(this).data('peakid');
            var challenge_id = $(this).data('challengeid');
            var is_checked = $('input[id=admin-edit-challenge-'+challenge_id+']:checked').val();
            var action = 'remove';
            if (is_checked == 'on') {
                action = 'add';
            }
            //console.log('peak:'+peak_id+' challenge:'+challenge_id+' checked:'+is_checked);
            $.post('{% url "admin_update_peak_to_challenge" %}', {id:peak_id, challengeid:challenge_id, action:action},
                    function(data) {
                        alert(data);
                    }
            );
        });

        $('.combine-from-route').on('click', function() {
            //disable all to and gpx radio buttons
            $(".combine-to-route").each(function() {
                $(this).attr('disabled', true);
                $(this).attr('checked', false);
            });
            $(".combine-to-gpx").each(function() {
                $(this).attr('disabled', true);
                $(this).attr('checked', false);
            });
            $(".combine-from-route").each(function() {
                if (this.checked) {
                    combineFromId = $(this).val();
                    console.log($(this));
                    $(".combine-to-route").each(function() {
                        if ($(this).val() != combineFromId) {
                            $(this).attr('disabled', false);
                        }
                    });
                }
            });
        });

        $('.combine-to-route').on('click', function() {
            //disable all gpx radio buttons
            $(".combine-to-gpx").each(function() {
                $(this).attr('disabled', true);
                $(this).attr('checked', false);
            });
            $(".combine-to-route").each(function() {
                if (this.checked) {
                    combineToId = $(this).val();
                    console.log($(this));
                    $(".combine-to-gpx").each(function() {
                        if ($(this).val() == combineFromId || $(this).val() == combineToId) {
                            $(this).attr('disabled', false);
                        }
                    });
                }
            });
        });

        $('#see-all-challenges-link').on('click', function() {
            $('#more-challenges').show();
            $('#see-fewer-challenges-link').show();
            $('#see-all-challenges-link').hide();
        });

        $('#see-fewer-challenges-link').on('click', function() {
            $('#more-challenges').hide();
            $('#see-all-challenges-link').show();
            $('#see-fewer-challenges-link').hide();
        });

        {% endif %}

    });

    function admin_update_peak_elevation() {
        var peak_id = $('#peak-elevation-id').val();
        var peak_elevation = $('#peak-elevation').val();
        var peak_elevation_units = $('input[name=peak-elevation-units]:checked').val();
        $.post('{% url "admin_update_peak_elevation" %}', {id:peak_id, elevation:peak_elevation, units:peak_elevation_units},
                function(data) {
                    $('#save-peak-elevation').prop('disabled', false);
                    $('#save-peak-elevation').html('Save');
                    var output = $("#edit-elevation-message");
                    output.html(data);
                    output.show();
                    $('#edit-elevation').modal('hide');
                    //update UI with new elevation
                    if (peak_elevation_units != 'ft') {
                        var elevation_meters = peak_elevation;
                        peak_elevation = Math.round(peak_elevation / .3048);
                    } else {
                        var elevation_feet = parseFloat(peak_elevation);
                        var elevation_meters = Math.round(elevation_feet * .3048);
                    }
                    $('.admin-edit-elevation').data('elevation', peak_elevation);
                    var elevation_formatted = numberWithCommas(peak_elevation) + ' ft / ' + numberWithCommas(elevation_meters) + ' m';
                    $('.peak-elevation-formatted').html(elevation_formatted);
                }
        );
    }

    function setElevationMeters() {
        if ($('#peak-elevation').val() != '') {
            var elevation_feet = parseFloat($('#peak-elevation').val());
            var elevation_meters = Math.round(elevation_feet * .3048);
            $('#peak-elevation').val(elevation_meters);
        }
        $('#peak-elevation').attr('placeholder','in meters...');
    }

    function setElevationFeet() {
        if ($('#peak-elevation').val() != '') {
            var elevation_meters = parseFloat($('#peak-elevation').val());
            var elevation_feet = Math.round(elevation_meters / .3048);
            $('#peak-elevation').val(elevation_feet);
        }
        $('#peak-elevation').attr('placeholder','in feet...');
    }

    function admin_update_peak_prominence() {
        var peak_id = $('#peak-prominence-id').val();
        var peak_prominence = $('#peak-prominence').val();
        var peak_prominence_units = $('input[name=peak-prominence-units]:checked').val();
        $.post('{% url "admin_update_peak_prominence" %}', {id:peak_id, prominence:peak_prominence, units:peak_prominence_units},
                function(data) {
                    $('#save-peak-prominence').prop('disabled', false);
                    $('#save-peak-prominence').html('Save');
                    var output = $("#edit-prominence-message");
                    output.html(data);
                    output.show();
                    $('#edit-prominence').modal('hide');
                    //update UI with new elevation
                    if (peak_prominence != '') {
                        if (peak_prominence_units != 'ft') {
                            var prominence_meters = peak_prominence;
                            peak_prominence = Math.round(peak_prominence / .3048);
                        } else {
                            var prominence_feet = parseFloat(peak_prominence);
                            var prominence_meters = Math.round(prominence_feet * .3048);
                        }
                        $('.admin-edit-prominence').data('prominence', peak_prominence);
                        var prominence_formatted = numberWithCommas(peak_prominence) + ' ft / ' + numberWithCommas(prominence_meters) + ' m';
                        $('.peak-prominence-formatted').html(prominence_formatted);
                        $('.peak-prominence-missing').hide();
                        $('.peak-prominence-data').show();
                    } else {
                        $('.admin-edit-prominence').data('prominence', '');
                        $('.peak-prominence-formatted').html('');
                        $('.peak-prominence-data').hide();
                        $('.peak-prominence-missing').show();
                    }
                }
        );
    }

    function setProminenceMeters() {
        if ($('#peak-prominence').val() != '') {
            var prominence_feet = parseFloat($('#peak-prominence').val());
            var prominence_meters = Math.round(prominence_feet * .3048);
            $('#peak-prominence').val(prominence_meters);
        }
        $('#peak-prominence').attr('placeholder','in meters...');
    }

    function setProminenceFeet() {
        if ($('#peak-prominence').val() != '') {
            var prominence_meters = parseFloat($('#peak-prominence').val());
            var prominence_feet = Math.round(prominence_meters / .3048);
            $('#peak-prominence').val(prominence_feet);
        }
        $('#peak-prominence').attr('placeholder', 'in feet...');
    }

    function admin_update_peak_range() {
        var peak_id = $('#peak-range-id').val();
        var peak_range = $('#peak-range').val();
        $.post('{% url "admin_update_peak_range" %}', {id:peak_id, range:peak_range},
                function(data) {
                    $('#save-peak-range').prop('disabled', false);
                    $('#save-peak-range').html('Save');
                    var output = $("#edit-range-message");
                    output.html(data);
                    output.show();
                    $('#edit-range').modal('hide');
                    $('.peak-range-div').data('url', '/peaks/#range=' + encodeURIComponent(peak_range));
                    $('.peak-range-formatted').html(peak_range);
                    $('.peak-range-formatted').attr('href', '/peaks/#range=' + encodeURIComponent(peak_range));
                    $('.admin-edit-range').data('range', peak_range);
                    if (peak_range != '') {
                        $('.peak-range-missing').hide();
                        $('.peak-range-data').show();
                    } else {
                        $('.peak-range-data').hide();
                        $('.peak-range-missing').show();
                    }
                }
        );
    }

    function openUrl(url) {
        window.location.href = url;
    }

    function loadPhotos(page) {

        if (page == 1) {
            $('#ajax-data-loading').css('display', 'inline');
            $('#photos-list').empty();
            $('#more-photos-list').empty();
        } else {
            $('#more-ajax-data-loading').css('display', 'inline');
        }
        var photoCount = '{{ peak_photos_count }}'.replace(',', '');
        var totalPages = Math.floor(parseInt(photoCount)/12);
        var photoIndex = 1;
        $.getJSON('{% url "peak_photos_list" %}?peak_id={{ peak.id }}&page='+page , function(data) {

            $.each( data, function( key, val ) {
                if (key=='photos') {
                    $.each( val, function( photokey, photoval ) {
                        photoCaptionClass = 'user-photo-info peak-photo-with-caption';

                        //build photos
                        noPhotoSlideshowClass = 'peakimg-responsive';
                        divClass = 'col-lg-3 col-md-3 col-sm-4 col-xs-6';
                        imgHeight = '240';
                        photoDivClass = 'top-photos';

                        borderClass = 'photo-grid-' + (photoIndex).toString();

                        slideshowclass = '';
                        hoverclass = 'hover-photos';
                        photoHtml = '<img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive">';

                        var photoCaption = photoval.caption;
                        if (photoCaption == 'None') {
                            photoCaption = '';
                        }

                        var adminMode = readCookie('admin-mode');
                        var removePhotoDivStyle = 'display: none;';
                        if (adminMode == 'true') {
                            removePhotoDivStyle = '';
                        }

                        if (page == 1) {
                            $('#photos-list').append('<a id="gallery-photo-' + photoval.photo_id + '" class="gallery-link" data-user="' + photoval.username + '" data-credit="" data-description="' + photoCaption + '" data-gallery href="{{ MEDIA_URL }}' + photoval.fullsize_url + '"><div class="' + divClass + ' ' + borderClass + '" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}' + photoval.thumbnail_url + '\'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;"><div class="' + photoDivClass + '"><div class="' + hoverclass + '"><div>' + photoHtml + '</div></div><div class="' + photoCaptionClass + '" style="display: none;"><span class="data photo-caption" style="position: absolute; bottom: -5px; left: 10px; color: #fff;"><p class="bagger" style="white-space: normal; font-size: 10px;">' + photoCaption + '</p><p class="bagger" style="font-size: 10px;">' + photoval.username + '&nbsp;&bull;&nbsp;<time class="timeago" datetime="' + photoval.created + 'T00:00:00">' + photoval.created + '</time></p></span></div><div style="' + removePhotoDivStyle + '" data-photoid="' + photoval.photo_id + '" class="admin-delete-photo remove-photo desktop-admin-link hidden-xs hidden-sm hidden-md"><i class="fa fa-times"></i></div></div></div></a>');
                        } else {
                            $('#more-photos-list').append('<a id="gallery-photo-' + photoval.photo_id + '" class="gallery-link" data-user="' + photoval.username + '" data-credit="" data-description="' + photoCaption + '" data-gallery href="{{ MEDIA_URL }}' + photoval.fullsize_url + '"><div class="' + divClass + ' ' + borderClass + '" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}' + photoval.thumbnail_url + '\'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;"><div class="' + photoDivClass + '"><div class="' + hoverclass + '"><div>' + photoHtml + '</div></div><div class="' + photoCaptionClass + '" style="display: none;"><span class="data photo-caption" style="position: absolute; bottom: -5px; left: 10px; color: #fff;"><p class="bagger" style="white-space: normal; font-size: 10px;">' + photoCaption + '</p><p class="bagger" style="font-size: 10px;">' + photoval.username + '&nbsp;&bull;&nbsp;<time class="timeago" datetime="' + photoval.created + 'T00:00:00">' + photoval.created + '</time></p></span></div><div style="' + removePhotoDivStyle + '" data-photoid="' + photoval.photo_id + '" class="admin-delete-photo remove-photo desktop-admin-link hidden-xs hidden-sm hidden-md"><i class="fa fa-times"></i></div></div></div></a>');
                        }
                        photoIndex++;
                        photos_displayed++;
                        photos.push('{{ MEDIA_URL }}' + photoval.fullsize_url);

                    });
                    $("time.timeago").timeago();
                }
            });

            //did we get at least 12 photos?
            if (photos.length < 12) {
                //var toIndex = 12 - photos.length;
                //return up to 50 photos to make sure we get enough with large thumbnails
                var toIndex = 50;
                var minLat = {{ peak.lat }} - .01;
                var maxLat = {{ peak.lat }} + .01;
                var minLng = {{ peak.long }} - .01;
                var maxLng = {{ peak.long }} + .01;
                //fill the grid with photos from Flickr
                $.getJSON('https://api.flickr.com/services/rest/?method=flickr.photos.search&api_key=********************************&license=1,2,3,4,5,6,7,8,9,10&has_geo=1&lat={{ peak.lat }}&lon={{ peak.long }}&radius=.2&radius_units=mi&extras=date_taken%2Cowner_name%2Curl_l&per_page='+toIndex+'&page=1&format=json&jsoncallback=?' , function(data) {
                    $.each( data, function( key, val ) {
                        if (key=='photos') {
                            $.each( val, function( key, val ) {
                                if (key=='photo') {
                                    $.each( val, function( photokey, photoval ) {
                                        if (typeof photoval.url_l != 'undefined' && photos_displayed < 12) {
                                            photoCaptionClass = 'user-photo-info peak-photo-with-caption';
                                            noPhotoSlideshowClass = 'peakimg-responsive';
                                            divClass = 'col-lg-3 col-md-3 col-sm-4 col-xs-6';
                                            imgHeight = '240';
                                            photoDivClass = 'top-photos';
                                            borderClass = 'photo-grid-' + (photoIndex).toString();
                                            slideshowclass = '';
                                            hoverclass = 'hover-photos';
                                            photoHtml = '<img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive">';
                                            var photoDate = new Date(photoval.datetaken.replace(/-/g, "/"));
                                            $('#photos-list').append('<a class="gallery-link" data-photo-url="https://www.flickr.com/photos/' + photoval.owner + '/' + photoval.id + '" data-user="' + photoval.ownername + '" data-credit="via Flickr" data-description="' + photoval.title + '" data-gallery href="' + photoval.url_l + '"><div class="' + divClass + ' ' + borderClass + '" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + photoval.url_l + '\'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;"><div class="' + photoDivClass + '"><div class="' + hoverclass + '"><div>' + photoHtml + '</div></div><div class="' + photoCaptionClass + '" style="display: none;"><span class="data photo-caption" style="position: absolute; bottom: -5px; left: 10px; color: #fff;"><p class="bagger" style="white-space: normal; font-size: 10px;">' + photoval.title + '</p><p class="bagger" style="font-size: 10px;">' + photoval.ownername + '&nbsp;&bull;&nbsp;<time class="timeago" datetime="' + $.datepicker.formatDate('yy-mm-dd', photoDate) + 'T00:00:00">' + $.datepicker.formatDate('yy-mm-dd', photoDate) + '</time></p></span></div></div></div></a>');
                                            photoIndex++;
                                            photos_displayed++;
                                            photos.push(photoval.url_l);
                                        }
                                    });
                                }
                            });
                            $("time.timeago").timeago();
                        }
                    });
                });
            }

            if (page == 1) {
                $('#ajax-data-loading').css('display', 'none');
            } else {
                $('#more-ajax-data-loading').css('display', 'none');
            }


            //show the photos footer
            if (photos_displayed < photoCount) {
                $('#see-more-photos-div').show();
                $('#photos-footer').show();
            } else {
                $('#see-more-photos-div').hide();
                $('#photos-footer').hide();
            }

            photos_page++;
        });

    }

    var timers = {};
    var mapExpanded = false;

    function round(value, decimals) {
      return Number(Math.round(value+'e'+decimals)+'e-'+decimals);
    }

    function getRepString (rep) {
      rep = rep+''; // coerce to string
      if (rep < 1000) {
        return rep; // return the same number
      }
      // divide and format
      return (rep/1000).toFixed(rep % 1000 != 0)+'K';
    }

    function updateURLParameter(url, param, paramVal){

        var newAdditionalURL = "";
        var tempArray = url.split("#");
        var baseURL = tempArray[0];
        var additionalURL = tempArray[1];
        var temp = "";

        if (additionalURL) {
            tempArray = additionalURL.split("&");
            for (i=0; i<tempArray.length; i++){
                if(tempArray[i].split('=')[0] != param){
                    newAdditionalURL += temp + tempArray[i];
                    temp = "&";
                }
            }
        }

        var rows_txt = temp + "" + param + "=" + paramVal;
        return newAdditionalURL + rows_txt;
    }

    function numberWithCommas(x) {
        return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    function setMapControls() {
        var check = checkIfMapboxStyleIsLoaded();
        if (!check) {
            // It's not safe to manipulate layers yet, so wait 200ms and then check again
            setTimeout(function() {
                setMapControls();
            }, 200);
            return;
        }
        // Whew, now it's safe to manipulate layers!
        var mapUnits = readCookie('map_units');
        if (mapUnits == 'meters') {
            toggleMapUnits(mapUnits);
        }
    }

    function addExtraMapLayers() {
        var check = checkIfMapboxStyleIsLoaded();
        if (!check) {
            // It's not safe to manipulate layers yet, so wait 200ms and then check again
            setTimeout(function() {
                addExtraMapLayers();
            }, 200);
            return;
        }
        // Whew, now it's safe to manipulate layers!
        // No extra map layers necessary
    }

    // Function to update background size of thumbnail based on div dimensions
    function updateMapCanvasSize() {
        // Execute the update after a small delay because the height and width dont always get updated right away.
        setTimeout(function() {
            // Update Background image size
            const div = document.getElementById('peak-map-col');
            const minWidth = 1260;
            const minHeight = 945;

            // Get the current dimensions of the div
            const divWidth = div.offsetWidth;
            const divHeight = div.offsetHeight;

            // Apply background size based on the div dimensions
            if (divWidth >= minWidth && divHeight >= minHeight) {
                // Use "cover" if div is larger than the minimum threshold
                div.style.backgroundSize = 'cover';
            } else {
                // Set to the minimum size otherwise
                div.style.backgroundSize = `${minWidth}px ${minHeight}px`;
            }

            // Center orange peak marker
            var centerMarkerTop = ($('#map-canvas').height() / 2) - 10;
            var centerMarkerLeft = ($('#map-canvas').width() / 2) - 10;
            $('#center-peak-marker').css({'top':centerMarkerTop});
            $('#center-peak-marker').css({'left':centerMarkerLeft});
        }, 100);
    }

    window.addEventListener('resize', updateMapCanvasSize);

    $(document).ready(function() {

        updateMapCanvasSize();

        $('#map-canvas').mousemove(function(e) {
            var offset = $(this).offset();
            pageX = e.pageX;
            pageY = e.pageY;
            mapX = (e.pageX - offset.left);
            mapY = (e.pageY - offset.top);
        });

        $('#map-canvas').on('touchstart', function(e) {
            var offset = $(this).offset();
            pageX = e.originalEvent.touches[0].pageX;
            pageY = e.originalEvent.touches[0].pageY;
            mapX = (pageX - offset.left);
            mapY = (pageY - offset.top);
        });


        {% if user.is_staff %}

        $('#remove-main-peak-photo').on('click', function(event){
            event.preventDefault();
            var peakName = '{{ peak.name }}';
            if(confirm('Are you sure you want to delete the main photo for '+peakName+'?')){
                var peak_id = '{{ peak.id }}';
                $.post('{% url "peak_delete_main_photo" %}', {peak_id: peak_id}, function(data){
                    if(data.success){
                        location.reload(true);
                    } else {
                        alert('An error ocurred when trying to delete this photo, please try again later.');
                    }
                });
            }
        });

        {% endif %}

        $('#blueimp-gallery').on('open', function (event) {
            $('body,html').css('overflow','visible');
        });

        $("#blueimp-gallery").on('slide', function (event, index, slide) {
            var gallery = $('#blueimp-gallery').data('gallery');
            var caption = gallery.list[index].getAttribute('data-description'),
                username = gallery.list[index].getAttribute('data-user'),
                credit = gallery.list[index].getAttribute('data-credit'),
                photo_url = gallery.list[index].getAttribute('data-photo-url'),
                caption_node = gallery.container.find('.description-text-caption');
                username_node = gallery.container.find('.description-text-user');
            caption_node.empty();
            username_node.empty();
            if (caption) {
                caption_node[0].appendChild(document.createTextNode(caption));
            }
            if (username) {
                var newdiv = document.createElement('div');
                if (credit) {
                    newdiv.innerHTML = '<a style="color: #fff;" target="_blank" href="' + photo_url + '/"><i style="margin-right: 5px;" class="fa fa-camera" aria-hidden="true"></i> ' + username + ' ' + credit + '</a>';
                } else {
                    newdiv.innerHTML = '<a style="color: #fff;" href="/members/' + username + '/"><i style="margin-right: 5px;" class="fa fa-camera" aria-hidden="true"></i> ' + username;
                }
                username_node[0].appendChild(newdiv);
            }

        });

        var window_width = $(window).width();

        $('#slideshow1').cycle({
            fx: 'fade' // choose your transition type, ex: fade, scrollUp, shuffle, etc...
        });

        //highlights stuff
        {% if user.is_authenticated %}
        $('#edit-highlights-link-div').on('click', 'a', function() {
            $('#highlights-header').hide();
            $('#highlights-content').hide();
            $('#edit-highlights-header').fadeIn(300);
            $('#edit-highlights-form').fadeIn(300, function () {
                autosize($('.peak-highlight-input'));
                autosize.update($('.peak-highlight-input'));
            });
            return false;
        });
        {% else %}
        $('#edit-highlights-link-div').on('click', 'a', function() {
            if ($('#navbar-login-link').is(':visible')) {
                $('#navbar-login-link').click();
            } else {
                //window.location.href = '/accounts/login/';
            }
            return false;
        });
        {% endif %}

        $('#edit-highlights-cancel').click(function(){
            $('#peak-highlights-fieldset').empty();
            var new_index = 1;
            var haveHighlights = false;
            for (var i = 0; i < initial_highlights.length; i++) {
                $('#peak-highlights-fieldset').append('<div><textarea class="peak-highlight-input" name="peak-highlight-' + new_index + '"  data-index="' + new_index + '" id="peak-highlight-' + new_index + '" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ peak.name }}...">'+initial_highlights[i]+'</textarea></div>');
                haveHighlights = true;
                new_index++;
            }
            $('#peak-highlights-fieldset').append('<div><textarea class="peak-highlight-input" name="peak-highlight-' + new_index + '" data-index="' + new_index + '" id="peak-highlight-new" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ peak.name }}..."></textarea></div>');
            $('#edit-highlights-header').hide();
            $('#edit-highlights-form').hide();
            $('#highlights-header').fadeIn(300);
            if (haveHighlights) {
                $('#highlights-content').fadeIn(300);
                $('#edit-highlights-link').html('edit highlights');
            } else {
                $('#highlights-content').fadeOut(300);
                $('#edit-highlights-link').html('add a highlight!');
            }
            return false;
        });

        $('#peak-highlights-fieldset').on('keyup', 'textarea', function() {
            var index = $(this).data('index');
            var num_fields = $('.peak-highlight-input').length;
            if (index == num_fields && $(this).val().length > 0) {
                var new_index = index + 1;
                if ($('#peak-highlight-' + new_index).length == 0) {
                    $('#peak-highlights-fieldset').append('<div><textarea class="peak-highlight-input" name="peak-highlight-' + new_index + '"  data-index="' + new_index + '" id="peak-highlight-' + new_index + '" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ peak.name }}..."></textarea></div>');
                }
            }
            autosize($('.peak-highlight-input'));
            autosize.update($('.peak-highlight-input'));
            return false;
        });

        // process the form
        $('#edit-highlights-save').click(function(event) {

            var url = "/peaks/edit_highlights/{{ peak.id }}/";
            $('#edit-highlights-save').html('<i class="fa fa-spinner fa-spin fa-fw"></i>');
            $('#edit-highlights-save').prop("disabled",true);

            $.ajax({
                type: "POST",
                url: url,
                data: $("#edithighlights_form").serialize(),
                success: function(data)
                {
                    //console.log(data);
                    //update page with new highlights
                    $('#peak-highlights-fieldset').empty();
                    $('#highlights-list').empty();
                    var haveHighlights = false;
                    $.each( data, function( key, val ) {
                        if (key=='highlights') {
                            new_index = 1;
                            initial_highlights = [];
                            $.each( val, function( highlightkey, highlightval ) {
                                //console.log(highlightval);
                                haveHighlights = true;
                                $('#peak-highlights-fieldset').append('<div><textarea class="peak-highlight-input" name="peak-highlight-' + new_index + '"  data-index="' + new_index + '" id="peak-highlight-' + new_index + '" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ peak.name }}...">'+highlightval+'</textarea></div>');
                                $('#highlights-list').append('<li style="list-style: initial; margin-bottom: 20px;">'+highlightval+'</li>');
                                initial_highlights.push(highlightval);
                                new_index++;
                            });
                        }
                    });
                    $('#peak-highlights-fieldset').append('<div><textarea class="peak-highlight-input" name="peak-highlight-' + new_index + '" data-index="' + new_index + '" id="peak-highlight-new" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ peak.name }}..."></textarea></div>');
                    $('#edit-highlights-link-div').html('<a id="edit-highlights-link" style="cursor: pointer; color: #00B1F2; font-weight: 500;">edit highlights</a>');
                    $('#edit-highlights-save').html('Save highlights');
                    $('#edit-highlights-save').prop("disabled",false);
                    $('#edit-highlights-header').hide();
                    $('#edit-highlights-form').hide();
                    $('#highlights-header').fadeIn(300);
                    if (haveHighlights) {
                        $('#highlights-content').fadeIn(300);
                        $('#edit-highlights-link').html('edit highlights');
                    } else {
                        $('#highlights-content').fadeOut(300);
                        $('#edit-highlights-link').html('add a highlight!');
                    }
                }
            });
            event.preventDefault();
        });

        $('#see-more-photos').on('click', function(){
            loadPhotos(photos_page);
        });

        $('#peak-search').on('hide.bs.modal', function (e) {
          $('#peak-search').removeClass('modal fade right');
        });

        $('#peak-search').on('hidden.bs.modal', function (e) {
          $('#peak-search').attr('style','left: 0px; right: auto; width: 240px; position: absolute; display: inline-block;');
          $('#peak-search').addClass('hidden-xs');
          $('#peak-search').addClass('hidden-sm');
          $('#mobile-collapse-nav').hide();
        });

        //Load some photos
        loadPhotos(photos_page);

        //Add photo stuff
        $('.add-photo').click(function() {
            var window_width = $(window).width();
            if (window_width < 768) {
                //$('#add-photo-file').trigger('click');
                $('.qq-upload-button-selector:eq(1)').find('input').trigger('click');
            } else {
                $('#add-photo').modal('show');
            }
            return false;
        });

        $('#add-photo-close-success').click(function() {
            $('#add-photo').modal('hide');
            return false;
        });

        $('#add-photo-close-error').click(function() {
            $('#add-photo').modal('hide');
            return false;
        });

        $('#add-photo-button').click(function() {
            $('#add-photo-file').trigger('click');
            return false;
        });

        $('#add-photo').on('shown.bs.modal', function (e) {
            $('.loading').hide();
            var window_width = $(window).width();
            if (window_width >= 768) {
                $('#add-photo-form-container').show();
                $('#add-photo-success-container').hide();
                $('#add-photo-error-message-container').hide();
                $('#add-photo-error-message').html('');
            }
            $('.qq-upload-button-text').html('Add jpg peak photo');
            $('.qq-upload-button-selector').find('input').prop('disabled', false);
            $('.qq-upload-list-selector').hide();
            $('.qq-upload-button-selector').show();
        });

        {% if user.is_superuser %}
        //remove photo stuff
        $('#photos-list').on('click', '.remove-photo', function(e) {
            var photo_id = $(this).data('photoid');
            $('#confirm-modal-label').html('Delete this photo?');
            $('#confirm-modal-body').html('<div style="margin-top: 20px; text-align: center;"><a data-photoid="' + photo_id + '" class="btn btn-primary confirm-remove-photo" style="width: 100px;">Delete</a><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">Cancel</button></div>');
            $('#confirm-modal').modal('show');
            $('.modal-backdrop').hide();
            return false;
        });

        $('#more-photos-list').on('click', '.remove-photo', function(e) {
            var photo_id = $(this).data('photoid');
            $('#confirm-modal-label').html('Delete this photo?');
            $('#confirm-modal-body').html('<div style="margin-top: 20px; text-align: center;"><a data-photoid="' + photo_id + '" class="btn btn-primary confirm-remove-photo" style="width: 100px;">Delete</a><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">Cancel</button></div>');
            $('#confirm-modal').modal('show');
            $('.modal-backdrop').hide();
            return false;
        });

        $('body').on('click', '.confirm-remove-photo', function(e) {
            $('#confirm-modal').modal('hide');
            $('#photos-list').empty();
            var photo_id = $(this).data('photoid');
            $.post('{% url "summit_delete_photo" %}', {photo_id: photo_id}, function(data) {
                if (data.success){
                    photos_displayed = 0;
                    photos_page = 1;
                    photos = [];
                    loadPhotos(1);
                } else {
                    alert('An error occurred deleting this photo, please try again later');
                }
            });
        });
        {% endif %}


        $(window).resize(function() {
            var window_width = $(window).width();
            //hide or show add photo modal?
            if (window_width < 768) {
                if ($('#mobile-add-photo-error-message').html() == '') {
                    $('#add-photo').modal('hide');
                }
            } else {
                if (!uploaderIdle) {
                    $('.qq-upload-button-text').html('<i class="fa fa-spinner fa-spin"></i>');
                    $('.qq-upload-button-selector').find('input').prop('disabled', true);
                    $('.qq-upload-button-text').blur();
                    $('#add-photo').modal('show');
                }
            }
            $("div#map-canvas").height($('#peak-photo-col').height());
            $("div#map-canvas").width($('#peak-photo-col').width());
            // map.resize();
        });

        var uploader1 = new qq.s3.FineUploader({
            debug: false,
            multiple: false,
            element: document.getElementById('peak-photo-upload'),
            template: 'qq-image-template',
            request: {
                endpoint: 'https://peakery-media.s3.amazonaws.com',
                accessKey: 'AKIAJPWBM4YZXFHWBTPQ'
            },
            signature: {
                endpoint: '{% url "s3signature" %}'
            },
            uploadSuccess: {
                endpoint: '{% url "s3_peak_photo_upload" %}',
                params: {
                    'peak_id': '{{ peak.id }}'
                }
            },
            iframeSupport: {
                localBlankPagePath: '/api/s3blank/'
            },
            retry: {
               enableAuto: false // defaults to false
            },
            validation: {
                acceptFiles: ['image/jpg, image/jpeg, .jpg, .jpeg'],
                allowedExtensions: ['jpg', 'jpeg'],
                sizeLimit: 10000000,
                image: {
                    minHeight: 1440,
                    minWidth: 1920
                }
            },
            messages: {
                typeError: 'Sorry, must be a JPG file.',
                sizeError: 'Sorry, this file is too big. Must be under 10 MB.',
                minHeightImageError: 'Sorry, this photo is too small. Must be at least 1920x1440 pixels.',
                minWidthImageError: 'Sorry, this photo is too small. Must be at least 1920x1440 pixels.'
            },
            showMessage: function (message) {
                var window_width = $(window).width();
                $('#add-photo-error-message').html(message);
                $('#mobile-add-photo-error-message').html(message);
                $('#add-photo-error-message-container').show();
                $('#mobile-add-photo-error-message-container').show();
                $('#mobile-add-photo-error-close-container').show();
                if (window_width < 768) {
                    $('#add-photo').modal('show');
                }
            },
            text: {
                fileInputTitle: 'Choose file(s)'
            },
            callbacks: {
                onSubmit: function(id, name) {
                    uploaderIdle = false;
                    $('.loading').show();
                    $('#add-photo-error-message').html('');
                    $('#mobile-add-photo-error-message').html('');
                    $('#add-photo-error-message-container').hide();
                    $('#mobile-add-photo-error-message-container').hide();
                    $('#mobile-add-photo-error-close-container').hide();
                },
                onSubmitted: function(id, name) {
                    $('.qq-upload-button-text').html('<i class="fa fa-spinner fa-spin"></i>');
                    $('.qq-upload-button-selector').find('input').prop('disabled', true);
                    $('.qq-upload-button-text').blur();
                },
                onComplete: function(id, name, responseJSON, maybeXhr) {
                    uploaderIdle = true;
                    var window_width = $(window).width();
                    $('.loading').hide();
                    $('#add-photo-form-container').fadeOut(200);
                    if (window_width < 768) {
                        $('#add-photo').modal('show');
                    }
                    $('#add-photo-success-photo').html('<img src="{{ MEDIA_URL }}'+responseJSON.photo_url+'" style="width: 250px;">');
                    $('#add-photo-success-container').fadeIn(200);
                },
                onAllComplete: function(successful, failed) {

                },
                onCancel: function(id, name) {},
                onUpload: function(id, name) {},
                onUploadChunk: function(id, name, chunkData) {},
                onUploadChunkSuccess: function(id, chunkData, responseJSON, xhr) {},
                onResume: function(id, fileName, chunkData) {},
                onProgress: function(id, name, loaded, total) {},
                onTotalProgress: function(loaded, total) {},
                onError: function(id, name, reason, maybeXhrOrXdr) {
                    uploaderIdle = true;
                    $('.loading').hide();
                    var window_width = $(window).width();
                    $('#add-photo-error-message').html('Error uploading ' + name + '. Hit the Retry button on the photo to try again.');
                    $('#mobile-add-photo-error-message').html('Error uploading ' + name + '. Hit the Retry button on the photo to try again.');
                    $('#add-photo-error-message-container').show();
                    $('#mobile-add-photo-error-message-container').show();
                    $('#mobile-add-photo-error-close-container').show();
                    if (window_width < 768) {
                        $('#add-photo').modal('show');
                    }
                },
                onAutoRetry: function(id, name, attemptNumber) {},
                onManualRetry: function(id, name) {},
                onValidateBatch: function(fileOrBlobData) {},
                onValidate: function(fileOrBlobData) {},
                onSubmitDelete: function(id) {},
                onDelete: function(id) {},
                onDeleteComplete: function(id, xhrOrXdr, isError) {},
                onPasteReceived: function(blob) {},
                onStatusChange: function(id, oldStatus, newStatus) {},
                onSessionRequestComplete: function(response, success, xhrOrXdr) {}
            },
            objectProperties: {
                acl: 'public-read',
                key: function (fileId) {

                    var filename = uploader1.getName(fileId);
                    var uuid = uploader1.getUuid(fileId);
                    var ext = filename.substr(filename.lastIndexOf('.') + 1).toLowerCase();

                    return  'items/main/{{ peak.slug_new_text }}_' + uuid + '.' + ext;

                }
            }
        });

        $('#add-photo-file-xxx').change(function() {
            var window_width = $(window).width();
            if (window_width < 768) {
                $('.loading').show();
                $('#add-photo-error-message').html('');
            } else {
                $('#add-photo-button').html('<i class="fa fa-spinner fa-spin"></i>');
                $('#add-photo-button').blur();
                $('#add-photo-error-message').html('');
            }
            //check file type
            var val = $(this).val().toLowerCase();
            var regex = new RegExp("(.*?)\.(jpg|jpeg)$");
            if(!(regex.test(val))) {
                $('#add-photo-error-message').html('Invalid file type. Please upload JPG files only.');
                $('#add-photo-button').html('Add peak photo');
                if (window_width < 768) {
                    $('#add-photo').modal('show');
                    $('#add-photo-error-container').show();
                }
                return false;
            }

            //check image dimensions
            var _URL = window.URL || window.webkitURL;
            var file, img;
            var validImage = true;
            if ((file = this.files[0])) {
                img = new Image();
                img.onload = function() {
                    int_width = parseInt(this.width);
                    int_height = parseInt(this.height);
                    if (int_width > int_height) {
                        if (int_width < 1920) {
                            validImage = false;
                        } else if (int_height < 1440) {
                            validImage = false;
                        }
                    } else {
                        if (int_height < 1920) {
                            validImage = false;
                        } else if (int_width < 1440) {
                            validImage = false;
                        }
                    }
                    if (!validImage) {
                        $('#add-photo-error-message').html('Photo is too small: ' + this.width + 'x' + this.height);
                        $('#add-photo-button').html('Add peak photo');
                        if (window_width < 768) {
                            $('#add-photo').modal('show');
                            $('#add-photo-error-container').show();
                        }
                    } else {
                        var data = new FormData();
                        $.each($('#add-photo-file')[0].files, function(i, file) {
                            data.append('file-'+i, file);
                        });
                        $.ajax({
                            url: '/peaks/{{ peak.id }}/upload-photo/',
                            data: data,
                            dataType: 'json',
                            cache: false,
                            contentType: false,
                            processData: false,
                            type: 'POST',
                            success: function(data){
                                if (data.success == true) {
                                    $('#add-photo-button').html('Add peak photo');
                                    $('#add-photo-form-container').fadeOut(200);
                                    if (window_width < 768) {
                                        $('#add-photo').modal('show');
                                    }
                                    $('#add-photo-success-photo').html('<img src="{{ MEDIA_URL }}'+data.photo_url+'" style="width: 250px;">');
                                    $('#add-photo-success-container').fadeIn(200);
                                    $('.loading').hide();
                                    //$('#photos-list').empty();
                                    //photos_page = 1;
                                    //loadPhotos(photos_page);
                                } else {
                                    $('#add-photo-button').html('Add peak photo');
                                    $('#add-photo-form-container').fadeOut(200);
                                    $('#add-photo-form-message').html(data.error_message);
                                    if (window_width < 768) {
                                        $('#add-photo').modal('show');
                                    }
                                    $('#add-photo-error-container').fadeIn(200);
                                    $('.loading').hide();
                                }
                            },
                            error: function(data){
                                $('#add-photo-button').html('Add peak photo');
                                $('#add-photo-form-container').fadeOut(200);
                                if (window_width < 768) {
                                    $('#add-photo').modal('show');
                                }
                                $('#add-photo-form-message').html('Error saving image. Please try again.');
                                $('#add-photo-error-container').fadeIn(200);
                                $('.loading').hide();
                            }
                        });
                    }
                };
                img.onerror = function() {
                    $('#add-photo-button').html('Add peak photo');
                    $('#add-photo-error-message').html('Invalid file: ' + file.type);
                    if (window_width < 768) {
                        $('#add-photo').modal('show');
                        $('#add-photo-error-container').show();
                    }
                };
                img.src = _URL.createObjectURL(file);
            }
        });

        $('#add-photo-close-error').click(function() {
            $('#add-photo').modal('hide');
            return false;
        });

        $('#add-photo-close-success-xxx').click(function() {
            $('#add-photo').modal('hide');
            $('#photos-list').empty();
            photos_page = 1;
            loadPhotos(photos_page);
            return false;
        });

    });

</script>

{% endblock %}

{% block gallery %}
<div id="blueimp-gallery" class="blueimp-gallery blueimp-gallery-controls" data-use-bootstrap-modal="false" data-slideshow-interval="4000">
    <!-- The container for the modal slides -->
    <div class="slides"></div>
    <!-- Controls for the borderless lightbox -->
    <h3 class="title"></h3>
    <div class="description">
        <div class="description-text">
            <div class="description-text-caption"></div>
            <div class="description-text-user"></div>
        </div>
    </div>
    <a class="prev">‹</a>
    <a class="next">›</a>
    <a class="close">×</a>
    <a class="play-pause"></a>
    <ol class="indicator"></ol>
    <!-- The modal dialog, which will be used to wrap the lightbox content -->
    <div class="modal fade">
        <div class="modal-dialog" style="width: 80%;">
            <div class="modal-content">
                <div class="modal-header">
                    <button style="color: #fff;" type="button" class="close" aria-hidden="true">&times;</button>
                    <h4 class="modal-title"></h4>
                </div>
                <div class="modal-body next"></div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default pull-left prev">
                        <i class="glyphicon glyphicon-chevron-left"></i>
                        Previous
                    </button>
                    <button type="button" class="btn btn-primary next">
                        Next
                        <i class="glyphicon glyphicon-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="hidden-sm hidden-md hidden-lg"><div class="loading" style="display: none;">Loading&#8230;</div></div>
{% endblock %}