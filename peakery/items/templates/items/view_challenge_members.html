{% extends "base.html" %}
{% load static %}
{% load humanize %}
{% load directory_tags %}
{% block title %}{{ group.name }} members{% endblock %}
{% block titlemeta_overwrite %}{{ group.name }} members{% endblock %}
{% block description %}Member progress in {{ group.name }}.{% endblock %}

{% block directory_active %}active{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row hidden-xs">
        <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 65px; border-radius: 12px 12px 0 0;"><span style="font-size: 24px; font-weight: 600;">
            <div id="breadcrumbs">
                <ul>
                    <li><h1 class="regions-title">{{ group.name }}</h1></li>
                </ul>
                <div class="pull-right hidden-xs" style="margin-top: -5px;">
                    <a class="region-header-sub-links" href="/challenges/{{ group.slug }}/">Info</a><a class="region-header-sub-links" href="/challenges/{{ group.slug }}/map/">Map</a><a class="region-header-sub-links" href="/challenges/{{ group.slug }}/peaks/">Peaks</a><a class="region-header-sub-links" href="/challenges/{{ group.slug }}/summits/">Climbs</a><a style="{{ subnav_members_style }}" class="region-header-sub-links" href="/challenges/{{ group.slug }}/members/">Members</a>
                </div>
            </div>
        </span></div>
    </div>
{% endblock %}

{% block content %}

<style>
   @media screen and (max-width: 767px) and (min-width: 1px) {
       #content-body {
           margin-top: 20px;
       }
       .content-pane {
           margin-top: 99px;
       }
       .mobile-regions-subnav-fixed {
           margin-top: 50px;
       }
       #selectMobileMemberfilter {
           margin-top: -6px;
       }
       .member-divider {
            height: 50px;
        }
       .filter-bar {
           top: 120px;
       }
   }
   @media screen and (max-width: 1023px) and (min-width: 768px) {
       .content-pane {
           margin-top: 49px;
       }
       #selectMobileMemberfilter {
           margin-top: -2px;
       }
       .member-divider {
            height: 50px;
        }
       .mobile-regions-subnav-fixed {
           margin-top: 71px;
       }
       .filter-bar {
           top: 141px;
       }
   }
    @media screen and (min-width: 1024px) {
        .content-pane {
           margin-top: 49px;
       }
        .mobile-regions-subnav-fixed {
           margin-top: 0px;
       }
        #selectMobileMemberfilter {
           margin-top: 0px;
       }
        .member-divider {
            height: 60px;
        }
    }
   @media screen and (min-width: 768px) {
        .regions-subnav-fixed {
            top: 141px;
        }
    }
    #chart_div {cursor: pointer;}
</style>

<script type="text/javascript">

    var member_type = 'all_members';
    var sort_type = 'most_recent_summit';

</script>

<div class="container">
    <!-- Mobile header -->
    <div class="row sub-header-row hidden-lg hidden-md hidden-sm" style="height: 50px; position: fixed; width: 100%; z-index: 2;">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999; display: flex; justify-content: space-between;">
            <a class="mobile-header-sub-links" href="/challenges/{{ group.slug }}/">Info</a><a class="mobile-header-sub-links" href="/challenges/{{ group.slug }}/map/">Map</a><a class="mobile-header-sub-links" href="/challenges/{{ group.slug }}/peaks/">Peaks</a><a class="mobile-header-sub-links" href="/challenges/{{ group.slug }}/summits/">Summits</a><a style="{{ subnav_members_style }}" class="mobile-header-sub-links" href="/challenges/{{ group.slug }}/members/">Members</a>
        </div>
    </div>
    <!-- End mobile header -->

    <div class="row sub-header-row hidden-xs hidden-sm regions-subnav-fixed" style="height: 50px; padding-right: 0px;">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999;">
            <a style="margin-left: 0px;" id="view-all-members" class="region-header-sort-links ajax-link" onclick="loadMembers('all_members', sort_type, '1');">All {{ members_count }} Members</a><a id="view-pursuers" class="region-header-sort-links ajax-link" onclick="loadMembers('pursuers', sort_type, '1');">{{ pursuers_count }} Pursuers</a><a id="view-finishers" class="region-header-sort-links ajax-link" onclick="loadMembers('finishers', sort_type, '1');">{{ finishers_count }} Finishers</a>
            <span class="pull-right">Sort by:<a style="margin-left: 10px; color: rgb(153, 153, 153); font-weight: 300;" id="sort-pct-complete" class="region-header-sort-links ajax-link" onclick="loadMembers(member_type, 'pct_complete', '1');">% Completed</a><a style="margin-left: 35px; color: rgb(153, 153, 153); font-weight: 300;" id="sort-most-recent-summit" class="region-header-sort-links ajax-link" onclick="loadMembers(member_type, 'most_recent_summit', '1');">Most recent climb</a></span>
        </div>
    </div>

    <div class="row content-pane">

        <div class="col-md-12">
            <div class="row sub-header-row hidden-md hidden-lg filter-bar" style="height: 50px; padding-right: 0px;">
                <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12" style="height: 50px;line-height: 26px;">
                    <div style="float: left; width: 50%; height: 100%; border-right: solid 1px #cfcfcf;">
                        <div class="select" id="selectMobileMemberfilter">
                            <input type="hidden" id="hdnMobileMemberFilter" value="">
                            <button class="btn btn-default member-filter-button" style="border: none; padding-top: 7px; padding-bottom: 7px; margin-left: 1px; color: #00b1f2; font-size: 12px; padding-left: 5px; padding-right: 5px; background-color: #f2f2f2;" data-toggle="dropdown" aria-expanded="false">
                                <span id="mobile-memberfilter-title">All {{ members_count }} members</span>
                                <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu memberfilter-list" style="cursor: pointer; height: 140px; left: 5px; overflow: auto; top: 50px;">
                                <li style="float: none; height: 30px; margin: 10px 0 0;"><a onclick="loadMembers('all_members', sort_type, '1');" class="memberfilter-item" data-value="all_members" href="#">All {{ members_count }} members</a></li>
                                <li style="float: none; height: 30px; margin: 10px 0 0;"><a onclick="loadMembers('pursuers', sort_type, '1');" class="memberfilter-item" data-value="pursuers" href="#">{{ pursuers_count }} Pursuers</a></li>
                                <li style="float: none; height: 30px; margin: 10px 0 0;"><a onclick="loadMembers('finishers', sort_type, '1');"class="memberfilter-item" data-value="finishers" href="#">{{ finishers_count }} Finishers</a></li>
                            </ul>
                        </div>
                    </div>
                    <div style="float: left; margin-left: 10px;">
                        <div class="select" id="selectMobileSortMembers" style="margin-top: 7px;">
                            <button class="btn btn-default mobile-sortmembers-button" style="border: none; padding-top: 7px; padding-bottom: 7px; margin-left: 1px; color: #00b1f2; font-size: 12px; padding-left: 5px; padding-right: 5px; background-color: #f2f2f2;" data-toggle="dropdown">
                                <span id="mobile-sortmembers-title">% Completed</span>
                                <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu mobile-sortsummits-list" style="height: 100px; left: 50%; top: 50px; overflow: auto;">
                                <li style="float: none; height: 30px; margin: 10px 0 0;"><a onclick="loadMembers(member_type, 'pct_complete', '1');" href="#">% Completed</a></li>
                                <li style="float: none; height: 30px; margin: 10px 0 0;"><a onclick="loadMembers(member_type, 'most_recent_summit', '1');" href="#">Most recent climb</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-12">
            <div class="row" id="ajax-data-loading" style="display: none;">
              <div class="col-md-12 data-loading" style="text-align: center; color: #666;">
                <i class="fa fa-spinner fa-spin fa-5x"></i>
              </div>
            </div>
            <div class="row dark-background-row"><div class="sp-60"></div></div>
            <div class="row" id="members-list">
            </div>
        </div>
    </div>
    <div class="row dark-background-row" id="pagination" style="border-bottom: none; color: #ccc; margin-top: -1px; height: 80px; margin-left: 0px; display: none;">
        <div class="col-md-12">
            <div class="row" style="padding-left: 15px; margin-left: 5px; text-align: center;">
                <span id="pagination-pages" class="step-links"></span>
            </div>
        </div>
    </div>
    <div class="row dark-header-row hidden-lg hidden-md hidden-sm">
        <div class="row">
            <div style="height: 28px;"></div>
        </div>
    </div>
    <div class="row dark-header-row hidden-xs">
        <div class="row">
            <div style="height: 53px;"></div>
        </div>
    </div>
</div>

<script type="text/javascript">

    var viewport_height = $(window).height();
    var viewport_width = $(window).width();
    var footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
    var data_loading_margin = (viewport_height - footer_height) / 3;
    $('.data-loading').css('margin-top', data_loading_margin);
    $(window).resize(function() {
        viewport_height = $(window).height();
        viewport_width = $(window).width();
        footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
        data_loading_margin = (viewport_height - footer_height) / 3;
        $('.data-loading').css('margin-top', data_loading_margin);
    });

    function loadMembers(type, sort, page) {

        member_type = type;
        sort_type = sort;

        $('#pagination').css('display','none');
        window.scrollTo(0, 0);

        var totalPages;
        var membersCount = {{ members_count }};
        var pursuersCount = {{ pursuers_count }};
        var finishersCount = {{ finishers_count }};
        var displayCount;

        switch(member_type) {
            case 'all_members':
                $('#view-all-members').css('color', '#f24100');
                $('#view-all-members').css('font-weight', '500');
                $('#view-pursuers').css('color', '#999');
                $('#view-pursuers').css('font-weight', '300');
                $('#view-active-pursuers').css('color', '#999');
                $('#view-active-pursuers').css('font-weight', '300');
                $('#view-finishers').css('color', '#999');
                $('#view-finishers').css('font-weight', '300');
                $('#mobile-memberfilter-title').html('All {{ members_count }} members');
                totalPages = Math.ceil(membersCount/25);
                displayCount = membersCount;
                break;
            case 'pursuers':
                $('#view-all-members').css('color', '#999');
                $('#view-all-members').css('font-weight', '300');
                $('#view-pursuers').css('color', '#f24100');
                $('#view-pursuers').css('font-weight', '500');
                $('#view-active-pursuers').css('color', '#999');
                $('#view-active-pursuers').css('font-weight', '300');
                $('#view-finishers').css('color', '#999');
                $('#view-finishers').css('font-weight', '300');
                $('#mobile-memberfilter-title').html('Pursuers');
                totalPages = Math.ceil(pursuersCount/25);
                displayCount = pursuersCount;
                break;
            case 'finishers':
                $('#view-all-members').css('color', '#999');
                $('#view-all-members').css('font-weight', '300');
                $('#view-pursuers').css('color', '#999');
                $('#view-pursuers').css('font-weight', '300');
                $('#view-active-pursuers').css('color', '#999');
                $('#view-active-pursuers').css('font-weight', '300');
                $('#view-finishers').css('color', '#f24100');
                $('#view-finishers').css('font-weight', '500');
                $('#mobile-memberfilter-title').html('Finishers');
                totalPages = Math.ceil(finishersCount/25);
                displayCount = finishersCount;
                break;
            default:
                $('#view-all-members').css('color', '#f24100');
                $('#view-all-members').css('font-weight', '500');
                $('#view-pursuers').css('color', '#999');
                $('#view-pursuers').css('font-weight', '300');
                $('#view-active-pursuers').css('color', '#999');
                $('#view-active-pursuers').css('font-weight', '300');
                $('#view-finishers').css('color', '#999');
                $('#view-finishers').css('font-weight', '300');
                $('#mobile-memberfilter-title').html('All {{ members_count }} members');
                totalPages = Math.ceil(membersCount/25);
                displayCount = membersCount;
        }

        switch(sort_type) {
            case 'most_recent_summit':
                $('#sort-most-recent-summit').css('color', '#f24100');
                $('#sort-most-recent-summit').css('font-weight', '500');
                $('#sort-pct-complete').css('color', '#999');
                $('#sort-pct-complete').css('font-weight', '300');
                $('#mobile-sortmembers-title').html('Most recent summit');
                break;
            case 'pct_complete':
                $('#sort-most-recent-summit').css('color', '#999');
                $('#sort-most-recent-summit').css('font-weight', '300');
                $('#sort-pct-complete').css('color', '#f24100');
                $('#sort-pct-complete').css('font-weight', '500');
                $('#mobile-sortmembers-title').html('% Completed');
                break;
            default:
                $('#sort-most-recent-summit').css('color', '#f24100');
                $('#sort-most-recent-summit').css('font-weight', '500');
                $('#sort-pct-complete').css('color', '#999');
                $('#sort-pct-complete').css('font-weight', '300');
                $('#mobile-sortmembers-title').html('% Completed');
        }

        window.location.hash = 'type='+member_type+'&order='+sort_type+'&page='+page;

        $('#members-list').empty();
        $('#ajax-data-loading').css('display', 'inline');
        var counter = 1;
        $.getJSON('{% url "challenges_list_members" %}?challenge_id={{ group.id }}&type='+member_type+'&sort='+sort_type+'&page='+page , function(data) {
            var totalPeaks = {{ group.get_items_order_by_elevation|length }};
            var pctComplete;
            var progressClass;
            $.each( data, function( key, val ) {
                if (key=='members') {

                    $.each( val, function( memberkey, memberval ) {
                        pctComplete = Math.round(memberval.current_round_count / totalPeaks * 100);
                        if (pctComplete == 100) {
                            progressClass = 'progress-bar-green';
                        } else {
                            progressClass = 'progress-bar-blue';
                        }
                        var roundsCompleted = memberval.rounds_completed;
                        var currentRound = parseInt(roundsCompleted) + 1;
                        var i = 1;
                        var completedHtml = '';
                        var currentRoundHtml = '';
                        var completedRoundHtml = ''
                        var memberUsernameClass = 'member-username';
                        var memberProgressClass = 'progress';
                        var memberLastSummitClass = 'member-last-summit';
                        if (roundsCompleted > 0) {
                            currentRoundHtml = 'Round ' + currentRound + ': ';
                            memberUsernameClass = 'member-username member-username-two-bars';
                            memberProgressClass = 'progress progress-two-bars';
                            memberLastSummitClass = 'member-last-summit member-last-summit-two-bars';
                            while (i <= roundsCompleted) {
                                if (memberval.current_round_count > 0) {
                                    completedRoundHtml = 'Round ' + i + ': ';
                                }
                                completedHtml = '<div class="hidden-xs ' + memberProgressClass + '"><div class="progress-bar progress-bar-green" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: 100%;"><span class="user-challenge-show">' + completedRoundHtml + totalPeaks + ' out of ' + totalPeaks + ' peaks - completed!</span></div></div>';
                                i++;
                            }
                        }

                        var dividerDiv = '';
                        if (counter > 1) {
                            dividerDiv = '<div class="row dark-background-row"><div class="member-divider"></div></div>';
                        }

                        var memberRank = ((page - 1) * 25) + counter;

                        var currentHtml = '';
                        if (memberval.current_round_count >= 0) {
                            currentHtml = '<div class="' + memberProgressClass + '"><div class="progress-bar ' + progressClass + '" role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100" style="width: ' + pctComplete + '%;"><span class="user-challenge-show">' + currentRoundHtml + memberval.current_round_count + ' out of ' + totalPeaks + ' peaks ('+ pctComplete + '%)</span></div></div>';
                        }
                        //if zero peaks on 2nd or later round, don't show
                        if (roundsCompleted > 0 && memberval.current_round_count == 0) {
                            currentHtml = '';
                            //also remove the hidden-xs class from the completed round
                            completedHtml = completedHtml.replace("hidden-xs", "");
                        }

                        var display_date = $.format.date(memberval.most_recent_summit[0].summitlog_date + 'T00:00:00', 'MMM, d yyyy');
                        var desktop_date = '<span class="hidden-xs"> on <a href="/' + memberval.most_recent_summit[0].peak_slug + '/summits/' + memberval.most_recent_summit[0].summitlog_id + '/">' + memberval.most_recent_summit[0].summitlog_date + '</a></span>';
                        var mobile_date = '<span class="hidden-sm hidden-md hidden-lg">&nbsp;&bull;&nbsp;<a href="/' + memberval.most_recent_summit[0].peak_slug + '/summits/' + memberval.most_recent_summit[0].summitlog_id + '/"><time class="timeago" datetime="' + memberval.most_recent_summit[0].summitlog_date + 'T00:00:00">' + memberval.most_recent_summit[0].summitlog_date + '</time></a></span>';

                        $('#members-list').append('<div class="col-md-12">' + dividerDiv + '<div class="row">  <div class="col-lg-3 col-md-3 col-sm-3 col-xs-4 hover-photos" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}' + memberval.avatar_url + '\'); background-size: cover; background-position: center top; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><a href="/members/' + memberval.username + '/"><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography leftpeak-responsive leftthird-responsive"></a></div></div>   <div class="col-lg-9 col-md-9 col-sm-9 col-xs-8 leftpeak-responsive leftthird-responsive" style="display: table; background-color: #fff;"><div class="leftpeak-responsive" style="aspect-ratio: 12/3; padding-top: 3%; vertical-align: middle; padding-left: 15px; padding-right: 15px; margin-bottom:7px;"><div class="' + memberUsernameClass + '"><span><a href="/members/' + memberval.username + '/" style="color: #000;">' + memberval.username + '</a></span></div>' + completedHtml + currentHtml + '<div class="hidden-xs ' + memberLastSummitClass + '"><span><span class="hidden-xs">last peak</span> <a href="/' + memberval.most_recent_summit[0].peak_slug + '/">' + memberval.most_recent_summit[0].peak_name + '</a>' + desktop_date + mobile_date + '</span></div></div></div></div></div>');
                        counter++;
                    });
                    //show pagination
                    var paginationHtml = '';
                    if (parseInt(page) == totalPages && totalPages > 1) {
                        paginationHtml = '<div style="display: inline-block; height: 55px;"><a onclick="loadMembers(\''+member_type+'\', \''+sort_type+'\', ' + (parseInt(page)-1).toString() + ');"><img src="{% static 'img/font-awesome_4-7-0_arrow-circle-left_256_0_00b1f2_none.png' %}" style="height: 26px; margin-bottom: 5px;"></a></div><div class="current" style="margin-left: 20px; margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">members ' + ((parseInt(page)-1)*25+1).toString() + ' - ' + numberWithCommas(displayCount) + ' of ' + numberWithCommas(displayCount) + '</div>';
                    } else if (parseInt(page) > 1) {
                        paginationHtml = '<div style="display: inline-block; height: 55px;"><a onclick="loadMembers(\''+member_type+'\', \''+sort_type+'\', ' + (parseInt(page)-1).toString() + ');"><img src="{% static 'img/font-awesome_4-7-0_arrow-circle-left_256_0_00b1f2_none.png' %}" style="height: 26px; margin-bottom: 5px;"></a></div><div class="current" style="margin-left: 20px; margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">members ' + ((parseInt(page)-1)*25+1).toString() + ' - ' + ((parseInt(page))*25).toString() + ' of ' + numberWithCommas(displayCount) + '</div>';
                    } else if (totalPages > 1) {
                        paginationHtml = '<div class="current" style="margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">members 1 - 25 of ' + numberWithCommas(displayCount) + '</div>';
                    } else {
                        paginationHtml = '<div class="current" style="margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">members 1 - ' + numberWithCommas(displayCount) + ' of ' + numberWithCommas(displayCount) + '</div>';
                    }
                    if (parseInt(page) < totalPages) {
                        paginationHtml = paginationHtml + '<div style="display: inline-block; height: 55px;"><a onclick="loadMembers(\''+member_type+'\', \''+sort_type+'\', ' + (parseInt(page)+1).toString() + ');"><img src="{% static 'img/font-awesome_4-7-0_arrow-circle-right_256_0_00b1f2_none.png'%}" style="height: 26px; margin-bottom: 5px;"></i></a></div>';
                        $('#pagination-pages').html(paginationHtml);
                        $('#pagination').css('display', 'inherit');
                    } else {
                        $('#pagination-pages').html(paginationHtml);
                        $('#pagination').css('display', 'inherit');
                    }

                }
            });
            $("time.timeago").timeago();
            $('#ajax-data-loading').css('display', 'none');
        });

    }

    function numberWithCommas(x) {
        return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    $(document).ready(function() {

        // Hide mobile filter bar on on scroll down
        var didScroll;
        var lastScrollTop = 0;
        var delta = 5;

        $(window).scroll(function(event){
            didScroll = true;
        });

        document.addEventListener("touchstart", ScrollStart, false);

        setInterval(function() {
            if (didScroll) {
                hasScrolled();
                didScroll = false;
            }
        }, 100);

        function ScrollStart() {
            //start of scroll event for iOS
            hasScrolled();
        }

        function hasScrolled() {
            var st = $(this).scrollTop();

            // Make sure they scroll more than delta
            if (Math.abs(lastScrollTop - st) <= delta)
                return;

            // If they scrolled down and are past the filter bar, add class .filter-scrollup.
            // This is necessary so you never see what is "behind" the navbar.
            if (st > lastScrollTop && st > 50) {
                // Scroll Down
                $('.filter-bar').hide();
            } else {
                // Scroll Up
                if (st + $(window).height() < $(document).height()) {
                    $('.filter-bar').show();
                }
            }
            lastScrollTop = st;
        }

        var vars = [], hash, type, sort, page;
        var q = document.URL.split('#')[1];
        if(q != undefined){
            q = q.split('&');
            for(var i = 0; i < q.length; i++){
                hash = q[i].split('=');
                vars.push(hash[1]);
                vars[hash[0]] = hash[1];
            }
        }

        if (vars['type'] != undefined) {
            type = vars['type'];
        } else {
            type = 'all_members';
        }

        if (vars['order'] != undefined) {
            sort = vars['order'];
        } else {
            sort = 'pct_complete';
        }

        if (vars['page'] != undefined) {
            page = vars['page'];
        } else {
            page = '1';
        }

        loadMembers(type, sort, page);

    });

</script>

{% endblock %}
