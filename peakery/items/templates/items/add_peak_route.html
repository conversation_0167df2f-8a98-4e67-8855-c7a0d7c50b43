{% extends "base_no_header_footer.html" %}
{% load static %}
{% load avatar_tags %}
{% load item_tags %}
{% load cache %}
{% load humanize %}

{% block title %}Add summit route for {{ peak.name }}{% endblock %}
{% block titlemeta %}Add summit route for {{ peak.name }}{% endblock %}
{% block description %}Add a summit route for {{ peak.name }}{% endblock %}

{% block mapbox %}
{% include "mapbox/mapbox.html" %}
{% endblock %}

{% block full_height_form %}
<form class="summitlog_form" method="POST" action="{% url "add_route" peak.id %}">
{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row" style="border-bottom: none;">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 form-header-bar" style="padding-left: 0px;">
            <div class="hidden-xs form-header-bar-img-div"><img class="form-header-bar-img" src="{{ peak.get_thumbnail_480 }}" /></div>
            <span style="font-size: 24px; font-weight: 600; color: #fff;">
            <div id="breadcrumbs" style="margin-left: 15px;">
                <div class="pull-right cancel-x-button-div">
                    <a class="cancel-x-button" onclick="cancelEdit();"><i class="fa fa-times"></i></a>
                </div>
                <div class="pull-right save-changes-button-div">
                    <button class="btn btn-secondary set2 input edit_route save-changes-button" type="submit" id="edit_route" disabled>Save route</button>
                </div>
                <div>
                    <div class="form-header-title ellipsis"><span class="hidden-xs hidden-sm">Add summit route for </span><span class="header-peak-name">{{ peak.name }}</span></div>
                </div>
            </div>
            </span>
        </div>
    </div>
{% endblock %}

{% block content %}

    <script src="{% static 'vendor/s3.fine-uploader/s3.fine-uploader.js' %}"></script>

    <style type="text/css">

        body.modal-open {
            overflow: visible;
        }

        .remove-photo {
            position: absolute;
            top: 10px;
            right: 10px;
            color: #fff;
            font-size: 24px;
        }

        .remove-photo:hover {
            color: #ccc;
        }

        .remove-step {
            font-size: 24px;
        }

        .noUi-base {
            margin-left: 0px;
            width: 100%;
        }

        .noUi-handle {
            border: none;
            border-radius: 10px;
            background: #00b2f2;
            cursor: pointer;
            box-shadow: none;
        }

        .noUi-handle:before, .noUi-handle:after {
            content: none;
        }

        .noUi-connect {
            background: #00b2f2;
            box-shadow: none;
        }

        .noUi-horizontal {
            height: 4px;
        }

        .noUi-background {
            background: #999;
            box-shadow: none;
        }

        .noUi-target {
            border-radius: 4px;
            border: none;
            box-shadow: none;
        }

        .toggle-off.btn {
            padding-left: 16px;
        }

        .ui-autocomplete {
            max-height: 100px;
            overflow-y: auto;
            /* prevent horizontal scrollbar */
            overflow-x: hidden;
            /* add padding to account for vertical scrollbar */
            padding-right: 20px;
        }
            /* IE 6 doesn't support max-height
           * we use height instead, but this forces the menu to always be this tall
           */
        * html .ui-autocomplete {
            height: 100px;
        }

        form.summitlog_form input[type="text"], form.summitlog_form input[type="password"] {
            width: 100%;
        }

        form.summitlog_form fieldset {
            margin-bottom: 0px;
        }

        #difficulty_rating_container:after {
            font-family: Font Awesome\ 5 Free;
            font-weight: 900;
            height: 0px;
            width: 0px;
            margin-right: 10px;
        }

        @media screen and (max-width: 767px) and (min-width: 1px) {
            #content-body {
               margin-top: 0px;
               padding-bottom: 0px;
            }
            html, body {
                letter-spacing: .03125em;
            }
            .content-pane {
               margin-top: 30px;
            }
            #difficulty_rating_container {
                width: 100%;
            }
            ul#peakroute-files textarea {
                font-size: 14px;
            }
            .row-full-width .col-xs-12 {
                padding-left: 10px;
                padding-right: 10px;
            }
            #txtAddRouteType, #txtAddRouteFeature, #txtAddChallengeTag, #txtAddGearTag, #route-related-link {
                width: 70%;
                font-size: 16px;
            }
            #btnAddRouteType, #btnAddRouteFeature, #btnAddRouteChallenge, #btnAddGearTag, #btnAddRelatedLink {
                width: 84px;
            }
            .header-help {
                color: #999;
                font-size: 11px;
                font-weight: 300;
                margin-left: 10px;
            }
            .field-title {
                font-size: 16px;
                font-weight: 700;
            }
            .form-header-title {
                font-size: 14px;
                font-weight: 500;
            }
            input {
                font-size: 16px;
                font-weight: 300;
            }
            form textarea {
                font-size: 16px;
                font-weight: 300;
            }
            .route-info-section {
                margin-top: 20px;
            }
            .field-title-spacer {
                height: 0px;
            }
            .header-peak-name {
                color: #fff;
            }
            .header-peak {
                font-size: 18px;
                font-weight: 500;
            }
            .close-route-edit {
                margin-right: 0px;
            }
            #edit_route {
                width: 125px;
            }
        }
        @media screen and (min-width: 768px) {

            #content-body {
               margin-top: 30px;
            }
            .content-pane {
               margin-top: 0px;
            }
            #txtAddRouteType, #txtAddRouteFeature, #txtAddChallengeTag, #txtAddGearTag {
                width: 33%;
                font-size: 18px;
            }
            #route-related-link {
                width: 70%;
                font-size: 18px;
            }
            #btnAddRouteType, #btnAddRouteFeature, #btnAddRouteChallenge, #btnAddGearTag, #btnAddRelatedLink {
                width: 150px;
            }
            input {
                font-size: 18px;
                font-weight: 300;
            }
            form textarea {
                font-size: 18px;
                font-weight: 300;
            }
            .route-info-section {
                margin-top: 60px;
            }
            .field-title-spacer {
                height: 10px;
            }
            #edit_route {
                width: 160px;
            }
        }
        @media screen and (min-width: 768px) and (max-width: 1023px) {
            ul#peakroute-files textarea {
                font-size: 16px;
            }
            .header-peak-name {
                color: #fff;
            }
            .header-peak {
                font-size: 18px;
                font-weight: 500;
            }
            .close-route-edit {
                margin-right: 0px;
            }
            .header-help {
                color: #999;
                font-size: 12px;
                font-weight: 300;
                margin-left: 10px;
            }
            .field-title {
                font-size: 18px;
                font-weight: 700;
            }
            .form-header-title {
                font-size: 18px;
                font-weight: 500;
            }
        }
        @media screen and (min-width: 1024px) {
            ul#peakroute-files textarea {
                font-size: 18px;
            }
            .header-peak-name {
                color: #f24100;
            }
            .header-peak {
                font-size: 20px;
                font-weight: 300;
            }
            .close-route-edit {
                margin-right: 20px;
            }
            .header-help {
                color: #999;
                font-size: 14px;
                font-weight: 300;
                margin-left: 10px;
            }
            .field-title {
                font-size: 21px;
                font-weight: 700;
            }
            .form-header-title {
                font-size: 20px;
                font-weight: 300;
            }
            #gm-custom-mapunits {
                right: 142px;
            }
        }
        @media screen and (min-width: 1px) and (max-width: 1279px) {
             .no-header-container {
               padding-left: 0px;
            }
        }
        ::-webkit-input-placeholder { font-weight: 300; }
        ::-moz-placeholder { font-weight: 300; } /* firefox 19+ */
        :-ms-input-placeholder { font-weight: 300; } /* ie */
        input:-moz-placeholder { font-weight: 300; }

        .gm-style-mtc {
            opacity: .8;
        }

        .remove-step {
            color: #eee;
            cursor: pointer;
            position: absolute;
            top: 10px;
            right: 10px;
        }
        .remove-step:hover {
            color: #ccc;
        }

        .qq-uploader {
            width: 100%;
            height: 100%;
            border-radius: 0px;
            background-color: #33c1f5;
        }

        .qq-upload-list {
            -webkit-box-shadow: none;
            -moz-box-shadow: none;
            box-shadow: none;
        }

        .qq-upload-list li.qq-upload-success {
            background-color: #f6f6f6;
            color: #424242;
            border-bottom: none;
            border-top: none;
        }

        .qq-upload-list li.qq-in-progress {
            background-color: #f6f6f6;
            color: #424242;
            border-bottom: none;
            border-top: none;
        }

        div.summitlog.caption {
            width: 100% !important;
        }

        @media screen and (min-width: 1px) and (max-width: 479px) {
            .qq-upload-retry-container {
                position: absolute;
                margin-top: -53px;
            }
            .qq-upload-button-selector {
                width: 120px;
                min-height: 80px;
                font-size: 15px;
                text-align: center;
                background-color: #33c1f5;
                z-index: 1;
                margin: 0px;
            }
            .qq-upload-button-text {
                line-height: 80px;
            }
            .qq-thumbnail-wrapper, .qq-thumbnail-selector {
                width: 120px;
                max-width: 120px;
                min-height: 80px;
            }
            .remove-photo {
                width: 120px;
                max-width: 120px;
                right: 6px;
                top: 11px;
                vertical-align: top;
                position: absolute;
                display: table-cell;
                text-align: right;
            }
            .route-step-photo {
                position: relative;
                align-content: stretch;
                width: 120px;
            }
        }
        @media screen and (min-width: 480px) and (max-width: 767px) {
            .qq-upload-retry-container {
                position: absolute;
                margin-top: -53px;
            }
            .qq-upload-button-selector {
                width: 200px;
                min-height: 120px;
                font-size: 18px;
                text-align: center;
                background-color: #33c1f5;
                z-index: 1;
                margin: 0px;
            }
            .qq-upload-button-text {
                line-height: 120px;
            }
            .qq-thumbnail-wrapper, .qq-thumbnail-selector {
                width: 200px;
                max-width: 200px;
                min-height: 133px;
            }
            .remove-photo {
                width: 200px;
                max-width: 200px;
                right: 6px;
                top: 11px;
                vertical-align: top;
                position: absolute;
                display: table-cell;
                text-align: right;
            }
            .route-step-photo {
                position: relative;
                align-content: stretch;
                width: 200px;
            }
        }
        @media screen and (min-width: 768px) {
            .qq-upload-retry-container {
                position: absolute;
                margin-top: -53px;
            }
            .qq-upload-button-selector {
                width: 200px;
                min-height: 120px;
                font-size: 18px;
                text-align: center;
                background-color: #33c1f5;
                z-index: 1;
                margin: 0px;
            }
            .qq-upload-button-text {
                line-height: 120px;
            }
            .qq-thumbnail-wrapper, .qq-thumbnail-selector {
                width: 200px;
                max-width: 200px;
                min-height: 133px;
            }
            .remove-photo {
                width: 200px;
                max-width: 200px;
                right: 6px;
                top: 11px;
                vertical-align: top;
                position: absolute;
                display: table-cell;
                text-align: right;
            }
            .route-step-photo {
                position: relative;
                align-content: stretch;
                width: 200px;
            }
        }

        .qq-alert-dialog-selector, .qq-confirm-dialog-selector, .qq-prompt-dialog-selector {
            margin: auto;
            padding: 20px;
        }

        ul#summitlog-gpx-files li div.a {
            background-color: transparent;
            margin: 0px;
        }

    </style>

    <script type="text/template" id="qq-image-template">
        <div class="qq-uploader-selector qq-uploader" qq-drop-area-text="" style="max-height: none; background-color: #00b1f2; border: none; padding: 0px; min-height: 0px; overflow-y: visible;">
            <div class="qq-upload-drop-area-selector qq-upload-drop-area" qq-hide-dropzone>
                <span class="qq-upload-drop-area-text-selector"></span>
            </div>
            <ul class="qq-upload-list-selector qq-upload-list" aria-live="polite" aria-relevant="additions removals" style="max-height: none;">
                <li style="padding: 0px; margin: 0px;">
                    <div class="qq-thumbnail-wrapper" style="position: relative; align-content: stretch;">
                        <div>
                            <img class="qq-thumbnail-selector" qq-max-size="300" qq-server-scale style="margin-right: 0px;">
                        </div>
                        <div class="remove-photo"><i style="text-shadow: 0px 0px 5px #ccc;" class="fa fa-times" aria-hidden="true"></i></div>
                    </div>
                    <div class="qq-upload-retry-container">
                        <button type="button" class="qq-upload-retry-selector qq-upload-retry btn btn-secondary" style="font-size: 12px;">Retry</button>
                    </div>
                </li>
            </ul>
            <div class="qq-upload-button-selector btn btn-secondary">
                <div class="qq-upload-button-text">Add photo</div>
            </div>
            <span class="qq-drop-processing-selector qq-drop-processing">
                <span>Processing dropped files...</span>
                <span class="qq-drop-processing-spinner-selector qq-drop-processing-spinner"></span>
            </span>

            <dialog class="qq-alert-dialog-selector">
                <div class="qq-dialog-message-selector"></div>
                <div class="qq-dialog-buttons">
                    <button type="button" class="qq-cancel-button-selector btn btn-secondary">Close</button>
                </div>
            </dialog>

            <dialog class="qq-confirm-dialog-selector">
                <div class="qq-dialog-message-selector"></div>
                <div class="qq-dialog-buttons">
                    <button type="button" class="qq-cancel-button-selector btn btn-secondary">No</button>
                    <button type="button" class="qq-ok-button-selector btn btn-secondary">Yes</button>
                </div>
            </dialog>

            <dialog class="qq-prompt-dialog-selector">
                <div class="qq-dialog-message-selector"></div>
                <input type="text">
                <div class="qq-dialog-buttons">
                    <button type="button" class="qq-cancel-button-selector btn btn-secondary">Cancel</button>
                    <button type="button" class="qq-ok-button-selector btn btn-secondary">Ok</button>
                </div>
            </dialog>
        </div>
    </script>

    <script type="text/template" id="qq-gpx-template">
        <div class="qq-uploader-selector qq-uploader" qq-drop-area-text="" style="max-height: none; background-color: #f6f6f6; border: none; padding: 0px; min-height: 0px;">
            <div class="qq-upload-drop-area-selector qq-upload-drop-area" qq-hide-dropzone>
                <span class="qq-upload-drop-area-text-selector"></span>
            </div>
            <ul class="qq-upload-list-selector qq-upload-list" aria-live="polite" aria-relevant="additions removals" style="max-height: none; display: none;">
                <li style="padding: 5px;">
                    <div style="display: flex; display: -webkit-flex;">
                        <div class="qq-thumbnail-wrapper" style="position: relative; align-content: stretch;">
                            <div>
                                <img class="qq-thumbnail-selector" qq-max-size="300" qq-server-scale style="margin-right: 0px;">
                            </div>
                        </div>
                    </div>
                </li>
            </ul>
            <div id="gpx-upload-button" class="qq-upload-button-selector btn btn-secondary" style="width: 245px; height: 55px; min-height: 55px; font-size: 18px; text-align: center; background-color: #33c1f5; margin: 5px; z-index: 1;">
                <div id="gpx-upload-button-div">Choose GPX file</div>
            </div>
            <span class="qq-drop-processing-selector qq-drop-processing">
                <span>Processing dropped files...</span>
                <span class="qq-drop-processing-spinner-selector qq-drop-processing-spinner"></span>
            </span>

            <dialog class="qq-alert-dialog-selector">
                <div class="qq-dialog-message-selector"></div>
                <div class="qq-dialog-buttons">
                    <button type="button" class="qq-cancel-button-selector btn btn-secondary">Close</button>
                </div>
            </dialog>

            <dialog class="qq-confirm-dialog-selector">
                <div class="qq-dialog-message-selector"></div>
                <div class="qq-dialog-buttons">
                    <button type="button" class="qq-cancel-button-selector btn btn-secondary">No</button>
                    <button type="button" class="qq-ok-button-selector btn btn-secondary">Yes</button>
                </div>
            </dialog>

            <dialog class="qq-prompt-dialog-selector">
                <div class="qq-dialog-message-selector"></div>
                <input type="text">
                <div class="qq-dialog-buttons">
                    <button type="button" class="qq-cancel-button-selector btn btn-secondary">Cancel</button>
                    <button type="button" class="qq-ok-button-selector btn btn-secondary">Ok</button>
                </div>
            </dialog>
        </div>
    </script>

    <div class="container" style="padding-left: 0px; padding-right: 0px;">

        <div class="content-pane" style="background-color: #f6f6f6; padding-top: 10px;">

            <input type="hidden" name="route_id" value="{{ route.id }}">
            <!--STEP 2-->

            <div class="row row-full-width">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <fieldset class="routeName">
                        <span class="field-title">Route name</span>
                        <div class="field-title-spacer"></div>
                        <input type="text" name="route-name" id="route-name" style="padding-left: 10px;" value="{{ route.name }}"></input>
                    </fieldset>
                </div>
            </div>

            <div class="row row-full-width route-info-section">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="padding-left: 0px; padding-right: 0px;">
                    <fieldset>
                        <div id="route-type">
                            <span class="field-title" style="margin-left: 15px;">Route type <span class="header-help" style="margin-top: 15px; padding-bottom: 0px; color: #999;">select all that are part of the route</span></span>
                            <div class="field-title-spacer"></div>
                            <div id="route-type-choices">
                                <div class="toggle-switch"><input class="route-type-input" type="checkbox" name="chkRouteTypes" value="trail"><label>trail</label></div>
                                <div class="toggle-switch"><input class="route-type-input" type="checkbox" name="chkRouteTypes" value="climbers path"><label>climbers path</label></div>
                                <div class="toggle-switch"><input class="route-type-input" type="checkbox" name="chkRouteTypes" value="off-trail"><label>off-trail</label></div>
                                <div class="toggle-switch"><input class="route-type-input" type="checkbox" name="chkRouteTypes" value="scramble"><label>scramble</label></div>
                                <div class="toggle-switch"><input class="route-type-input" type="checkbox" name="chkRouteTypes" value="mountaineering"><label>mountaineering</label></div>
                                <div class="toggle-switch"><input class="route-type-input" type="checkbox" name="chkRouteTypes" value="rock climb"><label>rock climb</label></div>
                                <div class="toggle-switch"><input class="route-type-input" type="checkbox" name="chkRouteTypes" value="ice climb"><label>ice climb</label></div>
                                <div class="toggle-switch"><input class="route-type-input" type="checkbox" name="chkRouteTypes" value="snow climb"><label>snow climb</label></div>
                                <div class="toggle-switch"><input class="route-type-input" type="checkbox" name="chkRouteTypes" value="glacier climb"><label>glacier climb</label></div>
                                <div class="toggle-switch"><input class="route-type-input" type="checkbox" name="chkRouteTypes" value="road - paved"><label>road - paved</label></div>
                                <div class="toggle-switch"><input class="route-type-input" type="checkbox" name="chkRouteTypes" value="road - dirt"><label>road - dirt</label></div>
                            </div>
                        </div>
                        <div style="margin-top: 10px; margin-left: 15px; float: left; width: 100%;">
                            <input type="text" maxlength="20" name="txtAddRouteType" id="txtAddRouteType" style="padding-left: 10px;" placeholder="other type...">
                            <button type="button" id="btnAddRouteType" class="btn btn-secondary" style="height: 52px; margin-top: -2px; margin-left: 2px;">Add</button>
                        </div>
                    </fieldset>
                </div>
            </div>

            <div class="row row-full-width route-info-section">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <span class="field-title">Difficulty rating <div class="helpText" style="display: inline; margin-left: 10px;"><a id="difficulty-info-link" data-toggle="modal" data-target="#difficulty-info" style="cursor: pointer; color: #00B1F2; font-weight: 500; font-size: 14px;">more info</a></div></span>
                    <div class="field-title-spacer"></div>
                    <fieldset>
                        <div id="difficulty_rating_container">
                            <select name="difficulty-rating" id="difficulty-rating">
                                <option value="">select rating</option>
                                <option value="Class 1">Class 1</option>
                                <option value="Class 2">Class 2</option>
                                <option value="Class 3">Class 3</option>
                                <option value="Class 4">Class 4</option>
                                <option value="Class 5">Class 5</option>
                            </select>
                        </div>
                    </fieldset>
                </div>
            </div>

            <div class="row row-full-width route-info-section hidden-xs">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <span class="field-title route-info-section">Route GPS track</span>
                    <span class="pull-right"><a style="cursor: pointer;" id="remove-gpx-file" style="display: none;">Remove GPX File</a></span>
                </div>
                <div class="field-title-spacer"></div>
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <fieldset class="summitLogGpx">
                    <ul id="summitlog-gpx-files" class="vert add_photo">

                    <li class="gpx-file-1">
                        <div class="a clearfix">
                            <div id="gpx-file-1" class="imageContainer">
                                <noscript>
                                    <p>Please enable JavaScript to use file uploader.</p>
                                </noscript>
                            </div>
                        </div>
                    </li>

                    </ul>
                    </fieldset>
                    <p style="display: none;" id="gpx-cropper-info">Adjust start point<span class="pull-right">Adjust end point to the summit</span></p>
                    <div id="gpx-cropper-container" style="display: none; width: 100%; margin-left: 10px; padding-right: 10px; padding-bottom: 20px;">
                        {% if route.gpx_file %}
                        <div id="gpx-cropper" style=""></div>
                        <input type="hidden" name="gpx-start-index" id="gpx-start-index" value="{{ route.gpx_start_index }}">
                        <input type="hidden" name="gpx-end-index" id="gpx-end-index" value="{{ route.gpx_end_index }}">
                        <input type="hidden" name="gpx-start-lat" id="gpx-start-lat" value="{{ route.gpx_start_lat }}">
                        <input type="hidden" name="gpx-end-lat" id="gpx-end-lat" value="{{ route.gpx_end_lat }}">
                        <input type="hidden" name="gpx-start-lon" id="gpx-start-lon" value="{{ route.gpx_start_lon }}">
                        <input type="hidden" name="gpx-end-lon" id="gpx-end-lon" value="{{ route.gpx_end_lon }}">
                        <input type="hidden" name="gpx-file" id="gpx-file" value="{{ route.gpx_file }}">
                        {% else %}
                        <div id="gpx-cropper" style=""></div>
                        <input type="hidden" name="gpx-start-index" id="gpx-start-index" value="0">
                        <input type="hidden" name="gpx-end-index" id="gpx-end-index" value="500">
                        <input type="hidden" name="gpx-start-lat" id="gpx-start-lat" value="">
                        <input type="hidden" name="gpx-end-lat" id="gpx-end-lat" value="">
                        <input type="hidden" name="gpx-start-lon" id="gpx-start-lon" value="">
                        <input type="hidden" name="gpx-end-lon" id="gpx-end-lon" value="">
                        <input type="hidden" name="gpx-file" id="gpx-file" value="">
                        {% endif %}
                        <div id="route-step-files">
                            <input type="hidden" name="route-step-file-list" id="route-step-file-list" value="">
                        </div>
                    </div>
                    <div id="map-canvas" style="width: 100%; height: 0px; display: none;">
                        <div id="gm-custom-mapunits" class="gmnoprint gm-style-mtc" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 100px; top: 0px;">
                            <div id="gm-custom-mapunitsbutton" draggable="false" title="Change map units" style="cursor: pointer; direction: ltr; overflow: hidden; text-align: center; position: relative; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 11px; -webkit-background-clip: padding-box; background-clip: padding-box; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; font-weight: 500;">
                                <span id="gm-custom-mapunitsbutton-label-feet" class="gm-custom-mapunits-selected">Feet</span> | <span id="gm-custom-mapunitsbutton-label-meters" class="gm-custom-mapunits-unselected">Meters</span>
                            </div>
                        </div>
                        <div id="gm-custom-maptype" class="gmnoprint gm-style-mtc" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 104px; right: 0px; top: 0px;">
                            <div id="gm-custom-mapbutton" draggable="false" title="Change map style" style="cursor: pointer; direction: ltr; overflow: hidden; text-align: left; position: relative; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 11px; -webkit-background-clip: padding-box; background-clip: padding-box; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; font-weight: 500;">
                                <span id="gm-custom-mapbutton-label">Terrain</span><img src="https://maps.gstatic.com/mapfiles/arrow-down.png" draggable="false" style="-webkit-user-select: none; border: 0px; padding: 0px; margin: -2px 0px 0px; position: absolute; right: 6px; top: 50%; width: 7px; height: 4px;">
                            </div>
                            <div id="gm-custom-mapdropdown" style="background-color: white; z-index: -1; padding-left: 2px; padding-right: 2px; border-bottom-left-radius: 2px; border-bottom-right-radius: 2px; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; position: absolute; top: 100%; left: 0px; right: 0px; text-align: left; display: none;">
                                <div id="gm-custom-mapoption-terrain" draggable="false" title="Show street map with terrain" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    Terrain
                                </div>
                                <div id="gm-custom-mapoption-natatl" draggable="false" title="Show natural atlas" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    Natural Atlas (US)
                                </div>
                                <div id="gm-custom-mapoption-outdoors" draggable="false" title="Show open topo map" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    OpenTopoMap
                                </div>
                                <div id="gm-custom-mapoption-topo" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    Topo Govt (as avail)
                                </div>
                                <div id="gm-custom-mapoption-satstreets" draggable="false" title="Show satellite imagery with street map" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    Satellite
                                </div>
                                <div id="gm-custom-mapoption-sat" draggable="false" title="Show satellite imagery" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    Satellite Topo
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="marker-tooltip" data-url="" data-index="" style="cursor: pointer; display: none; position:absolute; width: 200px; height: 150px; background: rgba(0, 0, 0, 0.3); margin: 15px;"></div>
                </div>
            </div>

            <div class="row row-full-width">
                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 route-info-section">
                    <span class="field-title">Start location name / trailhead</span>
                    <div class="field-title-spacer"></div>
                    <input type="text" name="start-location" id="start-location" style="padding-left: 10px;" value="{% if route.start_location %}{{ route.start_location }}{% endif %}"></input>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 route-info-section">
                    <span class="field-title">Start elevation</span>
                    <div class="field-title-spacer"></div>
                    <fieldset>
                        <div>
                            <input type="text" name="elevation-start" id="elevation-start" style="width: 25%; padding-left: 10px;" value="">
                            <div class="btn-group" data-toggle="buttons" style="margin-left: 15px;">
                                <label class="btn active">
                                <input type="radio" name="elevation-start-units" id="elevation-start-ft" value="ft" checked><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i> <span>  ft</span>
                                </label>
                                <label class="btn" style="margin-left: 5px;">
                                <input type="radio" name="elevation-start-units" id="elevation-start-m" value="m"><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i><span> m</span>
                                </label>
                            </div>
                        </div>
                    </fieldset>
                </div>
            </div>

            <div class="row row-full-width">
                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 route-info-section">
                    <span class="field-title">Distance to summit <span class="header-help" style="margin-top: 15px; padding-bottom: 0px; color: #999;">one way</span></span>
                    <div class="field-title-spacer"></div>
                    <fieldset>
                        <div>
                            <input type="text" name="distance-to-summit" id="distance-to-summit" style="width: 25%; padding-left: 10px;" value="{% if route.distance_to_summit %}{{ route.distance_to_summit }}{% endif %}">
                            <div class="btn-group" data-toggle="buttons" style="margin-left: 15px;">
                                <label class="btn active">
                                <input type="radio" name="distance-to-summit-units" id="distance-to-summit-miles" value="mi" checked><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i> <span>  mi</span>
                                </label>
                                <label class="btn" style="margin-left: 5px;">
                                <input type="radio" name="distance-to-summit-units" id="distance-to-summit-km" value="km"><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i><span> km</span>
                                </label>
                            </div>
                            <input type="hidden" name="init-distance-to-summit" id="init-distance-to-summit" value="{% if route.distance_to_summit %}{{ route.distance_to_summit }}{% endif %}">
                        </div>
                    </fieldset>
                    <a name="steps"></a>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12 route-info-section">
                    <span class="field-title">Elevation gain <span class="header-help" style="margin-top: 15px; padding-bottom: 0px; color: #999;">one way</span></span>
                    <div class="field-title-spacer"></div>
                    <fieldset>
                        <div>
                            <input type="text" name="elevation-gain" id="elevation-gain" style="width: 33%; padding-left: 10px;" value="">
                            <div class="btn-group" data-toggle="buttons" style="margin-left: 15px;">
                                <label class="btn active">
                                <input type="radio" name="elevation-gain-units" id="elevation-gain-ft" value="ft" checked><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i> <span>  ft</span>
                                </label>
                                <label class="btn" style="margin-left: 5px;">
                                <input type="radio" name="elevation-gain-units" id="elevation-gain-m" value="m"><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i><span> m</span>
                                </label>
                            </div>
                        </div>
                    </fieldset>
                </div>
            </div>

            <div class="row row-full-width route-info-section">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <span class="field-title">Route step-by-step</span>
                    <span class="header-help" style="margin-top: 15px; padding-bottom: 0px; color: #999;">add as many steps as needed</span>
                    <div class="field-title-spacer"></div>
                    <fieldset class="summitLogImages">
                    <ul id="peakroute-files" class="vert add_photo"></ul>
                    </fieldset>
                    <button type="button" onclick="addStep(); return false;" style="width: 167px; height: 57px; font-size: 18px; padding: 0 20px; margin-top: -20px;" class="btn set2 input" id="add_next_step" />Add next step</button>
                    <div id="upload-ajax-spinner" style="display: none;"><img src="{% static 'img/misc/loading.gif' %}" /></div>
                </div>
            </div>

            <div class="row row-full-width route-info-section">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="padding-left: 0px; padding-right: 0px;">
                    <fieldset>
                        <div id="route-features">
                            <span class="field-title" style="margin-left: 15px;">Route features <span class="header-help" style="margin-top: 15px; padding-bottom: 0px; color: #999;">select one or more</span></span>
                            <div class="field-title-spacer"></div>
                            <div id="feature-tag-choices">
                                <div class="toggle-switch"><input class="feature-tag-input" type="checkbox" name="chkRouteFeatures" value="summit views"><label>summit views</label></div>
                                <div class="toggle-switch"><input class="feature-tag-input" type="checkbox" name="chkRouteFeatures" value="views en route"><label>views en route</label></div>
                                <div class="toggle-switch"><input class="feature-tag-input" type="checkbox" name="chkRouteFeatures" value="alpine vegetation"><label>alpine vegetation</label></div>
                                <div class="toggle-switch"><input class="feature-tag-input" type="checkbox" name="chkRouteFeatures" value="wildflowers"><label>wildflowers</label></div>
                                <div class="toggle-switch"><input class="feature-tag-input" type="checkbox" name="chkRouteFeatures" value="wildlife"><label>wildlife</label></div>
                                <div class="toggle-switch"><input class="feature-tag-input" type="checkbox" name="chkRouteFeatures" value="ripe berries"><label>ripe berries</label></div>
                                <div class="toggle-switch"><input class="feature-tag-input" type="checkbox" name="chkRouteFeatures" value="lake/pond"><label>lake/pond</label></div>
                                <div class="toggle-switch"><input class="feature-tag-input" type="checkbox" name="chkRouteFeatures" value="waterfall"><label>waterfall</label></div>
                                <div class="toggle-switch"><input class="feature-tag-input" type="checkbox" name="chkRouteFeatures" value="snowfield"><label>snowfield</label></div>
                                <div class="toggle-switch"><input class="feature-tag-input" type="checkbox" name="chkRouteFeatures" value="glacier"><label>glacier</label></div>
                                <div class="toggle-switch"><input class="feature-tag-input" type="checkbox" name="chkRouteFeatures" value="hut/lodge"><label>hut/lodge</label></div>
                                <div class="toggle-switch"><input class="feature-tag-input" type="checkbox" name="chkRouteFeatures" value="scree/talus"><label>scree/talus</label></div>
                            </div>
                        </div>
                        <div style="margin-top: 10px; margin-left: 15px; float: left; width: 100%;">
                            <input type="text" maxlength="20" name="txtAddRouteFeature" id="txtAddRouteFeature" style="padding-left: 10px;" placeholder="other route feature...">
                            <button type="button" id="btnAddRouteFeature" class="btn btn-secondary" style="height: 52px; margin-top: -2px; margin-left: 2px;">Add</button>
                        </div>
                    </fieldset>
                </div>
            </div>

            <div class="row row-full-width route-info-section">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="padding-left: 0px; padding-right: 0px;">
                    <fieldset>
                        <div id="route-challenges">
                            <span class="field-title" style="margin-left: 15px;">Route obstacles <span class="header-help" style="margin-top: 15px; padding-bottom: 0px; color: #999;">select one or more</span></span>
                            <div class="field-title-spacer"></div>
                            <div id="challenge-tag-choices">
                                <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkRouteChallenges" value="road/access issues"><label>road/access issues</label></div>
                                <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkRouteChallenges" value="routefinding"><label>routefinding</label></div>
                                <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkRouteChallenges" value="water crossing"><label>water crossing</label></div>
                                <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkRouteChallenges" value="bushwhacking"><label>bushwhacking</label></div>
                                <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkRouteChallenges" value="blowdowns"><label>blowdowns</label></div>
                                <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkRouteChallenges" value="no water source"><label>no water source</label></div>
                                <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkRouteChallenges" value="rockfall/loose rock"><label>rockfall/loose rock</label></div>
                                <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkRouteChallenges" value="seasonal snow"><label>seasonal snow</label></div>
                                <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkRouteChallenges" value="avalanche danger"><label>avalanche danger</label></div>
                                <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkRouteChallenges" value="crevasse danger"><label>crevasse danger</label></div>
                                <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkRouteChallenges" value="ice fall"><label>ice fall</label></div>
                                <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkRouteChallenges" value="cornices"><label>cornices</label></div>
                                <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkRouteChallenges" value="summit block"><label>summit block</label></div>
                                <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkRouteChallenges" value="big exposure"><label>big exposure</label></div>
                                <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkRouteChallenges" value="rappel"><label>rappel</label></div>
                            </div>
                        </div>
                        <div style="margin-top: 10px; margin-left: 15px; float: left; width: 100%;">
                            <input type="text" maxlength="20" name="txtAddChallengeTag" id="txtAddChallengeTag" style="padding-left: 10px;" placeholder="other route obstacle...">
                            <button type="button" id="btnAddRouteChallenge" class="btn btn-secondary" style="height: 52px; margin-top: -2px; margin-left: 2px;">Add</button>
                        </div>
                    </fieldset>
                </div>
            </div>

            <div class="row row-full-width route-info-section">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="padding-left: 0px; padding-right: 0px;">
                    <fieldset>
                        <div id="route-key-gear">
                            <span class="field-title" style="margin-left: 15px;">Key gear <span class="header-help" style="margin-top: 15px; padding-bottom: 0px; color: #999;">recommended gear for this route</span></span>
                            <div class="field-title-spacer"></div>
                            <div id="gear-tag-choices">
                                <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="ice axe"><label>ice axe</label></div>
                                <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="crampons"><label>crampons</label></div>
                                <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="helmet"><label>helmet</label></div>
                                <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="rope/harness"><label>rope/harness</label></div>
                                <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="climbing rack"><label>climbing rack</label></div>
                                <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="skis"><label>skis</label></div>
                                <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="snowboard"><label>snowboard</label></div>
                                <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="snowshoes"><label>snowshoes</label></div>
                                <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="bike"><label>bike</label></div>
                                <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="trekking poles"><label>trekking poles</label></div>
                                <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="mountaineering boots"><label>mountaineering boots</label></div>
                                <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="GPS device"><label>GPS device</label></div>
                            </div>
                        </div>
                        <div style="margin-top: 10px; margin-left: 15px; float: left; width: 100%;">
                            <input type="text" maxlength="20" name="txtAddGearTag" id="txtAddGearTag" style="padding-left: 10px;" placeholder="other key gear...">
                            <button type="button" id="btnAddGearTag" class="btn btn-secondary" style="height: 52px; margin-top: -2px; margin-left: 2px;">Add</button>
                        </div>
                    </fieldset>
                </div>
            </div>

            <a name="gettingthere"></a>
            <div class="row row-full-width route-info-section">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <fieldset class="whatHappened">
                        <span class="field-title">Getting there</span>
                        <div class="field-title-spacer"></div>
                        <textarea name="getting-there" id="getting-there" style="display: block; min-height: 115px; height: auto; resize: none; width: 100%; border: 1px solid #99999F;" placeholder="info & directions to the route's start point...">{% if route.getting_there %}{{ route.getting_there }}{% endif %}</textarea>
                        <script type="text/javascript">
                            $(function(){
                                $('textarea#getting-there').elastic();
                                $('textarea#getting-there').placeholder();
                            });
                        </script>
                    </fieldset>
                </div>
            </div>

            <a name="redtape"></a>
            <div class="row row-full-width route-info-section">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <fieldset class="whatHappened">
                        <span class="field-title">Red tape</span>
                        <div class="field-title-spacer"></div>
                        <textarea name="red-tape" id="red-tape" style="display: block; min-height: 115px; height: auto; resize: none; width: 100%; border: 1px solid #99999F;" placeholder="restrictions, permits, fees, etc.">{% if route.red_tape %}{{ route.red_tape }}{% endif %}</textarea>
                        <script type="text/javascript">
                            $(function(){
                                $('textarea#red-tape').elastic();
                                $('textarea#red-tape').placeholder();
                            });
                        </script>
                    </fieldset>
                </div>
            </div>

            <a name="addlinfo"></a>
            <div class="row row-full-width route-info-section" style="padding-bottom: 20px;">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <fieldset>
                        <div id="route-field-related-link">
                            <span class="field-title">More info</span>
                            <div class="field-title-spacer"></div>
                            <div id="route-related-links">
                            </div>
                            <div>
                                <input type="text" name="route-related-link" id="route-related-link" style="padding-left: 10px;" placeholder="enter link...">
                                <button type="button" id="btnAddRelatedLink" class="btn btn-secondary" style="height: 52px; margin-top: -2px; margin-left: 10px;">Add</button>
                                <input value="" name="route_related_links" id="route_related_links" type="hidden">
                            </div>
                        </div>
                    </fieldset>
                </div>
            </div>

        </div>

    </div>

    <div class="difficulty-info-modal modal fade" id="difficulty-info" tabindex="-1" role="dialog" aria-labelledby="difficulty-info-label">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                  <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                  <h4 class="modal-title" id="difficulty-info-label">A note on difficulty ratings</h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="margin-bottom: 5px; font-size: 16px;">All routes on peakery are rated using the Yosemite Decimal System (YDS) Class based on these definitions:</div>
                    </div>
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="font-size: 15px; margin-top: 10px;"><strong>Class 1</strong>: Hiking on a well-established trail.</div>
                    </div>
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="font-size: 15px; margin-top: 10px;"><strong>Class 2</strong>: Hiking cross-country, off-trail, or on crude paths. May occasionally use hands.</div>
                    </div>
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="font-size: 15px; margin-top: 10px;"><strong>Class 3</strong>: Scrambling requiring hands and feet with increased exposure. A rope is rarely used. Falls will be bad but might not be fatal.</div>
                    </div>
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="font-size: 15px; margin-top: 10px;"><strong>Class 4</strong>: Simple climbing with exposure. A rope is often used and natural protection can be found easily. Falls will usually be fatal.</div>
                    </div>
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="font-size: 15px; margin-top: 10px;"><strong>Class 5</strong>: Rock climbing involving technical moves. A rope is always used along with belaying and protection hardware. Un-roped falls will be fatal.</div>
                    </div>
                    <div class="row">
                        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="margin-top: 20px; font-size: 16px;">In the future, peakery plans to add more international rating systems.</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="message-modal modal fade" id="message-modal" tabindex="-1" role="dialog" aria-labelledby="message-modal-label">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                  <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                  <h4 class="modal-title" id="message-modal-label"></h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div id="message-modal-body" class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="margin-bottom: 5px; font-size: 16px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="confirm-modal modal fade" id="confirm-modal" tabindex="-1" role="dialog" aria-labelledby="confirm-modal-label">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                  <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                  <h4 class="modal-title" id="confirm-modal-label"></h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div id="confirm-modal-body" class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="margin-bottom: 5px; font-size: 16px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="savetext-modal modal fade" id="savetext-modal" tabindex="-1" role="dialog" aria-labelledby="savetext-modal-label">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                  <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                  <h4 class="modal-title" id="savetext-modal-label"></h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div id="savetext-modal-body" class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="margin-bottom: 5px; font-size: 16px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript">

    var totalSteps = 0;
    var uploaderIdle = true;
    var formSubmitted = false;
    var points = [];
    var linePoints = [];
    var lineData;
    var raw_points = [];
    var map = null;
    var poly = null;
    var bounds = null;
    var mapDiv = document.getElementById('map-canvas');
    var gpx_url, gpx_basefile;
    var requestIndex;
    var iconstyle;

    function saveRoute() {
        $('.summitlog_form').submit();
    }

    function isFutureDate(text){
        var arrDate = text.split("-");
        var date = new Date(arrDate[0], arrDate[1]-1, arrDate[2]);
        var _now=new Date();
        if(date.getTime()>_now.getTime()){
            $("span.invalid-date").show();
            return true;
        }
        $("span.invalid-date").hide();
        return false;
    }

    function processURL(url){
        var id;

        if (url.indexOf('youtube.com') > -1) {
            <!-- CHANGED -->
            id = url.split('v=')[1].split('&')[0];
            return id;
        } else if (url.indexOf('youtu.be') > -1) {
            id = url.split('/')[1];
            return id;
        } else {
            throw new Error('Unrecognised URL');
        }
    }

    function cancelEdit() {
        if ($('#edit_route').prop('disabled') == true) {
            window.location.href = '/{{ peak.slug_new_text }}/routes/';
        } else {
            $('#confirm-modal-label').html('Discard your changes?');
            $('#confirm-modal-body').html('<div style="margin-top: 20px; text-align: center;"><a href="/{{ peak.slug_new_text }}/routes/" class="btn btn-primary" style="width: 100px;">Discard</a><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">Cancel</button></div>');
            $('#confirm-modal').modal('show');
        }
    }

    function confirmRemovePhoto(where) {
        $('#confirm-modal-label').html('Are you sure?');
        $('#confirm-modal-body').html('<div style="margin-top: 20px; text-align: center;"><button type="button" onclick="removePhoto(\''+where+'\'); return false;" class="btn btn-primary" style="width: 100px;">Discard</button><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">Cancel</button></div>');
        $('#confirm-modal').modal('show');
    }

    function confirmRemoveStep(where) {
        $('#confirm-modal-label').html('Are you sure?');
        $('#confirm-modal-body').html('<div style="margin-top: 20px; text-align: center;"><button type="button" onclick="removeStep(\''+where+'\'); return false;" class="btn btn-primary" style="width: 100px;">Discard</button><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">Cancel</button></div>');
        $('#confirm-modal').modal('show');
    }

    function saveUnaddedText() {
        //when click the button to save unadded text
        if ($('#txtAddRouteType').val() != '') {
            $("#btnAddRouteType").click();
        }
        if ($('#txtAddRouteFeature').val() != '') {
            $("#btnAddRouteFeature").click();
        }
        if ($('#txtAddChallengeTag').val() != '') {
            $("#btnAddRouteChallenge").click();
        }
        if ($('#txtAddGearTag').val() != '') {
            $("#btnAddGearTag").click();
        }
        if ($('#route-related-link').val() != '') {
            $("#btnAddRelatedLink").click();
        }
        $('#savetext-modal').modal('hide');
        formSubmitted = true;
        $('#edit_route').html('<i class="fa fa-spinner fa-spin"></i>');
        if (uploaderIdle) {
            saveRoute();
        }
    }

    function replaceAll(str, find, replace) {
      return str.replace(new RegExp(find, 'g'), replace);
    }

    function removeRelatedLink(link_id) {
        var link_div = '#related-link-'+link_id;
        var link_url_div = '#related-link-url-'+link_id;
        var link_url = $(link_url_div).html();
        $(link_div).remove();
        var current_links = $("#route_related_links").val();
        var selected_links = current_links.replace('|'+link_url+'|,', '');
        $("#route_related_links").val(selected_links);
        if (uploaderIdle) {
            $('.edit_route').prop('disabled', false);
        }
    }

    function removeGPX() {
        $("#remove-gpx-file").html('Removing GPX File...');
        points = [];
        raw_points = [];
        linePoints = [];
        map = null;
        bounds = null;
        $("#gpx-file-1").show();
        $(".summitLogGpx").show();
        $("#map-canvas").hide();
        $("#remove-gpx-file").hide();
        $("#remove-gpx-file").html('Remove GPX File');
        $("#gpx-cropper-container").hide();
        $("#gpx-cropper-info").hide();
        $("#gpx-file").val('');
        if (uploaderIdle) {
            $('.edit_route').prop('disabled', false);
        }
    }

    function removePhoto(where) {
        $('#confirm-modal').modal('hide');
        var step_number = where.split("-")[1];

        var uploader1 = new qq.s3.FineUploader({
            debug: false,
            multiple: false,
            element: document.getElementById(where),
            template: 'qq-image-template',
            request: {
                endpoint: 'https://peakery-media.s3.amazonaws.com',
                accessKey: 'AKIAJPWBM4YZXFHWBTPQ'
            },
            signature: {
                endpoint: '{% url "s3signature" %}'
            },
            uploadSuccess: {
                endpoint: '{% url "s3_route_photo_upload" %}',
                params: {
                    'route_id': '{{ route.id }}',
                    'step_number': step_number
                }
            },
            iframeSupport: {
                localBlankPagePath: '/api/s3blank/'
            },
            retry: {
               enableAuto: false // defaults to false
            },
            validation: {
                acceptFiles: ['image/jpg, image/jpeg, .jpg, .jpeg'],
                allowedExtensions: ['jpg', 'jpeg'],
                sizeLimit: 10000000,
                image: {
                    minHeight: 1000,
                    minWidth: 1000
                }
            },
            messages: {
                typeError: 'Sorry, must be a JPG file.',
                sizeError: 'Sorry, this file is too big. Must be under 10 MB.',
                minHeightImageError: 'Sorry, this photo is too small. Must be at least 1000 x 1000 pixels.',
                minWidthImageError: 'Sorry, this photo is too small. Must be at least 1000 x 1000 pixels.'
            },
            showMessage: function (message) {
                $('#message-modal-label').html('Error');
                $('#message-modal-body').html(message);
                $('#message-modal').modal('show');
            },
            text: {
                fileInputTitle: 'Choose file(s)'
            },
            callbacks: {
                onSubmit: function(id, name) {},
                onSubmitted: function(id, name) {
                    $("#"+where).find('div.qq-upload-button-selector').hide();
                    $("li.file-"+step_number).find("div.caption, div.checker").show();
                    uploaderIdle = false;
                    $('.edit_route').prop('disabled', false);
                    $('#route-step-file-'+step_number).data('status', 'pending');
                },
                onComplete: function(id, name, responseJSON, maybeXhr) {
                    $("#"+where).show();
                    set_image(where, responseJSON);
                    jailai(where);
                    $('.edit_route').prop('disabled', false);
                },
                onAllComplete: function(successful, failed) {
                    //update status for this file
                    $('#route-step-file-'+step_number).data('status', 'complete');
                    //check status of all added steps to see if any pending uploads
                    var pendingUploads = false;
                    $('.route-step-file').each(function(i, obj) {
                        if ($(this).data('status') == 'pending') {
                            pendingUploads = true;
                        }
                    });
                    if (!pendingUploads) {
                        uploaderIdle = true;
                        if (formSubmitted) {
                            saveRoute();
                        }
                    }
                },
                onCancel: function(id, name) {},
                onUpload: function(id, name) {},
                onUploadChunk: function(id, name, chunkData) {},
                onUploadChunkSuccess: function(id, chunkData, responseJSON, xhr) {},
                onResume: function(id, fileName, chunkData) {},
                onProgress: function(id, name, loaded, total) {},
                onTotalProgress: function(loaded, total) {},
                onError: function(id, name, reason, maybeXhrOrXdr) {
                    $('#message-modal-label').html('Error');
                    $('#message-modal-body').html('Error uploading ' + name + '. Hit the Retry button on the photo to try again.');
                    $('#message-modal').modal('show');
                },
                onAutoRetry: function(id, name, attemptNumber) {},
                onManualRetry: function(id, name) {},
                onValidateBatch: function(fileOrBlobData) {},
                onValidate: function(fileOrBlobData) {},
                onSubmitDelete: function(id) {},
                onDelete: function(id) {},
                onDeleteComplete: function(id, xhrOrXdr, isError) {},
                onPasteReceived: function(blob) {},
                onStatusChange: function(id, oldStatus, newStatus) {},
                onSessionRequestComplete: function(response, success, xhrOrXdr) {}
            },
            objectProperties: {
                acl: 'public-read',
                key: function (fileId) {

                    var filename = uploader1.getName(fileId);
                    var uuid = uploader1.getUuid(fileId);
                    var ext = filename.substr(filename.lastIndexOf('.') + 1).toLowerCase();

                    return  'items/routes/{{ peak.slug_new_text }}_' + uuid + '.' + ext;

                }
            }
        });
        $("div#"+where).css("background-image","none");
        $("#route-step-"+where).val('');
    }

    function removeStep(where) {
        $('#confirm-modal').modal('hide');
        var step_number = where.split("-")[1];
        $('#step-'+step_number+'-details').html('');
        removePhoto(where);
        $('.'+where).hide();
    }

    function numberWithCommas(x) {
        return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    function validateForm() {
        //form validation

        //must enter route name
        if ($('#route-name').val() == '') {
            $('#message-modal-label').html('Invalid Route');
            $('#message-modal-body').html('<div style="margin-top: 20px; text-align: left;"><p>Please specify a route name.</p><p style="text-align: center;"><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">OK</button></p></div>');
            $('#message-modal').modal('show');
            return false;
        }

        //validate any URLs
        var validRelatedLink = true;
        if ($('#route-related-link').val() != '') {
            var validRelatedLink = isUrlValid($('#route-related-link').val());
        }
        if (validRelatedLink) {
            //any text left un-added in input fields?
            if ($('#txtAddRouteType').val() != '' || $('#txtAddRouteFeature').val() != '' || $('#txtAddChallengeTag').val() != '' || $('#txtAddGearTag').val() != '' || $('#route-related-link').val() != '') {
                var unsavedText = '';
                if ($('#txtAddRouteType').val() != '') {
                    unsavedText = unsavedText + '<p>Route type: <b>' + $('#txtAddRouteType').val() + '</b></p>';
                }
                if ($('#txtAddRouteFeature').val() != '') {
                    unsavedText = unsavedText + '<p>Route feature: <b>' + $('#txtAddRouteFeature').val() + '</b></p>';
                }
                if ($('#txtAddChallengeTag').val() != '') {
                    unsavedText = unsavedText + '<p>Route obstacle: <b>' + $('#txtAddChallengeTag').val() + '</b></p>';
                }
                if ($('#txtAddGearTag').val() != '') {
                    unsavedText = unsavedText + '<p>Key gear: <b>' + $('#txtAddGearTag').val() + '</b></p>';
                }
                if ($('#route-related-link').val() != '') {
                    unsavedText = unsavedText + '<p>More info: <b>' + $('#route-related-link').val() + '</b></p>';
                }
                $('#savetext-modal-label').html('Save your changes?');
                $('#savetext-modal-body').html('<div style="margin-top: 20px; text-align: left;"><p>You entered the following info but didn’t click the "Add" button next to it. Save this too?</p><p>' + unsavedText + '</p><p style="text-align: center;"><a onclick="saveUnaddedText();" class="btn btn-primary" style="width: 100px;">Save</a><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">Cancel</button></p></div>');
                $('#savetext-modal').modal('show');
            } else {
                formSubmitted = true;
                $('#edit_route').html('<i class="fa fa-spinner fa-spin"></i>');
                if (uploaderIdle) {
                    saveRoute();
                }
            }
        } else {
            var urlErrors = '';
            if (!validRelatedLink) {
                urlErrors = urlErrors + '<p>More info: <b>' + $('#route-related-link').val() + '</b></p>';
            }
            $('#message-modal-label').html('Invalid URL');
            $('#message-modal-body').html('<div style="margin-top: 20px; text-align: left;"><p>You entered the following invalid URL(s)</p><p>' + urlErrors + '</p><p style="text-align: center;"><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">OK</button></p></div>');
            $('#message-modal').modal('show');
        }
    }

    function isUrlValid(url) {
        return /^(https?|s?ftp):\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i.test(url);
    }

    $(document).ready(function() {

        //init with one route step
        addStep();

        $('.route-info-section').on('click', '.remove-photo', function() {
            $(this).closest('li').hide();
            $(this).closest('.qq-upload-list').next('.qq-upload-button-selector').show();
        });

        $('#remove-gpx-file').hide();

        //load elevation values
        {% if route.start_elevation %}
        $('#elevation-start').val(numberWithCommas({{ route.start_elevation }}));
        {% endif %}
        {% if route.elevation_gain %}
        $('#elevation-gain').val(numberWithCommas({{ route.elevation_gain }}));
        {% endif %}

        //pre-select meters?
        {% if not peak.is_usa %}
        $('#elevation-start-m').click();
        $('#elevation-gain-m').click();
        $('#distance-to-summit-km').click();
        setElevationStartMeters();
        setElevationGainMeters();
        setDistanceToSummitKm();
        {% endif %}

        //when click the submit button
        $('#edit_route').on('click', function(e) {
            e.preventDefault();
            //validate form
            validateForm();
        });

        //switch map units
        $("#gm-custom-mapunits").click(function(){
            if ($('#gm-custom-mapunitsbutton-label-meters').hasClass('gm-custom-mapunits-selected')) {
                toggleMapUnits('feet');
                scale.setUnit('imperial');
            } else {
                toggleMapUnits('meters');
                scale.setUnit('metric');
            }
        });

        //Disable scrollZoom
        $('#map-canvas').on('mouseleave', function() {
            map.scrollZoom.disable();
        });

        //Custom Google Map type stuff

        $('#gm-custom-mapbutton').on('mouseenter', function(){
           $('#gm-custom-mapdropdown').show();
        });

        $('#gm-custom-mapbutton').on('mouseleave', function(){
           $('#gm-custom-mapdropdown').hide();
        });

        $('#gm-custom-mapbutton').on('touchstart', function() {
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-satstreets').on('click', function() {
            toggleMapType('satellite');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-satstreets').on('touchstart', function() {
            toggleMapType('satellite');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-satstreets').on('mouseenter', function() {
            $('#gm-custom-mapdropdown').show();
        });

        $('#gm-custom-mapoption-satstreets').on('mouseleave', function() {
            $('#gm-custom-mapdropdown').hide();
        });

        $('#gm-custom-mapoption-topo').on('click', function() {
            toggleMapType('caltopo');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-topo').on('touchstart', function() {
            toggleMapType('caltopo');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-topo').on('mouseenter', function() {
            $('#gm-custom-mapdropdown').show();
        });

        $('#gm-custom-mapoption-topo').on('mouseleave', function() {
            $('#gm-custom-mapdropdown').hide();
        });

        $('#gm-custom-mapoption-sat').on('click', function() {
            toggleMapType('sat_topo');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-sat').on('touchstart', function() {
            toggleMapType('sat_topo');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-sat').on('mouseenter', function() {
            $('#gm-custom-mapdropdown').show();
        });

        $('#gm-custom-mapoption-sat').on('mouseleave', function() {
            $('#gm-custom-mapdropdown').hide();
        });

        $('#gm-custom-mapoption-terrain').on('click', function() {
            toggleMapType('terrain');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-terrain').on('touchstart', function() {
            toggleMapType('terrain');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-terrain').on('mouseenter', function() {
            $('#gm-custom-mapdropdown').show();
        });

        $('#gm-custom-mapoption-terrain').on('mouseleave', function() {
            $('#gm-custom-mapdropdown').hide();
        });

        $('#gm-custom-mapoption-outdoors').on('click', function() {
            toggleMapType('outdoors');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-outdoors').on('touchstart', function() {
            toggleMapType('outdoors');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-outdoors').on('mouseenter', function() {
            $('#gm-custom-mapdropdown').show();
        });

        $('#gm-custom-mapoption-outdoors').on('mouseleave', function() {
            $('#gm-custom-mapdropdown').hide();
        });

        //Natural atlas stuff
        $('#gm-custom-mapoption-natatl').on('click', function() {
            toggleMapType('natural_atlas');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-natatl').on('touchstart', function() {
            toggleMapType('natural_atlas');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-natatl').on('mouseenter', function() {
            $('#gm-custom-mapdropdown').show();
        });

        $('#gm-custom-mapoption-natatl').on('mouseleave', function() {
            $('#gm-custom-mapdropdown').hide();
        });

        $('#route-type-choices').on('change', '.route-type-input', function () {
            if (this.checked) {
                $(this).next().animate({
                    backgroundColor: '#f24100',
                },200);
                $(this).next().css('color','#fff');
            } else {
                $(this).next().animate({
                    backgroundColor: '#e6e6e6',
                },200);
                $(this).next().css('color','#000');
            }
        });

        $('#feature-tag-choices').on('change', '.feature-tag-input', function () {
            if (this.checked) {
                $(this).next().animate({
                    backgroundColor: '#f24100',
                },200);
                $(this).next().css('color','#fff');
            } else {
                $(this).next().animate({
                    backgroundColor: '#e6e6e6',
                },200);
                $(this).next().css('color','#000');
            }
        });

        $('#challenge-tag-choices').on('change', '.challenge-tag-input', function () {
            if (this.checked) {
                $(this).next().animate({
                    backgroundColor: '#f24100',
                },200);
                $(this).next().css('color','#fff');
            } else {
                $(this).next().animate({
                    backgroundColor: '#e6e6e6',
                },200);
                $(this).next().css('color','#000');
            }
        });

        $('#gear-tag-choices').on('change', '.gear-tag-input', function () {
            if (this.checked) {
                $(this).next().animate({
                    backgroundColor: '#f24100',
                },200);
                $(this).next().css('color','#fff');
            } else {
                $(this).next().animate({
                    backgroundColor: '#e6e6e6',
                },200);
                $(this).next().css('color','#000');
            }
        });

        //set metadata checkboxes
        {% if route.route_metadata %}
        var json = "{{ route.route_metadata }}".replace(/&quot;/g,'"');
        var route_metadata = $.parseJSON(json);

        $.each(route_metadata, function (key, data) {
            if (key == 'types') {
                for (var i = 0; i < data.length; i++) {
                    if ($('.route-type-input[value="'+data[i]+'"]').prop('checked') == false) {
                        $('.route-type-input[value="'+data[i]+'"]').prop('checked', true);
                        $('.route-type-input[value="'+data[i]+'"]').next().animate({
                            backgroundColor: '#f24100',
                        },200);
                        $('.route-type-input[value="'+data[i]+'"]').next().css('color','#fff');
                    } else {
                        var new_type = data[i];
                        var numTypes = $(".user-added-type").length;
                        var typeId = '#user-type-'+numTypes;
                        $("#route-type-choices").append('<div class="toggle-switch"><input id="user-type-'+numTypes+'" class="route-type-input user-added-type" type="checkbox" name="chkRouteTypes" value="'+new_type+'"><label>'+new_type+'</label></div>');
                        $("#user-type-"+numTypes).prop('checked', true);
                        $("#user-type-"+numTypes).next().animate({
                            backgroundColor: '#f24100',
                        },200);
                        $("#user-type-"+numTypes).next().css('color','#fff');
                    }
                }
            }
            if (key == 'gear') {
                for (var i = 0; i < data.length; i++) {
                    if ($('.gear-tag-input[value="'+data[i]+'"]').prop('checked') == false) {
                        $('.gear-tag-input[value="' + data[i] + '"]').prop('checked', true);
                        $('.gear-tag-input[value="' + data[i] + '"]').next().animate({
                            backgroundColor: '#f24100',
                        },200);
                        $('.gear-tag-input[value="' + data[i] + '"]').next().css('color','#fff');
                    } else {
                        var new_gear = data[i];
                        var numGear = $(".user-added-gear").length;
                        var gearId = '#user-gear-'+numGear;
                        $("#gear-tag-choices").append('<div class="toggle-switch"><input id="user-gear-'+numGear+'" class="gear-tag-input user-added-gear" type="checkbox" name="chkGearTags" value="'+new_gear+'"><label>'+new_gear+'</label></div>');
                        $("#user-gear-"+numGear).prop('checked', true);
                        $("#user-gear-"+numGear).next().animate({
                            backgroundColor: '#f24100',
                        },200);
                        $("#user-gear-"+numGear).next().css('color','#fff');
                    }
                }
            }
            if (key == 'features') {
                for (var i = 0; i < data.length; i++) {
                    if ($('.feature-tag-input[value="'+data[i]+'"]').prop('checked') == false) {
                        $('.feature-tag-input[value="' + data[i] + '"]').prop('checked', true);
                        $('.feature-tag-input[value="' + data[i] + '"]').next().animate({
                            backgroundColor: '#f24100',
                        },200);
                        $('.feature-tag-input[value="' + data[i] + '"]').next().css('color','#fff');
                    } else {
                        var new_feature = data[i];
                        var numFeatures = $(".user-added-feature").length;
                        var featureId = '#user-feature-'+numFeatures;
                        $("#feature-tag-choices").append('<div class="toggle-switch"><input id="user-feature-'+numFeatures+'" class="feature-tag-input user-added-feature" type="checkbox" name="chkRouteFeatures" value="'+new_feature+'"><label>'+new_feature+'</label></div>');
                        $("#user-feature-"+numFeatures).prop('checked', true);
                        $("#user-feature-"+numFeatures).next().animate({
                            backgroundColor: '#f24100',
                        },200);
                        $("#user-feature-"+numFeatures).next().css('color','#fff');
                    }
                }
            }
            if (key == 'challenges') {
                for (var i = 0; i < data.length; i++) {
                    if ($('.challenge-tag-input[value="'+data[i]+'"]').prop('checked') == false) {
                        $('.challenge-tag-input[value="' + data[i] + '"]').prop('checked', true);
                        $('.challenge-tag-input[value="' + data[i] + '"]').next().animate({
                            backgroundColor: '#f24100',
                        },200);
                        $('.challenge-tag-input[value="' + data[i] + '"]').next().css('color','#fff');
                    } else {
                        var new_challenge = data[i];
                        var numChallenges = $(".user-added-challenge").length;
                        var challengeId = '#user-challenge-'+numChallenges;
                        $("#challenge-tag-choices").append('<div class="toggle-switch"><input id="user-challenge-'+numChallenges+'" class="challenge-tag-input user-added-challenge" type="checkbox" name="chkRouteFeatures" value="'+new_challenge+'"><label>'+new_challenge+'</label></div>');
                        $("#user-challenge-"+numChallenges).prop('checked', true);
                        $("#user-challenge-"+numChallenges).next().animate({
                            backgroundColor: '#f24100',
                        },200);
                        $("#user-challenge-"+numChallenges).next().css('color','#fff');
                    }
                }
            }
        });
        {% endif %}

        //set difficulty
         {% if route.difficulty %}
            $('#difficulty_rating_container').css('background-color','#fff');
            $("#difficulty-rating").val('{{ route.difficulty }}');
        {% else %}
            $("#difficulty-rating").val($("#difficulty-rating option:first").val());
            $('#difficulty_rating_container').css('background-color','#fff');
        {% endif %}

        //add related links
        {% for l in related_links %}
        addRelatedLink('{{ l.related_url }}');
        {% endfor %}

        var gpxSlider = document.getElementById('gpx-cropper');

        noUiSlider.create(gpxSlider, {
            {% if route.gpx_start_index or route.gpx_end_index %}
            start: [ {{ route.gpx_start_index }}, {{ route.gpx_end_index }} ],
            {% else %}
            start: [ 0, 500 ],
            {% endif %}
            step: 1,
            connect: true,
            behaviour: 'drag',
            range: {
                'min': [ 0 ],
                'max': [ 500 ]
            }
        });

        gpxSlider.noUiSlider.on('set', function(values, handle, unencoded, tap, positions){
            //console.log(unencoded[0]);
            //console.log(unencoded[1]);

            var startIndex = unencoded[0];
            var endIndex = unencoded[1];

            if (startIndex > 0 || endIndex < 500) {

                requestIndex = Math.floor((Math.random() * 1000000000));
                $("#distance-to-summit").val('');
                $("#elevation-start").val('');
                $("#elevation-gain").val('');
                $.post('{% url "process_route_gpx" peak.id %}', { gpx_file: gpx_basefile, start_index: startIndex, end_index: endIndex, request_index: requestIndex},
                    function(response){
                        var responseJSON = $.parseJSON(response);
                        var success = responseJSON.success;
                        if (success == 'true') {
                            if (requestIndex == responseJSON.request_index) {
                                var distanceToSummit = parseFloat(responseJSON.length_2d).toFixed(1);
                                if (distanceToSummit > 0) {
                                    $("#distance-to-summit").val(parseFloat(responseJSON.length_2d).toFixed(1));
                                    $("#init-distance-to-summit").val(parseFloat(responseJSON.length_2d).toFixed(1));
                                    if ($('#distance-to-summit-km').is(':checked')) {
                                        setDistanceToSummitKm();
                                    }
                                }
                                var elevationStart = parseFloat(responseJSON.start_elevation);
                                if (elevationStart > 0) {
                                    $("#elevation-start").val(numberWithCommas(responseJSON.start_elevation));
                                    if ($('#elevation-start-m').is(':checked')) {
                                        setElevationStartMeters();
                                    }
                                }
                                var elevationGain = parseFloat(responseJSON.uphill);
                                if (elevationGain > 0) {
                                    $("#elevation-gain").val(numberWithCommas(responseJSON.uphill));
                                    if ($('#elevation-gain-m').is(':checked')) {
                                        setElevationGainMeters();
                                    }
                                }
                            }
                        }
                });

            }

        });

        gpxSlider.noUiSlider.on('slide', function(values, handle, unencoded, tap, positions){
            if (uploaderIdle) {
                $('.edit_route').prop('disabled', false);
            }
            $("#gpx-start-index").val(unencoded[0]);
            var startStep = unencoded[0];
            $("#gpx-end-index").val(unencoded[1]);
            var endStep = unencoded[1];
            var totalPoints = points.length;
            var pointStep = totalPoints / 500;
            var newPoints = [];
            linePoints = [];
            bounds = null;
            bounds = new mapboxgl.LngLatBounds();
            var new_center = null;
            for (var i = 0; i < totalPoints; i++) {
                if (i >= (pointStep * startStep) && i <= (pointStep * endStep)) {
                    if (new_center == null) {
                        new_center = points[i];
                    }
                    if (handle == 1) {
                        new_center = points[i];
                    }
                    newPoints.push(points[i]);
                    linePoints.push([points[i].lng, points[i].lat]);
                    bounds.extend(points[i]);
                }
            }

            lineData = {
                "type": "Feature",
                "properties": {},
                "geometry": {
                    "type": "LineString",
                    "coordinates": linePoints
                }
            }
            map.getSource('route-data').setData(lineData);

            var new_start_index = parseInt(pointStep * startStep);
            var new_end_index = parseInt(pointStep * endStep);
            if (new_end_index >= raw_points.length) {
                new_end_index = raw_points.length - 1;
            }
            $("#gpx-start-lat").val(raw_points[new_start_index][0]);
            $("#gpx-start-lon").val(raw_points[new_start_index][1]);
            $("#gpx-end-lat").val(raw_points[new_end_index][0]);
            $("#gpx-end-lon").val(raw_points[new_end_index][1]);

        });

        $("#btnAddRouteType").click( function() {
            var new_type = $("#txtAddRouteType").val();
            if (new_type != '') {
                var numTypes = $(".user-added-type").length;
                var typeId = '#user-type-'+numTypes;
                $("#route-type-choices").append('<div class="toggle-switch"><input id="user-type-'+numTypes+'" class="route-type-input user-added-type" type="checkbox" name="chkRouteTypes" value="'+new_type+'"><label>'+new_type+'</label></div>');
                $("#user-type-"+numTypes).prop('checked', true);
                $("#user-type-"+numTypes).next().animate({
                    backgroundColor: '#f24100',
                },200);
                $("#user-type-"+numTypes).next().css('color','#fff');
                $("#txtAddRouteType").val('');
                if (uploaderIdle) {
                    $('.edit_route').prop('disabled', false);
                }
            }
            return false;
        });

        $("#btnAddRouteFeature").click( function() {
            var new_feature = $("#txtAddRouteFeature").val();
            if (new_feature != '') {
                var numFeatures = $(".user-added-feature").length;
                var featureId = '#user-feature-'+numFeatures;
                $("#feature-tag-choices").append('<div class="toggle-switch"><input id="user-feature-'+numFeatures+'" class="feature-tag-input user-added-feature" type="checkbox" name="chkRouteFeatures" value="'+new_feature+'"><label>'+new_feature+'</label></div>');
                $("#user-feature-"+numFeatures).prop('checked', true);
                $("#user-feature-"+numFeatures).next().animate({
                    backgroundColor: '#f24100',
                },200);
                $("#user-feature-"+numFeatures).next().css('color','#fff');
                $("#txtAddRouteFeature").val('');
                if (uploaderIdle) {
                    $('.edit_route').prop('disabled', false);
                }
            }
            return false;
        });

        $("#btnAddRouteChallenge").click( function() {
            var new_challenge = $("#txtAddChallengeTag").val();
            if (new_challenge != '') {
                var numChallenges = $(".user-added-challenge").length;
                var challengeId = '#user-challenge-'+numChallenges;
                $("#challenge-tag-choices").append('<div class="toggle-switch"><input id="user-challenge-'+numChallenges+'" class="challenge-tag-input user-added-challenge" type="checkbox" name="chkRouteChallenges" value="'+new_challenge+'"><label>'+new_challenge+'</label></div>');
                $("#user-challenge-"+numChallenges).prop('checked', true);
                $("#user-challenge-"+numChallenges).next().animate({
                    backgroundColor: '#f24100',
                },200);
                $("#user-challenge-"+numChallenges).next().css('color','#fff');
                $("#txtAddChallengeTag").val('');
                if (uploaderIdle) {
                    $('.edit_route').prop('disabled', false);
                }
            }
            return false;
        });

        $("#btnAddGearTag").click( function() {
            var new_gear = $("#txtAddGearTag").val();
            if (new_gear != '') {
                var numGear = $(".user-added-gear").length;
                var gearId = '#user-gear-'+numGear;
                $("#gear-tag-choices").append('<div class="toggle-switch"><input id="user-gear-'+numGear+'" class="gear-tag-input user-added-gear" type="checkbox" name="chkGearTags" value="'+new_gear+'"><label>'+new_gear+'</label></div>');
                $("#user-gear-"+numGear).prop('checked', true);
                $("#user-gear-"+numGear).next().animate({
                    backgroundColor: '#f24100',
                },200);
                $("#user-gear-"+numGear).next().css('color','#fff');
                $("#txtAddGearTag").val('');
                if (uploaderIdle) {
                    $('.edit_route').prop('disabled', false);
                }
            }
            return false;
        });

        $("#btnAddRelatedLink").click( function() {
            if ($("#route-related-link").val() != '') {
                var validRelatedLink = isUrlValid($('#route-related-link').val());
                if (validRelatedLink) {
                    var link_url = $("#route-related-link").val();
                    var numLinks = $(".related-links").length;
                    $("#route-related-links").append('<div class="related-links" id="related-link-' + numLinks + '" style="width: 98%; background-color: #f0f0f0; display: inline-block; height: 100px; margin-right: 20px; margin-bottom: 20px;"><p><span id="related-link-url-' + numLinks + '" style="display: inline-block; margin-left: 20px; font-weight: 500; line-height: 110px;">' + link_url + '</span><span class="pull-right" style="padding-top: 35px; padding-right: 20px; font-size: 30px; color: #cccccc;"><a style="color: #ccc;" class="ajax-link" onclick="removeRelatedLink(\'' + numLinks + '\');"><i class="fa fa-times"></i></a></span></p></div>');
                    var current_links = $("#route_related_links").val();
                    $("#route_related_links").val(current_links + '|' + link_url + '|,');
                    $("#route-related-link").val('');
                    if (uploaderIdle) {
                        $('.edit_route').prop('disabled', false);
                    }
                } else {
                    var urlErrors = '<p>More info: <b>' + $('#route-related-link').val() + '</b></p>';
                    $('#message-modal-label').html('Invalid URL');
                    $('#message-modal-body').html('<div style="margin-top: 20px; text-align: left;"><p>You entered the following invalid URL</p><p>' + urlErrors + '</p><p style="text-align: center;"><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">OK</button></p></div>');
                    $('#message-modal').modal('show');
                }
            }
            return false;
        });

        function addRelatedLink(link_url) {
            var numLinks = $(".related-links").length;
            $("#route-related-links").append('<div class="related-links" id="related-link-'+numLinks+'" style="width: 98%; background-color: #f0f0f0; display: inline-block; height: 100px; margin-right: 20px; margin-bottom: 20px;"><p><span id="related-link-url-'+numLinks+'" style="display: inline-block; margin-left: 20px; font-weight: 500; line-height: 100px;">'+link_url+'</span><span class="pull-right" style="padding-top: 35px; padding-right: 20px; font-size: 30px; color: #cccccc;"><a style="color: #ccc;" class="ajax-link" onclick="removeRelatedLink(\''+numLinks+'\');"><i class="fa fa-times"></i></a></span></p></div>');
            var current_links = $("#route_related_links").val();
            $("#route_related_links").val(current_links+'|'+link_url+'|,');
        }

        /*
         * Step 2 javascripts
         */
        $(document).bind('close.facebox', function() {location.reload(true) });
        $('#step2').facebox();
        //$('input[title!=""]').hint();
        //$('a#skip2').click(function(){ $("a#step2").trigger('click') });
        $("#date").datepicker({ dateFormat: 'yy-mm-dd', changeMonth: true, changeYear: true, yearRange: "1950:{% now "Y" %}",
            onSelect: function(dateText, inst) {
                isFutureDate(dateText);
            }
        });
        $("textarea").click(function(){ reset_textareas($(this)) });

        $('.add_form').submit(function() {
            $("div#upload-ajax-spinner").show();
            var d = $("input#date").val();
            if(d!=''){
                if(isFutureDate(d)){
                    return false;
                }
            }
            $("textarea").each(function(k,v){reset_textareas($(v))});
            $(this).ajaxSubmit({
                target: '#output',
                success:    function(e) {
                    //fileuploader.js is causing the conflict:
                    if(e.indexOf("True") >= 0){
                        $("a#step2").trigger('click');
                    }else{
                        //error from django
                    }
                }
            });
            return false;
        });

        var gpxuploader = new qq.s3.FineUploader({
            debug: false,
            multiple: false,
            element: document.getElementById('gpx-file-1'),
            template: 'qq-gpx-template',
            request: {
                endpoint: 'https://peakery-media.s3.amazonaws.com',
                accessKey: 'AKIAJPWBM4YZXFHWBTPQ'
            },
            signature: {
                endpoint: '{% url "s3signature" %}'
            },
            uploadSuccess: {
                endpoint: '{% url "s3_route_gpx_upload" %}',
                params: {
                    'route_id': '{{ route.id }}'
                }
            },
            iframeSupport: {
                localBlankPagePath: '/api/s3blank/'
            },
            retry: {
               enableAuto: false // defaults to false
            },
            validation: {
                acceptFiles: ['.gpx'],
                allowedExtensions: ['gpx'],
                acceptType: '.gpx'
            },
            button: document.getElementById('gpx-upload-button'),
            text: {
                fileInputTitle: 'Choose file(s)'
            },
            callbacks: {
                onSubmitted: function(id, name) {
                    $('#gpx-upload-button-div').prop('disabled', true);
                    $('#gpx-upload-button-div').html('<i style="font-size: 22px;" class="fa fa-spinner fa-2x fa-spin" aria-hidden="true"></i>');
                    if (uploaderIdle) {
                        $('#add_peak2').prop('disabled', false);
                    }
                },
                onComplete: function(id, name, responseJSON, maybeXhr) {
                    if (responseJSON.gpx_file != '' && responseJSON.valid_file == 'true') {
                        $("#gpx-file-1").show();
                        $("#map-canvas").height(400);
                        $(".summitLogGpx").hide();
                        $('#remove-gpx-file').show();
                        set_gpx('gpx-file-1', responseJSON);
                        jailai("gpx-file-1");
                        $('#gpx-upload-button-div').html('Choose file(s)');
                        $('#gpx-upload-button-div').prop('disabled', false);
                    }
                    else {
                        //alert("Sorry, that is not a valid GPX file");
                        $('#message-modal-label').html('Error');
                        $('#message-modal-body').html('Sorry, that is not a valid GPX file. GPX files must include location, time and elevation data.');
                        $('#message-modal').modal('show');
                        reset_gpx_spinner('#gpx-file-1');
                        $('#gpx-upload-button-div').html('Choose file(s)');
                        $('#gpx-upload-button-div').prop('disabled', false);
                    }
                }
            },
            messages: {
                typeError: "Invalid file type. Please add only GPX files.",
                sizeError: "File is too large, maximum file size is 12MB.",
                minSizeError: "{file} is too small, minimum file size is {minSizeLimit}.",
                emptyError: "{file} is empty, please select files again without it.",
                allowedExtensionsError : "{file} is not allowed.",
                onLeave: "The files are being uploaded, if you leave now the upload will be cancelled."
            },
            showMessage: function (message) {
                $('#message-modal-label').html('Error');
                $('#message-modal-body').html(message);
                $('#message-modal').modal('show');
            },
            objectProperties: {
                acl: 'public-read',
                key: function (fileId) {

                    var filename = gpxuploader.getName(fileId);
                    var uuid = gpxuploader.getUuid(fileId);
                    var ext = filename.substr(filename.lastIndexOf('.') + 1).toLowerCase();

                    return  'gpx/{{ peak.slug_new_text }}_' + uuid + '.' + ext;

                }
            }
        });

        $("textarea[id^='file-']").change(function(){
            photo_id = $(this).attr('title');
            if (photo_id!=''){
                caption = $(this).val();
                $.get('{% url "summit_add_photo_caption" %}', {photo_id:photo_id, caption:caption}, function(data){})
            }
        });

        $("input[id^='set-main-']").click(function(){
            var checked = $(this).is(":checked");
            var peak_id = $(this).val();
            var slot_id = $(this).attr('id').replace('set-main-','');
            var photo_id = $("textarea#file-"+slot_id).attr('title');
            $.get('{% url "set_as_peak_thumbnail"  %}',
                    {peak_id:peak_id, photo_id:photo_id, checked:checked},
                    function(data){
                        response = parseInt(data);
                        if(data==1){}else{}
                    });
        });

        //add steps
        {% for s in route_steps %}
        if('{{ s.image }}' != '') {
            {% if s.description %}
            $("#step-{{ s.step_number }}-details").html('{{ s.description }}');
            {% endif %}
            $("#file-{{ s.step_number }}").show();
            //$("div#file-{{ s.step_number }}").html("<img style='width: 360px; height: 240px; margin-left: -30px;' src='{{ S3_MEDIA_URL }}{{ s.image }}'>");
            $("div#file-{{ s.step_number }}").html('<div class="top-photos"><div><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div><div class="remove-photo" onclick="confirmRemovePhoto(\'file-{{ s.step_number }}\');"><i style="text-shadow: 0px 0px 5px #ccc;" class="fa fa-times" aria-hidden="true"></i></div>');
            $("div#file-{{ s.step_number }}").css("background-image", "url('{{ S3_MEDIA_URL }}{{ s.image }}')");
            $("li.file-{{ s.step_number }}").show();
            $("#route-step-file-{{ s.step_number }}").val("{{ s.image }}");
        } else {
            initStep({{ s.step_number }});
            {% if s.description %}
            $("#step-{{ s.step_number }}-details").html('{{ s.description }}');
            {% endif %}
            $("li.file-{{ s.step_number }}").show();
        }
        {% endfor %}

        /*
         * Step 3 javascripts
         */
        $("h4.routes-companion-info").click(function(){
            $("div.routes-companion-info").slideToggle();
            //if ($(this).hasClass("open")){$(this).removeClass("open")}else{$(this).addClass("open")}
        });

        var availableRoutes = [ {% for r in routes %}"{{ r.name }}"{% if not forloop.last %},{% endif %}{% endfor %}];
        $( "#route_up" ).autocomplete({source: availableRoutes});
        $( "#route_down" ).autocomplete({source: availableRoutes});

        $("span#same-route").click(function(){
            var up = $("input#route_up").val();
            if (up!=""){
                $("input#route_down").val(up);
                $("input#route_down").removeClass("blur");
            }
        });

        $("#route_up").change(function(){
            if ($(this).val() == 'add_new_route') {
                window.location.href = "/peaks/{{ peak.id }}/routes/add/";
            }
        });

        $("div#more").click(function(){
            index = parseInt( $("input#total_fellows").val() );
            cloned = $("#fellow-X").clone();
            cloned.find("input[id='fellow']").attr("name", "fellow-"+index).val("").hint();
            cloned.find("input[id='email']").attr("name", "email-"+index).val("").hint();
            cloned.show();
            $("div#more-fellows").append(cloned);
            index = index + 1;
            $("input#total_fellows").val(index)
        });

        $('#remove-gpx-file').on('click', function() {
            removeGPX();
        });

        $('#route-name').on('input', function() {
            if (uploaderIdle) {
                $('.edit_route').prop('disabled', false);
            }
        });

        $('#route-type-choices').find('input').on('change', function() {
            if (uploaderIdle) {
                $('.edit_route').prop('disabled', false);
            }
        });

        $('#difficulty-rating').on('change', function() {
            if (uploaderIdle) {
                $('.edit_route').prop('disabled', false);
            }
            if ($('#difficulty-rating').val() != '') {
                $('#difficulty_rating_container').css('background-color','#fff');
            } else {
                $('#difficulty_rating_container').css('background-color','#fff');
            }
        });

        $('#start-location').on('input', function() {
            if (uploaderIdle) {
                $('.edit_route').prop('disabled', false);
            }
        });

        $('#elevation-start').on('input', function() {
            if (uploaderIdle) {
                $('.edit_route').prop('disabled', false);
            }
        });

        $('#distance-to-summit').on('input', function() {
            if (uploaderIdle) {
                $('.edit_route').prop('disabled', false);
            }
        });

        $('#elevation-gain').on('input', function() {
            if (uploaderIdle) {
                $('.edit_route').prop('disabled', false);
            }
        });

        $('.route-step-text').on('input', function() {
            if (uploaderIdle) {
                $('.edit_route').prop('disabled', false);
            }
        });

        $('#feature-tag-choices').find('input').on('change', function() {
            if (uploaderIdle) {
                $('.edit_route').prop('disabled', false);
            }
        });

        $('#challenge-tag-choices').find('input').on('change', function() {
            if (uploaderIdle) {
                $('.edit_route').prop('disabled', false);
            }
        });

        $('#gear-tag-choices').find('input').on('change', function() {
            if (uploaderIdle) {
                $('.edit_route').prop('disabled', false);
            }
        });

        $('#getting-there').on('input', function() {
            if (uploaderIdle) {
                $('.edit_route').prop('disabled', false);
            }
        });

        $('#red-tape').on('input', function() {
            if (uploaderIdle) {
                $('.edit_route').prop('disabled', false);
            }
        });

        $("#elevation-start-m").on('change', function() {
            setElevationStartMeters();
        });

        $("#elevation-start-ft").on('change', function() {
            setElevationStartFeet();
        });

        $("#elevation-gain-m").on('change', function() {
            setElevationGainMeters();
        });

        $("#elevation-gain-ft").on('change', function() {
            setElevationGainFeet();
        });

        $("#distance-to-summit-km").on('change', function() {
            setDistanceToSummitKm();
        });

        $("#distance-to-summit-miles").on('change', function() {
            setDistanceToSummitMiles();
        });

        $(document).on("keydown", ":input:not(textarea)", function(event) {
            if (event.keyCode == 13) {
                return false;
            } else {
                return event.keyCode;
            }
        });

    });

    function set_gpx(where, responseJSON){

        var valid_file = responseJSON.valid_file;

        if (valid_file == 'true') {

            gpx_url = responseJSON.gpx_file;
            gpx_basefile = responseJSON.gpx_filename;

            var distanceToSummit = parseFloat(responseJSON.length_2d).toFixed(1);
            if (distanceToSummit > 0) {
                $("#distance-to-summit").val(parseFloat(responseJSON.length_2d).toFixed(1));
                $("#init-distance-to-summit").val(parseFloat(responseJSON.length_2d).toFixed(1));
                if ($('#distance-to-summit-km').is(':checked')) {
                    setDistanceToSummitKm();
                }
            }

            var elevationStart = parseFloat(responseJSON.start_elevation);
            if (elevationStart > 0) {
                $("#elevation-start").val(numberWithCommas(responseJSON.start_elevation));
                if ($('#elevation-start-m').is(':checked')) {
                    setElevationStartMeters();
                }
            }

            var elevationGain = parseFloat(responseJSON.uphill);
            if (elevationGain > 0) {
                $("#elevation-gain").val(numberWithCommas(responseJSON.uphill));
                if ($('#elevation-gain-m').is(':checked')) {
                    setElevationGainMeters();
                }
            }

            $("#time-to-summit-hrs").val(responseJSON.total_hours);
            $("#time-to-summit-min").val(responseJSON.total_minutes);
            $("#total-trip-time-hrs").val(responseJSON.total_hours);
            $("#total-trip-time-min").val(responseJSON.total_minutes);
            $("#gpx-start-index").val(responseJSON.start_index);
            $("#gpx-end-index").val(responseJSON.end_index);
            $("#gpx-start-lat").val(responseJSON.start_lat);
            $("#gpx-start-lon").val(responseJSON.start_lon);
            $("#gpx-end-lat").val(responseJSON.end_lat);
            $("#gpx-end-lon").val(responseJSON.end_lon);
            $("#gpx-file").val(responseJSON.gpx_filename);
            $("#map-canvas").show();
            $("#map-canvas").height(400);
            if (map == null) {
                var latLng = new mapboxgl.LngLat({{ peak.long }}, {{ peak.lat }});
                map = new mapboxgl.Map({
                    container: mapDiv, // HTML container id
                    style: mapStyle, // style URL
                    center: latLng, // starting position as [lng, lat]
                    zoom: 14,
                    scrollZoom: false
                });
                //add peak marker
                iconstyle = 'marker_icon_peak';
                //create an HTML element for the marker
                var el = document.createElement('div');
                el.className = iconstyle;
                var marker = new mapboxgl.Marker(el)
                    .setLngLat(latLng)
                    .setOffset([-5, -10])
                    .setDraggable(false)
                    .addTo(map);

                function calculateCenter() {
                    center = map.getCenter();
                }

                map.on('load', function () {
                    calculateCenter();
                    var new_center = null;
                    $.ajax({
                        type: "GET",
                        url: gpx_url,
                        dataType: "xml",
                        success: function (xml) {
                            bounds = new mapboxgl.LngLatBounds();
                            $(xml).find("trkpt").each(function () {

                                if ($("#gpx-start-lat").val() == '') {
                                    $("#gpx-start-lat").val($(this).attr("lat"));
                                }
                                if ($("#gpx-start-lon").val() == '') {
                                    $("#gpx-start-lon").val($(this).attr("lon"));
                                }
                                $("#gpx-end-lat").val($(this).attr("lat"));
                                $("#gpx-end-lon").val($(this).attr("lon"));

                                var lat = $(this).attr("lat");
                                var lon = $(this).attr("lon");
                                var p = new mapboxgl.LngLat(lon, lat);
                                points.push(p);
                                linePoints.push([p.lng, p.lat]);
                                if (new_center == null) {
                                    new_center = p;
                                }
                                raw_points.push([lat, lon]);
                                bounds.extend(p);
                            });
                            $(xml).find("rtept").each(function () {

                                if ($("#gpx-start-lat").val() == '') {
                                    $("#gpx-start-lat").val($(this).attr("lat"));
                                }
                                if ($("#gpx-start-lon").val() == '') {
                                    $("#gpx-start-lon").val($(this).attr("lon"));
                                }
                                $("#gpx-end-lat").val($(this).attr("lat"));
                                $("#gpx-end-lon").val($(this).attr("lon"));

                                var lat = $(this).attr("lat");
                                var lon = $(this).attr("lon");
                                var p = new mapboxgl.LngLat(lon, lat);
                                points.push(p);
                                linePoints.push([p.lng, p.lat]);
                                if (new_center == null) {
                                    new_center = p;
                                }
                                raw_points.push([lat, lon]);
                                bounds.extend(p);
                            });

                            lineData = {
                                "type": "Feature",
                                "properties": {},
                                "geometry": {
                                    "type": "LineString",
                                    "coordinates": linePoints
                                }
                            }

                            map.addSource('route-data', {
                                type: 'geojson',
                                data: lineData
                            });

                            map.addLayer({
                                "id": "route-layer",
                                "type": "line",
                                "source": "route-data",
                                "layout": {
                                    "line-join": "round",
                                    "line-cap": "round"
                                },
                                "paint": {
                                    "line-color": "#fc202e",
                                    "line-width": 4
                                }
                            });

                            // fit bounds to track
                            map.fitBounds(bounds, {padding: 50});

                            $("#gpx-start-index").val('0');
                            $("#gpx-end-index").val('500');
                            var gpxSlider = document.getElementById('gpx-cropper');
                            gpxSlider.noUiSlider.set([0, 500]);

                            $("#gpx-cropper-container").show();
                            $("#gpx-cropper-info").show();
                        }
                    });
                    setMapControls();
                });

                map.on('resize', function () {
                    map.setCenter(center);
                });

                map.on('click', function () {
                    map.scrollZoom.enable();
                });

            }
        } else {
            //alert('Sorry, errors found in GPX file.');
            $('#message-modal-label').html('Error');
            $('#message-modal-body').html('Sorry, errors found in GPX file');
            $('#message-modal').modal('show');
        }

    }

    function setMapControls() {
        var check = checkIfMapboxStyleIsLoaded();
        if (!check) {
            // It's not safe to manipulate layers yet, so wait 200ms and then check again
            setTimeout(function() {
                setMapControls();
            }, 200);
            return;
        }
        // Whew, now it's safe to manipulate layers!
        var mapUnits = readCookie('map_units');
        if (mapUnits == 'meters') {
            toggleMapUnits(mapUnits);
        }
    }

    function addExtraMapLayers() {
        var check = checkIfMapboxStyleIsLoaded();
        if (!check) {
            // It's not safe to manipulate layers yet, so wait 200ms and then check again
            setTimeout(function() {
                addExtraMapLayers();
            }, 200);
            return;
        }
        // Whew, now it's safe to manipulate layers!
        if (linePoints.length > 0) {
            addPolyline();
        }
    }

    function addPolyline() {
        map.addSource('route-data', {
            type: 'geojson',
            data: lineData
        });
        map.addLayer({
            "id": "route-layer",
            "type": "line",
            "source": "route-data",
            "layout": {
                "line-join": "round",
                "line-cap": "round"
            },
            "paint": {
                "line-color": "#fc202e",
                "line-width": 4
            }
        });
    }

    function set_image(where, responseJSON){
        image_url = responseJSON["image"];
        image_filename = responseJSON["image_filename"];
        img = "<img style='width: 360px; height: 240px; margin-left: -30px;' src='"+image_url+"' >";
        div_html = '<div class="top-photos"><div><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div><div class="remove-photo" onclick="confirmRemovePhoto(\'' + where + '\');"><i style="text-shadow: 0px 0px 5px #ccc;" class="fa fa-times" aria-hidden="true"></i></div>';
        //$("div#"+where).html(div_html);
        //$("div#"+where).css("background-image","url('"+image_url+"')");
        $("textarea#"+where).attr('title',responseJSON["photo_id"]);
        $("#route-step-"+where).val(image_filename);
    }

    function reset_textareas(selector){
        var value = $(selector).val();
        if (value=='full details of your trip...' || value=='write a caption...'){$(selector).val("")}
        $(selector).removeClass("blur");
    }

    function jailai(selector){
        $("textarea#"+selector).effect("highlight", {}, 3000);
    }

    function reset_spinner(selector){
        $(selector).find('div.qq-upload-button').removeClass('qq-upload-loading');
        $(selector).find('div.qq-upload-button').children('div').hide();
    };

    function reset_gpx_spinner(selector){
        $(selector).find('div.qq-upload-button').removeClass('qq-upload-loading');
        $(selector).find('div.qq-upload-button').children('div').hide();
    };

    function insertStep(after_step) {
        totalSteps++;
        var step_number = totalSteps.toString();

        //add a hidden element to store the image filename
        $('#route-step-files').append('<input class="route-step-file" type="hidden" id="route-step-file-'+step_number+'" name="route-step-file-'+step_number+'" value="">');

        //add element
        $('.file-'+after_step).after('<li class="route-step file-'+step_number+'" style="margin-bottom: 20px; width: 100%;"><div style="display: flex; display: -webkit-flex;"><div class="summitlog caption textareaContainer" style="margin-top: 0px; position: relative; align-content: stretch; width: 100%; width: 100% !important; margin-top: 0px !important;"><textarea maxlength="1000" class="route-step-text" name="step-'+step_number+'-details" id="step-'+step_number+'-details" style="resize: none; border: 1px solid #99999F; width: 100% !important; height: 100% !important; padding-top: 16px; color: #333 !important; position: absolute;" placeholder="Step details..."></textarea><div class="remove-step" onclick="confirmRemoveStep(\'file-'+step_number+'\');"><i class="fa fa-times" aria-hidden="true"></i></div></div><div class="route-step-photo" id="file-'+step_number+'"></div></div><div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="text-align: center;margin-top: 20px;"><a class="insert-step" id="insert-'+step_number+'" style="display: none; cursor: pointer;" onclick="insertStep('+step_number+');">add step in between</a></div></li>');

        var where = 'file-'+step_number;
        var uploader1 = new qq.s3.FineUploader({
            debug: false,
            multiple: false,
            element: document.getElementById(where),
            template: 'qq-image-template',
            request: {
                endpoint: 'https://peakery-media.s3.amazonaws.com',
                accessKey: 'AKIAJPWBM4YZXFHWBTPQ'
            },
            signature: {
                endpoint: '{% url "s3signature" %}'
            },
            uploadSuccess: {
                endpoint: '{% url "s3_route_photo_upload" %}',
                params: {
                    'route_id': '{{ route.id }}',
                    'step_number': step_number
                }
            },
            iframeSupport: {
                localBlankPagePath: '/api/s3blank/'
            },
            retry: {
               enableAuto: false // defaults to false
            },
            validation: {
                acceptFiles: ['image/jpg, image/jpeg, .jpg, .jpeg'],
                allowedExtensions: ['jpg', 'jpeg'],
                sizeLimit: 10000000,
                image: {
                    minHeight: 1000,
                    minWidth: 1000
                }
            },
            messages: {
                typeError: 'Sorry, must be a JPG file.',
                sizeError: 'Sorry, this file is too big. Must be under 10 MB.',
                minHeightImageError: 'Sorry, this photo is too small. Must be at least 1000 x 1000 pixels.',
                minWidthImageError: 'Sorry, this photo is too small. Must be at least 1000 x 1000 pixels.'
            },
            showMessage: function (message) {
                $('#message-modal-label').html('Error');
                $('#message-modal-body').html(message);
                $('#message-modal').modal('show');
            },
            text: {
                fileInputTitle: 'Choose file(s)'
            },
            callbacks: {
                onSubmit: function(id, name) {},
                onSubmitted: function(id, name) {
                    $("#"+where).find('div.qq-upload-button-selector').hide();
                    $("li.file-"+step_number).find("div.caption, div.checker").show();
                    uploaderIdle = false;
                    $('.edit_route').prop('disabled', false);
                    $('#route-step-file-'+step_number).data('status', 'pending');
                },
                onComplete: function(id, name, responseJSON, maybeXhr) {
                    $("#"+where).show();
                    set_image(where, responseJSON);
                    jailai(where);
                    $('.edit_route').prop('disabled', false);
                },
                onAllComplete: function(successful, failed) {
                    //update status for this file
                    $('#route-step-file-'+step_number).data('status', 'complete');
                    //check status of all added steps to see if any pending uploads
                    var pendingUploads = false;
                    $('.route-step-file').each(function(i, obj) {
                        if ($(this).data('status') == 'pending') {
                            pendingUploads = true;
                        }
                    });
                    if (!pendingUploads) {
                        uploaderIdle = true;
                        if (formSubmitted) {
                            saveRoute();
                        }
                    }
                },
                onCancel: function(id, name) {},
                onUpload: function(id, name) {},
                onUploadChunk: function(id, name, chunkData) {},
                onUploadChunkSuccess: function(id, chunkData, responseJSON, xhr) {},
                onResume: function(id, fileName, chunkData) {},
                onProgress: function(id, name, loaded, total) {},
                onTotalProgress: function(loaded, total) {},
                onError: function(id, name, reason, maybeXhrOrXdr) {
                    $('#message-modal-label').html('Error');
                    $('#message-modal-body').html('Error uploading ' + name + '. Hit the Retry button on the photo to try again.');
                    $('#message-modal').modal('show');
                },
                onAutoRetry: function(id, name, attemptNumber) {},
                onManualRetry: function(id, name) {},
                onValidateBatch: function(fileOrBlobData) {},
                onValidate: function(fileOrBlobData) {},
                onSubmitDelete: function(id) {},
                onDelete: function(id) {},
                onDeleteComplete: function(id, xhrOrXdr, isError) {},
                onPasteReceived: function(blob) {},
                onStatusChange: function(id, oldStatus, newStatus) {},
                onSessionRequestComplete: function(response, success, xhrOrXdr) {}
            },
            objectProperties: {
                acl: 'public-read',
                key: function (fileId) {

                    var filename = uploader1.getName(fileId);
                    var uuid = uploader1.getUuid(fileId);
                    var ext = filename.substr(filename.lastIndexOf('.') + 1).toLowerCase();

                    return  'items/routes/{{ peak.slug_new_text }}_' + uuid + '.' + ext;

                }
            }
        });

        //loop through steps to show insert between link
        var tmpId, tmpSteps, tmpStepNumber;
        tmpSteps = '';
        $('.insert-step').each(function(i, obj) {
            $(this).show();
            tmpId = $(this).attr('id');
            tmpStepNumber = tmpId.split("-")[1];
            //add step number to list of steps in order
            tmpSteps = tmpSteps + tmpStepNumber + ',';
        });
        $('#route-step-file-list').val(tmpSteps);
        //hide the last one, don't need it
        $('#'+tmpId).hide();

        $('#add_next_step').blur();
    }

    function addStep() {
        totalSteps++;
        var step_number = totalSteps.toString();

        //add a hidden element to store the image filename
        $('#route-step-files').append('<input class="route-step-file" type="hidden" id="route-step-file-'+step_number+'" name="route-step-file-'+step_number+'" value="">');

        //add element
        $('#peakroute-files').append('<li class="route-step file-'+step_number+'" style="margin-bottom: 20px; width: 100%;"><div style="display: flex; display: -webkit-flex;"><div class="summitlog caption textareaContainer" style="margin-top: 0px; position: relative; align-content: stretch; width: 100%; width: 100% !important; margin-top: 0px !important;"><textarea maxlength="1000" class="route-step-text" name="step-'+step_number+'-details" id="step-'+step_number+'-details" style="resize: none; border: 1px solid #99999F; width: 100% !important; height: 100% !important; padding-top: 16px; color: #333 !important; position: absolute;" placeholder="Step details..."></textarea><div class="remove-step" onclick="confirmRemoveStep(\'file-'+step_number+'\');"><i class="fa fa-times" aria-hidden="true"></i></div></div><div class="route-step-photo" id="file-'+step_number+'"></div></div><div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="text-align: center;margin-top: 20px;"><a class="insert-step" id="insert-'+step_number+'" style="display: none; cursor: pointer;" onclick="insertStep('+step_number+');">add step in between</a></div></li>');

        var where = 'file-'+step_number;
        var uploader1 = new qq.s3.FineUploader({
            debug: false,
            multiple: false,
            element: document.getElementById(where),
            template: 'qq-image-template',
            request: {
                endpoint: 'https://peakery-media.s3.amazonaws.com',
                accessKey: 'AKIAJPWBM4YZXFHWBTPQ'
            },
            signature: {
                endpoint: '{% url "s3signature" %}'
            },
            uploadSuccess: {
                endpoint: '{% url "s3_route_photo_upload" %}',
                params: {
                    'route_id': '{{ route.id }}',
                    'step_number': step_number
                }
            },
            iframeSupport: {
                localBlankPagePath: '/api/s3blank/'
            },
            retry: {
               enableAuto: false // defaults to false
            },
            validation: {
                acceptFiles: ['image/jpg, image/jpeg, .jpg, .jpeg'],
                allowedExtensions: ['jpg', 'jpeg'],
                sizeLimit: 10000000,
                image: {
                    minHeight: 1000,
                    minWidth: 1000
                }
            },
            messages: {
                typeError: 'Sorry, must be a JPG file.',
                sizeError: 'Sorry, this file is too big. Must be under 10 MB.',
                minHeightImageError: 'Sorry, this photo is too small. Must be at least 1000 x 1000 pixels.',
                minWidthImageError: 'Sorry, this photo is too small. Must be at least 1000 x 1000 pixels.'
            },
            showMessage: function (message) {
                $('#message-modal-label').html('Error');
                $('#message-modal-body').html(message);
                $('#message-modal').modal('show');
            },
            text: {
                fileInputTitle: 'Choose file(s)'
            },
            callbacks: {
                onSubmit: function(id, name) {},
                onSubmitted: function(id, name) {
                    $("#"+where).find('div.qq-upload-button-selector').hide();
                    $("li.file-"+step_number).find("div.caption, div.checker").show();
                    uploaderIdle = false;
                    $('.edit_route').prop('disabled', false);
                    $('#route-step-file-'+step_number).data('status', 'pending');
                },
                onComplete: function(id, name, responseJSON, maybeXhr) {
                    $("#"+where).show();
                    set_image(where, responseJSON);
                    jailai(where);
                    $('.edit_route').prop('disabled', false);
                },
                onAllComplete: function(successful, failed) {
                    //update status for this file
                    $('#route-step-file-'+step_number).data('status', 'complete');
                    //check status of all added steps to see if any pending uploads
                    var pendingUploads = false;
                    $('.route-step-file').each(function(i, obj) {
                        if ($(this).data('status') == 'pending') {
                            pendingUploads = true;
                        }
                    });
                    if (!pendingUploads) {
                        uploaderIdle = true;
                        if (formSubmitted) {
                            saveRoute();
                        }
                    }
                },
                onCancel: function(id, name) {},
                onUpload: function(id, name) {},
                onUploadChunk: function(id, name, chunkData) {},
                onUploadChunkSuccess: function(id, chunkData, responseJSON, xhr) {},
                onResume: function(id, fileName, chunkData) {},
                onProgress: function(id, name, loaded, total) {},
                onTotalProgress: function(loaded, total) {},
                onError: function(id, name, reason, maybeXhrOrXdr) {
                    $('#message-modal-label').html('Error');
                    $('#message-modal-body').html('Error uploading ' + name + '. Hit the Retry button on the photo to try again.');
                    $('#message-modal').modal('show');
                },
                onAutoRetry: function(id, name, attemptNumber) {},
                onManualRetry: function(id, name) {},
                onValidateBatch: function(fileOrBlobData) {},
                onValidate: function(fileOrBlobData) {},
                onSubmitDelete: function(id) {},
                onDelete: function(id) {},
                onDeleteComplete: function(id, xhrOrXdr, isError) {},
                onPasteReceived: function(blob) {},
                onStatusChange: function(id, oldStatus, newStatus) {},
                onSessionRequestComplete: function(response, success, xhrOrXdr) {}
            },
            objectProperties: {
                acl: 'public-read',
                key: function (fileId) {

                    var filename = uploader1.getName(fileId);
                    var uuid = uploader1.getUuid(fileId);
                    var ext = filename.substr(filename.lastIndexOf('.') + 1).toLowerCase();

                    return  'items/routes/{{ peak.slug_new_text }}_' + uuid + '.' + ext;

                }
            }
        });

        //loop through steps to show insert between link
        var tmpId, tmpSteps, tmpStepNumber;
        tmpSteps = '';
        $('.insert-step').each(function(i, obj) {
            $(this).show();
            tmpId = $(this).attr('id');
            tmpStepNumber = tmpId.split("-")[1];
            //add step number to list of steps in order
            tmpSteps = tmpSteps + tmpStepNumber + ',';
        });
        $('#route-step-file-list').val(tmpSteps);
        //hide the last one, don't need it
        $('#'+tmpId).hide();

        $('#add_next_step').blur();
    }

    function initStep(step_number) {
        var where = 'file-'+step_number;
        var uploader = new qq.FileUploader({
            element: document.getElementById(where),
            action: '{% url "upload_route_photo" %}',
            allowedExtensions: ['jpg', 'jpeg'],
            params: {route_id: '{{ route.id }}', step_number: step_number},
            debug: false,
            onSubmit: function(id, fileName){
                $("#"+where).find('div.qq-upload-button').addClass('qq-upload-loading');
                $("li.file-"+step_number).find("div.caption, div.checker").show();
                uploaderIdle = false;
                $('.edit_route').prop('disabled', true);
            },
            onProgress: function(id, fileName, loaded, total){
            },
            onComplete: function(id, fileName, responseJSON){
                if(responseJSON['success'] == 'true') {
                    $("#"+where).show();
                    set_image(where, responseJSON);
                    jailai(where);
                    $('.edit_route').prop('disabled', false);
                }
                else {
                    $('#message-modal-label').html('Error');
                    $('#message-modal-body').html('Sorry, that photo is too small (min dimensions {{ photo_minsize|first }} x {{ photo_minsize|last }} )');
                    $('#message-modal').modal('show');
                    reset_spinner('#'+where);
                }
            },
            onCancel: function(id, fileName){},
            messages: {
                typeError: "Invalid file type. Please add only JPG files.",
                sizeError: "File is too large, maximum file size is 12MB.",
                minSizeError: "{file} is too small, minimum file size is {minSizeLimit}.",
                emptyError: "{file} is empty, please select files again without it.",
                allowedExtensionsError : "{file} is not allowed.",
                onLeave: "The files are being uploaded, if you leave now the upload will be cancelled."
            },
            showMessage: function (message) {
                $('#message-modal-label').html('Error');
                $('#message-modal-body').html(message);
                $('#message-modal').modal('show');
            }
        });
    }

    //unit conversions
    function setElevationStartMeters() {
        if ($('#elevation-start').val() != '') {
            var elevation_start_ft = parseFloat(replaceAll($('#elevation-start').val(),',',''));
            var elevation_start_m = (elevation_start_ft * 0.3048).toFixed(0);
            $('#elevation-start').val(numberWithCommas(elevation_start_m));
            if (uploaderIdle) {
                $('.edit_route').prop('disabled', false);
            }
        }
    }

    function setElevationStartFeet() {
        if ($('#elevation-start').val() != '') {
            var elevation_start_m = parseFloat(replaceAll($('#elevation-start').val(),',',''));
            var elevation_start_ft = (elevation_start_m / 0.3048).toFixed(0);
            $('#elevation-start').val(numberWithCommas(elevation_start_ft));
            if (uploaderIdle) {
                $('.edit_route').prop('disabled', false);
            }
        }
    }

    function setElevationGainMeters() {
        if ($('#elevation-gain').val() != '') {
            var elevation_gain_ft = parseFloat(replaceAll($('#elevation-gain').val(),',',''));
            var elevation_gain_m = (elevation_gain_ft * 0.3048).toFixed(0);
            $('#elevation-gain').val(numberWithCommas(elevation_gain_m));
            if (uploaderIdle) {
                $('.edit_route').prop('disabled', false);
            }
        }
    }

    function setElevationGainFeet() {
        if ($('#elevation-gain').val() != '') {
            var elevation_gain_m = parseFloat(replaceAll($('#elevation-gain').val(),',',''));
            var elevation_gain_ft = (elevation_gain_m / 0.3048).toFixed(0);
            $('#elevation-gain').val(numberWithCommas(elevation_gain_ft));
            if (uploaderIdle) {
                $('.edit_route').prop('disabled', false);
            }
        }
    }

    function setDistanceToSummitKm() {
        if ($('#distance-to-summit').val() != '') {
            var distance_to_summit_miles = parseFloat($('#distance-to-summit').val());
            var distance_to_summit_km = (distance_to_summit_miles * 1.60934).toFixed(1);
            $('#distance-to-summit').val(numberWithCommas(distance_to_summit_km));
            var init_distance_to_summit = parseFloat($('#init-distance-to-summit').val());
            var init_distance_to_summit_km = (init_distance_to_summit * 1.60934).toFixed(1);
            $('#init-distance-to-summit').val(init_distance_to_summit_km);
            if (uploaderIdle) {
                $('.edit_route').prop('disabled', false);
            }
        }
    }

    function setDistanceToSummitMiles() {
        if ($('#distance-to-summit').val() != '') {
            var distance_to_summit_km = parseFloat($('#distance-to-summit').val());
            var distance_to_summit_miles = (distance_to_summit_km / 1.60934).toFixed(1);
            $('#distance-to-summit').val(numberWithCommas(distance_to_summit_miles));
            var init_distance_to_summit_km = parseFloat($('#init-distance-to-summit').val());
            var init_distance_to_summit_miles = (init_distance_to_summit_km / 1.60934).toFixed(1);
            $('#init-distance-to-summit').val(init_distance_to_summit_miles);
            if (uploaderIdle) {
                $('.edit_route').prop('disabled', false);
            }
        }
    }

    $(function(){
        $('textarea.addACaption').placeholder();
    });

    </script>

{% endblock %}

{% block end_full_height_form %}
</form>
{% endblock %}