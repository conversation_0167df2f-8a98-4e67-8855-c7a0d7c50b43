{% extends "base.html" %}
{% load thumbnail %}
{% load avatar_tags %}

{% block title %}Peak Baggers{% endblock %}
{% block titlemeta %}Peak Baggers - {% endblock %}
{% block description %}Peakery’s top mountaineers{% endblock %}
{% block image_rel %}{% endblock %}

{% block peakbaggers_active %}active{% endblock %}

{% block content %}

<h1 class="peak_title">Peak baggers</h1>

{% autopaginate baggers 10 %}
{% for bagger in baggers %}
<ul id="bagger" class="peakbaggers">
    <li>
        <div class="avatar">
            <a href="{% url "user_profile" bagger %}">{% avatar bagger 250 %}</a>
        </div>
        <div class="content">
            <h2><a href="{% url "user_profile" bagger %}">{{ bagger }}</a>
                <span> {{ bagger.count }} peak{{ bagger.count|pluralize }} bagged
                    {% with bagger.get_profile.days_since_last_summit as days_since_last_summit %}
                    {% if days_since_last_summit >= 0 %}
                    &bull; last summit {{ days_since_last_summit }} day{{ days_since_last_summit|pluralize }} ago
                    {% endif %}
                    {% endwith %}
                </span>
                <span>
                    &bull; latest...
                </span>
            </h2>
            <ul class="peaks_bagged">
                {% for s in bagger.get_profile.summits_10 %}
                <li>
                    <a href="{{ s.item.get_absolute_url }}"><img src="{% thumbnail s.item.get_thumbnail 100x80 crop %}"></a>
                    <a href="{{ s.item.get_absolute_url }}" class="item_name">{{ s.item }}</a>
                </li>
                {% endfor %}
            </ul><!-- END peaks_bagged -->
        </div><!-- END content -->
    </li>
</ul><!-- END baggers -->
{% endfor %}

<div>
    {% paginate %}
</div>


{% endblock %}
