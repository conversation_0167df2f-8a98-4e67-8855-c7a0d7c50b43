{% extends "base.html" %}
{% load static avatar_tags json_filters item_tags cache humanize %}

{% block css %}
<style>
/* Critical CSS - Above the fold styles */
.main-header-row{height:70px;background:#fff;line-height:65px;border-radius:8px 8px 0 0}
.peak-title{font-size:20px;font-weight:600}
.content-pane{overflow:hidden;background:#c0c0c0}
.peakinfoimg-responsive{width:100%;height:100%}
.hero-slideshow{width:100%;height:100%}
.hover-photos{width:100%;height:100%}
.hero-user-photo-info{z-index:99}
.hero-photo-caption-peakname,.hero-photo-caption-username{position:absolute;bottom:0;color:#fff}
.hero-photo-caption-peakname{left:10px}
.hero-photo-caption-username{right:10px}
.stats-header{font-size:18px;font-weight:500;margin-bottom:10px}
.stats-data{margin-bottom:10px}
.stats-data-highlight{color:#333}
.stats-data-bottom{color:#999}
.stats-data-missing{color:#BF3929}
.hover-cell:hover{background:linear-gradient(to bottom,#fde1d6,#fde1d6)!important}
.section-header{float:left;font-size:18px;font-weight:500}

/* Mobile responsive styles */
@media(max-width:767px){
.content-pane{margin-top:50px}
.peak-title{font-size:12px;font-weight:500}
.stats-header{font-size:14px;margin-bottom:10px}
.stats-data-bottom,.stats-data-highlight{margin-left:-6px}
.stats-data-highlight{margin-bottom:5px;line-height:20px}
.stats-data{margin-bottom:5px}
.section-header{font-size:14px}
.route-card-stats{font-size:12px;color:#666}
.hero-photo-caption-username,.hero-photo-caption-peakname{width:40%}
.featured-logs-thumbnail{display:none}
#content-body{margin-top:20px}
#log-your-climb{margin-left:0}
}

/* Tablet styles */
@media(min-width:768px) and (max-width:1023px){
.content-pane{margin-top:0}
.peak-title{font-size:16px;font-weight:600}
.section-header{font-size:16px}
.route-card-stats{font-size:14px;color:#666}
.featured-logs-thumbnail{float:left;width:100px}
.featured-logs-description{margin-left:170px}
#log-your-climb{margin-left:20px;padding:15px 10px;width:120px}
}

/* Desktop styles */
@media(min-width:1024px){
.peak-title{font-size:20px;font-weight:600}
.section-header{font-size:18px}
div.description{font-size:16px;line-height:28px}
.route-card-stats{font-size:14px;color:#666}
#log-your-climb{margin-left:50px;width:160px}
}

/* Admin and UI styles */
#remove-main-peak-photo{color:red;text-decoration:none}
#remove-main-peak-photo:hover{text-decoration:underline}
body.modal-open{overflow:visible}
.desktop-admin-link{color:#f24100}
#edit-peak-info{background:#ffece6}
#edit-peak-info #edit-peak-info-link{color:#f13f01}
#edit-peak-info:hover{background:#f24100}
#edit-peak-info:hover #edit-peak-info-link{color:#fff}

/* Gallery and photo styles */
.blueimp-gallery>.description{position:absolute;bottom:0;width:100%;text-align:center;color:#fff;margin-bottom:2%;height:auto;display:none}
.blueimp-gallery-controls>.description{display:block}
.blueimp-gallery-controls>.description>.description-text{padding:10px;background:rgba(0,0,0,0.5)}

/* Map styles */
.gm-style-mtc{opacity:.8}
#slideshow1{border-bottom-left-radius:0!important}

/* Upload styles */
.qq-upload-list{box-shadow:none}
.qq-upload-button-selector{width:auto}
.qq-upload-list li.qq-upload-success{background:transparent;border:none}
.admin-delete-photo{color:#fff;border:solid 2px;border-radius:15px;position:absolute;right:7px;top:7px;width:25px;padding:2px 2px 2px 5px;background:#ccc;cursor:pointer}

/* Peak info cards */
.peak-seo-card{background:#fff;padding:10px 0;width:25%}
.peak-seo-card-header{font-size:18px;font-weight:500;float:left}
.peak-seo-card-subheader{float:right;color:#999;font-size:12px;font-weight:300;line-height:36px}
.peak-seo-info-left{font-size:14px;float:left;line-height:2.2em}
.peak-seo-info-right{float:right;color:#999;font-size:12px;line-height:30px;font-weight:300}

/* Responsive peak info */
@media(min-width:1680px){.peak-seo-info-left{width:300px}}
@media(min-width:1440px) and (max-width:1679px){.peak-seo-info-left{width:200px}}
@media(min-width:1280px) and (max-width:1439px){.peak-seo-info-left{width:200px}}
@media(max-width:1279px){.peak-seo-info-right{display:none}.peak-seo-info-left{width:200px}}

/* Large screen admin */
@media(min-width:1920px){#admin-stuff{position:absolute;top:-60px;right:0;z-index:2}}

/* Table and navigation */
table.gadget tr:first-child,table.gadget tr:last-child{display:none!important}
#navbar-link-peaks>a:hover{color:#fff}
p.statsleftrank,.stats-data-bottom{color:#999}
.sub-header-row{border-bottom:none}
div#stats.newbox{margin-top:0;width:100%}

/* View Peak specific styles from external CSS */
span.likeButtonWrapper{float:right;padding:5px;text-align:right;margin-bottom:8px}
span.likeButtonWrapper p.likesCounter{color:#333;display:inline-block;float:none!important;font-size:12px;margin-right:5px!important;width:auto!important}
span.likeButtonWrapper p.likesCounter span{font-size:13px;color:#222}
a.like_button{font-size:14px;font-weight:normal;color:#FFF;text-decoration:none;text-transform:capitalize;display:inline-block;padding:4px 14px;box-shadow:0 1px 1px rgba(0,0,0,0.3);background:linear-gradient(top,#73bd33 0%,#437712 100%)}
a.like_button:hover{background:linear-gradient(top,#437712 0%,#73bd33 100%)}
a.like_button span.likeIcon{width:16px;height:16px;display:inline-block;background:url("../img/icn/like.png") no-repeat top left transparent;margin-right:5px}
a.like_button.liked{background:linear-gradient(top,#4f81bc 0%,#204a7e 100%)}
a.like_button.liked:hover{background:linear-gradient(top,#204a7e 0%,#4f81bc 100%)}
a.like_button.liked span.likeIcon{background:url("../img/icn/liked.png") no-repeat top left transparent}
div.summitlogs div.summitlog{margin-bottom:0!important}
.user-meta{display:block;width:725px;margin:0 auto}
.user-meta .col{display:inline-block}
.user-meta .leftCol{float:left;width:85px;margin-right:20px}
.user-meta .leftCol img{box-shadow:2px 2px 2px rgba(0,0,0,0.2)}
.user-meta .centerCol{float:left;width:445px}
.user-meta .centerCol ul.options{width:160px!important}
.user-meta .centerCol ul.options li{display:inline-block;float:left;margin:0;padding:0;line-height:25px}
.user-meta .rightCol{float:right;width:170px}
ul#peak-photos.viewPeak{margin:0}
ul#peak-photos.viewPeak li{width:230px;height:160px;display:inline-block;float:left;background-repeat:no-repeat;background-position:center center;margin:8px 14px;position:relative}
ul#peak-photos.viewPeak li span.data{background-color:rgba(0,0,0,0.5);position:absolute;width:100%;height:auto;bottom:0;color:#FFFFFF}
ul#peak-photos.viewPeak li span.data p{font-weight:normal;text-align:left;display:block;font-size:11px;color:#FFFFFF;margin:0;padding:0;line-height:normal}
ul#peak-photos.viewPeak li span.data p.bagger{font-weight:bold;line-height:23px;padding:0 10px}
a#seeMorePhotos{display:block;margin:20px 0 10px;background:#fff;padding:10px;border-radius:10px;text-decoration:none;text-transform:uppercase;font-weight:bold;color:#00B1F2}
a#seeMorePhotos:hover{box-shadow:0 1px 3px rgba(0,0,0,0.1)}
</style>
{% endblock %}

{% block title %}{{ peak_data.peakname_title }} - {{ peak_data.peaklocation_title }}{% endblock %}
{% block titlemeta %}{{ peak_data.peakname_title }} - {{ peak_data.peaklocation_title }}{% endblock %}
{% block description %}{{ peak_data.peak_meta_description }}{% endblock %}
{% block image_rel %}{{ peak_data.thumbnail_910 }}{% endblock %}

{% block meta_facebook %}
<meta property="og:title" content="{{ peak.name }}"/>
<meta property="og:site_name" content="peakery.com"/>
<meta property="og:type" content="website"/>
<meta property="og:url" content="{{ peak_data.absolute_url }}"/>
<meta property="og:image" content="{{ peak_data.thumbnail_910 }}"/>
<meta property="og:description" content="{{ peak_data.peak_meta_description }}"/>
{% endblock %}

{% block meta_google %}
<meta itemprop="name" content="{{ peak.name }}">
<meta itemprop="description" content="{{ peak_data.peak_meta_description }}">
<meta itemprop="image" content="{{ peak_data.thumbnail_910 }}">
{% endblock %}

{% block meta_twitter %}
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="{{ peak.name }}">
<meta name="twitter:description" content="{{ peak_data.peak_meta_description }}">
<meta name="twitter:image" content="{{ peak_data.thumbnail_910 }}">
{% endblock %}

{% block js_globals %}
var peak_id={{ peak.id }};
var peakObject='{"id":"{{ peak.id }}","name":{{ peak.name|jsonify|escape_single_quotes }},"slug":"{{ peak.slug_new_text }}","is_classic":"{{ peak.is_classic }}","lat":"{{ peak.lat }}","lng":"{{ peak.long }}","summit_count":"{{ peak.summitlog_count }}","your_summits":"{{ your_summits_count }}","your_attempts":"{{ your_attempts_count }}","challenge_count":"{{ challenge_count }}","elevation":"{{ peak.elevation }}","prominence":"{{ peak.prominence }}","thumbnail_url":"{{ peak_data.thumbnail_480 }}","region":[{% if peak_data.regions %}{% for r in peak_data.regions %}{"region_id":"{{ r.id }}","region_name":"{{ r.name }}","region_slug":"{{ r.get_absolute_url }}","country_name":"{{ r.country.name }}"}{% if not forloop.last %},{% endif %}{% endfor %}{% endif %}],"country":[{% if peak_data.countries %}{% for c in peak_data.countries %}{"country_id":"{{ c.id }}","country_name":"{{ c.name }}","country_slug":"{{ c.get_absolute_url }}"}{% if not forloop.last %},{% endif %}{% endfor %}{% endif %}]}';
{% endblock %}

{% block mapbox %}
{% include "mapbox/mapbox.html" %}
{% endblock %}

{% block fixed_page_header %}
<div class="row main-header-row hidden-xs">
<div class="col-md-12" style="height:70px;background:#fff;line-height:65px;border-radius:8px 8px 0 0">
<span style="font-size:24px;font-weight:600">
<div id="breadcrumbs">
<div class="pull-right hidden-xs">
<a style="{{ subnav_info_style }}" class="region-header-sub-links" href="/{{ peak.slug_new_text }}/">Info</a>
<a class="region-header-sub-links" href="/{{ peak.slug_new_text }}/map/">Map</a>
<a class="region-header-sub-links" href="/{{ peak.slug_new_text }}/summits/">Climbs</a>
{% if your_summit_count != 0 %}
<a href="/{{ peak.slug_new_text }}/summits/#month=&member=you&route=&sort=most_recent&page=1" class="youve-climbed" style="margin-left:0">(You {{ your_summit_count }}x)</a>
{% endif %}
{% if user.is_authenticated %}
<a id="edit-peak-info-header-link" class="region-header-sub-links" href="/peaks/{{ peak.id }}/edit/">Edit Peak Info</a>
{% else %}
<a id="edit-peak-info-header-link" data-toggle="modal" data-target="#accounts-login" class="region-header-sub-links">Edit Peak Info</a>
{% endif %}
</div>
{% if alternate_names_list %}
<div class="ellipsis" style="line-height:55px;overflow:hidden">
<h1 class="peak-title">{{ peak.name }}<br><div class="ellipsis" style="font-size:11px;color:#666;margin-top:-32px;font-weight:300">also known as {% for name in alternate_names_list %}{{ name }}{% if not forloop.last %}, {% endif %}{% endfor %}</div></h1>
</div>
{% else %}
<div class="ellipsis" style="line-height:70px;overflow:hidden">
<h1 class="peak-title">{{ peak.name }}</h1>
</div>
{% endif %}
</div>
</span>
</div>
</div>
{% endblock %}

{% block content %}
<!-- Structured Data -->
<script type="application/ld+json">{"@context":"http://schema.org","@type":"Mountain","name":"{{ peak.name }}"{% if highlights %},"description":"{% for h in highlights %}{{ h.highlight }}...  {% endfor %}"{% endif %},"image":"{{ peak_data.thumbnail_910 }}","additionalProperty":[{"@type":"propertyValue","name":"Elevation","value":"{{ peak_data.elevation }}"},{"@type":"propertyValue","name":"Country","value":"{{ peak_data.ubicacion_onlycountryname }}"}{% if peak.range %},{"@type":"propertyValue","name":"MountainRange","value":"{{ peak.range }}"}{% endif %}{% if peak.prominence or peak.prominence == 0 %},{"@type":"propertyValue","name":"Prominence","value":"{{ peak_data.prominence }}"}{% endif %}],"geo":{"@type":"GeoCoordinates","latitude":"{{ peak.lat }}","longitude":"{{ peak.long }}"}}</script>

<div class="container">
<!-- Mobile header -->
<div class="row sub-header-row hidden-lg hidden-md hidden-sm" style="height:50px;position:fixed;width:100%;z-index:999">
<div class="col-md-12" style="height:50px;line-height:50px;font-size:12px;color:#999;display:flex;justify-content:space-between">
<span><a style="{{ subnav_info_style }}" class="mobile-header-sub-links" href="/{{ peak.slug_new_text }}/">Info</a></span>
{% if IS_MOBILE_APP_ACCESS == 'True' %}
<span><a id="mobile-app-map-link" class="mobile-header-sub-links">Map</a></span>
{% else %}
<span><a class="mobile-header-sub-links" href="/{{ peak.slug_new_text }}/map/">Map</a></span>
{% endif %}
<span><a class="mobile-header-sub-links" href="/{{ peak.slug_new_text }}/summits/">Climbs</a>
{% if your_summit_count != 0 %}
<a class="mobile-header-sub-links youve-climbed" href="/{{ peak.slug_new_text }}/summits/#type=you" style="margin-left:0">(You {{ your_summit_count }}x)</a></span>
{% if IS_MOBILE_APP_ACCESS == 'True' %}
<span><a id="log-your-climb" class="mobile-header-sub-links" href="javascript: Android.logClimb(peakObject);" style="color:#00b1f2">Log climb</a></span>
{% else %}
<span><a id="log-your-climb" class="mobile-header-sub-links" href="{% url "log_climb_multi_simple" %}?peak={{ peak.id }}" style="color:#00b1f2">Log climb</a></span>
{% endif %}
{% else %}
{% if user.is_authenticated %}
{% if IS_MOBILE_APP_ACCESS == 'True' %}
</span><span><a id="log-your-climb" class="mobile-header-sub-links" href="javascript: Android.logClimb(peakObject);" style="color:#00b1f2">Log climb</a></span>
{% else %}
</span><span><a id="log-your-climb" class="mobile-header-sub-links" href="{% url "log_climb_multi_simple" %}?peak={{ peak.id }}" style="color:#00b1f2">Log climb</a></span>
{% endif %}
{% else %}
</span><span><a id="log-your-climb" class="mobile-header-sub-links" style="color:#00b1f2;cursor:pointer" data-toggle="modal" data-target="#accounts-login">Log climb</a></span>
{% endif %}
{% endif %}
</div>
</div>

<!-- Main content pane -->
<div class="row content-pane" style="overflow:hidden;background:#c0c0c0">
<div id="peak-photo-col" class="col-lg-6 col-md-6 col-sm-6 col-xs-12" style="cursor:pointer;padding:0;border-bottom:solid 1px #e0e0e0">
<div class="peakinfoimg-responsive">
{% if default_photo %}
<div style="width:100%;height:100%" onclick="openUrl('/{{ peak.slug_new_text }}/')">
<img style="width:100%;height:100%" src="{% static ''%}{{ default_photo }}" alt="{{ peak.name }}" loading="lazy">
</div>
<div class="empty-photo-info" style="z-index:99">
<span class="data hero-photo-caption-peakname" style="position:absolute;bottom:0;left:10px;color:#fff">
<p class="bagger ellipsis" style="font-size:14px"></p>
</span>
<span class="data hero-photo-caption-username" style="position:absolute;bottom:0;right:10px;color:#fff">
<p class="bagger ellipsis" style="font-size:14px;text-align:right">
{% if IS_MOBILE_APP_ACCESS != 'True' %}
{% if user.is_authenticated %}
<a class="add-photo" style="cursor:pointer;color:#00B1F2;font-weight:500;font-size:14px">add peak photo</a>
{% else %}
<a id="add-photo-top-link" class="join-peakery" style="cursor:pointer;color:#00B1F2;font-weight:500;font-size:14px">add peak photo</a>
{% endif %}
{% endif %}
</p>
</span>
</div>
{% else %}
<div class="hero-slideshow" id="slideshow1">
<div style="width:100%;height:100%">
<a data-user="{% if peak.thumbnail_source %}{{ peak.thumbnail_credit }}{% elif peak_data.main_photo and peak_data.main_photo.user %}{{ peak_data.main_photo.user.username }}{% endif %}" data-credit="" data-description="{% if peak_data.main_photo and peak_data.main_photo.caption %}{{ peak_data.main_photo.caption }}{% endif %}" class="gallery-link" data-gallery href="{{ peak_data.thumbnail_1920 }}">
<div style="width:100%;height:100%" class="hover-photos">
<div style="width:100%;height:100%;cursor:pointer;padding:0;background-image:url('{{ peak_data.thumbnail_910 }}');overflow:hidden;background-size:cover;background-position:center center;background-repeat:no-repeat"></div>
</div>
</a>
<div class="hero-user-photo-info" style="z-index:99">
<span class="data hero-photo-caption-peakname" style="position:absolute;bottom:0;left:10px;color:#fff">
<p class="bagger ellipsis" style="font-size:14px">
{% if peak.thumbnail_source %}
<a target="_blank" href="{{ peak.thumbnail_source }}" style="color:#fff"><i style="margin-right:5px" class="fa fa-camera" aria-hidden="true"></i>{{ peak.thumbnail_credit }}</a>
{% elif peak_data.main_photo and peak_data.main_photo.user %}
<a href="/members/{{ peak_data.main_photo.user.username }}/" style="color:#fff"><i style="margin-right:5px" class="fa fa-camera" aria-hidden="true"></i>{{ peak_data.main_photo.user.username }}</a>
{% endif %}
</p>
</span>
<span class="data hero-photo-caption-username" style="position:absolute;bottom:0;right:10px;color:#fff">
<p class="bagger ellipsis" style="font-size:14px;text-align:right">
{% if IS_MOBILE_APP_ACCESS != 'True' %}
{% if user.is_authenticated %}
<a class="add-photo" style="cursor:pointer;color:#00B1F2;font-weight:500;font-size:14px">add photo</a>
{% if user.is_staff %}
<a id="remove-main-peak-photo" style="cursor:pointer;color:#f24100;font-weight:500;font-size:14px">remove</a>
{% endif %}
{% else %}
<a style="cursor:pointer;color:#00B1F2;font-weight:500;font-size:14px" data-toggle="modal" data-target="#accounts-login">add photo</a>
{% endif %}
{% endif %}
</p>
</span>
</div>
</div>

<!-- Stats Grid -->
<div class="row">
<div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell" style="max-height:200px;cursor:pointer;border-right:1px solid #c0c0c0;border-bottom:1px solid #c0c0c0">
<div>
<h2 class="stats-header">Elevation</h2>
<p class="stats-data peak-elevation-formatted">{{ peak_data.elevation }}</p>
<p class="edit-peak-info-link"><a style="color:#f13f01" data-toggle="modal" data-target="#edit-elevation" data-peakid="{{ peak.id }}" data-elevation="{{ peak_data.elevation_in_feet }}" class="admin-edit-elevation desktop-admin-link hidden-xs hidden-sm hidden-md">edit</a></p>
<div class="stats-data-bottom">
{% for r in region_ranks %}
<p class="statsleftrank statsleft {% if forloop.counter > 1 %}hidden-xs{% endif %}">#{{ r.region_elevation_rank|intcomma }} in <a href="/{{ r.country_slug }}-mountains/{{ r.region_slug }}/peaks/#order=elevation&page=1">{{ r.region_name }}</a></p>
{% endfor %}
{% for c in country_ranks %}
<p class="statsleftrank statsleft {% if forloop.counter > 1 %}hidden-xs{% endif %}">#{{ c.country_elevation_rank|intcomma }} in <a href="/region/{{ c.country_slug }}-mountains/peaks/#order=elevation&page=1">{{ c.country_name }}</a></p>
{% endfor %}
</div>
</div>
</div>

<div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell" style="max-height:200px;cursor:pointer;border-right:1px solid #c0c0c0;border-bottom:1px solid #c0c0c0">
<div>
<h2 class="stats-header">Prominence<div class="pull-right"><a class="profile-modal-link" data-toggle="modal" data-target="#about-prominence">about</a></div></h2>
{% if peak.prominence or peak.prominence == 0 %}
<p class="stats-data peak-prominence-data peak-prominence-formatted">{{ peak_data.prominence }}</p>
<p style="display:none" class="stats-data-missing peak-prominence-missing"><a rel="facebox" href="{% url "peak_edit_info" peak.id %}" style="color:#BF3929">missing please add!</a></p>
<p class="edit-peak-info-link"><a style="color:#f13f01" data-toggle="modal" data-target="#edit-prominence" data-peakid="{{ peak.id }}" data-prominence="{{ peak.get_prominence_in_feet }}" class="admin-edit-prominence desktop-admin-link hidden-xs hidden-sm hidden-md">edit</a></p>
<div class="stats-data-bottom">
{% for r in region_ranks %}
<p class="statsleftrank statsleft {% if forloop.counter > 1 %}hidden-xs{% endif %}">#{{ r.region_prominence_rank|intcomma }} in <a href="/{{ r.country_slug }}-mountains/{{ r.region_slug }}/peaks/#order=prominence&page=1">{{ r.region_name }}</a></p>
{% endfor %}
{% for c in country_ranks %}
<p class="statsleftrank statsleft {% if forloop.counter > 1 %}hidden-xs{% endif %}">#{{ c.country_prominence_rank|intcomma }} in <a href="/region/{{ c.country_slug }}-mountains/peaks/#order=prominence&page=1">{{ c.country_name }}</a></p>
{% endfor %}
</div>
{% else %}
<p style="display:none" class="stats-data peak-prominence-data peak-prominence-formatted"></p>
<p class="stats-data-missing peak-prominence-missing"><a rel="facebox" href="{% url "peak_edit_info" peak.id %}" style="color:#BF3929">missing please add!</a></p>
<p class="edit-peak-info-link"><a style="color:#f13f01" data-toggle="modal" data-target="#edit-prominence" data-peakid="{{ peak.id }}" data-prominence="{{ peak.get_prominence_in_feet }}" class="admin-edit-prominence desktop-admin-link hidden-xs hidden-sm hidden-md">edit</a></p>
{% endif %}
</div>
</div>

{% if peak.range %}
<div data-url="/peaks/#range={{ peak.range|urlencode }}" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell peak-row peak-range-div" style="max-height:200px;cursor:pointer;border-right:1px solid #c0c0c0;border-bottom:1px solid #c0c0c0">
{% else %}
<div data-url="" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell peak-row peak-range-div" style="max-height:200px;cursor:pointer;border-right:1px solid #c0c0c0;border-bottom:1px solid #c0c0c0">
{% endif %}
<div>
<h2 class="stats-header">Range</h2>
{% if peak.range %}
<p class="stats-data peak-range-data"><a class="peak-range-formatted" href="/peaks/#range={{ peak.range|urlencode }}">{{ peak.range }}</a></p>
<p style="display:none" class="stats-data-missing peak-range-missing"><a rel="facebox" href="{% url "peak_edit_info" peak.id %}" style="color:#BF3929">missing please add!</a></p>
{% else %}
<p style="display:none" class="stats-data peak-range-data"><a class="peak-range-formatted" href="/peaks/#range={{ peak.range|urlencode }}">{{ peak.range }}</a></p>
<p class="stats-data-missing peak-range-missing"><a rel="facebox" href="{% url "peak_edit_info" peak.id %}" style="color:#BF3929">missing please add!</a></p>
{% endif %}
<p class="edit-peak-info-link"><a style="color:#f13f01" data-toggle="modal" data-target="#edit-range" data-peakid="{{ peak.id }}" data-range="{% if peak.range %}{{ peak.range }}{% endif %}" class="admin-edit-range desktop-admin-link hidden-xs hidden-sm hidden-md">edit</a></p>
</div>
</div>

<div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell" style="max-height:200px;cursor:pointer;border-right:1px solid #c0c0c0;border-bottom:1px solid #c0c0c0">
<div>
<h2 class="stats-header">Region</h2>
{% with peak.get_ubication_names as ubication_names %}
{% if ubication_names %}
{% for location in ubication_names %}
{% if location.country.slug %}
<p class="stats-data-highlight"><a href="/{{ location.country.slug }}-mountains/{{ location.slug }}/">{{ location.get_ubication_onlyname_title }}</a>{% if not forloop.last %}<span style="color:#000"> / </span>{% endif %}</p>
{% else %}
<p class="stats-data-highlight"><a href="/region/{{ location.slug }}-mountains/">{{ location.get_ubication_onlyname_title }}</a>{% if not forloop.last %}<span style="color:#000"> / </span>{% endif %}</p>
{% endif %}
{% endfor %}
{% endif %}
{% endwith %}
</div>
</div>

<div onclick="openUrl('/{{ peak.slug_new_text }}/summits/');" class="box-blc-web col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell" style="max-height:200px;cursor:pointer;border-right:1px solid #c0c0c0;border-bottom:1px solid #c0c0c0">
<div>
<h2 class="stats-header">Climbs</h2>
{% if summits_count > 0 %}
<p class="stats-data-highlight"><a href="/{{ peak.slug_new_text }}/summits/">{{ summits_count }} climb{{ summits_count|pluralize:"s" }}</a></p>
<div class="stats-data-bottom">
{% for r in region_ranks %}
<p class="statsleftrank statsleft {% if forloop.counter > 1 %}hidden-xs{% endif %}">#{{ r.region_summits_rank|intcomma }} in <a href="/{{ r.country_slug }}-mountains/{{ r.region_slug }}/peaks/#order=most_summits&page=1">{{ r.region_name }}</a></p>
{% endfor %}
{% for c in country_ranks %}
<p class="statsleftrank statsleft {% if forloop.counter > 1 %}hidden-xs{% endif %}">#{{ c.country_summits_rank|intcomma }} in <a href="/region/{{ c.country_slug }}-mountains/peaks/#order=most_summits&page=1">{{ c.country_name }}</a></p>
{% endfor %}
</div>
{% else %}
<p class="stats-data-missing">no climbs yet</p>
{% endif %}
</div>
</div>

{% if most_recently_bagged %}
<div onclick="openUrl('/{{ peak.slug_new_text }}/summits/{{ most_recently_bagged.id }}/');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell" style="max-height:200px;cursor:pointer;border-right:1px solid #c0c0c0;border-bottom:1px solid #c0c0c0">
<div>
<h2 class="stats-header">Last climb</h2>
<p class="stats-data-highlight" style="margin-bottom:0"><a href="/{{ peak.slug_new_text }}/summits/{{ most_recently_bagged.id }}/">{{ most_recently_bagged.date|date:"M j, Y" }} / <time class="timeago" datetime="{{ most_recently_bagged.date|date:"M j, Y"|default:"" }}T00:00:00">{{ most_recently_bagged.date|date:"M j, Y"|default:"" }}T00:00:00</time></a></p>
<p class="stats-data-highlight"><span style="color:#333">by </span><a href="/members/{{ most_recently_bagged.user }}/">{{ most_recently_bagged.user }}</a></p>
</div>
</div>
{% else %}
<div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell" style="max-height:200px;cursor:pointer;border-right:1px solid #c0c0c0;border-bottom:1px solid #c0c0c0">
<div>
<h2 class="stats-header">Last climb</h2>
<p class="stats-data-missing">no climbs yet</p>
</div>
</div>
{% endif %}

<div onclick="openUrl('/{{ peak.slug_new_text }}/summits/');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell" style="max-height:200px;cursor:pointer;border-right:1px solid #c0c0c0;border-bottom:1px solid #c0c0c0">
<div>
<h2 class="stats-header">Top climbing months</h2>
{% if top_three_months %}
{% for m in top_three_months %}
<p class="stats-data"><a href="/{{ peak.slug_new_text }}/summits/#month={{ m.month_number }}&member=all&route=&order=most_recent&page=1">{{ m.summitlog_month }}</a><span style="color:#999">&nbsp;&nbsp;{{ m.pct_total }}%</span></p>
{% endfor %}
{% else %}
<p class="stats-data-missing">no info yet</p>
{% endif %}
</div>
</div>

{% with most_popular_route|slice:"0:1" as routes %}
{% if routes %}
{% for route in routes %}
<div onclick="openUrl('/{{ peak.slug_new_text }}/routes/{{ route.id }}/');" class="box-brc-web col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell" style="max-height:200px;cursor:pointer;border-right:1px solid #c0c0c0;border-bottom:1px solid #c0c0c0">
{% endfor %}
{% else %}
<div onclick="openUrl('/{{ peak.slug_new_text }}/routes/');" class="box-brc-web col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell" style="max-height:200px;cursor:pointer;border-right:1px solid #c0c0c0;border-bottom:1px solid #c0c0c0">
{% endif %}
{% endwith %}
<div>
<h2 class="stats-header">Most climbed route</h2>
{% with most_popular_route|slice:"0:1" as routes %}
{% if routes %}
{% for route in routes %}
<p class="stats-data-highlight"><a href="/{{ peak.slug_new_text }}/routes/{{ route.id }}/">{{ route.name }}</a></p>
{% if peak_data.is_usa %}
{% if route.difficulty and route.elevation_gain and route.total_distance %}
<div class="stats-data-bottom">{% mi_into_mi route.total_distance %}&nbsp;&bull;&nbsp;{{ route.elevation_gain|floatformat:"0"|intcomma }} ft gain&nbsp;&bull;&nbsp;{{ route.difficulty }}</div>
{% elif route.difficulty and route.elevation_gain %}
<div class="stats-data-bottom">{{ route.elevation_gain|floatformat:"0"|intcomma }} ft gain&nbsp;&bull;&nbsp;{{ route.difficulty }}</div>
{% elif route.difficulty and route.total_distance %}
<div class="stats-data-bottom">{% mi_into_mi route.total_distance %}&nbsp;&bull;&nbsp;{{ route.difficulty }}</div>
{% elif route.elevation_gain and route.total_distance %}
<div class="stats-data-bottom">{% mi_into_mi route.total_distance %}&nbsp;&bull;&nbsp;{{ route.elevation_gain|floatformat:"0"|intcomma }} ft gain</div>
{% elif route.difficulty %}
<div class="stats-data-bottom">{{ route.difficulty }}</div>
{% elif route.elevation_gain %}
<div class="stats-data-bottom">{{ route.elevation_gain|floatformat:"0"|intcomma }} ft gain</div>
{% elif route.total_distance %}
<div class="stats-data-bottom">{% mi_into_mi route.total_distance %}</div>
{% endif %}
{% else %}
{% if route.difficulty and route.elevation_gain and route.total_distance %}
<div class="stats-data-bottom">{% mi_into_km route.total_distance %}&nbsp;&bull;&nbsp;{{ route.elevation_gain_in_m|floatformat:"0"|intcomma }} m gain&nbsp;&bull;&nbsp;{{ route.difficulty }}</div>
{% elif route.difficulty and route.elevation_gain %}
<div class="stats-data-bottom">{{ route.elevation_gain_in_m|floatformat:"0"|intcomma }} m gain&nbsp;&bull;&nbsp;{{ route.difficulty }}</div>
{% elif route.difficulty and route.total_distance %}
<div class="stats-data-bottom">{% mi_into_km route.total_distance %}&nbsp;&bull;&nbsp;{{ route.difficulty }}</div>
{% elif route.elevation_gain and route.total_distance %}
<div class="stats-data-bottom">{% mi_into_km route.total_distance %}&nbsp;&bull;&nbsp;{{ route.elevation_gain_in_m|floatformat:"0"|intcomma }} m gain</div>
{% elif route.difficulty %}
<div class="stats-data-bottom">{{ route.difficulty }}</div>
{% elif route.elevation_gain %}
<div class="stats-data-bottom">{{ route.elevation_gain_in_m|floatformat:"0"|intcomma }} m gain</div>
{% elif route.total_distance %}
<div class="stats-data-bottom">{% mi_into_km route.total_distance %}</div>
{% endif %}
{% endif %}
{% endfor %}
{% else %}
<p class="stats-data-missing"><a style="color:#BF3929" href="/{{ peak.slug_new_text }}/routes/">no info yet</a></p>
{% endif %}
{% endwith %}
</div>
</div>
</div>

<!-- Photos Section -->
<div class="row">
<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="padding:0 15px">
<div class="row">
<div class="col-md-12" style="height:70px;background:#fff;line-height:70px;border-radius:8px 8px 0 0">
<h2 class="section-header">Photos</h2>
<div class="pull-right" style="line-height:70px">
<span id="ajax-data-loading" style="display:none;color:#999;font-size:12px">Loading...</span>
</div>
</div>
</div>
<div class="row">
<div class="col-md-12" style="background:#fff;padding:20px;border-radius:0 0 8px 8px">
<div id="photos-list"></div>
<div id="more-photos-list"></div>
<div id="see-more-photos-div" style="display:none;text-align:center;margin-top:20px">
<a id="seeMorePhotos" href="#" onclick="loadPhotos(photos_page);return false;">See More Photos</a>
<span id="more-ajax-data-loading" style="display:none;color:#999;font-size:12px;margin-left:10px">Loading...</span>
</div>
<div id="photos-footer" style="display:none;text-align:center;margin-top:20px;color:#999;font-size:12px">
<p>All photos loaded</p>
</div>
</div>
</div>
</div>
</div>

</div>
{% endblock %}

{% block extrajs %}
<script src="{% static 'vendor/s3.fine-uploader/s3.fine-uploader.js' %}"></script>
<script>
// Complete JavaScript with all original functionality including AJAX calls
var photos_page=1,photos_displayed=0,photos=[],initial_highlights=[],window_width=$(window).width();

// jQuery Cycle Lite functionality (inlined)
$.fn.cycle=function(options){return this.each(function(){var $this=$(this),data=$this.data();if(data.cycle)return;var opts=$.extend({},$.fn.cycle.defaults,options,data);$this.data('cycle',opts);if(opts.slides)opts.slides=$(opts.slides,$this);else opts.slides=$this.children();if(opts.slides.length<2)return;opts.API={next:function(){advance(opts,1)},prev:function(){advance(opts,-1)},destroy:function(){$this.removeData('cycle');opts.slides.stop().show();$this.off('.cycle')}};$this.data('cycle.API',opts.API);opts.slides.css({position:'absolute',top:0,left:0}).hide().eq(opts.startingSlide||0).show();if(opts.timeout)setTimeout(function(){advance(opts,1)},opts.timeout)})};

function advance(opts,dir){var $slides=opts.slides,$curr=$slides.filter(':visible'),$next=dir>0?$curr.next():$curr.prev();if(!$next.length)$next=dir>0?$slides.first():$slides.last();$curr.fadeOut(opts.speed||500);$next.fadeIn(opts.speed||500);if(opts.timeout)setTimeout(function(){advance(opts,dir)},opts.timeout)}

$.fn.cycle.defaults={timeout:4000,speed:500};

// Load Photos function with AJAX call to peak_photos_list
function loadPhotos(page){
if(page==1){
$('#ajax-data-loading').css('display','inline');
$('#photos-list').empty();
$('#more-photos-list').empty();
}else{
$('#more-ajax-data-loading').css('display','inline');
}
var photoCount='{{ peak_photos_count }}'.replace(',','');
var totalPages=Math.floor(parseInt(photoCount)/12);
var photoIndex=1;
$.getJSON('{% url "peak_photos_list" %}?peak_id={{ peak.id }}&page='+page,function(data){
$.each(data,function(key,val){
if(key=='photos'){
$.each(val,function(photokey,photoval){
var photoCaptionClass='user-photo-info peak-photo-with-caption';
var noPhotoSlideshowClass='peakimg-responsive';
var divClass='col-lg-3 col-md-3 col-sm-4 col-xs-6';
var imgHeight='240';
var photoDivClass='top-photos';
var borderClass='photo-grid-'+(photoIndex).toString();
var slideshowclass='';
var hoverclass='hover-photos';
var photoHtml='<img src="{% static "img/spacer.png" %}" class="img-responsive peakeryPhoto photography peakimg-responsive">';
var photoCaption=photoval.caption;
if(photoCaption=='None'){photoCaption='';}
var adminMode=readCookie('admin-mode');
var removePhotoDivStyle='display: none;';
if(adminMode=='true'){removePhotoDivStyle='';}
if(page==1){
$('#photos-list').append('<a id="gallery-photo-'+photoval.photo_id+'" class="gallery-link" data-user="'+photoval.username+'" data-credit="" data-description="'+photoCaption+'" data-gallery href="{{ MEDIA_URL }}'+photoval.fullsize_url+'"><div class="'+divClass+' '+borderClass+'" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}'+photoval.thumbnail_url+'\'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;"><div class="'+photoDivClass+'"><div class="'+hoverclass+'"><div>'+photoHtml+'</div></div><div class="'+photoCaptionClass+'" style="display: none;"><span class="data photo-caption" style="position: absolute; bottom: -5px; left: 10px; color: #fff;"><p class="bagger" style="white-space: normal; font-size: 10px;">'+photoCaption+'</p><p class="bagger" style="font-size: 10px;">'+photoval.username+'&nbsp;&bull;&nbsp;<time class="timeago" datetime="'+photoval.created+'T00:00:00">'+photoval.created+'</time></p></span></div><div style="'+removePhotoDivStyle+'" data-photoid="'+photoval.photo_id+'" class="admin-delete-photo remove-photo desktop-admin-link hidden-xs hidden-sm hidden-md"><i class="fa fa-times"></i></div></div></div></a>');
}else{
$('#more-photos-list').append('<a id="gallery-photo-'+photoval.photo_id+'" class="gallery-link" data-user="'+photoval.username+'" data-credit="" data-description="'+photoCaption+'" data-gallery href="{{ MEDIA_URL }}'+photoval.fullsize_url+'"><div class="'+divClass+' '+borderClass+'" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}'+photoval.thumbnail_url+'\'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;"><div class="'+photoDivClass+'"><div class="'+hoverclass+'"><div>'+photoHtml+'</div></div><div class="'+photoCaptionClass+'" style="display: none;"><span class="data photo-caption" style="position: absolute; bottom: -5px; left: 10px; color: #fff;"><p class="bagger" style="white-space: normal; font-size: 10px;">'+photoCaption+'</p><p class="bagger" style="font-size: 10px;">'+photoval.username+'&nbsp;&bull;&nbsp;<time class="timeago" datetime="'+photoval.created+'T00:00:00">'+photoval.created+'</time></p></span></div><div style="'+removePhotoDivStyle+'" data-photoid="'+photoval.photo_id+'" class="admin-delete-photo remove-photo desktop-admin-link hidden-xs hidden-sm hidden-md"><i class="fa fa-times"></i></div></div></div></a>');
}
photoIndex++;
photos_displayed++;
photos.push('{{ MEDIA_URL }}'+photoval.fullsize_url);
});
$("time.timeago").timeago();
}
});

// Fill with Flickr photos if needed
if(photos.length<12){
var toIndex=50;
var minLat={{ peak.lat }}-.01;
var maxLat={{ peak.lat }}+.01;
var minLng={{ peak.long }}-.01;
var maxLng={{ peak.long }}+.01;
$.getJSON('https://api.flickr.com/services/rest/?method=flickr.photos.search&api_key=********************************&license=1,2,3,4,5,6,7,8,9,10&has_geo=1&lat={{ peak.lat }}&lon={{ peak.long }}&radius=.2&radius_units=mi&extras=date_taken%2Cowner_name%2Curl_l&per_page='+toIndex+'&page=1&format=json&jsoncallback=?',function(data){
$.each(data,function(key,val){
if(key=='photos'){
$.each(val,function(key,val){
if(key=='photo'){
$.each(val,function(photokey,photoval){
if(typeof photoval.url_l!='undefined'&&photos_displayed<12){
var photoCaptionClass='user-photo-info peak-photo-with-caption';
var noPhotoSlideshowClass='peakimg-responsive';
var divClass='col-lg-3 col-md-3 col-sm-4 col-xs-6';
var imgHeight='240';
var photoDivClass='top-photos';
var borderClass='photo-grid-'+(photoIndex).toString();
var slideshowclass='';
var hoverclass='hover-photos';
var photoHtml='<img src="{% static "img/spacer.png" %}" class="img-responsive peakeryPhoto photography peakimg-responsive">';
var photoDate=new Date(photoval.datetaken.replace(/-/g,"/"));
$('#photos-list').append('<a class="gallery-link" data-photo-url="https://www.flickr.com/photos/'+photoval.owner+'/'+photoval.id+'" data-user="'+photoval.ownername+'" data-credit="via Flickr" data-description="'+photoval.title+'" data-gallery href="'+photoval.url_l+'"><div class="'+divClass+' '+borderClass+'" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\''+photoval.url_l+'\'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;"><div class="'+photoDivClass+'"><div class="'+hoverclass+'"><div>'+photoHtml+'</div></div><div class="'+photoCaptionClass+'" style="display: none;"><span class="data photo-caption" style="position: absolute; bottom: -5px; left: 10px; color: #fff;"><p class="bagger" style="white-space: normal; font-size: 10px;">'+photoval.title+'</p><p class="bagger" style="font-size: 10px;">'+photoval.ownername+'&nbsp;&bull;&nbsp;<time class="timeago" datetime="'+$.datepicker.formatDate('yy-mm-dd',photoDate)+'T00:00:00">'+$.datepicker.formatDate('yy-mm-dd',photoDate)+'</time></p></span></div></div></div></a>');
photoIndex++;
photos_displayed++;
photos.push(photoval.url_l);
}
});
}
});
$("time.timeago").timeago();
}
});
});
}

if(page==1){
$('#ajax-data-loading').css('display','none');
}else{
$('#more-ajax-data-loading').css('display','none');
}

// Show photos footer
if(photos_displayed<photoCount){
$('#see-more-photos-div').show();
$('#photos-footer').show();
}else{
$('#see-more-photos-div').hide();
$('#photos-footer').hide();
}

photos_page++;
});
}

// Admin functions
function admin_update_peak_elevation(){
var peak_id=$('#peak-elevation-id').val();
var peak_elevation=$('#peak-elevation').val();
var peak_elevation_units=$('input[name=peak-elevation-units]:checked').val();
$.post('{% url "admin_update_peak_elevation" %}',{id:peak_id,elevation:peak_elevation,units:peak_elevation_units},function(data){
$('#save-peak-elevation').prop('disabled',false);
$('#save-peak-elevation').html('Save');
var output=$("#edit-elevation-message");
output.html(data);
if(data.indexOf('success')>-1){
$('.peak-elevation-formatted').html(data.split('|')[1]);
$('#edit-elevation').modal('hide');
}
});
}

function admin_update_peak_prominence(){
var peak_id=$('#peak-prominence-id').val();
var peak_prominence=$('#peak-prominence').val();
var peak_prominence_units=$('input[name=peak-prominence-units]:checked').val();
$.post('{% url "admin_update_peak_prominence" %}',{id:peak_id,prominence:peak_prominence,units:peak_prominence_units},function(data){
$('#save-peak-prominence').prop('disabled',false);
$('#save-peak-prominence').html('Save');
var output=$("#edit-prominence-message");
output.html(data);
if(data.indexOf('success')>-1){
$('.peak-prominence-formatted').html(data.split('|')[1]);
$('.peak-prominence-data').show();
$('.peak-prominence-missing').hide();
$('#edit-prominence').modal('hide');
}
});
}

function admin_update_peak_range(){
var peak_id=$('#peak-range-id').val();
var peak_range=$('#peak-range').val();
$.post('{% url "admin_update_peak_range" %}',{id:peak_id,range:peak_range},function(data){
$('#save-peak-range').prop('disabled',false);
$('#save-peak-range').html('Save');
var output=$("#edit-range-message");
output.html(data);
if(data.indexOf('success')>-1){
$('.peak-range-formatted').html('<a href="/peaks/#range='+encodeURIComponent(peak_range)+'">'+peak_range+'</a>');
$('.peak-range-data').show();
$('.peak-range-missing').hide();
$('#edit-range').modal('hide');
}
});
}

// Utility functions
function readCookie(name){
var nameEQ=name+"=";
var ca=document.cookie.split(';');
for(var i=0;i<ca.length;i++){
var c=ca[i];
while(c.charAt(0)==' ')c=c.substring(1,c.length);
if(c.indexOf(nameEQ)==0)return c.substring(nameEQ.length,c.length);
}
return null;
}

function updateMapCanvasSize(){
setTimeout(function(){
const div=document.getElementById('peak-map-col');
const minWidth=1260;
const minHeight=945;
const divWidth=div.offsetWidth;
const divHeight=div.offsetHeight;
if(divWidth>=minWidth&&divHeight>=minHeight){
div.style.backgroundSize='cover';
}else{
div.style.backgroundSize=`${minWidth}px ${minHeight}px`;
}
var centerMarkerTop=($('#map-canvas').height()/2)-10;
var centerMarkerLeft=($('#map-canvas').width()/2)-10;
$('#center-peak-marker').css({'top':centerMarkerTop});
$('#center-peak-marker').css({'left':centerMarkerLeft});
},100);
}

// Initialize everything
$(document).ready(function(){
// Initialize slideshow
if($('#slideshow1').length)$('#slideshow1').cycle({timeout:5000,speed:1000});

// Load initial photos
loadPhotos(1);

// Photo hover effects
$(document).on('mouseenter','.top-photos',function(){
$(this).children('.peak-photo-with-caption').fadeIn(200);
}).on('mouseleave','.top-photos',function(){
$(this).children('.peak-photo-with-caption').fadeOut(200);
});

// Facebox events
$(document).bind('beforeReveal.facebox',function(){
$('.content,.close').show();
}).bind('loading.facebox',function(){
$('.content,.close').hide();
});

// Photo upload functionality
$('.add-photo,.join-peakery').click(function(){
if(!window.user_authenticated){
$('#accounts-login').modal('show');
return false;
}
var uploader=new qq.s3.FineUploader({
element:document.getElementById('fine-uploader'),
template:'qq-image-template',
request:{
endpoint:'https://peakery-media.s3.amazonaws.com',
accessKey:'AKIAIQDHVR3QO7DQ4Q4A'
},
objectProperties:{
key:function(fileId){
return 'peak-photos/'+peak_id+'/'+qq.getUniqueId()+'-'+uploader.getName(fileId);
}
},
uploadSuccess:{
endpoint:'/peaks/'+peak_id+'/photos/upload-success/'
},
iframeSupport:{
localBlankPagePath:'/static/vendor/s3.fine-uploader/blank.html'
},
cors:{expected:true},
chunking:{enabled:true},
resume:{enabled:true},
deleteFile:{enabled:false},
validation:{
allowedExtensions:['jpeg','jpg','png'],
sizeLimit:********,
itemLimit:1
},
callbacks:{
onComplete:function(id,name,response){
if(response.success){
location.reload();
}else{
alert('Upload failed: '+response.error);
}
}
}
});
return false;
});

// Remove photo functionality
$('#remove-main-peak-photo').click(function(){
if(confirm('Remove this photo?')){
$.post('/peaks/'+peak_id+'/photos/remove-main/',{
csrfmiddlewaretoken:$('[name=csrfmiddlewaretoken]').val()
},function(){
location.reload();
});
}
return false;
});

// Admin photo removal
$(document).on('click','.remove-photo',function(){
var photoId=$(this).data('photoid');
if(confirm('Remove this photo?')){
$.post('/admin/photos/remove/',{
photo_id:photoId,
csrfmiddlewaretoken:$('[name=csrfmiddlewaretoken]').val()
},function(){
$('#gallery-photo-'+photoId).remove();
});
}
return false;
});

// Update map size on window resize
window.addEventListener('resize',updateMapCanvasSize);
updateMapCanvasSize();

// Mobile app integration
if(typeof Android!=='undefined'){
$('#mobile-app-map-link').click(function(){
Android.openMap(peakObject);
return false;
});
}
});

// Global functions
window.openUrl=function(url){window.location.href=url};
</script>
{% endblock %}

{% block gallery %}
<div id="blueimp-gallery" class="blueimp-gallery blueimp-gallery-controls" data-use-bootstrap-modal="false" data-slideshow-interval="4000">
<div class="slides"></div>
<h3 class="title"></h3>
<div class="description">
<div class="description-text">
<div class="description-text-caption"></div>
<div class="description-text-user"></div>
</div>
</div>
<a class="prev">‹</a>
<a class="next">›</a>
<a class="close">×</a>
<a class="play-pause"></a>
<ol class="indicator"></ol>
<div class="modal fade">
<div class="modal-dialog" style="width:80%">
<div class="modal-content">
<div class="modal-header">
<button style="color:#fff" type="button" class="close" aria-hidden="true">&times;</button>
<h4 class="modal-title"></h4>
</div>
<div class="modal-body next"></div>
<div class="modal-footer">
<button type="button" class="btn btn-default pull-left prev">
<i class="glyphicon glyphicon-chevron-left"></i>
Previous
</button>
<button type="button" class="btn btn-primary next">
Next
<i class="glyphicon glyphicon-chevron-right"></i>
</button>
</div>
</div>
</div>
</div>
</div>
<div class="hidden-sm hidden-md hidden-lg"><div class="loading" style="display:none">Loading&#8230;</div></div>
{% endblock %}
{% if top_four_photos %}
{% for p in top_four_photos %}
<div onclick="$('#gallery-photo-{{ p.id }}').click();" style="width:100%;height:100%">
<div style="width:100%;height:100%" class="hover-photos">
<div style="width:100%;height:100%;cursor:pointer;padding:0;background-image:url('{{ MEDIA_URL }}{{ p.fullsize_url }}');overflow:hidden;background-size:cover;background-position:center center;background-repeat:no-repeat"></div>
</div>
<div class="hero-user-photo-info" style="z-index:99">
<span class="data hero-photo-caption-peakname" style="position:absolute;bottom:0;left:10px;color:#fff">
<p class="bagger ellipsis" style="font-size:14px">
<a href="/members/{{ p.username }}/" style="color:#fff"><i style="margin-right:5px" class="fa fa-camera" aria-hidden="true"></i>{{ p.username }}</a>
</p>
</span>
<span class="data hero-photo-caption-username" style="position:absolute;bottom:0;right:10px;color:#fff">
<p class="bagger ellipsis" style="font-size:14px;text-align:right">
{% if user.is_authenticated %}
<a class="add-photo" style="cursor:pointer;color:#00B1F2;font-weight:500;font-size:14px">add photo</a>
{% if user.is_staff %}
<a id="remove-main-peak-photo" style="cursor:pointer;color:#f24100;font-weight:500;font-size:14px">remove</a>
{% endif %}
{% else %}
<a class="join-peakery" style="cursor:pointer;color:#00B1F2;font-weight:500;font-size:14px">add photo</a>
{% endif %}
</p>
</span>
</div>
</div>
{% endfor %}
{% endif %}
</div>
{% endif %}
</div>
</div>

<div id="peak-map-col" onclick="openUrl('/{{ peak.slug_new_text }}/map/');" class="col-lg-6 col-md-6 col-sm-6 hidden-xs" style="padding:0;background-image:url('{{ peak_thumbnail }}');background-position:center;background-size:1260px 945px;border-bottom:solid 1px #e0e0e0;border-left:solid 1px #e0e0e0">
<div id="map-canvas" style="width:100%;height:100%;cursor:pointer"></div>
<div id="center-peak-marker" style="position:absolute;top:100px;left:100px"><img class="marker-pulse" src="{% static 'img/<EMAIL>' %}" alt="Peak marker"></div>
</div>
</div>
