{% load widget_tweaks %}
<script type="text/javascript">
    $(document).ready(function(){
            swapField('id_password');
            $('input[title!=""]').hint();
            $('a#step2_login').facebox();
            $('.add_form').ajaxForm({
                    target: '#output',
                    success:    function(e) {
                        if(e=='True'){
                            $("#step2_login").trigger('click');
                        }else{
                            //error from django
                            $("p#message").html("<span style='color:red'>Incorrect password.</span>");
                            $("input#id_password").val("");
                        }
                    }
            });
    });
</script>

<div style="width: 400px">
<H1>member login</H1>
<div id="output" style="display: none"></div>
<div id="form">
    <p id="message"></p>
    <form action="{% url "login_user_lite" %}" method="POST" id="login_form" class="add_form">
        <fieldset>
            {{ form.username|attr:"title:username or email" }}
        </fieldset>
        <fieldset>
            <input type="text" title="password" id="id_password_clear" />
            <input type="password" id="id_password" name="password" style="display: none">
            {# form.password #}
        </fieldset>
        <input class="btn set2 input" type="submit" value="LOG IN!" />
    </form>
</div>

<a id="step2_login" href="{% url "summit_2" peak.id %}"></a>
</div>



<div class="faceboxMainWrapper" id="signUpLightbox">
    <div class="main">
        {% load account_tags %}
        {% register_form 'go_to_profile' request '_nav' %}
        <span class="alreadyAMember">
            <p>Already a member? <a href="{% url "login" %}?next={% url "login_and_reload" "login" %}" rel="facebox" class="blue">Login here</a></p>
        </span>
        <script type="text/javascript">
            $(function(){
                $("div#signUpLightbox input[type='image']").attr('src','{{ MEDIA_URL }}img/btn/sign_up_with_facebook.png');
            });
        </script>
    </div>
</div>