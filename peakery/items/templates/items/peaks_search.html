{% extends "base.html" %}
{% load static %}
{% block jquery_import %}
    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.7.0/jquery.min.js"></script>
{% endblock %}

{% block jquery_form_import %}
    <script type="text/javascript" src="{{MEDIA_URL}}js/jquery.form.2.92.js"></script>
{% endblock %}

{% block title %}{{ peak_page_title }}{% endblock %}
{% block titlemeta %}{{ peak_page_title }}{% endblock %}

{% block description %}{{ peak_page_description }}{% endblock %}
{% block image_rel %}{% endblock %}

{% block css %}
    <link href="{{ MEDIA_URL }}css/explore.css" rel="stylesheet" type="text/css" media="screen" />
{% endblock %}

{% block mapbox %}
{% include "mapbox/mapbox.html" %}
{% endblock %}

{% block extrajs %}
    <style>
        .ui-autocomplete {max-height: 200px; overflow-y: auto; overflow-x: hidden; padding-right: 20px;}
        * html .ui-autocomplete {height: 200px;}
        .ui-autocomplete-loading { background: white url('{{ MEDIA_URL }}img/misc/ajax1.gif') right center no-repeat; }
    </style>
    <script type="text/javascript">
        $(document).ready(function(){
            //var max_width = $("div.peak_list_cont").width()-189;
            //$("div.col_data").width(max_width);

//        $( "input#n" ).autocomplete({
//                                source: "{% url "autocomplete_city" %}",
//                                minLength: 2
//        });
        });
    </script>
    <style>

    .geocoder {
        position: absolute;
        top: 106px;
        left: 13px;
        background-color: transparent;
    }

    .mapboxgl-ctrl-geocoder--icon-search {
        display: none;
    }

    .mapboxgl-ctrl-geocoder {
        background-color: transparent;
        box-shadow: none;
        z-index: 9;
    }

    .mapboxgl-ctrl-geocoder--input {
        width: 215px;
        height: 46px;
        border: none !important;
        background-color: #e9e9e9;
    }

    .mapboxgl-ctrl-geocoder--input:focus {
        outline: none !important;
    }

    .mapboxgl-ctrl-geocoder--input::placeholder {
        font-size: 14px;
        font-weight: 300;
    }

    .mapboxgl-ctrl-geocoder .suggestions {
        width: 215px !important;
    }

    .mapboxgl-ctrl-geocoder .suggestions > li {
        width: 100% !important;
    }

    .mapboxgl-ctrl-geocoder--pin-right {
        top: 7px;
        left: 214px;
        position: absolute;
    }

    .mapboxgl-ctrl-geocoder--pin-right > button {
        background-color: transparent;
    }

    .mapboxgl-ctrl-geocoder--powered-by {
        display: none !important;
    }

      th.sortable.sorted {
        font-weight: bold;
        background-color: #00b1f2;
      }
      th.sortable.sorted a {
        color: #fff;
      }
      tr.asc th.sortable.sorted a:after {
          content: '\00a0\2193';
      }
      tr.desc th.sortable.sorted a:after {
          content: '\00a0\2191';
      }
      th a {
        color: #00b1f2;
      }
      .table-bordered {
          border-left: none;
          border-right: none;
      }

      .extra-challenge-link {
          cursor: pointer;
      }

      .table-hover-cell {
          cursor: pointer;
      }

      input[type="search"] {
          border: 1px solid #ccc;
      }

      .mobile-header-subtitle, .desktop-admin-link {
          color: #f24100;
      }

      div#explore .rightCol {
          margin-top: 0px;
      }

        .toggle-switch {height: 40px;}

        .toggle-switch label {line-height: 40px; color: #666;}

        div#explore .leftCol {
            background-color: #fff;
        }

      div#length-slider-range {
        -webkit-border-radius: 4px;
        -moz-border-radius: 4px;
        border-radius: 4px;
        height: 2px;
        overflow: visible;
        background-color: #ccc;
    }

    #length-slider {
        padding-top: 0px;
        margin-left: 8px;
    }

    div#vertical-slider-range {
        -webkit-border-radius: 4px;
        -moz-border-radius: 4px;
        border-radius: 4px;
        height: 2px;
        overflow: visible;
        background-color: #ccc;
    }

    #vertical-slider {
        padding-top: 8px;
        margin-left: 8px;
    }

    div#last-climbed-slider-range {
        -webkit-border-radius: 4px;
        -moz-border-radius: 4px;
        border-radius: 4px;
        height: 2px;
        overflow: visible;
        background-color: #ccc;
    }

    #last-climbed-slider {
        padding-top: 8px;
        margin-left: 8px;
    }

    #last-climbed-slider-range > .noUi-base > .noUi-origin > .noUi-handle-upper {
        display: none;
    }

    #summits-slider, #elevation-slider, #prominence-slider, #difficulty-slider {
        padding-top: 8px;
    }

    .noUi-connect {
        background: #ccc;
    }

    @media screen and (min-width: 769px) {
        .noUi-horizontal .noUi-handle {
            width: 35px;
            height: 35px;
            left: -24px;
            top: -17px;
            background-color: transparent;
            background-size: contain;
        }
    }

    .noUi-horizontal .noUi-handle {
        background-image: url({% static '' %}img/search-drag-handle-grey.png);
    }

    .gm-custom-mapunits-selected {
        color: #333;
    }

    .gm-custom-mapunits-unselected {
        color: #999;
    }

    .peak-list-table > tbody > tr > td {
        font-size: 11px;
    }

    .floatThead-container {
        top: 69px !important;
    }

    </style>
{% endblock %}

{% block bodyclass %}wrapper_set1 explore{% endblock %}
{% block explore_active %}{% if not showing_user_peaks %}active{% endif %}{% endblock %}
{% block map_active %}{% if not showing_user_peaks %}active{% endif %}{% endblock %}
{% block explored_class %}{% if not showing_user_peaks %}explored{% endif %}{% endblock %}
{% block explore_link %}javascript:void(0){% endblock %}

{% block content %}

<style>

   @media screen and (max-width: 1023px) and (min-width: 1px) {
       #content-body {
           margin-top: 53px;
           padding-bottom: 0px;
       }
   }
    @media screen and (min-width: 1024px) {
       #content-body {
           margin-top: 3px;
       }
    }
    #content-holder {
        background-image: none;
    }

</style>

    <div id="explore" class="peak_list_cont clearfix" style="margin-top: 17px;">
        <div id="mobile-collapse-nav" style="display: none; position: absolute; left: 0px; top: -35px; z-index: 9999;"></div>
        <div class="full-width-container" style="width: 100%; height: inherit;">
        <div class="row peak-list-content" style="background-color:#000;">
        <table class="hidden-lg hidden-md" id="mobile-peak-search" style="width: 100%; position: fixed; top: 70px; z-index: 9999;">
            <tr style="background-color: #f1f1f1;">
                <td id="mobile-search-title" style="padding-left: 80px; height: 49px; width: 80%; vertical-align: middle; text-align: center; font-size: 12px;">{% if q and n %}"{{ q }}" near {{ n }}{% elif q %}"{{ q }}"{% elif n %}Peaks near {{ n }}{% else %}All peaks{% endif %}</td>
                <td style="height: 50px; vertical-align: middle; font-size: 20px; text-align: right; padding-right: 15px;"><a id="peak-search-mobile" style="cursor: pointer;"><i class="fa fa-filter"></i></a></td>
            </tr>
        </table>
        <div id="peak-search" tabindex="-1" class="hidden-xs hidden-sm col_search leftCol" style="overflow-y: auto; position: absolute; width: 240px; height: -webkit-fill-available; z-index: 999; box-shadow: 1px 0 2px 0 rgba(0,0,0,0.30);">
            <div class="wrp clearfix">
                <div class="block">

                    {% if showing_user_peaks %}
                        <h3 class="blue" style="font-size: 25px; margin: 0px;padding: 10px 10px 0px 10px;text-align: center; height:20px;">
                            <a style="text-decoration: none;" href="{{ userinfo.get_profile.get_absolute_url }}">{{ userinfo.username }}'s</a></h3>
                        <h4 style="text-align: center;margin-top: 5px;">summited peaks</h4>
                    {% endif %}
                    {% if showing_list_peaks %}
                        <h3 class="blue" style="font-size: 18px; margin: 0px;padding: 10px 10px 10px 10px;text-align: center; height:20px;">
                            <a style="text-decoration: none;" href="{{ group.get_absolute_url }}">{{ group.name }}</a></h3>
                    {% endif %}
                    {% if showing_country_peaks %}
                        <h3 class="blue" style="font-size: 18px; margin: 0px;padding: 10px 10px 10px 10px;text-align: center; height:20px;">
                            <a style="text-decoration: none;" href="{{ country.get_absolute_url }}">{% if region_name %}{{region_name}}, {% endif %}{{ country.name }}</a></h3>
                    {% endif %}

                    <form id="explore" method="get" action="/peaks">

                        <div id="filter-peaks" style="text-align: center; font-weight: 500; padding-top: 5px;"><img src="https://peakery-static.s3.amazonaws.com/img/sliders-h-solid.png" style="width: 30px; padding-right: 10px; margin-top: -3px;">Filter peaks</div>

                        <fieldset>
                            <span>
                                <input id="q" type="search" name="q" class="peak-list-search-input" placeholder="peak name..." value="{{ q }}" style="font-size: 14px; padding: 8px 10px; border-radius: 12px; background-color: #e9e9e9; border-color: #e9e9e9;"/>
                                <a href="javascript:void(0)" id="clear_peak_name" class="clear" style="display: none;"><i class="fa fa-times"></i></a>
                            </span>
                        </fieldset>

                        <fieldset>
                            <span>
                                <input id="n" type="search" name="n" class="peak-list-search-input" placeholder="" value="{{ n }}" style="font-size: 14px; padding: 8px 10px; border-radius: 12px; background-color: #e9e9e9; border-color: #e9e9e9;"/>
                                <a href="javascript:void(0)" id="clear_near_location" class="clear" style="display: none;"><i class="fa fa-times"></i></a>
                            </span>
                        </fieldset>

                        <fieldset style="text-align: center;">
                            <input id="search-peaks-btn" type="submit" value="Search peaks" class="btn peak-list-btn set2 input" style="height: 45px; border-radius: 12px;" />
                        </fieldset>

                        <div id="map" style="display: none;"></div>

                        <div id="geocoder" class="geocoder"></div>

                        <meta charset="utf-8">

                        <style>
                            .elevation_slider {float: left; width: 100%; padding: 5%;}
                            #slider-range #prominence-slider-range #summits-slider-range #difficulty-slider-range #length-slider-range #vertical-slider-range #last-climbed-slider-range .ui-widget-header {background: #1f60f6;}
                            #slider-range #prominence-slider-range #summits-slider-range #difficulty-slider-range #length-slider-range #vertical-slider-range #last-climbed-slider-range .ui-slider-range {background: #1f60f6;}
                        </style>

                        <input type="text" id="hdnElevMin" hidden value="">
                        <input type="text" id="hdnElevMax" hidden value="">
                        <input type="text" id="hdnPromMin" hidden value="">
                        <input type="text" id="hdnPromMax" hidden value="">
                        <input type="text" id="hdnSummitsMin" hidden value="">
                        <input type="text" id="hdnSummitsMax" hidden value="">
                        <input type="text" id="hdnDifficultyMin" hidden value="">
                        <input type="text" id="hdnDifficultyMax" hidden value="">

                        <input type="text" id="hdnLengthMin" hidden value="">
                        <input type="text" id="hdnLengthMax" hidden value="">
                        <input type="text" id="hdnVerticalMin" hidden value="">
                        <input type="text" id="hdnVerticalMax" hidden value="">
                        <input type="text" id="hdnLastClimbedMin" hidden value="">
                        <input type="text" id="hdnLastClimbedMax" hidden value="">

                        <input type="text" id="hdnClassics" hidden value="">
                        <input type="text" id="hdnInChallenge" hidden value="">
                        <input type="text" id="hdnYouClimbed" hidden value="">

                        <input type="text" id="hdnKeyword" hidden value="">
                        <input type="text" id="hdnNear" hidden value="">
                        <input type="text" id="hdnLat" hidden value="">
                        <input type="text" id="hdnLng" hidden value="">
                        <input type="text" id="hdnSortKey" hidden value="">
                        <input type="text" id="hdnSortDir" hidden value="">
                        <input type="text" id="hdnPage" hidden value="">
                        <textarea id="hdnNearTxt" style="display: none;"></textarea>

                        <div id="map-tags" style="margin-top: 10px;">
                            <div id="map-tag-choices">
                                <div class="toggle-switch" style="border: none; width: 105px; left: -3px; margin-bottom: 8px;"><input class="map-tag-input" type="checkbox" id="chkMapTagsClassics"><label style="border-radius: 12px !important;">Classics</label></div>
                                <div class="toggle-switch" style="border: none; width: 105px; left: -12px; margin-bottom: 8px;"><input class="map-tag-input" type="checkbox" id="chkMapTagsInChallenge" value="inchallenges"><label style="border-radius: 12px !important;">In Challenge</label></div>
                                <div class="toggle-switch" style="border: none; width: 105px; left: -3px;"><input class="map-tag-input" type="checkbox" id="chkMapTagsYouClimbed" value="youclimbed"><label style="border-radius: 12px !important;"><i class="fas fa-check"></i> You</label></div>
                                <div class="toggle-switch" style="border: none; width: 105px; left: -12px;"><input class="map-tag-input" type="checkbox" id="chkMapTagsYouNotClimbed" value="younotclimbed"><label style="border-radius: 12px !important;"><i class="fas fa-ban" style="padding-right: 0.2em;"></i>You</label></div>
                            </div>
                        </div>

                        <div id="gm-custom-mapunits" class="gmnoprint gm-style-mtc" style="margin: 10px; z-index: 2; cursor: pointer; text-align: center; width: 215px; top: 0px;">
                            <div id="gm-custom-mapunitsbutton" draggable="false" title="Change units" style="cursor: pointer; direction: ltr; overflow: hidden; text-align: center; position: relative; color: #666; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: #eee; padding: 11px; font-weight: 500; border-radius: 12px;">
                                <span id="gm-custom-mapunitsbutton-label-feet" class="gm-custom-mapunits-selected">Feet</span> | <span id="gm-custom-mapunitsbutton-label-meters" class="gm-custom-mapunits-unselected">Meters</span>
                            </div>
                        </div>

                        <div id="length-slider" class="elevation_slider">
                            <div style="font-size: 14px; margin-left: -8px;">
                                <div id="length-label" style="float: left; font-size: 12px; color: #aaa;">Length</div>
                                <div class="pull-right" style="margin-top: -6px; margin-right: 5px;">
                                    <input type="text" disabled value="0  to  20+ mi" class="length-ammount" id="length-amount" style="padding-right: 5px; border:0; font-weight:300; width: 120px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                    <input type="text" disabled value="0  to  32+ km" class="length-ammount" id="length-amount-meters" style="display: none; padding-right: 0px; border:0; font-weight:300; width: 120px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                    <a href="#" style="display: none; margin-left: 5px; text-decoration: underline; font-size: 12px; float: right; height: 35px; line-height: 3;" id ='length_bt_showinmeters'></a>
                                </div>
                            </div>
                            <div id="length-slider-range" style="margin-left: -9px; margin-top: 0px; width: 218px; float: left;"></div>
                        </div>

                        <div id="vertical-slider" class="elevation_slider">
                            <div style="font-size: 14px; margin-left: -8px;">
                                <div id="vertical-label" style="float: left; font-size: 12px; color: #aaa;">Vertical gain</div>
                                <div class="pull-right" style="margin-top: -6px; margin-right: 5px;">
                                    <input type="text" disabled value="0  to  10,000+ ft" class="vertical-ammount" id="vertical-amount" style="padding-right: 5px; border:0; font-weight:300; width: 140px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                    <input type="text" disabled value="0  to  3000+ m" class="vertical-ammount" id="vertical-amount-meters" style="display: none; padding-right: 0px; border:0; font-weight:300; width: 120px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                    <a href="#" style="display: none; margin-left: 5px; text-decoration: underline; font-size: 12px; float: right; height: 35px; line-height: 3;" id ='vertical_bt_showinmeters'></a>
                                </div>
                            </div>
                            <div id="vertical-slider-range" style="margin-left: -9px; margin-top: 0px; width: 218px; float: left;"></div>
                        </div>

                        <div id="summits-slider" class="elevation_slider">
                            <div style="font-size: 14px; margin-left: -8px;">
                                <div id="summits-label" style="float: left; font-size: 12px; color: #aaa;">Popularity</div>
                                <div class="pull-right" style="margin-top: -6px; margin-right: 5px;">
                                    <input type="text" disabled value="0  to  500+ climbs" class="summits-ammount" id="summits-amount" style="padding-right: 5px; border:0; font-weight:300; width: 150px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                </div>
                            </div>
                            <div id="summits-slider-range" style="margin-left: -9px; margin-top: 0px; width: 218px; float: left;"></div>
                        </div>

                        <div id="last-climbed-slider" class="elevation_slider">
                            <div style="font-size: 14px; margin-left: -8px;">
                                <div id="last-climbed-label" style="float: left; font-size: 12px; color: #aaa;">Last climbed</div>
                                <div class="pull-right" style="margin-top: -6px; margin-right: 5px;">
                                    <input type="text" disabled value="any time" class="last-climbed-ammount" id="last-climbed-amount" style="padding-right: 5px; border:0; font-weight:300; width: 120px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                </div>
                            </div>
                            <div id="last-climbed-slider-range" style="margin-left: -9px; margin-top: 0px; width: 218px; float: left;"></div>
                        </div>

                        <div id="elevation-slider" class="elevation_slider">
                            <div style="font-size: 14px; margin-left: -8px;">
                                <div id="elevation-label" style="float: left; font-size: 12px; color: #aaa;">Elevation</div>
                                <div class="pull-right" style="margin-top: -6px; margin-right: 5px;">
                                    <input type="text" disabled value="0  to  29,500 ft" class="ammount" id="amount" style="padding-right: 0px; border:0; font-weight:300; width: 130px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                    <input type="text" disabled value="0  to  9,000 m" class="ammount" id="amount-meters" style="display: none; padding-right: 0px; border:0; font-weight:300; width: 130px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                    <a href="#" style="display: none; margin-left: 5px; text-decoration: underline; font-size: 12px; float: right; height: 35px; line-height: 3;" id ='bt_showinmeters'></a>
                                </div>
                            </div>
                            <div id="slider-range" style="margin-left: -9px; margin-top: 0px; width: 218px; float: left;"></div>
                        </div>

                        <div id="prominence-slider" class="elevation_slider">
                            <div style="font-size: 14px; margin-left: -8px;">
                                <div id="prominence-label" style="float: left; font-size: 12px; color: #aaa;">Prominence</div>
                                <div class="pull-right" style="margin-top: -6px; margin-right: 5px;">
                                    <input type="text" disabled value="0  to  29,500 ft" class="prominence-ammount" id="prominence-amount" style="padding-right: 0px; border:0; font-weight:300; width: 130px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                    <input type="text" disabled value="0  to  9,000 m" class="prominence-ammount" id="prominence-amount-meters" style="display: none; padding-right: 0px; border:0; font-weight:300; width: 130px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                    <a href="#" style="display: none; margin-left: 5px; text-decoration: underline; font-size: 12px; float: right; height: 35px; line-height: 3;" id ='prominence_bt_showinmeters'></a></div>
                            </div>
                            <div id="prominence-slider-range" style="margin-left: -9px; margin-top: 0px; width: 218px; float: left;"></div>
                        </div>

                        <div id="difficulty-slider" class="elevation_slider">
                            <div style="font-size: 14px; margin-left: -8px;">
                                <div id="difficulty-label" style="float: left; font-size: 12px; color: #aaa;">Difficulty</div>
                                <div class="pull-right" style="margin-top: -6px; margin-right: 5px;">
                                    <input type="text" disabled value="Class 1  to  5" class="difficulty-ammount" id="difficulty-amount" style="padding-right: 5px; border:0; font-weight:300; width: 120px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                </div>
                            </div>
                            <div id="difficulty-slider-range" style="margin-left: -9px; margin-top: 0px; width: 218px; float: left;"></div>
                        </div>

                        {% if request.user.is_authenticated %}
                        <div id="add-missing-peak" class="elevation_slider" style="padding-top: 10px; padding-left: 20px; padding-bottom: 40px;">
                            <div style="font-size: 14px; margin-left: -8px;">
                                <div id="add-missing-peak-label" style="float: left; font-size: 14px; color: #aaa; text-align: center; width: 100%;"><a href="/peaks/add/">Add a missing peak</a></div>
                            </div>
                        </div>
                        {% else %}
                        <div id="add-missing-peak" class="elevation_slider" style="padding-top: 10px; padding-left: 20px; padding-bottom: 40px;">
                            <div style="font-size: 14px; margin-left: -8px;">
                                <div id="add-missing-peak-label" style="float: left; font-size: 14px; color: #aaa; text-align: center; width: 100%;"><a data-toggle="modal" data-target="#accounts-login">Add a missing peak</a></div>
                            </div>
                        </div>
                        {% endif %}

                    </form>

                </div><!-- END block -->
            </div>
        </div><!-- END col_1 -->
        <div id="peaks-list" class="col_data rightCol">
            <table id="peaks-table" class="peak-list-table table table-striped table-bordered" style="display: none; margin-bottom: 0px;">
                <thead id="peaks-table-head"></thead>
                <tbody id="peaks-table-body"></tbody>
            </table>
            <div id="no-peaks-found" class="hidden-xs hidden-sm hidden-md hidden-lg" style="height: 100%; width: 100%; position: absolute; left: 240px; top: 14px; padding-left: 20px; color: #fff; font-size: 26px; margin-top: 9px;">
                <span style="color: #f24100;"><span id="no-peaks-message"></span> "<span id="no-peaks-query"></span>".</span>
                <span id="add-missing-peak" style="display: none;">
                <br />
                Is peakery missing this peak?
                <br />
                If so, please <a href="/peaks/add/">add it</a>.
                </span>
            </div>
            <div class="row sub-header-row" id="pagination" style="border-bottom: none; background-color: #333; color: #ccc; margin-top: -1px; height: 80px; margin-left: 0px; display: none;">
                <div class="col-md-12">
                    <div class="row" style="padding-left: 15px; margin-left: 5px; text-align: center;">
                        <span id="pagination-pages" class="step-links"></span>
                    </div>
                </div>
            </div>
        </div><!-- END col_2 -->
        <div class="row" id="ajax-data-loading" style="display: none;">
          <div class="col-md-12 data-loading" style="text-align: center; color: #666;">
            <i class="fa fa-spinner fa-spin fa-5x"></i>
          </div>
        </div>
        </div>
        </div>
    </div><!-- END peak_list_cont -->

    {% if request.user.is_superuser %}
    <div class="edit-elevation-modal modal fade" id="edit-elevation" tabindex="-1" role="dialog" aria-labelledby="edit-elevation-label">
        <div class="modal-dialog" role="document" style="max-width: 600px;">
            <div class="modal-content">
                <div class="modal-header">
                  <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                  <h4 class="modal-title" id="edit-elevation-label">Edit Elevation</h4>
                </div>
                <div class="modal-body">
                    <div class="row row-full-width summitlog-section"  style="padding-bottom: 20px;">
                        <div class="col-md-12">
                            <span class="field-title">Elevation</span>
                            <div class="field-title-spacer"></div>
                            <fieldset>
                                <div>
                                    <input value="" type="number" name="peak-elevation" id="peak-elevation" style="width: 125px; padding: 10px;" placeholder="in feet...">
                                    <input type="hidden" id="peak-elevation-id" value="">
                                    <div class="btn-group" data-toggle="buttons" style="margin-left: 15px;">
                                        <label class="btn active">
                                        <input type="radio" name="peak-elevation-units" id="peak-elevation-ft" value="ft" checked><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i> <span>  ft</span>
                                        </label>
                                        <label class="btn" style="margin-left: 5px;">
                                        <input type="radio" name="peak-elevation-units" id="peak-elevation-m" value="m"><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i><span> m</span>
                                        </label>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                    </div>
                    <div class="row row-full-width summitlog-section"  style="padding-bottom: 20px;">
                        <div class="pull-left save-changes-button-div">
                            <button class="btn btn-secondary set2 input save-changes-button" id="save-peak-elevation" style="width: 120px;">Save</button>
                        </div>
                    </div>
                    <div id="edit-elevation-message" class="row row-full-width summitlog-section" style="display: none; padding-bottom: 20px; color: #F24100;"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="edit-prominence-modal modal fade" id="edit-prominence" tabindex="-1" role="dialog" aria-labelledby="edit-prominence-label">
        <div class="modal-dialog" role="document" style="max-width: 600px;">
            <div class="modal-content">
                <div class="modal-header">
                  <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                  <h4 class="modal-title" id="edit-prominence-label">Edit Prominence</h4>
                </div>
                <div class="modal-body">
                    <div class="row row-full-width summitlog-section" style="padding-bottom: 20px;">
                        <div class="col-md-12">
                            <span class="field-title">Prominence</span>
                            <div class="field-title-spacer"></div>
                            <fieldset>
                                <div>
                                    <input value="" type="number" name="peak-prominence" id="peak-prominence" style="width: 125px; padding: 10px;" placeholder="in feet...">
                                    <input type="hidden" id="peak-prominence-id" value="">
                                    <div class="btn-group" data-toggle="buttons" style="margin-left: 15px;">
                                        <label class="btn active">
                                        <input type="radio" name="peak-prominence-units" id="peak-prominence-ft" value="ft" checked><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i> <span>  ft</span>
                                        </label>
                                        <label class="btn" style="margin-left: 5px;">
                                        <input type="radio" name="peak-prominence-units" id="peak-prominence-m" value="m"><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i><span> m</span>
                                        </label>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                    </div>
                    <div class="row row-full-width summitlog-section"  style="padding-bottom: 20px;">
                        <div class="pull-left save-changes-button-div">
                            <button class="btn btn-secondary set2 input save-changes-button" id="save-peak-prominence" style="width: 120px;">Save</button>
                        </div>
                    </div>
                    <div id="edit-prominence-message" class="row row-full-width summitlog-section" style="display: none; padding-bottom: 20px; color: #F24100;"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="edit-range-modal modal fade" id="edit-range" tabindex="-1" role="dialog" aria-labelledby="edit-range-label">
        <div class="modal-dialog" role="document" style="max-width: 600px;">
            <div class="modal-content">
                <div class="modal-header">
                  <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                  <h4 class="modal-title" id="edit-range-label">Edit Range</h4>
                </div>
                <div class="modal-body">
                    <div class="row row-full-width summitlog-section" style="padding-bottom: 20px;">
                        <div class="col-md-12">
                            <span class="field-title">Range</span>
                            <div class="field-title-spacer"></div>
                            <fieldset class="peakRange">
                                <input value="" type="text" name="peak-range" id="peak-range" style="width: 50%; min-width: 340px; padding: 10px;" placeholder="enter mountain range name..."></input>
                                <input type="hidden" id="peak-range-id" value="">
                            </fieldset>
                        </div>
                    </div>
                    <div class="row row-full-width summitlog-section"  style="padding-bottom: 20px;">
                        <div class="pull-left save-changes-button-div">
                            <button class="btn btn-secondary set2 input save-changes-button" id="save-peak-range" style="width: 120px;">Save</button>
                        </div>
                    </div>
                    <div id="edit-range-message" class="row row-full-width summitlog-section" style="display: none; padding-bottom: 20px; color: #F24100;"></div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <script type="text/javascript">

        var floatHeadMargin = 50;

        /* Given a query in the form "lng, lat" or "lat, lng"
        * returns the matching geographic coordinate(s)
        * as search results in carmen geojson format,
        * https://github.com/mapbox/carmen/blob/master/carmen-geojson.md */
        const coordinatesGeocoder = function (query) {
        // Match anything which looks like
        // decimal degrees coordinate pair.
            const matches = query.match(
                /^[ ]*(?:Lat: )?(-?\d+\.?\d*)[, ]+(?:Lng: )?(-?\d+\.?\d*)[ ]*$/i
            );
            if (!matches) {
                return null;
            }

            function coordinateFeature(lng, lat) {
                return {
                    center: [lng, lat],
                    geometry: {
                        type: 'Point',
                        coordinates: [lng, lat]
                    },
                    place_name: 'Lat: ' + lat + ', Long: ' + lng,
                    place_type: ['coordinate'],
                    properties: {},
                    type: 'Feature'
                };
            }

            const coord1 = Number(matches[1]);
            const coord2 = Number(matches[2]);
            const geocodes = [];

            geocodes.push(coordinateFeature(coord1, coord2));

            return geocodes;
        };

        $(function(){

            const map = new mapboxgl.Map({
            container: 'map',
            style: 'mapbox://styles/mapbox/streets-v11',
            center: [-79.4512, 43.6568],
            zoom: 13
            });

            // Add the control to the map.
        const geocoder = new MapboxGeocoder({
            accessToken: mapboxgl.accessToken,
            mapboxgl: mapboxgl,
            types: 'country, region, place',
            localGeocoder: coordinatesGeocoder,
            flyTo: { duration: 0 },
            limit: 10,
            marker: false,
            render: function (item) {
                // extract the item's maki icon or use a default
                const maki = item.properties.maki || 'marker';
                return `<div class='geocoder-dropdown-item'>
                <span class='geocoder-dropdown-text'>
                ${item.place_name.replace(', United States','')}
                </span>
                </div>`;
            }
        });

        geocoder.on('results', e => {
            $('.map-tag-input').hide();
        });

        geocoder.on('result', e => {
            $('.mapboxgl-ctrl-geocoder--input').val(e['result'].place_name.replace(', United States',''));
            setTimeout(function(){
                $('.map-tag-input').show();
            }, 1000);
            u = updateURLParameter(window.location.hash, 'lat', e.result.center[1]);
            u = updateURLParameter('#'+u, 'lat', e.result.center[1]);
            window.location.hash = u;
            u = updateURLParameter(window.location.hash, 'lng', e.result.center[0]);
            u = updateURLParameter('#'+u, 'lng', e.result.center[0]);
            window.location.hash = u;
            //loadPeaksFromHash();
        });

        document.getElementById('geocoder').appendChild(geocoder.onAdd(map));

        $('.mapboxgl-ctrl-geocoder--input').attr("placeholder", "jump to place...");

//            Top-left nav button ("find peaks") width to be it same at the width of the leftCol:
            var leftColWidth = $('div#explore .leftCol').width();
            $('li.headlink').css('width', leftColWidth);
//            Peak name input needs to show the remove text icon when the user enter text on it:
            var a = $('a#clear_peak_name');
            var a2 = $('a#clear_near_location');

            var input = $('input#q');
            var input2 = $('input#n');

            if ( input.val() != "" ) {
                a.css('display', 'block');
            }

            if ( input2.val() != "" ) {
                a2.css('display', 'block');
            }

            input.keyup(function(){
                if( $(this).val() != "" ) {
                    a.css('display', 'block');
                } else {
                    a.css('display', 'none');
                }
            });

            input2.keyup(function(){
                if( $(this).val() != "" ) {
                    a2.css('display', 'block');
                } else {
                    a2.css('display', 'none');
                }
            });

            a.click(function(){
                input.val('');
                $(this).css('display', 'none');
            });

            a2.click(function(){
                input2.val('');
                $(this).css('display', 'none');
            });
            $(document).on('click', '.peak-row', function (e) {
                var url = $(this).data('url');
                window.location.href = url;
            });


            $('.toggle-switch').on('mouseenter', 'input', function() {
                var currentBackground = $(this).siblings('label').css('background-color');
                if (currentBackground == 'rgb(234, 234, 234)') {
                    $(this).siblings('label').css('background-color','#ccc');
                } else if (currentBackground == 'rgb(238, 238, 238)') {
                    $(this).siblings('label').css('background-color','#ccc');
                } else if (currentBackground == 'rgb(230, 230, 230)') {
                    $(this).siblings('label').css('background-color','#ccc');
                } else {
                    //$(this).siblings('label').css('background-color','#ff4400');
                }
            });

            $('.toggle-switch').on('mouseleave', 'input', function() {
                var currentBackground = $(this).siblings('label').css('background-color');
                if (currentBackground == 'rgb(204, 204, 204)') {
                    $(this).siblings('label').css('background-color','rgb(234, 234, 234)');
                } else if (currentBackground == 'rgb(238, 238, 238)') {
                    $(this).siblings('label').css('background-color','rgb(234, 234, 234)');
                } else if (currentBackground == 'rgb(230, 230, 230)') {
                    $(this).siblings('label').css('background-color','rgb(234, 234, 234)');
                } else {
                    //$(this).siblings('label').css('background-color','#f24100');
                }
            });

            $('#map-tag-choices').on('change', '.map-tag-input', function () {
                var color;
                if (this.id == 'chkMapTagsYouClimbed' || this.id == 'chkMapTagsYouNotClimbed') {
                    {% if request.user.is_authenticated %}
                        if (this.id == 'chkMapTagsYouClimbed') {
                            color = "#10b025";
                        } else {
                            color = "#f24100";
                        }
                        //pass
                    {% else %}
                        return;
                    {% endif %}
                }
                if (this.checked) {
                    $(this).next().animate({
                        backgroundColor: color,
                    }, 200);
                    $(this).next().css('color', '#fff');
                } else {
                    $(this).next().animate({
                        backgroundColor: '#e6e6e6',
                    }, 200);
                    $(this).next().css('color', '#666');
                }
            });

            $('#chkMapTagsClassics').on('click', function () {
                delayShowClassics();
            });

            $('#chkMapTagsInChallenge').on('click', function () {
                delayShowInChallenge();
            });

            $('#chkMapTagsYouClimbed').on('click', function () {
                {% if request.user.is_authenticated %}
                delayShowYouClimbed();
                {% else %}
                $('#navbar-login-link').click();
                {% endif %}
            });

            $('#chkMapTagsYouNotClimbed').on('click', function () {
                {% if request.user.is_authenticated %}
                    delayShowYouNotClimbed();
                {% else %}
                    $('#navbar-login-link').click();
                {% endif %}
            });

            {% if request.user.is_superuser %}
            $(document).on('click', '.admin-edit-elevation', function (e) {
                var peak_id = $(this).data('peakid');
                $('#peak-elevation-id').val(peak_id);
                $('#peak-elevation').val('');
                $("#peak-elevation-ft").click();
                $("#edit-elevation-message").html('');
                $("#edit-elevation-message").hide();
                var elevation = $(this).data('elevation');
                $('#peak-elevation').val(elevation);
            });
            $("#peak-elevation-m").on('change', function() {
                setElevationMeters();
            });
            $("#peak-elevation-ft").on('change', function() {
                setElevationFeet();
            });
            $('#save-peak-elevation').on('click', function() {
                if ($('#peak-elevation').val() != '') {
                    $('#save-peak-elevation').prop('disabled', true);
                    $('#save-peak-elevation').html('<i class="fa fa-spinner fa-spin"></i>');
                    admin_update_peak_elevation();
                }
            });

            $(document).on('click', '.admin-edit-prominence', function (e) {
                var peak_id = $(this).data('peakid');
                $('#peak-prominence-id').val(peak_id);
                $('#peak-prominence').val('');
                $("#peak-prominence-ft").click();
                $("#edit-prominence-message").html('');
                $("#edit-prominence-message").hide();
                var prominence = $(this).data('prominence');
                $('#peak-prominence').val(prominence);
            });
            $("#peak-prominence-m").on('change', function() {
                setProminenceMeters();
            });
            $("#peak-prominence-ft").on('change', function() {
                setProminenceFeet();
            });
            $('#save-peak-prominence').on('click', function() {
                if ($('#peak-prominence').val() != '') {
                    $('#save-peak-prominence').prop('disabled', true);
                    $('#save-peak-prominence').html('<i class="fa fa-spinner fa-spin"></i>');
                    admin_update_peak_prominence();
                }
            });

            $(document).on('click', '.admin-edit-range', function (e) {
                var peak_id = $(this).data('peakid');
                $('#peak-range-id').val(peak_id);
                $('#peak-range').val('');
                $("#edit-range-message").html('');
                $("#edit-range-message").hide();
                var range = $(this).data('range');
                $('#peak-range').val(range);
            });
            $('#save-peak-range').on('click', function() {
                if ($('#peak-range').val() != '') {
                    $('#save-peak-range').prop('disabled', true);
                    $('#save-peak-range').html('<i class="fa fa-spinner fa-spin"></i>');
                    admin_update_peak_range();
                }
            });

            $(document).on('click', '.admin-edit-challenges', function (e) {
                var peak_id = $(this).data('peakid');
                var challenge_id = $(this).data('challengeid');
                var is_checked = $('input[id=admin-edit-challenge-'+peak_id+']:checked').val();
                var action = 'remove';
                var div_color = '#f24100';
                if (is_checked == 'on') {
                    action = 'add';
                    div_color = '#00b1f2';
                }
                $.post('{% url "admin_update_peak_to_challenge" %}', {id:peak_id, challengeid:challenge_id, action:action},
                        function(data) {
                            alert(data);
                            $('#admin-edit-challenge-' + peak_id + '-div').css('color',div_color);
                        }
                );
            });
            {% endif %}

        });

        var timers = {};

        function admin_update_peak_elevation() {
            var peak_id = $('#peak-elevation-id').val();
            var peak_elevation = $('#peak-elevation').val();
            var peak_elevation_units = $('input[name=peak-elevation-units]:checked').val();
            $.post('{% url "admin_update_peak_elevation" %}', {id:peak_id, elevation:peak_elevation, units:peak_elevation_units},
                    function(data) {
                        $('#save-peak-elevation').prop('disabled', false);
                        $('#save-peak-elevation').html('Save');
                        var output = $("#edit-elevation-message");
                        output.html(data);
                        output.show();
                        setTimeout(function() {
                            $('#edit-elevation').modal('hide');
                        }, 3000)
                    }
            );
        }

        function setElevationMeters() {
            if ($('#peak-elevation').val() != '') {
                var elevation_feet = parseFloat($('#peak-elevation').val());
                var elevation_meters = Math.floor(elevation_feet * .3048);
                $('#peak-elevation').val(elevation_meters);
            }
            $('#peak-elevation').attr('placeholder','in meters...');
        }

        function setElevationFeet() {
            if ($('#peak-elevation').val() != '') {
                var elevation_meters = parseFloat($('#peak-elevation').val());
                var elevation_feet = Math.floor(elevation_meters / .3048);
                $('#peak-elevation').val(elevation_feet);
            }
            $('#peak-elevation').attr('placeholder','in feet...');
        }

        function admin_update_peak_prominence() {
            var peak_id = $('#peak-prominence-id').val();
            var peak_prominence = $('#peak-prominence').val();
            var peak_prominence_units = $('input[name=peak-prominence-units]:checked').val();
            $.post('{% url "admin_update_peak_prominence" %}', {id:peak_id, prominence:peak_prominence, units:peak_prominence_units},
                    function(data) {
                        $('#save-peak-prominence').prop('disabled', false);
                        $('#save-peak-prominence').html('Save');
                        var output = $("#edit-prominence-message");
                        output.html(data);
                        output.show();
                        setTimeout(function() {
                            $('#edit-prominence').modal('hide');
                        }, 3000)
                    }
            );
        }

        function setProminenceMeters() {
            if ($('#peak-prominence').val() != '') {
                var prominence_feet = parseFloat($('#peak-prominence').val());
                var prominence_meters = Math.floor(prominence_feet * .3048);
                $('#peak-prominence').val(prominence_meters);
            }
            $('#peak-prominence').attr('placeholder','in meters...');
        }

        function setProminenceFeet() {
            if ($('#peak-prominence').val() != '') {
                var prominence_meters = parseFloat($('#peak-prominence').val());
                var prominence_feet = Math.floor(prominence_meters / .3048);
                $('#peak-prominence').val(prominence_feet);
            }
            $('#peak-prominence').attr('placeholder', 'in feet...');
        }

        function admin_update_peak_range() {
            var peak_id = $('#peak-range-id').val();
            var peak_range = $('#peak-range').val();
            $.post('{% url "admin_update_peak_range" %}', {id:peak_id, range:peak_range},
                    function(data) {
                        $('#save-peak-range').prop('disabled', false);
                        $('#save-peak-range').html('Save');
                        var output = $("#edit-range-message");
                        output.html(data);
                        output.show();
                        setTimeout(function() {
                            $('#edit-range').modal('hide');
                        }, 3000)
                    }
            );
        }

        function htmlDecode(value) {
          return $("<textarea/>").html(value).text();
        }

        function updateURLParameter(url, param, paramVal) {

            var newAdditionalURL = "";
            var tempArray = url.split("#");
            var baseURL = tempArray[0];
            var additionalURL = tempArray[1];
            var temp = "";

            if (additionalURL) {
                tempArray = additionalURL.split("&");
                for (i = 0; i < tempArray.length; i++) {
                    if (tempArray[i].split('=')[0] != param) {
                        newAdditionalURL += temp + tempArray[i];
                        temp = "&";
                    }
                }
            }

            if (paramVal === undefined) {
                return newAdditionalURL;
            }

            const rows_txt = temp + "" + param + "=" + paramVal;
            return newAdditionalURL + rows_txt;
        }

        function getRepString (rep) {
          rep = rep+''; // coerce to string
          if (rep < 1000) {
            return rep; // return the same number
          }
          // divide and format
          return (rep/1000).toFixed(rep % 1000 != 0)+'K';
        }

        function delayShowKeywordNear(target, keyword, lat, lng) {
            clearTimeout(timers[target]);
            timers[target] = setTimeout(function() {
                q = keyword;
                u = updateURLParameter(window.location.hash, 'q', q);

                var wanted_classics;
                if ($('#chkMapTagsClassics')[0].checked) {
                    wanted_classics = 'true';
                } else {
                    wanted_classics = 'false';
                }

                var wanted_in_challenge;
                if ($('#chkMapTagsInChallenge')[0].checked) {
                    wanted_in_challenge = 'true';
                } else {
                    wanted_in_challenge = 'false';
                }

                if ($('#chkMapTagsYouClimbed')[0].checked) {
                    u = updateURLParameter('#' + u, 'you_climbed', 'true');
                } else if ($('#chkMapTagsYouNotClimbed')[0].checked) {
                    u = updateURLParameter('#' + u, 'you_climbed', 'false');
                }


                u = updateURLParameter('#' + u, 'lat', lat);
                u = updateURLParameter('#' + u, 'lng', lng);
                u = updateURLParameter('#' + u, 'classics', wanted_classics);
                u = updateURLParameter('#' + u, 'in_challenge', wanted_in_challenge);

                window.location.hash = u;
                loadPeaksFromHash();
            }, 500);
        }

        function delayShowDataelevation(target, values) {
            clearTimeout(timers[target]);
            timers[target] = setTimeout(function() {
                elev_min = ~~values[0];
                elev_max = ~~values[1];
                u = updateURLParameter(window.location.hash, 'elev_min', elev_min);
                u = updateURLParameter('#'+u, 'elev_max', elev_max);
                window.location.hash = u;
                loadPeaksFromHash();
            }, 500);
        }

        function delayShowDataprominence(target, values) {
            clearTimeout(timers[target]);
            timers[target] = setTimeout(function() {
                prominence_min = ~~values[0];
                prominence_max = ~~values[1];
                u = updateURLParameter(window.location.hash, 'prom_min', prominence_min);
                u = updateURLParameter('#'+u, 'prom_max', prominence_max);
                window.location.hash = u;
                loadPeaksFromHash();
            }, 500);
        }

        function delayShowDataSummits(target, values) {
            clearTimeout(timers[target]);
            timers[target] = setTimeout(function() {
                summits_min = ~~values[0];
                summits_max = ~~values[1];
                u = updateURLParameter(window.location.hash, 'summits_min', summits_min);
                u = updateURLParameter('#'+u, 'summits_max', summits_max);
                window.location.hash = u;
                loadPeaksFromHash();
            }, 500);
        }

        function delayShowDataDifficulty(target, values) {
            clearTimeout(timers[target]);
            timers[target] = setTimeout(function() {
                difficulty_min = ~~values[0];
                difficulty_max = ~~values[1];
                u = updateURLParameter(window.location.hash, 'difficulty_min', difficulty_min);
                u = updateURLParameter('#'+u, 'difficulty_max', difficulty_max);
                window.location.hash = u;
                loadPeaksFromHash();
            }, 500);
        }

        function delayShowDataLength(target, values) {
            clearTimeout(timers[target]);
            timers[target] = setTimeout(function() {
                length_min = ~~values[0];
                length_max = ~~values[1];
                u = updateURLParameter(window.location.hash, 'length_min', length_min);
                u = updateURLParameter('#'+u, 'length_max', length_max);
                window.location.hash = u;
                loadPeaksFromHash();
            }, 500);
        }

        function delayShowDataVertical(target, values) {
            clearTimeout(timers[target]);
            timers[target] = setTimeout(function() {
                vertical_min = ~~values[0];
                vertical_max = ~~values[1];
                u = updateURLParameter(window.location.hash, 'vertical_min', vertical_min);
                u = updateURLParameter('#'+u, 'vertical_max', vertical_max);
                window.location.hash = u;
                loadPeaksFromHash();
            }, 500);
        }

        function delayShowDataLastClimbed(target, values) {
            clearTimeout(timers[target]);
            timers[target] = setTimeout(function() {
                last_climbed_min = ~~values[0];
                last_climbed_max = ~~values[1];
                u = updateURLParameter(window.location.hash, 'last_climbed_min', last_climbed_min);
                u = updateURLParameter('#'+u, 'last_climbed_max', last_climbed_max);
                window.location.hash = u;
                loadPeaksFromHash();
            }, 500);
        }

        function delayShowClassics() {
            var wanted_classics;
            if ($('#chkMapTagsClassics')[0].checked) {
                wanted_classics = 'true';
            } else {
                wanted_classics = 'false';
            }
            u = updateURLParameter(window.location.hash, 'classics', wanted_classics);
            u = updateURLParameter('#'+u, 'classics', wanted_classics);
            window.location.hash = u;
            loadPeaksFromHash();
        }

        function delayShowInChallenge() {
            var wanted_in_challenge;
            if ($('#chkMapTagsInChallenge')[0].checked) {
                wanted_in_challenge = 'true';
            } else {
                wanted_in_challenge = 'false';
            }
            u = updateURLParameter(window.location.hash, 'in_challenge', wanted_in_challenge);
            u = updateURLParameter('#'+u, 'in_challenge', wanted_in_challenge);
            window.location.hash = u;
            loadPeaksFromHash();
        }

        function delayShowYouClimbed() {
            var wanted_you_climbed = undefined;
            if ($('#chkMapTagsYouClimbed')[0].checked) {
                wanted_you_climbed = 'true';
                if ($('#chkMapTagsYouNotClimbed')[0].checked === true) {
                    $("#chkMapTagsYouNotClimbed").trigger("click")
                }
            }
            u = updateURLParameter(window.location.hash, 'you_climbed', wanted_you_climbed);
            u = updateURLParameter('#' + u, 'you_climbed', wanted_you_climbed);
            window.location.hash = u;
            loadPeaksFromHash();
        }

        function delayShowYouNotClimbed() {
            var wanted_you_climbed = undefined;
            if ($('#chkMapTagsYouNotClimbed')[0].checked) {
                wanted_you_climbed = 'false';
                if ($('#chkMapTagsYouClimbed')[0].checked === true) {
                    $("#chkMapTagsYouClimbed").trigger("click")
                }
            }
            u = updateURLParameter(window.location.hash, 'you_climbed', wanted_you_climbed);
            u = updateURLParameter('#' + u, 'you_climbed', wanted_you_climbed);
            window.location.hash = u;
            loadPeaksFromHash();
        }

        function numberWithCommas(x) {
            return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        }

        function openUrl(url) {
            window.location.href = url;
        }

        function sortPeaks(sort_key, sort_dir) {
            u = updateURLParameter(window.location.hash, 'sort_key', sort_key);
            u = updateURLParameter('#'+u, 'sort_dir', sort_dir);
            u = updateURLParameter('#' + u, 'page', '1');
            window.location.hash = u;
            loadPeaksFromHash();
        }

        function loadPage(page_number) {
            u = updateURLParameter(window.location.hash, 'page', page_number);
            window.location.hash = u;
            loadPeaksFromHash();
        }

        function resetSliders() {

            var elevationSlider = document.getElementById('slider-range');
            //if elevation in meters
            if ($('#bt_showinmeters').text() == '[ft]') {
                elevationSlider.noUiSlider.set([0, 9000]);
            } else {
                elevationSlider.noUiSlider.set([0, 29500]);
            }

            var prominenceSlider = document.getElementById('prominence-slider-range');
            //if prominence in meters
            if ($('#prominence_bt_showinmeters').text() == '[ft]') {
                prominenceSlider.noUiSlider.set([0, 9000]);
            } else {
                prominenceSlider.noUiSlider.set([0, 29500]);
            }

            var summitsSlider = document.getElementById('summits-slider-range');
            summitsSlider.noUiSlider.set([0, 500]);

            var difficultySlider = document.getElementById('difficulty-slider-range');
            difficultySlider.noUiSlider.set([1, 5]);

            var lengthSlider = document.getElementById('length-slider-range');
            //if elevation in meters
            if ($('#length_bt_showinmeters').text() == '[ft]') {
                lengthSlider.noUiSlider.set([0, 32]);
            } else {
                lengthSlider.noUiSlider.set([0, 20]);
            }

            var verticalSlider = document.getElementById('vertical-slider-range');
            //if elevation in meters
            if ($('#vertical_bt_showinmeters').text() == '[ft]') {
                verticalSlider.noUiSlider.set([0, 3000]);
            } else {
                verticalSlider.noUiSlider.set([0, 10000]);
            }

            var lastClimbedSlider = document.getElementById('last-climbed-slider-range');
            lastClimbedSlider.noUiSlider.set([0, 12]);

            $('#elevation-label').css('color','#aaa');
            $('#amount').css('color','#aaa');
            $('#amount').css('-webkit-text-fill-color','#aaa');
            $('#slider-range > div > div').css('background-color','#ccc');
            $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');

            $('#prominence-label').css('color','#aaa');
            $('#prominence-amount').css('color','#aaa');
            $('#prominence-amount').css('-webkit-text-fill-color','#aaa');
            $('#prominence-slider-range > div > div').css('background-color','#ccc');
            $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');

            $('#summits-label').css('color','#aaa');
            $('#summits-amount').css('color','#aaa');
            $('#summits-amount').css('-webkit-text-fill-color','#aaa');
            $('#summits-slider-range > div > div').css('background-color','#ccc');
            $('#summits-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');

            $('#difficulty-label').css('color','#aaa');
            $('#difficulty-amount').css('color','#aaa');
            $('#difficulty-amount').css('-webkit-text-fill-color','#aaa');
            $('#difficulty-slider-range > div > div').css('background-color','#ccc');
            $('#difficulty-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');

            $('#length-label').css('color','#aaa');
            $('#length-amount').css('color','#aaa');
            $('#length-amount').css('-webkit-text-fill-color','#aaa');
            $('#length-slider-range > div > div').css('background-color','#ccc');
            $('#length-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');

            $('#vertical-label').css('color','#aaa');
            $('#vertical-amount').css('color','#aaa');
            $('#vertical-amount').css('-webkit-text-fill-color','#aaa');
            $('#vertical-slider-range > div > div').css('background-color','#ccc');
            $('#vertical-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');

            $('#last-climbed-label').css('color','#aaa');
            $('#last-climbed-amount').css('color','#aaa');
            $('#last-climbed-amount').css('-webkit-text-fill-color','#aaa');
            $('#last-climbed-slider-range > div > div').css('background-color','#ccc');
            $('#last-climbed-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');

            $( "#amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29500) + " ft");
            $( "#amount-meters" ).val(numberWithCommas(Math.floor(0)) + "  to  " + numberWithCommas(Math.floor(9000)) + " m");

            $( "#prominence-amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29500) + " ft");
            $( "#prominence-amount-meters" ).val(numberWithCommas(Math.floor(0)) + "  to  " + numberWithCommas(Math.floor(9000)) + " m");

            $( "#summits-amount" ).val( "0  to  500+ climbs" );

            $( "#difficulty-amount" ).val( "Class 1 to 5" );

            //$( "#length-amount" ).val( "0  to  20+ mi" );
            $( "#length-amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(20) + "+ mi");
            $( "#length-amount-meters" ).val(numberWithCommas(Math.floor(0)) + "  to  " + numberWithCommas(Math.floor(32)) + "+ km");

            //$( "#vertical-amount" ).val( "0  to  10,000+ ft" );
            $( "#vertical-amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(10000) + "+ ft");
            $( "#vertical-amount-meters" ).val(numberWithCommas(Math.floor(0)) + "  to  " + numberWithCommas(Math.floor(3000)) + "+ m");

            $( "#last-climbed-amount" ).val( "any time" );

            window.location.hash = '';
        }

        function loadPeaksFromHash () {

            var vars = [], hash, keyword, near, range, wanted_elev_min, wanted_elev_max, wanted_prom_min, wanted_prom_max, wanted_summits_min, wanted_summits_max, wanted_difficulty_min, wanted_difficulty_max, wanted_length_min, wanted_length_max, wanted_vertical_min, wanted_vertical_max, wanted_last_climbed_min, wanted_last_climbed_max, classics, in_challenge, you_climbed, lat, lng, sort_key, sort_dir, page;
            var q = document.URL.split('#')[1];
            if(q != undefined){
                q = q.split('&');
                for(var i = 0; i < q.length; i++){
                    hash = q[i].split('=');
                    vars.push(hash[1]);
                    vars[hash[0]] = hash[1];
                }
            }

            if (vars['q'] != undefined) {
                keyword = decodeURIComponent(vars['q']);
            } else {
                keyword = '';
            }

            if (vars['n'] != undefined) {
                near = decodeURIComponent(vars['n']);
            } else {
                near = '';
            }

            if (vars['range'] != undefined) {
                range = vars['range'];
            } else {
                range = '';
            }

            if (vars['elev_min'] != undefined) {
                wanted_elev_min = vars['elev_min'];
            } else {
                wanted_elev_min = '0';
            }

            if (vars['elev_max'] != undefined) {
                wanted_elev_max = vars['elev_max'];
            } else {
                //if elevation in meters
                if ($('#bt_showinmeters').text() == '[ft]') {
                    wanted_elev_max = '29527';
                } else {
                    wanted_elev_max = '29500';
                }
            }

            if (vars['prom_min'] != undefined) {
                wanted_prom_min = vars['prom_min'];
            } else {
                wanted_prom_min = '0';
            }

            if (vars['prom_max'] != undefined) {
                wanted_prom_max = vars['prom_max'];
            } else {
                //if prominence in meters
                if ($('#prominence_bt_showinmeters').text() == '[ft]') {
                    wanted_prom_max = '29527';
                } else {
                    wanted_prom_max = '29500';
                }
            }

            if (vars['summits_min'] != undefined) {
                wanted_summits_min = vars['summits_min'];
            } else {
                wanted_summits_min = '0';
            }

            if (vars['summits_max'] != undefined) {
                wanted_summits_max = vars['summits_max'];
            } else {
                wanted_summits_max = '500';
            }

            if (vars['difficulty_min'] != undefined) {
                wanted_difficulty_min = vars['difficulty_min'];
            } else {
                wanted_difficulty_min = '1';
            }

            if (vars['difficulty_max'] != undefined) {
                wanted_difficulty_max = vars['difficulty_max'];
            } else {
                wanted_difficulty_max = '5';
            }

            if (vars['length_min'] != undefined) {
                wanted_length_min = vars['length_min'];
            } else {
                wanted_length_min = '0';
            }

            if (vars['length_max'] != undefined) {
                wanted_length_max = vars['length_max'];
            } else {
                wanted_length_max = '20';
            }

            if (vars['vertical_min'] != undefined) {
                wanted_vertical_min = vars['vertical_min'];
            } else {
                wanted_vertical_min = '0';
            }

            if (vars['vertical_max'] != undefined) {
                wanted_vertical_max = vars['vertical_max'];
            } else {
                wanted_vertical_max = '10000';
            }

            if (vars['last_climbed_min'] != undefined) {
                wanted_last_climbed_min = vars['last_climbed_min'];
            } else {
                wanted_last_climbed_min = '0';
            }

            if (vars['last_climbed_max'] != undefined) {
                wanted_last_climbed_max = vars['last_climbed_max'];
            } else {
                wanted_last_climbed_max = '11';
            }

            if (vars['classics'] != undefined) {
                classics = vars['classics'];
            } else {
                classics = 'false';
            }

            if (vars['in_challenge'] != undefined) {
                in_challenge = vars['in_challenge'];
            } else {
                in_challenge = 'false';
            }

            if (vars['you_climbed'] != undefined) {
                you_climbed = vars['you_climbed'];
            } else {
                you_climbed = undefined;
            }

            if (vars['lat'] != undefined) {
                lat = vars['lat'];
            } else {
                if ('{{ lat }}' != '') {
                    lat = '{{ lat }}';
                } else {
                    lat = '';
                }
            }

            if (vars['lng'] != undefined) {
                lng = vars['lng'];
            } else {
                if ('{{ lon }}' != '') {
                    lng = '{{ lon }}';
                } else {
                    lng = '';
                }
            }

            if (vars['sort_key'] != undefined) {
                sort_key = vars['sort_key'];
            } else {
                sort_key = 'summits';
            }

            if (vars['sort_dir'] != undefined) {
                sort_dir = vars['sort_dir'];
            } else {
                sort_dir = 'desc';
            }

            if (vars['page'] != undefined) {
                page = vars['page'];
            } else {
                page = '1';
            }

            loadPeaks(keyword, near, range, wanted_elev_min, wanted_elev_max, wanted_prom_min, wanted_prom_max, wanted_summits_min, wanted_summits_max, wanted_difficulty_min, wanted_difficulty_max, wanted_length_min, wanted_length_max, wanted_vertical_min, wanted_vertical_max, wanted_last_climbed_min, wanted_last_climbed_max, classics, in_challenge, you_climbed, lat, lng, sort_key, sort_dir, page);

        }

        function loadPeaks(keyword, near, range, wanted_elev_min, wanted_elev_max, wanted_prom_min, wanted_prom_max, wanted_summits_min, wanted_summits_max, wanted_difficulty_min, wanted_difficulty_max, wanted_length_min, wanted_length_max, wanted_vertical_min, wanted_vertical_max, wanted_last_climbed_min, wanted_last_climbed_max, classics, in_challenge, you_climbed, lat, lng, sort_key, sort_dir, page) {

            //hide search modal
            //$('#peak-search').modal('hide');

            //set hash for map page link
            $('#navbar-link-map a').attr('href','/map/'+window.location.hash);

            near = $('.mapboxgl-ctrl-geocoder--input').val();
            $('#hdnNear').val(near);

            //set title
            if (keyword != '' && near == '') {
                document.title = '"'+keyword + '" mountains';
            } else if (keyword == '' && near != '') {
                document.title = 'Mountains near "'+near+'"';
            } else if (keyword != '' && near != '') {
                document.title = '"' + keyword + '" mountains near "' + near + '"';
            }

            //if elevation in meters
            if ($('#bt_showinmeters').text() == '[ft]') {
                if (wanted_elev_min != 0 || wanted_elev_max != 29527) {
                    $('#elevation-label').css('color', '#f24100');
                    $('#amount').css('color', '#f24100');
                    $('#amount').css('-webkit-text-fill-color', '#f24100');
                    $('#slider-range > div > div').css('background-color', '#f24100');
                    $('#slider-range .noUi-background').css('background-color', '#CCC');
                    $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                }
            } else {
                if (wanted_elev_min != 0 || wanted_elev_max != 29500) {
                    $('#elevation-label').css('color', '#f24100');
                    $('#amount').css('color', '#f24100');
                    $('#amount').css('-webkit-text-fill-color', '#f24100');
                    $('#slider-range > div > div').css('background-color', '#f24100');
                    $('#slider-range .noUi-background').css('background-color', '#CCC');
                    $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                }
            }

            //if prominence in meters
            if ($('#prominence_bt_showinmeters').text() == '[ft]') {
                if (wanted_prom_min != 0 || wanted_prom_max != 29527) {
                    $('#prominence-label').css('color', '#f24100');
                    $('#prominence-amount').css('color', '#f24100');
                    $('#prominence-amount').css('-webkit-text-fill-color', '#f24100');
                    $('#prominence-slider-range > div > div').css('background-color', '#f24100');
                    $('#prominence-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                }
            } else {
                if (wanted_prom_min != 0 || wanted_prom_max != 29500) {
                    $('#prominence-label').css('color', '#f24100');
                    $('#prominence-amount').css('color', '#f24100');
                    $('#prominence-amount').css('-webkit-text-fill-color', '#f24100');
                    $('#prominence-slider-range > div > div').css('background-color', '#f24100');
                    $('#prominence-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                }
            }

            if (wanted_summits_min != 0 || wanted_summits_max != 500) {
                $('#summits-label').css('color','#f24100');
                $('#summits-amount').css('color','#f24100');
                $('#summits-amount').css('-webkit-text-fill-color', '#f24100');
                $('#summits-slider-range > div > div').css('background-color','#f24100');
                $('#summits-slider-range .noUi-background').css('background-color', '#CCC');
                $('#summits-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
            }

            if (wanted_difficulty_min != 1 || wanted_difficulty_max != 5) {
                $('#difficulty-label').css('color','#f24100');
                $('#difficulty-amount').css('color','#f24100');
                $('#difficulty-amount').css('-webkit-text-fill-color', '#f24100');
                $('#difficulty-slider-range > div > div').css('background-color','#f24100');
                $('#difficulty-slider-range .noUi-background').css('background-color', '#CCC');
                $('#difficulty-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
            }

            //if length in meters
            if ($('#length_bt_showinmeters').text() == '[ft]') {
                if (wanted_length_min != 0 || wanted_length_max != 20) {
                    $('#length-label').css('color','#f24100');
                    $('#length-amount').css('color','#f24100');
                    $('#length-amount').css('-webkit-text-fill-color', '#f24100');
                    $('#length-slider-range > div > div').css('background-color','#f24100');
                    $('#length-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#length-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                }
            } else {
                if (wanted_length_min != 0 || wanted_length_max != 20) {
                    $('#length-label').css('color','#f24100');
                    $('#length-amount').css('color','#f24100');
                    $('#length-amount').css('-webkit-text-fill-color', '#f24100');
                    $('#length-slider-range > div > div').css('background-color','#f24100');
                    $('#length-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#length-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                }
            }

            //if vertical in meters
            if ($('#vertical_bt_showinmeters').text() == '[ft]') {
                if (wanted_vertical_min != 0 || wanted_vertical_max != 9842) {
                    $('#vertical-label').css('color','#f24100');
                    $('#vertical-amount').css('color','#f24100');
                    $('#vertical-amount').css('-webkit-text-fill-color', '#f24100');
                    $('#vertical-slider-range > div > div').css('background-color','#f24100');
                    $('#vertical-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#vertical-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                }
            } else {
                if (wanted_vertical_min != 0 || wanted_vertical_max != 10000) {
                    $('#vertical-label').css('color','#f24100');
                    $('#vertical-amount').css('color','#f24100');
                    $('#vertical-amount').css('-webkit-text-fill-color', '#f24100');
                    $('#vertical-slider-range > div > div').css('background-color','#f24100');
                    $('#vertical-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#vertical-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                }
            }

            if (wanted_last_climbed_min != 0 || wanted_last_climbed_max != 11) {
                $('#last-climbed-label').css('color','#f24100');
                $('#last-climbed-amount').css('color','#f24100');
                $('#last-climbed-amount').css('-webkit-text-fill-color', '#f24100');
                $('#last-climbed-slider-range > div > div').css('background-color','#f24100');
                $('#last-climbed-slider-range .noUi-background').css('background-color', '#CCC');
                $('#last-climbed-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
            }

            var rowClass = '';
            var sortName = '';
            var sortElev = '';
            var sortProm = '';
            var sortRange = '';
            var sortDist = '';
            var sortSummits = '';
            var sortLast = '';
            var sortNameOpp = 'asc';
            var sortElevOpp = 'desc';
            var sortPromOpp = 'desc';
            var sortRangeOpp = 'desc';
            var sortDistOpp = 'desc';
            var sortSummitsOpp = 'desc';
            var sortLastOpp = 'desc';
            if (sort_key == 'name') {
                sortName = 'sorted';
                if (sort_dir == 'asc') {
                    sortNameOpp = 'desc';
                } else {
                    sortNameOpp = 'asc';
                }
            }
            if (sort_key == 'elevation') {
                sortElev = 'sorted';
                if (sort_dir == 'asc') {
                    sortElevOpp = 'desc';
                } else {
                    sortElevOpp = 'asc';
                }
            }
            if (sort_key == 'prominence') {
                sortProm = 'sorted';
                if (sort_dir == 'asc') {
                    sortPromOpp = 'desc';
                } else {
                    sortPromOpp = 'asc';
                }
            }
            if (sort_key == 'range') {
                sortRange = 'sorted';
                if (sort_dir == 'asc') {
                    sortRangeOpp = 'desc';
                } else {
                    sortRangeOpp = 'asc';
                }
            }
            if (sort_key == 'distance') {
                sortDist = 'sorted';
                if (sort_dir == 'asc') {
                    sortDistOpp = 'desc';
                } else {
                    sortDistOpp = 'asc';
                }
            }
            if (sort_key == 'summits') {
                sortSummits = 'sorted';
                if (sort_dir == 'asc') {
                    sortSummitsOpp = 'desc';
                } else {
                    sortSummitsOpp = 'asc';
                }
            }
            if (sort_key == 'last') {
                sortLast = 'sorted';
                if (sort_dir == 'asc') {
                    sortLastOpp = 'desc';
                } else {
                    sortLastOpp = 'asc';
                }
            }

            var counter = 0;
            var strTemp = '';

            $('#peaks-table-head').empty();
            $('#peaks-table-body').empty();
            $("#peaks-table").floatThead('reflow');
            $('#ajax-data-loading').css('display', 'inline');
            $('#no-peaks-found').addClass('hidden-md');
            $('#no-peaks-found').addClass('hidden-lg');
            $('#pagination').css('display','none');
            var params = '';
            if (keyword.length > 0) params = params + '&q=' + keyword;
            if (near.length > 0) params = params + '&n=' + near;
            if (range.length > 0) params = params + '&range=' + range;
            if (wanted_elev_min.length > 0) params = params + '&elev_min=' + wanted_elev_min;
            if (wanted_elev_max.length > 0) params = params + '&elev_max=' + wanted_elev_max;
            if (wanted_prom_min.length > 0) params = params + '&prom_min=' + wanted_prom_min;
            if (wanted_prom_max.length > 0) params = params + '&prom_max=' + wanted_prom_max;
            if (wanted_summits_min.length > 0) params = params + '&summits_min=' + wanted_summits_min;
            if (wanted_summits_max.length > 0) params = params + '&summits_max=' + wanted_summits_max;
            if (wanted_difficulty_min.length > 0) params = params + '&difficulty_min=' + wanted_difficulty_min;
            if (wanted_difficulty_max.length > 0) params = params + '&difficulty_max=' + wanted_difficulty_max;
            if (wanted_length_min.length > 0) params = params + '&length_min=' + wanted_length_min;
            if (wanted_length_max.length > 0) params = params + '&length_max=' + wanted_length_max;
            if (wanted_vertical_min.length > 0) params = params + '&vertical_min=' + wanted_vertical_min;
            if (wanted_vertical_max.length > 0) params = params + '&vertical_max=' + wanted_vertical_max;
            if (wanted_last_climbed_min.length > 0) params = params + '&last_climbed_min=' + wanted_last_climbed_min;
            if (wanted_last_climbed_max.length > 0) params = params + '&last_climbed_max=' + wanted_last_climbed_max;
            if (classics.length > 0) params = params + '&classics=' + classics;
            if (in_challenge.length > 0) params = params + '&in_challenge=' + in_challenge;
            if (you_climbed !== undefined) params = params + '&you_climbed=' + you_climbed;
            if (lat.length > 0) params = params + '&lat=' + lat;
            if (lng.length > 0) params = params + '&lng=' + lng;
            params = params + '&sort_key=' + sort_key;
            params = params + '&sort_dir=' + sort_dir;
            params = params + '&page=' + page;

            //set mobile title
            if (keyword.length > 0 && near.length > 0) {
                $('#mobile-search-title').html('"' + keyword + '" peaks near ' + near);
            } else if (keyword.length > 0) {
                $('#mobile-search-title').html('"' + keyword + '" peaks');
            } else if (near.length > 0) {
                $('#mobile-search-title').html('Peaks near ' + near);
            } else if (lat.length > 0 && lng.length > 0) {
                if (lat != 'None' && lng != 'None') {
                    $('#mobile-search-title').html('Peaks near you');
                }
            } else {
                $('#mobile-search-title').html('All peaks');
            }

            //update hidden parameters
            $('#hdnKeyword').val(keyword);
            if (keyword.length > 0) {
                $('#q').val(keyword);
                $('#clear_peak_name').html('<i class="fa fa-times"></i>');
                $('#clear_peak_name').show();
            }
            $('#hdnNear').val(near);
            if (near.length > 0) {
                $('#n').val(near);
                $('#clear_near_location').html('<i class="fa fa-times"></i>');
                $('#clear_near_location').show();
            }
            $('#hdnElevMin').val(wanted_elev_min);
            $('#hdnElevMax').val(wanted_elev_max);
            $('#hdnPromMin').val(wanted_prom_min);
            $('#hdnPromMax').val(wanted_prom_max);
            $('#hdnSummitsMin').val(wanted_summits_min);
            $('#hdnSummitsMax').val(wanted_summits_max);
            $('#hdnDifficultyMin').val(wanted_difficulty_min);
            $('#hdnDifficultyMax').val(wanted_difficulty_max);

            $('#hdnLengthMin').val(wanted_length_min);
            $('#hdnLengthMax').val(wanted_length_max);
            $('#hdnVerticalMin').val(wanted_vertical_min);
            $('#hdnVerticalMax').val(wanted_vertical_max);
            $('#hdnLastClimbedMin').val(wanted_last_climbed_min);
            $('#hdnLastClimbedMax').val(wanted_last_climbed_max);

            $('#hdnClassics').val(classics);
            $('#hdnInChallenge').val(in_challenge);
            $('#hdnYouClimbed').val(you_climbed);

            if (lat != '' && lat.toLowerCase() != 'none') {
                $('#hdnLat').val(lat);
            }
            if (lng != '' && lng.toLowerCase() != 'none') {
                $('#hdnLng').val(lng);
            }
            $('#hdnSortKey').val(sort_key);
            $('#hdnSortDir').val(sort_dir);
            $('#hdnPage').val(page);

            //set toggle filters
            if (classics == 'true') {
                $('#chkMapTagsClassics').next().animate({
                        backgroundColor: '#f24100',
                    },200);
                $('#chkMapTagsClassics').next().css('color','#fff');
                $('#chkMapTagsClassics').prop('checked', true);
            }
            if (in_challenge == 'true') {
                $('#chkMapTagsInChallenge').next().animate({
                        backgroundColor: '#f24100',
                    },200);
                $('#chkMapTagsInChallenge').next().css('color','#fff');
                $('#chkMapTagsInChallenge').prop('checked', true);
            }
            if (you_climbed == 'true') {
                $('#chkMapTagsYouClimbed').next().animate({
                        backgroundColor: '#10b025',
                    },200);
                $('#chkMapTagsYouClimbed').next().css('color','#fff');
                $('#chkMapTagsYouClimbed').prop('checked', true);
            } else if (you_climbed == 'false') {
                $('#chkMapTagsYouNotClimbed').next().animate({
                    backgroundColor: '#f24100',
                }, 200);
                $('#chkMapTagsYouNotClimbed').next().css('color', '#fff');
                $('#chkMapTagsYouNotClimbed').prop('checked', true);
            }

            var mobileFilters = '';

            if (params.length > 0) params = '?' + params.slice(-1*(params.length-1));
            var byRegion = false;
            var totalPeaks = 0;
            var totalPages = 0;
            var search_status = '';
            window.scrollTo(0, 0);
            $.getJSON('{% url "peaks_search" %}' + params , function(data) {
                $.each( data, function( key, val ) {
                    var currentRequest = true;
                    if (key=='parameters') {
                        $.each( val, function( parameterkey, parameterval ) {
                            $('#hdnNearTxt').html(parameterval.near);
                            search_status = parameterval.status;
                            if (htmlDecode(parameterval.keyword) != $('#hdnKeyword').val()) currentRequest = false;
                            //if ($('#hdnNearTxt').val().toLowerCase() != $('#hdnNear').val().toLowerCase()) currentRequest = false;
                            if (parameterval.elev_min != $('#hdnElevMin').val()) currentRequest = false;
                            if (parameterval.elev_max != $('#hdnElevMax').val()) currentRequest = false;
                            if (parameterval.prom_min != $('#hdnPromMin').val()) currentRequest = false;
                            if (parameterval.prom_max != $('#hdnPromMax').val()) currentRequest = false;
                            if (parameterval.summits_min != $('#hdnSummitsMin').val()) currentRequest = false;
                            if (parameterval.summits_max != $('#hdnSummitsMax').val()) currentRequest = false;
                            if (parameterval.difficulty_min != $('#hdnDifficultyMin').val()) currentRequest = false;
                            if (parameterval.difficulty_max != $('#hdnDifficultyMax').val()) currentRequest = false;

                            if (parameterval.length_min != $('#hdnLengthMin').val()) currentRequest = false;
                            if (parameterval.length_max != $('#hdnLengthMax').val()) currentRequest = false;
                            if (parameterval.vertical_min != $('#hdnVerticalMin').val()) currentRequest = false;
                            if (parameterval.vertical_max != $('#hdnVerticalMax').val()) currentRequest = false;
                            if (parameterval.last_climbed_min != $('#hdnLastClimbedMin').val()) currentRequest = false;
                            if (parameterval.last_climbed_max != $('#hdnLastClimbedMax').val()) currentRequest = false;

                            if (parameterval.classics != $('#hdnClassics').val()) currentRequest = false;
                            if (parameterval.in_challenge != $('#hdnInChallenge').val()) currentRequest = false;
                            if (parameterval.you_climbed != $('#hdnYouClimbed').val()) currentRequest = false;

                            if (parameterval.lat != $('#hdnLat').val()) currentRequest = false;
                            if (parameterval.lng != $('#hdnLng').val()) currentRequest = false;
                            if (parameterval.sort != $('#hdnSortKey').val()) currentRequest = false;
                            if (parameterval.sort_dir != $('#hdnSortDir').val()) currentRequest = false;
                            if (parameterval.page != $('#hdnPage').val()) currentRequest = false;
                            if (parameterval.state_id != '0') {
                                byRegion = true;
                                //update hash to reflect that results are sorted by summits rather than distance
                                u = updateURLParameter(window.location.hash, 'sort_key', 'summits');
                                u = updateURLParameter('#'+u, 'sort_dir', 'desc');
                                window.location.hash = u;
                                sortSummits = 'sorted';
                                sortSummitsOpp = 'asc';
                            }
                            totalPages = Math.ceil(parseInt(parameterval.peak_count)/40);
                            totalPeaks = parameterval.peak_count;

                            //set up filter description for mobile header
                            if ($('#bt_showinmeters').text() == '[ft]') {
                                var elevMinMeters = Math.ceil(parseFloat(parameterval.elev_min) * 0.3048);
                                var elevMaxMeters = Math.ceil(parseFloat(parameterval.elev_max) * 0.3048);
                                if (parameterval.elev_max != '29527') {
                                    mobileFilters = mobileFilters + getRepString(elevMinMeters.toString()) + '-' + getRepString(elevMaxMeters.toString()) + ' m elev &bull; ';
                                } else if (parameterval.elev_min != '0') {
                                    mobileFilters = mobileFilters + getRepString(elevMinMeters.toString()) + '+ m elev &bull; ';
                                }
                            } else {
                                if (parameterval.elev_max != '29500') {
                                    mobileFilters = mobileFilters + getRepString(parameterval.elev_min) + '-' + getRepString(parameterval.elev_max) + ' ft elev &bull; ';
                                } else if (parameterval.elev_min != '0') {
                                    mobileFilters = mobileFilters + getRepString(parameterval.elev_min) + '+ ft elev &bull; ';
                                }
                            }
                            if ($('#prominence_bt_showinmeters').text() == '[ft]') {
                                var elevMinMeters = Math.ceil(parseFloat(parameterval.elev_min) * 0.3048);
                                var elevMaxMeters = Math.ceil(parseFloat(parameterval.elev_max) * 0.3048);
                                if (parameterval.prom_max != '29527') {
                                    mobileFilters = mobileFilters + getRepString(elevMinMeters.toString()) + '-' + getRepString(elevMaxMeters.toString()) + ' m prom &bull; ';
                                } else if (parameterval.prom_min != '0') {
                                    mobileFilters = mobileFilters + getRepString(elevMinMeters.toString()) + '+ m prom &bull; ';
                                }
                            } else {
                                if (parameterval.prom_max != '29500') {
                                    mobileFilters = mobileFilters + getRepString(parameterval.prom_min) + '-' + getRepString(parameterval.prom_max) + ' ft prom &bull; ';
                                } else if (parameterval.prom_min != '0') {
                                    mobileFilters = mobileFilters + getRepString(parameterval.prom_min) + '+ ft prom &bull; ';
                                }
                            }
                            if (parameterval.summits_max != '500') {
                                mobileFilters = mobileFilters + parameterval.summits_min + '-' + parameterval.summits_max + ' summits &bull; ';
                            } else if (parameterval.summits_min != '0') {
                                mobileFilters = mobileFilters + parameterval.summits_min + '+ summits &bull; ';
                            }
                            if (parameterval.difficulty_max != '5') {
                                mobileFilters = mobileFilters + 'Class ' + parameterval.difficulty_min + '-' + parameterval.difficulty_max + ' &bull; ';
                            } else if (parameterval.difficulty_min != '1') {
                                mobileFilters = mobileFilters + 'Class ' + parameterval.difficulty_min + '+ &bull; ';
                            }

                        });
                    }

                    if (!currentRequest) {
                        return false;
                    }

                    //set mobile title
                    var mobileTitle = '';
                    if (keyword.length > 0 && near.length > 0) {
                        mobileTitle = '"' + keyword + '" peaks near ' + near;
                    } else if (keyword.length > 0) {
                        mobileTitle = '"' + keyword + '" peaks';
                    } else if (near.length > 0) {
                        mobileTitle = 'Peaks near ' + near;
                    } else if (lat.length > 0 && lng.length > 0) {
                        if (lat != 'None' && lng != 'None') {
                            mobileTitle = 'Peaks near you';
                        } else {
                            mobileTitle = 'All peaks';
                        }
                    } else {
                        mobileTitle = 'All peaks';
                    }
                    if (mobileFilters != '') {
                        mobileTitle = mobileTitle + '<br /><span class="mobile-header-subtitle">' + mobileFilters.slice(0, -8) + '</span>';
                    }
                    $('#mobile-search-title').html(mobileTitle);

                    if (key=='peaks') {

                        var havePeaks = false;

                        $.each( val, function( peakkey, peakval ) {
                            havePeaks = true;

                            if (counter % 2 == 0) {
                                rowClass = 'bg';
                            } else {
                                rowClass = '';
                            }

                            //build country string
                            var country = '';
                            $.each( peakval.country, function( countrykey, countryval ) {
                                country = country + '<div><a href="' + countryval.country_slug + '/">' + countryval.country_name + '</a></div>';
                            });
                            if (country == '') {
                                country = 'Unknown Country';
                            }

                            //build region string
                            var region = '';
                            var mobile_region = '';
                            var region_bull_class = 'hidden-lg hidden-md hidden-sm hidden-xs';
                            $.each( peakval.region, function( regionkey, regionval ) {
                                region_bull_class = '';
                                region = region + '<div style="margin-bottom: 5px;"><a class="truncate" href="' + regionval.region_slug + '">' + regionval.region_name + ', ' + regionval.country_name + '</a></div>';
                                mobile_region = '<div style="margin-bottom: 5px;"><a class="truncate" href="' + regionval.region_slug + '">' + regionval.country_name + '</a></div>';
                            });
                            if (region == '') {
                                region = country;
                                mobile_region = country;
                            }

                            //build challenges string
                            var challenges = '';
                            var extra_challenges = '';
                            var challenge_counter = 0;
                            $.each( peakval.group, function( groupkey, groupval ) {
                                if (challenge_counter == 0) {
                                    challenges = challenges + '<div style="margin-bottom: 5px;"><a class="truncate" href="' + groupval.group_slug + '">' + groupval.group_name + '</a></div>';
                                } else {
                                    extra_challenges = extra_challenges + '<div style="margin-bottom: 5px;"><a class="truncate" href="' + groupval.group_slug + '">' + groupval.group_name + '</a></div>';
                                }
                                challenge_counter++;
                            });
                            if (challenge_counter > 1) {
                                challenge_counter--;
                                challenges = challenges + '<div><a class="extra-challenge-link" data-peakid="' + peakval.id + '">+ ' + challenge_counter + ' more</a></div>';
                                challenges = challenges.replace('class="truncate"','class="truncate-one"');
                            }

                            var showImperial = ''
                            var showMetric = '';
                            if ($('#gm-custom-mapunitsbutton-label-feet').hasClass('gm-custom-mapunits-selected')) {
                                showMetric = 'display: none;';
                            } else {
                                showImperial = 'display: none;';
                            }

                            //buld distance string
                            var distance = '';
                            if (near.length > 0) {
                                var miles = Math.round(peakval.miles_away);
                                var km = Math.round(peakval.miles_away*1.60934);
                                distance = '<span class="peak-elevation-feet" style="' + showImperial + '">' + miles.toString() + ' mi</span><span class="peak-elevation-meters" style="' + showMetric + '">' + km.toString() + ' km</span>';
                            }

                            //build summits string
                            var summits;
                            if (peakval.summit_count > 0) {
                                summits = '<a href="/' + peakval.slug + '/summits/">' + peakval.summit_count + '</a>';
                            } else {
                                summits = '<a href="/' + peakval.slug + '/summits/">0</a>';
                            }
                            if (peakval.your_summits > 0) {
                                summits = summits + '<br /><div id="peak-your-summits-badge" style="width: 40px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00b524; font-size: 9px; color: #fff; float: left; text-align: center; line-height: 20px;"><i class="fa fa-check" style="filter: drop-shadow(0px 0px 1px #333); float: left; margin-left: 7px; line-height: 20px;"></i> ' + peakval.your_summits + 'x</div>';
                            }

                            var classic = '';
                            if (peakval.is_classic == 'True') {
                                classic = '<div id="peak-classic-badge" style="width: 65px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00a7ee; font-size: 8px; color: #fff; float: left; text-align: center; line-height: 20px; margin-top: -25px;"><i class="fa fa-star" style="float: left; margin-left: 4px; margin-top: 4px; line-height: 16px; filter: drop-shadow(0px 0px 1px #333); color: #feeeb9; background-color: #00a7ee; width: 18px; border-radius: 5px;"></i></div>';
                            }

                            strTemp = '<tr class="' + rowClass + '" style="height: 60px;">';
                            strTemp = strTemp + '<td data-url="/' + peakval.slug + '" class="table-hover-cell peak_avatar peak-row" style="border-left: none; vertical-align: middle; padding: 0px; width: 80px;"><a href="/' + peakval.slug + '/"><img class="hover-photos" style="height: 60px; width: 80px;" src="{{ MEDIA_URL }}' + peakval.thumbnail_url + '" alt="' + peakval.name + '"/>' + classic + '</a></td>';
                            strTemp = strTemp + '<td data-url="/' + peakval.slug + '" class="table-hover-cell peak_avatar peak-row" style="vertical-align: middle; padding: 0px; padding-left: 5px; padding-right: 5px;"><a class="truncate" href="/' + peakval.slug + '/">' + peakval.name + '</a></td>';
                            strTemp = strTemp + '<td data-url="/' + peakval.slug + '" class="table-hover-cell peak-row" style="vertical-align: middle;"><div style="margin-bottom: 5px; white-space: nowrap;"><span class="peak-elevation-feet" style="' + showImperial + '">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span class="peak-elevation-meters" style="' + showMetric + '">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span></div><div style="white-space: nowrap;"><a data-toggle="modal" data-target="#edit-elevation" data-peakid="' + peakval.id + '" data-elevation="' + Math.round(peakval.elevation) + '" class="admin-edit-elevation desktop-admin-link hidden-xs hidden-sm hidden-md">edit</a></div></td>';
                            if (Math.round(peakval.prominence) > 0) {
                                strTemp = strTemp + '<td data-url="/' + peakval.slug + '" class="table-hover-cell hidden-xs peak-row" style="vertical-align: middle;"><div style="margin-bottom: 5px;"><span class="peak-elevation-feet" style="' + showImperial + '">' + numberWithCommas(Math.round(peakval.prominence)) + ' ft</span><span class="peak-elevation-meters" style="' + showMetric + '">' + numberWithCommas(Math.round(peakval.prominence * .3048)) + ' m</span></div><div><a data-toggle="modal" data-target="#edit-prominence" data-peakid="' + peakval.id + '" data-prominence="' + Math.round(peakval.prominence) + '" class="admin-edit-prominence desktop-admin-link hidden-xs hidden-sm hidden-md">edit</a></div></td>';
                            } else {
                                strTemp = strTemp + '<td data-url="/' + peakval.slug + '" class="table-hover-cell hidden-xs peak-row" style="vertical-align: middle;"><div style="margin-bottom: 5px;">&nbsp;<a data-toggle="modal" data-target="#edit-prominence" data-peakid="' + peakval.id + '" data-prominence="" class="admin-edit-prominence desktop-admin-link hidden-xs hidden-sm hidden-md">edit</a></div></td>';
                            }
                            strTemp = strTemp + '<td class="table-hover-cell" style="vertical-align: middle;">' + region + '</td>';
                            if (near.length > 0 && byRegion == false) {
                                strTemp = strTemp + '<td style="vertical-align: middle;" class="table-hover-cell hidden-xs">' + distance + '</td>';
                            }
                            strTemp = strTemp + '<td data-url="/peaks/?range=' + escape(peakval.range) + '" class="table-hover-cell hidden-xs truncate peak-row" style="vertical-align: middle;"><a href="/peaks/?range=' + escape(peakval.range) + '">' + peakval.range + '</a> <a data-toggle="modal" data-target="#edit-range" data-peakid="' + peakval.id + '" data-range="' + peakval.range + '" class="admin-edit-range desktop-admin-link hidden-xs hidden-sm hidden-md">edit</a></td>';
                            strTemp = strTemp + '<td class="table-hover-cell hidden-xs" style="vertical-align: middle;">' + challenges + '<div id="extra-challenges-' + peakval.id + '" style="display: none;">' + extra_challenges + '</div>{% if request.user.is_superuser and latest_challenge_added %}{% for c in latest_challenge_added %}<div id="admin-edit-challenge-' + peakval.id + '-div" class="admin-edit-challenges desktop-admin-link hidden-xs hidden-sm hidden-md" style="color: #f24100;" data-peakid="' + peakval.id + '" data-challengeid="{{ c.id }}"><input type="checkbox" id="admin-edit-challenge-' + peakval.id + '"> {{ c.name }}</div>{% endfor %}{% endif %}</td>';
                            strTemp = strTemp + '<td data-url="/' + peakval.slug + '/summits/" class="table-hover-cell peak-row" style="vertical-align: middle;">' + summits + '</td>';
                            strTemp = strTemp + '<td data-url="/' + peakval.slug + '/summits/' + peakval.last_summit_id + '/" class="table-hover-cell hidden-xs peak-row" style="vertical-align: middle; white-space: nowrap;"><a href="/' + peakval.slug + '/summits/' + peakval.last_summit_id + '/"><time class="timeago" datetime="' + peakval.last_summit_date + 'T00:00:00">' + peakval.last_summit_date + '</time></a></td>';
                            strTemp = strTemp + '</tr>';
                            $('#peaks-table-body').append(strTemp);
                            counter ++;
                        });
                        if (!havePeaks) {
                            $('#ajax-data-loading').css('display', 'none');
                            if (search_status == 'no_location') {
                                $('#mobile-search-title').html('No location found for "<span style="color: #ff0000;">' + near + '</span>"');
                                $('#no-peaks-message').html('No location found for ');
                                $('#no-peaks-query').html(near);
                                $('#add-missing-peak').hide();
                            } else {
                                $('#mobile-search-title').html('No peaks found for "<span style="color: #ff0000;">' + keyword + '</span>"');
                                $('#no-peaks-message').html('No peaks found for ');
                                $('#no-peaks-query').html(keyword);
                                $('#add-missing-peak').show();
                            }
                            $('#no-peaks-found').removeClass('hidden-md');
                            $('#no-peaks-found').removeClass('hidden-lg');
                            $("#peaks-table").floatThead('reflow');
                        } else {

                            $('#ajax-data-loading').css('display', 'none');
                            strTemp = '<tr class="' + sort_dir + '" style="height: 50px;">';
                            strTemp = strTemp + '<th style="border-left: none; cursor: pointer;" onclick="sortPeaks(\'name\', \'' + sortNameOpp + '\');" colspan="2" class="sortable ' + sortName + '" sortkey="name"><a>Peak name</a></th>';
                            strTemp = strTemp + '<th style="cursor: pointer;" onclick="sortPeaks(\'elevation\', \'' + sortElevOpp + '\');" class="sortable ' + sortElev + '" sortkey="elevation"><a>Elev<span class="hidden-xs">ation</span></a></th>';
                            strTemp = strTemp + '<th style="cursor: pointer;" onclick="sortPeaks(\'prominence\', \'' + sortPromOpp + '\');" class="hidden-xs sortable ' + sortProm + '" sortkey="prominence"><a>Prominence</a></th>';
                            strTemp = strTemp + '<th>Region</th>';
                            if (near.length > 0 && byRegion == false) {
                                strTemp = strTemp + '<th style="cursor: pointer;" onclick="sortPeaks(\'distance\', \'' + sortDistOpp + '\');" class="hidden-xs sortable ' + sortDist + '" sortkey="distance"><a>Distance</a></th>';
                            }
                            strTemp = strTemp + '<th style="cursor: pointer;" onclick="sortPeaks(\'range\', \'' + sortRangeOpp + '\');" class="hidden-xs sortable ' + sortRange + '" sortkey="range"><a>Range</a></th>';
                            strTemp = strTemp + '<th class="hidden-xs">Challenges</th>';
                            strTemp = strTemp + '<th style="cursor: pointer;" onclick="sortPeaks(\'summits\', \'' + sortSummitsOpp + '\');" class="sortable ' + sortSummits + '" sortkey="summits"><a class="hidden-xs">Climbs</a><a class="hidden-sm hidden-md hidden-lg">Logs</a></th>';
                            strTemp = strTemp + '<th style="cursor: pointer;" onclick="sortPeaks(\'last\', \'' + sortLastOpp + '\');" class="hidden-xs sortable ' + sortLast + '" sortkey="last"><a>Last</a></th>';
                            strTemp = strTemp + '</tr>';
                            $('#peaks-table-head').append(strTemp);

                            $('#peaks-table').show();
                            //show pagination
                            var paginationHtml = '';
                            if (parseInt(page) == totalPages) {
                                paginationHtml = '<div style="display: inline-block; height: 55px;"><a href="javascript:loadPage(' + (parseInt(page)-1).toString() + ');"><img src="{% static 'img/font-awesome_4-7-0_arrow-circle-left_256_0_00b1f2_none.png' %}" style="height: 26px; margin-bottom: 5px;"></a></div><div class="current" style="margin-left: 20px; margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">peaks ' + ((parseInt(page)-1)*40+1).toString() + ' - ' + numberWithCommas(totalPeaks) + ' of ' + numberWithCommas(totalPeaks) + '</div>';
                            } else if (parseInt(page) > 1) {
                                paginationHtml = '<div style="display: inline-block; height: 55px;"><a href="javascript:loadPage(' + (parseInt(page) - 1).toString() + ');"><img src="{% static 'img/font-awesome_4-7-0_arrow-circle-left_256_0_00b1f2_none.png' %}" style="height: 26px; margin-bottom: 5px;"></a></div><div class="current" style="margin-left: 20px; margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">peaks ' + ((parseInt(page) - 1) * 40 + 1).toString() + ' - ' + ((parseInt(page)) * 40).toString() + ' of ' + numberWithCommas(totalPeaks) + '</div>';
                            } else {
                                paginationHtml = '<div class="current" style="margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">peaks 1 - 40 of ' + numberWithCommas(totalPeaks) + '</div>';
                            }
                            if (parseInt(page) < totalPages) {
                                paginationHtml = paginationHtml + '<div style="display: inline-block; height: 55px;"><a href="javascript:loadPage(' + (parseInt(page) + 1).toString() + ');"><img src="{% static 'img/font-awesome_4-7-0_arrow-circle-right_256_0_00b1f2_none.png'%}" style="height: 26px; margin-bottom: 5px;"></i></a></div>';
                                $('#pagination-pages').html(paginationHtml);
                                $('#pagination').css('display', 'inherit');
                            } else {
                                $('#pagination-pages').html(paginationHtml);
                                $('#pagination').css('display', 'inherit');
                            }
                            $("time.timeago").timeago();
                            /*$("#peaks-table").floatThead({
                                position: 'auto',
                                top: floatHeadMargin
                            });*/
                            $("#peaks-table").floatThead('reflow');
                            setAdminMode();
                            //$("#peaks-table").scrollTop(0);
                            //$('.truncate').truncate({
                            //  lines: 2,
                            //  lineHeight: 12
                            //});
                            //$('.truncate-one').truncate({
                            //  lines: 1,
                            //  lineHeight: 12
                            //});
                        }
                    }
                });
            });

        }

        function setSliderUnits(units) {

            if (units == null) {
                units = 'feet';
            }

            //elevation
            var $this = $('#bt_showinmeters');
            var elevationSlider = document.getElementById('slider-range');
            if (units == 'feet') {
                $('#amount-meters').hide();
                $('#amount').show();

                $this.removeClass('meters');
                $this.text('[m]');

                //reset elevation slider
                elevationSlider.noUiSlider.updateOptions({
                    range: {
                        'min': 0,
                        '3.3%': 500,
                        '5.0%': 1000,
                        '6.7%': 1500,
                        '8.3%': 2000,
                        '10.0%': 2500,
                        '11.7%': 3000,
                        '13.3%': 3500,
                        '15.0%': 4000,
                        '16.7%': 4500,
                        '18.3%': 5000,
                        '20.0%': 5500,
                        '21.7%': 6000,
                        '23.3%': 6500,
                        '25.0%': 7000,
                        '26.7%': 7500,
                        '28.3%': 8000,
                        '30.0%': 8500,
                        '31.7%': 9000,
                        '33.3%': 9500,
                        '35.0%': 10000,
                        '36.7%': 10500,
                        '38.3%': 11000,
                        '40.0%': 11500,
                        '41.7%': 12000,
                        '43.3%': 12500,
                        '45.0%': 13000,
                        '46.7%': 13500,
                        '48.3%': 14000,
                        '50.0%': 14500,
                        '51.7%': 15000,
                        '53.3%': 15500,
                        '55.0%': 16000,
                        '56.7%': 16500,
                        '58.3%': 17000,
                        '60.0%': 17500,
                        '61.7%': 18000,
                        '63.3%': 18500,
                        '65.0%': 19000,
                        '66.7%': 19500,
                        '68.3%': 20000,
                        '70.0%': 20500,
                        '71.7%': 21000,
                        '73.3%': 21500,
                        '75.0%': 22000,
                        '76.7%': 22500,
                        '78.3%': 23000,
                        '80.0%': 23500,
                        '81.7%': 24000,
                        '83.3%': 24500,
                        '85.0%': 25000,
                        '86.7%': 25500,
                        '88.3%': 26000,
                        '90.0%': 26500,
                        '91.7%': 27000,
                        '93.3%': 27500,
                        '95.0%': 28000,
                        '96.7%': 28500,
                        '98.3%': 29000,
                        'max': 29500
                    }
                });
                elevationSlider.noUiSlider.set([0, 29500]);
                $('#elevation-label').css('color','#aaa');
                $('#amount').css('color','#aaa');
                $('#amount').css('-webkit-text-fill-color','#aaa');
                $('#slider-range > div > div').css('background-color','#ccc');
                $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
                $( "#amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29500) + " ft");
                $( "#amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(9000) + " m");
                u = updateURLParameter(window.location.hash, 'elev_min', 0);
                u = updateURLParameter('#'+u, 'elev_max', 29500);
                window.location.hash = u;
                //loadPeaksFromHash();
            } else {
                $('#amount').hide();
                $('#amount-meters').show();

                $this.addClass('meters');
                $this.text('[ft]');

                //reset elevation slider
                elevationSlider.noUiSlider.updateOptions({
                    range: {
                        'min': 0,
                        '4.3%': 200,
                        '6.5%': 400,
                        '8.7%': 600,
                        '10.9%': 800,
                        '13.0%': 1000,
                        '15.2%': 1200,
                        '17.4%': 1400,
                        '19.6%': 1600,
                        '21.7%': 1800,
                        '23.9%': 2000,
                        '26.1%': 2200,
                        '28.3%': 2400,
                        '30.4%': 2600,
                        '32.6%': 2800,
                        '34.8%': 3000,
                        '37.0%': 3200,
                        '39.1%': 3400,
                        '41.3%': 3600,
                        '43.5%': 3800,
                        '45.7%': 4000,
                        '47.8%': 4200,
                        '50.0%': 4400,
                        '52.2%': 4600,
                        '54.3%': 4800,
                        '56.5%': 5000,
                        '58.7%': 5200,
                        '60.9%': 5400,
                        '63.0%': 5600,
                        '65.2%': 5800,
                        '67.4%': 6000,
                        '69.6%': 6200,
                        '71.7%': 6400,
                        '73.9%': 6600,
                        '76.1%': 6800,
                        '78.3%': 7000,
                        '80.4%': 7200,
                        '82.6%': 7400,
                        '84.8%': 7600,
                        '87.0%': 7800,
                        '89.1%': 8000,
                        '91.3%': 8200,
                        '93.5%': 8400,
                        '95.7%': 8600,
                        '97.8%': 8800,
                        'max': 9000
                    }
                });
                elevationSlider.noUiSlider.set([0, 9000]);
                $('#elevation-label').css('color','#aaa');
                $('#amount').css('color','#aaa');
                $('#amount').css('-webkit-text-fill-color','#aaa');
                $('#slider-range > div > div').css('background-color','#ccc');
                $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
                $( "#amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29527) + " ft");
                $( "#amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(9000) + " m");
                u = updateURLParameter(window.location.hash, 'elev_min', 0);
                u = updateURLParameter('#'+u, 'elev_max', 29527);
                window.location.hash = u;
                //loadPeaksFromHash();
            }

            //prominence
            var $this = $('#prominence_bt_showinmeters');
            var prominenceSlider = document.getElementById('prominence-slider-range');
            if (units == 'feet') {
                $('#prominence-amount-meters').hide();
                $('#prominence-amount').show();

                $this.removeClass('meters');
                $this.text('[m]');

                //reset prominence slider
                prominenceSlider.noUiSlider.updateOptions({
                    range: {
                        'min': 0,
                        '3.1%': 50,
                        '4.7%': 100,
                        '6.3%': 200,
                        '7.8%': 300,
                        '9.4%': 500,
                        '10.9%': 1000,
                        '12.5%': 1500,
                        '14.1%': 2000,
                        '15.6%': 2500,
                        '17.2%': 3000,
                        '18.8%': 3500,
                        '20.3%': 4000,
                        '21.9%': 4500,
                        '23.4%': 5000,
                        '25.0%': 5500,
                        '26.6%': 6000,
                        '28.1%': 6500,
                        '29.7%': 7000,
                        '31.3%': 7500,
                        '32.8%': 8000,
                        '34.4%': 8500,
                        '35.9%': 9000,
                        '37.5%': 9500,
                        '39.1%': 10000,
                        '40.6%': 10500,
                        '42.2%': 11000,
                        '43.8%': 11500,
                        '45.3%': 12000,
                        '46.9%': 12500,
                        '48.4%': 13000,
                        '50.0%': 13500,
                        '51.6%': 14000,
                        '53.1%': 14500,
                        '54.7%': 15000,
                        '56.3%': 15500,
                        '57.8%': 16000,
                        '59.4%': 16500,
                        '60.9%': 17000,
                        '62.5%': 17500,
                        '64.1%': 18000,
                        '65.6%': 18500,
                        '67.2%': 19000,
                        '68.8%': 19500,
                        '70.3%': 20000,
                        '71.9%': 20500,
                        '73.4%': 21000,
                        '75.0%': 21500,
                        '76.6%': 22000,
                        '78.1%': 22500,
                        '79.7%': 23000,
                        '81.3%': 23500,
                        '82.8%': 24000,
                        '84.4%': 24500,
                        '85.9%': 25000,
                        '87.5%': 25500,
                        '89.1%': 26000,
                        '90.6%': 26500,
                        '92.2%': 27000,
                        '93.8%': 27500,
                        '95.3%': 28000,
                        '96.9%': 28500,
                        '98.4%': 29000,
                        'max': 29500
                    }
                });
                prominenceSlider.noUiSlider.set([0, 29500]);
                $('#prominence-label').css('color','#aaa');
                $('#prominence-amount').css('color','#aaa');
                $('#prominence-amount').css('-webkit-text-fill-color','#aaa');
                $('#prominence-slider-range > div > div').css('background-color','#ccc');
                $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
                $( "#prominence-amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29500) + " ft");
                $( "#prominence-amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(9000) + " m");
                u = updateURLParameter(window.location.hash, 'prom_min', 0);
                u = updateURLParameter('#'+u, 'prom_max', 29500);
                window.location.hash = u;
                //loadPeaksFromHash();
            } else {
                $('#prominence-amount').hide();
                $('#prominence-amount-meters').show();

                $this.addClass('meters');
                $this.text('[ft]');

                //reset prominence slider
                prominenceSlider.noUiSlider.updateOptions({
                    range: {
                        'min': 0,
                        '2.1%': 10,
                        '3.2%': 20,
                        '4.2%': 30,
                        '5.3%': 50,
                        '6.3%': 100,
                        '7.4%': 200,
                        '8.4%': 300,
                        '9.5%': 400,
                        '10.5%': 500,
                        '11.6%': 600,
                        '12.6%': 700,
                        '13.7%': 800,
                        '14.7%': 900,
                        '15.8%': 1000,
                        '16.8%': 1100,
                        '17.9%': 1200,
                        '18.9%': 1300,
                        '20.0%': 1400,
                        '21.1%': 1500,
                        '22.1%': 1600,
                        '23.2%': 1700,
                        '24.2%': 1800,
                        '25.3%': 1900,
                        '26.3%': 2000,
                        '27.4%': 2100,
                        '28.4%': 2200,
                        '29.5%': 2300,
                        '30.5%': 2400,
                        '31.6%': 2500,
                        '32.6%': 2600,
                        '33.7%': 2700,
                        '34.7%': 2800,
                        '35.8%': 2900,
                        '36.8%': 3000,
                        '37.9%': 3100,
                        '38.9%': 3200,
                        '40.0%': 3300,
                        '41.1%': 3400,
                        '42.1%': 3500,
                        '43.2%': 3600,
                        '44.2%': 3700,
                        '45.3%': 3800,
                        '46.3%': 3900,
                        '47.4%': 4000,
                        '48.4%': 4100,
                        '49.5%': 4200,
                        '50.5%': 4300,
                        '51.6%': 4400,
                        '52.6%': 4500,
                        '53.7%': 4600,
                        '54.7%': 4700,
                        '55.8%': 4800,
                        '56.8%': 4900,
                        '57.9%': 5000,
                        '58.9%': 5100,
                        '60.0%': 5200,
                        '61.1%': 5300,
                        '62.1%': 5400,
                        '63.2%': 5500,
                        '64.2%': 5600,
                        '65.3%': 5700,
                        '66.3%': 5800,
                        '67.4%': 5900,
                        '68.4%': 6000,
                        '69.5%': 6100,
                        '70.5%': 6200,
                        '71.6%': 6300,
                        '72.6%': 6400,
                        '73.7%': 6500,
                        '74.7%': 6600,
                        '75.8%': 6700,
                        '76.8%': 6800,
                        '77.9%': 6900,
                        '78.9%': 7000,
                        '80.0%': 7100,
                        '81.1%': 7200,
                        '82.1%': 7300,
                        '83.2%': 7400,
                        '84.2%': 7500,
                        '85.3%': 7600,
                        '86.3%': 7700,
                        '87.4%': 7800,
                        '88.4%': 7900,
                        '89.5%': 8000,
                        '90.5%': 8100,
                        '91.6%': 8200,
                        '92.6%': 8300,
                        '93.7%': 8400,
                        '94.7%': 8500,
                        '95.8%': 8600,
                        '96.8%': 8700,
                        '97.9%': 8800,
                        '98.9%': 8900,
                        'max': 9000
                    }
                });
                prominenceSlider.noUiSlider.set([0, 9000]);
                $('#prominence-label').css('color','#aaa');
                $('#prominence-amount').css('color','#aaa');
                $('#prominence-amount').css('-webkit-text-fill-color','#aaa');
                $('#prominence-slider-range > div > div').css('background-color','#ccc');
                $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
                $( "#prominence-amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29527) + " ft");
                $( "#prominence-amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(9000) + " m");
                u = updateURLParameter(window.location.hash, 'prom_min', 0);
                u = updateURLParameter('#'+u, 'prom_max', 29527);
                window.location.hash = u;
                //loadPeaksFromHash();
            }

            //length
            var $this = $('#length_bt_showinmeters');
            var lengthSlider = document.getElementById('length-slider-range');
            if (units == 'feet') {
                $('#length-amount-meters').hide();
                $('#length-amount').show();

                $this.removeClass('meters');
                $this.text('[m]');

                //reset length slider
                lengthSlider.noUiSlider.updateOptions({
                    range: {
                        'min': 0,
                        '5.0%': 1,
                        '10.0%': 2,
                        '15.0%': 3,
                        '20.0%': 4,
                        '25.0%': 5,
                        '30.0%': 6,
                        '35.0%': 7,
                        '40.0%': 8,
                        '45.0%': 9,
                        '50.0%': 10,
                        '55.0%': 11,
                        '60.0%': 12,
                        '65.0%': 13,
                        '70.0%': 14,
                        '75.0%': 15,
                        '80.0%': 16,
                        '85.0%': 17,
                        '90.0%': 18,
                        '95.0%': 19,
                        'max': 20
                    }
                });
                lengthSlider.noUiSlider.set([0, 20]);
                $('#length-label').css('color','#aaa');
                $('#length-amount').css('color','#aaa');
                $('#length-amount').css('-webkit-text-fill-color','#aaa');
                $('#length-slider-range > div > div').css('background-color','#ccc');
                $('#length-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
                $( "#length-amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(20) + "+ mi");
                $( "#length-amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(32) + "+ km");
                u = updateURLParameter(window.location.hash, 'length_min', 0);
                u = updateURLParameter('#'+u, 'length_max', 20);
                window.location.hash = u;
                //loadPeaksFromHash();
            } else {
                $('#length-amount').hide();
                $('#length-amount-meters').show();

                $this.addClass('meters');
                $this.text('[ft]');

                //reset length slider
                lengthSlider.noUiSlider.updateOptions({
                    range: {
                        'min': 0,
                        '3.0%': 1,
                        '6.0%': 2,
                        '9.0%': 3,
                        '12.0%': 4,
                        '15.0%': 5,
                        '18.0%': 6,
                        '21.0%': 7,
                        '24.0%': 8,
                        '27.0%': 9,
                        '30.0%': 10,
                        '33.0%': 11,
                        '36.0%': 12,
                        '39.0%': 13,
                        '42.0%': 14,
                        '45.0%': 15,
                        '48.0%': 16,
                        '51.0%': 17,
                        '54.0%': 18,
                        '57.0%': 19,
                        '60.0%': 20,
                        '63.0%': 21,
                        '66.0%': 22,
                        '69.0%': 23,
                        '72.0%': 24,
                        '75.0%': 25,
                        '78.0%': 26,
                        '81.0%': 27,
                        '84.0%': 28,
                        '87.0%': 29,
                        '90.0%': 30,
                        '95.0%': 31,
                        'max': 32
                    }
                });
                lengthSlider.noUiSlider.set([0, 32]);
                $('#length-label').css('color','#aaa');
                $('#length-amount').css('color','#aaa');
                $('#length-amount').css('-webkit-text-fill-color','#aaa');
                $('#length-slider-range > div > div').css('background-color','#ccc');
                $('#length-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
                $( "#length-amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(20) + "+ mi");
                $( "#length-amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(32) + "+ km");
                u = updateURLParameter(window.location.hash, 'length_min', 0);
                u = updateURLParameter('#'+u, 'length_max', 20);
                window.location.hash = u;
                //loadPeaksFromHash();
            }

            //vertical
            var $this = $('#vertical_bt_showinmeters');
            var verticalSlider = document.getElementById('vertical-slider-range');
            if (units == 'feet') {
                $('#vertical-amount-meters').hide();
                $('#vertical-amount').show();

                $this.removeClass('meters');
                $this.text('[m]');

                //reset vertical slider
                verticalSlider.noUiSlider.updateOptions({
                    range: {
                        'min': 0,
                        '9.0%': 500,
                        '18.0%': 1000,
                        '27.0%': 2000,
                        '36.0%': 3000,
                        '45.0%': 4000,
                        '54.0%': 5000,
                        '63.0%': 6000,
                        '72.0%': 7000,
                        '81.0%': 8000,
                        '90.0%': 9000,
                        'max': 10000
                    }
                });
                verticalSlider.noUiSlider.set([0, 10000]);
                $('#vertical-label').css('color','#aaa');
                $('#vertical-amount').css('color','#aaa');
                $('#vertical-amount').css('-webkit-text-fill-color','#aaa');
                $('#vertical-slider-range > div > div').css('background-color','#ccc');
                $('#vertical-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
                $( "#vertical-amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(10000) + "+ ft");
                $( "#vertical-amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(3000) + "+ m");
                u = updateURLParameter(window.location.hash, 'vertical_min', 0);
                u = updateURLParameter('#'+u, 'vertical_max', 10000);
                window.location.hash = u;
                //loadPeaksFromHash();
            } else {
                $('#vertical-amount').hide();
                $('#vertical-amount-meters').show();

                $this.addClass('meters');
                $this.text('[ft]');

                //reset vertical slider
                verticalSlider.noUiSlider.updateOptions({
                    range: {
                        'min': 0,
                        '10.0%': 300,
                        '20.0%': 600,
                        '30.0%': 900,
                        '40.0%': 1200,
                        '50.0%': 1500,
                        '60.0%': 1800,
                        '70.0%': 2100,
                        '80.0%': 2400,
                        '90.0%': 2700,
                        'max': 3000
                    }
                });
                verticalSlider.noUiSlider.set([0, 3000]);
                $('#vertical-label').css('color','#aaa');
                $('#vertical-amount').css('color','#aaa');
                $('#vertical-amount').css('-webkit-text-fill-color','#aaa');
                $('#vertical-slider-range > div > div').css('background-color','#ccc');
                $('#vertical-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
                $( "#vertical-amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(9842) + "+ ft");
                $( "#vertical-amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(3000) + "+ m");
                u = updateURLParameter(window.location.hash, 'vertical_min', 0);
                u = updateURLParameter('#'+u, 'vertical_max', 9842);
                window.location.hash = u;
                //loadPeaksFromHash();
            }

        }

        function toggleMapUnits(units) {
            if (units != '' && units != null) {
                if (units == 'feet') {
                    createCookie('map_units', 'feet', 365);
                    $('#gm-custom-mapunitsbutton-label-meters').removeClass('gm-custom-mapunits-selected');
                    $('#gm-custom-mapunitsbutton-label-meters').addClass('gm-custom-mapunits-unselected');
                    $('#gm-custom-mapunitsbutton-label-feet').addClass('gm-custom-mapunits-selected');
                    $('#gm-custom-mapunitsbutton-label-feet').removeClass('gm-custom-mapunits-unselected');
                } else {
                    createCookie('map_units', 'meters', 365);
                    $('#gm-custom-mapunitsbutton-label-feet').removeClass('gm-custom-mapunits-selected');
                    $('#gm-custom-mapunitsbutton-label-feet').addClass('gm-custom-mapunits-unselected');
                    $('#gm-custom-mapunitsbutton-label-meters').addClass('gm-custom-mapunits-selected');
                    $('#gm-custom-mapunitsbutton-label-meters').removeClass('gm-custom-mapunits-unselected');
                }
            }
        }

        $(document).ready(function(){

            var viewport_height = $(window).height();
            var viewport_width = $(window).width();
            var footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
            var data_loading_margin = (viewport_height - footer_height) / 2;
            if (viewport_width < 1024) {
                floatHeadMargin = 99;
            }
            $('.data-loading').css('margin-top', data_loading_margin);
            //$('.data-loading').css('margin-bottom', data_loading_margin);
            $(window).resize(function() {
                viewport_height = $(window).height();
                viewport_width = $(window).width();
                if (viewport_width < 1024) {
                    floatHeadMargin = 99;
                } else {
                    floatHeadMargin = 50;
                }
                footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
                data_loading_margin = (viewport_height - footer_height) / 2;
                $('.data-loading').css('margin-top', data_loading_margin);
                //$('.data-loading').css('margin-bottom', data_loading_margin);
            });

            var vars = [], hash, keyword, near, range, wanted_elev_min, wanted_elev_max, wanted_prom_min, wanted_prom_max, wanted_summits_min, wanted_summits_max, wanted_difficulty_min, wanted_difficulty_max, wanted_length_min, wanted_length_max, wanted_vertical_min, wanted_vertical_max, wanted_last_climbed_min, wanted_last_climbed_max, classics, in_challenge, you_climbed, lat, lng, sort_key, sort_dir, page;

            //Look for querystring values
            var q = document.URL.split('?')[1];
            if(q != undefined){
                q = q.split('&');
                for(var i = 0; i < q.length; i++){
                    hash = q[i].split('=');
                    vars.push(hash[1]);
                    vars[hash[0]] = hash[1];
                }
            }

            if (vars['q'] != undefined) {
                keyword = decodeURIComponent(vars['q']);
            } else {
                keyword = '';
            }

            if (vars['n'] != undefined) {
                near = decodeURIComponent(vars['n']);
            } else {
                near = '';
            }

            //Look for url hash values
            var q = document.URL.split('#')[1];
            if(q != undefined){
                q = q.split('&');
                for(var i = 0; i < q.length; i++){
                    hash = q[i].split('=');
                    vars.push(hash[1]);
                    vars[hash[0]] = hash[1];
                }
            }

            if (vars['q'] != undefined) {
                keyword = decodeURIComponent(vars['q']);
            }

            if (vars['n'] != undefined) {
                near = decodeURIComponent(vars['n']);
            }

            if (vars['range'] != undefined) {
                range = vars['range'];
            } else {
                range = '';
            }

            if (vars['elev_min'] != undefined) {
                wanted_elev_min = vars['elev_min'];
            } else {
                wanted_elev_min = '0';
            }

            if (vars['elev_max'] != undefined) {
                wanted_elev_max = vars['elev_max'];
            } else {
                //if elevation in meters
                if ($('#bt_showinmeters').text() == '[ft]') {
                    wanted_elev_max = '29527';
                } else {
                    wanted_elev_max = '29500';
                }
            }

            if (vars['prom_min'] != undefined) {
                wanted_prom_min = vars['prom_min'];
            } else {
                wanted_prom_min = '0';
            }

            if (vars['prom_max'] != undefined) {
                wanted_prom_max = vars['prom_max'];
            } else {
                //if prominence in meters
                if ($('#prominence_bt_showinmeters').text() == '[ft]') {
                    wanted_prom_max = '29527';
                } else {
                    wanted_prom_max = '29500';
                }
            }

            if (vars['summits_min'] != undefined) {
                wanted_summits_min = vars['summits_min'];
            } else {
                wanted_summits_min = '0';
            }

            if (vars['summits_max'] != undefined) {
                wanted_summits_max = vars['summits_max'];
            } else {
                wanted_summits_max = '500';
            }

            if (vars['difficulty_min'] != undefined) {
                wanted_difficulty_min = vars['difficulty_min'];
            } else {
                wanted_difficulty_min = '1';
            }

            if (vars['difficulty_max'] != undefined) {
                wanted_difficulty_max = vars['difficulty_max'];
            } else {
                wanted_difficulty_max = '5';
            }

            if (vars['length_min'] != undefined) {
                wanted_length_min = vars['length_min'];
            } else {
                wanted_length_min = '0';
            }

            if (vars['length_max'] != undefined) {
                wanted_length_max = vars['length_max'];
            } else {
                wanted_length_max = '20';
            }

            if (vars['vertical_min'] != undefined) {
                wanted_vertical_min = vars['vertical_min'];
            } else {
                wanted_vertical_min = '0';
            }

            if (vars['vertical_max'] != undefined) {
                wanted_vertical_max = vars['vertical_max'];
            } else {
                wanted_vertical_max = '10000';
            }

            if (vars['last_climbed_min'] != undefined) {
                wanted_last_climbed_min = vars['last_climbed_min'];
            } else {
                wanted_last_climbed_min = '0';
            }

            if (vars['last_climbed_max'] != undefined) {
                wanted_last_climbed_max = vars['last_climbed_max'];
            } else {
                wanted_last_climbed_max = '11';
            }

            if (vars['classics'] != undefined) {
                classics = vars['classics'];
            } else {
                classics = 'false';
            }

            if (vars['in_challenge'] != undefined) {
                in_challenge = vars['in_challenge'];
            } else {
                in_challenge = 'false';
            }

            if (vars['you_climbed'] != undefined) {
                you_climbed = vars['you_climbed'];
            } else {
                you_climbed = undefined;
            }

            if (vars['lat'] != undefined) {
                lat = vars['lat'];
            } else {
                if ('{{ lat }}' != '' && '{{ lat }}' != 'None') {
                    lat = '{{ lat }}';
                } else {
                    lat = '';
                }
            }

            if (vars['lng'] != undefined) {
                lng = vars['lng'];
            } else {
                if ('{{ lon }}' != '' && '{{ lon }}' != 'None') {
                    lng = '{{ lon }}';
                } else {
                    lng = '';
                }
            }

            if (vars['sort_key'] != undefined) {
                sort_key = vars['sort_key'];
            } else {
                sort_key = 'summits';
            }

            if (vars['sort_dir'] != undefined) {
                sort_dir = vars['sort_dir'];
            } else {
                sort_dir = 'desc';
            }

            if (vars['page'] != undefined) {
                page = vars['page'];
            } else {
                page = '1';
            }

            //switch map units
            $("#gm-custom-mapunits").click(function(){
                //toggle map units
                if ($('#gm-custom-mapunitsbutton-label-meters').hasClass('gm-custom-mapunits-selected')) {
                    toggleMapUnits('feet');
                    setSliderUnits('feet');
                } else {
                    toggleMapUnits('meters');
                    setSliderUnits('meters');
                }
                loadPeaksFromHash();
            });

            //Set up the sliders
            var elevationSlider = document.getElementById('slider-range');

            noUiSlider.create(elevationSlider, {
                start: [ 0, 29500 ],
                snap: true,
                connect: true,
                start: [wanted_elev_min, wanted_elev_max],
                range: {
                    'min': 0,
                    '3.3%': 500,
                    '5.0%': 1000,
                    '6.7%': 1500,
                    '8.3%': 2000,
                    '10.0%': 2500,
                    '11.7%': 3000,
                    '13.3%': 3500,
                    '15.0%': 4000,
                    '16.7%': 4500,
                    '18.3%': 5000,
                    '20.0%': 5500,
                    '21.7%': 6000,
                    '23.3%': 6500,
                    '25.0%': 7000,
                    '26.7%': 7500,
                    '28.3%': 8000,
                    '30.0%': 8500,
                    '31.7%': 9000,
                    '33.3%': 9500,
                    '35.0%': 10000,
                    '36.7%': 10500,
                    '38.3%': 11000,
                    '40.0%': 11500,
                    '41.7%': 12000,
                    '43.3%': 12500,
                    '45.0%': 13000,
                    '46.7%': 13500,
                    '48.3%': 14000,
                    '50.0%': 14500,
                    '51.7%': 15000,
                    '53.3%': 15500,
                    '55.0%': 16000,
                    '56.7%': 16500,
                    '58.3%': 17000,
                    '60.0%': 17500,
                    '61.7%': 18000,
                    '63.3%': 18500,
                    '65.0%': 19000,
                    '66.7%': 19500,
                    '68.3%': 20000,
                    '70.0%': 20500,
                    '71.7%': 21000,
                    '73.3%': 21500,
                    '75.0%': 22000,
                    '76.7%': 22500,
                    '78.3%': 23000,
                    '80.0%': 23500,
                    '81.7%': 24000,
                    '83.3%': 24500,
                    '85.0%': 25000,
                    '86.7%': 25500,
                    '88.3%': 26000,
                    '90.0%': 26500,
                    '91.7%': 27000,
                    '93.3%': 27500,
                    '95.0%': 28000,
                    '96.7%': 28500,
                    '98.3%': 29000,
                    'max': 29500
                }
            });

            elevationSlider.noUiSlider.on('slide', function(values, handle, unencoded, tap, positions){
                //if meters
                if ($('#amount-meters').is(':visible')) {
                    if (unencoded[0] == unencoded[1]) {
                        $("#amount").val(numberWithCommas(unencoded[1]) + " ft");
                        $("#amount-meters").val(numberWithCommas(Math.floor(unencoded[1])) + " m");
                    } else {
                        $("#amount").val(numberWithCommas(unencoded[0]) + "  to  " + numberWithCommas(unencoded[1]) + " ft");
                        $("#amount-meters").val(numberWithCommas(Math.floor(unencoded[0])) + "  to  " + numberWithCommas(Math.floor(unencoded[1])) + " m");
                    }
                    if (0 != unencoded[0] || 9000 != unencoded[1]) {
                        $('#elevation-label').css('color', '#f24100');
                        $('.ammount').css('color', '#f24100');
                        $('.ammount').css('-webkit-text-fill-color', '#f24100');
                        $('#slider-range > div > div').css('background-color', '#f24100');
                        $('#slider-range .noUi-background').css('background-color', '#CCC');
                        $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                    } else {
                        $('#elevation-label').css('color', '#aaa');
                        $('.ammount').css('color', '#aaa');
                        $('.ammount').css('-webkit-text-fill-color', '#aaa');
                        $('#slider-range > div > div').css('background-color', '#ccc');
                        $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
                    }
                } else {
                    if (unencoded[0] == 29500) {
                        $("#amount").val(numberWithCommas(unencoded[1]) + " ft");
                        $("#amount-meters").val(numberWithCommas(Math.floor(unencoded[1])) + " m");
                    } else {
                        $("#amount").val(numberWithCommas(unencoded[0]) + "  to  " + numberWithCommas(unencoded[1]) + " ft");
                        $("#amount-meters").val(numberWithCommas(Math.floor(unencoded[0])) + "  to  " + numberWithCommas(Math.floor(unencoded[1])) + " m");
                    }
                    if (0 != unencoded[0] || 29500 != unencoded[1]) {
                        $('#elevation-label').css('color', '#f24100');
                        $('.ammount').css('color', '#f24100');
                        $('.ammount').css('-webkit-text-fill-color', '#f24100');
                        $('#slider-range > div > div').css('background-color', '#f24100');
                        $('#slider-range .noUi-background').css('background-color', '#CCC');
                        $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                    } else {
                        $('#elevation-label').css('color', '#aaa');
                        $('.ammount').css('color', '#aaa');
                        $('.ammount').css('-webkit-text-fill-color', '#aaa');
                        $('#slider-range > div > div').css('background-color', '#ccc');
                        $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
                    }
                }
            });
            elevationSlider.noUiSlider.on('end', function(values, handle, unencoded, tap, positions){
                //if meters
                if ($('#bt_showinmeters').text() == '[ft]') {
                    delayShowDataelevation("#amount", [unencoded[0]/0.3048,unencoded[1]/0.3048]);
                } else {
                    delayShowDataelevation("#amount", unencoded);
                }
            });
            var elevationValues = elevationSlider.noUiSlider.get();
            if ($('#amount-meters').is(':visible')) {
                if (elevationValues[0] == elevationValues[1]) {
                    $("#amount").val(numberWithCommas(parseInt(elevationValues[1])) + " ft");
                    $("#amount-meters").val(numberWithCommas(parseInt(Math.floor(elevationValues[1]))) + " m");
                } else {
                    $("#amount").val(numberWithCommas(parseInt(elevationValues[0])) + "  to  " + numberWithCommas(parseInt(elevationValues[1])) + " ft");
                    $("#amount-meters").val(numberWithCommas(Math.floor(parseInt(elevationValues[0]))) + "  to  " + numberWithCommas(parseInt(Math.floor(elevationValues[1]))) + " m");
                }
            } else {
                if (elevationValues[0] == elevationValues[1]) {
                    $("#amount").val(numberWithCommas(parseInt(elevationValues[1])) + " ft");
                    $("#amount-meters").val(numberWithCommas(parseInt(Math.floor(elevationValues[1]))) + " m");
                } else {
                    $("#amount").val(numberWithCommas(parseInt(elevationValues[0])) + "  to  " + numberWithCommas(parseInt(elevationValues[1])) + " ft");
                    $("#amount-meters").val(numberWithCommas(Math.floor(parseInt(elevationValues[0]))) + "  to  " + numberWithCommas(parseInt(Math.floor(elevationValues[1]))) + " m");
                }
            }


            var prominenceSlider = document.getElementById('prominence-slider-range');

            noUiSlider.create(prominenceSlider, {
                start: [ 0, 29500 ],
                snap: true,
                connect: true,
                start: [wanted_prom_min, wanted_prom_max],
                range: {
                    'min': 0,
                    '3.1%': 50,
                    '4.7%': 100,
                    '6.3%': 200,
                    '7.8%': 300,
                    '9.4%': 500,
                    '10.9%': 1000,
                    '12.5%': 1500,
                    '14.1%': 2000,
                    '15.6%': 2500,
                    '17.2%': 3000,
                    '18.8%': 3500,
                    '20.3%': 4000,
                    '21.9%': 4500,
                    '23.4%': 5000,
                    '25.0%': 5500,
                    '26.6%': 6000,
                    '28.1%': 6500,
                    '29.7%': 7000,
                    '31.3%': 7500,
                    '32.8%': 8000,
                    '34.4%': 8500,
                    '35.9%': 9000,
                    '37.5%': 9500,
                    '39.1%': 10000,
                    '40.6%': 10500,
                    '42.2%': 11000,
                    '43.8%': 11500,
                    '45.3%': 12000,
                    '46.9%': 12500,
                    '48.4%': 13000,
                    '50.0%': 13500,
                    '51.6%': 14000,
                    '53.1%': 14500,
                    '54.7%': 15000,
                    '56.3%': 15500,
                    '57.8%': 16000,
                    '59.4%': 16500,
                    '60.9%': 17000,
                    '62.5%': 17500,
                    '64.1%': 18000,
                    '65.6%': 18500,
                    '67.2%': 19000,
                    '68.8%': 19500,
                    '70.3%': 20000,
                    '71.9%': 20500,
                    '73.4%': 21000,
                    '75.0%': 21500,
                    '76.6%': 22000,
                    '78.1%': 22500,
                    '79.7%': 23000,
                    '81.3%': 23500,
                    '82.8%': 24000,
                    '84.4%': 24500,
                    '85.9%': 25000,
                    '87.5%': 25500,
                    '89.1%': 26000,
                    '90.6%': 26500,
                    '92.2%': 27000,
                    '93.8%': 27500,
                    '95.3%': 28000,
                    '96.9%': 28500,
                    '98.4%': 29000,
                    'max': 29500
                }
            });

            prominenceSlider.noUiSlider.on('slide', function(values, handle, unencoded, tap, positions){
                //if meters
                if ($('#prominence-amount-meters').is(':visible')) {
                    if (unencoded[0] == unencoded[1]) {
                        $( "#prominence-amount" ).val(numberWithCommas(unencoded[1]) + " ft");
                        $( "#prominence-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[1])) + " m");
                    } else {
                        $( "#prominence-amount" ).val( numberWithCommas(unencoded[0]) + "  to  " + numberWithCommas(unencoded[1]) + " ft");
                        $( "#prominence-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[0])) + "  to  " + numberWithCommas(Math.floor(unencoded[1])) + " m");
                    }
                    if (0 != unencoded[0] || 9000 != unencoded[1]) {
                        $('#prominence-label').css('color', '#f24100');
                        $('.prominence-ammount').css('color', '#f24100');
                        $('.prominence-ammount').css('-webkit-text-fill-color', '#f24100');
                        $('#prominence-slider-range > div > div').css('background-color', '#f24100');
                        $('#prominence-slider-range .noUi-background').css('background-color', '#CCC');
                        $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                    } else {
                        $('#prominence-label').css('color', '#aaa');
                        $('.prominence-ammount').css('color', '#aaa');
                        $('.prominence-ammount').css('-webkit-text-fill-color', '#aaa');
                        $('#prominence-slider-range > div > div').css('background-color', '#ccc');
                        $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
                    }
                } else {
                    if (unencoded[0] == unencoded[1]) {
                        $( "#prominence-amount" ).val(numberWithCommas(unencoded[1]) + " ft");
                        $( "#prominence-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[1])) + " m");
                    } else {
                        $( "#prominence-amount" ).val( numberWithCommas(unencoded[0]) + "  to  " + numberWithCommas(unencoded[1]) + " ft");
                        $( "#prominence-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[0])) + "  to  " + numberWithCommas(Math.floor(unencoded[1])) + " m");
                    }
                    if (0 != unencoded[0] || 29500 != unencoded[1]) {
                        $('#prominence-label').css('color', '#f24100');
                        $('.prominence-ammount').css('color', '#f24100');
                        $('.prominence-ammount').css('-webkit-text-fill-color', '#f24100');
                        $('#prominence-slider-range > div > div').css('background-color', '#f24100');
                        $('#prominence-slider-range .noUi-background').css('background-color', '#CCC');
                        $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                    } else {
                        $('#prominence-label').css('color', '#aaa');
                        $('.prominence-ammount').css('color', '#aaa');
                        $('.prominence-ammount').css('-webkit-text-fill-color', '#aaa');
                        $('#prominence-slider-range > div > div').css('background-color', '#ccc');
                        $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
                    }
                }
            });
            prominenceSlider.noUiSlider.on('end', function(values, handle, unencoded, tap, positions){
                //if meters
                if ($('#prominence_bt_showinmeters').text() == '[ft]') {
                    delayShowDataprominence("#prominence-amount", [unencoded[0]/0.3048,unencoded[1]/0.3048]);
                } else {
                    delayShowDataprominence("#prominence-amount", unencoded);
                }
            });
            var prominenceValues = prominenceSlider.noUiSlider.get();
            if ($('#prominence-amount-meters').is(':visible')) {
                if (prominenceValues[0] == prominenceValues[1]) {
                    $( "#prominence-amount" ).val(numberWithCommas(parseInt(prominenceValues[1])) + " ft");
                    $( "#prominence-amount-meters" ).val(numberWithCommas(parseInt(Math.floor(prominenceValues[1]))) + " m");
                } else {
                    $( "#prominence-amount" ).val( numberWithCommas(parseInt(prominenceValues[0])) + "  to  " + numberWithCommas(parseInt(prominenceValues[1])) + " ft");
                    $( "#prominence-amount-meters" ).val(numberWithCommas(Math.floor(parseInt(prominenceValues[0]))) + "  to  " + numberWithCommas(parseInt(Math.floor(prominenceValues[1]))) + " m");
                }
            } else {
                if (prominenceValues[0] == prominenceValues[1]) {
                    $( "#prominence-amount" ).val(numberWithCommas(parseInt(prominenceValues[1])) + " ft");
                    $( "#prominence-amount-meters" ).val(numberWithCommas(parseInt(Math.floor(prominenceValues[1]))) + " m");
                } else {
                    $( "#prominence-amount" ).val( numberWithCommas(parseInt(prominenceValues[0])) + "  to  " + numberWithCommas(parseInt(prominenceValues[1])) + " ft");
                    $( "#prominence-amount-meters" ).val(numberWithCommas(Math.floor(parseInt(prominenceValues[0]))) + "  to  " + numberWithCommas(parseInt(Math.floor(prominenceValues[1]))) + " m");
                }
            }


            var summitsSlider = document.getElementById('summits-slider-range');

            noUiSlider.create(summitsSlider, {
                start: [ 0, 500 ],
                snap: true,
                connect: true,
                start: [wanted_summits_min, wanted_summits_max],
                range: {
                    'min': 0,
                    '12.5%': 1,
                    '18.8%': 2,
                    '25.0%': 3,
                    '31.3%': 4,
                    '37.5%': 5,
                    '43.8%': 10,
                    '50.0%': 20,
                    '56.3%': 30,
                    '62.5%': 40,
                    '68.8%': 50,
                    '75.0%': 100,
                    '81.3%': 200,
                    '87.5%': 300,
                    '93.8%': 400,
                    'max': 500
                }
            });

            summitsSlider.noUiSlider.on('slide', function(values, handle, unencoded, tap, positions){
                if (unencoded[0] == 500) {
                    $( "#summits-amount" ).val( unencoded[1] + "+ climbs" );
                } else if (unencoded[0] == unencoded[1]) {
                    $( "#summits-amount" ).val( unencoded[1] + " climbs" );
                } else {
                    if (unencoded[1] == 500) {
                        $("#summits-amount").val(unencoded[0] + "  to  " + unencoded[1] + "+ climbs");
                    } else {
                        $("#summits-amount").val(unencoded[0] + "  to  " + unencoded[1] + " climbs");
                    }
                }
                if (0 != unencoded[0] || 500 != unencoded[1]) {
                    $('#summits-label').css('color','#f24100');
                    $('.summits-ammount').css('color', '#f24100');
                    $('.summits-ammount').css('-webkit-text-fill-color', '#f24100');
                    $('#summits-slider-range > div > div').css('background-color','#f24100');
                    $('#summits-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#summits-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                } else {
                    $('#summits-label').css('color','#aaa');
                    $('.summits-ammount').css('color', '#aaa');
                    $('.summits-ammount').css('-webkit-text-fill-color', '#aaa');
                    $('#summits-slider-range > div > div').css('background-color','#ccc');
                    $('#summits-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
                }
            });
            summitsSlider.noUiSlider.on('end', function(values, handle, unencoded, tap, positions){
                delayShowDataSummits("#summits-amount", unencoded);
            });
            var summitsValues = summitsSlider.noUiSlider.get();
            if (summitsValues[0] == 500) {
                $( "#summits-amount" ).val( parseInt(summitsValues[1]) + "+ climbs");
            } else if (summitsValues[0] == summitsValues[1]) {
                $( "#summits-amount" ).val( parseInt(summitsValues[1]) + " climbs");
            } else {
                if (summitsValues[1] == 500) {
                    $( "#summits-amount" ).val( parseInt(summitsValues[0]) + "  to  " + parseInt(summitsValues[1]) + "+ climbs");
                } else {
                    $( "#summits-amount" ).val( parseInt(summitsValues[0]) + "  to  " + parseInt(summitsValues[1]) + " climbs");
                }
            }


            var difficultySlider = document.getElementById('difficulty-slider-range');

            noUiSlider.create(difficultySlider, {
                start: [ 1, 5 ],
                snap: true,
                connect: true,
                start: [wanted_difficulty_min, wanted_difficulty_max],
                range: {
                    'min': 1,
                    '40.0%': 2,
                    '60.0%': 3,
                    '80.0%': 4,
                    'max': 5
                }
            });

            difficultySlider.noUiSlider.on('slide', function(values, handle, unencoded, tap, positions){
                if (unencoded[0] == unencoded[1]) {
                    $("#difficulty-amount").val("Class " + unencoded[0]);
                } else {
                    $("#difficulty-amount").val("Class " + unencoded[0] + "  to  " + unencoded[1]);
                }
                if (1 != unencoded[0] || 5 != unencoded[1]) {
                    $('#difficulty-label').css('color','#f24100');
                    $('.difficulty-ammount').css('color', '#f24100');
                    $('.difficulty-ammount').css('-webkit-text-fill-color', '#f24100');
                    $('#difficulty-slider-range > div > div').css('background-color','#f24100');
                    $('#difficulty-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#difficulty-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                } else {
                    $('#difficulty-label').css('color','#aaa');
                    $('.difficulty-ammount').css('color', '#aaa');
                    $('.difficulty-ammount').css('-webkit-text-fill-color', '#aaa');
                    $('#difficulty-slider-range > div > div').css('background-color','#ccc');
                    $('#difficulty-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
                }
            });
            difficultySlider.noUiSlider.on('end', function(values, handle, unencoded, tap, positions){
                delayShowDataDifficulty("#difficulty-amount", unencoded);
            });
            var difficultyValues = difficultySlider.noUiSlider.get();
            if (difficultyValues[0] == difficultyValues[1]) {
                $("#difficulty-amount").val("Class " + parseInt(difficultyValues[0]));
            } else  {
                $("#difficulty-amount").val("Class " + parseInt(difficultyValues[0]) + "  to  " + parseInt(difficultyValues[1]));
            }




            var lengthSlider = document.getElementById('length-slider-range');

            noUiSlider.create(lengthSlider, {
                start: [ 0, 20 ],
                snap: true,
                connect: true,
                start: [wanted_length_min, wanted_length_max],
                range: {
                    'min': 0,
                    '5.0%': 1,
                    '10.0%': 2,
                    '15.0%': 3,
                    '20.0%': 4,
                    '25.0%': 5,
                    '30.0%': 6,
                    '35.0%': 7,
                    '40.0%': 8,
                    '45.0%': 9,
                    '50.0%': 10,
                    '55.0%': 11,
                    '60.0%': 12,
                    '65.0%': 13,
                    '70.0%': 14,
                    '75.0%': 15,
                    '80.0%': 16,
                    '85.0%': 17,
                    '90.0%': 18,
                    '95.0%': 19,
                    'max': 20
                }
            });

            lengthSlider.noUiSlider.on('slide', function(values, handle, unencoded, tap, positions){
                //if meters
                if ($('#length-amount-meters').is(':visible')) {
                    if (unencoded[0] == 32) {
                        $( "#length-amount" ).val(numberWithCommas(unencoded[1]) + "+ mi");
                        $( "#length-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[1])) + "+ km");
                    } else if (unencoded[0] == unencoded[1]) {
                        $( "#length-amount" ).val(numberWithCommas(unencoded[1]) + " mi");
                        $( "#length-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[1])) + " km");
                    } else {
                        if (unencoded[1] == 32) {
                            $( "#length-amount" ).val( numberWithCommas(unencoded[0]) + "  to  " + numberWithCommas(unencoded[1]) + "+ mi");
                            $( "#length-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[0])) + "  to  " + numberWithCommas(Math.floor(unencoded[1])) + "+ km");
                        } else {
                            $( "#length-amount" ).val( numberWithCommas(unencoded[0]) + "  to  " + numberWithCommas(unencoded[1]) + " mi");
                            $( "#length-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[0])) + "  to  " + numberWithCommas(Math.floor(unencoded[1])) + " km");
                        }
                    }
                    if (0 != unencoded[0] || 32 != unencoded[1]) {
                        $('#length-label').css('color', '#f24100');
                        $('.length-ammount').css('color', '#f24100');
                        $('.length-ammount').css('-webkit-text-fill-color', '#f24100');
                        $('#length-slider-range > div > div').css('background-color', '#f24100');
                        $('#length-slider-range .noUi-background').css('background-color', '#CCC');
                        $('#length-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                    } else {
                        $('#length-label').css('color', '#aaa');
                        $('.length-ammount').css('color', '#aaa');
                        $('.length-ammount').css('-webkit-text-fill-color', '#aaa');
                        $('#length-slider-range > div > div').css('background-color', '#ccc');
                        $('#length-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
                    }
                } else {
                    if (unencoded[0] == 20) {
                        $( "#length-amount" ).val(numberWithCommas(unencoded[1]) + "+ mi");
                        $( "#length-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[1])) + "+ km");
                    } else if (unencoded[0] == unencoded[1]) {
                        $( "#length-amount" ).val(numberWithCommas(unencoded[1]) + " mi");
                        $( "#length-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[1])) + " km");
                    } else {
                        if (unencoded[1] == 20) {
                            $( "#length-amount" ).val( numberWithCommas(unencoded[0]) + "  to  " + numberWithCommas(unencoded[1]) + "+ mi");
                            $( "#length-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[0])) + "  to  " + numberWithCommas(Math.floor(unencoded[1])) + "+ km");
                        } else {
                            $( "#length-amount" ).val( numberWithCommas(unencoded[0]) + "  to  " + numberWithCommas(unencoded[1]) + " mi");
                            $( "#length-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[0])) + "  to  " + numberWithCommas(Math.floor(unencoded[1])) + " km");
                        }
                    }
                    if (0 != unencoded[0] || 20 != unencoded[1]) {
                        $('#length-label').css('color', '#f24100');
                        $('.length-ammount').css('color', '#f24100');
                        $('.length-ammount').css('-webkit-text-fill-color', '#f24100');
                        $('#length-slider-range > div > div').css('background-color', '#f24100');
                        $('#length-slider-range .noUi-background').css('background-color', '#CCC');
                        $('#length-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                    } else {
                        $('#length-label').css('color', '#aaa');
                        $('.length-ammount').css('color', '#aaa');
                        $('.length-ammount').css('-webkit-text-fill-color', '#aaa');
                        $('#length-slider-range > div > div').css('background-color', '#ccc');
                        $('#length-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
                    }
                }
            });
            lengthSlider.noUiSlider.on('end', function(values, handle, unencoded, tap, positions){
                //if meters
                if ($('#length_bt_showinmeters').text() == '[ft]') {
                    delayShowDataLength("#length-amount", [unencoded[0]/1.6,unencoded[1]/1.6]);
                } else {
                    delayShowDataLength("#length-amount", unencoded);
                }
            });
            var lengthValues = lengthSlider.noUiSlider.get();
            //if meters
            if ($('#length-amount-meters').is(':visible')) {
                if (lengthValues[0] == '32') {
                    $( "#length-amount" ).val(numberWithCommas(parseInt(lengthValues[1])) + "+ mi");
                    $( "#length-amount-meters" ).val(numberWithCommas(parseInt(Math.floor(lengthValues[1]))) + "+ km");
                } else if (lengthValues[0] == lengthValues[1]) {
                    $( "#length-amount" ).val(numberWithCommas(parseInt(lengthValues[1])) + " mi");
                    $( "#length-amount-meters" ).val(numberWithCommas(parseInt(Math.floor(lengthValues[1]))) + " km");
                } else {
                    if (lengthValues[1] == '32') {
                        $( "#length-amount" ).val( numberWithCommas(parseInt(lengthValues[0])) + "  to  " + numberWithCommas(parseInt(lengthValues[1])) + "+ mi");
                        $( "#length-amount-meters" ).val(numberWithCommas(Math.floor(parseInt(lengthValues[0]))) + "  to  " + numberWithCommas(parseInt(Math.floor(lengthValues[1]))) + "+ km");
                    } else {
                        $( "#length-amount" ).val( numberWithCommas(parseInt(lengthValues[0])) + "  to  " + numberWithCommas(parseInt(lengthValues[1])) + " mi");
                        $( "#length-amount-meters" ).val(numberWithCommas(Math.floor(parseInt(lengthValues[0]))) + "  to  " + numberWithCommas(parseInt(Math.floor(lengthValues[1]))) + " km");
                    }
                }
            } else {
                if (lengthValues[0] == '20') {
                    $( "#length-amount" ).val(numberWithCommas(parseInt(lengthValues[1])) + "+ mi");
                    $( "#length-amount-meters" ).val(numberWithCommas(parseInt(Math.floor(lengthValues[1]))) + "+ km");
                } else if (lengthValues[0] == lengthValues[1]) {
                    $( "#length-amount" ).val(numberWithCommas(parseInt(lengthValues[1])) + " mi");
                    $( "#length-amount-meters" ).val(numberWithCommas(parseInt(Math.floor(lengthValues[1]))) + " km");
                } else {
                    if (lengthValues[1] == '20') {
                        $( "#length-amount" ).val( numberWithCommas(parseInt(lengthValues[0])) + "  to  " + numberWithCommas(parseInt(lengthValues[1])) + "+ mi");
                        $( "#length-amount-meters" ).val(numberWithCommas(Math.floor(parseInt(lengthValues[0]))) + "  to  " + numberWithCommas(parseInt(Math.floor(lengthValues[1]))) + "+ km");
                    } else {
                        $( "#length-amount" ).val( numberWithCommas(parseInt(lengthValues[0])) + "  to  " + numberWithCommas(parseInt(lengthValues[1])) + " mi");
                        $( "#length-amount-meters" ).val(numberWithCommas(Math.floor(parseInt(lengthValues[0]))) + "  to  " + numberWithCommas(parseInt(Math.floor(lengthValues[1]))) + " km");
                    }
                }
            }


            var verticalSlider = document.getElementById('vertical-slider-range');

            noUiSlider.create(verticalSlider, {
                start: [ 0, 10000 ],
                snap: true,
                connect: true,
                start: [wanted_vertical_min, wanted_vertical_max],
                range: {
                    'min': 0,
                    '9.0%': 500,
                    '18.0%': 1000,
                    '27.0%': 2000,
                    '36.0%': 3000,
                    '45.0%': 4000,
                    '54.0%': 5000,
                    '63.0%': 6000,
                    '72.0%': 7000,
                    '81.0%': 8000,
                    '90.0%': 9000,
                    'max': 10000
                }
            });

            verticalSlider.noUiSlider.on('slide', function(values, handle, unencoded, tap, positions){
                //if meters
                if ($('#vertical-amount-meters').is(':visible')) {
                    if (unencoded[0] == 3000) {
                        $( "#vertical-amount" ).val(numberWithCommas(unencoded[1]) + "+ ft");
                        $( "#vertical-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[1])) + "+ m");
                    } else if (unencoded[0] == unencoded[1]) {
                        $( "#vertical-amount" ).val(numberWithCommas(unencoded[1]) + " ft");
                        $( "#vertical-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[1])) + " m");
                    } else {
                        if (unencoded[1] == 3000) {
                            $( "#vertical-amount" ).val( numberWithCommas(unencoded[0]) + "  to  " + numberWithCommas(unencoded[1]) + "+ ft");
                            $( "#vertical-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[0])) + "  to  " + numberWithCommas(Math.floor(unencoded[1])) + "+ m");
                        } else {
                            $( "#vertical-amount" ).val( numberWithCommas(unencoded[0]) + "  to  " + numberWithCommas(unencoded[1]) + " ft");
                            $( "#vertical-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[0])) + "  to  " + numberWithCommas(Math.floor(unencoded[1])) + " m");
                        }
                    }
                    if (0 != unencoded[0] || 3000 != unencoded[1]) {
                        $('#vertical-label').css('color', '#f24100');
                        $('.vertical-ammount').css('color', '#f24100');
                        $('.vertical-ammount').css('-webkit-text-fill-color', '#f24100');
                        $('#vertical-slider-range > div > div').css('background-color', '#f24100');
                        $('#vertical-slider-range .noUi-background').css('background-color', '#CCC');
                        $('#vertical-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                    } else {
                        $('#vertical-label').css('color', '#aaa');
                        $('.vertical-ammount').css('color', '#aaa');
                        $('.vertical-ammount').css('-webkit-text-fill-color', '#aaa');
                        $('#vertical-slider-range > div > div').css('background-color', '#ccc');
                        $('#vertical-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
                    }
                } else {
                    if (unencoded[0] == 10000) {
                        $( "#vertical-amount" ).val(numberWithCommas(unencoded[1]) + "+ ft");
                        $( "#vertical-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[1])) + "+ m");
                    } else if (unencoded[0] == unencoded[1]) {
                        $( "#vertical-amount" ).val(numberWithCommas(unencoded[1]) + " ft");
                        $( "#vertical-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[1])) + " m");
                    } else {
                        if (unencoded[1] == 10000) {
                            $( "#vertical-amount" ).val( numberWithCommas(unencoded[0]) + "  to  " + numberWithCommas(unencoded[1]) + "+ ft");
                            $( "#vertical-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[0])) + "  to  " + numberWithCommas(Math.floor(unencoded[1])) + "+ m");
                        } else {
                            $( "#vertical-amount" ).val( numberWithCommas(unencoded[0]) + "  to  " + numberWithCommas(unencoded[1]) + " ft");
                            $( "#vertical-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[0])) + "  to  " + numberWithCommas(Math.floor(unencoded[1])) + " m");
                        }
                    }
                    if (0 != unencoded[0] || 10000 != unencoded[1]) {
                        $('#vertical-label').css('color', '#f24100');
                        $('.vertical-ammount').css('color', '#f24100');
                        $('.vertical-ammount').css('-webkit-text-fill-color', '#f24100');
                        $('#vertical-slider-range > div > div').css('background-color', '#f24100');
                        $('#vertical-slider-range .noUi-background').css('background-color', '#CCC');
                        $('#vertical-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                    } else {
                        $('#vertical-label').css('color', '#aaa');
                        $('.vertical-ammount').css('color', '#aaa');
                        $('.vertical-ammount').css('-webkit-text-fill-color', '#aaa');
                        $('#vertical-slider-range > div > div').css('background-color', '#ccc');
                        $('#vertical-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
                    }
                }
            });
            verticalSlider.noUiSlider.on('end', function(values, handle, unencoded, tap, positions){
                //if meters
                if ($('#vertical_bt_showinmeters').text() == '[ft]') {
                    delayShowDataVertical("#prominence-amount", [unencoded[0]/0.3048,unencoded[1]/0.3048]);
                } else {
                    delayShowDataVertical("#prominence-amount", unencoded);
                }
            });
            var verticalValues = verticalSlider.noUiSlider.get();
            //if meters
            if ($('#vertical-amount-meters').is(':visible')) {
                if (verticalValues[0] == '3000') {
                    $( "#vertical-amount" ).val(numberWithCommas(parseInt(verticalValues[1])) + "+ ft");
                    $( "#vertical-amount-meters" ).val(numberWithCommas(parseInt(verticalValues[1])) + "+ m");
                } else if (verticalValues[0] == verticalValues[1]) {
                    $( "#vertical-amount" ).val(numberWithCommas(parseInt(verticalValues[1])) + " ft");
                    $( "#vertical-amount-meters" ).val(numberWithCommas(parseInt(verticalValues[1])) + " m");
                } else {
                    if (verticalValues[1] == '3000') {
                        $( "#vertical-amount" ).val( numberWithCommas(parseInt(verticalValues[0])) + "  to  " + numberWithCommas(parseInt(verticalValues[1])) + "+ ft");
                        $( "#vertical-amount-meters" ).val( numberWithCommas(parseInt(verticalValues[0])) + "  to  " + numberWithCommas(parseInt(verticalValues[1])) + "+ m");
                    } else {
                        $( "#vertical-amount" ).val( numberWithCommas(parseInt(verticalValues[0])) + "  to  " + numberWithCommas(parseInt(verticalValues[1])) + " ft");
                        $( "#vertical-amount-meters" ).val( numberWithCommas(parseInt(verticalValues[0])) + "  to  " + numberWithCommas(parseInt(verticalValues[1])) + " m");
                    }
                }
            } else {
                if (verticalValues[0] == '10000') {
                    $( "#vertical-amount" ).val(numberWithCommas(parseInt(verticalValues[1])) + "+ ft");
                    $( "#vertical-amount-meters" ).val(numberWithCommas(parseInt(verticalValues[1])) + "+ m");
                } else if (verticalValues[0] == verticalValues[1]) {
                    $( "#vertical-amount" ).val(numberWithCommas(parseInt(verticalValues[1])) + " ft");
                    $( "#vertical-amount-meters" ).val(numberWithCommas(parseInt(verticalValues[1])) + " m");
                } else {
                    if (verticalValues[1] == '10000') {
                        $( "#vertical-amount" ).val( numberWithCommas(parseInt(verticalValues[0])) + "  to  " + numberWithCommas(parseInt(verticalValues[1])) + "+ ft");
                        $( "#vertical-amount-meters" ).val( numberWithCommas(parseInt(verticalValues[0])) + "  to  " + numberWithCommas(parseInt(verticalValues[1])) + "+ m");
                    } else {
                        $( "#vertical-amount" ).val( numberWithCommas(parseInt(verticalValues[0])) + "  to  " + numberWithCommas(parseInt(verticalValues[1])) + " ft");
                        $( "#vertical-amount-meters" ).val( numberWithCommas(parseInt(verticalValues[0])) + "  to  " + numberWithCommas(parseInt(verticalValues[1])) + " m");
                    }
                }
            }


            var lastClimbedSlider = document.getElementById('last-climbed-slider-range');

            noUiSlider.create(lastClimbedSlider, {
                start: [ 0, 11 ],
                snap: true,
                connect: true,
                start: [wanted_last_climbed_min, wanted_last_climbed_max],
                range: {
                    'min': 0,
                    '9.0%': 1,
                    '18.0%': 2,
                    '27.0%': 3,
                    '36.0%': 4,
                    '45.0%': 5,
                    '54.0%': 6,
                    '63.0%': 7,
                    '72.0%': 8,
                    '81.0%': 9,
                    '90.0%': 10,
                    'max': 11
                }
            });

            lastClimbedSlider.noUiSlider.on('slide', function(values, handle, unencoded, tap, positions){
                switch(unencoded[0]) {
                    case 0:
                        $( "#last-climbed-amount" ).val( "any time" );
                        break;
                    case 1:
                        $( "#last-climbed-amount" ).val( "last 10 years" );
                        break;
                    case 2:
                        $( "#last-climbed-amount" ).val( "last 5 years" );
                        break;
                    case 3:
                        $( "#last-climbed-amount" ).val( "last 2 years" );
                        break;
                    case 4:
                        $( "#last-climbed-amount" ).val( "last year" );
                        break;
                    case 5:
                        $( "#last-climbed-amount" ).val( "last 90 days" );
                        break;
                    case 6:
                        $( "#last-climbed-amount" ).val( "last 60 days" );
                        break;
                    case 7:
                        $( "#last-climbed-amount" ).val( "last 30 days" );
                        break;
                    case 8:
                        $( "#last-climbed-amount" ).val( "last 7 days" );
                        break;
                    case 9:
                        $( "#last-climbed-amount" ).val( "last 3 days" );
                        break;
                    case 10:
                        $( "#last-climbed-amount" ).val( "last 2 days" );
                        break;
                    case 11:
                        $( "#last-climbed-amount" ).val( "last day" );
                        break;
                    default:
                        $( "#last-climbed-amount" ).val( "any time" );
                }
                if (0 != unencoded[0] || 11 != unencoded[1]) {
                    $('#last-climbed-label').css('color','#f24100');
                    $('.last-climbed-ammount').css('color', '#f24100');
                    $('.last-climbed-ammount').css('-webkit-text-fill-color', '#f24100');
                    $('#last-climbed-slider-range > div > div').css('background-color','#f24100');
                    $('#last-climbed-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#last-climbed-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                } else {
                    $('#last-climbed-label').css('color','#aaa');
                    $('.last-climbed-ammount').css('color', '#aaa');
                    $('.last-climbed-ammount').css('-webkit-text-fill-color', '#aaa');
                    $('#last-climbed-slider-range > div > div').css('background-color','#ccc');
                    $('#last-climbed-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
                }
            });
            lastClimbedSlider.noUiSlider.on('end', function(values, handle, unencoded, tap, positions){
                delayShowDataLastClimbed("#last-climbed-amount", unencoded);
            });
            var lastClimbedValues = lastClimbedSlider.noUiSlider.get();
            switch(parseInt(lastClimbedValues[0])) {
                case 0:
                    $( "#last-climbed-amount" ).val( "any time" );
                    break;
                case 1:
                    $( "#last-climbed-amount" ).val( "last 10 years" );
                    break;
                case 2:
                    $( "#last-climbed-amount" ).val( "last 5 years" );
                    break;
                case 3:
                    $( "#last-climbed-amount" ).val( "last 2 years" );
                    break;
                case 4:
                    $( "#last-climbed-amount" ).val( "last year" );
                    break;
                case 5:
                    $( "#last-climbed-amount" ).val( "last 90 days" );
                    break;
                case 6:
                    $( "#last-climbed-amount" ).val( "last 60 days" );
                    break;
                case 7:
                    $( "#last-climbed-amount" ).val( "last 30 days" );
                    break;
                case 8:
                    $( "#last-climbed-amount" ).val( "last 7 days" );
                    break;
                case 9:
                    $( "#last-climbed-amount" ).val( "last 3 days" );
                    break;
                case 10:
                    $( "#last-climbed-amount" ).val( "last 2 days" );
                    break;
                case 11:
                    $( "#last-climbed-amount" ).val( "last day" );
                    break;
                default:
                    $( "#last-climbed-amount" ).val( "any time" );
            }

            $('#peak-search-mobile').on('click', function(){
               $('#peak-search').addClass('modal fade right');
               $('#peak-search').attr('style','left: auto; right: 0px; margin-top: 0px; width: 240px;');
               $('#peak-search').removeClass('hidden-xs');
               $('#peak-search').removeClass('hidden-sm');
               $('#peak-search').modal('toggle');
               $('#mobile-collapse-nav').show();
               var height = $(window).height();
               var width = $(window).width() - 240;
               $('#mobile-collapse-nav').width(width);
               $('#mobile-collapse-nav').height(height);
            });

            $('#mobile-collapse-nav').on('click', function(){
                $('#peak-search').modal('hide');
            });

            $('#peaks-list').on('click', '.extra-challenge-link', function(){
                var peak_id = $(this).data('peakid');
                $(this).hide();
                $('#extra-challenges-'+peak_id).fadeIn(800);
            });

            $('#location-search-mobile').on('click', function(){
                $('#location-search-mobile').html('<i class="fa fa-spinner fa-spin"></i>');
                if (navigator.geolocation) {
                    var options = {
                      enableHighAccuracy: true,
                      timeout: 5000,
                      maximumAge: 0
                    };
                    navigator.geolocation.getCurrentPosition(showLocalPeaks, showLocationError, options);
                    $('#location-search-mobile').css('color','#f24100');
                } else {
                    $('#location-search-mobile').html('<i class="fa fa-location-arrow"></i>');
                    $('#mobile-search-title').html('Geolocation not supported');
                    $('#location-search-mobile').css('color','#00B1F2');
                }
            });

            function showLocalPeaks(position) {
                $('#location-search-mobile').html('<i class="fa fa-location-arrow"></i>');
                $('#mobile-search-title').html('Peaks near you');
                wanted_lat = position.coords.latitude;
                wanted_lng = position.coords.longitude;
                $('#hdnLat').val(wanted_lat);
                $('#hdnLng').val(wanted_lng);
                u = updateURLParameter('#', 'lat', wanted_lat);
                u = updateURLParameter('#'+u, 'lng', wanted_lng);
                //reset to page 1
                u = updateURLParameter('#' + u, 'page', '1');
                window.location.hash = u;
                loadPeaksFromHash();
            }

            function showLocationError(error) {
                $('#location-search-mobile').html('<i class="fa fa-location-arrow"></i>');
                switch(error.code) {
                    case error.PERMISSION_DENIED:
                        $('#mobile-search-title').html('Geolocation permission denied');
                        break;
                    case error.POSITION_UNAVAILABLE:
                        $('#mobile-search-title').html('Location information unavailable');
                        break;
                    case error.TIMEOUT:
                        $('#mobile-search-title').html('Location request timed out');
                        break;
                    case error.UNKNOWN_ERROR:
                        $('#mobile-search-title').html('An unknown error occurred');
                        break;
                }
            }

            $('#peak-search').on('hide.bs.modal', function (e) {
              $('#peak-search').removeClass('modal fade right');
            });

            $('#peak-search').on('hidden.bs.modal', function (e) {
              $('#peak-search').attr('style','left: 0px; right: auto; width: 240px; position: fixed; display: inline-block; top: 44px;');
              $('#peak-search').addClass('hidden-xs');
              $('#peak-search').addClass('hidden-sm');
              $('#mobile-collapse-nav').hide();
            })

            $('#explore').on('submit', function(e) {
                e.preventDefault();

                var q = document.URL.split('#')[1];
                if(q != undefined){
                    q = q.split('&');
                    for(var i = 0; i < q.length; i++){
                        hash = q[i].split('=');
                        vars.push(hash[1]);
                        vars[hash[0]] = hash[1];
                    }
                }

                if (vars['lat'] != undefined) {
                    lat = decodeURIComponent(vars['lat']);
                } else {
                    lat = '';
                }

                if (vars['lng'] != undefined) {
                    lng = decodeURIComponent(vars['lng']);
                } else {
                    lng = '';
                }

                if ($('.mapboxgl-ctrl-geocoder--input').val() == '') {
                    lat = '';
                    lng = '';
                    $('#hdnLat').val('');
                    $('#hdnLng').val('');
                }

                //reset filters
                resetSliders();
                if (lng != '' && lat != '') {
                    u = updateURLParameter(window.location.hash, 'sort_key', 'distance');
                    u = updateURLParameter('#'+u, 'sort_dir', 'asc');
                    u = updateURLParameter('#' + u, 'lat', lat);
                    u = updateURLParameter('#' + u, 'lng', lng);
                    window.location.hash = u;
                } else {
                    u = updateURLParameter(window.location.hash, 'sort_key', 'summits');
                    u = updateURLParameter('#'+u, 'sort_dir', 'desc');
                    window.location.hash = u;
                }
                $('#search-peaks-btn').blur();
                $('#peak-search').modal('hide');
                delayShowKeywordNear("#keyword-search", encodeURIComponent($('#q').val()).replace(/'/g, '%27'), encodeURIComponent(lat).replace(/'/g, '%27'), encodeURIComponent(lng).replace(/'/g, '%27'));
            });

            document.addEventListener('keyup', function(e) {
                if (e.keyCode == 27) {
                    $('#peak-search').modal('hide');
                }
            });

            window.onresize = function() {
                //$('.truncate').truncate({
                //  lines: 2,
                //  lineHeight: 12
                //});
                //$('.truncate-one').truncate({
                //  lines: 1,
                //  lineHeight: 12
                //});
            }

            loadPeaks(keyword, near, range, wanted_elev_min, wanted_elev_max, wanted_prom_min, wanted_prom_max, wanted_summits_min, wanted_summits_max, wanted_difficulty_min, wanted_difficulty_max, wanted_length_min, wanted_length_max, wanted_vertical_min, wanted_vertical_max, wanted_last_climbed_min, wanted_last_climbed_max, classics, in_challenge, you_climbed, lat, lng, sort_key, sort_dir, page);

        });

    </script>

{% endblock %}