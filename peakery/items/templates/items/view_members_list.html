{% extends "base.html" %}
{% load static %}
{% load item_tags %}
{% load avatar_tags %}
{% block title %}Members active in the World{% endblock %}
{% block titlemeta_overwrite %}Members active in the World{% endblock %}
{% block description %}Shows members who've climbed peaks in different regions around the world{% endblock %}
{% block image_rel %}{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row fixed-page-header subnav">
        <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 70px; font-weight: 500; border-radius: 12px 12px 0 0;"><span style="font-size: 24px; font-weight: 600;">
            <div id="breadcrumbs">
                <ul>
                    <li class="section-title-li" style="margin-top: 0px !important; width: 100%;">
                        <div class="section-title hidden-xs hidden-sm" style="font-weight: 600; float: left;">Members active in</div>
                        <div class="mobile-section-title hidden-xs hidden-md hidden-lg" style="font-weight: 500; float: left;">
                            <span id="mobile-members-count" style="color: #F24100;"></span> <span id="mobile-members-count-desc">Members</span> in
                        </div>
                        <div class="pull-right hidden-xs hidden-sm section-title-stats" style="margin-left: 15px;">
                            <span style="font-size: 14px; letter-spacing: 1.1px; font-weight: 300;"><span id="active-member-count"></span> <span id="active-member-count-desc">members</span> active in</span>
                            <span class="select" id="selectTimefilter">
                                <input type="hidden" id="hdnTimeFilter" value="90">
                                <button class="btn btn-default member-filter-button" style="margin-bottom: 11px; margin-left: -3px; font-size: 14px;" data-toggle="dropdown">
                                    <span id="timefilter-title">last 3 months</span>
                                    <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu timefilter-list" style="right: 20px; top: 58px; height: 120px; left: inherit; overflow: auto;">
                                    <li style="float: none;" data-value="90"><a class="timefilter-item" href="90">last 3 months</a></li>
                                    <li style="float: none;" data-value="365"><a class="timefilter-item" href="365">last year</a></li>
                                    <li style="float: none;" data-value="all"><a class="timefilter-item" href="all">all time</a></li>
                                </ul>
                            </span>
                        </div>
                        <div class="pull-right hidden-md hidden-lg" id="mobile-member-search" style="margin-left: 10px; margin-top: 24px; font-size: 22px; font-weight: 500; letter-spacing: 1.0px; margin-right: 6px;"><img id="mobile-member-search-icon" style="cursor: pointer; width: 24px;" src="{% static 'img/member-search-icon.png' %}"></div>
                        <div class="pull-right hidden-sm hidden-md hidden-lg" id="mobile-see-worldwide" style="margin-left: 10px; margin-top: 24px; font-size: 22px; font-weight: 500; letter-spacing: 1.0px; margin-right: 20px;"></div>
                        <div style="overflow: hidden; height: 70px;">
                            <button id="region-filter-button" class="btn btn-default member-filter-button">
                                <div id="region-title" class="section-title ellipsis" style="color: #00B1F2; font-weight: 500; width: auto; float: left; overflow: hidden; display: inline-block;">The World</div>
                                <div id="region-title-chevron" style="font-size: 16px; float: left; color: rgb(0, 177, 242);"><i class="fa fa-angle-down"></i></div>
                            </button>
                            <div class="hidden-xs" id="see-worldwide" style="margin-left: 10px; font-size: 14px; font-weight: 500; letter-spacing: 1.0px; position: absolute;"></div>
                            <form method="post" id="region-filter-form" class="region-searchbox" style="width: 100%;">
                                <input type="hidden" id="hdnCountryId" value="">
                                <input type="hidden" id="hdnRegionId" value="">
                                <input style="font-weight: 500 !important; display: none;" type="search" class="region-searchbox-input" name="rq1" id="rq1" placeholder="region or country..." autocomplete="off" autocorrect="off">
                                <input style="font-weight: 500 !important; display: none;" type="search" class="mobile-member-searchbox-input" name="mmq1" id="mmq1" placeholder="find a member..." autocomplete="off" autocorrect="off">
                                <span id="region-searchbox-icon" style="display: none;"></span>
                                <span id="mobile-member-searchbox-icon" style="display: none;"></span>
                            </form>
                        </div>
                    </li>
                </ul>
            </div>
        </span></div>
    </div>
    <div class="row sub-header-row hidden-xs hidden-sm" style="height: 50px;">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999;">
            Ranked by: <a id="sort-new-peaks" style="font-size: 12px; margin-left: 10px;" class="main-header-sub-links ajax-link" onclick="sortMembers('new_peaks', '1');">New peaks</a><a id="sort-total-summits" style="font-size: 12px; margin-left: 35px;" class="main-header-sub-links ajax-link" onclick="sortMembers('total_summits', '1');">Total summits</a><a id="sort-first-ascents" style="font-size: 12px; margin-left: 35px;" class="main-header-sub-links ajax-link" onclick="sortMembers('first_ascents', '1');">First ascents</a><a id="sort-koms" style="font-size: 12px; margin-left: 35px;" class="main-header-sub-links ajax-link" onclick="sortMembers('koms', '1');">KOMs</a><a id="sort-summit-stewards" style="font-size: 12px; margin-left: 35px;" class="main-header-sub-links ajax-link" onclick="sortMembers('summit_stewards', '1');">Stewards</a>
            <span class="pull-right page-subtitle">
                <form method="post" id="member-search-form" class="member-searchbox" action="javascript: memberSearch();">
                    <input type="search" class="member-searchbox-input" name="memq1" id="memq1" placeholder="find a member..." onkeyup="memberButtonUp();" required>
                    <span id="member-searchbox-icon" style="display: none;"></span>
                </form>
            </span>
        </div>
    </div>
{% endblock %}

{% block content %}

<style>
   @media screen and (max-width: 767px) and (min-width: 1px) {
       .member-divider {
            height: 50px;
        }
       #content-body {
            margin-top: 141px;
        }
       #region-title-chevron {
           margin-top: 2px;
       }
       .sub-header-row {
            margin-top: 81px;
        }
   }
   @media screen and (max-width: 1023px) and (min-width: 768px) {
       .member-divider {
            height: 50px;
        }
       #content-body {
            margin-top: 141px;
        }
       #region-title-chevron {
           margin-top: 5px;
       }
       .sub-header-row {
            margin-top: 81px;
        }
   }
    @media screen and (min-width: 1024px) {
        .member-divider {
            height: 60px;
        }
        #content-body {
            margin-top: 141px;
        }
        #region-title-chevron {
           margin-top: 5px;
       }
        .main-header-row {
            top: 70px !important;
        }
        .sub-header-row {
            margin-top: 10px;
        }
    }
   @media screen and (min-width: 1280px) {
       .sub-header-row {
            margin-top: 81px;
        }
   }
    #rq1, #memq1 {
        padding: 1px 10px;
        margin-left: 5px;
        border-color: #ccc;
    }
   .filter-bar {
        top: 121px;
    }
    ::-webkit-input-placeholder { font-weight: 300; font-size 14px; letter-spacing: .03125em; }
    ::-moz-placeholder { font-weight: 300; font-size 14px; letter-spacing: .03125em; } /* firefox 19+ */
    :-ms-input-placeholder { font-weight: 300; font-size 14px; letter-spacing: .03125em; } /* ie */
    input:-moz-placeholder { font-weight: 300; font-size 14px; letter-spacing: .03125em; }
</style>

<div class="container">
    <div class="row sub-header-row hidden-md hidden-lg filter-bar" style="height: 50px; padding-right: 0px;">
        <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12" style="height: 50px;line-height: 26px;">
            <div style="float: left; width: 50%; height: 100%; border-right: solid 1px #cfcfcf;">
                <div class="select" id="selectRankedBy" style="margin-top: 8px;">
                    <input type="hidden" id="hdnRankedBy" value="">
                    <button class="btn btn-default ranked-by-button" style="border: none; padding-top: 7px; padding-bottom: 7px; margin-left: 1px; color: #00b1f2; font-size: 12px; padding-left: 5px; padding-right: 5px; background-color: #f2f2f2; width: 95%; text-align: left;" data-toggle="dropdown" aria-expanded="false">
                        <span id="ranked-by-title">New peaks</span>
                        <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu ranked-by-list" style="cursor: pointer; height: 215px; left: 5px; overflow: auto; top: 50px;">
                        <li style="float: none; height: 30px; margin: 10px 0 0;" data-value="90"><a class="ranked-by-item" onclick="sortMembers('new_peaks', '1');">New peaks</a></li>
                        <li style="float: none; height: 30px; margin: 10px 0 0;" data-value="365"><a class="ranked-by-item" onclick="sortMembers('total_summits', '1');">Total summits</a></li>
                        <li style="float: none; height: 30px; margin: 10px 0 0;" data-value="all"><a class="ranked-by-item" onclick="sortMembers('first_ascents', '1');">First ascents</a></li>
                        <li style="float: none; height: 30px; margin: 10px 0 0;" data-value="all"><a class="ranked-by-item" onclick="sortMembers('koms', '1');">KOMs</a></li>
                        <li style="float: none; height: 30px; margin: 10px 0 0;" data-value="all"><a class="ranked-by-item" onclick="sortMembers('summit_stewards', '1');">Stewards</a></li>
                    </ul>
                </div>
            </div>
            <div style="float: left; margin-left: 10px; width: 45%;">
                <div class="select" id="selectMobileTimefilter" style="margin-top: 5px; width: 100%;">
                    <button class="btn btn-default mobile-timefilter-button" style="border: none; padding-top: 10px; padding-bottom: 7px; margin-left: 1px; color: #00b1f2; font-size: 12px; padding-left: 5px; padding-right: 5px; background-color: #f2f2f2; width: 100%; text-align: left;" data-toggle="dropdown">
                        <span id="mobile-timefilter-title">Last 3 months</span>
                        <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu mobile-timefilter-list" style="height: 138px; left: 50%; top: 50px; overflow: auto;">
                        <li style="float: none; height: 30px; margin: 10px 0 0;" data-value="90"><a class="mobile-timefilter-item" href="90">Last 3 months</a></li>
                        <li style="float: none; height: 30px; margin: 10px 0 0;" data-value="365"><a class="mobile-timefilter-item" href="365">Last year</a></li>
                        <li style="float: none; height: 30px; margin: 10px 0 0;" data-value="all"><a class="mobile-timefilter-item" href="all">All time</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12">
            <div class="row" id="ajax-data-loading" style="display: none;">
              <div class="col-md-12 data-loading" style="text-align: center; color: #666;">
                <i class="fa fa-spinner fa-spin fa-5x"></i>
              </div>
            </div>
            <div class="row" id="members-list"></div>
        </div>
    </div>
    <div class="row dark-background-row" id="pagination" style="border-bottom: none; color: #ccc; margin-top: -1px; height: 80px; margin-left: 0px; display: none;">
        <div class="col-md-12">
            <div class="row" style="padding-left: 15px; margin-left: 5px; text-align: center;">
                <span id="pagination-pages" class="step-links"></span>
            </div>
        </div>
    </div>
    <div class="row hidden-lg hidden-md hidden-sm">
        <div style="height: 178px;"></div>
    </div>
    <div class="row hidden-xs">
        <div style="height: 54px;"></div>
    </div>
</div>

<script type="text/javascript">

    var memberSort;
    var memberSince;
    var lastRegion = '';
    var lastRegionName = '';

    function sortMembers(sort) {

        memberSort = sort;

        switch(sort) {
            case 'new_peaks':
                $('#sort-new-peaks').css('color', '#F24100');
                $('#sort-new-peaks').css('font-weight', '500');
                $('#sort-total-summits').css('color', '#999');
                $('#sort-total-summits').css('font-weight', '300');
                $('#sort-first-ascents').css('color', '#999');
                $('#sort-first-ascents').css('font-weight', '300');
                $('#sort-koms').css('color', '#999');
                $('#sort-koms').css('font-weight', '300');
                $('#sort-summit-stewards').css('color', '#999');
                $('#sort-summit-stewards').css('font-weight', '300');
                $('#ranked-by-title').html('New peaks');
                break;
            case 'total_summits':
                $('#sort-new-peaks').css('color', '#999');
                $('#sort-new-peaks').css('font-weight', '300');
                $('#sort-total-summits').css('color', '#F24100');
                $('#sort-total-summits').css('font-weight', '500');
                $('#sort-first-ascents').css('color', '#999');
                $('#sort-first-ascents').css('font-weight', '300');
                $('#sort-koms').css('color', '#999');
                $('#sort-koms').css('font-weight', '300');
                $('#sort-summit-stewards').css('color', '#999');
                $('#sort-summit-stewards').css('font-weight', '300');
                $('#ranked-by-title').html('Total summits');
                break;
            case 'first_ascents':
                $('#sort-new-peaks').css('color', '#999');
                $('#sort-new-peaks').css('font-weight', '300');
                $('#sort-total-summits').css('color', '#999');
                $('#sort-total-summits').css('font-weight', '300');
                $('#sort-first-ascents').css('color', '#F24100');
                $('#sort-first-ascents').css('font-weight', '500');
                $('#sort-koms').css('color', '#999');
                $('#sort-koms').css('font-weight', '300');
                $('#sort-summit-stewards').css('color', '#999');
                $('#sort-summit-stewards').css('font-weight', '300');
                $('#ranked-by-title').html('First ascents');
                count_img = '<img src="{% static 'img/profile_award_flag.png' %}" style="height: 25px; margin-right: 14px; margin-left: 8px;">';
                break;
            case 'koms':
                $('#sort-new-peaks').css('color', '#999');
                $('#sort-new-peaks').css('font-weight', '300');
                $('#sort-total-summits').css('color', '#999');
                $('#sort-total-summits').css('font-weight', '300');
                $('#sort-first-ascents').css('color', '#999');
                $('#sort-first-ascents').css('font-weight', '300');
                $('#sort-koms').css('color', '#F24100');
                $('#sort-koms').css('font-weight', '500');
                $('#sort-summit-stewards').css('color', '#999');
                $('#sort-summit-stewards').css('font-weight', '300');
                $('#ranked-by-title').html('KOMs');
                count_img = '<img src="{% static 'img/profile_award_crown.png' %}" style="height: 20px; margin-right: 10px;">';
                break;
            case 'summit_stewards':
                $('#sort-new-peaks').css('color', '#999');
                $('#sort-new-peaks').css('font-weight', '300');
                $('#sort-total-summits').css('color', '#999');
                $('#sort-total-summits').css('font-weight', '300');
                $('#sort-first-ascents').css('color', '#999');
                $('#sort-first-ascents').css('font-weight', '300');
                $('#sort-koms').css('color', '#999');
                $('#sort-koms').css('font-weight', '300');
                $('#sort-summit-stewards').css('color', '#F24100');
                $('#sort-summit-stewards').css('font-weight', '500');
                $('#ranked-by-title').html('Summit stewards');
                count_img = '<img src="{% static 'img/profile_award_shield.png' %}" style="height: 25px; margin-right: 12px; margin-left: 4px;">';
                break;
            default:
                $('#sort-new-peaks').css('color', '#F24100');
                $('#sort-new-peaks').css('font-weight', '500');
                $('#sort-total-summits').css('color', '#999');
                $('#sort-total-summits').css('font-weight', '300');
                $('#sort-first-ascents').css('color', '#999');
                $('#sort-first-ascents').css('font-weight', '300');
                $('#sort-koms').css('color', '#999');
                $('#sort-koms').css('font-weight', '300');
                $('#sort-summit-stewards').css('color', '#999');
                $('#sort-summit-stewards').css('font-weight', '300');
                $('#ranked-by-title').html('New peaks');
        }

        loadMembers(memberSince, memberSort, '1');

    }

    function loadMembers(since, sort, page) {

        memberSince = since;
        memberSort = sort;
        var count_img = '';
        $('#pagination').css('display','none');
        window.scrollTo(0, 0);

        switch(sort) {
            case 'new_peaks':
                $('#sort-new-peaks').css('color', '#F24100');
                $('#sort-new-peaks').css('font-weight', '500');
                $('#sort-total-summits').css('color', '#999');
                $('#sort-total-summits').css('font-weight', '300');
                $('#sort-first-ascents').css('color', '#999');
                $('#sort-first-ascents').css('font-weight', '300');
                $('#sort-koms').css('color', '#999');
                $('#sort-koms').css('font-weight', '300');
                $('#sort-summit-stewards').css('color', '#999');
                $('#sort-summit-stewards').css('font-weight', '300');
                $('#ranked-by-title').html('New peaks');
                break;
            case 'total_summits':
                $('#sort-new-peaks').css('color', '#999');
                $('#sort-new-peaks').css('font-weight', '300');
                $('#sort-total-summits').css('color', '#F24100');
                $('#sort-total-summits').css('font-weight', '500');
                $('#sort-first-ascents').css('color', '#999');
                $('#sort-first-ascents').css('font-weight', '300');
                $('#sort-koms').css('color', '#999');
                $('#sort-koms').css('font-weight', '300');
                $('#sort-summit-stewards').css('color', '#999');
                $('#sort-summit-stewards').css('font-weight', '300');
                $('#ranked-by-title').html('Total summits');
                break;
            case 'first_ascents':
                $('#sort-new-peaks').css('color', '#999');
                $('#sort-new-peaks').css('font-weight', '300');
                $('#sort-total-summits').css('color', '#999');
                $('#sort-total-summits').css('font-weight', '300');
                $('#sort-first-ascents').css('color', '#F24100');
                $('#sort-first-ascents').css('font-weight', '500');
                $('#sort-koms').css('color', '#999');
                $('#sort-koms').css('font-weight', '300');
                $('#sort-summit-stewards').css('color', '#999');
                $('#sort-summit-stewards').css('font-weight', '300');
                $('#ranked-by-title').html('First ascents');
                count_img = '<img src="{% static 'img/profile_award_flag.png' %}" style="height: 25px; margin-right: 14px; margin-left: 8px;">';
                break;
            case 'koms':
                $('#sort-new-peaks').css('color', '#999');
                $('#sort-new-peaks').css('font-weight', '300');
                $('#sort-total-summits').css('color', '#999');
                $('#sort-total-summits').css('font-weight', '300');
                $('#sort-first-ascents').css('color', '#999');
                $('#sort-first-ascents').css('font-weight', '300');
                $('#sort-koms').css('color', '#F24100');
                $('#sort-koms').css('font-weight', '500');
                $('#sort-summit-stewards').css('color', '#999');
                $('#sort-summit-stewards').css('font-weight', '300');
                $('#ranked-by-title').html('KOMs');
                count_img = '<img src="{% static 'img/profile_award_crown.png' %}" style="height: 20px; margin-right: 10px;">';
                break;
            case 'summit_stewards':
                $('#sort-new-peaks').css('color', '#999');
                $('#sort-new-peaks').css('font-weight', '300');
                $('#sort-total-summits').css('color', '#999');
                $('#sort-total-summits').css('font-weight', '300');
                $('#sort-first-ascents').css('color', '#999');
                $('#sort-first-ascents').css('font-weight', '300');
                $('#sort-koms').css('color', '#999');
                $('#sort-koms').css('font-weight', '300');
                $('#sort-summit-stewards').css('color', '#F24100');
                $('#sort-summit-stewards').css('font-weight', '500');
                $('#ranked-by-title').html('Summit stewards');
                count_img = '<img src="{% static 'img/profile_award_shield.png' %}" style="height: 25px; margin-right: 12px; margin-left: 4px;">';
                break;
            default:
                $('#sort-new-peaks').css('color', '#F24100');
                $('#sort-new-peaks').css('font-weight', '500');
                $('#sort-total-summits').css('color', '#999');
                $('#sort-total-summits').css('font-weight', '300');
                $('#sort-first-ascents').css('color', '#999');
                $('#sort-first-ascents').css('font-weight', '300');
                $('#sort-koms').css('color', '#999');
                $('#sort-koms').css('font-weight', '300');
                $('#sort-summit-stewards').css('color', '#999');
                $('#sort-summit-stewards').css('font-weight', '300');
                $('#ranked-by-title').html('New peaks');
        }

        window.location.hash = 'since='+since+'&order='+sort+'&page='+page;

        document.activeElement.blur();
        $('#members-list').empty();
        $('#ajax-data-loading').css('display', 'inline');

        $('#active-member-count').html('');
        $('#mobile-members-count').html('');
        $('#see-worldwide').css('display','inline');
        $('#mobile-see-worldwide').css('display','inline');
        $('#mobile-member-search').css('display','inline');

        var country_id = $("#hdnCountryId").val();
        var region_id = $("#hdnRegionId").val();
        if (region_id == "-1"){
            region_id = "";
        }

        if (since == 'all') {
            $('#sort-new-peaks').html('Peaks');
        } else {
            $('#sort-new-peaks').html('New peaks');
        }
        var totalPages, totalMembers;
        var counter = 1;
        $.getJSON('{% url "members_list" %}?country_id=' + country_id + '&region_id=' + region_id + '&since=' + since + '&sort='+sort+'&page='+page , function(data) {
            $.each( data, function( key, val ) {
                if (key=='active_members') {
                    $('#active-member-count').html(numberWithCommas(val));
                    $('#mobile-members-count').html(numberWithCommas(val));
                    if (val != '1') {
                        $('#active-member-count-desc').html(' members');
                        $('#mobile-members-count-desc').html(' Members');
                    } else {
                        $('#active-member-count-desc').html(' member');
                        $('#mobile-members-count-desc').html(' Member');
                    }
                    totalMembers = val;
                    totalPages = Math.floor(parseInt(val)/20);
                }
                if (key=='members') {
                    $('#ajax-data-loading').css('display', 'none');
                    var member_index = (20 * (page-1)) + 1;
                    $.each( val, function( memberkey, memberval ) {
                        //build photos
                        var photo_index = 0;
                        var photo_grid_index = 2;
                        var photoHtml = '';
                        $.each( memberval.peaks, function( peakkey, peakval ) {
                            if (photo_index == 7) {
                                divClass = 'col-lg-3 col-md-3 col-sm-4 col-xs-6 hidden-xs hidden-md hidden-lg';
                                photoDivClass = 'top-photos';
                            } else {
                                divClass = 'col-lg-3 col-md-3 col-sm-4 col-xs-6';
                                photoDivClass = 'top-photos';
                            }
                            if (photo_index == 5) {
                                divClass = 'col-lg-3 col-md-3 col-sm-4 col-xs-6 hidden-xs';
                            }
                            if (photo_index == 6) {
                                divClass = 'col-lg-3 col-md-3 col-sm-4 col-xs-6 hidden-xs';
                            }

                            if (peakval.peak_thumbnail_url.indexOf("images/img/cache/default.png") == 0) {
                                photoCaptionClass = 'empty-photo-info';
                            } else {
                                photoCaptionClass = 'user-photo-info';
                            }

                            photoHtml = photoHtml + '<div class="' + divClass + ' hover-photos photo-grid-' + photo_grid_index + '" style="padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}' + peakval.peak_thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat;"><div class="top-photos"><div class="hover-photos"><a href="/' + peakval.slug + '/"><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></a></div><div class="' + photoCaptionClass + '" style="display: block;"><span class="data photo-caption-peakname-only" style="position: absolute; bottom: -5px; left: 10px; color: #fff;"><p class="bagger ellipsis"><a style="color: #fff;" href="/' + peakval.slug + '/">' + peakval.peak_name + '</a></p></span></div></div></div>';
                            photo_index++;
                            photo_grid_index++;
                            if (photo_index > 7) {
                                return false;
                            }
                        });
                        //count type
                        var count_type;
                        if (since == 'all' && sort == 'new_peaks') {
                            if (memberval.member_count == '1') {
                                count_type = 'peak';
                            } else {
                                count_type = 'peaks';
                            }
                        } else {
                            if (memberval.member_count == '1') {
                                count_type = memberval.count_type;
                            } else {
                                count_type = memberval.count_type_plural;
                            }
                        }
                        var avatar_img = '';
                        if (memberval.avatar_url != 'None') {
                            avatar_img = '{{ MEDIA_URL }}' + memberval.avatar_url;
                        } else {
                            avatar_img = 'https://s3-us-west-1.amazonaws.com/peakery-media/img/default-user.png';
                        }

                        $('#members-list').append('<div class="col-md-12"><div class="row"><div onclick="viewMember(\'/members/' + memberval.username + '\');" class="col-md-12" style="height: 70px; background-color: #fff; cursor: pointer;"><div><p style="height: 70px; line-height: 70px;"><span class="memberNumberCircle"><span>' + member_index + '</span></span><span class="stats-header" style="margin-left: 10px;"><a style="color: #000000;" href="/members/' + memberval.username + '">' + memberval.username + '</a></span><span>&nbsp;&nbsp;&nbsp;</span><span class="summit-list-stats pull-right" style="color: #666;">' + count_img + memberval.member_count + ' ' + count_type + '</span></div></div></div>');
                        $('#members-list').append('<div class="col-md-12"><div class="row"><div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 hover-photos photo-grid-1" style="padding-right: 0px; padding-left: 0px; background-image: url(\'' + avatar_img + '\'); background-size: cover; overflow: hidden;"><a style="color: #000000;" href="/members/' + memberval.username + '"><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></a></div>' + photoHtml + '</div></div>');
                        $('#members-list').append('<div class="col-md-12"><div class="row dark-background-row" style="margin-bottom: -1px;"><div class="member-divider"></div></div></div>');

                        member_index++;
                        counter++;
                    });
                }
            });
            //show pagination
            var paginationHtml = '';
            if (parseInt(page) == totalPages && totalPages > 1) {
                paginationHtml = '<div style="display: inline-block; height: 55px;"><a onclick="loadMembers(\''+memberSince+'\', \''+memberSort+'\', ' + (parseInt(page)-1).toString() + ');"><img src="{% static 'img/font-awesome_4-7-0_arrow-circle-left_256_0_00b1f2_none.png' %}" style="height: 26px; margin-bottom: 5px;"></a></div><div class="current" style="margin-left: 20px; margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">members ' + ((parseInt(page)-1)*20+1).toString() + ' - ' + numberWithCommas(totalMembers) + ' of ' + numberWithCommas(totalMembers) + '</div>';
            } else if (parseInt(page) > 1) {
                paginationHtml = '<div style="display: inline-block; height: 55px;"><a onclick="loadMembers(\''+memberSince+'\', \''+memberSort+'\', ' + (parseInt(page)-1).toString() + ');"><img src="{% static 'img/font-awesome_4-7-0_arrow-circle-left_256_0_00b1f2_none.png' %}" style="height: 26px; margin-bottom: 5px;"></a></div><div class="current" style="margin-left: 20px; margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">members ' + ((parseInt(page)-1)*20+1).toString() + ' - ' + ((parseInt(page))*20).toString() + ' of ' + numberWithCommas(totalMembers) + '</div>';
            } else if (totalPages > 1) {
                paginationHtml = '<div class="current" style="margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">members 1 - 20 of ' + numberWithCommas(totalMembers) + '</div>';
            } else {
                paginationHtml = '<div class="current" style="margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">members 1 - ' + numberWithCommas(totalMembers) + ' of ' + numberWithCommas(totalMembers) + '</div>';
            }
            if (parseInt(page) < totalPages) {
                paginationHtml = paginationHtml + '<div style="display: inline-block; height: 55px;"><a onclick="loadMembers(\''+memberSince+'\', \''+memberSort+'\', ' + (parseInt(page)+1).toString() + ');"><img src="{% static 'img/font-awesome_4-7-0_arrow-circle-right_256_0_00b1f2_none.png'%}" style="height: 26px; margin-bottom: 5px;"></i></a></div>';
                $('#pagination-pages').html(paginationHtml);
                $('#pagination').css('display', 'inherit');
            } else {
                $('#pagination-pages').html(paginationHtml);
                $('#pagination').css('display', 'inherit');
            }
        });

    }

    function loadRegionMembers(region, regionname) {
        lastRegion = region;
        lastRegionName = regionname;
        $(".autocomplete-suggestions").hide();
        document.title = 'Members active in '+regionname;
        var regionval = region.split("|");
        var regiontype = regionval[0];
        var regiondata = regionval[1];
        if (regiontype == 'region') {
            var tempTitle = regionname.split(",");
            $("#region-title").html(tempTitle[0]);
            $("#hdnRegionId").val(regiondata);
            $("#hdnCountryId").val('');
            createCookie('suggest_region_id',regiondata,365);
            createCookie('suggest_country_id','',365);
        } else {
            $("#region-title").html(regionname);
            $("#hdnCountryId").val(regiondata);
            $("#hdnRegionId").val('');
            createCookie('suggest_country_id',regiondata,365);
            createCookie('suggest_region_id','',365);
        }
        $('#rq1').css('display','none');
        $('#see-worldwide').html('<a href="javascript:loadWorldwide();">see world</a>');
        $('#mobile-see-worldwide').html('<a href="javascript:loadWorldwide();"><i class="fa fa-globe-americas" aria-hidden="true"></i></a>');
        loadMembers(memberSince, memberSort, '1');
    }

    function loadWorldwide() {
        $("#hdnCountryId").val('');
        $("#hdnRegionId").val('');
        document.title = 'Members active in the World';
        createCookie('suggest_region_id','',365);
        createCookie('suggest_country_id','',365);
        $('#mobile-see-worldwide').html('');
        if (lastRegion.length > 0) {
            $('#see-worldwide').html('<a href="javascript:loadRegionMembers(\'' + lastRegion + '\', \'' + lastRegionName + '\');">see ' + lastRegionName + '</a>');
        } else {
            $('#see-worldwide').html('');
        }
        $('#rq1').css('display','none');
        $('#rq1').val('');
        $('#region-filter-button').fadeIn(300);
        $('#region-title').html('The World');
        loadMembers(memberSince, memberSort, '1');
    }

    function numberWithCommas(x) {
        return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    function viewMember(url) {
        window.location.href = url;
    }

    $(document).ready(function() {

        // Hide mobile filter bar on on scroll down
        var didScroll;
        var lastScrollTop = 0;
        var delta = 5;

        $(window).scroll(function(event){
            didScroll = true;
        });

        document.addEventListener("touchstart", ScrollStart, false);

        setInterval(function() {
            if (didScroll) {
                hasScrolled();
                didScroll = false;
            }
        }, 100);

        function ScrollStart() {
            //start of scroll event for iOS
            hasScrolled();
        }

        function hasScrolled() {
            var st = $(this).scrollTop();

            // Make sure they scroll more than delta
            if (Math.abs(lastScrollTop - st) <= delta)
                return;

            // If they scrolled down and are past the filter bar, add class .filter-scrollup.
            // This is necessary so you never see what is "behind" the navbar.
            if (st > lastScrollTop && st > 50) {
                // Scroll Down
                $('.filter-bar').hide();
            } else {
                // Scroll Up
                if (st + $(window).height() < $(document).height()) {
                    $('.filter-bar').show();
                }
            }
            lastScrollTop = st;
        }

        var viewport_height = $(window).height();
        var viewport_width = $(window).width();
        var footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
        var data_loading_margin = (viewport_height - footer_height) / 3;
        $('.data-loading').css('margin-top', data_loading_margin);
        $(window).resize(function() {
            viewport_height = $(window).height();
            viewport_width = $(window).width();
            footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
            data_loading_margin = (viewport_height - footer_height) / 3;
            $('.data-loading').css('margin-top', data_loading_margin);
        });

        $('#region-filter-button').on('click', function() {
            $('#region-filter-button').css('display','none');
            $('.mobile-section-title').css('display','none');
            $('#mobile-see-worldwide').css('display','none');
            $('#mobile-member-search').css('display','none');
            $('#rq1').val('');
            $('#rq1').fadeIn(300);
            $('#rq1').focus();
        });

        $('#mobile-member-search-icon').on('click', function() {
            $('#region-filter-button').css('display','none');
            $('.mobile-section-title').css('display','none');
            $('#mobile-see-worldwide').css('display','none');
            $('#mobile-member-search').css('display','none');
            $('#mmq1').val('');
            $('#mmq1').fadeIn(300);
            $('#mmq1').focus();
        });

        $('.timefilter-list').on('click', '.timefilter-item', function (e) {
            e.preventDefault();
            $("#timefilter-title").html($(this).html());
            memberSince = $(this).attr("href");
            $("#hdnTimeFilter").val(since);
            loadMembers(memberSince, memberSort, '1');
        });

        $('.mobile-timefilter-list').on('click', '.mobile-timefilter-item', function (e) {
            e.preventDefault();
            $("#mobile-timefilter-title").html($(this).html());
            memberSince = $(this).attr("href");
            $("#hdnTimeFilter").val(since);
            loadMembers(memberSince, memberSort, '1');
        });

        var vars = [], hash, since, sort, page;
        var q = document.URL.split('#')[1];
        if(q != undefined){
            q = q.split('&');
            for(var i = 0; i < q.length; i++){
                hash = q[i].split('=');
                vars.push(hash[1]);
                vars[hash[0]] = hash[1];
            }
        }

        if (vars['since'] != undefined) {
            since = vars['since'];
            $("#hdnTimeFilter").val(since);
            if (since == '90') {
                $("#timefilter-title").html('last 3 months');
                $("#mobile-timefilter-title").html('last 3 months');
            } else if (since == '365') {
                $("#timefilter-title").html('last year');
                $("#mobile-timefilter-title").html('last year');
            } else if (since == 'all') {
                $("#timefilter-title").html('all time');
                $("#mobile-timefilter-title").html('all time');
            } else {
                $("#timefilter-title").html('last 3 months');
                $("#mobile-timefilter-title").html('last 3 months');
            }
        } else {
            since = '90';
            $("#timefilter-title").html('last 3 months');
            $("#mobile-timefilter-title").html('last 3 months');
        }

        if (vars['order'] != undefined) {
            sort = vars['order'];
        } else {
            sort = 'new_peaks';
        }

        if (vars['page'] != undefined) {
            page = vars['page'];
        } else {
            page = '1';
        }

        loadMembers(since, sort, page);

    });

</script>

{% endblock %}

