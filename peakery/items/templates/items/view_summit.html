{% extends "base.html" %}
{% load thumbnail %}
{% load avatar_tags %}

{% block title %}{{ summit.item.name }} {{ summit.item.get_elevation }}{% endblock %}
{% block description %}{{ summit.log|striptags|truncatewords:20 }}{% endblock %}
{% block image_rel %}{% thumbnail summit.get_user_photo 120x120 %}{% endblock %}

{% block meta_facebook %}
    <meta property="og:title" content="{{ summit.item.name }} - {{ summit.item.get_elevation }}"/>
    <meta property="og:site_name" content="peakery.com"/>
    <meta property="og:type" content="article"/>
    <meta property="og:url" content="{{ summit.get_absolute_url }}"/>
    <meta property="og:image" content="{% if summit.get_user_photo %}{% thumbnail summit.get_user_photo 120x120 %}{% else %}{% thumbnail summit.item.get_thumbnail 120x120 %}{% endif %}"/>
    <meta property="og:description" content="{% if summit.log %}{{ summit.log|striptags|truncatewords:20 }}{% else %}{{ summit.item.description|striptags|truncatewords:20 }}{% endif %}"/>
{% endblock %}

{% block extrajs %}
<script type="text/javascript">
    $(document).ready(function(){
        //$("span#seconds").countdown({seconds:5, callback:'go_to_peak_page()'});
        window.setInterval(go_to_peak_page(), 3000)
    });
    function go_to_peak_page(){location = '{{ summit.item.get_absolute_url }}'}
</script>
{% endblock %}


{% block content %}

<div class="content_l">

    <h1 class="peak_title">
        {{ summit.item.name }} <span>{{ summit.item.get_elevation }}</span>
        <span class="extrainfo">{{ summit.item.get_ubication_names_title }}</span>
    </h1>
    <center>
        <img src="{{ MEDIA_URL }}img/misc/ajax1.gif" />
    </center>
    <!--<h3>
        Redirecting to {{ summit.user }} summit log info in <span id="seconds">5</span> seconds...
    </h3>!-->

    
</div><!-- END content_left -->


{% endblock %}
