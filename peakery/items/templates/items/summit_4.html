{% load thumbnail humanize%}
<script type="text/javascript">

    $(document).ready(function(){
        $(document).bind('close.facebox', function() {location.reload(true) });
        $("#facebox .close").show();
    });

    $("a#remainingList").click(function(e){
        e.preventDefault();
        $("#MorePeakList").show();
        $("#remainingList").parent().hide();
    });

    $("a.embtn_toggle").click(function(){
        $("a.embtn_toggle").toggle();
    });

</script>


<div id="bagview4" class="summitLogLast facebox">
    <div class="new_bag congrats set2">
        <div class="clearfix mainContainer">
            <div class="leftCol">
                {% if not hide_summit_bagde %}
                    <h1><span style="color:#00B1F2">
                        {% if times > 1 %}
                            {{ peak.name }}</span> summited again!
                        {% else %}
                            {{ peak.name }}</span> summited!
                        {% endif %}
                    </h1>
                    <div class="peakimg {% if times > 1 %}xtimes{% endif %}">
                        <div class="peakimgw" style="height: 233px; width: 233px; background: url('{% thumbnail peak.get_thumbnail 233x233 crop %}')">
                            {% if times > 1 %}
                                <span class="times">{{ times }}x</span>
                            {% endif %}
                            <span class="peakname">
                                <span class="name">{{ peak.name }}</span>
                                <span class="info">{{ peak.get_elevation }}</span>
                            </span><!-- ENd peakname -->
                        </div><!-- ENd peakimg -->
                    </div>
                    <div class="a">
                        {% if times > 1 %}
                            <h5 style="font-weight: bold;">Your <span style="color:#00B1F2">summit badge</span> now shows {{ times }} ascents.</h5>
                        {% else %}
                            <h5 style="font-weight: bold;">You earned the <span style="color:#00B1F2">summit badge</span> </h5>
                        {% endif %}
                
                    </div>
                {% endif %}
            </div>
            <div class="rightCol">
                <div class="BadgePlus">
                    <ul class="j">
                        {% if badge_plus.0 == 'First' %}
                            <li id="firstAscent" class="clearfix">
                                <div class="leftCol">
                                    <img src="{{ MEDIA_URL }}img/peakbaggers/first-ascent.png"/>
                                </div>
                                <div class="rightCol">
                                    <span>Congrats! You snagged the <span style="color:#00B1F2">First Ascent</span></span>
                                    <span class="mini">This peak will forever bear your name</span>
                                </div>
                            </li>
                        {% endif %}
                        {% if badge_plus.0 == 'Multiple' %}
                            <li id="multipleTimes" class="clearfix">
                                <div class="leftCol">
                                    <span class="big">{{ badge_plus.1 }}x</span>
                                </div>
                                <div class="rightCol">
                                    <span>Your <span style="color:#00B1F2"> {{ badge_plus.1|ordinal }} summit </span> of {{ peak.name }}</span>
                                </div>
                            </li>
                        {% endif %}
                        {% if badge_plus.0 == 'Last' %}
                            <li id="youAreTheXPerson" class="clearfix">
                                <div class="leftCol">
                                    <span class="big">#{{ badge_plus.1 }}</span>
                                </div>
                                <div class="rightCol">
                                    <span>You're the <span style="color:#00B1F2">{{ badge_plus.1|ordinal }} person</span> to summit it in {{ summit.date.year }}</span>
                                </div>
                            </li>
                        {% endif %}
                        {% if badge_plus.0 == 'LastNow' %}
                            <li id="youAreTheXPerson" class="clearfix">
                                <div class="leftCol">
                                    <span class="big">#{{ badge_plus.1 }}</span>
                                </div>
                                <div class="rightCol">
                                   <span>You're the <span style="color:#00B1F2">{{ badge_plus.1|ordinal }} person</span> to summit this peak</span>
                                </div>
                            </li>
                        {% endif %}


                    </ul>
                </div>
                <ul class="j k">
                    {% for item_list in item_lists %}
                        <li class="clearfix">
                            <div class="leftCol">
                                <img src="{% thumbnail item_list.0.thumbnail 70x70 crop %}"/>

                            </div>
                            <div class="rightCol">
                                {% if item_list.1 == item_list.2 %}
                                    <span>CONGRATS!! You finished the peak list</span><br/>
                                {% else %}
                                    <span>You made progress in the peak list</span><br/>
                                {% endif %}
                                <span style="color:#00B1F2">{{ item_list.0 }}</span>
                                <div>
                                    <div class="progressbar">
                                        {% if item_list.3 < 30 %}
                                            <span class="progress" style="width: {{ item_list.3 }}%; display: inline-block; float: left;"></span>
                                            <p style="display: inline-block; font-size: 10px; color: #000; float: left; line-height: 23px; margin-left: 5px; padding: 0; width: auto;">{{ item_list.3 }}%</p>
                                        {% else %}
                                            <div style="width: {{ item_list.3 }}%;" class="progress" title="{{ item_list.3 }}%">{{ item_list.3 }}%</div>
                                        {% endif %}
                                        <span class="extra_info">{{ item_list.2 }} of {{ item_list.1 }}</span>
                                    </div>
                                </div>
                            </div>
                        </li>
                    {% endfor %}
                </ul>
                    {% if remaining_item_lists %}
                        <div class="remainingList">
                            <a id="remainingList" href="#" >+ {{ remaining_item_lists|length }} other peak list{{ remaining_item_lists|length|pluralize }}</a>
                        </div>
                    {% endif %}
                <div id="MorePeakList" style="display:none">
                    <ul class="j k">
                        {% for item_list in remaining_item_lists %}
                            <li class="clearfix">
                                <div class="leftCol">
                                    <img src="{% thumbnail item_list.0.thumbnail 70x70 crop %}" />
                                </div>
                                <div class="rightCol">
                                    {% if item_list.1 == item_list.2 %}
                                        <span>CONGRATS!! You finished the peak list</span><br/>
                                    {% else %}
                                        <span>You made progress in the peak list</span><br/>
                                    {% endif %}
                                    <span style="color:#00B1F2">{{ item_list.0 }}</span>
                                    <div>
                                        <div class="progressbar">
                                            {% if item_list.3 < 30 %}
                                            <span class="progress" style="width: {{ item_list.3 }}%; display: inline-block; float: left;"></span>
                                            <p style="display: inline-block; font-size: 10px; color: #000; float: left; line-height: 23px; margin-left: 5px; padding: 0; width: auto;">{{ item_list.3 }}%</p>
                                            {% else %}
                                            <div style="width: {{ item_list.3 }}%;" class="progress" title="{{ item_list.3 }}%">{{ item_list.3 }}%</div>
                                            {% endif %}
                                            <span class="extra_info">{{ item_list.2 }} of {{ item_list.1 }}</span>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>

{% comment %}<h2>Want to share your summit log?</h2>
<div class="peak_info">
    <div class="col_1">
        <h3>{{ peak.name }} <span>bagged {% if summit.date_entered %}{{ summit.date }}{% else %}date unknown{% endif %}</span></h3>
        <p>{{ summit.log|default:"" }}</p>
        <p>{% if summit.get_comma_separated_companions %}<strong>Companions:</strong> {{ summit.get_comma_separated_companions }}{% endif %}</p>
    </div>
    <div class="col_2">
        <ul class="vert peak_photos">
            {% for image in summit.get_summit_images %}
                <li>
                    <img src="{% thumbnail image.image 130x100 crop %}" />
                    <span>{{ image.caption|default:"" }}</span>
                </li>
                {% empty %}
            {% endfor %}
        </ul>
    </div>
</div><!-- ENd peak_desc -->{% endcomment %}

        <div class="block socialButtons">

            <ul class="horz lefted">
                <li>
                    <iframe src="http://www.facebook.com/plugins/like.php?href={{ peak.get_absolute_url }}&amp;layout=button_count&amp;show_faces=false&amp;width=100&amp;action=like&amp;font=helvetica&amp;colorscheme=light&amp;height=21" scrolling="no" frameborder="0" style="border:none; overflow:hidden; width:100px; height:21px;" allowTransparency="true"></iframe>
                </li>
                <li>
                    <a href="http://twitter.com/share" class="twitter-share-button" data-url="{{ peak.get_absolute_url }}" data-text="Awesome! I summited {{ peak.name }}" data-count="horizontal" data-via="peakery">Tweet</a><script type="text/javascript" src="http://platform.twitter.com/widgets.js"></script>
                </li>
                <li class="google">
                    <g:plusone size="medium"></g:plusone>
                </li>
            </ul>

            <a id="done-share"  href="{% url "summit_5" peak.id summit.id %}" class="btn set2 righted">Done</a>
        </div>


{% comment %}
<div class="block">
    <a id="fb-share-button" class="btn set2 lefted fb-btn" href="http://www.facebook.com/sharer.php?u={{ summit.get_absolute_url }}&t={{ peak.name }}-{{ peak.get_elevation }}">Share on Facebook</a>
    <a id="tw-share-button" class="btn set2 center tw-btn" href="http://twitter.com/home/<USER>">Share on twitter</a>
    <a id="email-share-button" rel="facebox" class="btn set3 righted" href="{% url "hubaddContact" summit.id %}">Email</a>
</div>{% endcomment %}

        <div style="position: relative; bottom: 69px; left: 242px; width: 80px; text-align: center"></div>
    </div><!-- ENd new_bag -->
</div><!-- ENd bagview4 -->
<div id="bagview4_email">
</div>
