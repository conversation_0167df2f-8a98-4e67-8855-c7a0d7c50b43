{% load thumbnail %}
<html>
  <head>
    <title>Peakery - {{ peak.name }} - {% block title %}{% endblock %}</title>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.4.4/jquery.min.js"></script>
    <script type="text/javascript" src="{{ MEDIA_URL }}js/jquery.panzoom.min.js"></script>
    {% block extra_js %}{% endblock %}
    <link href="{{MEDIA_URL}}css/style.css" rel="stylesheet" type="text/css" media="screen" />
    <link href="{{MEDIA_URL}}css/peak_image.css" rel="stylesheet" type="text/css" media="screen" />
    <script type="text/javascript">
        $(document).ready(function(){

            $.panzoom();

        });
    </script>
  </head>
  <body>

<div id="container">

          <header>
              <div class="left">
                  <div class="logo">
                      <a href="/">Peakery</a>
                  </div>
                  <div class="name">
                      <a href="{{ peak.get_absolute_url }}">{{ peak.name }}</a>
                  </div>
                  <div class="details">
                      <a href="{{ peak.get_absolute_url }}">see details</a>
                  </div>
              </div>

              <div class="ri">
                  <ul class="nearest_items">
                      <li class="title">Nearby peaks:</li>
                      {% for i in nearest_items %}
                      <li>
                          <a class="img" href="{{ i.get_absolute_url }}"><img src="{% thumbnail i.get_thumbnail 60x40 crop %}"></a>
                          <a href="{{ i.get_absolute_url }}">{{ i.name }}</a>
                          <span>{{ i.distance|floatformat:2 }} miles</span>
                      </li>
                      {% endfor %}
                  </ul>
              </div>
          </header>


          {% block item_media %}
          {% endblock %}
    
          <div id="img-footer">
              <div class="left">

                  {% if peak.range %}
                  <div>See more <strong>{{ peak.range }}</strong> peaks</div>
                  <ul>
                     {% for i in more_from_range %}
                     <li><a href="{{ i.get_absolute_url }}">{{ i.name }}</a></li>
                     {% endfor %}
                  </ul>
                  {% endif %}

              </div>
              <div class="ri">
                  <div>AD</div>
              </div>
          </div>

</div>

  </body>
</html>
