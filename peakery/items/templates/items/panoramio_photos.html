<script type="text/javascript">
    $(document).ready(function() {
        min_h = 160;
        $("ul#peak-photos li").each(function(k, v) {
            h = parseInt(($(v).css("height")).replace('px', ''));
            if (h > min_h) {
                min_h = h
            }
        });
        $("ul#peak-photos li").css("height", min_h + "px")
    });
</script>
{% for p in photos %}
    <li style="background-image: url('{{ p.photo_url }}')" class="panoramioPhoto photography">
        <a href="{{ p.photo_url }}" title="{% if p.caption != "" %}{{ p.caption }}{% else %}peakery{% endif %}"
           class="photoLink" author="{{ p.user_name }}" authorlink="{{ p.user_page }}" panoramio="true"
           {% if forloop.counter0 == 0 %}id="trigger"{% endif %}></a>
                <img src="{{ p.photo_url }}" alt="{{ p.caption }}" width="230" height="160" />
        {% comment %}{% if p.caption %}
        <span class="">
            {{ p.caption }}
        </span>
    {% endif %}
    <a class="font11px" href="{{ p.user_page }}" target="_blank">
        {{ p.user_name }}
    </a>{% endcomment %}
        <span class="data">
            {% if p.caption != "" %}
                <p class="caption" style="display: none;">{{ p.caption }}</p>
            {% endif %}
            <p class="bagger">{{ p.user_name }} &bull; Panoramio</p>
        </span>
    </li>
{% endfor %}