{% extends "base.html" %}
{% load thumbnail %}
{% load avatar_tags %}
{% load item_tags %}

{% block title %}Recent Summits{% endblock %}
{% block titlemeta %}Recent Summits - {% endblock %}
{% block description %}Recent Summits on peakery{% endblock %}

{% block content %}

<div id="recent_summits">

    <h1>Recent summits</h1>

    <div class="summits">
        {% autopaginate summits 32 %}
        {% for s in summits %}
        <div class="summit">
            <a href="{{ s.item.get_absolute_url }}"><img src="{% thumbnail s.item.get_thumbnail 220x179 crop %}" alt="{{ s.item.name }}" /></a>
            <div>
                <a href="{{ s.item.get_absolute_url }}">{{ s.item.name }}</a>
            </div>
            <span>
                summited by <a href="{% url "user_profile" s.user %}">{{ s.user }}</a>
            </span>
        </div>
        {% endfor %}
    </div>

    <div class="pagination">
        {% paginate %}
    </div>

</div><!-- END content_r -->



{% endblock %}

