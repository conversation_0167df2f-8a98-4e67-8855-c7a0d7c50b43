{% extends "base_no_header_footer.html" %}

{% load static %}
{% load avatar_tags %}
{% load item_tags %}
{% load cache %}
{% load humanize %}

{% block title %}Log your climb of {{ peak.name }}{% endblock %}
{% block titlemeta %}Log your climb of {{ peak.name }}{% endblock %}
{% block description %}Log your climb of {{ peak.name }}{% endblock %}

{% block mapbox %}
{% include "mapbox/mapbox.html" %}
{% endblock %}

{% block full_height_form %}
<form id="summitlog_form" class="summitlog_form" method="POST" action="{% url "summit_2" peak.id %}">
{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row" style="border-bottom: none;">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 form-header-bar">
            <div class="hidden-xs form-header-bar-img-div"><img class="form-header-bar-img" src="{{ peak.get_thumbnail_480 }}" /></div>
            <span style="font-size: 24px; font-weight: 600; color: #fff;">
            <div id="breadcrumbs" style="margin-left: 15px;">
                <div class="pull-right cancel-x-button-div">
                    <a class="cancel-x-button" onclick="cancelEdit();"><i class="fa fa-times"></i></a>
                </div>
                <div class="pull-right save-changes-button-div">
                    <button class="btn btn-secondary set2 input save-changes-button" type="submit" id="add_peak2" disabled>Save log</button>
                </div>
                <div>
                    <div class="form-header-title ellipsis"><span class="hidden-xs hidden-sm">Log your climb of </span><span class="header-peak-name">{{ peak.name }}</span></div>
                </div>
            </div>
            </span>
        </div>
    </div>
{% endblock %}

{% block content %}

<script src="{% static 'vendor/s3.fine-uploader/s3.fine-uploader.js' %}"></script>

<style>

    html, body {
        letter-spacing: .03125em;
    }

    body.modal-open {
        overflow: visible;
    }

    .help-block {
        display: block;
        margin-top: -7px;
        margin-bottom: 10px;
        color: #ff0000;
    }

    .remove-photo {
        cursor: pointer;
        color: #fff;
        font-size: 24px;
    }

    .remove-photo:hover {
        color: #ccc;
    }

    .ui-state-active {
        color: #fff;
        background-color: #f24100;
    }

   @media screen and (min-width: 1024px) {
       .gpx-stat-1 {
           border-right: solid 1px #eeeeee;
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-2 {
           background-color: #f6f6f6;
           border-right: solid 1px #eeeeee;
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-3 {
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-4 {
           border-right: solid 1px #eeeeee;
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-5 {
           background-color: #f6f6f6;
           border-right: solid 1px #eeeeee;
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-6 {
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .header-peak-name {
            color: #f24100;
        }
        .header-peak {
            font-size: 20px;
            font-weight: 300;
        }
        .close-log-edit {
            margin-right: 20px;
        }
        .header-help {
            color: #999;
            font-size: 14px;
            font-weight: 300;
            margin-left: 10px;
        }
        .field-title {
            font-size: 21px;
            font-weight: 700;
        }
        .form-header-title {
            font-size: 20px;
            font-weight: 300;
        }
        #extra-photo-1 {
            top: 74px;
            left: 330px;
        }
        #extra-photo-2 {
            top: 74px;
            left: 640px;
        }
        #extra-photo-3 {
            top: 74px;
            left: 950px;
        }
        #hide-new-summit-route-span {
            top: 94px;
        }
        #add_peak2 {
            width: 140px;
        }
        #gm-custom-mapunits {
            right: 142px;
        }

        ::-webkit-input-placeholder { font-size: 18px; }
        ::-moz-placeholder { font-size: 18px; } /* firefox 19+ */
        :-ms-input-placeholder { font-size: 18px; } /* ie */
        input:-moz-placeholder { font-size: 18px; }
        input { font-size: 18px; }
   }
   @media screen and (min-width: 768px) and (max-width: 1023px) {
       .gpx-stat-1 {
           border-right: solid 1px #eeeeee;
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-2 {
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-3 {
           background-color: #f6f6f6;
           border-right: solid 1px #eeeeee;
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-4 {
           background-color: #f6f6f6;
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-5 {
           border-right: solid 1px #eeeeee;
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-6 {
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .header-peak-name {
            color: #fff;
        }
        .header-peak {
            font-size: 18px;
            font-weight: 500;
        }
        .close-log-edit {
            margin-right: 0px;
        }
        .header-help {
            color: #999;
            font-size: 12px;
            font-weight: 300;
            margin-left: 10px;
        }
        .field-title {
            font-size: 18px;
            font-weight: 700;
        }
        .form-header-title {
            font-size: 18px;
            font-weight: 500;
        }
        #extra-photo-1 {
            top: 48px;
            left: 330px;
            margin-top: 15px !important;
        }
        #extra-photo-2 {
            top: 48px;
            left: 640px;
        }
        #extra-photo-3 {
            top: 48px;
            left: 950px;
        }
        #hide-new-summit-route-span {
            top: 87px;
        }
        #add_peak2 {
            width: 140px;
        }

        ::-webkit-input-placeholder { font-size: 16px; }
        ::-moz-placeholder { font-size: 16px; } /* firefox 19+ */
        :-ms-input-placeholder { font-size: 16px; } /* ie */
        input:-moz-placeholder { font-size: 16px; }
        input { font-size: 16px; }
   }
   @media screen and (min-width: 1px) and (max-width: 767px) {
       .gpx-stat-1 {
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-2 {
           background-color: #f6f6f6;
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-3 {
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-4 {
           background-color: #f6f6f6;
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-5 {
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       .gpx-stat-6 {
           background-color: #f6f6f6;
           border-top: solid 1px #eeeeee;
           border-bottom: solid 1px #eeeeee;
           height: 180px;
       }
       #txtAddActivityTag, #txtAddChallengeTag, #txtAddGearTag, #summit-related-link {
            width: 70%;
            font-size: 16px;
        }
        #btnAddActivityTag, #btnAddChallengeTag, #btnAddGearTag, #btnAddRelatedLink {
            width: 84px;
        }
       input {
            font-size: 16px;
            font-weight: 300;
        }
        form textarea {
            font-size: 16px;
            font-weight: 300;
        }
        .header-peak-name {
            color: #fff;
        }
        .header-peak {
            font-size: 18px;
            font-weight: 500;
        }
        .close-log-edit {
            margin-right: 0px;
        }
        .header-help {
            color: #999;
            font-size: 11px;
            font-weight: 300;
            margin-left: 10px;
        }
        .field-title {
            font-size: 16px;
            font-weight: 700;
        }
        .summitlog-section {
            margin-top: 20px;
        }
        .field-title-spacer {
            height: 0px;
        }
        .form-header-title {
            font-size: 14px;
            font-weight: 500;
        }
        #extra-photo-1 {
            top: 48px;
            left: 330px;
        }
        #extra-photo-2 {
            top: 48px;
            left: 640px;
        }
        #extra-photo-3 {
            top: 48px;
            left: 950px;
        }
        #hide-new-summit-route-span {
            top: 75px;
        }

        .autocomplete-suggestions {
            height: 270px !important;
        }
        .summit-companion {
            width: 100%;
        }
        #add_peak2 {
            width: 110px;
        }

        ::-webkit-input-placeholder { font-size: 16px; }
        ::-moz-placeholder { font-size: 16px; } /* firefox 19+ */
        :-ms-input-placeholder { font-size: 16px; } /* ie */
        input:-moz-placeholder { font-size: 16px; }
        input { font-size: 16px; }
   }

   @media screen and (max-width: 767px) and (min-width: 1px) {
       #content-body {
           margin-top: 0px;
           padding-bottom: 0px;
       }
       .content-pane {
           margin-top: 20px;
       }
   }
    @media screen and (min-width: 768px) {
        #content-body {
           margin-top: 30px;
       }
        .content-pane {
           margin-top: 0px;
       }
        input {
            font-size: 18px;
            font-weight: 300;
        }
        form textarea {
            font-size: 18px;
            font-weight: 300;
        }
        #txtAddActivityTag, #txtAddChallengeTag, #txtAddGearTag {
            width: 33%;
            font-size: 18px;
        }
        #summit-related-link {
            width: 70%;
            font-size: 18px;
        }
        #btnAddActivityTag, #btnAddChallengeTag, #btnAddGearTag, #btnAddRelatedLink {
            width: 150px;
        }
        .summitlog-section {
            margin-top: 60px;
        }
        .field-title-spacer {
            height: 10px;
        }
        .summit-companion {
            width: 46%;
        }
    }
    @media screen and (min-width: 1px) and (max-width: 1279px) {
         .no-header-container {
           padding-left: 0px;
       }
    }
    ::-webkit-input-placeholder { font-weight: 300; }
    ::-moz-placeholder { font-weight: 300; } /* firefox 19+ */
    :-ms-input-placeholder { font-weight: 300; } /* ie */
    input:-moz-placeholder { font-weight: 300; }
    #ui-datepicker-div { display: none; }

    #summitlog-files li {
        float: left;
        margin-right: 7px;
    }

    ul#summitlog-files .qq-upload-button.loading {
        height: 63%;
        padding-top: 95px;
        padding-left: 150px;
    }

    ul#summitlog-files li div.a:hover {
        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        box-shadow: none;
    }

    .slow .toggle-group { transition: background-color 2.5s ease; -webkit-transition: background-color 2.5s ease; }

    .qq-upload-list {
        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        box-shadow: none;
    }

    .qq-upload-list li.qq-upload-success {
        background-color: #f6f6f6;
        color: #424242;
        border-bottom: none;
        border-top: none;
    }

    .qq-upload-list li.qq-in-progress {
        background-color: #f6f6f6;
        color: #424242;
        border-bottom: none;
        border-top: none;
    }

    div.summitlog.caption {
        width: 100% !important;
    }

    @media screen and (min-width: 1px) and (max-width: 479px) {
        .qq-thumbnail-wrapper, .qq-thumbnail-selector {
            width: 120px;
            max-width: 120px;
            min-height: 80px;
        }
        .remove-photo {
            width: 120px;
            max-width: 120px;
            right: 6px;
            top: 11px;
            vertical-align: top;
            position: absolute;
            display: table-cell;
            text-align: right;
        }
    }
    @media screen and (min-width: 480px) and (max-width: 767px) {
        .qq-thumbnail-wrapper, .qq-thumbnail-selector {
            width: 200px;
            max-width: 200px;
            min-height: 133px;
        }
        .remove-photo {
            width: 200px;
            max-width: 200px;
            right: 6px;
            top: 11px;
            vertical-align: top;
            position: absolute;
            display: table-cell;
            text-align: right;
        }
    }
    @media screen and (min-width: 768px) {
        .qq-thumbnail-wrapper, .qq-thumbnail-selector {
            width: 200px;
            max-width: 200px;
            min-height: 133px;
        }
        .remove-photo {
            width: 200px;
            max-width: 200px;
            right: 6px;
            top: 11px;
            vertical-align: top;
            position: absolute;
            display: table-cell;
            text-align: right;
        }
    }

    .qq-alert-dialog-selector, .qq-confirm-dialog-selector, .qq-prompt-dialog-selector {
        margin: auto;
        padding: 20px;
    }

    ul#summitlog-gpx-files li div.a {
        background-color: transparent;
        margin: 0px;
    }


</style>

    <script type="text/template" id="qq-image-template">
        <div class="qq-uploader-selector qq-uploader" qq-drop-area-text="" style="max-height: none; background-color: #f6f6f6; border: none; padding: 0px; min-height: 0px;">
            <div class="qq-upload-drop-area-selector qq-upload-drop-area" qq-hide-dropzone>
                <span class="qq-upload-drop-area-text-selector"></span>
            </div>
            <ul class="qq-upload-list-selector qq-upload-list" aria-live="polite" aria-relevant="additions removals" style="max-height: none;">
                <li style="padding: 5px;">
                    <div style="display: flex; display: -webkit-flex;">
                        <div class="qq-thumbnail-wrapper" style="position: relative; align-content: stretch;">
                            <div>
                                <img class="qq-thumbnail-selector" qq-max-size="300" qq-server-scale style="margin-right: 0px;">
                            </div>
                            <div id="file-2-remove-photo" class="remove-photo"><i style="text-shadow: 0px 0px 5px #ccc;" class="fa fa-times" aria-hidden="true"></i></div>
                        </div>
                        <div class="summitlog caption textareaContainer" style="margin-top: 0px; align-content: stretch; position: relative;">
                            <textarea id="" name="" class="blur addACaption" placeholder="write a caption..." style="width: 99% !important; height: 100% !important; padding-top: 16px; color: #333 !important; position: absolute;"></textarea>
                            <input class="hiddenPhoto" type="hidden" id="" name="" value="">
                        </div>
                        <div style="position: absolute;">
                            <button type="button" class="qq-upload-retry-selector qq-upload-retry btn btn-secondary">Retry</button>
                        </div>
                    </div>
                </li>
            </ul>
            <div class="qq-upload-button-selector btn btn-secondary" style="width: 245px; height: 55px; font-size: 18px; text-align: center; background-color: #33c1f5; margin: 5px; z-index: 1;">
                <div>Choose JPG file(s)</div>
            </div>
            <span class="qq-drop-processing-selector qq-drop-processing">
                <span>Processing dropped files...</span>
                <span class="qq-drop-processing-spinner-selector qq-drop-processing-spinner"></span>
            </span>

            <dialog class="qq-alert-dialog-selector">
                <div class="qq-dialog-message-selector"></div>
                <div class="qq-dialog-buttons">
                    <button type="button" class="qq-cancel-button-selector btn btn-secondary">Close</button>
                </div>
            </dialog>

            <dialog class="qq-confirm-dialog-selector">
                <div class="qq-dialog-message-selector"></div>
                <div class="qq-dialog-buttons">
                    <button type="button" class="qq-cancel-button-selector btn btn-secondary">No</button>
                    <button type="button" class="qq-ok-button-selector btn btn-secondary">Yes</button>
                </div>
            </dialog>

            <dialog class="qq-prompt-dialog-selector">
                <div class="qq-dialog-message-selector"></div>
                <input type="text">
                <div class="qq-dialog-buttons">
                    <button type="button" class="qq-cancel-button-selector btn btn-secondary">Cancel</button>
                    <button type="button" class="qq-ok-button-selector btn btn-secondary">Ok</button>
                </div>
            </dialog>
        </div>
    </script>

    <script type="text/template" id="qq-gpx-template">
        <div class="qq-uploader-selector qq-uploader" qq-drop-area-text="" style="max-height: none; background-color: #f6f6f6; border: none; padding: 0px; min-height: 0px;">
            <div class="qq-upload-drop-area-selector qq-upload-drop-area" qq-hide-dropzone>
                <span class="qq-upload-drop-area-text-selector"></span>
            </div>
            <ul class="qq-upload-list-selector qq-upload-list" aria-live="polite" aria-relevant="additions removals" style="max-height: none; display: none;">
                <li style="padding: 5px;">
                    <div style="display: flex; display: -webkit-flex;">
                        <div class="qq-thumbnail-wrapper" style="position: relative; align-content: stretch;">
                            <div>
                                <img class="qq-thumbnail-selector" qq-max-size="300" qq-server-scale style="margin-right: 0px;">
                            </div>
                        </div>
                    </div>
                </li>
            </ul>
            <div id="gpx-upload-button" class="qq-upload-button-selector btn btn-secondary" style="width: 245px; height: 55px; font-size: 18px; text-align: center; background-color: #33c1f5; margin: 5px; z-index: 1;">
                <div id="gpx-upload-button-div">Choose GPX file</div>
            </div>
            <span class="qq-drop-processing-selector qq-drop-processing">
                <span>Processing dropped files...</span>
                <span class="qq-drop-processing-spinner-selector qq-drop-processing-spinner"></span>
            </span>

            <dialog class="qq-alert-dialog-selector">
                <div class="qq-dialog-message-selector"></div>
                <div class="qq-dialog-buttons">
                    <button type="button" class="qq-cancel-button-selector btn btn-secondary">Close</button>
                </div>
            </dialog>

            <dialog class="qq-confirm-dialog-selector">
                <div class="qq-dialog-message-selector"></div>
                <div class="qq-dialog-buttons">
                    <button type="button" class="qq-cancel-button-selector btn btn-secondary">No</button>
                    <button type="button" class="qq-ok-button-selector btn btn-secondary">Yes</button>
                </div>
            </dialog>

            <dialog class="qq-prompt-dialog-selector">
                <div class="qq-dialog-message-selector"></div>
                <input type="text">
                <div class="qq-dialog-buttons">
                    <button type="button" class="qq-cancel-button-selector btn btn-secondary">Cancel</button>
                    <button type="button" class="qq-ok-button-selector btn btn-secondary">Ok</button>
                </div>
            </dialog>
        </div>
    </script>

    <div class="container" style="padding-left: 0px; padding-right: 0px;">

        <div class="content-pane" style="background-color: #f6f6f6; padding-top: 40px;">

            <input type="hidden" name="summit_id" value="{{ summit.id }}">
            <!--STEP 2-->

            <div class="row row-full-width">
                <div class="col-md-12">
                    <fieldset>
                        <div class="form-group" id="summit-field-date">
                            <span class="field-title" style="display: block;">Date of climb<span class="header-help" style="margin-top: 15px; padding-bottom: 0px; color: #999;">estimate if don't know exact date</span></span>
                            <div class="field-title-spacer"></div>
                            <div class="ui-datepicker-title">
                                <select class="ui-datepicker-month">
                                    <option value="0">Jan</option>
                                    <option value="1">Feb</option>
                                    <option value="2">Mar</option>
                                    <option value="3">Apr</option>
                                    <option value="4">May</option>
                                    <option value="5">Jun</option>
                                    <option value="6">Jul</option>
                                    <option value="7">Aug</option>
                                    <option value="8">Sep</option>
                                    <option value="9">Oct</option>
                                    <option value="10">Nov</option>
                                    <option value="11">Dec</option>
                                </select>
                                <select class="ui-datepicker-year"></select>
                            </div>
                            <div id="date"></div>
                            <input type="hidden" name="summit-date" id="summit-date">
                        </div>
                    </fieldset>
                </div>
            </div>

            <div class="row row-full-width summitlog-section">
                <div class="col-md-12">
                    <fieldset>
                        <div id="summit-field-outcome">
                            <span class="field-title" style="display: block;">Outcome</span>
                            <div class="btn-group" data-toggle="buttons">
                                <label class="btn active">
                                <input type="radio" name="rdoOutcome" id="rdoSuccess" value="0" checked><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i> <span>  successful summit</span>
                                </label>
                                <label class="btn">
                                <input type="radio" name="rdoOutcome" id="rdoAttempt" value="1"><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i><span> attempt</span>
                                </label>
                            </div>
                        </div>
                    </fieldset>
                </div>
            </div>

            <div class="row row-full-width summitlog-section">
                <div class="col-md-12">
                    <fieldset class="whatHappened">
                        <span class="field-title" style="display: block;">Trip report</span>
                        <div class="field-title-spacer"></div>
                        <textarea onkeyup="textAreaAdjust();" name="log" class="route-step-text" id="summit" style="display: block; height: 100px; resize: none; width: 100%; border: 1px solid #CCC; padding: 8px 10px; overflow: hidden;" placeholder="what happened out there? share your story..."></textarea>
                    </fieldset>
                </div>
            </div>

            <div class="row row-full-width summitlog-section">
                <div class="col-md-12">
                    <span class="field-title" style="display: block;">Photos<span class="header-help" style="margin-top: 15px; padding-bottom: 0px; color: #999;">add photos from your climb</span></span>
                    <div class="field-title-spacer"></div>
                    <div id="uploader"></div>
                </div>
            </div>

            <div class="row row-full-width hidden-xs hidden-sm summitlog-section">
                <div class="col-md-12">
                    <span class="field-title">GPS track<span class="header-help" style="margin-top: 15px; padding-bottom: 0px; color: #999;">add a GPX file from your climb</span></span>
                    <span class="pull-right"><a style="cursor: pointer;" id="remove-gpx-file" style="display: none;">Remove GPX file</a></span>
                    <div class="field-title-spacer"></div>
                    <div id="map-canvas" style="width: 100%; height: 0px; display: none;">
                        <div id="gm-custom-mapunits" class="gmnoprint gm-style-mtc" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 100px; top: 0px;">
                            <div id="gm-custom-mapunitsbutton" draggable="false" title="Change map units" style="cursor: pointer; direction: ltr; overflow: hidden; text-align: center; position: relative; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 11px; -webkit-background-clip: padding-box; background-clip: padding-box; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; font-weight: 500;">
                                <span id="gm-custom-mapunitsbutton-label-feet" class="gm-custom-mapunits-selected">Feet</span> | <span id="gm-custom-mapunitsbutton-label-meters" class="gm-custom-mapunits-unselected">Meters</span>
                            </div>
                        </div>
                        <div id="gm-custom-maptype" class="gmnoprint gm-style-mtc" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 104px; right: 0px; top: 0px;">
                            <div id="gm-custom-mapbutton" draggable="false" title="Change map style" style="cursor: pointer; direction: ltr; overflow: hidden; text-align: left; position: relative; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 11px; -webkit-background-clip: padding-box; background-clip: padding-box; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; font-weight: 500;">
                                <span id="gm-custom-mapbutton-label">Terrain</span><img src="https://maps.gstatic.com/mapfiles/arrow-down.png" draggable="false" style="-webkit-user-select: none; border: 0px; padding: 0px; margin: -2px 0px 0px; position: absolute; right: 6px; top: 50%; width: 7px; height: 4px;">
                            </div>
                            <div id="gm-custom-mapdropdown" style="background-color: white; z-index: -1; padding-left: 2px; padding-right: 2px; border-bottom-left-radius: 2px; border-bottom-right-radius: 2px; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; position: absolute; top: 100%; left: 0px; right: 0px; text-align: left; display: none;">
                                <div id="gm-custom-mapoption-terrain" draggable="false" title="Show street map with terrain" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    Terrain
                                </div>
                                <div id="gm-custom-mapoption-natatl" draggable="false" title="Show natural atlas" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    Natural Atlas (US)
                                </div>
                                <div id="gm-custom-mapoption-outdoors" draggable="false" title="Show open topo map" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    OpenTopoMap
                                </div>
                                <div id="gm-custom-mapoption-topo" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    Topo Govt (as avail)
                                </div>
                                <div id="gm-custom-mapoption-satstreets" draggable="false" title="Show satellite imagery with street map" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    Satellite
                                </div>
                                <div id="gm-custom-mapoption-sat" draggable="false" title="Show satellite imagery" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    Satellite Topo
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="marker-tooltip" data-url="" data-index="" style="cursor: pointer; display: none; position:absolute; width: 200px; height: 150px; background: rgba(0, 0, 0, 0.3); margin: 15px;"></div>
                    <input type="hidden" name="gpx-file" id="gpx-file" value="">
                    <fieldset class="summitLogGpx">
                    <ul id="summitlog-gpx-files" class="vert add_photo">

                    <li class="gpx-file-1">
                        <div class="a clearfix">
                            <div id="gpx-file-1" class="imageContainer">
                                <noscript>
                                    <p>Please enable JavaScript to use file uploader.</p>
                                </noscript>
                            </div>
                        </div>
                    </li>

                    </ul>
                    </fieldset>
                </div>
            </div>

            <div class="row row-full-width summitlog-section">
                <div class="col-md-12">
                    <span class="field-title" style="display: block;">Route to summit</span>
                    <div class="field-title-spacer"></div>
                    <fieldset id="routes">
                        <div class="clearfix routeUp" style="width: 100%;">
                            <span class="a arrowUp onFocus" style="padding-bottom: 0px;">
                                <div id="route_up_container">
                                    <select name="route_up" id="route_up" style="cursor: pointer;">
                                        <option value="0" disabled selected="selected">select route you took up</option>
                                        {% for rt in all_routes_up %}
                                            <option value="{{ rt.key }}">{{ rt.name }} &bull; {{ rt.percentage }}% of climbs</option>
                                        {% endfor %}
                                        <optgroup label="===========================" disabled="disabled"></optgroup>
                                        <option value="add_new_route">Or add a new route...</option>
                                    </select>
                                </div>
                            </span>
                        </div>
                        <input type="text" name="txtNewSummitRoute" id="txtNewSummitRoute" style="padding-left: 10px; width: 100%; height: 55px; display: none;" placeholder="name of summit route...">
                        <div id="hide-new-summit-route-span" style="position: absolute; right: 30px; font-size: 20px; color: #cccccc; display: none;"><a id="hide-new-summit-route" style="color: #ccc;" class="ajax-link"><i class="fa fa-times"></i></a></div>
                    </fieldset>
                </div>
            </div>

            <div class="row row-full-width summitlog-section">
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 gpx-stat-1">
                    <span class="field-title" style="display: block;">Distance to summit</span>
                    <div class="field-title-spacer"></div>
                    <fieldset>
                        <div>
                            <input type="number" step="any" min="0" max="1000" name="distance-to-summit" id="distance-to-summit" style="width: 100px; padding-left: 10px;">
                            <div class="btn-group" data-toggle="buttons" style="margin-left: 15px;">
                                <label class="btn active">
                                <input type="radio" name="distance-to-summit-units" id="distance-to-summit-miles" value="mi" checked><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i> <span>  mi</span>
                                </label>
                                <label class="btn" style="margin-left: 5px;">
                                <input type="radio" name="distance-to-summit-units" id="distance-to-summit-km" value="km"><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i><span> km</span>
                                </label>
                            </div>
                        </div>
                    </fieldset>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 gpx-stat-4">
                    <span class="field-title" style="display: block;">Total trip distance</span>
                    <div class="field-title-spacer"></div>
                    <fieldset>
                        <div>
                            <input type="number" step="any" min="0" max="1000" name="total-trip-distance" id="total-trip-distance" style="width: 100px; padding-left: 10px;">
                            <div class="btn-group" data-toggle="buttons" style="margin-left: 15px;">
                                <label class="btn active">
                                <input type="radio" name="total-trip-distance-units" id="total-trip-distance-miles" value="mi" checked><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i> <span>  mi</span>
                                </label>
                                <label class="btn" style="margin-left: 5px;">
                                <input type="radio" name="total-trip-distance-units" id="total-trip-distance-km" value="km"><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i><span> km</span>
                                </label>
                            </div>
                        </div>
                    </fieldset>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 gpx-stat-2">
                    <span class="field-title" style="display: block;">Elevation start</span>
                    <div class="field-title-spacer"></div>
                    <fieldset>
                        <div>
                            <input type="number" step="any" min="-1000" max="30000" name="elevation-start" id="elevation-start" style="width: 100px; padding-left: 10px;">
                            <div class="btn-group" data-toggle="buttons" style="margin-left: 15px;">
                                <label class="btn active">
                                <input type="radio" name="elevation-start-units" id="elevation-start-ft" value="ft" checked><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i> <span>  ft</span>
                                </label>
                                <label class="btn" style="margin-left: 5px;">
                                <input type="radio" name="elevation-start-units" id="elevation-start-m" value="m"><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i><span> m</span>
                                </label>
                            </div>
                        </div>
                    </fieldset>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 gpx-stat-5">
                    <span class="field-title" style="display: block;">Elevation gain</span>
                    <div class="field-title-spacer"></div>
                    <fieldset>
                        <div>
                            <input type="number" step="any" min="0" max="100000" name="elevation-gain" id="elevation-gain" style="width: 100px; padding-left: 10px;">
                            <div class="btn-group" data-toggle="buttons" style="margin-left: 15px;">
                                <label class="btn active">
                                <input type="radio" name="elevation-gain-units" id="elevation-gain-ft" value="ft" checked><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i> <span>  ft</span>
                                </label>
                                <label class="btn" style="margin-left: 5px;">
                                <input type="radio" name="elevation-gain-units" id="elevation-gain-m" value="m"><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i><span> m</span>
                                </label>
                            </div>
                        </div>
                    </fieldset>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 gpx-stat-3">
                    <span class="field-title" style="display: block;">Time to summit</span>
                    <div class="field-title-spacer"></div>
                    <fieldset>
                        <div>
                            <input type="number" step="any" min="0" max="1000" name="time-to-summit-hrs" id="time-to-summit-hrs" style="width: 100px; padding-left: 10px;">
                            <span style="margin-left: 5px;">hrs</span>
                            <input type="number" step="any" min="0" max="59" name="time-to-summit-min" id="time-to-summit-min" style="width: 100px; padding-left: 10px; margin-left: 5px;">
                            <span style="margin-left: 5px;">min</span>
                        </div>
                    </fieldset>
                </div>
                <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12 gpx-stat-6">
                    <span class="field-title" style="display: block;">Total trip time</span>
                    <div class="field-title-spacer"></div>
                    <fieldset>
                        <div>
                            <input type="number" step="any" min="0" max="1000" name="total-trip-time-hrs" id="total-trip-time-hrs" style="width: 100px; padding-left: 10px;">
                            <span style="margin-left: 5px;">hrs</span>
                            <input type="number" step="any" min="0" max="59" name="total-trip-time-min" id="total-trip-time-min" style="width: 100px; padding-left: 10px; margin-left: 5px;">
                            <span style="margin-left: 5px;">min</span>
                        </div>
                    </fieldset>
                </div>
            </div>

            <div class="row row-full-width hidden-xs summitlog-section">
                <div class="col-md-12">
                    <fieldset>
                        <div id="summit-field-triptype">
                            <span class="field-title" style="display: block;">Trip type</span>
                            <div style="margin-left: 5px;">
                                <div class="btn-group" data-toggle="buttons">
                                    <label class="btn" style="margin-right: 15px;">
                                    <input type="radio" name="rdoTripType" id="rdoOutAndBack" value="out-and-back"><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i> <span>  out-and-back</span>
                                    </label>
                                    <label class="btn" style="margin-right: 15px;">
                                    <input type="radio" name="rdoTripType" id="rdoLoop" value="loop"><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i><span> loop</span>
                                    </label>
                                    <label class="btn">
                                    <input type="radio" name="rdoTripType" id="rdoPointToPoint" value="point-to-point/traverse"><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i><span> point-to-point/traverse</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                </div>
            </div>

            <div class="row row-full-width hidden-xs summitlog-section">
                <div class="col-md-12" style="padding-left: 0px; padding-right: 0px;">
                    <fieldset>
                        <div id="summit-field-trip-activities">
                            <span class="field-title" style="display: block; margin-left: 15px;">Trip activities <span class="hidden-xs header-help" style="margin-top: 15px; padding-bottom: 0px; color: #999;">select all that apply</span></span>
                            <div class="field-title-spacer"></div>
                            <div id="activity-tag-choices">
                                <div class="toggle-switch"><input class="activity-tag-input" type="checkbox" name="chkActivityTags" value="hiking"><label>hiking</label></div>
                                <div class="toggle-switch"><input class="activity-tag-input" type="checkbox" name="chkActivityTags" value="scrambling"><label>scrambling</label></div>
                                <div class="toggle-switch"><input class="activity-tag-input" type="checkbox" name="chkActivityTags" value="rock climbing"><label>rock climbing</label></div>
                                <div class="toggle-switch"><input class="activity-tag-input" type="checkbox" name="chkActivityTags" value="ice climbing"><label>ice climbing</label></div>
                                <div class="toggle-switch"><input class="activity-tag-input" type="checkbox" name="chkActivityTags" value="mountaineering"><label>mountaineering</label></div>
                                <div class="toggle-switch"><input class="activity-tag-input" type="checkbox" name="chkActivityTags" value="glacier climb"><label>glacier climb</label></div>
                                <div class="toggle-switch"><input class="activity-tag-input" type="checkbox" name="chkActivityTags" value="skiing"><label>skiing</label></div>
                                <div class="toggle-switch"><input class="activity-tag-input" type="checkbox" name="chkActivityTags" value="snowboarding"><label>snowboarding</label></div>
                                <div class="toggle-switch"><input class="activity-tag-input" type="checkbox" name="chkActivityTags" value="snowshoeing"><label>snowshoeing</label></div>
                                <div class="toggle-switch"><input class="activity-tag-input" type="checkbox" name="chkActivityTags" value="biking"><label>biking</label></div>
                                <div class="toggle-switch"><input class="activity-tag-input" type="checkbox" name="chkActivityTags" value="running"><label>running</label></div>
                                <div class="toggle-switch"><input class="activity-tag-input" type="checkbox" name="chkActivityTags" value="glissading"><label>glissading</label></div>
                            </div>
                            <div style="margin-top: 10px; margin-left: 15px; float: left; width: 100%;">
                                <input type="text" name="txtAddActivityTag" id="txtAddActivityTag" style="padding-left: 10px; line-height: 38px;" placeholder="other activity...">
                                <button type="button" id="btnAddActivityTag" class="btn btn-secondary" style="height: 56px; margin-top: -2px; margin-left: 2px;">Add</button>
                            </div>
                        </div>
                    </fieldset>
                </div>
            </div>

            <div class="row row-full-width hidden-xs summitlog-section">
                <div class="col-md-12" style="padding-left: 0px; padding-right: 0px;">
                    <fieldset>
                        <div id="summit-field-trip-challenges">
                            <span class="field-title" style="display: block; margin-left: 15px;">Obstacles <span class="hidden-xs header-help" style="margin-top: 15px; padding-bottom: 0px; color: #999;">select all that apply</span></span>
                            <div class="field-title-spacer"></div>
                            <div id="challenge-tag-choices">
                                <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkChallengeTags" value="road/access issues"><label>road/access issues</label></div>
                                <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkChallengeTags" value="routefinding"><label>routefinding</label></div>
                                <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkChallengeTags" value="stream crossing"><label>stream crossing</label></div>
                                <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkChallengeTags" value="bushwhacking"><label>bushwhacking</label></div>
                                <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkChallengeTags" value="blowdowns"><label>blowdowns</label></div>
                                <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkChallengeTags" value="no water source"><label>no water source</label></div>
                                <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkChallengeTags" value="rockfall/loose rock"><label>rockfall/loose rock</label></div>
                                <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkChallengeTags" value="snow on route"><label>snow on route</label></div>
                                <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkChallengeTags" value="avalanche danger"><label>avalanche danger</label></div>
                                <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkChallengeTags" value="crevasse danger"><label>crevasse danger</label></div>
                                <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkChallengeTags" value="buggy"><label>buggy</label></div>
                                <div class="toggle-switch"><input class="challenge-tag-input" type="checkbox" name="chkChallengeTags" value="weather"><label>weather</label></div>
                            </div>
                            <div style="margin-top: 10px; margin-left: 15px; float: left; width: 100%;">
                                <input type="text" name="txtAddChallengeTag" id="txtAddChallengeTag" style="padding-left: 10px; line-height: 38px;" placeholder="other obstacle...">
                                <button type="button" id="btnAddChallengeTag" class="btn btn-secondary" style="height: 56px; margin-top: -2px; margin-left: 2px;">Add</button>
                            </div>
                        </div>
                    </fieldset>
                </div>
            </div>

            <div class="row row-full-width hidden-xs summitlog-section">
                <div class="col-md-12" style="padding-left: 0px; padding-right: 0px;">
                    <fieldset>
                        <div id="summit-field-trip-gear">
                            <span class="field-title" style="display: block; margin-left: 15px;">Key gear <span class="hidden-xs header-help" style="margin-top: 15px; padding-bottom: 0px; color: #999;">gear used or recommended</span></span>
                            <div class="field-title-spacer"></div>
                            <div id="gear-tag-choices">
                                <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="ice axe"><label>ice axe</label></div>
                                <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="crampons"><label>crampons</label></div>
                                <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="helmet"><label>helmet</label></div>
                                <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="rope/harness"><label>rope/harness</label></div>
                                <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="climbing rack"><label>climbing rack</label></div>
                                <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="skis"><label>skis</label></div>
                                <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="snowboard"><label>snowboard</label></div>
                                <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="snowshoes"><label>snowshoes</label></div>
                                <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="bike"><label>bike</label></div>
                                <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="trekking poles"><label>trekking poles</label></div>
                                <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="mountaineering boots"><label>mountaineering boots</label></div>
                                <div class="toggle-switch"><input class="gear-tag-input" type="checkbox" name="chkGearTags" value="GPS device"><label>GPS device</label></div>
                            </div>
                            <div style="margin-top: 10px; margin-left: 15px; float: left; width: 100%;">
                                <input type="text" name="txtAddGearTag" id="txtAddGearTag" style="padding-left: 10px; line-height: 38px;" placeholder="other key gear...">
                                <button type="button" id="btnAddGearTag" class="btn btn-secondary" style="height: 56px; margin-top: -2px; margin-left: 2px;">Add</button>
                            </div>
                        </div>
                    </fieldset>
                </div>
            </div>

            <div class="row row-full-width summitlog-section">
                <div class="col-md-12">
                    <fieldset>
                        <div id="summit-field-companions">
                            <span class="field-title" style="display: block;">Companions</span>
                            <div class="field-title-spacer"></div>
                            <div id="summit-companions">
                            </div>
                            <div>
                                <input type="text" class="member-searchbox-input" name="summitmemq1" id="summitmemq1" style="width: 100%; max-width: 422px; padding-left: 10px; line-height: 38px;" placeholder="name or peakery member..." onkeyup="memberButtonUp();">
                                <button type="button" id="btnAddSummitCompanion" class="btn btn-secondary" style="width: 150px; height: 56px; margin-top: -2px;">Add</button>
                                <input value="" name="fellow_selected_users" id="fellow_selected_users" type="hidden">
                                <input value="" name="fellow_nonmember_baggers" id="fellow_nonmember_baggers" type="hidden">
                            </div>
                        </div>
                    </fieldset>
                </div>
                <div id="mobile-companions-spacer" class="hidden-sm hidden-md hidden-lg" style="height: 270px;"></div>
            </div>

            <div class="row row-full-width hidden-xs hidden-sm summitlog-section">
                <div class="col-md-12">
                    <fieldset>
                        <div id="summit-field-video-link">
                            <span class="field-title" style="display: block;">Your videos</span>
                            <div class="field-title-spacer"></div>
                            <div id="summit-videos">
                            </div>
                            <div>
                                <input type="text" name="summit-video-link" id="summit-video-link" style="width: 50%; padding-left: 10px; line-height: 38px;" placeholder="YouTube or Vimeo link...">
                                <button type="button" id="btnAddVideoLink" class="btn btn-secondary" style="width: 150px; height: 56px; margin-top: -2px; margin-left: 10px;">Add</button>
                                <input value="" name="summit_video_links" id="summit_video_links" type="hidden">
                            </div>
                        </div>
                    </fieldset>
                </div>
            </div>

            <div class="row row-full-width hidden-xs hidden-sm summitlog-section">
                <div class="col-md-12">
                    <fieldset>
                        <div id="summit-field-related-link">
                            <span class="field-title" style="display: block;">Related links</span>
                            <div class="field-title-spacer"></div>
                            <div id="summit-related-links">
                            </div>
                            <div>
                                <input type="text" name="summit-related-link" id="summit-related-link" style="width: 50%; padding-left: 10px; line-height: 38px;" placeholder="link to your blog post, photo set, etc...">
                                <button type="button" id="btnAddRelatedLink" class="btn btn-secondary" style="width: 150px; height: 56px; margin-top: -2px; margin-left: 10px;">Add</button>
                                <input value="" name="summit_related_links" id="summit_related_links" type="hidden">
                            </div>
                        </div>
                    </fieldset>
                </div>
            </div>

        </div>

    </div>

    <div class="message-modal modal fade" id="message-modal" tabindex="-1" role="dialog" aria-labelledby="message-modal-label">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                  <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                  <h4 class="modal-title" id="message-modal-label"></h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div id="message-modal-body" class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="margin-bottom: 5px; font-size: 16px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="confirm-modal modal fade" id="confirm-modal" tabindex="-1" role="dialog" aria-labelledby="confirm-modal-label">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                  <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                  <h4 class="modal-title" id="confirm-modal-label"></h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div id="confirm-modal-body" class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="margin-bottom: 5px; font-size: 16px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="savetext-modal modal fade" id="savetext-modal" tabindex="-1" role="dialog" aria-labelledby="savetext-modal-label">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                  <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                  <h4 class="modal-title" id="savetext-modal-label"></h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div id="savetext-modal-body" class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="margin-bottom: 5px; font-size: 16px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

<style type="text/css">
    .ui-autocomplete {
        max-height: 100px;
        overflow-y: auto;
        /* prevent horizontal scrollbar */
        overflow-x: hidden;
        /* add padding to account for vertical scrollbar */
        padding-right: 20px;
    }
        /* IE 6 doesn't support max-height
       * we use height instead, but this forces the menu to always be this tall
       */
    * html .ui-autocomplete {
        height: 100px;
    }
</style>

<script type="text/javascript">

var uploaderIdle = true;
var formSubmitted = false;
var points = [];
var linePoints = [];
var lineData;
var map = null;
var poly = null;
var bounds = null;
var mapDiv = document.getElementById('map-canvas');
var iconstyle;

function isFutureDate(text){
    var arrDate = text.split("-");
    var date = new Date(arrDate[0], arrDate[1]-1, arrDate[2]);
    var _now=new Date();
    if(date.getTime()>_now.getTime()){
        $("span.invalid-date").show();
        return true;
    }
    $("span.invalid-date").hide();
    return false;
}

function loadSummitCompanion(data, value) {
    var companiondata = data.split("|");
    var username = companiondata[0];
    var avatar_url = companiondata[1];
    var user_id = companiondata[2];
    if (user_id != '0') {
        $("#summit-companions").append('<div class="summit-companion" id="companion-' + user_id + '" style="background-color: #f0f0f0; display: inline-block; height: 100px; margin-right: 20px; margin-bottom: 20px;"><p><span style="display: inline;"><img src="{{ MEDIA_URL }}' + avatar_url + '" style="width: 100px; height: 100px;"></span><span style="display: inline; margin-left: 20px; font-weight: 500;">' + username + '</span><span class="pull-right" style="padding-top: 35px; padding-right: 20px; font-size: 30px; color: #cccccc;"><a style="color: #ccc;" class="ajax-link" onclick="removeCompanion(\'' + user_id + '\');"><i class="fa fa-times"></i></a></span></p></div>');
        var current_selected_users = $("#fellow_selected_users").val();
        $("#fellow_selected_users").val(current_selected_users+'|'+user_id+'|,');
    } else {
        var user_slug = slugify(username);
        if ($('#companion-' + user_slug).length == 0) {
            $("#summit-companions").append('<div class="summit-companion" id="companion-' + user_slug + '" style="background-color: #f0f0f0; display: inline-block; height: 100px; margin-right: 20px; margin-bottom: 20px;"><p><span style="display: inline;"><img src="{{ MEDIA_URL }}' + avatar_url + '" style="width: 100px; height: 100px;"></span><span style="display: inline; margin-left: 20px; font-weight: 500;">' + username + '</span><span class="pull-right" style="padding-top: 35px; padding-right: 20px; font-size: 30px; color: #cccccc;"><a style="color: #ccc;" class="ajax-link" onclick="removeCompanion(\'' + user_slug + '\');"><i class="fa fa-times"></i></a></span></p></div>');
            var current_nonmember_baggers = $("#fellow_nonmember_baggers").val();
            $("#fellow_nonmember_baggers").val(current_nonmember_baggers + '|' + username + '|,');
        }
    }
    $("#summitmemq1").val('');
    $("#summitmemq1").focus();
}

function removeCompanion(user_id) {
    if (!isNaN(user_id)) {
        var companion_div = '#companion-' + user_id;
        $(companion_div).remove();
        var current_selected_users = $("#fellow_selected_users").val();
        var selected_users = current_selected_users.replace('|' + user_id + '|,', '');
        $("#fellow_selected_users").val(selected_users);
    } else {
        var companion_div = '#companion-' + user_id;
        $(companion_div).remove();
        var current_nonmember_baggers = $("#fellow_nonmember_baggers").val();
        var nonmember_baggers = current_nonmember_baggers.replace('|' + user_id + '|,', '');
        $("#fellow_nonmember_baggers").val(nonmember_baggers);
    }
}

function slugify(str) {
    str = str.replace(/^\s+|\s+$/g, ''); // trim
    str = str.toLowerCase();

    // remove accents, swap ñ for n, etc
    var from = "àáäâèéëêìíïîòóöôùúüûñç·/_,:;";
    var to   = "aaaaeeeeiiiioooouuuunc------";
    for (var i=0, l=from.length ; i<l ; i++) {
    str = str.replace(new RegExp(from.charAt(i), 'g'), to.charAt(i));
    }

    str = str.replace(/[^a-z0-9 -]/g, '') // remove invalid chars
    .replace(/\s+/g, '-') // collapse whitespace and replace by -
    .replace(/-+/g, '-'); // collapse dashes

    return str;
}

function processURL(url){
    var numVideos = $(".related-videos").length;
    var id;
    var video_url;

    if (url.indexOf('youtube.com') > -1) {
        <!-- CHANGED -->
        id = url.split('v=')[1].split('&')[0];
        video_thumb_url = 'http://i2.ytimg.com/vi/' + id + '/hqdefault.jpg';
        $("#summit-videos").append('<div class="related-videos" id="video-' + numVideos + '" style="width: 98%; background-color: #f0f0f0; display: inline-block; height: 100px; margin-right: 20px; margin-bottom: 20px;"><p><span style="display: inline;"><img src="' + video_thumb_url + '" style="width: 150px; height: 100px;"></span><span id="video-url-' + numVideos + '" style="display: inline; margin-left: 20px; font-weight: 500;">' + url + '</span><span class="pull-right" style="padding-top: 35px; padding-right: 20px; font-size: 30px; color: #cccccc;"><a style="color: #ccc;" class="ajax-link" onclick="removeVideo(\'' + numVideos + '\');"><i class="fa fa-times"></i></a></span></p></div>');
        var current_videos = $("#summit_video_links").val();
        $("#summit_video_links").val(current_videos + '|' + url + '|,');
        $("#summit-video-link").val('');
    } else if (url.indexOf('youtu.be') > -1) {
        id = url.split('/')[3];
        video_thumb_url = 'http://i2.ytimg.com/vi/' + id + '/hqdefault.jpg';
        $("#summit-videos").append('<div class="related-videos" id="video-' + numVideos + '" style="width: 98%; background-color: #f0f0f0; display: inline-block; height: 100px; margin-right: 20px; margin-bottom: 20px;"><p><span style="display: inline;"><img src="' + video_thumb_url + '" style="width: 150px; height: 100px;"></span><span id="video-url-' + numVideos + '" style="display: inline; margin-left: 20px; font-weight: 500;">' + url + '</span><span class="pull-right" style="padding-top: 35px; padding-right: 20px; font-size: 30px; color: #cccccc;"><a style="color: #ccc;" class="ajax-link" onclick="removeVideo(\'' + numVideos + '\');"><i class="fa fa-times"></i></a></span></p></div>');
        var current_videos = $("#summit_video_links").val();
        $("#summit_video_links").val(current_videos + '|' + url + '|,');
        $("#summit-video-link").val('');
    } else if (url.indexOf('vimeo') > -1) {
        id = url.substr(url.lastIndexOf('/') + 1);
        $.getJSON('https://www.vimeo.com/api/v2/video/' + id + '.json?callback=?', {format: "json"}, function(data) {
            video_thumb_url = data[0].thumbnail_large;
            $("#summit-videos").append('<div class="related-videos" id="video-' + numVideos + '" style="width: 98%; background-color: #f0f0f0; display: inline-block; height: 100px; margin-right: 20px; margin-bottom: 20px;"><p><span style="display: inline;"><img src="' + video_thumb_url + '" style="width: 150px; height: 100px;"></span><span id="video-url-' + numVideos + '" style="display: inline; margin-left: 20px; font-weight: 500;">' + url + '</span><span class="pull-right" style="padding-top: 35px; padding-right: 20px; font-size: 30px; color: #cccccc;"><a style="color: #ccc;" class="ajax-link" onclick="removeVideo(\'' + numVideos + '\');"><i class="fa fa-times"></i></a></span></p></div>');
            var current_videos = $("#summit_video_links").val();
            $("#summit_video_links").val(current_videos + '|' + url + '|,');
            $("#summit-video-link").val('');
        });
    } else {
        $('#message-modal-label').html('Invalid URL');
        $('#message-modal-body').html('<div style="margin-top: 20px; text-align: left;"><p>Only YouTube or Vimeo supported</p><p style="text-align: center;"><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">OK</button></p></div>');
        $('#message-modal').modal('show');
    }
}

function removeVideo(video_id) {
    var video_div = '#video-'+video_id;
    var video_url_div = '#video-url-'+video_id;
    var video_url = $(video_url_div).html();
    $(video_div).remove();
    var current_videos = $("#summit_video_links").val();
    var selected_videos = current_videos.replace('|'+video_url+'|,', '');
    $("#summit_video_links").val(selected_videos);
}

function removeRelatedLink(link_id) {
    var link_div = '#related-link-'+link_id;
    var link_url_div = '#related-link-url-'+link_id;
    var link_url = $(link_url_div).html();
    $(link_div).remove();
    var current_links = $("#summit_related_links").val();
    var selected_links = current_links.replace('|'+link_url+'|,', '');
    $("#summit_related_links").val(selected_links);
}

function removeGPX() {
    $("#remove-gpx-file").html('Removing GPX file...');
    points = [];
    linePoints = [];
    map = null;
    bounds = null;
    $("#gpx-file-1").show();
    $(".summitLogGpx").show();
    $("#map-canvas").hide();
    $("#remove-gpx-file").hide();
    $("#remove-gpx-file").html('Remove GPX file');
    $("#gpx-file").val('');
    if (uploaderIdle) {
        $('#add_peak2').prop('disabled', false);
    }
}

function validateForm() {
    //form validation
    //validate any URLs
    var validVideoLink = true;
    var validRelatedLink = true;
    var isYoutubeOrVimeo = true;
    if ($('#summit-video-link').val() != '') {
        var videoUrl = $('#summit-video-link').val();
        var validVideoLink = isUrlValid(videoUrl);
        var isYoutubeOrVimeo = false;
        if (videoUrl.indexOf('youtube.com') > -1 || videoUrl.indexOf('youtu.be') > -1 || videoUrl.indexOf('vimeo') > -1) {
            isYoutubeOrVimeo = true;
        }
    }
    if ($('#summit-related-link').val() != '') {
        var validRelatedLink = isUrlValid($('#summit-related-link').val());
    }
    if (validVideoLink && validRelatedLink && isYoutubeOrVimeo) {
        //any text left un-added in input fields?
        if ($('#txtAddActivityTag').val() != '' || $('#txtAddChallengeTag').val() != '' || $('#txtAddGearTag').val() != '' || $('#summitmemq1').val() != '' || $('#summit-video-link').val() != '' || $('#summit-related-link').val() != '') {
            var unsavedText = '';
            if ($('#txtAddActivityTag').val() != '') {
                unsavedText = unsavedText + '<p>Trip activity: <b>' + $('#txtAddActivityTag').val() + '</b></p>';
            }
            if ($('#txtAddChallengeTag').val() != '') {
                unsavedText = unsavedText + '<p>Obstacle: <b>' + $('#txtAddChallengeTag').val() + '</b></p>';
            }
            if ($('#txtAddGearTag').val() != '') {
                unsavedText = unsavedText + '<p>Key gear: <b>' + $('#txtAddGearTag').val() + '</b></p>';
            }
            if ($('#summitmemq1').val() != '') {
                unsavedText = unsavedText + '<p>Companion: <b>' + $('#summitmemq1').val() + '</b></p>';
            }
            if ($('#summit-video-link').val() != '') {
                unsavedText = unsavedText + '<p>Video: <b>' + $('#summit-video-link').val() + '</b></p>';
            }
            if ($('#summit-related-link').val() != '') {
                unsavedText = unsavedText + '<p>Related link: <b>' + $('#summit-related-link').val() + '</b></p>';
            }
            $('#savetext-modal-label').html('Save your changes?');
            $('#savetext-modal-body').html('<div style="margin-top: 20px; text-align: left;"><p>You entered the following info but didn’t click the "Add" button next to it. Save this too?</p><p>' + unsavedText + '</p><p style="text-align: center;"><a onclick="saveUnaddedText();" class="btn btn-primary" style="width: 100px;">Save</a><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">Cancel</button></p></div>');
            $('#savetext-modal').modal('show');
        } else {
            formSubmitted = true;
            $('#add_peak2').html('<i class="fa fa-spinner fa-spin"></i>');
            if (uploaderIdle) {
                saveLog();
            }
        }
    } else if (!isYoutubeOrVimeo) {
        var urlErrors = '<p>Your videos: <b>' + $('#summit-video-link').val() + '</b></p>';
        $('#message-modal-label').html('Invalid URL');
        $('#message-modal-body').html('<div style="margin-top: 20px; text-align: left;"><p>Only YouTube or Vimeo supported</p><p>' + urlErrors + '</p><p style="text-align: center;"><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">OK</button></p></div>');
        $('#message-modal').modal('show');
    } else {
        var urlErrors = '';
        if (!validVideoLink) {
            urlErrors = urlErrors + '<p>Your videos: <b>' + $('#summit-video-link').val() + '</b></p>';
        }
        if (!validRelatedLink) {
            urlErrors = urlErrors + '<p>Related links: <b>' + $('#summit-related-link').val() + '</b></p>';
        }
        $('#message-modal-label').html('Invalid URL');
        $('#message-modal-body').html('<div style="margin-top: 20px; text-align: left;"><p>You entered the following invalid URL(s)</p><p>' + urlErrors + '</p><p style="text-align: center;"><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">OK</button></p></div>');
        $('#message-modal').modal('show');
    }
}

function isUrlValid(url) {
    return /^(https?|s?ftp):\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i.test(url);
}

function saveLog() {
    //submit form
    $('#summitlog_form').submit();
}

$(document).ready(function(){

    //pre-select meters?
    {% if not peak.is_usa %}
    $('#elevation-start-m').click();
    $('#elevation-gain-m').click();
    $('#distance-to-summit-km').click();
    $('#total-trip-distance-km').click();
    setElevationStartMeters();
    setElevationGainMeters();
    setDistanceToSummitKm();
    setTotalTripDistanceKm();
    {% endif %}

    $('#remove-gpx-file').hide();

    var maxYear = new Date().getFullYear();
    var minYear = 1950;
    var currMonth = new Date().getMonth();

    for (var i = minYear; i<=maxYear; i++){
        $('.ui-datepicker-year').append('<option value="'+i+'">'+i+'</option>');
    }
    $('.ui-datepicker-year').val(maxYear);
    $('.ui-datepicker-month').val(currMonth);

    //when click the submit button
    $('#add_peak2').on('click', function(e) {
        e.preventDefault();
        //validate form
        validateForm();
    });

    $('.toggle-switch').on('mouseenter', 'input', function() {
        var currentBackground = $(this).siblings('label').css('background-color');
        if (currentBackground == 'rgb(234, 234, 234)') {
            $(this).siblings('label').css('background-color','#ccc');
        } else if (currentBackground == 'rgb(238, 238, 238)') {
            $(this).siblings('label').css('background-color','#ccc');
        } else if (currentBackground == 'rgb(230, 230, 230)') {
            $(this).siblings('label').css('background-color','#ccc');
        } else {
            $(this).siblings('label').css('background-color','#ff4400');
        }
    });

    $('.toggle-switch').on('mouseleave', 'input', function() {
        var currentBackground = $(this).siblings('label').css('background-color');
        if (currentBackground == 'rgb(204, 204, 204)') {
            $(this).siblings('label').css('background-color','rgb(234, 234, 234)');
        } else if (currentBackground == 'rgb(238, 238, 238)') {
            $(this).siblings('label').css('background-color','rgb(234, 234, 234)');
        } else if (currentBackground == 'rgb(230, 230, 230)') {
            $(this).siblings('label').css('background-color','rgb(234, 234, 234)');
        } else {
            $(this).siblings('label').css('background-color','#f24100');
        }
    });

    $('#summitmemq1').on('keydown', function (e) {
        if(e.which === 13){
            var companiondata = $('#summitmemq1').val() + '|img/default-user.png|0';
            loadSummitCompanion(companiondata, '');
            if (uploaderIdle) {
                $('#add_peak2').prop('disabled', false);
            }
        }
    });

    $("#btnAddSummitCompanion").click( function() {
        if ($("#summitmemq1").val() != '') {
            var companiondata = $('#summitmemq1').val() + '|img/default-user.png|0';
            loadSummitCompanion(companiondata, '');
            if (uploaderIdle) {
                $('#add_peak2').prop('disabled', false);
            }
        }
        return false;
    });

    //check form input changes to enable save button
    $('#summit-date').on('change', function() {
        if (uploaderIdle) {
            $('#add_peak2').prop('disabled', false);
        }
    });
    $('#rdoSuccess').on('change', function() {
        if (uploaderIdle) {
            $('#add_peak2').prop('disabled', false);
        }
    });
    $('#rdoAttempt').on('change', function() {
        if (uploaderIdle) {
            $('#add_peak2').prop('disabled', false);
        }
    });
    $('#summit').on('input', function() {
        if (uploaderIdle) {
            $('#add_peak2').prop('disabled', false);
        }
    });
    $('#distance-to-summit').on('input', function() {
        if (uploaderIdle) {
            $('#add_peak2').prop('disabled', false);
        }
    });
    $('#elevation-start').on('input', function() {
        if (uploaderIdle) {
            $('#add_peak2').prop('disabled', false);
        }
    });
    $('#time-to-summit-hrs').on('input', function() {
        if (uploaderIdle) {
            $('#add_peak2').prop('disabled', false);
        }
    });
    $('#time-to-summit-min').on('input', function() {
        if (uploaderIdle) {
            $('#add_peak2').prop('disabled', false);
        }
    });
    $('#total-trip-distance').on('input', function() {
        if (uploaderIdle) {
            $('#add_peak2').prop('disabled', false);
        }
    });
    $('#elevation-gain').on('input', function() {
        if (uploaderIdle) {
            $('#add_peak2').prop('disabled', false);
        }
    });
    $('#total-trip-time-hrs').on('input', function() {
        if (uploaderIdle) {
            $('#add_peak2').prop('disabled', false);
        }
    });
    $('#total-trip-time-min').on('input', function() {
        if (uploaderIdle) {
            $('#add_peak2').prop('disabled', false);
        }
    });
    $('#rdoOutAndBack').on('input', function() {
        if (uploaderIdle) {
            $('#add_peak2').prop('disabled', false);
        }
    });
    $('#rdoLoop').on('input', function() {
        if (uploaderIdle) {
            $('#add_peak2').prop('disabled', false);
        }
    });
    $('#rdoPointToPoint').on('input', function() {
        if (uploaderIdle) {
            $('#add_peak2').prop('disabled', false);
        }
    });
    $('#summitmemq1').on('input', function() {
        if (uploaderIdle) {
            $('#add_peak2').prop('disabled', false);
        }
    });

    $(document).on("keydown", ":input:not(textarea)", function(event) {
        if (event.keyCode == 13) {
            return false;
        } else {
            return event.keyCode;
        }
    });

    $('#remove-gpx-file').on('click', function() {
        removeGPX();
    });

    $("#btnAddActivityTag").click( function() {
        var new_activity = $("#txtAddActivityTag").val();
        if (new_activity != '') {
            var numActivities = $(".user-added-activity").length;
            var activityId = '#user-activity-' + numActivities;
            $("#activity-tag-choices").append('<div class="toggle-switch"><input id="user-activity-'+numActivities+'" class="activity-tag-input user-added-activity" type="checkbox" name="chkActivityTags" value="'+new_activity+'"><label>'+new_activity+'</label></div>');
            $("#user-activity-"+numActivities).prop('checked', true);
            $("#user-activity-"+numActivities).next().animate({
                backgroundColor: '#f24100',
            },200);
            $("#user-activity-"+numActivities).next().css('color','#fff');
            $("#txtAddActivityTag").val('');
            if (uploaderIdle) {
                $('#add_peak2').prop('disabled', false);
            }
        }
        return false;
    });

    $('#activity-tag-choices').on('change', '.activity-tag-input', function () {
        if (this.checked) {
            $(this).next().animate({
                backgroundColor: '#f24100',
            },200);
            $(this).next().css('color','#fff');
        } else {
            $(this).next().animate({
                backgroundColor: '#e6e6e6',
            },200);
            $(this).next().css('color','#000');
        }
        if (uploaderIdle) {
            $('#add_peak2').prop('disabled', false);
        }
    });

    $("#btnAddChallengeTag").click( function() {
        var new_challenge = $("#txtAddChallengeTag").val();
        if (new_challenge != '') {
            var numChallenges = $(".user-added-challenge").length;
            var challengeId = '#user-challenge-' + numChallenges;
            $("#challenge-tag-choices").append('<div class="toggle-switch"><input id="user-challenge-' + numChallenges + '" class="challenge-tag-input user-added-challenge" type="checkbox" name="chkChallengeTags" value="' + new_challenge + '"><label>' + new_challenge + '</label></div>');
            $("#user-challenge-" + numChallenges).prop('checked', true);
            $("#user-challenge-" + numChallenges).next().animate({
                backgroundColor: '#f24100',
            }, 200);
            $("#user-challenge-" + numChallenges).next().css('color', '#fff');
            $("#txtAddChallengeTag").val('');
            if (uploaderIdle) {
                $('#add_peak2').prop('disabled', false);
            }
        }
        return false;
    });

    $('#challenge-tag-choices').on('change', '.challenge-tag-input', function () {
        if (this.checked) {
            $(this).next().animate({
                backgroundColor: '#f24100',
            },200);
            $(this).next().css('color','#fff');
        } else {
            $(this).next().animate({
                backgroundColor: '#e6e6e6',
            },200);
            $(this).next().css('color','#000');
        }
        if (uploaderIdle) {
            $('#add_peak2').prop('disabled', false);
        }
    });

    $("#btnAddGearTag").click( function() {
        var new_gear = $("#txtAddGearTag").val();
        if (new_gear != '') {
            var numGear = $(".user-added-gear").length;
            var gearId = '#user-gear-' + numGear;
            $("#gear-tag-choices").append('<div class="toggle-switch"><input id="user-gear-' + numGear + '" class="gear-tag-input user-added-gear" type="checkbox" name="chkGearTags" value="' + new_gear + '"><label>' + new_gear + '</label></div>');
            $("#user-gear-" + numGear).prop('checked', true);
            $("#user-gear-" + numGear).next().animate({
                backgroundColor: '#f24100',
            }, 200);
            $("#user-gear-" + numGear).next().css('color', '#fff');
            $("#txtAddGearTag").val('');
            if (uploaderIdle) {
                $('#add_peak2').prop('disabled', false);
            }
        }
        return false;
    });

    $('#gear-tag-choices').on('change', '.gear-tag-input', function () {
        if (this.checked) {
            $(this).next().animate({
                backgroundColor: '#f24100',
            },200);
            $(this).next().css('color','#fff');
        } else {
            $(this).next().animate({
                backgroundColor: '#e6e6e6',
            },200);
            $(this).next().css('color','#000');
        }
        if (uploaderIdle) {
            $('#add_peak2').prop('disabled', false);
        }
    });

    $("#btnAddVideoLink").click( function() {
        if ($("#summit-video-link").val() != '') {
            var validVideoLink = isUrlValid($('#summit-video-link').val());
            if (validVideoLink) {
                var video_url = $("#summit-video-link").val();
                processURL(video_url);
                if (uploaderIdle) {
                    $('#add_peak2').prop('disabled', false);
                }
            } else {
                var urlErrors = '<p>Your videos: <b>' + $('#summit-video-link').val() + '</b></p>';
                $('#message-modal-label').html('Invalid URL');
                $('#message-modal-body').html('<div style="margin-top: 20px; text-align: left;"><p>You entered the following invalid URL</p><p>' + urlErrors + '</p><p style="text-align: center;"><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">OK</button></p></div>');
                $('#message-modal').modal('show');
            }
        }
        return false;
    });

    $("#btnAddRelatedLink").click( function() {
        if ($("#summit-related-link").val() != '') {
            var validRelatedLink = isUrlValid($('#summit-related-link').val());
            if (validRelatedLink) {
                var link_url = $("#summit-related-link").val();
                var numLinks = $(".related-links").length;
                $("#summit-related-links").append('<div class="related-links" id="related-link-' + numLinks + '" style="width: 98%; background-color: #f0f0f0; display: inline-block; height: 100px; margin-right: 20px; margin-bottom: 20px;"><p><span id="related-link-url-' + numLinks + '" style="display: inline-block; margin-left: 20px; font-weight: 500; line-height: 110px;">' + link_url + '</span><span class="pull-right" style="padding-top: 35px; padding-right: 20px; font-size: 30px; color: #cccccc;"><a style="color: #ccc;" class="ajax-link" onclick="removeRelatedLink(\'' + numLinks + '\');"><i class="fa fa-times"></i></a></span></p></div>');
                var current_links = $("#summit_related_links").val();
                $("#summit_related_links").val(current_links + '|' + link_url + '|,');
                $("#summit-related-link").val('');
                if (uploaderIdle) {
                    $('#add_peak2').prop('disabled', false);
                }
            } else {
                var urlErrors = '<p>Related links: <b>' + $('#summit-related-link').val() + '</b></p>';
                $('#message-modal-label').html('Invalid URL');
                $('#message-modal-body').html('<div style="margin-top: 20px; text-align: left;"><p>You entered the following invalid URL</p><p>' + urlErrors + '</p><p style="text-align: center;"><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">OK</button></p></div>');
                $('#message-modal').modal('show');
            }
        }
        return false;
    });

    /*
     * Step 2 javascripts
     */
    $(document).bind('close.facebox', function() {location.reload(true) });
    $('#step2').facebox();
    $('input[title!=""]').hint();
    //$('a#skip2').click(function(){ $("a#step2").trigger('click') });
    $("#date").datepicker({
        dateFormat: 'yy-mm-dd',
        dayNamesMin: [ "S", "M", "T", "W", "T", "F", "S" ],
        changeMonth: false,
        changeYear: false,
        yearRange: "1950:{% now "Y" %}",
        onSelect: function() {
            var date = new Date($(this).val().substring(0,4), $(this).val().substring(5,7)-1, $(this).val().substring(8,10));
            var dd = date.getDate();
            var mm = date.getMonth();
            var yyyy = date.getFullYear();
            //make sure date not in the future
            var selectedDate = new Date(yyyy, mm, dd);
            var today = new Date();
            if (selectedDate > today) {
                var tempDate = new Date($('#summit-date').val().substring(0,4), $('#summit-date').val().substring(5,7)-1, $('#summit-date').val().substring(8,10));
                dd = tempDate.getDate();
                mm = tempDate.getMonth();
                yyyy = tempDate.getFullYear();
                var originalDate = new Date(yyyy, mm, dd);
                $("#date").datepicker("setDate", originalDate);
                $('.ui-datepicker-month').val(mm);
                $('.ui-datepicker-year').val(yyyy);
            } else {
                if (dd < 10) {
                    dd = '0' + dd;
                }
                mm = mm + 1;
                if (mm < 10) {
                    mm = '0' + mm;
                }
                var date = yyyy + '-' + mm + '-' + dd;
                $('#summit-date').val(date).trigger('change');
            }
        }
    });
    $("#date").datepicker("setDate", new Date());

    var today = new Date();
    var dd = today.getDate();
    var mm = today.getMonth()+1;
    var yyyy = today.getFullYear();
    if (dd < 10) {
        dd = '0' + dd;
    }
    if (mm < 10) {
        mm = '0' + mm;
    }
    var today = yyyy + '-' + mm + '-' + dd;
    $('#summit-date').val(today);
    $("textarea").click(function(){ reset_textareas($(this)) });

    $('.ui-datepicker-month').on('change', function () {
        var newMonth = parseInt($('.ui-datepicker-month').val()) + 1;
        var newYear = $('.ui-datepicker-year').val();
        $("#date").datepicker( "setDate", newYear+'-'+newMonth+'-01' );
        $('.ui-state-active').removeClass('ui-state-active');
        $(this).blur();
    });

    $('.ui-datepicker-year').on('change', function () {
        var newMonth = parseInt($('.ui-datepicker-month').val()) + 1;
        var newYear = $('.ui-datepicker-year').val();
        $("#date").datepicker( "setDate", newYear+'-'+newMonth+'-01' );
        $('.ui-state-active').removeClass('ui-state-active');
        $(this).blur();
    });

    var loading = "<div style='width:108px;'>Uploading...</div>";

    var allowedExtensions = ['jpg', 'jpeg'];
    var fileNum = 1;
    var uploader = new qq.s3.FineUploader({
        debug: false,
        element: document.getElementById('uploader'),
        template: 'qq-image-template',
        request: {
            endpoint: 'https://peakery-media.s3.amazonaws.com',
            accessKey: 'AKIAJPWBM4YZXFHWBTPQ'
        },
        signature: {
            endpoint: '{% url "s3signature" %}'
        },
        uploadSuccess: {
            endpoint: '{% url "s3_summit_photo_upload" %}',
            params: {
                'summit_id': '{{ summit.id }}',
                'photo_index': function() {
                    return fileNum++;
                }
            }
        },
        iframeSupport: {
            localBlankPagePath: '/api/s3blank/'
        },
        retry: {
           enableAuto: false // defaults to false
        },
        validation: {
            acceptFiles: ['image/jpg, image/jpeg, .jpg, .jpeg'],
            allowedExtensions: allowedExtensions,
            sizeLimit: 10000000,
            image: {
                minHeight: 1000,
                minWidth: 1000
            }
        },
        messages: {
            typeError: 'Sorry, must be a JPG file.',
            sizeError: 'Sorry, this file is too big. Must be under 10 MB.',
            minHeightImageError: 'Sorry, this photo is too small. Must be at least 1000 x 1000 pixels.',
            minWidthImageError: 'Sorry, this photo is too small. Must be at least 1000 x 1000 pixels.'
        },
        showMessage: function (message) {
            $('#message-modal-label').html('Error');
            $('#message-modal-body').html(message);
            $('#message-modal').modal('show');
        },
        text: {
            fileInputTitle: 'Choose file(s)'
        },
        callbacks: {
            onSubmit: function(id, name) {},
            onSubmitted: function(id, name) {
                uploaderIdle = false;
                $('#add_peak2').prop('disabled', false);
                $('.qq-upload-list > li').each(function () {
                    var photoId = $(this).attr('qq-file-id');
                    photoId++;
                    $(this).find('.remove-photo').attr('id','file-' + photoId + '-remove-photo');
                    $(this).find('.remove-photo').on('click', function() {
                        confirmRemovePhoto('file-' + photoId);
                    });
                    $(this).find('.addACaption').attr('id','file-' + photoId + '-caption');
                    $(this).find('.hiddenPhoto').attr('id','file-' + photoId + '-photo');
                });
            },
            onComplete: function(id, name, responseJSON, maybeXhr) {
                var fileId = id + 1;
                $('#file-' + fileId + '-caption').attr('name', 'caption'+responseJSON.photo_id);
                $('#file-' + fileId + '-photo').attr('name', 'photo'+responseJSON.photo_id);
                $('#file-' + fileId + '-photo').val(responseJSON.photo_id);
            },
            onAllComplete: function(successful, failed) {
                uploaderIdle = true;
                if (formSubmitted) {
                    saveLog();
                }
            },
            onCancel: function(id, name) {},
            onUpload: function(id, name) {},
            onUploadChunk: function(id, name, chunkData) {},
            onUploadChunkSuccess: function(id, chunkData, responseJSON, xhr) {},
            onResume: function(id, fileName, chunkData) {},
            onProgress: function(id, name, loaded, total) {},
            onTotalProgress: function(loaded, total) {},
            onError: function(id, name, reason, maybeXhrOrXdr) {
                $('#message-modal-label').html('Error');
                $('#message-modal-body').html('Error uploading ' + name + '. Hit the Retry button on the photo to try again.');
                $('#message-modal').modal('show');
            },
            onAutoRetry: function(id, name, attemptNumber) {},
            onManualRetry: function(id, name) {},
            onValidateBatch: function(fileOrBlobData) {},
            onValidate: function(fileOrBlobData) {},
            onSubmitDelete: function(id) {},
            onDelete: function(id) {},
            onDeleteComplete: function(id, xhrOrXdr, isError) {},
            onPasteReceived: function(blob) {},
            onStatusChange: function(id, oldStatus, newStatus) {},
            onSessionRequestComplete: function(response, success, xhrOrXdr) {}
        },
        objectProperties: {
            acl: 'public-read',
            key: function (fileId) {

                var filename = uploader.getName(fileId);
                var uuid = uploader.getUuid(fileId);
                var ext = filename.substr(filename.lastIndexOf('.') + 1).toLowerCase();

                return  'items/users/{{ peak.slug_new_text }}_' + uuid + '.' + ext;

            }
        }
    });

    var gpxuploader = new qq.s3.FineUploader({
        debug: false,
        multiple: false,
        element: document.getElementById('gpx-file-1'),
        template: 'qq-gpx-template',
        request: {
            endpoint: 'https://peakery-media.s3.amazonaws.com',
            accessKey: 'AKIAJPWBM4YZXFHWBTPQ'
        },
        signature: {
            endpoint: '{% url "s3signature" %}'
        },
        uploadSuccess: {
            endpoint: '{% url "s3_summit_gpx_upload" %}',
            params: {
                'summit_id': '{{ summit.id }}'
            }
        },
        iframeSupport: {
            localBlankPagePath: '/api/s3blank/'
        },
        retry: {
           enableAuto: false // defaults to false
        },
        validation: {
            acceptFiles: ['.gpx'],
            allowedExtensions: ['gpx']
        },
        button: document.getElementById('gpx-upload-button'),
        text: {
            fileInputTitle: 'Choose file(s)'
        },
        callbacks: {
            onSubmitted: function(id, name) {
                $('#gpx-upload-button-div').prop('disabled', true);
                $('#gpx-upload-button-div').html('<i style="font-size: 22px;" class="fa fa-spinner fa-2x fa-spin" aria-hidden="true"></i>');
                if (uploaderIdle) {
                    $('#add_peak2').prop('disabled', false);
                }
            },
            onComplete: function(id, name, responseJSON, maybeXhr) {
                if (responseJSON.gpx_file != '' && responseJSON.valid_file == 'true') {
                    $("#gpx-file-1").show();
                    $("#map-canvas").height(400);
                    $(".summitLogGpx").hide();
                    $('#remove-gpx-file').show();
                    set_gpx('gpx-file-1', responseJSON);
                    jailai("gpx-file-1");
                    $('#gpx-upload-button-div').html('Choose file(s)');
                    $('#gpx-upload-button-div').prop('disabled', false);
                }
                else {
                    //alert("Sorry, that is not a valid GPX file");
                    $('#message-modal-label').html('Error');
                    $('#message-modal-body').html('Sorry, that is not a valid GPX file. GPX files must include location, time and elevation data.');
                    $('#message-modal').modal('show');
                    reset_gpx_spinner('#gpx-file-1');
                    $('#gpx-upload-button-div').html('Choose file(s)');
                    $('#gpx-upload-button-div').prop('disabled', false);
                }
            }
        },
        messages: {
            typeError: "Invalid file type. Please add only GPX files.",
            sizeError: "File is too large, maximum file size is 12MB.",
            minSizeError: "{file} is too small, minimum file size is {minSizeLimit}.",
            emptyError: "{file} is empty, please select files again without it.",
            allowedExtensionsError : "{file} is not allowed.",
            onLeave: "The files are being uploaded, if you leave now the upload will be cancelled."
        },
        showMessage: function (message) {
            $('#message-modal-label').html('Error');
            $('#message-modal-body').html(message);
            $('#message-modal').modal('show');
        },
        objectProperties: {
            acl: 'public-read',
            key: function (fileId) {

                var filename = gpxuploader.getName(fileId);
                var uuid = gpxuploader.getUuid(fileId);
                var ext = filename.substr(filename.lastIndexOf('.') + 1).toLowerCase();

                return  'gpx/{{ peak.slug_new_text }}_' + uuid + '.' + ext;

            }
        }
    });

    /*
     * Step 3 javascripts
     */
    $("h4.routes-companion-info").click(function(){
        $("div.routes-companion-info").slideToggle();
        //if ($(this).hasClass("open")){$(this).removeClass("open")}else{$(this).addClass("open")}
    });

    $("span#same-route").click(function(){
        var up = $("input#route_up").val();
        if (up!=""){
            $("input#route_down").val(up);
            $("input#route_down").removeClass("blur");
        }
    });

    $("#route_up").change(function(){
        if ($(this).val() == 'add_new_route') {
            $('#route_up_container').hide();
            $('#txtNewSummitRoute').show().focus();
            $('#hide-new-summit-route-span').show();
        }
        if (uploaderIdle) {
            $('#add_peak2').prop('disabled', false);
        }
    });

    //if no peak routes...
    {% if not all_routes_up %}
        $('#route_up_container').hide();
        $('#txtNewSummitRoute').show();
    {% endif %}

    $("#hide-new-summit-route").on('click', function(){
        $('#txtNewSummitRoute').hide().val('');
        $('#hide-new-summit-route-span').hide();
        $('#route_up_container').show();
        $('#route_up').val('0');
    });

    $("div#more").click(function(){
        index = parseInt( $("input#total_fellows").val() );
        cloned = $("#fellow-X").clone();
        cloned.find("input[id='fellow']").attr("name", "fellow-"+index).val("").hint();
        cloned.find("input[id='email']").attr("name", "email-"+index).val("").hint();
        cloned.show();
        $("div#more-fellows").append(cloned);
        index = index + 1;
        $("input#total_fellows").val(index)
    });

    //unit conversions
    $("#elevation-start-m").on('change', function() {
        setElevationStartMeters();
    });

    $("#elevation-start-ft").on('change', function() {
        setElevationStartFeet();
    });

    $("#elevation-gain-m").on('change', function() {
        setElevationGainMeters();
    });

    $("#elevation-gain-ft").on('change', function() {
        setElevationGainFeet();
    });

    $("#distance-to-summit-km").on('change', function() {
        setDistanceToSummitKm();
    });

    $("#distance-to-summit-miles").on('change', function() {
        setDistanceToSummitMiles();
    });

    $("#total-trip-distance-km").on('change', function() {
        setTotalTripDistanceKm();
    });

    $("#total-trip-distance-miles").on('change', function() {
        setTotalTripDistanceMiles();
    });

    textAreaAdjust();

    //switch map units
    $("#gm-custom-mapunits").click(function(){
        if ($('#gm-custom-mapunitsbutton-label-meters').hasClass('gm-custom-mapunits-selected')) {
            toggleMapUnits('feet');
            scale.setUnit('imperial');
        } else {
            toggleMapUnits('meters');
            scale.setUnit('metric');
        }
    });

    //Custom Google Map type stuff

    $('#gm-custom-mapbutton').on('mouseenter', function(){
       $('#gm-custom-mapdropdown').show();
    });

    $('#gm-custom-mapbutton').on('mouseleave', function(){
       $('#gm-custom-mapdropdown').hide();
    });

    $('#gm-custom-mapbutton').on('touchstart', function() {
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-satstreets').on('click', function() {
        toggleMapType('satellite');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-satstreets').on('touchstart', function() {
        toggleMapType('satellite');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-satstreets').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
    });

    $('#gm-custom-mapoption-satstreets').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
    });

    $('#gm-custom-mapoption-topo').on('click', function() {
        toggleMapType('caltopo');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-topo').on('touchstart', function() {
        toggleMapType('caltopo');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-topo').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
    });

    $('#gm-custom-mapoption-topo').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
    });

    $('#gm-custom-mapoption-sat').on('click', function() {
        toggleMapType('sat_topo');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-sat').on('touchstart', function() {
        toggleMapType('sat_topo');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-sat').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
    });

    $('#gm-custom-mapoption-sat').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
    });

    $('#gm-custom-mapoption-terrain').on('click', function() {
        toggleMapType('terrain');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-terrain').on('touchstart', function() {
        toggleMapType('terrain');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-terrain').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
    });

    $('#gm-custom-mapoption-terrain').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
    });

    $('#gm-custom-mapoption-outdoors').on('click', function() {
        toggleMapType('outdoors');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-outdoors').on('touchstart', function() {
        toggleMapType('outdoors');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-outdoors').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
    });

    $('#gm-custom-mapoption-outdoors').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
    });

    //Natural atlas stuff
    $('#gm-custom-mapoption-natatl').on('click', function() {
        toggleMapType('natural_atlas');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-natatl').on('touchstart', function() {
        toggleMapType('natural_atlas');
        $('#gm-custom-mapdropdown').toggle();
    });

    $('#gm-custom-mapoption-natatl').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
    });

    $('#gm-custom-mapoption-natatl').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
    });

});

function saveUnaddedText() {
    //when click the button to save unadded text
    if ($('#txtAddActivityTag').val() != '') {
        $("#btnAddActivityTag").click();
    }
    if ($('#txtAddChallengeTag').val() != '') {
        $("#btnAddChallengeTag").click();
    }
    if ($('#txtAddGearTag').val() != '') {
        $("#btnAddGearTag").click();
    }
    if ($('#summitmemq1').val() != '') {
        $("#btnAddSummitCompanion").click();
    }
    if ($('#summit-video-link').val() != '') {
        $("#btnAddVideoLink").click();
    }
    if ($('#summit-related-link').val() != '') {
        $("#btnAddRelatedLink").click();
    }
    $('#savetext-modal').modal('hide');
    formSubmitted = true;
    $('#add_peak2').html('<i class="fa fa-spinner fa-spin"></i>');
    if (uploaderIdle) {
        saveLog();
    }
}

function replaceAll(str, find, replace) {
  return str.replace(new RegExp(find, 'g'), replace);
}

function numberWithCommas(x) {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

function textAreaAdjust() {
    $('#summit').height(1);
    var scrollHeight = document.getElementById("summit").scrollHeight;
    $('#summit').height(25+scrollHeight);
}

function set_gpx(where, responseJSON){

    var valid_file = responseJSON.valid_file;

    if (valid_file == 'true') {

        gpx_url = responseJSON.gpx_file;

        var totalTripDistance = parseFloat(responseJSON.length_2d);
        if (totalTripDistance > 0) {
            $("#total-trip-distance").val(responseJSON.length_2d);
            if ($('#total-trip-distance-km').is(':checked')) {
                setTotalTripDistanceKm();
            }
        }

        var distanceToSummit = parseFloat(responseJSON.distance_to_summit);
        if (distanceToSummit > 0) {
            $("#distance-to-summit").val(responseJSON.distance_to_summit);
            if ($('#distance-to-summit-km').is(':checked')) {
                setDistanceToSummitKm();
            }
        }

        var elevationStart = parseFloat(responseJSON.start_elevation);
        if (elevationStart > 0) {
            $("#elevation-start").val(responseJSON.start_elevation);
            if ($('#elevation-start-m').is(':checked')) {
                setElevationStartMeters();
            }
        }

        var elevationGain = parseFloat(responseJSON.uphill);
        if (elevationGain > 0) {
            $("#elevation-gain").val(responseJSON.uphill);
            if ($('#elevation-gain-m').is(':checked')) {
                setElevationGainMeters();
            }
        }

        if (distanceToSummit > 0) {
            if (responseJSON.distance_to_summit_hours != '0') {
                $("#time-to-summit-hrs").val(responseJSON.distance_to_summit_hours);
            }
            if (responseJSON.distance_to_summit_minutes != '0') {
                $("#time-to-summit-min").val(responseJSON.distance_to_summit_minutes);
            }
        }
        if (responseJSON.total_hours != '0') {
            $("#total-trip-time-hrs").val(responseJSON.total_hours);
        }
        if (responseJSON.total_minutes != '0') {
            $("#total-trip-time-min").val(responseJSON.total_minutes);
        }
        $("#gpx-file").val(responseJSON.gpx_filename);
        $("#map-canvas").show();
        $("#map-canvas").height(400);
        if (map == null) {
            var latLng = new mapboxgl.LngLat({{ peak.long }}, {{ peak.lat }});
            map = new mapboxgl.Map({
                container: mapDiv, // HTML container id
                style: mapStyle, // style URL
                center: latLng, // starting position as [lng, lat]
                zoom: 14
            });
            //add peak marker
            iconstyle = 'marker_icon_peak';
            //create an HTML element for the marker
            var el = document.createElement('div');
            el.className = iconstyle;
            var marker = new mapboxgl.Marker(el)
                .setLngLat(latLng)
                .setOffset([-5, -10])
                .setDraggable(false)
                .addTo(map);

            function calculateCenter() {
                center = map.getCenter();
            }

            map.on('load', function () {
                calculateCenter();
                var new_center = null;
                $.ajax({
                    type: "GET",
                    url: gpx_url,
                    dataType: "xml",
                    success: function (xml) {
                        bounds = new mapboxgl.LngLatBounds();
                        $(xml).find("trkpt").each(function () {

                            if ($("#gpx-start-lat").val() == '') {
                                $("#gpx-start-lat").val($(this).attr("lat"));
                            }
                            if ($("#gpx-start-lon").val() == '') {
                                $("#gpx-start-lon").val($(this).attr("lon"));
                            }
                            $("#gpx-end-lat").val($(this).attr("lat"));
                            $("#gpx-end-lon").val($(this).attr("lon"));

                            var lat = $(this).attr("lat");
                            var lon = $(this).attr("lon");
                            var p = new mapboxgl.LngLat(lon, lat);
                            points.push(p);
                            linePoints.push([p.lng, p.lat]);
                            if (new_center == null) {
                                new_center = p;
                            }
                            bounds.extend(p);
                        });
                        $(xml).find("rtept").each(function () {

                            if ($("#gpx-start-lat").val() == '') {
                                $("#gpx-start-lat").val($(this).attr("lat"));
                            }
                            if ($("#gpx-start-lon").val() == '') {
                                $("#gpx-start-lon").val($(this).attr("lon"));
                            }
                            $("#gpx-end-lat").val($(this).attr("lat"));
                            $("#gpx-end-lon").val($(this).attr("lon"));

                            var lat = $(this).attr("lat");
                            var lon = $(this).attr("lon");
                            var p = new mapboxgl.LngLat(lon, lat);
                            points.push(p);
                            linePoints.push([p.lng, p.lat]);
                            if (new_center == null) {
                                new_center = p;
                            }
                            bounds.extend(p);
                        });

                        lineData = {
                            "type": "Feature",
                            "properties": {},
                            "geometry": {
                                "type": "LineString",
                                "coordinates": linePoints
                            }
                        }
                        map.addSource('route-data', {
                            type: 'geojson',
                            data: lineData
                        });
                        map.addLayer({
                            "id": "route-layer",
                            "type": "line",
                            "source": "route-data",
                            "layout": {
                                "line-join": "round",
                                "line-cap": "round"
                            },
                            "paint": {
                                "line-color": "#fc202e",
                                "line-width": 4
                            }
                        });

                        //create an HTML element for the marker
                        var el = document.createElement('div');
                        el.className = 'route_marker_fc202e';

                        startmarker = new mapboxgl.Marker(el)
                            .setLngLat(points[0])
                            .setOffset([-5, -10])
                            .setDraggable(false)
                            .addTo(map);

                        // fit bounds to track
                        map.fitBounds(bounds, {padding: 50});

                        setMapControls();
                    }
                });

            });

            map.on('resize', function(e) {
                map.setCenter(center);
            });

        }
    } else {
        //alert('Sorry, errors found in GPX file.');
        $("#gpx-file-1").show();
        $(".summitLogGpx").show();
        $("#map-canvas").hide();
        $("#remove-gpx-file").hide();
        $("#remove-gpx-file").html('Remove GPX file');
        $("#gpx-file").val('');
        if (uploaderIdle) {
            $('#add_peak2').prop('disabled', false);
        }
        $('#message-modal-label').html('Error');
        $('#message-modal-body').html('Sorry, errors found in GPX file. GPX files must include location, time and elevation data.');
        $('#message-modal').modal('show');
    }
}

function setMapControls() {
    var check = checkIfMapboxStyleIsLoaded();
    if (!check) {
        // It's not safe to manipulate layers yet, so wait 200ms and then check again
        setTimeout(function() {
            setMapControls();
        }, 200);
        return;
    }
    // Whew, now it's safe to manipulate layers!
    var mapUnits = readCookie('map_units');
    if (mapUnits == 'meters') {
        toggleMapUnits(mapUnits);
    }
}

function addExtraMapLayers() {
    var check = checkIfMapboxStyleIsLoaded();
    if (!check) {
        // It's not safe to manipulate layers yet, so wait 200ms and then check again
        setTimeout(function() {
            addExtraMapLayers();
        }, 200);
        return;
    }
    // Whew, now it's safe to manipulate layers!
    if (linePoints.length > 0) {
        addPolyline();
    }
}

function addPolyline() {
    map.addSource('route-data', {
        type: 'geojson',
        data: lineData
    });
    map.addLayer({
        "id": "route-layer",
        "type": "line",
        "source": "route-data",
        "layout": {
            "line-join": "round",
            "line-cap": "round"
        },
        "paint": {
            "line-color": "#fc202e",
            "line-width": 4
        }
    });
    map.addLayer({
        "id": "route",
        "type": "line",
        "source": {
            "type": "geojson",
            "data": {
                "type": "Feature",
                "properties": {},
                "geometry": {
                    "type": "LineString",
                    "coordinates": linePoints
                }
            }
        },
        "layout": {
            "line-join": "round",
            "line-cap": "round"
        },
        "paint": {
            "line-color": "#fc202e",
            "line-width": 4
        }
    });
}

function reset_textareas(selector){
    var value = $(selector).val();
    if (value=='full details of your trip...' || value=='write a caption...'){$(selector).val("")}
    $(selector).removeClass("blur");
}

function jailai(selector){
    $("textarea#"+selector).effect("highlight", {}, 3000);
}

function reset_spinner(selector){
    $(selector).find(selector+'-spinner').remove();
    $(selector).find('div.qq-upload-button').removeClass('loading');
    $(selector).find('div.qq-upload-button').children('div').hide();
    $(selector).siblings('.caption').hide();
    $(selector).find('div.qq-uploader').show();
}

function reset_gpx_spinner(selector){
    $(selector).find('div.qq-upload-button').removeClass('qq-upload-loading');
    $(selector).find('div.qq-upload-button').children('div').hide();
};

function cancelEdit() {
    if ($('#add_peak2').prop('disabled') == true) {
        window.location.href = '/{{ peak.slug_new_text }}/';
    } else {
        $('#confirm-modal-label').html('Discard your changes?');
        $('#confirm-modal-body').html('<div style="margin-top: 20px; text-align: center;"><a href="/{{ peak.slug_new_text }}" class="btn btn-primary" style="width: 100px;">Discard</a><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">Cancel</button></div>');
        $('#confirm-modal').modal('show');
    }
}

function confirmRemovePhoto(where) {
    $('#confirm-modal-label').html('Are you sure?');
    $('#confirm-modal-body').html('<div style="margin-top: 20px; text-align: center;"><button onclick="removePhoto(\''+where+'\'); return false;" class="btn btn-primary" style="width: 100px;">Discard</button><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">Cancel</button></div>');
    $('#confirm-modal').modal('show');
}

function removePhoto(where) {
    $('#confirm-modal').modal('hide');
    var step_number = where.split("-")[1];
    step_number--;
    $('.qq-file-id-'+step_number).remove();
}

//unit conversions
function setElevationStartMeters() {
    if ($('#elevation-start').val() != '') {
        var elevation_start_ft = parseFloat(replaceAll($('#elevation-start').val(),',',''));
        var elevation_start_m = (elevation_start_ft * 0.3048).toFixed(0);
        $('#elevation-start').val(elevation_start_m);
        $('.add_peak2').prop('disabled', false);
    }
}

function setElevationStartFeet() {
    if ($('#elevation-start').val() != '') {
        var elevation_start_m = parseFloat(replaceAll($('#elevation-start').val(),',',''));
        var elevation_start_ft = (elevation_start_m / 0.3048).toFixed(0);
        $('#elevation-start').val(elevation_start_ft);
        $('.add_peak2').prop('disabled', false);
    }
}

function setElevationGainMeters() {
    if ($('#elevation-gain').val() != '') {
        var elevation_gain_ft = parseFloat(replaceAll($('#elevation-gain').val(),',',''));
        var elevation_gain_m = (elevation_gain_ft * 0.3048).toFixed(0);
        $('#elevation-gain').val(elevation_gain_m);
        $('.add_peak2').prop('disabled', false);
    }
}

function setElevationGainFeet() {
    if ($('#elevation-gain').val() != '') {
        var elevation_gain_m = parseFloat(replaceAll($('#elevation-gain').val(),',',''));
        var elevation_gain_ft = (elevation_gain_m / 0.3048).toFixed(0);
        $('#elevation-gain').val(elevation_gain_ft);
        $('.add_peak2').prop('disabled', false);
    }
}

function setDistanceToSummitKm() {
    if ($('#distance-to-summit').val() != '') {
        var distance_to_summit_miles = parseFloat($('#distance-to-summit').val());
        var distance_to_summit_km = (distance_to_summit_miles * 1.60934).toFixed(1);
        $('#distance-to-summit').val(distance_to_summit_km);
        $('.add_peak2').prop('disabled', false);
    }
}

function setDistanceToSummitMiles() {
    if ($('#distance-to-summit').val() != '') {
        var distance_to_summit_km = parseFloat($('#distance-to-summit').val());
        var distance_to_summit_miles = (distance_to_summit_km / 1.60934).toFixed(1);
        $('#distance-to-summit').val(distance_to_summit_miles);
        $('.add_peak2').prop('disabled', false);
    }
}

function setTotalTripDistanceKm() {
    if ($('#total-trip-distance').val() != '') {
        var total_trip_distance_miles = parseFloat($('#total-trip-distance').val());
        var total_trip_distance_km = (total_trip_distance_miles * 1.60934).toFixed(1);
        $('#total-trip-distance').val(total_trip_distance_km);
        $('.add_peak2').prop('disabled', false);
    }
}

function setTotalTripDistanceMiles() {
    if ($('#total-trip-distance').val() != '') {
        var total_trip_distance_km = parseFloat($('#total-trip-distance').val());
        var total_trip_distance_miles = (total_trip_distance_km / 1.60934).toFixed(1);
        $('#total-trip-distance').val(total_trip_distance_miles);
        $('.add_peak2').prop('disabled', false);
    }
}

$(function(){
    $('textarea.addACaption').placeholder();
});

</script>

{% endblock %}

{% block end_full_height_form %}
</form>
{% endblock %}