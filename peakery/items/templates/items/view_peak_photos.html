{% load thumbnail %}

<li style='{% autoescape on %}background-image: url("{% thumbnail photo.image 230x160 crop='-0,0' %}"){% endautoescape %}' class="peakeryPhoto photography">
    <a href="{% if photo.image.width > 910 or photo.image.height > 680 %}{% thumbnail photo.image 910x680 upscale %}{% else %}{{ MEDIA_URL }}{{ photo.image }}{% endif %}" class="photoLink" title="{% if photo.caption %}{{ photo.caption }}{% else %}peakery{% endif %}" author="{{ photo.user }}" authorlink="{% url "user_profile" photo.user %}" {% if forloop.counter0 == 0 %}id="trigger"{% endif %} panoramio="false"></a>
    <span class="data">
        {% if photo.caption %}
            <p class="caption">{{ photo.caption|truncatewords:10 }}</p>
        {% endif %}
        {% if photo.summit_log.date_entered %}
            <p class="bagger">{{ photo.user }} &bull; {{ photo.summit_log.date|date:"M j, Y"|default:"" }}</p>
        {% endif %}
    </span>
</li>