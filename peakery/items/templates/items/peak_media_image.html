{% load item_tags %}
<div style="overflow: hidden; width: {% get_image_width image_url %}px" class="image">
    <img src="{{ image_url }}">
    {% if peak.has_thumbnail %}
    <div id="bigphoto" class="facebox">
            {% if peak.has_thumbnail_credit %}
            <div class="credit">
                {% if peak.thumbnail_source %}
                    <a href="{{ peak.thumbnail_source }}" target="_blank"><span>{{ peak.thumbnail_credit|default:"photo credit" }}</span></a>
                {% else %}
                    <span>{{ peak.thumbnail_credit }}</span>
                {% endif %}
            </div>
            {% endif %}
        </div>
    {% endif %}
</div>
