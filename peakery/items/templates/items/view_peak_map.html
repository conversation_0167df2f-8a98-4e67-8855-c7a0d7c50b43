{% extends "base.html" %}
{% load static %}

{% block css %}
    <link href="{{ MEDIA_URL }}css/view-peak.css?v=1" rel="stylesheet" type="text/css" media="screen"/>
    <link href="{{ MEDIA_URL }}css/lightboxes/django-follow.css" rel="stylesheet" type="text/css" media="screen"/>
    <link href="{{ MEDIA_URL }}css/jquery.lightbox-0.5.css" rel="stylesheet" type="text/css" media="screen"/>
    <link href="{{ MEDIA_URL }}css/jquery.lightbox-0.5.css" rel="stylesheet" type="text/css" media="screen"/>
    <link href="{{ MEDIA_URL }}css/tipsy.css" rel="stylesheet" type="text/css" media="screen"/>
{% endblock %}

{% load avatar_tags %}
{% load json_filters %}
{% block title %}{{ peak.get_peakname_title }} map - {{ peak.get_peaklocation_title }}{% endblock %}
{% block titlemeta %}{{ peak.get_peakname_title }} map - {{ peak.get_peaklocation_title }}{% endblock %}
{% block description %}Map of hiking trails and routes up {{ peak.get_peak_title_for_meta }}{% endblock %}

{% block js_globals %}
    var peak_id = {{ peak.id }};
    var peakObject = '{"id": "{{ peak.id }}", "name": {{ peak.name|jsonify|escape_single_quotes }}, "slug": "{{ peak.slug_new_text }}", "is_classic": "{{ peak.is_classic }}", "lat": "{{ peak.lat }}", "lng": "{{ peak.long }}","summit_count": "{{ peak.summitlog_count }}", "your_summits": "{{ your_summits_count }}", "your_attempts": "{{ your_attempts_count }}", "challenge_count": "{{ challenge_count }}", "elevation": "{{ peak.elevation }}","prominence": "{{ peak.prominence }}", "thumbnail_url": "{{ peak.get_thumbnail_480 }}", "region": [{% if peak.region %}{% for r in peak.region.all %}{"region_id": "{{ r.id }}", "region_name": "{{ r.name }}","region_slug": "{{ r.get_absolute_url }}", "country_name": "{{ r.country.name }}"}{% if not forloop.last %},{% endif %}{% endfor %}{% endif %}], "country": [{% if peak.country %}{% for c in peak.country.all %}{"country_id": "{{ c.id }}", "country_name": "{{ c.name }}","country_slug": "{{ c.get_absolute_url }}"}{% if not forloop.last %},{% endif %}{% endfor %}{% endif %}]}';
{% endblock %}

{% block mapbox %}
    {% include "mapbox/mapbox.html" %}
{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row hidden-xs"
         style="position: absolute; top: 70px; left: 15px; width: 100%; max-width: 100%;">
        <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 65px;"><span
                style="font-size: 24px; font-weight: 600;">
            <div id="breadcrumbs">
                <div class="pull-right hidden-xs">
                    <a style="{{ subnav_info_style }}" class="region-header-sub-links" href="/{{ peak.slug_new_text }}">Info</a>
                    <a style="{{ subnav_map_style }}" class="region-header-sub-links"
                       href="/{{ peak.slug_new_text }}/map/">Map</a>
                    <a style="{{ subnav_summits_style }}" class="region-header-sub-links"
                       href="/{{ peak.slug_new_text }}/summits/">Climbs</a>
                    {% if your_summit_count != 0 %}
                        <a href="/{{ peak.slug_new_text }}/summits/#month=&member=you&route=&sort=most_recent&page=1"
                           class="youve-climbed" style="margin-left: 0px;">(You {{ your_summit_count }}x)</a>
                    {% endif %}
                </div>
                {% if alternate_names_list %}
                    <div class="ellipsis" style="line-height: 55px;overflow: hidden;">
                    <h1 class="peak-title">{{ peak.name }}<br><div class="ellipsis"
                                                                   style="font-size: 11px;color: #999999;margin-top: -32px;font-weight: 100;">also known as {% for a in alternate_names_list %}
                        {{ a.name }}{% if not forloop.last %}, {% endif %}{% endfor %}</div></h1>
                </div>
                {% else %}
                    <div class="ellipsis" style="line-height: 70px; overflow: hidden;">
                     <h1 class="peak-title">{{ peak.name }}</h1>
                </div>
                {% endif %}
            </div>
        </span></div>
    </div>
{% endblock %}

{% block content %}

    <style>

        body {
            overflow: hidden;
        }

        #gm-custom-mapregiondropdown::-webkit-scrollbar-track {
            -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            background-color: #F5F5F5;
        }

        #gm-custom-mapregiondropdown::-webkit-scrollbar {
            width: 6px;
            background-color: #F5F5F5;
        }

        #gm-custom-mapregiondropdown::-webkit-scrollbar-thumb {
            border-radius: 10px;
            -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
            background-color: #555;
        }

        body.explore div#main {
            margin-top: -2px;
        }

        #content-holder {
            background-image: none;
        }

        .gm-style-mtc {
            opacity: .8;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 46px;
            height: 25px;
            margin-bottom: 0px;
            margin-top: 5px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            -webkit-transition: .4s;
            transition: .4s;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 17px;
            width: 17px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            -webkit-transition: .4s;
            transition: .4s;
        }

        input:checked + .slider {
            background-color: #2196F3;
        }

        input:focus + .slider {
            box-shadow: 0 0 1px #2196F3;
        }

        input:checked + .slider:before {
            -webkit-transform: translateX(20px);
            -ms-transform: translateX(20px);
            transform: translateX(20px);
        }

        /* Rounded sliders */
        .slider.round {
            border-radius: 25px;
        }

        .slider.round:before {
            border-radius: 50%;
        }

        .blueimp-gallery > .description {
            position: absolute;
            bottom: 0px;
            width: 100%;
            text-align: center;
            color: #fff;
            margin-bottom: 2%;
            height: auto;
            display: none;
        }

        .blueimp-gallery-controls > .description {
            display: block;
        }

        .blueimp-gallery-controls > .description > .description-text {
            padding: 10px;
            background: rgba(0, 0, 0, 0.5);
        }

        #peak-search-mobile > i {
            line-height: 30px;
            margin-left: 9px;
            font-size: 12px;
        }

        #peak-search-layers > i {
            line-height: 30px;
            margin-left: 7px;
            color: #333;
            font-size: 16px;
        }

        #peak-search-legend > i {
            line-height: 30px;
            margin-left: 6px;
            font-size: 18px;
            color: #333;
        }

        #gm-custom-mapfilter, #gm-custom-maplayers, #gm-custom-maplegend {
            -moz-box-shadow: 0 0 2px rgba(0, 0, 0, .1);
            -webkit-box-shadow: 0 0 2px rgba(0, 0, 0, .1);
            box-shadow: 0 0 0 2px rgba(0, 0, 0, .1);
        }

        #peak-search-3d {
            cursor: pointer;
            font-size: 16px;
            color: #333;
            font-weight: 700;
            line-height: 30px;
            text-align: center;
            margin-left: 4px;
        }

        @media screen and (min-width: 1024px) {
            #gm-custom-maplegend {
                bottom: 233px;
            }

            #gm-custom-map3d {
                bottom: 278px;
            }
        }

        @media screen and (min-width: 1px) and (max-width: 1023px) {
            #gm-custom-maplegend {
                top: 100px;
            }

            #gm-custom-map3d {
                top: 145px;
            }
        }

        .mapboxgl-ctrl {
            margin-bottom: 15px !important;
        }

        #gm-custom-mapdropdown, #gm-custom-mapbutton {
            opacity: 1;
            webkit-backdrop-filter: blur(5px);
            backdrop-filter: blur(5px);
        }

        #gm-custom-mapunits:hover {
            background-color: transparent !important;
        }

        #gm-custom-mapbutton {
            border: 2px solid rgba(0, 0, 0, 0.15);
        }

        #gm-custom-mapdropdown {
            box-shadow: rgba(0, 0, 0, 0.3) 0px 1px 8px 0px;
        }

        #gm-custom-mapbutton {
            width: 180px;
            margin-left: 90px;
            border-top-right-radius: 8px;
            border-top-left-radius: 8px;
            border-bottom-right-radius: 8px;
            border-bottom-left-radius: 8px;
        }

        #gm-custom-mapbutton:hover {
            border-bottom-right-radius: 0px;
            border-bottom-left-radius: 0px;
        }

        #gm-custom-mapdropdown {
            width: 179px;
            margin-left: 93px;
            border-bottom-right-radius: 8px;
            border-bottom-left-radius: 8px;
        }

        #gm-custom-mapoption-terrain, #gm-custom-mapoption-natatl, #gm-custom-mapoption-outdoors, #gm-custom-mapoption-streets, #gm-custom-mapoption-topo, #gm-custom-mapoption-satstreets, #gm-custom-mapoption-sat {
            width: 176px;
            margin-left: -2px;
            border-bottom: solid 1px #f2f2f2;
        }

        .gm-custom-mapoption-region {
            width: 260px;
            margin-left: -1px;
            border-bottom: solid 1px #f2f2f2;
        }

        #gm-custom-mapoption-3d {
            width: 176px;
            margin-left: -2px;
            border-bottom: solid 1px #f2f2f2;
        }

        #gm-custom-mapoption-3d {
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
        }

        #gm-custom-mapoption-streets:hover {
            background-color: rgb(235, 235, 235) !important;
            color: rgb(0, 0, 0) !important;
        }

        .gm-custom-mapoption-region:hover {
            background-color: #ebebeb !important;
            color: #000 !important;
        }

        @media screen and (max-width: 767px) and (min-width: 1px) {
            #peaks-map {
                top: 50px;
            }
        }

        @media screen and (max-width: 1023px) and (min-width: 768px) {
            #peaks-map {
                top: 20px;
            }

            #log-your-climb {
                margin-left: 20px;
            }

            .peak-title {
                font-size: 16px;
                font-weight: 600;
            }
        }

        @media screen and (max-width: 767px) and (min-width: 1px) {
            #gm-custom-mapregiondropdown {
                right: -315px;
                top: 30px;
                max-height: 480px;
            }

            #content-body {
                margin-top: 20px;
            }

            #mobile-peak-search {
                top: 50px;
            }

            #log-your-climb {
                margin-left: 0px;
            }

            .peak-title {
                font-size: 12px;
                font-weight: 500;
            }
        }

        @media screen and (min-width: 768px) {
            #gm-custom-mapregiondropdown {
                right: -531px;
                top: 52px;
                max-height: 640px;
            }

            #mobile-peak-search {
                top: 2px;
            }

            #peak-search {
                top: 14px;
            }

            #content-body {
                margin-top: 69px;
            }
        }

        @media screen and (min-width: 1024px) {
            div#explore .leftCol {
                margin-top: -14px;
            }

            #log-your-climb {
                margin-left: 50px;
            }

            .peak-title {
                font-size: 20px;
                font-weight: 600;
            }

            #peaks-map {
                top: 20px;
            }

            #gm-custom-mapunits {
                right: 142px;
            }

            #collapse-search {
                top: 0px;
            }
        }

        .photo-marker {
            display: block;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            padding: 0;
        }

        .map-tooltip-info {
            background-color: rgba(0, 0, 0, .6);
            -webkit-backdrop-filter: blur(0px) !important;
        }

        .toggle-switch {
            height: 40px;
        }

        .toggle-switch label {
            line-height: 40px;
            color: #666;
        }

        div#explore .leftCol {
            background-color: #fff;
        }


        div#length-slider-range {
            -webkit-border-radius: 4px;
            -moz-border-radius: 4px;
            border-radius: 4px;
            height: 2px;
            overflow: visible;
            background-color: #ccc;
        }

        #length-slider {
            padding-top: 0px;
            margin-left: 8px;
        }

        div#vertical-slider-range {
            -webkit-border-radius: 4px;
            -moz-border-radius: 4px;
            border-radius: 4px;
            height: 2px;
            overflow: visible;
            background-color: #ccc;
        }

        #vertical-slider {
            padding-top: 8px;
            margin-left: 8px;
        }

        div#last-climbed-slider-range {
            -webkit-border-radius: 4px;
            -moz-border-radius: 4px;
            border-radius: 4px;
            height: 2px;
            overflow: visible;
            background-color: #ccc;
        }

        #last-climbed-slider {
            padding-top: 8px;
            margin-left: 8px;
        }

        #last-climbed-slider-range > .noUi-base > .noUi-origin > .noUi-handle-upper {
            display: none;
        }

        #summits-slider, #elevation-slider, #prominence-slider, #difficulty-slider {
            padding-top: 8px;
        }

        .noUi-connect {
            background: #ccc;
        }

        .marker_icon:hover, .marker_icon_red:hover, .marker_icon_green:hover, .marker_icon_redgreen:hover, .marker_icon_yellow:hover, .marker_icon_purple:hover, .marker_icon_peak:hover, .marker-icon-hover {
            background-image: url('{% static 'img/<EMAIL>' %}') !important;
            height: 28px;
            width: 28px;
            -webkit-animation-name: markerPulse;
            -webkit-animation-duration: 3s;
            -webkit-animation-iteration-count: infinite;
        }

        .marker_icon_classic {
            background-image: url({% static '' %}img/<EMAIL>);
            height: 28px;
            width: 28px;
        }

        .marker_icon_firstascent {
            background-image: url({% static '' %}img/<EMAIL>);
            height: 28px;
            width: 28px;
        }

        .marker_icon_kom {
            background-image: url({% static '' %}img/<EMAIL>);
            height: 28px;
            width: 28px;
        }

        .marker_icon_steward {
            background-image: url({% static '' %}img/<EMAIL>);
            height: 28px;
            width: 28px;
        }

        @-webkit-keyframes markerPulse {
            from {
                -webkit-filter: brightness(1.2) saturate(1.5);
            }
            50% {
                -webkit-filter: brightness(0.9) saturate(1);
            }
            to {
                -webkit-filter: brightness(1.2) saturate(1.5);
            }
        }

        @media screen and (min-width: 769px) {
            .noUi-horizontal .noUi-handle {
                width: 35px;
                height: 35px;
                left: -24px;
                top: -17px;
                background-color: transparent;
                background-size: contain;
            }
        }

        .noUi-horizontal .noUi-handle {
            background-image: url({% static '' %}img/search-drag-handle-grey.png);
        }

        /*tooltip animations*/
        .scale-in-tl {
            -webkit-animation: scale-in-tl .1s cubic-bezier(0.50, 1.3, .70, 1) both;
            animation: scale-in-tl .1s cubic-bezier(0.50, 1.3, .70, 1) both;
            opacity: 1;
        }

        @-webkit-keyframes scale-in-tl {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
                opacity: 1;
            }
        }

        @keyframes scale-in-tl {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
                opacity: 1;
            }
        }

        .scale-out-tl {
            -webkit-animation: scale-out-tl .1s cubic-bezier(0.3, .05, .5, .1) both;
            animation: scale-out-tl .1s cubic-bezier(0.3, .05, .5, .1) both;
            opacity: 0;
        }

        @-webkit-keyframes scale-out-tl {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
                opacity: 1;
            }
        }

        @keyframes scale-out-tl {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
                opacity: 1;
            }
        }

        .scale-in-tm {
            -webkit-animation: scale-in-tm .1s cubic-bezier(0.50, 1.3, .70, 1) both;
            animation: scale-in-tm .1s cubic-bezier(0.50, 1.3, .70, 1) both;
            opacity: 1;
        }

        @-webkit-keyframes scale-in-tm {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
                opacity: 1;
            }
        }

        @keyframes scale-in-tm {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
                opacity: 1;
            }
        }

        .scale-out-tm {
            -webkit-animation: scale-out-tm .1s cubic-bezier(0.3, .05, .5, .1) both;
            animation: scale-out-tm .1s cubic-bezier(0.3, .05, .5, .1) both;
            opacity: 0;
        }

        @-webkit-keyframes scale-out-tm {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
                opacity: 1;
            }
        }

        @keyframes scale-out-tm {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
                opacity: 1;
            }
        }

        .scale-in-tr {
            -webkit-animation: scale-in-tr .1s cubic-bezier(0.50, 1.3, .70, 1) both;
            animation: scale-in-tr .1s cubic-bezier(0.50, 1.3, .70, 1) both;
            opacity: 1;
        }

        @-webkit-keyframes scale-in-tr {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
                opacity: 1;
            }
        }

        @keyframes scale-in-tr {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
                opacity: 1;
            }
        }

        .scale-out-tr {
            -webkit-animation: scale-out-tr .1s cubic-bezier(0.3, .05, .5, .1) both;
            animation: scale-out-tr .1s cubic-bezier(0.3, .05, .5, .1) both;
            opacity: 0;
        }

        @-webkit-keyframes scale-out-tr {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
                opacity: 1;
            }
        }

        @keyframes scale-out-tr {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
                opacity: 1;
            }
        }

        /*bottom*/
        .scale-in-bl {
            -webkit-animation: scale-in-bl .1s cubic-bezier(0.50, 1.3, .70, 1) both;
            animation: scale-in-bl .1s cubic-bezier(0.50, 1.3, .70, 1) both;
            opacity: 1;
        }

        @-webkit-keyframes scale-in-bl {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
                opacity: 1;
            }
        }

        @keyframes scale-in-bl {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
                opacity: 1;
            }
        }

        .scale-out-bl {
            -webkit-animation: scale-out-bl .1s cubic-bezier(0.3, .05, .5, .1) both;
            animation: scale-out-bl .1s cubic-bezier(0.3, .05, .5, .1) both;
            opacity: 0;
        }

        @-webkit-keyframes scale-out-bl {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
                opacity: 1;
            }
        }

        @keyframes scale-out-bl {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
                opacity: 1;
            }
        }

        .scale-in-bm {
            -webkit-animation: scale-in-bm .1s cubic-bezier(0.50, 1.3, .70, 1) both;
            animation: scale-in-bm .1s cubic-bezier(0.50, 1.3, .70, 1) both;
            opacity: 1;
        }

        @-webkit-keyframes scale-in-bm {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
                opacity: 1;
            }
        }

        @keyframes scale-in-bm {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
                opacity: 1;
            }
        }

        .scale-out-bm {
            -webkit-animation: scale-out-bm .1s cubic-bezier(0.3, .05, .5, .1) both;
            animation: scale-out-bm .1s cubic-bezier(0.3, .05, .5, .1) both;
            opacity: 0;
        }

        @-webkit-keyframes scale-out-bm {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
                opacity: 1;
            }
        }

        @keyframes scale-out-bm {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
                opacity: 1;
            }
        }

        .scale-in-br {
            -webkit-animation: scale-in-br .1s cubic-bezier(0.50, 1.3, .70, 1) both;
            animation: scale-in-br .1s cubic-bezier(0.50, 1.3, .70, 1) both;
            opacity: 1;
        }

        @-webkit-keyframes scale-in-br {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
                opacity: 1;
            }
        }

        @keyframes scale-in-br {
            0% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
                opacity: 1;
            }
        }

        .scale-out-br {
            -webkit-animation: scale-out-br .1s cubic-bezier(0.3, .05, .5, .1) both;
            animation: scale-out-br .1s cubic-bezier(0.3, .05, .5, .1) both;
            opacity: 0;
        }

        @-webkit-keyframes scale-out-br {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
                opacity: 1;
            }
        }

        @keyframes scale-out-br {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
                -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
                opacity: 1;
            }
            100% {
                -webkit-transform: scale(0);
                transform: scale(0);
                -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
                opacity: 1;
            }
        }

        .mapboxgl-ctrl-attrib {
            display: none !important;
        }

    </style>

    <div id="explore" class="peak_list_cont clearfix">
        <div id="mobile-collapse-nav"
             style="display: none; position: absolute; left: 0px; top: -35px; z-index: 9999;"></div>
        <div class="full-width-container" style="width: 100%; height: inherit;">
            <div class="row peak-list-content" style="min-height: 100px;">
                <!-- Mobile header -->
                <div class="row sub-header-row hidden-lg hidden-md hidden-sm"
                     style="height: 50px; position: fixed; left: 15px; width: 100%; z-index: 999;">
                    <div class="col-md-12"
                         style="height: 50px; line-height: 50px; font-size: 12px; color: #999; display: flex; justify-content: space-between;">
                        <span><a style="{{ subnav_info_style }}" class="mobile-header-sub-links"
                                 href="/{{ peak.slug_new_text }}/">Info</a></span>
                        <span><a style="{{ subnav_map_style }}" class="mobile-header-sub-links"
                                 href="/{{ peak.slug_new_text }}/map/">Map</a></span>
                        <span><a style="{{ subnav_summits_style }}" class="mobile-header-sub-links"
                                 href="/{{ peak.slug_new_text }}/summits/">Climbs</a>
                            {% if your_summit_count != 0 %}
                                <a class="mobile-header-sub-links youve-climbed"
                                   href="/{{ peak.slug_new_text }}/summits/#type=you"
                                   style="margin-left: 0px;">(You {{ your_summit_count }}x)</a></span>
                                {% if IS_MOBILE_APP_ACCESS == 'True' %}
                                    <span><a id="log-your-climb" class="mobile-header-sub-links"
                                             href="javascript: Android.logClimb(peakObject);" style="color: #00b1f2;">Log climb</a></span>
                                {% else %}
                                    <span><a id="log-your-climb" class="mobile-header-sub-links"
                                             href="{% url "log_climb_multi_simple" %}?peak={{ peak.id }}"
                                             style="color: #00b1f2;">Log climb</a></span>
                                {% endif %}
                            {% else %}
                                {% if user.is_authenticated %}
                                    {% if IS_MOBILE_APP_ACCESS == 'True' %}
                                        </span><span><a id="log-your-climb" class="mobile-header-sub-links"
                                                        href="javascript: Android.logClimb(peakObject);"
                                                        style="color: #00b1f2;">Log climb</a></span>
                                    {% else %}
                                        </span><span><a id="log-your-climb" class="mobile-header-sub-links"
                                                        href="{% url "log_climb_multi_simple" %}?peak={{ peak.id }}"
                                                        style="color: #00b1f2;">Log climb</a></span>
                                    {% endif %}
                                {% else %}
                                    </span><span><a id="log-your-climb" class="mobile-header-sub-links"
                                                    style="color: #00b1f2; cursor: pointer;" data-toggle="modal"
                                                    data-target="#accounts-login">Log climb</a></span>
                                {% endif %}
                            {% endif %}
                    </div>
                </div>
                <!-- End mobile header -->
                <div id="peaks-map" class="col_data rightCol">
                    <input type="text" id="hdnElevMin" hidden value="">
                    <input type="text" id="hdnElevMax" hidden value="">
                    <input type="text" id="hdnPromMin" hidden value="">
                    <input type="text" id="hdnPromMax" hidden value="">
                    <input type="text" id="hdnSummitsMin" hidden value="">
                    <input type="text" id="hdnSummitsMax" hidden value="">
                    <input type="text" id="hdnDifficultyMin" hidden value="">
                    <input type="text" id="hdnDifficultyMax" hidden value="">
                    <input type="text" id="hdnNear" hidden value="">
                    <input type="text" id="hdnLat" hidden value="">
                    <input type="text" id="hdnLng" hidden value="">
                    <input type="text" id="hdnBounds" hidden value="">
                    <input type="text" id="hdnNearQuery" hidden value="false">
                    <div id="map-canvas" style="width: 100%; height: 100%;">
                        <div id="gm-custom-mapunits" class="gmnoprint gm-style-mtc"
                             style="display: none; margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 100px; top: 0px;">
                            <div id="gm-custom-mapunitsbutton" draggable="false" title="Change map units"
                                 style="cursor: pointer; direction: ltr; overflow: hidden; text-align: center; position: relative; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 11px; -webkit-background-clip: padding-box; background-clip: padding-box; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; font-weight: 500;">
                                <span id="gm-custom-mapunitsbutton-label-feet"
                                      class="gm-custom-mapunits-selected">Feet</span> | <span
                                    id="gm-custom-mapunitsbutton-label-meters" class="gm-custom-mapunits-unselected">Meters</span>
                            </div>
                        </div>
                        <div id="gm-custom-map3d" class="gmnoprint gm-style-mtc"
                             style="display: none; margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 30px; height: 30px; border-radius: 5px; right: 0px; background-color: #fff; opacity: 1;">
                            <a id="peak-search-3d" style="cursor: pointer;">3D</a>
                        </div>
                        <div id="gm-custom-maplegend" class="gmnoprint gm-style-mtc"
                             style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 30px; height: 30px; border-radius: 5px; right: 0px; background-color: #fff; opacity: 1;">
                            <a id="peak-search-legend" style="cursor: pointer;"><i style="margin-left: 5px;"
                                                                                   class="fas fa-eye"></i></a>
                        </div>
                        <div id="gm-custom-mapfilter" class="gmnoprint gm-style-mtc hidden-lg hidden-md"
                             style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 30px; height: 30px; border-radius: 5px; top: 5px; right: 0px; background-color: #fff; opacity: 1;">
                            <a id="peak-search-mobile" style="cursor: pointer;"><i class="fa fa-filter"></i></a>
                        </div>
                        <div id="gm-custom-maplayers" class="gmnoprint gm-style-mtc hidden-lg hidden-md"
                             style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 30px; height: 30px; border-radius: 5px; top: 53px; right: 0px; background-color: #fff; opacity: 1;">
                            <a id="peak-search-layers" style="cursor: pointer;"><i class="fas fa-layer-group"></i></a>
                        </div>
                        <div id="gm-custom-maptype" class="gmnoprint"
                             style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 180px; right: 50%; top: 0px;">
                            <div id="gm-custom-mapbutton" class="hidden-xs hidden-sm" draggable="false"
                                 style="cursor: pointer; direction: ltr; overflow: hidden; text-align: left; position: relative; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); height: 54px; padding: 8px; -webkit-background-clip: padding-box; background-clip: padding-box; font-weight: 500;">
                                <div style="float: left;"><img
                                        style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                        src="{% static '' %}img/<EMAIL>">
                                </div>
                                <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Terrain</div>
                            </div>
                        </div>
                        <div id="gm-custom-mapdropdown-container" class="gmnoprint"
                             style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; right: 50%; margin-right: 191px; top: 0px;">
                            <div id="gm-custom-mapdropdown"
                                 style="z-index: 10; padding-left: 2px; padding-right: 2px; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; position: absolute; top: 50px; left: 0px; right: 0px; text-align: left; display: none;">
                                <div id="gm-custom-mapoption-terrain" draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/<EMAIL>">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Terrain</div>
                                </div>
                                <div id="gm-custom-mapoption-natatl" draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/<EMAIL>">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Natural Atlas
                                        <span style="font-size: 10px;">(US)</span></div>
                                </div>
                                <div id="gm-custom-mapoption-outdoors" draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/<EMAIL>">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">OpenTopoMap
                                    </div>
                                </div>
                                <div id="gm-custom-mapoption-streets" draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/<EMAIL>">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Streets</div>
                                </div>
                                <div id="gm-custom-mapoption-topo" draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500; width: 177px; border-right: 1px solid #aaa;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/<EMAIL>">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Government Topo
                                        <span style="font-size: 10px;">&nbsp;<i class="fa fa-caret-right"
                                                                                aria-hidden="true"></i></span></div>
                                </div>
                                <div id="gm-custom-mapoption-satstreets" draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/<EMAIL>">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite</div>
                                </div>
                                <div id="gm-custom-mapoption-sat" draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/<EMAIL>">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite Topo
                                    </div>
                                </div>
                                <div class="" id="gm-custom-mapoption-3d" draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/<EMAIL>"></div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">3D Map</div>
                                </div>
                            </div>
                            <div id="gm-custom-mapregiondropdown"
                                 style="z-index: 10; padding-left: 0px; padding-right: 2px; position: absolute; text-align: left; overflow: hidden auto; display: none;">
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-argentina-ign-50k"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/ar.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Argentina (IGN -
                                        50K)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-argentina-ign-100k"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/ar.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Argentina (IGN -
                                        100K)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-australia-nsw"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/au.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Australia - NSW
                                        (SIX)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-australia-qld"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/au.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Australia - QLD
                                        (QTopo)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-australia-sa"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/au.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Australia - SA
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-australia-ts"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/au.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Australia - TAS
                                        (LIST)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-australia-vic"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/au.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Australia - VIC
                                        (VicMap)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-austria-bergfex"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/at.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Austria
                                        (BergFex)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-austria-bev"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/at.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Austria (BEV)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-belgium-ngi"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/be.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Belgium (NGI)
                                    </div>
                                </div>

                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-brazil"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/br.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Brazil
                                        (IBGE)
                                    </div>
                                </div>

                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-canada-caltopo"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/ca.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Canada (NRCAN)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-canada-on"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/ca.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Canada - ON
                                        (OBM)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-canada-qc"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/ca.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Canada - QC
                                        (MERN)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-croatia-dgu"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/hr.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Croatia (DGU)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-czechia-cuzk"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/cz.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Czechia (ČÚZK)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-finland-nls"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/fi.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Finland (NLS)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-france-ign"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/fr.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">France (IGN)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-germany-oa"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/de.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Germany
                                        (OutdoorActive)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-hongkong-landsd"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/hk.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Hong Kong
                                        (LandsD)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-iceland-caltopo"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/is.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Iceland
                                        (CalTopo)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-iceland-new"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/is.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Iceland
                                        (Landmælingar)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-israel-hikingosm"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/il.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Israel (Hiking
                                        OSM)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-japan-gsi"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/jp.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Japan (GSI)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-luxembourg"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/lu.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Luxembourg
                                        (ACT)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-mexico-inegi"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/mx.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Mexico (INEGI)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-newzealand-linz"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/nz.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">New Zealand
                                        (LINZ)
                                    </div>
                                </div>
                                 <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-norway-new"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/no.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Norway
                                        (Kartvertek)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-norway-kartverket"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/no.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Norway
                                        (Old Kartvertek)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-norway-janmayen"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/no.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Norway - Jan
                                        Mayen (NPI)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-norway-svalbard"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/no.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Norway -
                                        Svalbard (NPI)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-philippines-namria"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/ph.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Philippines
                                        (NAMRIA)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-poland-geoportal"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/pl.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Poland
                                        (Geoportal)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-slovakia-dgu"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/sk.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Slovakia (DGU)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-slovenia-prostor"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/si.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Slovenia
                                        (ProStor)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-southafrica-ngi-50k" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/za.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">South Africa NGI 50K</div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-spain-ign"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/es.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Spain (IGN)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-spain-cataluna"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/es.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Spain - Cataluña
                                        (ICGC)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-sweden-sgu"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/se.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Sweden (SGU)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-switzerland-swisstopo"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/ch.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Switzerland
                                        (swisstopo)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-taiwan-nlsc"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/tw.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Taiwan (NLSC)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-uk-os" draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/gb.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">United Kingdom
                                        (OS)
                                    </div>
                                </div>
                                <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-us-caltopo"
                                     draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img
                                            style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;"
                                            src="{% static '' %}img/flags/us.svg">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">United States
                                        (USGS)
                                    </div>
                                </div>
                            </div>
                            <div id="gm-custom-maplegend-dropdown"
                                 style="z-index: 10; border: 2px solid rgba(0,0,0,0.15); position: absolute; width: 270px; top: 52px; left: 40px; right: 0px; text-align: left; border-radius: 8px; display: none;">
                                <div id="gm-custom-maplegend-highest" draggable="false"
                                     style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500; border-top-left-radius: 8px; border-top-right-radius: 8px;">
                                    <div style="float: left;"><img style="width: 34px;"
                                                                   src="{% static '' %}img/<EMAIL>">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Highest peak on
                                        map
                                    </div>
                                </div>
                                <div id="gm-custom-maplegend-yoursummits" draggable="false"
                                     style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img style="width: 34px;"
                                                                   src="{% static '' %}img/<EMAIL>">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Climbed by you
                                        <div style="float: right; display: none;"><label class="switch"><input
                                                id="map-summits-toggle" type="checkbox"><span
                                                class="slider round"></span></label></div>
                                    </div>
                                </div>
                                <div id="gm-custom-maplegend-yourattempts" draggable="false"
                                     style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img style="width: 34px;"
                                                                   src="{% static '' %}img/<EMAIL>">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Attempted by you
                                        <div style="float: right; display: none;"><label class="switch"><input
                                                id="map-attempts-toggle" type="checkbox"><span
                                                class="slider round"></span></label></div>
                                    </div>
                                </div>
                                <div id="gm-custom-maplegend-unclimbed" draggable="false"
                                     style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img style="width: 34px;"
                                                                   src="{% static '' %}img/<EMAIL>">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Unclimbed by you
                                        <div style="float: right; display: none;"><label class="switch"><input
                                                id="map-unclimbed-toggle" type="checkbox"><span
                                                class="slider round"></span></label></div>
                                    </div>
                                </div>
                                <div id="gm-custom-maplegend-yourkings" draggable="false"
                                     style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img style="width: 34px;"
                                                                   src="{% static '' %}img/<EMAIL>">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Your King of the
                                        Mountains
                                        <div style="float: right; {% if not request.user.is_authenticated %}display: none;{% endif %}">
                                            <label class="switch"><input id="map-kom-toggle" type="checkbox"><span
                                                    class="slider round"></span></label></div>
                                    </div>
                                </div>
                                <div id="gm-custom-maplegend-yourstewards" draggable="false"
                                     style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img style="width: 34px;"
                                                                   src="{% static '' %}img/<EMAIL>">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Your Summit
                                        Stewards
                                        <div style="float: right; {% if not request.user.is_authenticated %}display: none;{% endif %}">
                                            <label class="switch"><input id="map-stewards-toggle" type="checkbox"><span
                                                    class="slider round"></span></label></div>
                                    </div>
                                </div>
                                <div id="gm-custom-maplegend-yourfirstascents" draggable="false"
                                     style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img style="width: 34px;"
                                                                   src="{% static '' %}img/<EMAIL>">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Your First
                                        Ascents
                                        <div style="float: right; {% if not request.user.is_authenticated %}display: none;{% endif %}">
                                            <label class="switch"><input id="map-firstascents-toggle"
                                                                         type="checkbox"><span
                                                    class="slider round"></span></label></div>
                                    </div>
                                </div>
                                <div id="gm-custom-maplegend-classics" draggable="false"
                                     style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img style="width: 34px;"
                                                                   src="{% static '' %}img/<EMAIL>">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Classic peaks
                                        <div style="float: right;"><label class="switch"><input id="map-classics-toggle"
                                                                                                type="checkbox"><span
                                                class="slider round"></span></label></div>
                                    </div>
                                </div>
                                <div id="gm-custom-maplegend-challenges" draggable="false"
                                     style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                    <div style="float: left;"><img style="width: 34px;"
                                                                   src="{% static '' %}img/<EMAIL>">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Peak Challenges
                                        <div style="float: right;"><label class="switch"><input
                                                id="map-challenges-toggle" type="checkbox"><span
                                                class="slider round"></span></label></div>
                                    </div>
                                </div>
                                <div id="gm-custom-maplegend-photos" draggable="false"
                                     style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500; border-bottom-left-radius: 8px; border-bottom-right-radius: 8px;">
                                    <div style="float: left;"><img style="width: 34px;"
                                                                   src="{% static '' %}img/<EMAIL>">
                                    </div>
                                    <div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Photos
                                        <div style="float: right;"><label class="switch"><input id="map-photos-toggle"
                                                                                                type="checkbox"><span
                                                class="slider round"></span></label></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div id="message_map_div" class="gmnoprint gm-style-mtc"
                             style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 104px; right: 0px; top: 0px; display: none;">
                            <div id="message_map" draggable="false"
                                 style="direction: ltr; overflow: hidden; text-align: left; position: relative; color: rgb(86, 86, 86); font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 11px; border-bottom-left-radius: 2px; border-top-left-radius: 2px; border-bottom-right-radius: 2px; border-top-right-radius: 2px; -webkit-background-clip: padding-box; background-clip: padding-box; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; font-weight: 500;">

                            </div>
                        </div>
                    </div>
                    <div id="marker-tooltip" data-url="" data-index=""
                         style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; cursor: pointer; opacity: 0; position:absolute; width: 200px; height: 150px; background: rgba(0, 0, 0, 0.3); margin: 15px;"></div>
                    <div id="photo-tooltip" data-url="" data-index=""
                         style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; cursor: pointer; opacity: 0; position:absolute; width: 200px; height: 150px; background: rgba(0, 0, 0, 0.3); margin: 15px;"></div>
                    <div id="challenge-tooltip" data-url="" data-index=""
                         style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; cursor: pointer; opacity: 0; position:absolute; width: 200px; height: 150px; background: rgba(0, 0, 0, 0.3); margin: 15px;"></div>

                </div><!-- END col_2 -->
            </div>
        </div>
    </div><!-- END paek_list_cont -->

    <script type="text/javascript">

        var map;
        var mapCountry = '';
        var topo;
        var outdoors;
        var pageX, pageY, mapX, mapY;
        var iconstyle;

        $(function () {

            //add log this climb option to log climb dropdown
            $('#log-climb-log-manually .navbar-primary .log-climb-dropdown-label-div').html('Log another peak');
            $('#log-climb-log-this-peak .navbar-primary').attr('href', '/peaks/log_climb/?peak={{ peak.id }}');
            $('#log-climb-log-this-peak .navbar-primary .log-climb-dropdown-label-div').html('Log {{ peak.name }}');
            $('#log-climb-log-this-peak').show();

//            Top-left nav button ("find peaks") width to be it same at the width of the leftCol:
            var leftColWidth = $('div#explore .leftCol').width();
            $('li.headlink').css('width', leftColWidth);
//            Peak name input needs to show the remove text icon when the user enter text on it:
            var a = $('a#clear_peak_name');
            var a2 = $('a#clear_near_location');

            var input = $('input#q');
            var input2 = $('input#n');

            if (input.val() != "") {
                a.css('display', 'block');
            }

            if (input2.val() != "") {
                a2.css('display', 'block');
            }

            input.keyup(function () {
                if ($(this).val() != "") {
                    a.css('display', 'block');
                } else {
                    a.css('display', 'none');
                }
            });

            input2.keyup(function () {
                if ($(this).val() != "") {
                    a2.css('display', 'block');
                } else {
                    a2.css('display', 'none');
                }
            });

            a.click(function () {
                input.val('');
                $(this).css('display', 'none');
            });

            a2.click(function () {
                input2.val('');
                $(this).css('display', 'none');
            });

            $('#peak-search').css({left: '0px'});

        });

        var timers = {};

        function hideMapTooltip() {
            if ($('#marker-tooltip').hasClass('scale-in-tl')) {
                $('#marker-tooltip').addClass('scale-out-tl').removeClass('scale-in-tl');
            } else if ($('#marker-tooltip').hasClass('scale-in-tm')) {
                $('#marker-tooltip').addClass('scale-out-tm').removeClass('scale-in-tm');
            } else if ($('#marker-tooltip').hasClass('scale-in-tr')) {
                $('#marker-tooltip').addClass('scale-out-tr').removeClass('scale-in-tr');
            } else if ($('#marker-tooltip').hasClass('scale-in-bl')) {
                $('#marker-tooltip').addClass('scale-out-bl').removeClass('scale-in-bl');
            } else if ($('#marker-tooltip').hasClass('scale-in-bm')) {
                $('#marker-tooltip').addClass('scale-out-bm').removeClass('scale-in-bm');
            } else if ($('#marker-tooltip').hasClass('scale-in-br')) {
                $('#marker-tooltip').addClass('scale-out-br').removeClass('scale-in-br');
            }
        }

        function hidePhotoTooltip() {
            if ($('#photo-tooltip').hasClass('scale-in-tl')) {
                $('#photo-tooltip').addClass('scale-out-tl').removeClass('scale-in-tl');
            } else if ($('#photo-tooltip').hasClass('scale-in-tm')) {
                $('#photo-tooltip').addClass('scale-out-tm').removeClass('scale-in-tm');
            } else if ($('#photo-tooltip').hasClass('scale-in-tr')) {
                $('#photo-tooltip').addClass('scale-out-tr').removeClass('scale-in-tr');
            } else if ($('#photo-tooltip').hasClass('scale-in-bl')) {
                $('#photo-tooltip').addClass('scale-out-bl').removeClass('scale-in-bl');
            } else if ($('#photo-tooltip').hasClass('scale-in-bm')) {
                $('#photo-tooltip').addClass('scale-out-bm').removeClass('scale-in-bm');
            } else if ($('#photo-tooltip').hasClass('scale-in-br')) {
                $('#photo-tooltip').addClass('scale-out-br').removeClass('scale-in-br');
            }
        }

        function hideChallengeTooltip() {
            if ($('#challenge-tooltip').hasClass('scale-in-tl')) {
                $('#challenge-tooltip').addClass('scale-out-tl').removeClass('scale-in-tl');
            } else if ($('#challenge-tooltip').hasClass('scale-in-tm')) {
                $('#challenge-tooltip').addClass('scale-out-tm').removeClass('scale-in-tm');
            } else if ($('#challenge-tooltip').hasClass('scale-in-tr')) {
                $('#challenge-tooltip').addClass('scale-out-tr').removeClass('scale-in-tr');
            } else if ($('#challenge-tooltip').hasClass('scale-in-bl')) {
                $('#challenge-tooltip').addClass('scale-out-bl').removeClass('scale-in-bl');
            } else if ($('#challenge-tooltip').hasClass('scale-in-bm')) {
                $('#challenge-tooltip').addClass('scale-out-bm').removeClass('scale-in-bm');
            } else if ($('#challenge-tooltip').hasClass('scale-in-br')) {
                $('#challenge-tooltip').addClass('scale-out-br').removeClass('scale-in-br');
            }
        }

        function round(value, decimals) {
            return Number(Math.round(value + 'e' + decimals) + 'e-' + decimals);
        }

        function getRepString(rep) {
            rep = rep + ''; // coerce to string
            if (rep < 1000) {
                return rep; // return the same number
            }
            // divide and format
            return (rep / 1000).toFixed(rep % 1000 != 0) + 'K';
        }

        function updateURLParameter(url, param, paramVal) {

            var newAdditionalURL = "";
            var tempArray = url.split("#");
            var baseURL = tempArray[0];
            var additionalURL = tempArray[1];
            var temp = "";

            if (additionalURL) {
                tempArray = additionalURL.split("&");
                for (i = 0; i < tempArray.length; i++) {
                    if (tempArray[i].split('=')[0] != param) {
                        newAdditionalURL += temp + tempArray[i];
                        temp = "&";
                    }
                }
            }

            if (paramVal === undefined) {
                return newAdditionalURL;
            }

            const rows_txt = temp + "" + param + "=" + encodeURI(paramVal);
            return newAdditionalURL + rows_txt;
        }

        function numberWithCommas(x) {
            return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        }

        function loadPeaks() {

            //set map center cookie
            var NewMapCenter = map.getCenter();
            var NewMapZoom = map.getZoom();
            createCookie('map_lat', NewMapCenter.lat, 365);
            createCookie('map_lng', NewMapCenter.lng, 365);

            //get 2d map bounds or create 3d map bounds
            var mapButton = $('#gm-custom-mapbutton').html();
            if (mapButton.includes('3D Map')) {
                var camLat = map.getCenter().lat;
                var camLng = map.getCenter().lng;
                var sw = new mapboxgl.LngLat(camLng - .10, camLat - .10);
                var ne = new mapboxgl.LngLat(camLng + .10, camLat + .10);
                var bounds = new mapboxgl.LngLatBounds(sw, ne);
            } else if ($('#peak-search-3d').html() == '2D') {
                var camLat = map.getCenter().lat;
                var camLng = map.getCenter().lng;
                var sw = new mapboxgl.LngLat(camLng - .10, camLat - .10);
                var ne = new mapboxgl.LngLat(camLng + .10, camLat + .10);
                var bounds = new mapboxgl.LngLatBounds(sw, ne);
            } else {
                var bounds = map.getBounds();
            }

            var counter = 0;

            var params = '';
            params = params + '&elev_min=0';
            params = params + '&elev_max=29500';
            params = params + '&prom_min=0';
            params = params + '&prom_max=29500';
            params = params + '&summits_min=0';
            params = params + '&summits_max=500';
            params = params + '&difficulty_min=1';
            params = params + '&difficulty_max=5';
            params = params + '&bounds=' + bounds.getSouthWest().lat + ',' + bounds.getSouthWest().lng + ',' + bounds.getNorthEast().lat + ',' + bounds.getNorthEast().lng + '&zoom=' + map.getZoom() + '&near=' + $('#hdnNearQuery').val();

            //update hidden parameters
            $('#hdnElevMin').val(0);
            $('#hdnElevMax').val(29500);
            $('#hdnPromMin').val(0);
            $('#hdnPromMax').val(29500);
            $('#hdnSummitsMin').val(0);
            $('#hdnSummitsMax').val(500);
            $('#hdnDifficultyMin').val(1);
            $('#hdnDifficultyMax').val(5);
            $('#hdnBounds').val(bounds.getSouthWest().lat + ',' + bounds.getSouthWest().lng + ',' + bounds.getNorthEast().lat + ',' + bounds.getNorthEast().lng);

            var LatLngList = [];

            if (params.length > 0) params = '?' + params.slice(-1 * (params.length - 1));
            $.getJSON('{% url "peaks_map_get_peaks" %}' + params + '&get_photos=true', function (data) {
                $.each(data, function (key, val) {
                    var currentRequest = true;
                    if (key == 'parameters') {
                        $.each(val, function (parameterkey, parameterval) {
                            if (parameterval.elev_min != $('#hdnElevMin').val()) currentRequest = false;
                            if (parameterval.elev_max != $('#hdnElevMax').val()) currentRequest = false;
                            if (parameterval.prom_min != $('#hdnPromMin').val()) currentRequest = false;
                            if (parameterval.prom_max != $('#hdnPromMax').val()) currentRequest = false;
                            if (parameterval.summits_min != $('#hdnSummitsMin').val()) currentRequest = false;
                            if (parameterval.summits_max != $('#hdnSummitsMax').val()) currentRequest = false;
                            if (parameterval.difficulty_min != $('#hdnDifficultyMin').val()) currentRequest = false;
                            if (parameterval.difficulty_max != $('#hdnDifficultyMax').val()) currentRequest = false;
                            if (parameterval.bounds != $('#hdnBounds').val()) currentRequest = false;
                        });
                    }

                    if (!currentRequest) {
                        return false;
                    }

                    if (key == 'peaks') {

                        var havePeaks = false;

                        $.each(val, function (peakkey, peakval) {

                            if (!havePeaks) {

                                //first time through, delete highest peak marker and remove any markers not on map
                                deletehighest();
                                //delete markers out of margins
                                delete_old_markers(val);

                            }

                            havePeaks = true;

                            //build country string
                            var country = '';
                            $.each(peakval.country, function (countrykey, countryval) {
                                country = country + '<div><a href="' + countryval.country_slug + '/">' + countryval.country_name + '</a></div>';
                            });
                            if (country == '') {
                                country = 'Unknown Country';
                            }

                            //build region string
                            var region = '';
                            var mobile_region = '';
                            var region_bull_class = 'hidden-lg hidden-md hidden-sm hidden-xs';
                            $.each(peakval.region, function (regionkey, regionval) {
                                region_bull_class = '';
                                region = region + '<div style="margin-bottom: 5px;"><a class="truncate" href="' + regionval.region_slug + '/">' + regionval.region_name + ', ' + regionval.country_name + '</a></div>';
                                mobile_region = '<div style="margin-bottom: 5px;"><a class="truncate" href="' + regionval.region_slug + '/">' + regionval.country_name + '</a></div>';
                            });
                            if (region == '') {
                                region = country;
                                mobile_region = country;
                            }

                            //build challenges string
                            var challenges = '';
                            var challenge_count = peakval.challenge_count;
                            if (challenge_count > 0) {
                                challenges = ' &bull; <span>' + challenge_count + ' <i class="fa fa-trophy"></i></span>';
                            }

                            //build summits string
                            var summits, tooltip_your_summits;
                            if (peakval.summit_count > 0) {
                                summits = '<a href="/' + peakval.slug + '/summits/">' + peakval.summit_count + '</a>';
                            } else {
                                summits = '<a href="/' + peakval.slug + '/summits/">0</a><br /><a href="/' + peakval.slug + '/">Be the first!</a>';
                            }
                            if (peakval.your_summits > 0) {
                                summits = summits + '<br /><span style="color: #3fc663; font-weight: 500;">' + peakval.your_summits + 'x by you</span>';
                                tooltip_your_summits = '&nbsp;<span style="color: #3fc663; font-weight: 500;">(You ' + peakval.your_summits + 'x)</span>';
                            } else {
                                tooltip_your_summits = '';
                            }

                            //tooltip vars
                            var tooltip_html, tooltip_width, tooltip_height, marker_top, marker_left,
                                tooltip_total_width, tooltip_total_height;

                            //show tooltip badges?
                            var showClassic = 'display: none;';
                            var showChallenges = 'display: none;';
                            var showYourSummits = 'display: none;';
                            if (peakval.is_classic == 'True') {
                                showClassic = '';
                            }
                            if (peakval.challenge_count > 0) {
                                showChallenges = '';
                            }
                            if (peakval.your_summits > 0) {
                                showYourSummits = '';
                            }

                            //build tooltip string
                            if (peakval.thumbnail_url.indexOf('default.png.350x245_q95_crop.jpg') < 0) {
                                tooltip_html = '<img class="map-tooltip-img hover-photos-hover" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px;" src="{{ MEDIA_URL }}' + peakval.thumbnail_url + '"><div class="map-tooltip-peak-badges" style="font-weight:500; top: 115px; width: 220px; position: absolute; background-color: transparent; background-image: linear-gradient(to bottom, transparent 0px, rgba(0, 0, 0, 0.7) 100%); padding-left: 5px;"><div id="peak-classic-badge" style="' + showClassic + ' width: 65px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00a7ee; font-size: 8px; color: #fff; float: left; text-align: center; line-height: 20px;"><i class="fa fa-star" style="float: left; margin-left: 5px; line-height: 18px; filter: drop-shadow(0px 0px 1px #333); color: #feeeb9;"></i> CLASSIC</div><div id="peak-challenges-badge" style="' + showChallenges + ' width: 95px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #5c48e0; font-size: 8px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-trophy" style="filter: drop-shadow(0px 0px 1px #333); color: #feeeb9; float: left; margin-left: 5px; line-height: 18px;"></i> ' + peakval.challenge_count + ' CHALLENGE' + ((peakval.challenge_count != 1) ? 'S' : '') + '</div><div id="peak-your-summits-badge" style="' + showYourSummits + ' width: 40px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00b524; font-size: 9px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-check" style="filter: drop-shadow(0px 0px 1px #333); float: left; margin-left: 5px; line-height: 20px;"></i> ' + peakval.your_summits + 'x</div></div><div class="map-tooltip-info" style="border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; bottom: -35px; height: 100px; background-color: transparent !important;"><div style="margin-left: -8px; margin-top: 43px; height: 55px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; box-shadow: 0 5px 8px 0 rgba(0,0,0,0.30);"><div class="map-tooltip-peak-name ellipsis" style="color: #333; text-align: left; padding-left: 10px; font-size: 15px; width: 210px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; padding-left: 10px;"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div></div>';
                                tooltip_width = 220;
                                tooltip_height = 165;
                                tooltip_total_width = 250;
                                tooltip_total_height = 230;
                            } else {
                                if (peakval.is_classic == 'True' || peakval.challenge_count > 0 || peakval.your_summits > 0) {
                                    tooltip_html = '<div class="map-tooltip-peak-badges" style="font-weight:500; top: 4px; z-index: 2; width: 220px; position: absolute; background-color: transparent; padding-left: 5px;"><div id="peak-classic-badge" style="' + showClassic + ' width: 65px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00a7ee; font-size: 8px; color: #fff; float: left; text-align: center; line-height: 20px;"><i class="fa fa-star" style="float: left; margin-left: 5px; line-height: 18px; filter: drop-shadow(0px 0px 1px #333); color: #feeeb9;"></i> CLASSIC</div><div id="peak-challenges-badge" style="' + showChallenges + ' width: 95px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #5c48e0; font-size: 8px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-trophy" style="filter: drop-shadow(0px 0px 1px #333); color: #feeeb9; float: left; margin-left: 5px; line-height: 18px;"></i> ' + peakval.challenge_count + ' CHALLENGE' + ((peakval.challenge_count != 1) ? 'S' : '') + '</div><div id="peak-your-summits-badge" style="' + showYourSummits + ' width: 40px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00b524; font-size: 9px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-check" style="filter: drop-shadow(0px 0px 1px #333); float: left; margin-left: 5px; line-height: 20px;"></i> ' + peakval.your_summits + 'x</div></div><div class="map-tooltip-info" style="height: 75px; border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; box-shadow: 0 5px 8px 0 rgba(0,0,0,0.30);"><div class="map-tooltip-peak-name ellipsis" style="margin-top: 25px; color: #333; text-align: left; font-size: 15px; width: 210px; height: 27px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; margin-bottom: -5px;"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div>';
                                    tooltip_width = 220;
                                    tooltip_height = 75;
                                    tooltip_total_width = 250;
                                    tooltip_total_height = 105;
                                } else {
                                    tooltip_html = '<div class="map-tooltip-info" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; box-shadow: 0 5px 8px 0 rgba(0,0,0,0.30);"><div class="map-tooltip-peak-name ellipsis" style="color: #333; text-align: left; font-size: 15px; width: 210px; height: 27px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; margin-bottom: -5px;"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div>';
                                    tooltip_width = 220;
                                    tooltip_height = 50;
                                    tooltip_total_width = 250;
                                    tooltip_total_height = 80;
                                }
                            }

                            var tooltip_url = '/' + peakval.slug;

                            var latLng = new mapboxgl.LngLat(peakval.lng, peakval.lat);

                            if (counter == 0) {
                                //highest peak gets red icon
                                iconstyle = 'marker_icon_red';
                            } else if (peakval.id == peak_id) {
                                // if the peak of the current iteration is the peak for this page, we use the orange marker
                                iconstyle = 'marker_icon_peak';
                            } else if (peakval.your_summits > 0) {
                                //if you have summited then green icon
                                iconstyle = 'marker_icon_green';
                            } else if (peakval.your_attempts > 0) {
                                //if you have attempted then yellow icon
                                iconstyle = 'marker_icon_yellow';
                            } else {
                                iconstyle = 'marker_icon';
                            }

                            if (isTouchDevice()) {
                                var is_draggable = false;
                            } else {
                                {% if request.user.is_staff %}
                                    var is_draggable = true;
                                {% else %}
                                    var is_draggable = false;
                                {% endif %}
                            }

                            //check if already exist so don't put again
                            var exists = false;
                            for (i = markersArray.length - 1; i >= 0; i--) {
                                if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng)) {
                                    exists = true;
                                    //if the highest is in the actual viewport, not as the highest, delete it
                                    if (iconstyle == 'marker_icon_red' || iconstyle == 'marker_icon_redgreen' || iconstyle == 'marker_icon_green') {
                                        markersArray[i].remove();
                                        markersArray.splice(i, 1);
                                        exists = false;
                                    }
                                }
                            }

                            if (!exists) {
                                var latLng = [peakval.lng, peakval.lat];
                                //add marker
                                //create an HTML element for the marker
                                var el = document.createElement('div');
                                el.className = iconstyle;
                                el.setAttribute('id', 'peak-marker-' + peakval.id);

                                if (peakval.is_classic == 'True') {
                                    el.setAttribute('data-classic', 'true');
                                }

                                {% if request.user.is_authenticated %}

                                    if (peakval.kom_user == '{{ request.user.id }}') {
                                        el.setAttribute('data-kom', 'true');
                                    }

                                    if (peakval.first_ascent_user == '{{ request.user.id }}') {
                                        el.setAttribute('data-firstascent', 'true');
                                    }

                                    if (peakval.summit_stewards != '') {
                                        var stewards = $.parseJSON(peakval.summit_stewards.replace(/&quot;/g, '"'));
                                        $.each(stewards, function (key, data) {
                                            if (key == 'summit_stewards') {
                                                for (var i = 0; i < data.length; i++) {
                                                    if (data[i] == '{{ request.user.id }}') {
                                                        el.setAttribute('data-steward', 'true');
                                                    }
                                                }
                                            }
                                        });
                                    }

                                {% endif %}

                                el.addEventListener('click', function (e) {
                                    if (isTouchDevice()) {
                                        hideMapTooltip();
                                        $('#gm-custom-mapdropdown').hide();
                                        $('#gm-custom-maplegend-dropdown').hide();

                                        var bottom = $('#map-canvas').height();
                                        var right = $('#map-canvas').width();
                                        var showClass = 'scale-in-';
                                        var hideClass = 'scale-out-';

                                        var markerX = this.getBoundingClientRect().x + 14;
                                        var markerY = this.getBoundingClientRect().y + 14 - 100;

                                        if (markerY < (bottom / 2)) {
                                            marker_top = markerY;
                                            showClass = showClass + 't';
                                            hideClass = hideClass + 't';
                                        } else {
                                            marker_top = markerY - tooltip_total_height - 15;
                                            showClass = showClass + 'b';
                                            hideClass = hideClass + 'b';
                                        }

                                        if (markerX < (right / 3)) {
                                            marker_left = markerX;
                                            showClass = showClass + 'l';
                                            hideClass = hideClass + 'l';
                                        } else if (markerX >= (right / 3) && markerX < ((right / 3) * 2)) {
                                            marker_left = markerX - (tooltip_total_width / 2);
                                            showClass = showClass + 'm';
                                            hideClass = hideClass + 'm';
                                        } else {
                                            marker_left = markerX - tooltip_total_width;
                                            showClass = showClass + 'r';
                                            hideClass = hideClass + 'r';
                                        }

                                        $('#marker-tooltip').html(marker.properties.tooltipContent).css({
                                            'left': marker_left,
                                            'top': marker_top,
                                            'width': tooltip_width,
                                            'height': tooltip_height
                                        });
                                        $('#marker-tooltip').removeClass('scale-out-tl').removeClass('scale-out-tm').removeClass('scale-out-tr').removeClass('scale-out-bl').removeClass('scale-out-bm').removeClass('scale-out-br');
                                        $('#marker-tooltip').addClass(showClass);
                                        $('#marker-tooltip').data('url', marker.properties.tooltipUrl);
                                        if ($('#gm-custom-mapunitsbutton-label-feet').hasClass('gm-custom-mapunits-unselected')) {
                                            $('#marker-tooltip').find('.peak-elevation-feet').hide();
                                            $('#marker-tooltip').find('.peak-elevation-meters').show();
                                        } else {
                                            $('#marker-tooltip').find('.peak-elevation-meters').hide();
                                            $('#marker-tooltip').find('.peak-elevation-feet').show();
                                        }
                                    } else {
                                        //console.log(peakval.slug);
                                        location = '/' + peakval.slug + '/';;
                                    }
                                    e.stopPropagation();
                                });

                                el.addEventListener('mouseover', function (e) {

                                    if (!isTouchDevice()) {

                                        var bottom = $('#map-canvas').height();
                                        var right = $('#map-canvas').width();
                                        var showClass = 'scale-in-';
                                        var hideClass = 'scale-out-';

                                        var markerX = this.getBoundingClientRect().x + 14;
                                        var markerY = this.getBoundingClientRect().y + 14 - 120;

                                        if (markerY < (bottom / 2)) {
                                            marker_top = markerY;
                                            showClass = showClass + 't';
                                            hideClass = hideClass + 't';
                                        } else {
                                            marker_top = markerY - tooltip_total_height - 15;
                                            showClass = showClass + 'b';
                                            hideClass = hideClass + 'b';
                                        }

                                        if (markerX < (right / 3)) {
                                            marker_left = markerX;
                                            showClass = showClass + 'l';
                                            hideClass = hideClass + 'l';
                                        } else if (markerX >= (right / 3) && markerX < ((right / 3) * 2)) {
                                            marker_left = markerX - (tooltip_total_width / 2);
                                            showClass = showClass + 'm';
                                            hideClass = hideClass + 'm';
                                        } else {
                                            marker_left = markerX - tooltip_total_width;
                                            showClass = showClass + 'r';
                                            hideClass = hideClass + 'r';
                                        }

                                        $('#marker-tooltip').html(marker.properties.tooltipContent).css({
                                            'left': marker_left,
                                            'top': marker_top,
                                            'width': tooltip_width,
                                            'height': tooltip_height
                                        }).addClass(showClass).removeClass('scale-out-tl').removeClass('scale-out-tm').removeClass('scale-out-tr').removeClass('scale-out-bl').removeClass('scale-out-bm').removeClass('scale-out-br');
                                        $('#marker-tooltip').data('url', marker.properties.tooltipUrl);
                                        if ($('#gm-custom-mapunitsbutton-label-feet').hasClass('gm-custom-mapunits-unselected')) {
                                            $('#marker-tooltip').find('.peak-elevation-feet').hide();
                                            $('#marker-tooltip').find('.peak-elevation-meters').show();
                                        } else {
                                            $('#marker-tooltip').find('.peak-elevation-meters').hide();
                                            $('#marker-tooltip').find('.peak-elevation-feet').show();
                                        }
                                    }
                                    e.stopPropagation();

                                });

                                el.addEventListener('mouseout', function (e) {
                                    if (isTouchDevice()) {
                                        //$('#marker-tooltip').hide();
                                    } else {
                                        hideMapTooltip();
                                    }
                                });

                                var marker = new mapboxgl.Marker(el)
                                    .setLngLat(latLng)
                                    .setOffset([-5, -10])
                                    .setDraggable(is_draggable);

                                marker.properties = {};
                                marker.properties.tooltipContent = tooltip_html;
                                marker.properties.tooltipUrl = tooltip_url;
                                marker.properties.iconstyle = iconstyle;
                                marker.properties.peakid = peakval.id;

                                {% if request.user.is_staff %}
                                    //editing functions
                                    marker.on('dragstart', function (e) {
                                        hideMapTooltip();
                                    });
                                    marker.on('dragend', function (e) {
                                        var point = marker.getLngLat();
                                        fix_item_location(peakval.id, point);
                                    });

                                    el.addEventListener('contextmenu', function (e) {
                                        if (confirm("Are you sure you want to delete this peak?")) {
                                            delete_peak_from_map(peakval.id);
                                            hideMapTooltip();
                                            for (i = markersArray.length - 1; i >= 0; i--) {
                                                if (markersArray[i].properties.peakid == peakval.id) {
                                                    markersArray[i].remove();
                                                    markersArray.splice(i, 1);
                                                }
                                            }
                                        }
                                    });
                                {% endif %}

                                markersArray.push(marker);
                                LatLngList.push(latLng);

                            }

                            counter++;
                        });

                        if (!havePeaks) {
                            //didn't have any peaks, so remove all markers
                            delete_old_markers(val);
                        }
                    }

                    //add photo markers
                    if (NewMapZoom >= 13) {
                        for (var i = photoMarkersArray.length - 1; i >= 0; --i) {
                            photoMarkersArray[i].addTo(map);
                        }
                    }

                    //add challenge markers
                    for (var i = challengeMarkersArray.length - 1; i >= 0; --i) {
                        challengeMarkersArray[i].addTo(map);
                    }

                    //add peak markers to map in reverse order so highest on top
                    for (var i = markersArray.length - 1; i >= 0; --i) {
                        markersArray[i].addTo(map);
                    }

                });

                //show classics if needed
                if ($("#map-classics-toggle").is(':checked')) {
                    $('.mapboxgl-marker[data-classic=true]').addClass('marker_icon_classic');
                }

                //show kom if needed
                if ($("#map-kom-toggle").is(':checked')) {
                    $('.mapboxgl-marker[data-kom=true]').addClass('marker_icon_kom');
                }

                //show first ascents if needed
                if ($("#map-firstascents-toggle").is(':checked')) {
                    $('.mapboxgl-marker[data-firstascent=true]').addClass('marker_icon_firstascent');
                }

                //show summit stewards if needed
                if ($("#map-stewards-toggle").is(':checked')) {
                    $('.mapboxgl-marker[data-steward=true]').addClass('marker_icon_steward');
                }

                //hide unclimbed if needed
                if (!$("#map-unclimbed-toggle").is(':checked')) {
                    $('.marker_icon').hide();
                }

                //hide attempts if needed
                if (!$("#map-attempts-toggle").is(':checked')) {
                    $('.marker_icon_yellow').hide();
                }

                //hide summits if needed
                if (!$("#map-summits-toggle").is(':checked')) {
                    $('.marker_icon_green').hide();
                }

            });

            $.getJSON('{% url "peaks_map_get_photos" %}' + params, function (data) {
                $.each(data, function (key, val) {
                    if (key == 'photos') {

                        var havePhotos = false;
                        var aryPeakPhotos = [];
                        delete_all_photo_markers(val);

                        $.each(val, function (photokey, photoval) {

                            if (!havePhotos) {

                                //first time through, delete all photo markers
                                delete_all_photo_markers(val);

                            }

                            havePhotos = true;

                            //tooltip vars
                            var tooltip_html, tooltip_width, tooltip_height, marker_top, marker_left,
                                tooltip_total_width, tooltip_total_height;

                            //build tooltip string
                            tooltip_html = '<div style="width: 100%; height: 100%; border-radius: 8px; background-image: url({{ MEDIA_URL }}' + photoval.thumbnail_url + '); background-size: cover;"></div>';
                            tooltip_width = 165;
                            tooltip_total_width = 205;
                            tooltip_height = 165;
                            tooltip_total_height = 165;

                            var tooltip_url = '/' + photoval.fullsize_url;

                            var latLng = new mapboxgl.LngLat(photoval.photo_lng, photoval.photo_lat);

                            var latLng = [photoval.photo_lng, photoval.photo_lat];
                            //add marker
                            //create an HTML element for the marker
                            var el_div = document.createElement('div');
                            var el = document.createElement('a');

                            el_div.className = 'marker-photo-icon';
                            el.className = 'gallery-link';

                            //hide photos if necessary
                            if ($("#map-photos-toggle").is(':checked')) {
                                el.style.display = 'block';
                            } else {
                                el.style.display = 'none';
                            }

                            el_div.style.backgroundImage = 'url({{ MEDIA_URL }}' + photoval.thumbnail_url + ')';
                            el_div.style.width = '20px';
                            el_div.style.height = '20px';
                            el_div.style.border = '2px solid rgba(255,255,255)';
                            el_div.style.backgroundSize = 'cover';
                            el_div.setAttribute('id', 'photo-marker-' + photoval.id);

                            el.setAttribute('data-user', photoval.username);
                            el.setAttribute('data-gallery', '');
                            el.setAttribute('data-description', encodeDoubleQuotes(photoval.caption));
                            el.setAttribute('data-createdate', photoval.createdate);
                            el.setAttribute('data-peak-slug', photoval.peak_slug);
                            el.setAttribute('data-summit-log-id', photoval.summit_log_id);
                            el.setAttribute('href', '{{ MEDIA_URL }}' + photoval.fullsize_url);

                            el.appendChild(el_div);

                            el.addEventListener('mouseover', function (e) {

                                if (!isTouchDevice()) {

                                    var bottom = $('#map-canvas').height();
                                    var right = $('#map-canvas').width();
                                    var showClass = 'scale-in-';
                                    var hideClass = 'scale-out-';

                                    var markerX = this.getBoundingClientRect().x + 18;

                                    if ($('#peak-search').css('left') == '0px') {
                                        markerX = markerX - 240;
                                    }

                                    var markerY = this.getBoundingClientRect().y + 18 - 50;

                                    if (markerY < (bottom / 2)) {
                                        marker_top = markerY;
                                        showClass = showClass + 't';
                                        hideClass = hideClass + 't';
                                    } else {
                                        marker_top = markerY - tooltip_total_height - 45;
                                        showClass = showClass + 'b';
                                        hideClass = hideClass + 'b';
                                    }

                                    if (markerX < (right / 3)) {
                                        marker_left = markerX;
                                        showClass = showClass + 'l';
                                        hideClass = hideClass + 'l';
                                    } else if (markerX >= (right / 3) && markerX < ((right / 3) * 2)) {
                                        marker_left = markerX - (tooltip_total_width / 2);
                                        showClass = showClass + 'm';
                                        hideClass = hideClass + 'm';
                                    } else {
                                        marker_left = markerX - tooltip_total_width;
                                        showClass = showClass + 'r';
                                        hideClass = hideClass + 'r';
                                    }

                                    $('#photo-tooltip').html(marker.properties.tooltipContent).css({
                                        'left': marker_left,
                                        'top': marker_top,
                                        'width': tooltip_width,
                                        'height': tooltip_height
                                    }).addClass(showClass).removeClass('scale-out-tl').removeClass('scale-out-tm').removeClass('scale-out-tr').removeClass('scale-out-bl').removeClass('scale-out-bm').removeClass('scale-out-br');
                                    $('#photo-tooltip').data('url', marker.properties.tooltipUrl);
                                }
                                e.stopPropagation();

                            });

                            el.addEventListener('mouseout', function (e) {
                                if (isTouchDevice()) {
                                    //$('#marker-tooltip').hide();
                                } else {
                                    hidePhotoTooltip();
                                }
                            });

                            var marker = new mapboxgl.Marker(el)
                                .setLngLat(latLng)
                                .setOffset([-5, -10]);

                            marker.properties = {};
                            marker.properties.tooltipContent = tooltip_html;
                            marker.properties.tooltipUrl = tooltip_url;
                            marker.properties.photoid = photoval.id;
                            marker.properties.lat = photoval.photo_lat;
                            marker.properties.lng = photoval.photo_lng;

                            photoMarkersArray.push(marker);

                        });
                    }
                })
            })

            $.getJSON('{% url "peaks_map_get_challenges" %}' + params, function (data) {
                $.each(data, function (key, val) {
                    if (key == 'challenges') {

                        var haveChallenges = false;
                        var aryPeakChallenges = [];
                        delete_all_challenge_markers();

                        $.each(val, function (challengekey, challengeval) {

                            if (!haveChallenges) {

                                //first time through, delete any markers not on map
                                //delete markers out of margins
                                delete_old_challenge_markers(val);

                            }

                            haveChallenges = true;

                            //tooltip vars
                            var tooltip_html, tooltip_width, tooltip_height, marker_top, marker_left,
                                tooltip_total_width, tooltip_total_height;

                            //build tooltip string
                            tooltip_html = '<img class="map-tooltip-img hover-photos-hover" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px;" src="{{ MEDIA_URL }}images/' + challengeval.thumbnail_url + '"><div class="map-tooltip-info" style="border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; bottom: -35px; height: 100px; background-color: transparent !important;"><div style="margin-left: -8px; margin-top: 43px; height: 55px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; box-shadow: 0 5px 8px 0 rgba(0,0,0,0.30);"><div class="map-tooltip-peak-name ellipsis" style="color: #333; text-align: left; padding-left: 10px; font-size: 15px; width: 210px;">' + challengeval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; padding-left: 10px;">' + challengeval.peak_count + ' peak' + ((challengeval.peak_count != 1) ? 's' : '') + '</div></div></div>';
                            tooltip_width = 220;
                            tooltip_height = 165;
                            tooltip_total_width = 250;
                            tooltip_total_height = 230;

                            var tooltip_url = '/challenges/' + challengeval.slug + '/';

                            var latLng = new mapboxgl.LngLat(challengeval.challenge_lng, challengeval.challenge_lat);

                            var latLng = [challengeval.challenge_lng, challengeval.challenge_lat];
                            //add marker
                            //create an HTML element for the marker
                            var el_div = document.createElement('div');
                            var el = document.createElement('a');

                            el.className = 'marker-challenge-icon';

                            //hide challenges if necessary
                            if ($("#map-challenges-toggle").is(':checked')) {
                                el.style.display = 'block';
                            } else {
                                el.style.display = 'none';
                            }

                            el.style.backgroundImage = 'url({% static '' %}img/<EMAIL>)';
                            el.style.width = '28px';
                            el.style.height = '28px';
                            //el.style.border = '2px solid rgba(255,255,255)';
                            el.style.backgroundSize = 'cover';
                            el.setAttribute('id', 'challenge-marker-' + challengeval.id);

                            el.addEventListener('click', function (e) {
                                if (isTouchDevice()) {
                                    hideChallengeTooltip();

                                    var bottom = $('#map-canvas').height();
                                    var right = $('#map-canvas').width();
                                    var showClass = 'scale-in-';
                                    var hideClass = 'scale-out-';

                                    var markerX = this.getBoundingClientRect().x + 14;
                                    var markerY = this.getBoundingClientRect().y + 14 - 100;

                                    if (markerY < (bottom / 2)) {
                                        marker_top = markerY;
                                        showClass = showClass + 't';
                                        hideClass = hideClass + 't';
                                    } else {
                                        marker_top = markerY - tooltip_total_height - 15;
                                        showClass = showClass + 'b';
                                        hideClass = hideClass + 'b';
                                    }

                                    if (markerX < (right / 3)) {
                                        marker_left = markerX;
                                        showClass = showClass + 'l';
                                        hideClass = hideClass + 'l';
                                    } else if (markerX >= (right / 3) && markerX < ((right / 3) * 2)) {
                                        marker_left = markerX - (tooltip_total_width / 2);
                                        showClass = showClass + 'm';
                                        hideClass = hideClass + 'm';
                                    } else {
                                        marker_left = markerX - tooltip_total_width;
                                        showClass = showClass + 'r';
                                        hideClass = hideClass + 'r';
                                    }

                                    $('#challenge-tooltip').html(marker.properties.tooltipContent).css({
                                        'left': marker_left,
                                        'top': marker_top,
                                        'width': tooltip_width,
                                        'height': tooltip_height
                                    });
                                    $('#challenge-tooltip').removeClass('scale-out-tl').removeClass('scale-out-tm').removeClass('scale-out-tr').removeClass('scale-out-bl').removeClass('scale-out-bm').removeClass('scale-out-br');
                                    $('#challenge-tooltip').addClass(showClass);
                                    $('#challenge-tooltip').data('url', marker.properties.tooltipUrl);
                                } else {
                                    location = '/challenges/' + challengeval.slug;
                                }
                                e.stopPropagation();
                            });

                            el.addEventListener('mouseover', function (e) {

                                if (!isTouchDevice()) {

                                    var bottom = $('#map-canvas').height();
                                    var right = $('#map-canvas').width();
                                    var showClass = 'scale-in-';
                                    var hideClass = 'scale-out-';

                                    var markerX = this.getBoundingClientRect().x + 14;

                                    if ($('#peak-search').css('left') == '0px') {
                                        markerX = markerX - 240;
                                    }

                                    var markerY = this.getBoundingClientRect().y + 14 - 50;

                                    if (markerY < (bottom / 2)) {
                                        marker_top = markerY;
                                        showClass = showClass + 't';
                                        hideClass = hideClass + 't';
                                    } else {
                                        marker_top = markerY - tooltip_total_height - 15;
                                        showClass = showClass + 'b';
                                        hideClass = hideClass + 'b';
                                    }

                                    if (markerX < (right / 3)) {
                                        marker_left = markerX;
                                        showClass = showClass + 'l';
                                        hideClass = hideClass + 'l';
                                    } else if (markerX >= (right / 3) && markerX < ((right / 3) * 2)) {
                                        marker_left = markerX - (tooltip_total_width / 2);
                                        showClass = showClass + 'm';
                                        hideClass = hideClass + 'm';
                                    } else {
                                        marker_left = markerX - tooltip_total_width;
                                        showClass = showClass + 'r';
                                        hideClass = hideClass + 'r';
                                    }

                                    $('#challenge-tooltip').html(marker.properties.tooltipContent).css({
                                        'left': marker_left,
                                        'top': marker_top,
                                        'width': tooltip_width,
                                        'height': tooltip_height
                                    }).addClass(showClass).removeClass('scale-out-tl').removeClass('scale-out-tm').removeClass('scale-out-tr').removeClass('scale-out-bl').removeClass('scale-out-bm').removeClass('scale-out-br');
                                    $('#challenge-tooltip').data('url', marker.properties.tooltipUrl);
                                }
                                e.stopPropagation();

                            });

                            el.addEventListener('mouseout', function (e) {
                                if (isTouchDevice()) {
                                    //$('#marker-tooltip').hide();
                                } else {
                                    hideChallengeTooltip();
                                }
                            });

                            var marker = new mapboxgl.Marker(el)
                                .setLngLat(latLng)
                                .setOffset([-5, -10]);

                            marker.properties = {};
                            marker.properties.tooltipContent = tooltip_html;
                            marker.properties.tooltipUrl = tooltip_url;
                            marker.properties.photoid = challengeval.id;
                            marker.properties.lat = challengeval.challenge_lat;
                            marker.properties.lng = challengeval.challenge_lng;

                            challengeMarkersArray.push(marker);

                        });
                    }
                })
            })

        }

        var markersArray = [];
        var photoMarkersArray = [];
        var challengeMarkersArray = [];
        var initLoad = true;

        const throttle = (fn, wait) => {
            let inThrottle, lastFn, lastTime;
            return function () {
                const context = this,
                    args = arguments;
                if (!inThrottle) {
                    fn.apply(context, args);
                    lastTime = Date.now();
                    inThrottle = true;
                } else {
                    clearTimeout(lastFn);
                    lastFn = setTimeout(function () {
                        if (Date.now() - lastTime >= wait) {
                            fn.apply(context, args);
                            lastTime = Date.now();
                        }
                    }, Math.max(wait - (Date.now() - lastTime), 0));
                }
            };
        };

        const throttleLoadPeaks = throttle(() => { loadPeaks(); }, 3500);

        function initialize() {

            var mapDiv = document.getElementById('map-canvas');
            var latLng = new mapboxgl.LngLat({{ peak.long }}, {{ peak.lat }});

            initMapType('terrain');

            var initZoom = 14;

            if (isTouchDevice()) {
                map = new mapboxgl.Map({
                    container: mapDiv, // HTML container id
                    style: mapStyle, // style URL
                    center: latLng, // starting position as [lng, lat]
                    zoom: initZoom
                });
            } else {
                map = new mapboxgl.Map({
                    container: mapDiv, // HTML container id
                    style: mapStyle, // style URL
                    center: latLng, // starting position as [lng, lat]
                    zoom: initZoom
                });
                scale = new mapboxgl.ScaleControl({maxWidth: 120, unit: 'imperial'});
                map.addControl(scale, 'bottom-right');
                var nav = new mapboxgl.NavigationControl();
                map.addControl(nav, 'bottom-right');
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
            }

            // Add geolocate control to the map.
            map_position = new mapboxgl.GeolocateControl({
                positionOptions: {enableHighAccuracy: true},
                trackUserLocation: true
            });
            map.addControl(map_position, 'bottom-right');

            //Add fullscreen control to the map.
            map_fullscreen = new mapboxgl.FullscreenControl();
            map.addControl(map_fullscreen, 'bottom-right');

            map.on('click', function (e) {
                if (isTouchDevice()) {
                    hideMapTooltip();
                    hidePhotoTooltip();
                    hideChallengeTooltip();
                    $('#gm-custom-mapdropdown').hide();
                    $('#gm-custom-maplegend-dropdown').hide();
                } else {
                    hideMapTooltip();
                    hidePhotoTooltip();
                    hideChallengeTooltip();
                    $('#gm-custom-mapdropdown').hide();
                    $('#gm-custom-maplegend-dropdown').hide();
                }
            });

            map.on('dragstart', function (e) {
                if (isTouchDevice()) {
                    hideMapTooltip();
                    hidePhotoTooltip();
                    hideChallengeTooltip();
                    $('#gm-custom-mapdropdown').hide();
                    $('#gm-custom-maplegend-dropdown').hide();
                } else {
                    hideMapTooltip();
                    hidePhotoTooltip();
                    hideChallengeTooltip();
                    $('#gm-custom-mapdropdown').hide();
                    $('#gm-custom-maplegend-dropdown').hide();
                }
            });

            map.on('moveend', (eventData) => {
                if (eventData.originalEvent !== undefined) { // We make sure an event triggered this callback.
                    throttleLoadPeaks();
                }
            });

            map.on('load', function () {
                if (initLoad) {
                    loadPeaks();
                }
                {% if peak.is_usa %}
                    toggleMapUnits('feet');
                    scale.setUnit('imperial');
                {% else %}
                    toggleMapUnits('meters');
                    scale.setUnit('metric');
                {% endif %}
            });

            var checking_style_status = false;
            map.on('styledata', function (e) {
                if (checking_style_status) {
                    // If already checking style status, bail out
                    // (important because styledata event may fire multiple times)
                    return;
                } else {
                    checking_style_status = true;
                    check_style_status();
                }
            });

            function check_style_status() {
                if (map.isStyleLoaded()) {
                    checking_style_status = false;
                    map.fire('map_style_finally_loaded');
                } else {
                    // If not yet loaded, repeat check after delay:
                    setTimeout(function () {
                        check_style_status();
                    }, 200);
                    return;
                }
            }

            map.on('map_style_finally_loaded', function (e) {
                //addExtraMapLayers();
            });

        }

        function setMapControls() {
            var check = checkIfMapboxStyleIsLoaded();
            if (!check) {
                // It's not safe to manipulate layers yet, so wait 200ms and then check again
                setTimeout(function () {
                    setMapControls();
                }, 200);
                return;
            }
            // Whew, now it's safe to manipulate layers!
            {% if peak.is_usa %}
                toggleMapUnits('feet');
                scale.setUnit('imperial');
            {% else %}
                toggleMapUnits('meters');
                scale.setUnit('metric');
            {% endif %}
        }

        function addExtraMapLayers(type = '') {
            var check = checkIfMapboxStyleIsLoaded();
            if (!check) {
                // It's not safe to manipulate layers yet, so wait 200ms and then check again
                setTimeout(function () {
                    addExtraMapLayers(type);
                }, 200);
                return;
            }
            // Whew, now it's safe to manipulate layers!
            // No extra map layers necessary

            if (type == 'satellite_3d') {
                try {
                    map.setTerrain(null);
                } catch (e) {
                    //pass
                }
                // add the DEM source as a terrain layer with exaggerated height
                try {
                    map.setTerrain({'source': 'mapbox-dem', 'exaggeration': 1.2});
                } catch (e) {
                    map.addSource('mapbox-dem', {
                        'type': 'raster-dem',
                        'url': 'mapbox://mapbox.mapbox-terrain-dem-v1',
                        'tileSize': 512,
                        'maxzoom': 14
                    });
                    map.setTerrain({'source': 'mapbox-dem', 'exaggeration': 1.2});
                }
                // add the sky layer
                try {
                    map.addLayer({
                        'id': 'sky',
                        'type': 'sky',
                        'paint': {
                            'sky-type': 'atmosphere',
                            'sky-atmosphere-sun': [0.0, 0.0],
                            'sky-atmosphere-sun-intensity': 15
                        }
                    });
                } catch (e) {
                    //pass
                }
                // enable map rotation using right click + drag
                map.dragRotate.enable();
                // enable map rotation using touch rotation gesture
                map.touchZoomRotate.enableRotation();
                // set pitch to 80 degrees
                //map.setPitch(80);
                map.easeTo({pitch: 80});

            } else {

                //switch to 2d mode
                map.setTerrain(null);
                // disable map rotation using right click + drag
                map.dragRotate.disable();
                // disable map rotation using touch rotation gesture
                map.touchZoomRotate.disableRotation();
                //set pitch back to 0
                map.setPitch(0);
                //map.easeTo({pitch: 0});
                //set bearing back to 0
                map.setBearing(0);

            }
        }

        $(document).ready(function () {

            var showPhotos = readCookie('map_photos');
            if (showPhotos == 'true') {
                $('#map-photos-toggle').prop("checked", true);
            } else if (showPhotos === null) {
                createCookie('map_photos', true, 365);
                $('#map-photos-toggle').prop("checked", true);
            } else {
                $('#map-photos-toggle').prop("checked", false);
            }

            var showClassics = readCookie('map_classics');
            if (showClassics == 'true') {
                $('#map-classics-toggle').prop("checked", true);
            } else if (showClassics === null) {
                createCookie('map_classics', true, 365);
                $('#map-classics-toggle').prop("checked", true);
            } else {
                $('#map-classics-toggle').prop("checked", false);
            }

            var showChallenges = readCookie('map_challenges');
            if (showChallenges == 'true') {
                $('#map-challenges-toggle').prop("checked", true);
            } else if (showChallenges === null) {
                createCookie('map_challenges', true, 365);
                $('#map-challenges-toggle').prop("checked", true);
            } else {
                $('#map-challenges-toggle').prop("checked", false);
            }

            var showFirstAscents = readCookie('map_firstascents');
            if (showFirstAscents == 'true') {
                $('#map-firstascents-toggle').prop("checked", true);
            } else if (showFirstAscents === null) {
                createCookie('map_firstascents', true, 365);
                $('#map-firstascents-toggle').prop("checked", true);
            } else {
                $('#map-firstascents-toggle').prop("checked", false);
            }

            var showStewards = readCookie('map_stewards');
            if (showStewards == 'true') {
                $('#map-stewards-toggle').prop("checked", true);
            } else if (showStewards === null) {
                createCookie('map_stewards', true, 365);
                $('#map-stewards-toggle').prop("checked", true);
            } else {
                $('#map-stewards-toggle').prop("checked", false);
            }

            var showKOM = readCookie('map_kom');
            if (showKOM == 'true') {
                $('#map-kom-toggle').prop("checked", true);
            } else if (showKOM === null) {
                createCookie('map_kom', true, 365);
                $('#map-kom-toggle').prop("checked", true);
            } else {
                $('#map-kom-toggle').prop("checked", false);
            }

            var showUnclimbed = readCookie('map_unclimbed');
            if (showUnclimbed == 'true') {
                $('#map-unclimbed-toggle').prop("checked", true);
            } else if (showUnclimbed === null) {
                createCookie('map_unclimbed', true, 365);
                $('#map-unclimbed-toggle').prop("checked", true);
            } else {
                $('#map-unclimbed-toggle').prop("checked", false);
            }

            var showAttempts = readCookie('map_attempts');
            if (showAttempts == 'true') {
                $('#map-attempts-toggle').prop("checked", true);
            } else if (showAttempts === null) {
                createCookie('map_attempts', true, 365);
                $('#map-attempts-toggle').prop("checked", true);
            } else {
                $('#map-attempts-toggle').prop("checked", false);
            }

            var showSummits = readCookie('map_summits');
            if (showSummits == 'true') {
                $('#map-summits-toggle').prop("checked", true);
            } else if (showSummits === null) {
                createCookie('map_summits', true, 365);
                $('#map-summits-toggle').prop("checked", true);
            } else {
                $('#map-summits-toggle').prop("checked", false);
            }

            $('#close-gallery').on('click', function (event) {
                $('#blueimp-gallery').data('gallery').close();
            });

            $('#blueimp-gallery').on('open', function (event) {
                $('body,html').css('overflow', 'visible');
                var gallery = $('#blueimp-gallery').data('gallery');
                //gallery.options['enableKeyboardNavigation'] = false;
                var index = gallery.index;
                var caption = decodeDoubleQuotes(gallery.list[index].getAttribute('data-description')),
                    username = gallery.list[index].getAttribute('data-user'),
                    credit = gallery.list[index].getAttribute('data-credit'),
                    createdate = gallery.list[index].getAttribute('data-createdate'),
                    peak_slug = gallery.list[index].getAttribute('data-peak-slug'),
                    summit_log_id = gallery.list[index].getAttribute('data-summit-log-id'),
                    photo_url = gallery.list[index].getAttribute('data-photo-url'),
                    caption_node = gallery.container.find('.description-text-caption');
                username_node = gallery.container.find('.description-text-user');
                caption_node.empty();
                username_node.empty();
                if (caption) {
                    caption_node[0].appendChild(document.createTextNode(caption));
                }
                if (username) {
                    var newdiv = document.createElement('div');
                    if (credit) {
                        newdiv.innerHTML = '<a style="color: #fff;" target="_blank" href="' + photo_url + '/"><i style="margin-right: 5px;" class="fa fa-camera" aria-hidden="true"></i> ' + username + ' ' + credit + '</a>&nbsp;&bull;&nbsp;<time class="timeago" datetime="' + createdate + 'T00:00:00">' + createdate + '</time>';
                    } else {
                        if (summit_log_id != '0') {
                            newdiv.innerHTML = '<a style="color: #fff;" href="/members/' + username + '/"><i style="margin-right: 5px;" class="fa fa-camera" aria-hidden="true"></i> ' + username + '</a>&nbsp;&bull;&nbsp;<a style="color: #fff;" href="/' + peak_slug + '/summits/' + summit_log_id + '/"><time class="timeago" datetime="' + createdate + 'T00:00:00">' + createdate + '</time>';
                        } else {
                            newdiv.innerHTML = '<a style="color: #fff;" href="/members/' + username + '/"><i style="margin-right: 5px;" class="fa fa-camera" aria-hidden="true"></i> ' + username + '</a>&nbsp;&bull;&nbsp;<time class="timeago" datetime="' + createdate + 'T00:00:00">' + createdate + '</time>';
                        }
                    }
                    username_node[0].appendChild(newdiv);
                }
                $("time.timeago").timeago();
            });

            $("#blueimp-gallery").on('slide', function (event, index, slide) {
                var gallery = $('#blueimp-gallery').data('gallery');
                //gallery.options['enableKeyboardNavigation'] = false;
                var index = gallery.index;
                var caption = decodeDoubleQuotes(gallery.list[index].getAttribute('data-description')),
                    username = gallery.list[index].getAttribute('data-user'),
                    credit = gallery.list[index].getAttribute('data-credit'),
                    createdate = gallery.list[index].getAttribute('data-createdate'),
                    peak_slug = gallery.list[index].getAttribute('data-peak-slug'),
                    summit_log_id = gallery.list[index].getAttribute('data-summit-log-id'),
                    photo_url = gallery.list[index].getAttribute('data-photo-url'),
                    caption_node = gallery.container.find('.description-text-caption');
                username_node = gallery.container.find('.description-text-user');
                caption_node.empty();
                username_node.empty();
                if (caption) {
                    caption_node[0].appendChild(document.createTextNode(caption));
                }
                if (username) {
                    var newdiv = document.createElement('div');
                    if (credit) {
                        newdiv.innerHTML = '<a style="color: #fff;" target="_blank" href="' + photo_url + '/"><i style="margin-right: 5px;" class="fa fa-camera" aria-hidden="true"></i> ' + username + ' ' + credit + '</a>&nbsp;&bull;&nbsp;<time class="timeago" datetime="' + createdate + 'T00:00:00">' + createdate + '</time>';
                    } else {
                        if (summit_log_id != '0') {
                            newdiv.innerHTML = '<a style="color: #fff;" href="/members/' + username + '/"><i style="margin-right: 5px;" class="fa fa-camera" aria-hidden="true"></i> ' + username + '</a>&nbsp;&bull;&nbsp;<a style="color: #fff;" href="/' + peak_slug + '/summits/' + summit_log_id + '/"><time class="timeago" datetime="' + createdate + 'T00:00:00">' + createdate + '</time>';
                        } else {
                            newdiv.innerHTML = '<a style="color: #fff;" href="/members/' + username + '/"><i style="margin-right: 5px;" class="fa fa-camera" aria-hidden="true"></i> ' + username + '</a>&nbsp;&bull;&nbsp;<time class="timeago" datetime="' + createdate + 'T00:00:00">' + createdate + '</time>';
                        }
                    }
                    username_node[0].appendChild(newdiv);
                }
                $("time.timeago").timeago();

            });

            $("#map-photos-toggle").change(function () {
                if ($(this).is(':checked')) {
                    $('.gallery-link').show();
                    createCookie('map_photos', true, 365);
                } else {
                    $('.gallery-link').hide();
                    createCookie('map_photos', false, 365);
                }
            });

            $("#map-classics-toggle").change(function () {
                if ($(this).is(':checked')) {
                    $('.mapboxgl-marker[data-classic=true]').addClass('marker_icon_classic');
                    createCookie('map_classics', true, 365);
                } else {
                    $('.mapboxgl-marker[data-classic=true]').removeClass('marker_icon_classic');
                    createCookie('map_classics', false, 365);
                }
            });

            $("#map-challenges-toggle").change(function () {
                if ($(this).is(':checked')) {
                    $('.marker-challenge-icon').show();
                    createCookie('map_challenges', true, 365);
                } else {
                    $('.marker-challenge-icon').hide();
                    createCookie('map_challenges', false, 365);
                }
            });

            $("#map-stewards-toggle").change(function () {
                if ($(this).is(':checked')) {
                    $('.mapboxgl-marker[data-steward=true]').addClass('marker_icon_steward');
                    createCookie('map_stewards', true, 365);
                } else {
                    $('.mapboxgl-marker[data-steward=true]').removeClass('marker_icon_steward');
                    createCookie('map_stewards', false, 365);
                }
            });

            $("#map-kom-toggle").change(function () {
                if ($(this).is(':checked')) {
                    $('.mapboxgl-marker[data-kom=true]').addClass('marker_icon_kom');
                    createCookie('map_kom', true, 365);
                } else {
                    $('.mapboxgl-marker[data-kom=true]').removeClass('marker_icon_kom');
                    createCookie('map_kom', false, 365);
                }
            });

            $("#map-firstascents-toggle").change(function () {
                if ($(this).is(':checked')) {
                    $('.mapboxgl-marker[data-firstascent=true]').addClass('marker_icon_firstascent');
                    createCookie('map_firstascents', true, 365);
                } else {
                    $('.mapboxgl-marker[data-firstascent=true]').removeClass('marker_icon_firstascent');
                    createCookie('map_firstascents', false, 365);
                }
            });

            $("#map-unclimbed-toggle").change(function () {
                if ($(this).is(':checked')) {
                    $('.marker_icon').show();
                    createCookie('map_unclimbed', true, 365);
                } else {
                    $('.marker_icon').hide();
                    createCookie('map_unclimbed', false, 365);
                }
            });

            $("#map-attempts-toggle").change(function () {
                if ($(this).is(':checked')) {
                    $('.marker_icon_yellow').show();
                    createCookie('map_attempts', true, 365);
                } else {
                    $('.marker_icon_yellow').hide();
                    createCookie('map_attempts', false, 365);
                }
            });

            $("#map-summits-toggle").change(function () {
                if ($(this).is(':checked')) {
                    $('.marker_icon_green').show();
                    createCookie('map_summits', true, 365);
                } else {
                    $('.marker_icon_green').hide();
                    createCookie('map_summits', false, 365);
                }
            });

            $('#peaks-map').css({left: '0px'});

            var vars = [], hash, lat, lng;
            var q = document.URL.split('#')[1];
            if (q != undefined) {
                q = q.split('&');
                for (var i = 0; i < q.length; i++) {
                    hash = q[i].split('=');
                    vars.push(hash[1]);
                    vars[hash[0]] = hash[1];
                }
            }

            if (vars['lat'] != undefined) {
                lat = vars['lat'];
            } else {
                lat = '';
            }

            if (vars['lng'] != undefined) {
                lng = vars['lng'];
            } else {
                lng = '';
            }

            var window_width = $(window).width();
            var height_pad = 120;
            var width_pad = 0;
            if (window_width < 1024) {
                height_pad = 170;
                width_pad = 0;
            }
            if (window_width < 768) {
                height_pad = 120;
                width_pad = 0;
            }
            var max_height = $(window).height() - height_pad;
            var max_width = $(window).width() - width_pad;
            $("div#map-canvas").height(max_height);
            $("div#map-canvas").width(max_width);
            $("#peak-search").height($(window).height());
            $("#explore .leftCol").height($("#explore .rightCol").height());
            $("div.peak_list_cont").css("margin-bottom", "0");
            initialize();
            init = true;

            //switch map units
            $("#gm-custom-mapunits").click(function () {
                //toggle map units
                if ($('#gm-custom-mapunitsbutton-label-meters').hasClass('gm-custom-mapunits-selected')) {
                    toggleMapUnits('feet');
                } else {
                    toggleMapUnits('meters');
                }
                loadPeaks();
            });

            $('#map-canvas').mousemove(function (e) {
                var offset = $(this).offset();
                pageX = e.pageX;
                pageY = e.pageY;
                mapX = (e.pageX - offset.left);
                mapY = (e.pageY - offset.top);
            });

            $('#map-canvas').on('touchstart', function (e) {
                var offset = $(this).offset();
                pageX = e.originalEvent.touches[0].pageX;
                pageY = e.originalEvent.touches[0].pageY;
                mapX = (pageX - offset.left);
                mapY = (pageY - offset.top);
            });

            $(window).resize(function () {
                var window_width = $(window).width();
                if (window_width < 1024) {
                    $('#peaks-map').css({left: '0px'});
                } else {
                    if ($('#peak-search').css('left') == '0px') {
                        $('#peaks-map').css({left: '0px'});
                    } else {
                        $('#peaks-map').css({left: '0px'});
                    }
                }
                var height_pad = 120;
                var width_pad = 240;
                if ($('#peak-search').css('left') != '0px') {
                    width_pad = 0;
                }
                if (window_width < 1024) {
                    height_pad = 120;
                    width_pad = 0;
                }
                if (window_width < 768) {
                    height_pad = 100;
                    width_pad = 0;
                }
                var max_height = $(window).height() - height_pad;
                var max_width = $(window).width() - width_pad;
                $("div#map-canvas").height(max_height);
                $("div#map-canvas").width(max_width);
                $("#peak-search").height($(window).height());
                map.resize();
            });

            $('#peak-search-mobile').on('click', function () {
                $('#peak-search').addClass('modal fade right');
                $('#peak-search').attr('style', 'left: auto; right: 0px; margin-top: 0px; width: 240px;');
                $('#peak-search').removeClass('hidden-xs');
                $('#peak-search').removeClass('hidden-sm');
                $('#peak-search').modal('toggle');
                $('#mobile-collapse-nav').show();
                var height = $(window).height();
                var width = $(window).width() - 240;
                $('#mobile-collapse-nav').width(width);
                $('#mobile-collapse-nav').height(height);
            });

            $('#peak-search-layers').on('click', function (e) {
                $('#gm-custom-maplegend-dropdown').hide();
                $('#gm-custom-mapdropdown').toggle();
                $('#gm-custom-mapdropdown').css('border-top-right-radius', '8px');
                $('#gm-custom-mapdropdown').css('border-top-left-radius', '8px');
                $('#gm-custom-mapoption-terrain').css('border-top-right-radius', '8px');
                $('#gm-custom-mapoption-terrain').css('border-top-left-radius', '8px');
                var height = $(window).height();
                var height_pad = (height - 480) / 2;
                $('#gm-custom-mapdropdown').css('top', height_pad);
                e.stopPropagation();
            });

            $('#peak-search-legend').on('click', function (e) {
                $('#gm-custom-maplegend-dropdown').toggle();
                var width = $(window).width();
                var height = $(window).height();
                var height_pad = (height - 435) / 2;
                if (width <= 400) {
                    var height_pad = (height - 585) / 2;
                }
                $('#gm-custom-maplegend-dropdown').css('top', height_pad);
                e.stopPropagation();
            });

            $('#gm-custom-maplegend-close').on('click', function (e) {
                $('#gm-custom-maplegend-dropdown').hide();
                e.stopPropagation();
            });

            $('#marker-tooltip').on('click', function () {
                window.location = $(this).data('url');
            });

            $('#challenge-tooltip').on('click', function () {
                window.location = $(this).data('url');
            });

            $('#gm-custom-mapbutton').on('mouseenter', function () {
                $('#gm-custom-mapdropdown').show();
                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '0px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '0px');
            });

            $('#gm-custom-mapbutton').on('mouseleave', function () {
                $('#gm-custom-mapdropdown').hide();
                $('#gm-custom-mapbutton').css('border-bottom-right-radius', '8px');
                $('#gm-custom-mapbutton').css('border-bottom-left-radius', '8px');
            });

            $('#gm-custom-mapbutton').on('touchstart', function () {
                $('#gm-custom-mapdropdown').toggle();
            });

            $('#unfollow-{{ bagger.id }}').hover(
                function () {
                    $(this).removeClass('unfollowButton').addClass('unfollowButtonHoverProfile').text('Unfollow').css('background-color', '#f24100').css('border-color', '#f24100');
                },
                function () {
                    $(this).removeClass('unfollowButtonHoverProfile').addClass('unfollowButton').text('You are following').css('background-color', '#00b330').css('border-color', '#00b330');
                });

            $("#unfollow-{{ bagger.id }}").on('click', function () {
                var btn = $("#unfollow-{{ bagger.id }}");
                var follow_btn = $("#follow-{{ bagger.id }}");
                btn.html('<i class="fa fa-spinner fa-spin"></i>');
                $.post('/accounts/unfollow/{{ bagger.id }}/', function (data) {
                    if (data == "True") {
                        follow_btn.html('Follow');
                        $("#unfollow-{{ bagger.id }}").hide();
                        $("#follow-{{ bagger.id }}").show();
                        $("#mobile-unfollow-{{ bagger.id }}").hide();
                        $("#mobile-follow-{{ bagger.id }}").show();
                    }
                });
            });

            $("#follow-{{ bagger.id }}").on('click', function () {
                var btn = $("#follow-{{ bagger.id }}");
                var unfollow_btn = $("#unfollow-{{ bagger.id }}");
                btn.html('<i class="fa fa-spinner fa-spin"></i>');
                $.post('/accounts/follow/{{ bagger.id }}/', function (data) {
                    if (data == "True") {
                        unfollow_btn.html('You are following');
                        $("#follow-{{ bagger.id }}").hide();
                        $("#unfollow-{{ bagger.id }}").show();
                        $("#mobile-follow-{{ bagger.id }}").hide();
                        $("#mobile-unfollow-{{ bagger.id }}").show();
                    }
                });
            });

            $("#mobile-unfollow-{{ bagger.id }}").on('click', function () {
                var btn = $("#mobile-unfollow-{{ bagger.id }}");
                var follow_btn = $("#mobile-follow-{{ bagger.id }}");
                btn.html('<i class="fa fa-spinner fa-spin"></i>');
                $.post('/accounts/unfollow/{{ bagger.id }}/', function (data) {
                    if (data == "True") {
                        follow_btn.html('follow');
                        $("#mobile-unfollow-{{ bagger.id }}").hide();
                        $("#mobile-follow-{{ bagger.id }}").show();
                        $("#unfollow-{{ bagger.id }}").hide();
                        $("#follow-{{ bagger.id }}").show();
                    }
                });
            });

            $("#mobile-follow-{{ bagger.id }}").on('click', function () {
                var btn = $("#mobile-follow-{{ bagger.id }}");
                var unfollow_btn = $("#mobile-unfollow-{{ bagger.id }}");
                btn.html('<i class="fa fa-spinner fa-spin"></i>');
                $.post('/accounts/follow/{{ bagger.id }}/', function (data) {
                    if (data == "True") {
                        unfollow_btn.html('following');
                        $("#mobile-follow-{{ bagger.id }}").hide();
                        $("#mobile-unfollow-{{ bagger.id }}").show();
                        $("#follow-{{ bagger.id }}").hide();
                        $("#unfollow-{{ bagger.id }}").show();
                    }
                });
            });

        });

        function getUrlVars() {
            var vars = [], hash;
            var hashes = window.location.href.slice(window.location.href.indexOf('?') + 1).split('&');
            for (var i = 0; i < hashes.length; i++) {
                hash = hashes[i].split('=');
                vars.push(hash[0]);
                vars[hash[0]] = hash[1];
            }
            return vars;
        }

        function fix_item_location(id, point) {
            $.post('{% url "fix_item_location" %}', {id: id, lat: point.lat, long: point.lng},
                function (data) {
                    var output = $("#message_map");
                    var output_div = $("#message_map_div");
                    output.html(data);
                    output_div.show();
                    setTimeout(function () {
                        $("#message_map_div").hide();
                    }, 10000)
                }
            );
        }

        function delete_peak_from_map(id) {
            $.post('{% url "delete_peak_from_map" %}', {id: id},
                function (data) {
                    var output = $("#message_map");
                    var output_div = $("#message_map_div");
                    output.html(data);
                    output_div.show();
                    setTimeout(function () {
                        $("#message_map_div").hide();
                    }, 10000)
                }
            );
        }

        function check_is_in(marker) {
            return map.getBounds().contains(marker.getPosition());
        }

        function delete_out_markers() {
            if (markersArray) {
                for (i in markersArray) {
                    if (!check_is_in(markersArray[i])) {
                        markersArray[i].remove();
                        markersArray.splice(i, 1);
                    }
                }
            }
        }

        function deletehighest() {
            if (markersArray) {
                for (i in markersArray) {
                    if (markersArray[i].properties.iconstyle == 'marker_icon_redgreen' || markersArray[i].properties.iconstyle == 'marker_icon_red') {
                        markersArray[i].remove();
                        markersArray.splice(i, 1);
                    }
                }
            }
        }

        function limit_number_of_markers(limit) {
            if (markersArray.length > limit) {
                for (i = markersArray.length - 1; i >= limit; i--) {
                    markersArray[i].remove();
                    markersArray.splice(i, 1);
                }
            }
        }

        function elevation_range(data) {
            if (markersArray) {
                for (i = markersArray.length - 1; i >= 0; i--) {
                    var inrange = false;
                    $.each(data, function (k, v) {
                        var latLng1 = new mapboxgl.LngLat(v.lng, v.lat);
                        if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng1)) {
                            inrange = true;
                        }
                    });
                    if (!inrange) {
                        markersArray[i].remove();
                        markersArray.splice(i, 1);
                    }
                }
            }
        }

        function delete_old_markers(data) {
            if (markersArray) {
                for (i = markersArray.length - 1; i >= 0; i--) {
                    var inrange = false;
                    $.each(data, function (k, v) {
                        var latLng1 = new mapboxgl.LngLat(v.lng, v.lat);
                        if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng1)) {
                            inrange = true;
                        }
                    });
                    if (!inrange) {
                        markersArray[i].remove();
                        markersArray.splice(i, 1);
                    }
                }
            }
        }

        function delete_old_photo_markers(data) {
            if (photoMarkersArray) {
                for (i = photoMarkersArray.length - 1; i >= 0; i--) {
                    var inrange = false;
                    $.each(data, function (k, v) {
                        var latLng1 = new mapboxgl.LngLat(v.photo_lng, v.photo_lat);
                        if (fromLatLngToString(photoMarkersArray[i].getLngLat()) == fromLatLngToString(latLng1)) {
                            inrange = true;
                        }
                    });
                    if (!inrange) {
                        photoMarkersArray[i].remove();
                        photoMarkersArray.splice(i, 1);
                    }
                }
            }
        }

        function delete_all_photo_markers() {
            if (photoMarkersArray) {
                for (i = photoMarkersArray.length - 1; i >= 0; i--) {
                    photoMarkersArray[i].remove();
                    photoMarkersArray.splice(i, 1);
                }
            }
        }

        function delete_old_challenge_markers(data) {
            if (challengeMarkersArray) {
                for (i = challengeMarkersArray.length - 1; i >= 0; i--) {
                    var inrange = false;
                    $.each(data, function (k, v) {
                        var latLng1 = new mapboxgl.LngLat(v.challenge_lng, v.challenge_lat);
                        if (fromLatLngToString(challengeMarkersArray[i].getLngLat()) == fromLatLngToString(latLng1)) {
                            inrange = true;
                        }
                    });
                    if (!inrange) {
                        challengeMarkersArray[i].remove();
                        challengeMarkersArray.splice(i, 1);
                    }
                }
            }
        }

        function delete_all_challenge_markers() {
            if (challengeMarkersArray) {
                for (i = challengeMarkersArray.length - 1; i >= 0; i--) {
                    challengeMarkersArray[i].remove();
                    challengeMarkersArray.splice(i, 1);
                }
            }
        }

        function fromLatLngToString(latLng) {
            return latLng.lat + ',' + latLng.lng;
        }

    </script>

    {% include "mapbox/map_layers.html" %}

{% endblock %}

{% block gallery %}
    <div id="blueimp-gallery" class="blueimp-gallery blueimp-gallery-controls" data-use-bootstrap-modal="false"
         data-slideshow-interval="4000">
        <!-- The container for the modal slides -->
        <div class="slides"></div>
        <!-- Controls for the borderless lightbox -->
        <h3 class="title"></h3>
        <div class="description">
            <div class="description-text">
                <div class="description-text-caption"></div>
                <div class="description-text-user"></div>
            </div>
        </div>
        <a class="prev">‹</a>
        <a class="next">›</a>
        <a class="close">×</a>
        <ol class="indicator"></ol>
        <!-- The modal dialog, which will be used to wrap the lightbox content -->
        <div class="modal fade">
            <div class="modal-dialog" style="width: 80%;">
                <div class="modal-content">
                    <div class="modal-header">
                        <button style="color: #fff;" type="button" class="close" aria-hidden="true">&times;</button>
                        <h4 class="modal-title"></h4>
                    </div>
                    <div class="modal-body next"></div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default pull-left prev">
                            <i class="glyphicon glyphicon-chevron-left"></i>
                            Previous
                        </button>
                        <button type="button" class="btn btn-primary next">
                            Next
                            <i class="glyphicon glyphicon-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="hidden-sm hidden-md hidden-lg">
        <div class="loading" style="display: none;">Loading&#8230;</div>
    </div>
{% endblock %}