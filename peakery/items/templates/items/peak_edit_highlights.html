{% extends "base.html" %}

{% load avatar_tags %}
{% load item_tags %}
{% load cache %}
{% load humanize %}

{% block title %}{{ peak.name }} - {{ peak.get_ubication_names_title }}{% endblock %}
{% block titlemeta %}{{ peak.name }} - {{ peak.get_ubication_names_title }} -  {% endblock %}
{% block description %}{{ peak.meta_description|striptags|truncatewords:160 }}{% endblock %}
{#{% block image_rel %}{% thumbnail peak.thumbnail "120x120" as im %}{{ im.url }}{% endthumbnail %}{% endblock %}#}
{% block image_rel %}thumbnail peak.thumbnail 120x120{% endblock %}

{% block meta_facebook %}
    <meta property="og:title" content="{{ peak.name }}"/>
    <meta property="og:site_name" content="peakery.com"/>
    <meta property="og:type" content="article"/>
    <meta property="og:url" content="{{ peak.get_absolute_url }}"/>
{#    <meta property="og:image" content="{% thumbnail peak.get_thumbnail "120x120" as im %}{% endthumbnail %}"/>#}
    <meta property="og:image" content="thumbnail peak.get_thumbnail 120x120"/>
    <meta property="og:description" content="{{ peak.meta_description|striptags|truncatewords:160 }}"/>
{% endblock %}

{% block content %}

    <div class="container">
        <div class="row main-header-row">
            <div class="col-md-12" style="height: 80px; background-color: #fff; line-height: 80px;"><span style="font-size: 24px; font-weight: 600;">
                <div id="breadcrumbs">
                    <ul>
                        <li><span style="font-size: 24px; font-weight: 600;">{{ peak.name }}</span></li>
                    </ul>
                    <div class="pull-right" style="margin-top: -10px;">
                        <a style="{{ subnav_info_style }}" class="main-header-sub-links" href="/{{ peak.slug_new_text }}">Info</a><a style="{{ subnav_peaks_style }}" class="main-header-sub-links" href="/{{ peak.slug_new_text }}/summits/">Summits</a><a style="{{ subnav_map_style }}" class="main-header-sub-links" href="/{{ peak.slug_new_text }}/routes/">Routes</a>
                    </div>
                </div>
            </span></div>
        </div>
        <div class="row background-row">
            <div class="sp-60"></div>
        </div>
        <div class="row">
            <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 70px; padding-top: 5px;"><h3>Edit Highlights</h3></div>
        </div>
        <div class="row" style="background-color: #fff; border-top: 1px solid #c0c0c0; padding-top: 20px; padding-left: 10px;">
            <div class="col-md-12">
                <div class="peak_desc">
                    <div class="description">
                        <form class="edithighlights_form" method="POST" action="/peaks/edit_highlights/{{ peak.id }}/">
                        <fieldset>
                        {% if highlights %}
                            {% for h in highlights %}
                                <div>
                                    <input type="text" name="peak-highlight-{{ h.id }}" id="peak-highlight-{{ h.id }}" style="width: 100%; font-size: 18px; font-weight: 700; padding-left: 10px; margin-bottom: 20px;" value="{{ h.highlight }}" placeholder="add a peak highlight">
                                </div>
                            {% endfor %}
                            <div>
                                <input type="text" name="peak-highlight-new" id="peak-highlight-new" style="width: 100%; font-size: 18px; font-weight: 700; padding-left: 10px; margin-bottom: 20px;" placeholder="add a new peak highlight">
                            </div>
                        {% else %}
                            <div>
                                <input type="text" name="peak-highlight-new" id="peak-highlight-new" style="width: 100%; font-size: 18px; font-weight: 700; padding-left: 10px; margin-bottom: 20px;" placeholder="add a new peak highlight">
                            </div>
                        {% endif %}
                        </fieldset>
                        <input style="width: 210px; height: 70px; font-size: 18px;" class="btn set2 input" id="edit-highlight-save" type="submit" value="Save Highlights" />
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div class="row background-row">
            <div class="sp-60"></div>
        </div>
    </div>

{% endblock %}
