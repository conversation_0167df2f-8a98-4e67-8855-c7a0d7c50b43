{% extends "base.html" %}
{% load static %}
{% load avatar_tags item_tags favorite_tags truncatechars verbatim %}
{% load json_filters %}
{% load humanize %}

{% block title %}{{ peak.get_peakname_title }} climbs - {{ peak.get_peaklocation_title }}{% endblock %}
{% block titlemeta %}{{ peak.get_peakname_title }} climbs - {{ peak.get_peaklocation_title }}{% endblock %}
{% block description %}{{ peak.get_peak_summits_meta_description }}{% endblock %}
{% block image_rel %}thumbnail peak.thumbnail 120x120{% endblock %}

{% block extrajs %}
    <script type="text/javascript">
    {% include "mapbox/polyline.js" %}
    </script>
    <script src="{% static 'js/GPXParser.js'%}"></script>
{% endblock %}

{% block meta_facebook %}
    <meta property="og:title" content="{{ peak.name }}"/>
    <meta property="og:site_name" content="peakery.com"/>
    <meta property="og:type" content="article"/>
    <meta property="og:url" content="{{ peak.get_absolute_url }}"/>
    <meta property="og:image" content="thumbnail peak.get_thumbnail 120x120"/>
    <meta property="og:description" content="{{ peak.get_peak_summits_meta_description }}"/>
{% endblock %}

{% block js_globals %}
    var peak_id = {{ peak.id }};
    var peakObject = '{"id": "{{ peak.id }}", "name": {{ peak.name|jsonify|escape_single_quotes }}, "slug": "{{ peak.slug_new_text }}", "is_classic": "{{ peak.is_classic }}", "lat": "{{ peak.lat }}", "lng": "{{ peak.long }}", "summit_count": "{{ peak.summitlog_count }}", "your_summits": "{{ your_summits_count }}", "your_attempts": "{{ your_attempts_count }}", "challenge_count": "{{ challenge_count }}", "elevation": "{{ peak.elevation }}", "prominence": "{{ peak.prominence }}", "thumbnail_url": "{{ peak.get_thumbnail_480 }}", "region": [{% if peak.region %}{% for r in peak.region.all %}{"region_id": "{{ r.id }}", "region_name": "{{ r.name }}", "region_slug": "{{ r.get_absolute_url }}", "country_name": "{{ r.country.name }}"}{% if not forloop.last %},{% endif %}{% endfor %}{% endif %}], "country": [{% if peak.country %}{% for c in peak.country.all %}{"country_id": "{{ c.id }}", "country_name": "{{ c.name }}", "country_slug": "{{ c.get_absolute_url }}"}{% if not forloop.last %},{% endif %}{% endfor %}{% endif %}]}';
{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row hidden-xs">
        <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 65px;"><span style="font-size: 24px; font-weight: 600;">
            <div id="breadcrumbs">
                <div class="pull-right hidden-xs">
                    <a style="{{ subnav_info_style }}" class="region-header-sub-links" href="/{{ peak.slug_new_text }}">Info</a>
                    <a style="{{ subnav_map_style }}" class="region-header-sub-links" href="/{{ peak.slug_new_text }}/map/">Map</a>
                    <a style="{{ subnav_summits_style }}" class="region-header-sub-links" href="/{{ peak.slug_new_text }}/summits/">Climbs</a>
                    {% if your_summit_count != 0 %}
                        <a class="youve-climbed" style="cursor: pointer; margin-left: 0px;">(You {{ your_summit_count }}x)</a>
                    {% endif %}
                </div>
                {% if alternate_names_list %}
                <div class="ellipsis" style="line-height: 55px;overflow: hidden;">
                    <h1 class="peak-title">{{ peak.name }}<br><div class="ellipsis" style="font-size: 11px;color: #999999;margin-top: -35px;font-weight: 100;">also known as {% for a in alternate_names_list %}{{ a.name }}{% if not forloop.last %}, {% endif %}{% endfor %}</div></h1>
                </div>
                {% else %}
                <div class="ellipsis" style="line-height: 70px; overflow: hidden;">
                     <h1 class="peak-title">{{ peak.name }}</h1>
                </div>
                {% endif %}
            </div>
        </span></div>
    </div>
{% endblock %}

{% block content %}

    <style type="text/css">

        .summit-card-header {
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }

        #navbar-link-peaks > a:hover {
            color: #ffffff;
        }

        .fa-rotate-45 {
            -webkit-transform: rotate(45deg);
            -moz-transform: rotate(45deg);
            -ms-transform: rotate(45deg);
            -o-transform: rotate(45deg);
            transform: rotate(45deg);
        }

        @media screen and (max-width: 767px) and (min-width: 1px) {
            #log-your-climb {
                margin-left: 0px;
            }
        }
        @media screen and (max-width: 1023px) and (min-width: 768px) {
            #log-your-climb {
                margin-left: 20px;
                padding: 15px 10px;
                width: 120px;
            }
            .peak-title {
                font-size: 16px;
                font-weight: 600;
            }
        }
        @media screen and (min-width: 1024px) {
            #log-your-climb {
                margin-left: 50px;
                width: 160px;
            }
            .peak-title {
                font-size: 20px;
                font-weight: 600;
            }
        }

        @media screen and (max-width: 767px) and (min-width: 1px) {
           #content-body {
               margin-top: 20px;
           }
           .content-pane {
               margin-top: 99px;
           }
           .peak-title {
                font-size: 12px;
                font-weight: 500;
            }
           .mobile-regions-subnav-fixed {
               margin-top: 50px;
           }
           #selectMobileRoutefilter {
               margin-top: 7px;
           }
           .peak-title {
                font-size: 12px;
                font-weight: 500;
            }
           .summit-card-peak-title {
                position: absolute;
                top: 0px;
                left: 16px;
                font-size: 13px !important;
                color: #333;
            }
            .summit-card-mobile-stats {
                position: absolute;
                top: 40px;
                left: 10px;
                margin-right: 10px;
                font-size: 12px !important;
                line-height: 20px;
            }
            .filter-bar {
                top: 120px;
            }
       }
       @media screen and (max-width: 1023px) and (min-width: 768px) {
           .content-pane {
               margin-top: 49px;
           }
           .mobile-regions-subnav-fixed {
               top: 121px;
           }
           #selectMobileRoutefilter {
               margin-top: 7px;
           }
           .peak-title {
                font-size: 16px;
                font-weight: 600;
            }
           .filter-bar {
               top: 141px;
           }
       }
        @media screen and (min-width: 1024px) {
            .content-pane {
               margin-top: 49px;
           }
            .mobile-regions-subnav-fixed {
               margin-top: 0px;
           }
            #selectMobileRoutefilter {
               margin-top: 0px;
           }
            .peak-title {
                font-size: 20px;
                font-weight: 600;
            }
        }
        #chart_div {cursor: pointer;}

        @media screen and (min-width: 768px) {
            .regions-subnav-fixed {
                top: 141px;
            }
        }

    </style>

<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>

<div class="container">
    <!-- Mobile header -->
    <div class="row sub-header-row hidden-lg hidden-md hidden-sm" style="height: 50px; position: fixed; width: 100%; z-index: 999;">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999; display: flex; justify-content: space-between;">
            <span><a style="{{ subnav_info_style }}" class="mobile-header-sub-links" href="/{{ peak.slug_new_text }}/">Info</a></span>
            {% if IS_MOBILE_APP_ACCESS == 'True' %}
            <span><a id="mobile-app-map-link" style="{{ subnav_map_style }}" class="mobile-header-sub-links">Map</a></span>
            {% else %}
            <span><a style="{{ subnav_map_style }}" class="mobile-header-sub-links" href="/{{ peak.slug_new_text }}/map/">Map</a></span>
            {% endif %}
            <span><a style="{{ subnav_summits_style }}" class="mobile-header-sub-links" href="/{{ peak.slug_new_text }}/summits/">Climbs</a>
            {% if your_summit_count != 0 %}
                <a class="mobile-header-sub-links youve-climbed" href="/{{ peak.slug_new_text }}/summits/#type=you" style="margin-left: 0px;">(You {{ your_summit_count }}x)</a></span>
                {% if IS_MOBILE_APP_ACCESS == 'True' %}
                <span><a id="log-your-climb" class="mobile-header-sub-links" href="javascript: Android.logClimb(peakObject);" style="color: #00b1f2;">Log climb</a></span>
                {% else %}
                <span><a id="log-your-climb" class="mobile-header-sub-links" href="{% url "log_climb_multi_simple" %}?peak={{ peak.id }}" style="color: #00b1f2;">Log climb</a></span>
                {% endif %}
            {% else %}
                {% if user.is_authenticated %}
                    {% if IS_MOBILE_APP_ACCESS == 'True' %}
                    </span><span><a id="log-your-climb" class="mobile-header-sub-links" href="javascript: Android.logClimb(peakObject);" style="color: #00b1f2;">Log climb</a></span>
                    {% else %}
                    </span><span><a id="log-your-climb" class="mobile-header-sub-links" href="{% url "log_climb_multi_simple" %}?peak={{ peak.id }}" style="color: #00b1f2;">Log climb</a></span>
                    {% endif %}
                {% else %}
                </span><span><a id="log-your-climb" class="mobile-header-sub-links" style="color: #00b1f2; cursor: pointer;" data-toggle="modal" data-target="#accounts-login">Log climb</a></span>
                {% endif %}
            {% endif %}
        </div>
    </div>
    <!-- End mobile header -->

    <div class="row sub-header-row hidden-xs hidden-sm regions-subnav-fixed" style="height: 50px; padding-right: 0px;">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999;">
            <span style="font-size: 14px; float: left;"><span id="summit-count" style="color: #f24100;"></span> <span id="summit-count-dscr" >climbs</span> by</span>
            <span class="select" id="selectMemberfilter" style="float: left; margin-top: -3px;">
                <button class="btn btn-default member-filter-button" style="margin-bottom: 0px; background-color: #f2f2f2; border-color: #f2f2f2; padding: 8px 8px; font-size: 15px;" data-toggle="dropdown">
                    <span id="memberfilter-title">all members</span>
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu memberfilter-list" style="height: 90px; left: inherit; overflow: auto; top: 70%;">
                    <li style="float: none;"><a class="memberfilter-item" data-value="all" href="#">all members</a></li>
                    <li style="float: none;"><a class="memberfilter-item" data-value="you" href="#">you</a></li>
                    <li style="float: none;"><a class="memberfilter-item" data-value="follow" href="#">members you follow</a></li>
                </ul>
            </span>
            <span style="font-size: 14px; float: left;">in</span>
            <span class="select" id="selectTimefilter" style="float: left; margin-top: -3px;">
                <button class="btn btn-default member-filter-button" style="margin-bottom: 0px; background-color: #f2f2f2; border-color: #f2f2f2; padding: 8px 8px; font-size: 15px;" data-toggle="dropdown">
                    <span id="timefilter-title">all months</span>
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu timefilter-list" style="height: 350px; left: inherit; overflow: auto; top: 70%;">
                    <li style="float: none;"><a id="timefilter-item-0" class="timefilter-item" data-value="" href="#">all months</a></li>
                    <li style="float: none;"><a id="timefilter-item-1" class="timefilter-item" data-value="1" href="#">January</a></li>
                    <li style="float: none;"><a id="timefilter-item-2" class="timefilter-item" data-value="2" href="#">February</a></li>
                    <li style="float: none;"><a id="timefilter-item-3" class="timefilter-item" data-value="3" href="#">March</a></li>
                    <li style="float: none;"><a id="timefilter-item-4" class="timefilter-item" data-value="4" href="#">April</a></li>
                    <li style="float: none;"><a id="timefilter-item-5" class="timefilter-item" data-value="5" href="#">May</a></li>
                    <li style="float: none;"><a id="timefilter-item-6" class="timefilter-item" data-value="6" href="#">June</a></li>
                    <li style="float: none;"><a id="timefilter-item-7" class="timefilter-item" data-value="7" href="#">July</a></li>
                    <li style="float: none;"><a id="timefilter-item-8" class="timefilter-item" data-value="8" href="#">August</a></li>
                    <li style="float: none;"><a id="timefilter-item-9" class="timefilter-item" data-value="9" href="#">September</a></li>
                    <li style="float: none;"><a id="timefilter-item-10" class="timefilter-item" data-value="10" href="#">October</a></li>
                    <li style="float: none;"><a id="timefilter-item-11" class="timefilter-item" data-value="11" href="#">November</a></li>
                    <li style="float: none;"><a id="timefilter-item-12" class="timefilter-item" data-value="12" href="#">December</a></li>
                </ul>
            </span>
            <span style="font-size: 14px; float: left;">by</span>
            <span class="select" id="selectRouteFilter" style="float: left; margin-top: 4px;">
                <button class="btn btn-default route-filter-button" style="margin-bottom: 0px; background-color: #f2f2f2; border-color: #f2f2f2; padding: 8px 8px; font-size: 15px;" data-toggle="dropdown">
                    <span id="routefilter-title" style="color: #00B1F2;">all routes</span>
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu routefilter-list" style="height: auto; left: inherit; overflow: auto; top: 70%;">
                    <li style="float: none;"><a class="routefilter-item" data-value="" href="#">all routes</a></li>
                    {% for r in routes %}
                    <li style="float: none;"><a class="routefilter-item" data-value="{{ r.id }}" href="#">{{ r.name }} &bull; {{ r.summit_count }} summit{{ r.summit_count|pluralize:"s" }}</a></li>
                    {% endfor %}
                </ul>
            </span>
            <span class="pull-right">Sort by:<a style="margin-left: 10px; color: rgb(153, 153, 153); font-weight: 300;" id="sort-most-recent" class="region-header-sort-links ajax-link" onclick="sortSummits('most_recent');">Most recent</a><a style="margin-left: 35px; color: rgb(153, 153, 153); font-weight: 300;" id="sort-most-liked" class="region-header-sort-links ajax-link" onclick="sortSummits('most_liked');">Most liked</a></span>
        </div>
    </div>

    <div class="row content-pane">

        <div class="col-md-12">
            <div class="row sub-header-row hidden-md hidden-lg filter-bar" style="height: 50px; padding-right: 0px;">
                <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12" style="height: 50px;line-height: 26px;">
                    <div style="float: left; width: 50%; height: 100%; border-right: solid 1px #cfcfcf;">
                        <div class="select" id="selectMobileRoutefilter">
                            <button class="btn btn-default route-filter-button" style="width: 95%; border: none; padding-top: 7px; padding-bottom: 7px; margin-left: 1px; color: #00b1f2; font-size: 12px; padding-left: 5px; padding-right: 5px; background-color: #f2f2f2;" data-toggle="dropdown" aria-expanded="false">
                                <span id="mobile-routefilter-title">All routes</span>
                                <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu routefilter-list" style="cursor: pointer; height: auto; left: 5px; overflow: auto; top: 50px;">
                                <li style="float: none; height: 30px; margin: 10px 0 0;"><a class="routefilter-item" data-value="" href="#">All routes</a></li>
                                {% for r in routes %}
                                <li style="float: none; height: 30px; margin: 10px 0 0;"><a class="routefilter-item" data-value="{{ r.key }}" href="#">{{ r.name }}</a></li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                    <div style="float: left; margin-left: 10px; width: 45%;">
                        <div class="select" id="selectMobileSortSummits" style="margin-top: 7px;">
                            <button class="btn btn-default mobile-sortsummits-button" style="width: 100%; text-align: left; border: none; padding-top: 7px; padding-bottom: 7px; margin-left: 1px; color: #00b1f2; font-size: 12px; padding-left: 5px; padding-right: 5px; background-color: #f2f2f2;" data-toggle="dropdown">
                                <span id="mobile-sortsummits-title">Most recent</span>
                                <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu mobile-sortsummits-list" style="height: 100px; left: 50%; top: 50px; overflow: auto;">
                                <li style="float: none; height: 30px; margin: 10px 0 0;"><a onclick="sortSummits('most_recent');" href="#">Most recent</a></li>
                                <li style="float: none; height: 30px; margin: 10px 0 0;"><a onclick="sortSummits('most_liked');" href="#">Most liked</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-12">
            <div class="row" id="ajax-data-loading" style="display: none;">
              <div class="col-md-12 data-loading" style="text-align: center; color: #666;">
                <i class="fa fa-spinner fa-spin fa-5x"></i>
              </div>
            </div>
            <div class="hidden-xs">
                <div id="chart-container" class="row sub-header-row" style="height: 205px; background-color: #fff; display: none;">
                    <div class="col-md-12" style="height: 205px;">
                        <div id="chart_div"></div>
                    </div>
                </div>
            </div>
            <div id="chart-spacer" style="display: none;" class="row dark-background-row hidden-xs"><div class="sp-60"></div></div>
            <div class="row" id="summits-list">
                <div class="col-md-12">

                </div>
            </div>
        </div>
    </div>
    <div class="row dark-background-row" id="pagination" style="border-bottom: none; color: #ccc; margin-top: -1px; height: 80px; margin-left: 0px; display: none;">
        <div class="col-md-12">
            <div class="row" style="padding-left: 15px; margin-left: 5px; text-align: center;">
                <span id="pagination-pages" class="step-links"></span>
            </div>
        </div>
    </div>
    <div class="row dark-header-row hidden-lg hidden-md hidden-sm">
        <div class="row">
            <div style="height: 28px;"></div>
        </div>
    </div>
    <div class="row dark-header-row hidden-xs">
        <div class="row">
            <div style="height: 53px;"></div>
        </div>
    </div>

</div>

<script type="text/javascript">

    var viewport_height = $(window).height();
    var viewport_width = $(window).width();
    var footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
    var data_loading_margin = (viewport_height - footer_height) / 3;
    $('.data-loading').css('margin-top', data_loading_margin);
    $(window).resize(function() {
        viewport_height = $(window).height();
        viewport_width = $(window).width();
        footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
        data_loading_margin = (viewport_height - footer_height) / 3;
        $('.data-loading').css('margin-top', data_loading_margin);
    });

    var summitSort;
    var summitMember;
    var summitMonth;
    var summitRoute;
    var chartData = [];
    var loading = false;

    google.load('visualization', '1', {packages: ['corechart', 'bar']});
    google.setOnLoadCallback(init);

    function drawAnnotations(chartdata) {
        try {
            var data;
            if (!chartdata) {
                chartdata = [[0, 'stroke-color: #FFFFFF; stroke-width: 2; color: #00b1f2'], [0, 'stroke-color: #FFFFFF; stroke-width: 2; color: #00b1f2'], [0, 'stroke-color: #FFFFFF; stroke-width: 2; color: #00b1f2'], [0, 'stroke-color: #FFFFFF; stroke-width: 2; color: #00b1f2'], [0, 'stroke-color: #FFFFFF; stroke-width: 2; color: #00b1f2'], [0, 'stroke-color: #FFFFFF; stroke-width: 2; color: #00b1f2'], [0, 'stroke-color: #FFFFFF; stroke-width: 2; color: #00b1f2'], [0, 'stroke-color: #FFFFFF; stroke-width: 2; color: #00b1f2'], [0, 'stroke-color: #FFFFFF; stroke-width: 2; color: #00b1f2'], [0, 'stroke-color: #FFFFFF; stroke-width: 2; color: #00b1f2'], [0, 'stroke-color: #FFFFFF; stroke-width: 2; color: #00b1f2'], [0, 'stroke-color: #FFFFFF; stroke-width: 2; color: #00b1f2']];
            }
            data = google.visualization.arrayToDataTable([
                ['Month', 'Summits', {role: 'style'}, {role: 'annotation'}],
                ['Jan', chartdata[0][0], chartdata[0][1], chartdata[0][0]],
                ['Feb', chartdata[1][0], chartdata[1][1], chartdata[1][0]],
                ['Mar', chartdata[2][0], chartdata[2][1], chartdata[2][0]],
                ['Apr', chartdata[3][0], chartdata[3][1], chartdata[3][0]],
                ['May', chartdata[4][0], chartdata[4][1], chartdata[4][0]],
                ['Jun', chartdata[5][0], chartdata[5][1], chartdata[5][0]],
                ['Jul', chartdata[6][0], chartdata[6][1], chartdata[6][0]],
                ['Aug', chartdata[7][0], chartdata[7][1], chartdata[7][0]],
                ['Sep', chartdata[8][0], chartdata[8][1], chartdata[8][0]],
                ['Oct', chartdata[9][0], chartdata[9][1], chartdata[9][0]],
                ['Nov', chartdata[10][0], chartdata[10][1], chartdata[10][0]],
                ['Dec', chartdata[11][0], chartdata[11][1], chartdata[11][0]]
            ]);

            var options = {
                backgroundColor: '#ffffff',
                legend: 'none',
                vAxis: {
                    baselineColor: '#ffffff',
                    gridlineColor: '#ffffff',
                    textPosition: 'none'
                },
                hAxis: {
                    textStyle: {color: '#00B1F2'}
                },
                animation: {
                    startup: true,
                    duration: 1000,
                    easing: 'out'
                },
                annotations: {
                    alwaysOutside: true,
                    stemLength: 3,
                    stemColor: '#ffffff',
                    textStyle: {
                        fontSize: 14,
                        color: '#000000',
                        auraColor: 'none'
                    }
                },
                chartArea: {
                    left: "0",
                    top: "5",
                    height: "80%",
                    width: "100%"
                },
                width: '100%',
                height: '100%',
                'tooltip': {
                    trigger: 'none'
                }
            };

            // The select handler. Call the chart's getSelection() method
            function selectHandler() {
                var selectedItem = chart.getSelection()[0];
                if (selectedItem) {
                    //var value = data.getValue(selectedItem.row, selectedItem.column);
                    //alert('The user selected row:' + selectedItem.row + ', column:' + selectedItem.column);
                    selectedIndex = selectedItem.row + 1;
                    summitMonth = selectedIndex;
                    $("#timefilter-title").html(getMonthName(selectedIndex));
                    loadSummits(summitMonth, summitMember, summitRoute, summitSort, '1');
                }
            }

            var clickHandler = function (e) {
                var parts = e.targetID.split('#');
                var selectedIndex;
                var reload = false;
                if (parts[0] == 'hAxis') {
                    selectedIndex = parts[3];
                    reload = true;
                } else if (parts[0] == 'bar') {
                    selectedIndex = parts[2];
                    reload = true;
                } else if (parts[0] == 'annotationtext') {
                    selectedIndex = parts[2];
                    reload = true;
                }
                if (reload) {
                    selectedIndex = parseInt(selectedIndex) + 1;
                    summitMonth = selectedIndex;
                    $("#timefilter-title").html(getMonthName(selectedIndex));
                    loadSummits(summitMonth, summitMember, summitRoute, summitSort, '1');
                }
            };

            var chart = new google.visualization.ColumnChart(document.getElementById('chart_div'));

            // Listen for the 'select' event, and call my function selectHandler() when
            // the user selects something on the chart.
            google.visualization.events.addListener(chart, 'click', clickHandler);

            $('#chart-container').show();
            $('#chart-spacer').show();

            chart.draw(data, options);
        }
        catch (err) {
            //pass
        }
    }

    function sortSummits(sort) {

        summitSort = sort;

        switch(sort) {
            case 'most_recent':
                $('#sort-most-recent').css('color', '#F24100');
                $('#sort-most-recent').css('font-weight', '500');
                $('#sort-most-liked').css('color', '#000000');
                $('#sort-most-liked').css('font-weight', '300');
                break;
            case 'most_liked':
                $('#sort-most-recent').css('color', '#000000');
                $('#sort-most-recent').css('font-weight', '300');
                $('#sort-most-liked').css('color', '#F24100');
                $('#sort-most-liked').css('font-weight', '500');
                break;
            default:
                $('#sort-most-recent').css('color', '#F24100');
                $('#sort-most-recent').css('font-weight', '500');
                $('#sort-most-liked').css('color', '#000000');
                $('#sort-most-liked').css('font-weight', '300');
        }

        loadSummits(summitMonth, summitMember, summitRoute, summitSort, '1');

    }

    function loadSummits(month, member, route, sort, page) {

        loading = true;

        summitSort = sort;
        summitMember = member;
        summitMonth = month;
        summitRoute = route;
        $('#pagination').css('display','none');
        $('#chart-container').css('display','none');
        $('#chart-spacer').css('display','none');
        window.scrollTo(0, 0);

        switch(sort) {
            case 'most_recent':
                $('#sort-most-recent').css('color', '#F24100');
                $('#sort-most-recent').css('font-weight', '500');
                $('#sort-most-liked').css('color', '#999');
                $('#sort-most-liked').css('font-weight', '300');
                break;
            case 'most_liked':
                $('#sort-most-recent').css('color', '#999');
                $('#sort-most-recent').css('font-weight', '300');
                $('#sort-most-liked').css('color', '#F24100');
                $('#sort-most-liked').css('font-weight', '500');
                break;
            default:
                $('#sort-most-recent').css('color', '#F24100');
                $('#sort-most-recent').css('font-weight', '500');
                $('#sort-most-liked').css('color', '#999');
                $('#sort-most-liked').css('font-weight', '300');
        }

        window.location.hash = 'month='+month+'&member='+member+'&route='+route+'&order='+sort+'&page='+page;

        $('#summits-list').empty();
        $('#ajax-data-loading').css('display', 'inline');
        var summitCount = 0;
        var totalPages = Math.ceil(parseInt(summitCount)/25);
        var counter = 1;
        var summitIds = '';

        //clear pagination
        $('#pagination-pages').html('');
        $('#pagination').css('display', 'none');

        chartData = [[0,'color: #00b1f2'],[0,'color: #00b1f2'],[0,'color: #00b1f2'],[0,'color: #00b1f2'],[0,'color: #00b1f2'],[0,'color: #00b1f2'],[0,'color: #00b1f2'],[0,'color: #00b1f2'],[0,'color: #00b1f2'],[0,'color: #00b1f2'],[0,'color: #00b1f2'],[0,'color: #00b1f2']];

        $.getJSON('{% url "peaks_summits_list" %}?item_id={{ peak.id }}&month='+month+'&member='+member+'&route_id='+route+'&sort='+sort+'&page='+page, function(data) {
            $.each( data, function( key, val ) {
                if (key=='summit_months') {
                    //reset chartData colors
                    for (var i=0; i < chartData.length; i++){
                        chartData[i][1] = 'stroke-color: #00b1f2; stroke-width: 2; color: #00b1f2';
                    }
                    var maxSummitsValue = 0;
                    $.each( val, function( monthkey, monthval ) {
                        if (parseInt(monthval.summits_count) > maxSummitsValue) {
                            maxSummitsValue = parseInt(monthval.summits_count);
                        }
                    });
                    $.each( val, function( monthkey, monthval ) {
                        if (parseInt(monthval.month) == parseInt(month)) {
                            monthcolor = 'stroke-color: #FFFFFF; stroke-width: 2; color: #f24100';
                            monthindex = parseInt(monthval.month)-1;
                            chartData[monthindex][0] = parseInt(monthval.summits_count);
                            chartData[monthindex][1] = monthcolor;
                            summitCount = parseInt(summitCount) + parseInt(monthval.summits_count);
                        } else {
                            colorshade = '#00b1f2';
                            var monthValue = parseInt(monthval.summits_count);
                            /*if (monthValue / maxSummitsValue < .1) {
                                colorshade = '#E5FFFF';
                            } else if (monthValue / maxSummitsValue < .2) {
                                colorshade = '#CCFFFF';
                            } else if (monthValue / maxSummitsValue < .3) {
                                colorshade = '#B3FFFF';
                            } else if (monthValue / maxSummitsValue < .4) {
                                colorshade = '#99FFFF';
                            } else if (monthValue / maxSummitsValue < .5) {
                                colorshade = '#7FFFFF';
                            } else if (monthValue / maxSummitsValue < .6) {
                                colorshade = '#66FFFF';
                            } else if (monthValue / maxSummitsValue < .7) {
                                colorshade = '#4DFEFF';
                            } else if (monthValue / maxSummitsValue < .8) {
                                colorshade = '#33E4FF';
                            } else if (monthValue / maxSummitsValue < .9) {
                                colorshade = '#1ACBFF';
                            }*/
                            monthcolor = 'stroke-color: #FFFFFF; stroke-width: 2; color: ' + colorshade;
                            monthindex = parseInt(monthval.month)-1;
                            chartData[monthindex][0] = parseInt(monthval.summits_count);
                            chartData[monthindex][1] = monthcolor;
                            if (month == '') {
                                summitCount = parseInt(summitCount) + parseInt(monthval.summits_count);
                            }
                        }
                    });
                    $('#summit-count').html(summitCount);
                    if (summitCount != 1) {
                        $('#summit-count-dscr').html('climbs');
                    } else {
                        $('#summit-count-dscr').html('climb');
                    }
                    totalPages = Math.ceil(parseInt(summitCount)/25);
                    drawAnnotations(chartData);
                }
                if (key=='summits') {
                    $.each( val, function( summitkey, summitval ) {
                        var summitdate = new Date(summitval.summitlog_date);
                        var today = new Date();
                        var timeDiff = Math.abs(today.getTime() - summitdate.getTime());
                        var diffDays = Math.ceil(timeDiff / (1000 * 3600 * 24));
                        var photo_arr = summitval.photos;
                        var avatar;
                        if (summitval.avatar_url != 'None') {
                            avatar = '<img src="{{MEDIA_URL}}' + summitval.avatar_url + '" class="summit-card-user-avatar">&nbsp;&nbsp;';
                        } else {
                            avatar = '';
                        }

                        //build country string
                        var country = '';
                        $.each( summitval.country, function( countrykey, countryval ) {
                            country = country + countryval.country_name + ' / ';
                        });
                        country = country.substr(0, country.length-3);
                        if (country == '') {
                            country = 'Unknown Country';
                        }

                        //build region string
                        var region = '';
                        $.each( summitval.region, function( regionkey, regionval ) {
                            region = region + regionval.region_name + ', ' + regionval.country_name + ' / ';
                        });
                        region = region.substr(0, region.length-3);
                        if (region == '') {
                            region = country;
                        }

                        var dividerDiv = '';
                        if (counter > 1) {
                            dividerDiv = '<div class="row dark-background-row"><div class="sp-60"></div></div>';
                        }

                        if (summitval.date_entered == 'False') {
                            var desktop_date = 'date unknown';
                            var mobile_date = 'date unknown';
                        } else {
                            var display_date = $.format.date(summitval.summitlog_date + 'T00:00:00', 'MMM d, yyyy');
                            var desktop_date = display_date + '&nbsp;/&nbsp;<time class="timeago" datetime="' + summitval.summitlog_date + 'T00:00:00">' + summitval.summitlog_date + '</time>';
                            var mobile_date = '<time class="timeago" datetime="' + summitval.summitlog_date + 'T00:00:00">' + summitval.summitlog_date + '</time>';
                        }

                        var header_border = '';
                        if (photo_arr.length == 0 && (summitval.gpx_file == 'None' || summitval.gpx_file == '') && summitval.log != null) {
                            header_border = 'border-bottom: 1px solid #c0c0c0; ';
                        }

                        var attempt = '';
                        var mobile_stats = '';

                        var max_username_length = 15;
                        var max_region_length = 27;
                        var trimmed_username = summitval.username.length > max_username_length ? summitval.username.substring(0, max_username_length - 3) + "..." : summitval.username;
                        var trimmed_region = region.length > max_region_length ? region.substring(0, max_region_length - 3) + "..." : region;

                        if (summitval.attempt == 'True') {
                            attempt = '<span class="summit-attempt">Attempt</span>&nbsp;&nbsp;&bull;&nbsp;&nbsp;';
                            mobile_stats = '<span class="summit-attempt">Attempt</span>&nbsp;&bull;&nbsp;'  + '<a href="/members/' + summitval.username + '/">' + trimmed_username + '</a>&nbsp;&bull;&nbsp;' + mobile_date;
                        } else {
                            mobile_stats = trimmed_region + '&nbsp;&bull;&nbsp;<a href="/members/' + summitval.username + '/">' + trimmed_username + '</a>&nbsp;&bull;&nbsp;' + mobile_date;
                        }

                        $('#summits-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">' + dividerDiv + '<div class="row" style="' + header_border + 'cursor: pointer;" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');"><div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 summit-card-header" style="background-color: #fff; border-right: 1px solid #c0c0c0; letter-spacing: 1.0px;"><div class="summit-list-stats summit-card-user-stats hidden-xs hidden-sm" style="float: right;">&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + avatar + '<a href="/members/' + summitval.username + '/">' + summitval.username + '</a>&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + desktop_date + '</div><div class="summit-list-stats summit-card-user-stats hidden-lg hidden-md hidden-xs" style="float: right;">&nbsp;&nbsp;&bull;&nbsp;&nbsp;<a href="/members/' + summitval.username + '/">' + summitval.username + '</a>&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + mobile_date + '</div><div class="summit-list-stats summit-card-peak-stats hidden-lg hidden-md hidden-xs" style="float: right;">' + attempt + region + '</div><div class="stats-header summit-card-peak-title" style="height: 70px; margin-top: 0px; margin-bottom: 0px;"><div class="summit-list-stats summit-card-peak-stats hidden-xs hidden-sm" style="float: right;">' + attempt + numberWithCommas(Math.round(summitval.elevation)) + ' ft / ' + numberWithCommas(Math.round(summitval.elevation * .3048)) + ' m&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + region + '</div><div class="ellipsis" style="overflow: hidden;"><a style="color: #000000;" href="/' + summitval.peak_slug + '/">' + summitval.peak_name + '</a></div></div><div class="summit-list-stats summit-card-mobile-stats hidden-lg hidden-md hidden-sm">' + mobile_stats + '</div></div></div>');

                        //get favorites count
                        var favorites_arr = summitval.favorites;
                        var favorites_count = favorites_arr.length;
                        if (favorites_count == '0') {
                            favorites_count = '&nbsp;';
                        }

                        //get comments count
                        var comments_arr = summitval.comments;
                        var comments_count = comments_arr.length;
                        if (comments_count == '0') {
                            comments_count = '&nbsp;';
                        }

                        var staticMapZoom = '/' + summitval.peak_long + ',' + summitval.peak_lat + ',5,0.00,0.00/';
                        var twoPhotoMapClass = 'col-lg-6 col-md-6 col-sm-4 hidden-xs map-card-web-2';
                        //gpx file?
                        if (summitval.gpx_file != 'None' && summitval.gpx_file != '') {
                            staticMapZoom = '/auto';
                            twoPhotoMapClass = 'col-lg-6 col-md-6 col-sm-4 col-xs-12 map-card-web-2 map-card-tablet-2 map-card-mobile-2';
                        }

                        //add photos
                        var photo_index = 0;
                        var photo_arr = summitval.photos;
                        var photos_count = photo_arr.length;
                        var photos_style = '';
                        if (photos_count == '0') {
                            photos_count = '&nbsp;';
                            photos_style = 'display: none;'
                        }

                        var staticMapUrl = summitval.map_mapbox_thumbnail;

                        if (parseInt(summitval.peak_long) != 9999 && parseInt(summitval.peak_lat) != 9999) {
                            staticMapPeakMarker = '/<EMAIL>(' + summitval.peak_long + ',' + summitval.peak_lat + ')';
                        } else {
                            staticMapPeakMarker = '/url';
                        }

        // if has gpx file
                        if (summitval.gpx_file != 'None' && summitval.gpx_file != '') {
                            //build stats overlay
                            var statsOverlayText = '';
                            var mobileStatsOverlayText = '';
                            var gpxStatsOverlay = '';
                            var mobileStatsOverlay = '';
                            var divStyle = '';
                            if (summitval.total_distance > 0 || summitval.total_trip_time > 0 || summitval.elevation_gain > 0) {
                                {% if peak.is_usa %}
                                    if (summitval.total_distance > 0) {
                                        gpxStatsOverlay = gpxStatsOverlay + parseFloat(summitval.total_distance).toFixed(1) + ' mi &bull; ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="fas fa-arrows-alt-h" style="margin-right: 5px;"></i>' + parseFloat(summitval.total_distance).toFixed(1) + ' mi</span><div style="width: 1px; height: 30px; background-color: #ccc; margin-top: 10px;"></div> ';
                                    }
                                    if (summitval.elevation_gain > 0) {
                                        gpxStatsOverlay = gpxStatsOverlay + parseInt(summitval.elevation_gain).toLocaleString() + ' ft gain &bull; ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="fas fa-long-arrow-alt-up fa-rotate-45" style="margin-right: 5px;"></i>' + parseInt(summitval.elevation_gain).toLocaleString() + ' ft gain</span><div style="width: 1px; height: 30px; background-color: #ccc; margin-top: 10px;"></div> ';
                                    }
                                    if (summitval.total_trip_time > 0) {
                                        totalSeconds = parseFloat(summitval.total_trip_time);
                                        hours = Math.floor(totalSeconds / 3600);
                                        totalSeconds %= 3600;
                                        minutes = Math.floor(totalSeconds / 60);
                                        gpxStatsOverlay = gpxStatsOverlay + hours + 'h ' + minutes + 'm ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="far fa-clock" style="margin-right: 5px;"></i>' + hours + ' hr ' + minutes + ' min </span></div>';
                                    }
                                {% else %}
                                    if (summitval.total_distance > 0) {
                                        km = parseFloat(summitval.total_distance) * 1.609344
                                        gpxStatsOverlay = gpxStatsOverlay + km.toFixed(1) + ' km &bull; ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="fas fa-arrows-alt-h" style="margin-right: 5px;"></i>' + km.toFixed(1) + ' km</span><div style="width: 1px; height: 30px; background-color: #ccc; margin-top: 10px;"></div> ';
                                    }
                                    if (summitval.elevation_gain > 0) {
                                        m = parseFloat(summitval.elevation_gain) * 0.3048
                                        gpxStatsOverlay = gpxStatsOverlay + parseInt(m).toLocaleString() + ' m gain &bull; ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="fas fa-long-arrow-alt-up" style="margin-right: 5px;"></i>' + parseInt(m).toLocaleString() + ' m gain</span><div style="width: 1px; height: 30px; background-color: #ccc; margin-top: 10px;"></div> ';
                                    }
                                    if (summitval.total_trip_time > 0) {
                                        totalSeconds = parseFloat(summitval.total_trip_time);
                                        hours = Math.floor(totalSeconds / 3600);
                                        totalSeconds %= 3600;
                                        minutes = Math.floor(totalSeconds / 60);
                                        gpxStatsOverlay = gpxStatsOverlay + hours + 'h ' + minutes + 'm ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="far fa-clock" style="margin-right: 5px;"></i>' + hours + ' hr ' + minutes + ' min </span>';
                                    }
                                {% endif %}
                            }

                            statsOverlayText = '<div class="user-photo-info hidden-xs" style="display: block; height: 60px;"><span class="data photo-caption" style="position: absolute; bottom: -5px; right: 10px; color: #fff;"><p class="bagger" style="font-size: 16px; margin: 0 10px 15px 0;">' + gpxStatsOverlay + '</p></span></div>';
                            mobileStatsOverlayText = '<div class="hidden-lg hidden-md hidden-sm col-xs-12" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; background-color: #fff; border-bottom: 1px solid #c0c0c0; margin-top: -1px; padding-left: 0px; padding-right: 0px;"><div class="col-md-12" style="height: 50px;line-height: 50px;font-size: 12px;color: #999;display: flex;justify-content: space-between;background-color: #f2f2f2;padding-left: 0px;padding-right: 1px;">' + mobileStatsOverlay + '</div></div>';

                            if (photo_arr.length == 0) {
                                $('#summits-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 map-card-web-0 map-card-tablet-0 map-card-mobile-0" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" id="map-' + summitval.id + '" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="img-responsive peakeryPhoto photography peakimg-responsive" src="{% static 'img/spacer.png'%}"></div>' + statsOverlayText + '</div>');
                            } else if (photo_arr.length == 1) {
                                $.each(summitval.photos, function (photokey, photoval) {
                                    photo_index++;
                                    $('#summits-list').append('<div class="summit-card-photo-' + photo_index + ' col-lg-3 col-md-3 col-sm-4 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img src="{% static 'img/spacer.png'%}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                });
                                $('#summits-list').append('<div class="col-lg-9 col-md-9 col-sm-8 col-xs-6 map-card-web-1 map-card-tablet-1 map-card-mobile-1" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png'%}"></div>' + statsOverlayText + '</div>');
                            } else if (photo_arr.length == 2) {
                                $.each(summitval.photos, function (photokey, photoval) {
                                    photo_index++;
                                    $('#summits-list').append('<div class="summit-card-photo-' + photo_index + ' col-lg-3 col-md-3 col-sm-4 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img src="{% static 'img/spacer.png'%}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                });
                                $('#summits-list').append('<div class="' + twoPhotoMapClass + '" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png'%}"></div>' + statsOverlayText + '</div>');
                            } else if (photo_arr.length == 3) {
                                $.each(summitval.photos, function (photokey, photoval) {
                                    photo_index++;
                                    $('#summits-list').append('<div class="summit-card-photo-' + photo_index + ' col-lg-3 col-md-3 col-sm-4 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img src="{% static 'img/spacer.png'%}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                    if (photo_index > 3) {
                                        return false;
                                    }
                                });
                                $('#summits-list').append('<div class="col-lg-3 col-md-3 col-sm-12 col-xs-6" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png'%}"></div>' + statsOverlayText + '</div>');
                            } else {
                                $.each(summitval.photos, function (photokey, photoval) {
                                    photo_index++;
                                    if (photo_index == 3) {
                                        divStyle = 'col-lg-3 col-md-3 hidden-sm col-xs-6';
                                    } else {
                                        divStyle = 'col-lg-3 col-md-3 col-sm-4 col-xs-6';
                                    }
                                    $('#summits-list').append('<div class="summit-card-photo-' + photo_index + ' ' + divStyle + ' summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img src="{% static 'img/spacer.png'%}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                    if (photo_index > 3) {
                                        return false;
                                    }
                                });
                                $('#summits-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 map-card-web-4 map-card-tablet-3 map-card-mobile-4" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png'%}"></div>' + statsOverlayText + '</div>');
                            }
                            $('#summits-list').append(mobileStatsOverlayText);
                        } else {

                            // if we don't have a GPX file
                            if (photo_arr.length == 1) {
                                var fillerPhoto = '';
                                $.each(summitval.photos, function (photokey, photoval) {
                                    photo_index++;
                                    if (fillerPhoto == '') {
                                        fillerPhoto = photoval.thumbnail_url;
                                    }
                                    $('#summits-list').append('<div class="summit-card-photo-' + photo_index + ' col-lg-3 col-md-3 col-sm-4 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><img src="{% static 'img/spacer.png'%}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                });
                                $('#summits-list').append('<div class="col-lg-9 col-md-9 col-sm-8 col-xs-6 map-card-web-1 map-card-tablet-1 map-card-mobile-1" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: linear-gradient(rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0.7) 100%), url(\'{{ MEDIA_URL }}images/' + fillerPhoto + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png'%}"></div></div>');
                            } else if (photo_arr.length == 2) {
                                var fillerPhoto = '';
                                $.each(summitval.photos, function (photokey, photoval) {
                                    photo_index++;
                                    if (fillerPhoto == '') {
                                        fillerPhoto = photoval.thumbnail_url;
                                    }
                                    $('#summits-list').append('<div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><img src="{% static 'img/spacer.png'%}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                });
                                $('#summits-list').append('<div class="' + twoPhotoMapClass + '" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: linear-gradient(rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0.7) 100%), url(\'{{ MEDIA_URL }}images/' + fillerPhoto + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png'%}"></div></div>');
                            } else if (photo_arr.length == 3) {
                                var fillerPhoto = '';
                                $.each(summitval.photos, function (photokey, photoval) {
                                    photo_index++;
                                    if (fillerPhoto == '') {
                                        fillerPhoto = photoval.thumbnail_url;
                                    }
                                    $('#summits-list').append('<div class="summit-card-photo-' + photo_index + ' col-lg-3 col-md-3 col-sm-4 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><img src="{% static 'img/spacer.png'%}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                    if (photo_index > 3) {
                                        return false;
                                    }
                                });
                                $('#summits-list').append('<div class="col-lg-3 col-md-3 col-sm-12 col-xs-6 map-card-tablet-3" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: linear-gradient(rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0.7) 100%), url(\'{{ MEDIA_URL }}images/' + fillerPhoto + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png'%}"></div></div>');
                            } else if (photo_arr.length >= 3) {
                                $.each(summitval.photos, function (photokey, photoval) {
                                    photo_index++;
                                    if (photo_index == 3) {
                                        divStyle = 'col-lg-3 col-md-3 hidden-sm col-xs-6';
                                    } else {
                                        divStyle = 'col-lg-3 col-md-3 col-sm-4 col-xs-6';
                                    }
                                    $('#summits-list').append('<div class="summit-card-photo-' + photo_index + ' ' + divStyle + ' summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><img src="{% static 'img/spacer.png'%}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                    if (photo_index > 3) {
                                        return false;
                                    }
                                });
                            }
                        }

                        //add log
                        if (summitval.log != null) {
                            var log_text = summitval.log.replace(/(?:\r\n|\r|\n)/g, '<br>');
                            if (log_text.length > 9999) {
                                log_text = summitval.log.substring(0, 700)+'...';
                            }
                            $('#summits-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; background-color: #fff; border-bottom: 1px solid #c0c0c0;"><div><p class="summit-log-summary">' + log_text + '</p></div></div>');
                        }
                        //footer
                        classLiked = '';
                        if (summitval.log != null || (summitval.gpx_file != 'None' && summitval.gpx_file != '') || photo_arr.length > 0 || summitval.peak_thumbnail_url != 'images/img/cache/default.png.350x245_q95_crop.jpg') {
                            $('#summits-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');"><div class="row" style="border-bottom-left-radius: 4px; border-bottom-right-radius: 4px;"><div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="height: 70px; background-color: #fff; z-index: 1; border-bottom-left-radius: 4px; border-bottom-right-radius: 4px; box-shadow: 0px 10px 10px rgba(0, 0, 0, 1);"><div><p><span class="summit-list-footer" style="font-weight: 600; line-height: 70px;"><a href="/' + summitval.peak_slug + '/summits/' + summitval.id + '/"><button style="padding-left: 13px; width: 100%; height: 100%; text-align: left; border: none; color: #00B1F2; font-weight: 600; position: absolute; left: -2px; top: -2px; z-index: 1; border-bottom-left-radius: 4px; border-bottom-right-radius: 4px;" class="btn btn-default btn-see-full-log">SEE FULL LOG</button></a></span><span class="summit-list-footer pull-right" style="margin-top: 8px; position: absolute; right: 30px; z-index: 2;"><span style="margin-left: 40px;"><a href="javascript:like_summit(' + summitval.id + ');" class="like_summit{{ is_authenticated }}" id="summitlog-with-' + summitval.id + '"><span class="' + classLiked + '" id="summitlog-like-' + summitval.id + '" style="width: 32px; height: 32px; display: inline-block; color: #ffffff; line-height: 32px; text-align: center; padding-left: 2px; font-size: 10px; font-weight: bold; background-size: 100%; background-image: url({% static 'img/fa-heart_256_0_00b1f2_none.png'%});">&nbsp;</span></a></span><span style="margin-left: 40px;"><a href="/' + summitval.peak_slug + '/summits/' + summitval.id + '/"><span id="summitlog-comment-' + summitval.id + '" style="width: 32px; height: 32px; display: inline-block; color: #ffffff; line-height: 30px; text-align: center; padding-left: 3px; font-size: 10px; font-weight: bold; background-size: 100%; background-image: url({% static 'img/fa-comment_256_0_00b1f2_none.png'%});">&nbsp;</span></a></span><span style="margin-left: 40px; ' + photos_style + '"><a href="/' + summitval.peak_slug + '/summits/' + summitval.id + '/"><span style="width: 32px; height: 32px; display: inline-block; color: #ffffff; line-height: 36px; text-align: center; padding-left: 2px; font-size: 10px; font-weight: bold; background-size: 100%; background-image: url({% static 'img/fa-camera_256_0_00b1f2_none.png'%});">' + photos_count + '</span></a></span></span></p><span class="peak-list-highlights"><p></p></span></div></div></div>');
                        }
                        $('#summits-list').append('</div>');


                        if (summitval.gpx_mapbox_thumbnail != '') {
                            $("#map-"+summitval.id).css("background-image", "url('" + summitval.gpx_mapbox_thumbnail + "')");
                        }

                        counter++;
                        summitIds = summitIds + summitval.id + ',';

                    });
                    //show pagination
                    var paginationHtml = '';
                    if (parseInt(page) == totalPages && totalPages > 1) {
                        paginationHtml = '<div style="display: inline-block; height: 55px;"><a onclick="loadSummits(\''+summitMonth+'\', \''+summitMember+'\', \''+summitRoute+'\', \''+summitSort+'\', ' + (parseInt(page)-1).toString() + ');"><img src="{% static 'img/font-awesome_4-7-0_arrow-circle-left_256_0_00b1f2_none.png' %}" style="height: 26px; margin-bottom: 5px;"></a></div><div class="current" style="margin-left: 20px; margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">summits ' + ((parseInt(page)-1)*25+1).toString() + ' - ' + numberWithCommas(summitCount) + ' of ' + numberWithCommas(summitCount) + '</div>';
                    } else if (parseInt(page) > 1) {
                        paginationHtml = '<div style="display: inline-block; height: 55px;"><a onclick="loadSummits(\''+summitMonth+'\', \''+summitMember+'\', \''+summitRoute+'\', \''+summitSort+'\', ' + (parseInt(page)-1).toString() + ');"><img src="{% static 'img/font-awesome_4-7-0_arrow-circle-left_256_0_00b1f2_none.png' %}" style="height: 26px; margin-bottom: 5px;"></a></div><div class="current" style="margin-left: 20px; margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">summits ' + ((parseInt(page)-1)*25+1).toString() + ' - ' + ((parseInt(page))*25).toString() + ' of ' + numberWithCommas(summitCount) + '</div>';
                    } else if (totalPages > 1) {
                        paginationHtml = '<div class="current" style="margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">summits 1 - 25 of ' + numberWithCommas(summitCount) + '</div>';
                    } else {
                        paginationHtml = '<div class="current" style="margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">summits 1 - ' + numberWithCommas(summitCount) + ' of ' + numberWithCommas(summitCount) + '</div>';
                    }
                    if (parseInt(page) < totalPages) {
                        paginationHtml = paginationHtml + '<div style="display: inline-block; height: 55px;"><a onclick="loadSummits(\''+summitMonth+'\', \''+summitMember+'\', \''+summitRoute+'\', \''+summitSort+'\', ' + (parseInt(page)+1).toString() + ');"><img src="{% static 'img/font-awesome_4-7-0_arrow-circle-right_256_0_00b1f2_none.png'%}" style="height: 26px; margin-bottom: 5px;"></i></a></div>';
                        $('#pagination-pages').html(paginationHtml);
                        $('#pagination').css('display', 'inherit');
                    } else if (totalPages > 0) {
                        $('#pagination-pages').html(paginationHtml);
                        $('#pagination').css('display', 'inherit');
                    } else {
                        $('#pagination-pages').html('');
                        $('#pagination').css('display', 'none');
                    }

                }
            });

            if (summitIds != '') {
                summitIds = summitIds.substring(0, summitIds.length - 1)
                loadLikesComments(summitIds)
            }
            $("time.timeago").timeago();
            $('#ajax-data-loading').css('display', 'none');
            loading = false;
        });

    }

    function loadLikesComments(summit_ids) {
        $.getJSON('{% url "summits_latest_likes_comments" %}?summit_ids=' + summit_ids, function(data) {
            $.each( data, function( key, val ) {
                if (key=='summits') {
                    $.each(val, function (summitkey, summitval) {
                        heart = '#summitlog-like-' + summitval.id;
                        bubble = '#summitlog-comment-' + summitval.id;
                        if (summitval.you_liked != '0') {
                            $(heart).addClass('liked');
                        }
                        if (summitval.like_count != '0') {
                            $(heart).html(summitval.like_count);
                        }
                        if (summitval.comment_count != '0') {
                            $(bubble).html(summitval.comment_count);
                        }
                    });
                }
            });
        });
    }

    function numberWithCommas(x) {
        return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    {% if user.is_authenticated %}
    function like_summit(id) {
        var likeButton = $('#summitlog-like-'+id);
        var summitID = id;
        var summitlogID = '18';
        if (likeButton.hasClass("login")){
            $.facebox( {ajax:'/accounts/login/?next=/accounts/login_reload/login/'});
        } else {
            if (likeButton.hasClass("liked")) {
                likeButton.removeClass("liked");
                likeButton.addClass("liking");
                var count = $('#summitlog-like-'+summitID).html();
                count = parseInt(count)-1;
                if (count > 0) {
                    $('#summitlog-like-'+summitID).html(count);
                } else {
                    $('#summitlog-like-'+summitID).html('&nbsp;');
                }
                var request = $.ajax({
                  method: "POST",
                  url: "/favorites/remove/",
                  dataType: "json",
                  data: {object_id: summitID, content_type_id: summitlogID}
                });
                request.done(function( data ) {
                  //$('#summitlog-like-'+summitID).html(data.count);
                  likeButton.removeClass("liking");
                });
            } else {
                likeButton.addClass("liked");
                likeButton.addClass("liking");
                var count = $('#summitlog-like-'+summitID).html();
                if (count == '&nbsp;') {
                    count = 1;
                } else {
                    count = parseInt(count)+1;
                }
                $('#summitlog-like-'+summitID).html(count);
                var request = $.ajax({
                  method: "POST",
                  url: "/favorites/add/",
                  dataType: "json",
                  data: {object_id: summitID, content_type_id: summitlogID}
                });
                request.done(function( data ) {
                  //$('#summitlog-like-'+summitID).html(data.count);
                  likeButton.removeClass("liking");
                });
            }
        }
        return false;
    }
    {% else %}
    function like_summit(id) {
        $('#accounts-login').modal('show');
    }
    {% endif %}

    function viewSummit(url) {
        window.location.href = url;
    }

    function highlightPhotos(summitid) {
        $('.summitimg-'+summitid).addClass('hover-photos-hover');
    }

    function unhighlightPhotos(summitid) {
        $('.summitimg-'+summitid).removeClass('hover-photos-hover');
    }

    function getMonthName(monthNumber) {
      var months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
      return months[monthNumber - 1];
    }

    function getOrdinal(i) {
        var j = i % 10,
            k = i % 100;
        if (j == 1 && k != 11) {
            return i + "st";
        }
        if (j == 2 && k != 12) {
            return i + "nd";
        }
        if (j == 3 && k != 13) {
            return i + "rd";
        }
        return i + "th";
     }

    function init() {

        var vars = [], hash, month, member, sort, page;
        var q = document.URL.split('#')[1];
        if(q != undefined){
            q = q.split('&');
            for(var i = 0; i < q.length; i++){
                hash = q[i].split('=');
                vars.push(hash[1]);
                vars[hash[0]] = hash[1];
            }
        }

        if (vars['month'] != undefined) {
            month = vars['month'];
            $("#timefilter-title").html(getMonthName(month));
        } else {
            month = '';
        }

        if (vars['member'] != undefined) {
            member = vars['member'];
            $("#memberfilter-title").html($('.memberfilter-item[data-value='+member+']').html());
        } else {
            member = 'all';
        }

        if (vars['route'] != undefined) {
            route = vars['route'];
            if (route != '') {
                var route_html = $('.routefilter-item[data-value='+route+']').html();
                var route_name = route_html.split('\u2022');
                $("#routefilter-title").html(route_name[0]);
                $("#mobile-routefilter-title").html(route_name[0]);
            }
        } else {
            route = '';
        }

        if (vars['order'] != undefined) {
            sort = vars['order'];
        } else {
            sort = 'most_recent';
        }

        if (vars['page'] != undefined) {
            page = vars['page'];
        } else {
            page = '1';
        }

        loadSummits(month, member, route, sort, page);

    }

    $(document).ready(function() {

        $("#mobile-app-map-link").on('click', function() {
            //Android.peakCoordinates('{{ peak.lat }},{{ peak.long }}');
            //Android.peakId('{{ peak.id }}');
            Android.peakInfo(peakObject);
        });

        //add log this climb option to log climb dropdown
        $('#log-climb-log-manually .navbar-primary .log-climb-dropdown-label-div').html('Log another peak');
        $('#log-climb-log-this-peak .navbar-primary').attr('href','/peaks/log_climb/?peak={{ peak.id }}');
        $('#log-climb-log-this-peak .navbar-primary .log-climb-dropdown-label-div').html('Log {{ peak.name }}');
        $('#log-climb-log-this-peak').show();

        // Hide mobile filter bar on on scroll down
        var didScroll;
        var lastScrollTop = 0;
        var delta = 5;

        $(window).scroll(function(event){
            didScroll = true;
        });

        document.addEventListener("touchstart", ScrollStart, false);

        setInterval(function() {
            if (didScroll) {
                hasScrolled();
                didScroll = false;
            }
        }, 100);

        function ScrollStart() {
            //start of scroll event for iOS
            hasScrolled();
        }

        function hasScrolled() {
            var st = $(this).scrollTop();

            // Make sure they scroll more than delta
            if (Math.abs(lastScrollTop - st) <= delta)
                return;

            // If they scrolled down and are past the filter bar, add class .filter-scrollup.
            // This is necessary so you never see what is "behind" the navbar.
            if (st > lastScrollTop && st > 50) {
                // Scroll Down
                $('.filter-bar').hide();
            } else {
                // Scroll Up
                if (st + $(window).height() < $(document).height()) {
                    $('.filter-bar').show();
                }
            }
            lastScrollTop = st;
        }

        $('.youve-climbed').on('click', function(e) {

            e.preventDefault();
            $("#memberfilter-title").html('you');
            summitMember = 'you';
            //reset months filter
            summitMonth = '';
            $("#timefilter-title").html('all months');
            //reset routes filter
            summitRoute = '';
            $("#routefilter-title").html('all routes');
            $("#mobile-routefilter-title").html('All routes');
            loadSummits(summitMonth, summitMember, summitRoute, summitSort, '1');

        });

        $('.memberfilter-item').on('click', function(e) {

            e.preventDefault();
            $("#memberfilter-title").html($(this).html());
            summitMember = $(this).data('value');
            //reset months filter
            summitMonth = '';
            $("#timefilter-title").html('all months');
            //reset routes filter
            summitRoute = '';
            $("#routefilter-title").html('all routes');
            $("#mobile-routefilter-title").html('All routes');
            loadSummits(summitMonth, summitMember, summitRoute, summitSort, '1');

        });

        $('.timefilter-item').on('click', function(e) {

            e.preventDefault();
            $("#timefilter-title").html($(this).html());
            summitMonth = $(this).data('value');
            loadSummits(summitMonth, summitMember, summitRoute, summitSort, '1');

        });

        $('.routefilter-item').on('click', function(e) {

            e.preventDefault();
            var route_html = $(this).html();
            var route_name = route_html.split('\u2022');
            $("#routefilter-title").html(route_name[0]);
            $("#mobile-routefilter-title").html(route_name[0]);
            summitRoute = $(this).data('value');
            //reset months filter
            $("#timefilter-title").html('all months');
            summitMonth = '';
            loadSummits(summitMonth, summitMember, summitRoute, summitSort, '1');

        });

        //create trigger to resizeEnd event
        $(window).resize(function() {
            if(this.resizeTO) clearTimeout(this.resizeTO);
            this.resizeTO = setTimeout(function() {
                $(this).trigger('resizeEnd');
            }, 500);
        });

        //redraw graph when window resize is completed
        $(window).on('resizeEnd', function() {
            if (loading == false) {
                drawAnnotations(chartData);
            }
        });

    });

</script>

{% endblock %}