{% extends "base.html" %}

{% load static %}
{% block jquery_import %}
    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.7.0/jquery.min.js"></script>
{% endblock %}

{% block jquery_form_import %}
    <script type="text/javascript" src="{{MEDIA_URL}}js/jquery.form.2.92.js"></script>
{% endblock %}

{% block title %}{{ group.name }} map{% endblock %}
{% block titlemeta_overwrite %}{{ group.name }}{% endblock %}

{% block description %}Map of {{ group.name }} peaks.{% endblock %}
{% block image_rel %}{% endblock %}

{% block css %}
    <link href="{{ MEDIA_URL }}css/explore.css" rel="stylesheet" type="text/css" media="screen" />
{% endblock %}

{% block extrajs %}
    <style>
        .ui-autocomplete {max-height: 200px; overflow-y: auto; overflow-x: hidden; padding-right: 20px;}
        * html .ui-autocomplete {height: 200px;}
        .ui-autocomplete-loading { background: white url('{{ MEDIA_URL }}img/misc/ajax1.gif') right center no-repeat; }
    </style>

{% endblock %}

{% block mapbox %}
{% include "mapbox/mapbox.html" %}
{% endblock %}

{% block bodyclass %}wrapper_set1 explore{% endblock %}
{% block explore_active %}{% if not showing_user_peaks %}active{% endif %}{% endblock %}
{% block map_active %}{% if not showing_user_peaks %}active{% endif %}{% endblock %}
{% block explored_class %}{% if not showing_user_peaks %}explored{% endif %}{% endblock %}
{% block explore_link %}javascript:void(0){% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row hidden-xs" style="position: absolute; top: 70px; left: 15px; width: 100%; max-width: 100%;">
        <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 65px; border-radius: 12px 12px 0 0;"><span style="font-size: 24px; font-weight: 600;">
            <div id="breadcrumbs">
                <ul>
                    <li><h1 class="regions-title">{{ group.name }}</h1></li>
                </ul>
                <div class="pull-right hidden-xs" style="margin-top: -5px;">
                    <a style="{{ subnav_info_style }}" class="region-header-sub-links" href="/challenges/{{ group.slug }}/">Info</a><a style="{{ subnav_map_style }}" class="region-header-sub-links" href="/challenges/{{ group.slug }}/map/">Map</a><a style="{{ subnav_peaks_style }}" class="region-header-sub-links" href="/challenges/{{ group.slug }}/peaks/">Peaks</a><a style="{{ subnav_challenges_style }}" class="region-header-sub-links" href="/challenges/{{ group.slug }}/summits/">Climbs</a><a style="{{ subnav_summits_style }}" class="region-header-sub-links" href="/challenges/{{ group.slug }}/members/">Members</a>
                </div>
            </div>
        </span></div>
    </div>
{% endblock %}

{% block content %}

<style>

    body {
        overflow: hidden;
    }

    #gm-custom-mapregiondropdown::-webkit-scrollbar-track
    {
      -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);
      border-radius: 10px;
      background-color: #F5F5F5;
    }

    #gm-custom-mapregiondropdown::-webkit-scrollbar
    {
      width: 6px;
      background-color: #F5F5F5;
    }

    #gm-custom-mapregiondropdown::-webkit-scrollbar-thumb
    {
      border-radius: 10px;
      -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
      background-color: #555;
    }

   body.explore div#main {
        margin-top: -2px;
    }
    #content-holder {
        background-image: none;
    }
    .gm-style-mtc {
        opacity: .8;
    }

    .switch {
        position: relative;
        display: inline-block;
        width: 46px;
        height: 25px;
        margin-bottom: 0px;
        margin-top: 5px;
    }

    .switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        -webkit-transition: .4s;
        transition: .4s;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 17px;
        width: 17px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        -webkit-transition: .4s;
        transition: .4s;
    }

    input:checked + .slider {
        background-color: #2196F3;
    }

    input:focus + .slider {
        box-shadow: 0 0 1px #2196F3;
    }

    input:checked + .slider:before {
        -webkit-transform: translateX(20px);
        -ms-transform: translateX(20px);
        transform: translateX(20px);
    }

    /* Rounded sliders */
    .slider.round {
        border-radius: 25px;
    }

    .slider.round:before {
        border-radius: 50%;
    }

    .blueimp-gallery > .description {
        position: absolute;
        bottom: 0px;
        width: 100%;
        text-align: center;
        color: #fff;
        margin-bottom: 2%;
        height: auto;
        display: none;
    }
    .blueimp-gallery-controls > .description {
        display: block;
    }
    .blueimp-gallery-controls > .description > .description-text {
        padding: 10px;
        background: rgba(0, 0, 0, 0.5);
    }

    #divSortPeaksElevation:hover {
        background-color: #ccc;
    }

    .marker-photo-icon {
        cursor: pointer;
        box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px 0px;
    }

    .filtered > i {
        color: #f24000;
    }

    .unfiltered > i {
        color: #333;
    }

    #peak-search-mobile > i {
        line-height: 30px;
        margin-left: 9px;
        font-size: 12px;
    }

    #peak-search-layers > i {
        line-height: 30px;
        margin-left: 7px;
        color: #333;
        font-size: 16px;
    }

    #peak-search-legend > i {
        line-height: 30px;
        margin-left: 6px;
        font-size: 18px;
        color: #333;
    }

    #gm-custom-mapfilter, #gm-custom-maplayers, #gm-custom-maplegend {
        -moz-box-shadow: 0 0 2px rgba(0,0,0,.1);
        -webkit-box-shadow: 0 0 2px rgba(0,0,0,.1);
        box-shadow: 0 0 0 2px rgba(0,0,0,.1);
    }

    #peak-search-3d {
        cursor: pointer;
        font-size: 16px;
        color: #333;
        font-weight: 700;
        line-height: 30px;
        text-align: center;
        margin-left: 4px;
    }

    @media screen and (min-width: 1024px) {
        #gm-custom-maplegend {
            bottom: 233px;
        }
        #gm-custom-map3d {
            bottom: 278px;
        }
    }

    @media screen and (min-width: 1px) and (max-width: 1023px) {
        #gm-custom-maplegend {
            top: 100px;
        }
        #gm-custom-map3d {
            top: 145px;
        }
    }

    .mapboxgl-ctrl {
        margin-bottom: 15px !important;
    }

    #gm-custom-mapdropdown, #gm-custom-mapbutton {
        opacity: 1;
        webkit-backdrop-filter: blur(5px);
        backdrop-filter: blur(5px);
    }

    #gm-custom-mapunits:hover {
        background-color: transparent !important;
    }

    #gm-custom-mapbutton {
        border: 2px solid rgba(0,0,0,0.15);
    }

    #gm-custom-mapdropdown {
        box-shadow: rgba(0, 0, 0, 0.3) 0px 1px 8px 0px;
    }

    #gm-custom-mapbutton {
        width: 180px;
        margin-left: 90px;
        border-top-right-radius: 8px;
        border-top-left-radius: 8px;
        border-bottom-right-radius: 8px;
        border-bottom-left-radius: 8px;
    }

    #gm-custom-mapbutton:hover {
        border-bottom-right-radius: 0px;
        border-bottom-left-radius: 0px;
    }

    #gm-custom-mapdropdown {
        width: 179px;
        margin-left: 93px;
        border-bottom-right-radius: 8px;
        border-bottom-left-radius: 8px;
    }

    #gm-custom-mapoption-terrain, #gm-custom-mapoption-natatl, #gm-custom-mapoption-outdoors, #gm-custom-mapoption-streets, #gm-custom-mapoption-topo, #gm-custom-mapoption-satstreets, #gm-custom-mapoption-sat {
        width: 176px;
        margin-left: -2px;
        border-bottom: solid 1px #f2f2f2;
    }

    .gm-custom-mapoption-region {
        width: 260px;
        margin-left: -1px;
        border-bottom: solid 1px #f2f2f2;
    }

    #gm-custom-mapoption-3d {
        width: 176px;
        margin-left: -2px;
        border-bottom: solid 1px #f2f2f2;
    }

    #gm-custom-mapoption-3d {
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
    }

    #gm-custom-mapoption-streets:hover {
        background-color: rgb(235, 235, 235) !important;
        color: rgb(0, 0, 0) !important;
    }

    .gm-custom-mapoption-region:hover {
        background-color: #ebebeb!important;
        color: #000!important;
    }

    #search-peaks-btn[disabled] {
        -webkit-text-fill-color: #fff;
        color: #fff;
    }

   @media screen and (max-width: 767px) and (min-width: 1px) {
       #gm-custom-mapregiondropdown {
            right: -315px;
           top: 30px;
           max-height: 480px;
       }
       #content-body {
           margin-top: 20px;
       }
       #peaks-map {
           top: 0px;
       }
       #gm-custom-maptype {
           right: 46% !important;
       }
       .mapboxgl-ctrl-bottom-right {
            bottom: 20px;
        }
       .mapboxgl-ctrl-geolocate, .mapboxgl-ctrl-fullscreen {
           width: 36px !important;
           height: 36px !important;
       }
       #gm-custom-maplegend, #gm-custom-maplayers, #gm-custom-mapfilter, #gm-custom-map3d {
           width: 36px !important;
           height: 36px !important;
           padding-top: 3px;
           padding-left: 3px;
       }
   }
    @media screen and (min-width: 768px) {
        #gm-custom-mapregiondropdown {
            right: -531px;
            top: 52px;
           max-height: 640px;
       }
        #peak-search {
            top: 0px;
        }
        #gm-custom-maplegend-dropdown {
            width: 320px !important;
            left: 20px !important;
        }
    }

    @media screen and (max-width: 767px) and (min-width: 1px) {
        #peaks-map {
            top: 99px;
        }
    }
   @media screen and (max-width: 1023px) and (min-width: 768px) {
        #peaks-map {
            top: 49px;
        }
    }
   @media screen and (max-width: 767px) and (min-width: 1px) {
       #content-body {
           margin-top: 20px;
       }
       #mobile-peak-search {
           top: 50px;
       }
   }
    @media screen and (min-width: 768px) {
       #mobile-peak-search {
           top: 0px;
       }
        #peak-search {
            top: 14px;
        }
   }

    @media screen and (min-width: 1024px) {
        div#explore .leftCol {
            margin-top: -14px;
        }

        #peaks-map {
            top: 0px;
        }

        #collapse-search {
            top: 0px;
        }

        #gm-custom-mapunits {
            right: 172px;
        }
    }

    .map-tooltip-info {
        background-color: rgba(0,0,0,.6);
        -webkit-backdrop-filter: blur(0px) !important;
    }

    .toggle-switch {height: 40px;}

    .toggle-switch label {line-height: 40px; color: #666;}

    div#explore .leftCol {
        background-color: #fff;
    }


    div#length-slider-range {
        -webkit-border-radius: 4px;
        -moz-border-radius: 4px;
        border-radius: 4px;
        height: 2px;
        overflow: visible;
        background-color: #ccc;
    }

    #length-slider {
        padding-top: 0px;
        margin-left: 8px;
    }

    div#vertical-slider-range {
        -webkit-border-radius: 4px;
        -moz-border-radius: 4px;
        border-radius: 4px;
        height: 2px;
        overflow: visible;
        background-color: #ccc;
    }

    #vertical-slider {
        padding-top: 8px;
        margin-left: 8px;
    }

    div#last-climbed-slider-range {
        -webkit-border-radius: 4px;
        -moz-border-radius: 4px;
        border-radius: 4px;
        height: 2px;
        overflow: visible;
        background-color: #ccc;
    }

    #last-climbed-slider {
        padding-top: 8px;
        margin-left: 8px;
    }

    #last-climbed-slider-range > .noUi-base > .noUi-origin > .noUi-handle-upper {
        display: none;
    }

    #summits-slider, #elevation-slider, #prominence-slider, #difficulty-slider {
        padding-top: 8px;
    }

    .noUi-connect {
        background: #ccc;
    }

    .marker_icon:hover, .marker_icon_red:hover, .marker_icon_green:hover, .marker_icon_redgreen:hover, .marker_icon_yellow:hover, .marker_icon_purple:hover, .marker_icon_peak:hover, .marker-icon-hover {
        background-image: url('{% static 'img/<EMAIL>' %}') !important;
        height: 28px;
        width: 28px;
        -webkit-animation-name: markerPulse;
        -webkit-animation-duration: 3s;
        -webkit-animation-iteration-count: infinite;
    }

    .marker_icon_classic {
        background-image: url({% static '' %}img/<EMAIL>);
        height: 28px;
        width: 28px;
    }

    .marker_icon_firstascent {
        background-image: url({% static '' %}img/<EMAIL>);
        height: 28px;
        width: 28px;
    }

    .marker_icon_kom {
        background-image: url({% static '' %}img/<EMAIL>);
        height: 28px;
        width: 28px;
    }

    .marker_icon_steward {
        background-image: url({% static '' %}img/<EMAIL>);
        height: 28px;
        width: 28px;
    }

    @-webkit-keyframes markerPulse {
        from { -webkit-filter: brightness(1.2) saturate(1.5); }
        50% { -webkit-filter: brightness(0.9) saturate(1); }
        to { -webkit-filter: brightness(1.2) saturate(1.5); }
    }

    @media screen and (min-width: 769px) {
        .noUi-horizontal .noUi-handle {
            width: 35px;
            height: 35px;
            left: -24px;
            top: -17px;
            background-color: transparent;
            background-size: contain;
        }
    }

    .noUi-horizontal .noUi-handle {
        background-image: url({% static '' %}img/search-drag-handle-grey.png);
    }

    /*tooltip animations*/
    .scale-in-tl{
        -webkit-animation: scale-in-tl .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        animation: scale-in-tl .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        opacity: 1;
    }

    @-webkit-keyframes scale-in-tl {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
        opacity: 1;
      }
    }
    @keyframes scale-in-tl {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
        opacity: 1;
      }
    }

    .scale-out-tl {
        -webkit-animation: scale-out-tl .1s cubic-bezier(0.3, .05, .5, .1) both;
        animation: scale-out-tl .1s cubic-bezier(0.3, .05, .5, .1) both;
        opacity: 0;
    }

    @-webkit-keyframes scale-out-tl {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
        opacity: 1;
      }
    }
    @keyframes scale-out-tl {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 0% 0%;
                transform-origin: 0% 0%;
        opacity: 1;
      }
    }

    .scale-in-tm{
        -webkit-animation: scale-in-tm .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        animation: scale-in-tm .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        opacity: 1;
    }

    @-webkit-keyframes scale-in-tm {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
        opacity: 1;
      }
    }
    @keyframes scale-in-tm {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
        opacity: 1;
      }
    }

    .scale-out-tm {
        -webkit-animation: scale-out-tm .1s cubic-bezier(0.3, .05, .5, .1) both;
        animation: scale-out-tm .1s cubic-bezier(0.3, .05, .5, .1) both;
        opacity: 0;
    }

    @-webkit-keyframes scale-out-tm {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
        opacity: 1;
      }
    }
    @keyframes scale-out-tm {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 50% 0%;
                transform-origin: 50% 0%;
        opacity: 1;
      }
    }

    .scale-in-tr{
        -webkit-animation: scale-in-tr .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        animation: scale-in-tr .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        opacity: 1;
    }

    @-webkit-keyframes scale-in-tr {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
        opacity: 1;
      }
    }
    @keyframes scale-in-tr {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
        opacity: 1;
      }
    }

    .scale-out-tr {
        -webkit-animation: scale-out-tr .1s cubic-bezier(0.3, .05, .5, .1) both;
        animation: scale-out-tr .1s cubic-bezier(0.3, .05, .5, .1) both;
        opacity: 0;
    }

    @-webkit-keyframes scale-out-tr {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
        opacity: 1;
      }
    }
    @keyframes scale-out-tr {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 100% 0%;
                transform-origin: 100% 0%;
        opacity: 1;
      }
    }

    /*bottom*/
    .scale-in-bl{
        -webkit-animation: scale-in-bl .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        animation: scale-in-bl .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        opacity: 1;
    }

    @-webkit-keyframes scale-in-bl {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
        opacity: 1;
      }
    }
    @keyframes scale-in-bl {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
        opacity: 1;
      }
    }

    .scale-out-bl {
        -webkit-animation: scale-out-bl .1s cubic-bezier(0.3, .05, .5, .1) both;
        animation: scale-out-bl .1s cubic-bezier(0.3, .05, .5, .1) both;
        opacity: 0;
    }

    @-webkit-keyframes scale-out-bl {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
        opacity: 1;
      }
    }
    @keyframes scale-out-bl {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 0% 100%;
                transform-origin: 0% 100%;
        opacity: 1;
      }
    }

    .scale-in-bm{
        -webkit-animation: scale-in-bm .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        animation: scale-in-bm .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        opacity: 1;
    }

    @-webkit-keyframes scale-in-bm {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
        opacity: 1;
      }
    }
    @keyframes scale-in-bm {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
        opacity: 1;
      }
    }

    .scale-out-bm {
        -webkit-animation: scale-out-bm .1s cubic-bezier(0.3, .05, .5, .1) both;
        animation: scale-out-bm .1s cubic-bezier(0.3, .05, .5, .1) both;
        opacity: 0;
    }

    @-webkit-keyframes scale-out-bm {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
        opacity: 1;
      }
    }
    @keyframes scale-out-bm {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 50% 100%;
                transform-origin: 50% 100%;
        opacity: 1;
      }
    }

    .scale-in-br{
        -webkit-animation: scale-in-br .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        animation: scale-in-br .1s cubic-bezier(0.50, 1.3, .70, 1) both;
        opacity: 1;
    }

    @-webkit-keyframes scale-in-br {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
        opacity: 1;
      }
    }
    @keyframes scale-in-br {
      0% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
        opacity: 1;
      }
    }

    .scale-out-br {
        -webkit-animation: scale-out-br .1s cubic-bezier(0.3, .05, .5, .1) both;
        animation: scale-out-br .1s cubic-bezier(0.3, .05, .5, .1) both;
        opacity: 0;
    }

    @-webkit-keyframes scale-out-br {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
        opacity: 1;
      }
    }
    @keyframes scale-out-br {
      0% {
        -webkit-transform: scale(1);
                transform: scale(1);
        -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
        opacity: 1;
      }
      100% {
        -webkit-transform: scale(0);
                transform: scale(0);
        -webkit-transform-origin: 100% 100%;
                transform-origin: 100% 100%;
        opacity: 1;
      }
    }

    .mapboxgl-ctrl-attrib {display: none !important;}

    #no-peaks-in-list {
        display: flex;
        justify-content: center;
        flex-direction: column;
        height: 100%;
        text-align: center;
        color: #f24000;
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
    }

    #collapse-search:hover, #collapse-peaklist:hover {
        background-color: #F96E30;
    }
    #collapse-search, #collapse-peaklist {
        background-color: #EC8157;
    }

    .peak-listitem-footer {
        box-shadow: 0 1px 8px 0 rgba(0,0,0,0.30);
    }

    @media screen and (max-width: 1023px) and (min-width: 1px) {
        #collapse-peaklist {
            display: none;
        }
    }

</style>

    <div id="explore" class="peak_list_cont clearfix">
        <div id="mobile-collapse-nav" style="display: none; position: absolute; left: 0px; top: -35px; z-index: 9999;"></div>
        <div class="full-width-container" style="width: 100%; height: inherit;">
        <div class="row peak-list-content" style="min-height: 100px;">
        <!-- Mobile header -->
        <div class="row sub-header-row hidden-lg hidden-md hidden-sm" style="height: 50px; position: fixed; width: 100%; z-index: 2; margin-left: 0px;">
            <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999; display: flex; justify-content: space-between;">
                <a style="{{ subnav_info_style }}" class="mobile-header-sub-links" href="/challenges/{{ group.slug }}/">Info</a><a style="{{ subnav_map_style }}" class="mobile-header-sub-links" href="/challenges/{{ group.slug }}/map/">Map</a><a style="{{ subnav_peaks_style }}" class="mobile-header-sub-links" href="/challenges/{{ group.slug }}/peaks/">Peaks</a><a style="{{ subnav_challenges_style }}" class="mobile-header-sub-links" href="/challenges/{{ group.slug }}/summits/">Summits</a><a style="{{ subnav_summits_style }}" class="mobile-header-sub-links" href="/challenges/{{ group.slug }}/members/">Members</a>
            </div>
        </div>
        <!-- End mobile header -->
        <table class="hidden-lg hidden-md" id="mobile-peak-search" style="width: 100%; position: relative;">
            <tr style="background-color: #f1f1f1;">
                <td id="mobile-search-title" style="padding-left: 80px; height: 49px; width: 80%; vertical-align: middle; text-align: center; font-size: 12px;">{% if q and n %}"{{ q }}" near {{ n }}{% elif q %}"{{ q }}"{% elif n %}Peaks near {{ n }}{% else %}All peaks{% endif %}</td>
                <td style="height: 49px; vertical-align: middle; font-size: 20px; text-align: right; padding-right: 15px;"><a id="peak-search-mobile" style="cursor: pointer;"><i class="fa fa-filter"></i></a></td>
            </tr>
        </table>
        <div id="collapse-search" style="position: absolute; top: 10px; left: 240px; height: 67px; width: 30px; z-index: 100; text-align: center; padding-top: 25px; cursor: pointer; color: #fff; box-shadow: 1px 2px 5px 0 rgba(0,0,0,0.30); border-radius: 0 8px 8px 0;" onclick="collapseSearch();"><i class="fa fa-chevron-left"></i></div>
        <div id="peak-search" tabindex="-1" class="hidden-xs hidden-sm col_search leftCol" style="position: absolute; width: 240px; height: 100%; z-index: 999">
            <div class="wrp clearfix">
                <div class="block">

                    {% if showing_user_peaks %}
                        <h3 class="blue" style="font-size: 25px; margin: 0px;padding: 10px 10px 0px 10px;text-align: center; height:20px;">
                            <a style="text-decoration: none;" href="{{ userinfo.get_profile.get_absolute_url }}">{{ userinfo.username }}'s</a></h3>
                        <h4 style="text-align: center;margin-top: 5px;">summited peaks</h4>
                    {% endif %}
                    {% if showing_list_peaks %}
                        <h3 class="blue" style="font-size: 18px; margin: 0px;padding: 10px 10px 10px 10px;text-align: center; height:20px;">
                            <a style="text-decoration: none;" href="{{ group.get_absolute_url }}">{{ group.name }}</a></h3>
                    {% endif %}
                    {% if showing_country_peaks %}
                        <h3 class="blue" style="font-size: 18px; margin: 0px;padding: 10px 10px 10px 10px;text-align: center; height:20px;">
                            <a style="text-decoration: none;" href="{{ country.get_absolute_url }}">{% if region_name %}{{region_name}}, {% endif %}{{ country.name }}</a></h3>
                    {% endif %}

                    <form id="explore" method="get" action="/peaks">

                        <meta charset="utf-8">

                        <style>
                            .elevation_slider {float: left; width: 100%; padding: 5%;}
                            #slider-range #prominence-slider-range #summits-slider-range #difficulty-slider-range #length-slider-range #vertical-slider-range #last-climbed-slider-range .ui-widget-header {background: #1f60f6;}
                            #slider-range #prominence-slider-range #summits-slider-range #difficulty-slider-range #length-slider-range #vertical-slider-range #last-climbed-slider-range .ui-slider-range {background: #1f60f6;}
                        </style>

                        <input type="text" id="hdnElevMin" hidden value="">
                        <input type="text" id="hdnElevMax" hidden value="">
                        <input type="text" id="hdnPromMin" hidden value="">
                        <input type="text" id="hdnPromMax" hidden value="">
                        <input type="text" id="hdnSummitsMin" hidden value="">
                        <input type="text" id="hdnSummitsMax" hidden value="">
                        <input type="text" id="hdnDifficultyMin" hidden value="">
                        <input type="text" id="hdnDifficultyMax" hidden value="">

                        <input type="text" id="hdnLengthMin" hidden value="">
                        <input type="text" id="hdnLengthMax" hidden value="">
                        <input type="text" id="hdnVerticalMin" hidden value="">
                        <input type="text" id="hdnVerticalMax" hidden value="">
                        <input type="text" id="hdnLastClimbedMin" hidden value="">
                        <input type="text" id="hdnLastClimbedMax" hidden value="">

                        <input type="text" id="hdnClassics" hidden value="">
                        <input type="text" id="hdnInChallenge" hidden value="">
                        <input type="text" id="hdnYouClimbed" hidden value="">
                        <input type="text" id="hdnKeyword" hidden value="">
                        <input type="text" id="hdnNear" hidden value="">
                        <input type="text" id="hdnLat" hidden value="">
                        <input type="text" id="hdnLng" hidden value="">
                        <input type="text" id="hdnBounds" hidden value="">
                        <input type="text" id="hdnNearQuery" hidden value="false">

                        <div id="map-tags" style="margin-top: 10px;">
                            <div id="map-tag-choices">
                                <div class="toggle-switch" style="border: none; width: 105px; left: -3px; margin-bottom: 8px;"><input class="map-tag-input" type="checkbox" id="chkMapTagsClassics"><label style="border-radius: 12px !important;">Classics</label></div>
                                <div class="toggle-switch" style="border: none; width: 105px; left: -12px; margin-bottom: 8px;"><input class="map-tag-input" type="checkbox" id="chkMapTagsInChallenge" value="inchallenges"><label style="border-radius: 12px !important;">In Challenge</label></div>
                                <div class="toggle-switch" style="border: none; width: 105px; left: -3px;"><input class="map-tag-input" type="checkbox" id="chkMapTagsYouClimbed" value="youclimbed"><label style="border-radius: 12px !important;"><i class="fas fa-check"></i> You</label></div>
                                <div class="toggle-switch" style="border: none; width: 105px; left: -12px;"><input class="map-tag-input" type="checkbox" id="chkMapTagsYouNotClimbed" value="younotclimbed"><label style="border-radius: 12px !important;"><i class="fas fa-ban" style="padding-right: 0.2em;"></i>You</label></div>
                            </div>
                        </div>

                        <div id="length-slider" class="elevation_slider">
                            <div style="font-size: 14px; margin-left: -8px;">
                                <div id="length-label" style="float: left; font-size: 12px; color: #aaa;">Length</div>
                                <div class="pull-right" style="margin-top: -6px; margin-right: 5px;">
                                    <input type="text" disabled value="0  to  20+ mi" class="length-ammount" id="length-amount" style="padding-right: 5px; border:0; font-weight:300; width: 120px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                    <input type="text" disabled value="0  to  32+ km" class="length-ammount" id="length-amount-meters" style="display: none; padding-right: 0px; border:0; font-weight:300; width: 120px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                    <a href="#" style="display: none; margin-left: 5px; text-decoration: underline; font-size: 12px; float: right; height: 35px; line-height: 3;" id ='length_bt_showinmeters'></a>
                                </div>
                            </div>
                            <div id="length-slider-range" style="margin-left: -9px; margin-top: 0px; width: 218px; float: left;"></div>
                        </div>

                        <div id="vertical-slider" class="elevation_slider">
                            <div style="font-size: 14px; margin-left: -8px;">
                                <div id="vertical-label" style="float: left; font-size: 12px; color: #aaa;">Vertical gain</div>
                                <div class="pull-right" style="margin-top: -6px; margin-right: 5px;">
                                    <input type="text" disabled value="0  to  10,000+ ft" class="vertical-ammount" id="vertical-amount" style="padding-right: 5px; border:0; font-weight:300; width: 140px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                    <input type="text" disabled value="0  to  3000+ m" class="vertical-ammount" id="vertical-amount-meters" style="display: none; padding-right: 0px; border:0; font-weight:300; width: 120px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                    <a href="#" style="display: none; margin-left: 5px; text-decoration: underline; font-size: 12px; float: right; height: 35px; line-height: 3;" id ='vertical_bt_showinmeters'></a>
                                </div>
                            </div>
                            <div id="vertical-slider-range" style="margin-left: -9px; margin-top: 0px; width: 218px; float: left;"></div>
                        </div>

                        <div id="summits-slider" class="elevation_slider">
                            <div style="font-size: 14px; margin-left: -8px;">
                                <div id="summits-label" style="float: left; font-size: 12px; color: #aaa;">Popularity</div>
                                <div class="pull-right" style="margin-top: -6px; margin-right: 5px;">
                                    <input type="text" disabled value="0  to  500+ climbs" class="summits-ammount" id="summits-amount" style="padding-right: 5px; border:0; font-weight:300; width: 150px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                </div>
                            </div>
                            <div id="summits-slider-range" style="margin-left: -9px; margin-top: 0px; width: 218px; float: left;"></div>
                        </div>

                        <div id="last-climbed-slider" class="elevation_slider">
                            <div style="font-size: 14px; margin-left: -8px;">
                                <div id="last-climbed-label" style="float: left; font-size: 12px; color: #aaa;">Last climbed</div>
                                <div class="pull-right" style="margin-top: -6px; margin-right: 5px;">
                                    <input type="text" disabled value="any time" class="last-climbed-ammount" id="last-climbed-amount" style="padding-right: 5px; border:0; font-weight:300; width: 120px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                </div>
                            </div>
                            <div id="last-climbed-slider-range" style="margin-left: -9px; margin-top: 0px; width: 218px; float: left;"></div>
                        </div>

                        <div id="elevation-slider" class="elevation_slider">
                            <div style="font-size: 14px; margin-left: -8px;">
                                <div id="elevation-label" style="float: left; font-size: 12px; color: #aaa;">Elevation</div>
                                <div class="pull-right" style="margin-top: -6px; margin-right: 5px;">
                                    <input type="text" disabled value="0  to  29,500 ft" class="ammount" id="amount" style="padding-right: 0px; border:0; font-weight:300; width: 130px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                    <input type="text" disabled value="0  to  9,000 m" class="ammount" id="amount-meters" style="display: none; padding-right: 0px; border:0; font-weight:300; width: 130px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                    <a href="#" style="display: none; margin-left: 5px; text-decoration: underline; font-size: 12px; float: right; height: 35px; line-height: 3;" id ='bt_showinmeters'></a>
                                </div>
                            </div>
                            <div id="slider-range" style="margin-left: -9px; margin-top: 0px; width: 218px; float: left;"></div>
                        </div>

                        <div id="prominence-slider" class="elevation_slider">
                            <div style="font-size: 14px; margin-left: -8px;">
                                <div id="prominence-label" style="float: left; font-size: 12px; color: #aaa;">Prominence</div>
                                <div class="pull-right" style="margin-top: -6px; margin-right: 5px;">
                                    <input type="text" disabled value="0  to  29,500 ft" class="prominence-ammount" id="prominence-amount" style="padding-right: 0px; border:0; font-weight:300; width: 130px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                    <input type="text" disabled value="0  to  9,000 m" class="prominence-ammount" id="prominence-amount-meters" style="display: none; padding-right: 0px; border:0; font-weight:300; width: 130px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                    <a href="#" style="display: none; margin-left: 5px; text-decoration: underline; font-size: 12px; float: right; height: 35px; line-height: 3;" id ='prominence_bt_showinmeters'></a></div>
                            </div>
                            <div id="prominence-slider-range" style="margin-left: -9px; margin-top: 0px; width: 218px; float: left;"></div>
                        </div>

                        <div id="difficulty-slider" class="elevation_slider">
                            <div style="font-size: 14px; margin-left: -8px;">
                                <div id="difficulty-label" style="float: left; font-size: 12px; color: #aaa;">Difficulty</div>
                                <div class="pull-right" style="margin-top: -6px; margin-right: 5px;">
                                    <input type="text" disabled value="Class 1  to  5" class="difficulty-ammount" id="difficulty-amount" style="padding-right: 5px; border:0; font-weight:300; width: 120px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                </div>
                            </div>
                            <div id="difficulty-slider-range" style="margin-left: -9px; margin-top: 0px; width: 218px; float: left;"></div>
                        </div>

                        <div class="row">
                          <div class="col-md-12" style="text-align: center; color: #c0c0c0; margin-top: 0px; margin-bottom: 5px;">
                            <div style="float: left; margin: 5px; padding-left: 2px;"><img style="width: 21px;" src="{% static 'img/<EMAIL>' %}"></div>
                            <div style="float: left; font-size: 10px; padding-top: 7px; margin-right: 3px;">Highest</div>
                            <div style="float: left; margin: 5px;"><img style="width: 21px;" src="{% static 'img/<EMAIL>' %}"></div>
                            <div style="float: left; font-size: 10px; width: 48px; text-align: left; line-height: 12px; padding-top: 5px;">Your summits</div>
                            <div style="float: left; margin: 5px;"><img style="width: 21px;" src="{% static 'img/<EMAIL>' %}"></div>
                            <div style="float: left; font-size: 10px; width: 48px; text-align: left; line-height: 12px; padding-top: 5px;">Your attempts</div>
                          </div>
                        </div>

                    </form>

                </div><!-- END block -->
            </div>
        </div><!-- END col_1 -->
        <div id="collapse-peaklist" class="hidden-xs" style="position: absolute; top: 10px; right: 240px; height: 67px; width: 30px; z-index: 100; text-align: center; padding-top: 25px; cursor: pointer; color: #fff; box-shadow: 1px 2px 5px 0 rgba(0,0,0,0.30); border-radius: 8px 0 0 8px;" onclick="collapsePeakList();"><i class="fa fa-chevron-right"></i></div>
        <div id="peak-list" tabindex="-1" class="hidden-xs hidden-sm col_search" style="overflow-y: auto; position: absolute; right: 0px; width: 240px; height: 100%; z-index: 999; background-color: #F2F2F2; box-shadow: -1px 1px 8px 0 rgba(0,0,0,0.30)">
        </div><!-- END col_2 -->
        <div id="peaks-map" class="col_data rightCol">
            <div id="map-canvas" style="width: 100%; height: 100%;">
                <div id="gm-custom-mapunits" class="gmnoprint gm-style-mtc" style="display: none; margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 100px; top: 0px;">
                    <div id="gm-custom-mapunitsbutton" draggable="false" title="Change map units" style="cursor: pointer; direction: ltr; overflow: hidden; text-align: center; position: relative; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 11px; -webkit-background-clip: padding-box; background-clip: padding-box; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; font-weight: 500;">
                        <span id="gm-custom-mapunitsbutton-label-feet" class="gm-custom-mapunits-selected">Feet</span> | <span id="gm-custom-mapunitsbutton-label-meters" class="gm-custom-mapunits-unselected">Meters</span>
                    </div>
                </div>
                <div id="gm-custom-map3d" class="gmnoprint gm-style-mtc" style="display: none; margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 30px; height: 30px; border-radius: 5px; right: 0px; background-color: #fff; opacity: 1;">
                    <a id="peak-search-3d" style="cursor: pointer;">3D</a>
                </div>
                <div id="gm-custom-maplegend" class="gmnoprint gm-style-mtc" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 30px; height: 30px; border-radius: 5px; right: 0px; background-color: #fff; opacity: 1;">
                    <a id="peak-search-legend" style="cursor: pointer;"><i style="margin-left: 5px;" class="fas fa-eye"></i></a>
                </div>
                <div id="gm-custom-mapfilter" class="gmnoprint gm-style-mtc hidden-lg hidden-md" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 30px; height: 30px; border-radius: 5px; top: 5px; right: 0px; background-color: #fff; opacity: 1;">
                    <a id="peak-search-mobile" style="cursor: pointer;"><i class="fa fa-filter"></i></a>
                </div>
                <div id="gm-custom-maplayers" class="gmnoprint gm-style-mtc hidden-lg hidden-md" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 30px; height: 30px; border-radius: 5px; top: 53px; right: 0px; background-color: #fff; opacity: 1;">
                    <a id="peak-search-layers" style="cursor: pointer;"><i class="fas fa-layer-group"></i></a>
                </div>
                <div id="gm-custom-maptype" class="gmnoprint" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 180px; right: 50%; top: 0px;">
                    <div id="gm-custom-mapbutton" class="hidden-xs hidden-sm" draggable="false" style="cursor: pointer; direction: ltr; overflow: hidden; text-align: left; position: relative; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); height: 54px; padding: 8px; -webkit-background-clip: padding-box; background-clip: padding-box; font-weight: 500;">
                        <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Terrain</div>
                    </div>
                </div>
                <div id="gm-custom-mapdropdown-container" class="gmnoprint" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; right: 50%; margin-right: 191px; top: 0px;">
                    <div id="gm-custom-mapdropdown" style="z-index: 10; padding-left: 2px; padding-right: 2px; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; position: absolute; top: 50px; left: 0px; right: 0px; text-align: left; display: none;">
                        <div id="gm-custom-mapoption-terrain" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Terrain</div>
                        </div>
                        <div id="gm-custom-mapoption-natatl" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Natural Atlas <span style="font-size: 10px;">(US)</span></div>
                        </div>
                        <div id="gm-custom-mapoption-outdoors" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">OpenTopoMap</div>
                        </div>
                        <div id="gm-custom-mapoption-streets" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Streets</div>
                        </div>
                        <div id="gm-custom-mapoption-topo" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500; width: 177px; border-right: 1px solid #aaa;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Government Topo <span style="font-size: 10px;">&nbsp;<i class="fa fa-caret-right" aria-hidden="true"></i></span></div>
                        </div>
                        <div id="gm-custom-mapoption-satstreets" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite</div>
                        </div>
                        <div id="gm-custom-mapoption-sat" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite Topo</div>
                        </div>
                        <div class="" id="gm-custom-mapoption-3d" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">3D Map</div>
                        </div>
                    </div>
                    <div id="gm-custom-mapregiondropdown" style="z-index: 10; padding-left: 0px; padding-right: 2px; position: absolute; text-align: left; overflow: hidden auto; display: none;">
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-argentina-ign-50k" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/ar.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Argentina (IGN - 50K)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-argentina-ign-100k" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/ar.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Argentina (IGN - 100K)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-australia-nsw" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/au.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Australia - NSW (SIX)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-australia-qld" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/au.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Australia - QLD (QTopo)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-australia-sa" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/au.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Australia - SA</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-australia-ts" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/au.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Australia - TAS (LIST)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-australia-vic" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/au.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Australia - VIC (VicMap)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-austria-bergfex" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/at.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Austria (BergFex)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-austria-bev" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/at.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Austria (BEV)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-belgium-ngi" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/be.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Belgium (NGI)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-brazil" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/br.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Brazil (IBGE)</div>
                                            </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-canada-caltopo" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/ca.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Canada (NRCAN)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-canada-on" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/ca.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Canada - ON (OBM)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-canada-qc" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/ca.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Canada - QC (MERN)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-croatia-dgu" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/hr.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Croatia (DGU)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-czechia-cuzk" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/cz.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Czechia (ČÚZK)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-finland-nls" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/fi.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Finland (NLS)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-france-ign" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/fr.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">France (IGN)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-germany-oa" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/de.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Germany (OutdoorActive)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-hongkong-landsd" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/hk.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Hong Kong (LandsD)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-iceland-caltopo" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/is.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Iceland (CalTopo)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-iceland-new" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/is.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Iceland (Landmælingar)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-israel-hikingosm" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/il.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Israel (Hiking OSM)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-japan-gsi" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/jp.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Japan (GSI)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-luxembourg" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/lu.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Luxembourg (ACT)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-mexico-inegi" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/mx.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Mexico (INEGI)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-newzealand-linz" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/nz.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">New Zealand (LINZ)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-norway-new" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/no.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Norway (Kartvertek)</div>
                                            </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-norway-kartverket" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/no.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Norway (Old Kartvertek)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-norway-janmayen" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/no.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Norway - Jan Mayen (NPI)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-norway-svalbard" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/no.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Norway - Svalbard (NPI)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-philippines-namria" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/ph.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Philippines (NAMRIA)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-poland-geoportal" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/pl.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Poland (Geoportal)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-slovakia-dgu" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/sk.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Slovakia (DGU)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-slovenia-prostor" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/si.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Slovenia (ProStor)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-southafrica-ngi-50k" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/za.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">South Africa NGI 50K</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-spain-ign" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/es.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Spain (IGN)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-spain-cataluna" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/es.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Spain - Cataluña (ICGC)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-sweden-sgu" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/se.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Sweden (SGU)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-switzerland-swisstopo" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/ch.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Switzerland (swisstopo)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-taiwan-nlsc" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/tw.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Taiwan (NLSC)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-uk-os" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/gb.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">United Kingdom (OS)</div>
                        </div>
                        <div class="gm-custom-mapoption-region" id="gm-custom-mapoption-us-caltopo" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/flags/us.svg"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">United States (USGS)</div>
                        </div>
                    </div>
                    <div id="gm-custom-maplegend-dropdown" style="z-index: 10; border: 2px solid rgba(0,0,0,0.15); position: absolute; width: 270px; top: 52px; left: 40px; right: 0px; text-align: left; border-radius: 8px; display: none;">
                        <div id="gm-custom-maplegend-highest" draggable="false" style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500; border-top-left-radius: 8px; border-top-right-radius: 8px;">
                            <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Highest peak on map</div>
                        </div>
                        <div id="gm-custom-maplegend-yoursummits" draggable="false" style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Climbed by you<div style="float: right; display: none;"><label class="switch"><input id="map-summits-toggle" type="checkbox"><span class="slider round"></span></label></div></div>
                        </div>
                        <div id="gm-custom-maplegend-yourattempts" draggable="false" style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Attempted by you<div style="float: right; display: none;"><label class="switch"><input id="map-attempts-toggle" type="checkbox"><span class="slider round"></span></label></div></div>
                        </div>
                        <div id="gm-custom-maplegend-unclimbed" draggable="false" style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Unclimbed by you<div style="float: right; display: none;"><label class="switch"><input id="map-unclimbed-toggle" type="checkbox"><span class="slider round"></span></label></div></div>
                        </div>
                        <div id="gm-custom-maplegend-yourkings" draggable="false" style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Your King of the Mountains<div style="float: right; {% if not request.user.is_authenticated %}display: none;{% endif %}"><label class="switch"><input id="map-kom-toggle" type="checkbox"><span class="slider round"></span></label></div></div>
                        </div>
                        <div id="gm-custom-maplegend-yourstewards" draggable="false" style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Your Summit Stewards<div style="float: right; {% if not request.user.is_authenticated %}display: none;{% endif %}"><label class="switch"><input id="map-stewards-toggle" type="checkbox"><span class="slider round"></span></label></div></div>
                        </div>
                        <div id="gm-custom-maplegend-yourfirstascents" draggable="false" style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Your First Ascents<div style="float: right; {% if not request.user.is_authenticated %}display: none;{% endif %}"><label class="switch"><input id="map-firstascents-toggle" type="checkbox"><span class="slider round"></span></label></div></div>
                        </div>
                        <div id="gm-custom-maplegend-classics" draggable="false" style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Classic peaks<div style="float: right;"><label class="switch"><input id="map-classics-toggle" type="checkbox"><span class="slider round"></span></label></div></div>
                        </div>
                        <div id="gm-custom-maplegend-challenges" draggable="false" style="border-bottom: solid 1px #f2f2f2; cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Peak Challenges<div style="float: right;"><label class="switch"><input id="map-challenges-toggle" type="checkbox"><span class="slider round"></span></label></div></div>
                        </div>
                        <div id="gm-custom-maplegend-photos" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500; border-bottom-left-radius: 8px; border-bottom-right-radius: 8px;">
                            <div style="float: left;"><img style="width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Photos<div style="float: right;"><label class="switch"><input id="map-photos-toggle" type="checkbox"><span class="slider round"></span></label></div></div>
                        </div>
                    </div>
                </div>
                <div id="message_map_div" class="gmnoprint gm-style-mtc" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 104px; right: 0px; top: 0px; display: none;">
                    <div id="message_map" draggable="false" style="direction: ltr; overflow: hidden; text-align: left; position: relative; color: rgb(86, 86, 86); font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 11px; border-bottom-left-radius: 2px; border-top-left-radius: 2px; border-bottom-right-radius: 2px; border-top-right-radius: 2px; -webkit-background-clip: padding-box; background-clip: padding-box; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; font-weight: 500;">

                    </div>
                </div>

            </div>
            <div id="marker-tooltip" data-url="" data-index="" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; cursor: pointer; opacity: 0; position:absolute; width: 200px; height: 150px; background: rgba(0, 0, 0, 0.3); margin: 15px;"></div>

        </div><!-- END col_2 -->
        </div>
        </div>
    </div><!-- END paek_list_cont -->

    <script type="text/javascript">

        var map;
        var mapCountry = '';
        var topo;
        var outdoors;
        var pageX, pageY, mapX, mapY;
        var iconstyle;

        $('#peak-list').on('mouseenter', '.hover-minicard', function() {
            $(this).find('.peak-listitem-footer').css('background-color','#fde1d6').css('box-shadow', '0 3px 8px 0 rgba(0,0,0,0.50)');
            var markerSelector = '#peak-marker-' + $(this).data('peakid');
            $('#peaks-map').find(markerSelector).addClass('marker-icon-hover');
        });

        $('#peak-list').on('mouseleave', '.hover-minicard', function() {
            $(this).find('.peak-listitem-footer').css('background-color','#fff').css('box-shadow', '0 1px 8px 0 rgba(0,0,0,0.30)');
            var markerSelector = '#peak-marker-' + $(this).data('peakid');
            $('#peaks-map').find(markerSelector).removeClass('marker-icon-hover');
        });

        $('#collapse-search').on('mouseenter', function() {
            $(this).find('i').css('font-size','18px');
        });

        $('#collapse-search').on('mouseleave', function() {
            $(this).find('i').css('font-size','16px');
        });

        $('#collapse-peaklist').on('mouseenter', function() {
            $(this).find('i').css('font-size','18px');
        });

        $('#collapse-peaklist').on('mouseleave', function() {
            $(this).find('i').css('font-size','16px');
        });

        $(function(){
//            Top-left nav button ("find peaks") width to be it same at the width of the leftCol:
            var leftColWidth = $('div#explore .leftCol').width();
            $('li.headlink').css('width', leftColWidth);
//            Peak name input needs to show the remove text icon when the user enter text on it:
            var a = $('a#clear_peak_name');
            var a2 = $('a#clear_near_location');

            var input = $('input#q');
            var input2 = $('input#n');

            if ( input.val() != "" ) {
                a.css('display', 'block');
            }

            if ( input2.val() != "" ) {
                a2.css('display', 'block');
            }

            input.keyup(function(){
                if( $(this).val() != "" ) {
                    a.css('display', 'block');
                } else {
                    a.css('display', 'none');
                }
            });

            input2.keyup(function(){
                if( $(this).val() != "" ) {
                    a2.css('display', 'block');
                } else {
                    a2.css('display', 'none');
                }
            });

            a.click(function(){
                input.val('');
                $(this).css('display', 'none');
            });

            a2.click(function(){
                input2.val('');
                $(this).css('display', 'none');
            });

            $('#peak-search').css({left: '0px'});

        });

        var timers = {};

        $('.toggle-switch').on('mouseenter', 'input', function() {
            var currentBackground = $(this).siblings('label').css('background-color');
            if (currentBackground == 'rgb(234, 234, 234)') {
                $(this).siblings('label').css('background-color','#ccc');
            } else if (currentBackground == 'rgb(238, 238, 238)') {
                $(this).siblings('label').css('background-color','#ccc');
            } else if (currentBackground == 'rgb(230, 230, 230)') {
                $(this).siblings('label').css('background-color','#ccc');
            } else {
                $(this).siblings('label').css('background-color','#ff4400');
            }
        });

        $('.toggle-switch').on('mouseleave', 'input', function() {
            var currentBackground = $(this).siblings('label').css('background-color');
            if (currentBackground == 'rgb(204, 204, 204)') {
                $(this).siblings('label').css('background-color','rgb(234, 234, 234)');
            } else if (currentBackground == 'rgb(238, 238, 238)') {
                $(this).siblings('label').css('background-color','rgb(234, 234, 234)');
            } else if (currentBackground == 'rgb(230, 230, 230)') {
                $(this).siblings('label').css('background-color','rgb(234, 234, 234)');
            } else {
                $(this).siblings('label').css('background-color','#f24100');
            }
        });

        $('#map-tag-choices').on('change', '.map-tag-input', function () {
            var color;
            if (this.id == 'chkMapTagsYouClimbed' || this.id == 'chkMapTagsYouNotClimbed') {
                {% if request.user.is_authenticated %}
                    if (this.id == 'chkMapTagsYouClimbed') {
                        color = "#10b025";
                    } else {
                        color = "#f24100";
                    }
                    //pass
                {% else %}
                    return;
                {% endif %}
            }
            if (this.checked) {
                $(this).next().animate({
                    backgroundColor: color,
                }, 200);
                $(this).next().css('color', '#fff');
            } else {
                $(this).next().animate({
                    backgroundColor: '#e6e6e6',
                }, 200);
                $(this).next().css('color', '#666');
            }
        });

        $('#chkMapTagsClassics').on('click', function () {
            delayShowClassics();
        });

        $('#chkMapTagsInChallenge').on('click', function () {
            delayShowInChallenge();
        });

        $('#chkMapTagsYouClimbed').on('click', function () {
            {% if request.user.is_authenticated %}
            delayShowYouClimbed();
            {% else %}
            $('#navbar-login-link').click();
            {% endif %}
        });

        $('#chkMapTagsYouNotClimbed').on('click', function () {
            {% if request.user.is_authenticated %}
                delayShowYouNotClimbed();
            {% else %}
                $('#navbar-login-link').click();
            {% endif %}
        });

        function hideMapTooltip() {
            if ($('#marker-tooltip').hasClass('scale-in-tl')) {
                $('#marker-tooltip').addClass('scale-out-tl').removeClass('scale-in-tl');
            } else if ($('#marker-tooltip').hasClass('scale-in-tm')) {
                $('#marker-tooltip').addClass('scale-out-tm').removeClass('scale-in-tm');
            } else if ($('#marker-tooltip').hasClass('scale-in-tr')) {
                $('#marker-tooltip').addClass('scale-out-tr').removeClass('scale-in-tr');
            } else if ($('#marker-tooltip').hasClass('scale-in-bl')) {
                $('#marker-tooltip').addClass('scale-out-bl').removeClass('scale-in-bl');
            } else if ($('#marker-tooltip').hasClass('scale-in-bm')) {
                $('#marker-tooltip').addClass('scale-out-bm').removeClass('scale-in-bm');
            } else if ($('#marker-tooltip').hasClass('scale-in-br')) {
                $('#marker-tooltip').addClass('scale-out-br').removeClass('scale-in-br');
            }
        }

        function hidePhotoTooltip() {
            if ($('#photo-tooltip').hasClass('scale-in-tl')) {
                $('#photo-tooltip').addClass('scale-out-tl').removeClass('scale-in-tl');
            } else if ($('#photo-tooltip').hasClass('scale-in-tm')) {
                $('#photo-tooltip').addClass('scale-out-tm').removeClass('scale-in-tm');
            } else if ($('#photo-tooltip').hasClass('scale-in-tr')) {
                $('#photo-tooltip').addClass('scale-out-tr').removeClass('scale-in-tr');
            } else if ($('#photo-tooltip').hasClass('scale-in-bl')) {
                $('#photo-tooltip').addClass('scale-out-bl').removeClass('scale-in-bl');
            } else if ($('#photo-tooltip').hasClass('scale-in-bm')) {
                $('#photo-tooltip').addClass('scale-out-bm').removeClass('scale-in-bm');
            } else if ($('#photo-tooltip').hasClass('scale-in-br')) {
                $('#photo-tooltip').addClass('scale-out-br').removeClass('scale-in-br');
            }
        }

        function hideChallengeTooltip() {
            if ($('#challenge-tooltip').hasClass('scale-in-tl')) {
                $('#challenge-tooltip').addClass('scale-out-tl').removeClass('scale-in-tl');
            } else if ($('#challenge-tooltip').hasClass('scale-in-tm')) {
                $('#challenge-tooltip').addClass('scale-out-tm').removeClass('scale-in-tm');
            } else if ($('#challenge-tooltip').hasClass('scale-in-tr')) {
                $('#challenge-tooltip').addClass('scale-out-tr').removeClass('scale-in-tr');
            } else if ($('#challenge-tooltip').hasClass('scale-in-bl')) {
                $('#challenge-tooltip').addClass('scale-out-bl').removeClass('scale-in-bl');
            } else if ($('#challenge-tooltip').hasClass('scale-in-bm')) {
                $('#challenge-tooltip').addClass('scale-out-bm').removeClass('scale-in-bm');
            } else if ($('#challenge-tooltip').hasClass('scale-in-br')) {
                $('#challenge-tooltip').addClass('scale-out-br').removeClass('scale-in-br');
            }
        }

        function collapseSearch() {
            if ($('#peak-search').css('left') == '0px') {
                $('#peak-search').animate({left: '-240px'});
                $('#collapse-search').animate({left: '0px'});
                $('#collapse-search').html('<i class="fa fa-filter"></i>');
                $('#peaks-map').animate({left: '0px'});
                var window_width = $(window).width();
                if ($('#peak-list').css('right') == '0px') {
                    window_width = window_width - 240;
                }
                $("div#map-canvas").animate({width: window_width},{complete: function() {map.resize();}});
            } else {
                $('#peak-search').animate({left: '0px'});
                $('#collapse-search').animate({left: '240px'});
                $('#collapse-search').html('<i class="fa fa-chevron-left"></i>');
                $('#peaks-map').animate({left: '240px'});
                var window_width = $(window).width();
                window_width = window_width - 240;
                if ($('#peak-list').css('right') == '0px') {
                    window_width = window_width - 240;
                }
                $("div#map-canvas").animate({width: window_width},{complete: function() {map.resize();}});
            }
        }

        function collapsePeakList() {
            if ($('#peak-list').css('right') == '0px') {
                $('#peak-list').animate({right: '-240px'});
                $('#collapse-peaklist').animate({right: '0px'});
                $('#collapse-peaklist').html('<i class="fa fa-list"></i>');
                $('#peaks-map').animate({right: '0px'});
                var window_width = $(window).width();
                if ($('#peak-search').css('left') == '0px') {
                    window_width = window_width - 240;
                }
                $("div#map-canvas").animate({width: window_width},{complete: function() {map.resize();}});
            } else {
                $('#peak-list').animate({right: '0px'});
                $('#collapse-peaklist').animate({right: '240px'});
                $('#collapse-peaklist').html('<i class="fa fa-chevron-right"></i>');
                $('#peaks-map').animate({right: '240px'});
                var window_width = $(window).width();
                window_width = window_width - 240;
                if ($('#peak-search').css('left') == '0px') {
                    window_width = window_width - 240;
                }
                $("div#map-canvas").animate({width: window_width},{complete: function() {map.resize();}});
            }
        }

        function round(value, decimals) {
          return Number(Math.round(value+'e'+decimals)+'e-'+decimals);
        }

        function getRepString (rep) {
          rep = rep+''; // coerce to string
          if (rep < 1000) {
            return rep; // return the same number
          }
          // divide and format
          return (rep/1000).toFixed(rep % 1000 != 0)+'K';
        }

        function updateURLParameter(url, param, paramVal) {

            var newAdditionalURL = "";
            var tempArray = url.split("#");
            var baseURL = tempArray[0];
            var additionalURL = tempArray[1];
            var temp = "";

            if (additionalURL) {
                tempArray = additionalURL.split("&");
                for (i = 0; i < tempArray.length; i++) {
                    if (tempArray[i].split('=')[0] != param) {
                        newAdditionalURL += temp + tempArray[i];
                        temp = "&";
                    }
                }
            }

            if (paramVal === undefined) {
                return newAdditionalURL;
            }

            const rows_txt = temp + "" + param + "=" + paramVal;
            return newAdditionalURL + rows_txt;
        }
        function delayShowKeywordNear(target, keyword, near) {
            clearTimeout(timers[target]);
            timers[target] = setTimeout(function() {
                q = keyword;
                n = near;
                u = updateURLParameter(window.location.hash, 'q', q);
                u = updateURLParameter('#' + u, 'n', n);
                window.location.hash = u;
                loadPeaksFromHash();
            }, 500);
        }
        function delayShowDataelevation(target, values) {
            clearTimeout(timers[target]);
            timers[target] = setTimeout(function() {
                elev_min = ~~values[0];
                elev_max = ~~values[1];
                u = updateURLParameter(window.location.hash, 'elev_min', elev_min);
                u = updateURLParameter('#'+u, 'elev_max', elev_max);
                window.location.hash = u;
                loadPeaksFromHash();
            }, 500);
        }
        function delayShowDataprominence(target, values) {
            clearTimeout(timers[target]);
            timers[target] = setTimeout(function() {
                prominence_min = ~~values[0];
                prominence_max = ~~values[1];
                u = updateURLParameter(window.location.hash, 'prom_min', prominence_min);
                u = updateURLParameter('#'+u, 'prom_max', prominence_max);
                window.location.hash = u;
                loadPeaksFromHash();
            }, 500);
        }
        function delayShowDataSummits(target, values) {
            clearTimeout(timers[target]);
            timers[target] = setTimeout(function() {
                summits_min = ~~values[0];
                summits_max = ~~values[1];
                u = updateURLParameter(window.location.hash, 'summits_min', summits_min);
                u = updateURLParameter('#'+u, 'summits_max', summits_max);
                window.location.hash = u;
                loadPeaksFromHash();
            }, 500);
        }
        function delayShowDataDifficulty(target, values) {
            clearTimeout(timers[target]);
            timers[target] = setTimeout(function() {
                difficulty_min = ~~values[0];
                difficulty_max = ~~values[1];
                u = updateURLParameter(window.location.hash, 'difficulty_min', difficulty_min);
                u = updateURLParameter('#'+u, 'difficulty_max', difficulty_max);
                window.location.hash = u;
                loadPeaksFromHash();
            }, 500);
        }

        function delayShowDataLength(target, values) {
            clearTimeout(timers[target]);
            timers[target] = setTimeout(function() {
                length_min = ~~values[0];
                length_max = ~~values[1];
                u = updateURLParameter(window.location.hash, 'length_min', length_min);
                u = updateURLParameter('#'+u, 'length_max', length_max);
                window.location.hash = u;
                loadPeaksFromHash();
            }, 500);
        }

        function delayShowDataVertical(target, values) {
            clearTimeout(timers[target]);
            timers[target] = setTimeout(function() {
                vertical_min = ~~values[0];
                vertical_max = ~~values[1];
                u = updateURLParameter(window.location.hash, 'vertical_min', vertical_min);
                u = updateURLParameter('#'+u, 'vertical_max', vertical_max);
                window.location.hash = u;
                loadPeaksFromHash();
            }, 500);
        }

        function delayShowDataLastClimbed(target, values) {
            clearTimeout(timers[target]);
            timers[target] = setTimeout(function() {
                last_climbed_min = ~~values[0];
                last_climbed_max = ~~values[1];
                u = updateURLParameter(window.location.hash, 'last_climbed_min', last_climbed_min);
                u = updateURLParameter('#'+u, 'last_climbed_max', last_climbed_max);
                window.location.hash = u;
                loadPeaksFromHash();
            }, 500);
        }

        function delayShowClassics() {
            var wanted_classics;
            if ($('#chkMapTagsClassics')[0].checked) {
                wanted_classics = 'true';
            } else {
                wanted_classics = 'false';
            }
            u = updateURLParameter(window.location.hash, 'classics', wanted_classics);
            u = updateURLParameter('#'+u, 'classics', wanted_classics);
            window.location.hash = u;
            loadPeaksFromHash();
        }

        function delayShowInChallenge() {
            var wanted_in_challenge;
            if ($('#chkMapTagsInChallenge')[0].checked) {
                wanted_in_challenge = 'true';
            } else {
                wanted_in_challenge = 'false';
            }
            u = updateURLParameter(window.location.hash, 'in_challenge', wanted_in_challenge);
            u = updateURLParameter('#'+u, 'in_challenge', wanted_in_challenge);
            window.location.hash = u;
            loadPeaksFromHash();
        }

        function delayShowYouClimbed() {
            var wanted_you_climbed = undefined;
            if ($('#chkMapTagsYouClimbed')[0].checked) {
                wanted_you_climbed = 'true';
                if ($('#chkMapTagsYouNotClimbed')[0].checked === true) {
                    $("#chkMapTagsYouNotClimbed").trigger("click")
                }
            }
            u = updateURLParameter(window.location.hash, 'you_climbed', wanted_you_climbed);
            u = updateURLParameter('#' + u, 'you_climbed', wanted_you_climbed);
            window.location.hash = u;
            loadPeaksFromHash();
        }

        function delayShowYouNotClimbed() {
            var wanted_you_climbed = undefined;
            if ($('#chkMapTagsYouNotClimbed')[0].checked) {
                wanted_you_climbed = 'false';
                if ($('#chkMapTagsYouClimbed')[0].checked === true) {
                    $("#chkMapTagsYouClimbed").trigger("click")
                }
            }
            u = updateURLParameter(window.location.hash, 'you_climbed', wanted_you_climbed);
            u = updateURLParameter('#' + u, 'you_climbed', wanted_you_climbed);
            window.location.hash = u;
            loadPeaksFromHash();
        }

        function numberWithCommas(x) {
            return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        }

        function resetSliders() {

            var elevationSlider = document.getElementById('slider-range');
            //if elevation in meters
            if ($('#bt_showinmeters').text() == '[ft]') {
                elevationSlider.noUiSlider.set([0, 9000]);
            } else {
                elevationSlider.noUiSlider.set([0, 29500]);
            }

            var prominenceSlider = document.getElementById('prominence-slider-range');
            //if prominence in meters
            if ($('#prominence_bt_showinmeters').text() == '[ft]') {
                prominenceSlider.noUiSlider.set([0, 9000]);
            } else {
                prominenceSlider.noUiSlider.set([0, 29500]);
            }

            var summitsSlider = document.getElementById('summits-slider-range');
            summitsSlider.noUiSlider.set([0, 500]);

            var difficultySlider = document.getElementById('difficulty-slider-range');
            difficultySlider.noUiSlider.set([1, 5]);

            var lengthSlider = document.getElementById('length-slider-range');
            //if elevation in meters
            if ($('#length_bt_showinmeters').text() == '[ft]') {
                lengthSlider.noUiSlider.set([0, 32]);
            } else {
                lengthSlider.noUiSlider.set([0, 20]);
            }

            var verticalSlider = document.getElementById('vertical-slider-range');
            //if elevation in meters
            if ($('#vertical_bt_showinmeters').text() == '[ft]') {
                verticalSlider.noUiSlider.set([0, 3000]);
            } else {
                verticalSlider.noUiSlider.set([0, 10000]);
            }

            var lastClimbedSlider = document.getElementById('last-climbed-slider-range');
            lastClimbedSlider.noUiSlider.set([0, 12]);

            $('#elevation-label').css('color','#aaa');
            $('#amount').css('color','#aaa');
            $('#amount').css('-webkit-text-fill-color','#aaa');
            $('#slider-range > div > div').css('background-color','#ccc');
            $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');

            $('#prominence-label').css('color','#aaa');
            $('#prominence-amount').css('color','#aaa');
            $('#prominence-amount').css('-webkit-text-fill-color','#aaa');
            $('#prominence-slider-range > div > div').css('background-color','#ccc');
            $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');

            $('#summits-label').css('color','#aaa');
            $('#summits-amount').css('color','#aaa');
            $('#summits-amount').css('-webkit-text-fill-color','#aaa');
            $('#summits-slider-range > div > div').css('background-color','#ccc');
            $('#summits-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');

            $('#difficulty-label').css('color','#aaa');
            $('#difficulty-amount').css('color','#aaa');
            $('#difficulty-amount').css('-webkit-text-fill-color','#aaa');
            $('#difficulty-slider-range > div > div').css('background-color','#ccc');
            $('#difficulty-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');

            $('#length-label').css('color','#aaa');
            $('#length-amount').css('color','#aaa');
            $('#length-amount').css('-webkit-text-fill-color','#aaa');
            $('#length-slider-range > div > div').css('background-color','#ccc');
            $('#length-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');

            $('#vertical-label').css('color','#aaa');
            $('#vertical-amount').css('color','#aaa');
            $('#vertical-amount').css('-webkit-text-fill-color','#aaa');
            $('#vertical-slider-range > div > div').css('background-color','#ccc');
            $('#vertical-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');

            $('#last-climbed-label').css('color','#aaa');
            $('#last-climbed-amount').css('color','#aaa');
            $('#last-climbed-amount').css('-webkit-text-fill-color','#aaa');
            $('#last-climbed-slider-range > div > div').css('background-color','#ccc');
            $('#last-climbed-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');

            $( "#amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29500) + " ft");
            $( "#amount-meters" ).val(numberWithCommas(Math.floor(0)) + "  to  " + numberWithCommas(Math.floor(9000)) + " m");

            $( "#prominence-amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29500) + " ft");
            $( "#prominence-amount-meters" ).val(numberWithCommas(Math.floor(0)) + "  to  " + numberWithCommas(Math.floor(9000)) + " m");

            $( "#summits-amount" ).val( "0  to  500+ climbs" );

            $( "#difficulty-amount" ).val( "Class 1 to 5" );

            //$( "#length-amount" ).val( "0  to  20+ mi" );
            $( "#length-amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(20) + "+ mi");
            $( "#length-amount-meters" ).val(numberWithCommas(Math.floor(0)) + "  to  " + numberWithCommas(Math.floor(32)) + "+ km");

            //$( "#vertical-amount" ).val( "0  to  10,000+ ft" );
            $( "#vertical-amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(10000) + "+ ft");
            $( "#vertical-amount-meters" ).val(numberWithCommas(Math.floor(0)) + "  to  " + numberWithCommas(Math.floor(3000)) + "+ m");

            $( "#last-climbed-amount" ).val( "any time" );

            window.location.hash = '';
        }

        function loadPeaksFromHash () {

            var vars = [], hash, keyword, near, wanted_elev_min, wanted_elev_max, wanted_prom_min, wanted_prom_max, wanted_summits_min, wanted_summits_max, wanted_difficulty_min, wanted_difficulty_max, wanted_length_min, wanted_length_max, wanted_vertical_min, wanted_vertical_max, wanted_last_climbed_min, wanted_last_climbed_max, classics, in_challenge, you_climbed, lat, lng;
            var q = document.URL.split('#')[1];
            if(q != undefined){
                q = q.split('&');
                for(var i = 0; i < q.length; i++){
                    hash = q[i].split('=');
                    vars.push(hash[1]);
                    vars[hash[0]] = hash[1];
                }
            }

            if (vars['q'] != undefined) {
                keyword = decodeURIComponent(vars['q']);
            } else {
                keyword = '';
            }

            if (vars['n'] != undefined) {
                near = decodeURIComponent(vars['n']);
            } else {
                near = '';
            }

            if (vars['elev_min'] != undefined) {
                wanted_elev_min = vars['elev_min'];
            } else {
                wanted_elev_min = '0';
            }

            if (vars['elev_max'] != undefined) {
                wanted_elev_max = vars['elev_max'];
            } else {
                //if elevation in meters
                if ($('#bt_showinmeters').text() == '[ft]') {
                    wanted_elev_max = '29527';
                } else {
                    wanted_elev_max = '29500';
                }
            }

            if (vars['prom_min'] != undefined) {
                wanted_prom_min = vars['prom_min'];
            } else {
                wanted_prom_min = '0';
            }

            if (vars['prom_max'] != undefined) {
                wanted_prom_max = vars['prom_max'];
            } else {
                //if prominence in meters
                if ($('#prominence_bt_showinmeters').text() == '[ft]') {
                    wanted_prom_max = '29527';
                } else {
                    wanted_prom_max = '29500';
                }
            }

            if (vars['summits_min'] != undefined) {
                wanted_summits_min = vars['summits_min'];
            } else {
                wanted_summits_min = '0';
            }

            if (vars['summits_max'] != undefined) {
                wanted_summits_max = vars['summits_max'];
            } else {
                wanted_summits_max = '500';
            }

            if (vars['difficulty_min'] != undefined) {
                wanted_difficulty_min = vars['difficulty_min'];
            } else {
                wanted_difficulty_min = '1';
            }

            if (vars['difficulty_max'] != undefined) {
                wanted_difficulty_max = vars['difficulty_max'];
            } else {
                wanted_difficulty_max = '5';
            }

            if (vars['length_min'] != undefined) {
                wanted_length_min = vars['length_min'];
            } else {
                wanted_length_min = '0';
            }

            if (vars['length_max'] != undefined) {
                wanted_length_max = vars['length_max'];
            } else {
                wanted_length_max = '20';
            }

            if (vars['vertical_min'] != undefined) {
                wanted_vertical_min = vars['vertical_min'];
            } else {
                wanted_vertical_min = '0';
            }

            if (vars['vertical_max'] != undefined) {
                wanted_vertical_max = vars['vertical_max'];
            } else {
                wanted_vertical_max = '10000';
            }

            if (vars['last_climbed_min'] != undefined) {
                wanted_last_climbed_min = vars['last_climbed_min'];
            } else {
                wanted_last_climbed_min = '0';
            }

            if (vars['last_climbed_max'] != undefined) {
                wanted_last_climbed_max = vars['last_climbed_max'];
            } else {
                wanted_last_climbed_max = '11';
            }

            if (vars['classics'] != undefined) {
                classics = vars['classics'];
            } else {
                classics = 'false';
            }

            if (vars['in_challenge'] != undefined) {
                in_challenge = vars['in_challenge'];
            } else {
                in_challenge = 'false';
            }

            if (vars['you_climbed'] != undefined) {
                you_climbed = vars['you_climbed'];
            } else {
                you_climbed = undefined;
            }

            if (vars['lat'] != undefined) {
                lat = vars['lat'];
            } else {
                lat = '';
            }

            if (vars['lng'] != undefined) {
                lng = vars['lng'];
            } else {
                lng = '';
            }

            loadPeaks(keyword, near, wanted_elev_min, wanted_elev_max, wanted_prom_min, wanted_prom_max, wanted_summits_min, wanted_summits_max, wanted_difficulty_min, wanted_difficulty_max, wanted_length_min, wanted_length_max, wanted_vertical_min, wanted_vertical_max, wanted_last_climbed_min, wanted_last_climbed_max, classics, in_challenge, you_climbed, lat, lng);

        }

        function loadPeaks(keyword, near, wanted_elev_min, wanted_elev_max, wanted_prom_min, wanted_prom_max, wanted_summits_min, wanted_summits_max, wanted_difficulty_min, wanted_difficulty_max, wanted_length_min, wanted_length_max, wanted_vertical_min, wanted_vertical_max, wanted_last_climbed_min, wanted_last_climbed_max, classics, in_challenge, you_climbed, lat, lng) {

            //hide search modal
            //$('#peak-search').modal('hide');

            //set map center cookie
            var NewMapCenter = map.getCenter();
            var NewMapZoom = map.getZoom();
            createCookie('map_lat',NewMapCenter.lat,365);
            createCookie('map_lng',NewMapCenter.lng,365);
            createCookie('map_zoom',NewMapZoom,365);

            //set hash for peaks page link
            //$('#navbar-link-peaks a').attr('href','/peaks/'+window.location.hash);

            //get 2d map bounds or create 3d map bounds
            var mapButton = $('#gm-custom-mapbutton').html();
            if (mapButton.includes('3D Map')) {
                var camLat = map.getCenter().lat;
                var camLng = map.getCenter().lng;
                var sw = new mapboxgl.LngLat(camLng - .15, camLat - .15);
                var ne = new mapboxgl.LngLat(camLng + .15, camLat + .15);
                var bounds = new mapboxgl.LngLatBounds(sw, ne);
            } else if ($('#peak-search-3d').html() == '2D') {
                var camLat = map.getCenter().lat;
                var camLng = map.getCenter().lng;
                var sw = new mapboxgl.LngLat(camLng - .15, camLat - .15);
                var ne = new mapboxgl.LngLat(camLng + .15, camLat + .15);
                var bounds = new mapboxgl.LngLatBounds(sw, ne);
            } else {
                var bounds = map.getBounds();
            }

            //if elevation in meters
            if ($('#bt_showinmeters').text() == '[ft]') {
                if (wanted_elev_min != 0 || wanted_elev_max != 29527) {
                    $('#elevation-label').css('color', '#f24100');
                    $('#amount').css('color', '#f24100');
                    $('#amount').css('-webkit-text-fill-color', '#f24100');
                    $('#slider-range > div > div').css('background-color', '#f24100');
                    $('#slider-range .noUi-background').css('background-color', '#CCC');
                    $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                }
            } else {
                if (wanted_elev_min != 0 || wanted_elev_max != 29500) {
                    $('#elevation-label').css('color', '#f24100');
                    $('#amount').css('color', '#f24100');
                    $('#amount').css('-webkit-text-fill-color', '#f24100');
                    $('#slider-range > div > div').css('background-color', '#f24100');
                    $('#slider-range .noUi-background').css('background-color', '#CCC');
                    $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                }
            }

            //if prominence in meters
            if ($('#prominence_bt_showinmeters').text() == '[ft]') {
                if (wanted_prom_min != 0 || wanted_prom_max != 29527) {
                    $('#prominence-label').css('color', '#f24100');
                    $('#prominence-amount').css('color', '#f24100');
                    $('#prominence-amount').css('-webkit-text-fill-color', '#f24100');
                    $('#prominence-slider-range > div > div').css('background-color', '#f24100');
                    $('#prominence-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                }
            } else {
                if (wanted_prom_min != 0 || wanted_prom_max != 29500) {
                    $('#prominence-label').css('color', '#f24100');
                    $('#prominence-amount').css('color', '#f24100');
                    $('#prominence-amount').css('-webkit-text-fill-color', '#f24100');
                    $('#prominence-slider-range > div > div').css('background-color', '#f24100');
                    $('#prominence-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                }
            }

            if (wanted_summits_min != 0 || wanted_summits_max != 500) {
                $('#summits-label').css('color','#f24100');
                $('#summits-amount').css('color','#f24100');
                $('#summits-amount').css('-webkit-text-fill-color', '#f24100');
                $('#summits-slider-range > div > div').css('background-color','#f24100');
                $('#summits-slider-range .noUi-background').css('background-color', '#CCC');
                $('#summits-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
            }

            if (wanted_difficulty_min != 1 || wanted_difficulty_max != 5) {
                $('#difficulty-label').css('color','#f24100');
                $('#difficulty-amount').css('color','#f24100');
                $('#difficulty-amount').css('-webkit-text-fill-color', '#f24100');
                $('#difficulty-slider-range > div > div').css('background-color','#f24100');
                $('#difficulty-slider-range .noUi-background').css('background-color', '#CCC');
                $('#difficulty-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
            }

            //if length in meters
            if ($('#length_bt_showinmeters').text() == '[ft]') {
                if (wanted_length_min != 0 || wanted_length_max != 20) {
                    $('#length-label').css('color','#f24100');
                    $('#length-amount').css('color','#f24100');
                    $('#length-amount').css('-webkit-text-fill-color', '#f24100');
                    $('#length-slider-range > div > div').css('background-color','#f24100');
                    $('#length-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#length-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                }
            } else {
                if (wanted_length_min != 0 || wanted_length_max != 20) {
                    $('#length-label').css('color','#f24100');
                    $('#length-amount').css('color','#f24100');
                    $('#length-amount').css('-webkit-text-fill-color', '#f24100');
                    $('#length-slider-range > div > div').css('background-color','#f24100');
                    $('#length-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#length-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                }
            }

            //if vertical in meters
            if ($('#vertical_bt_showinmeters').text() == '[ft]') {
                if (wanted_vertical_min != 0 || wanted_vertical_max != 9842) {
                    $('#vertical-label').css('color','#f24100');
                    $('#vertical-amount').css('color','#f24100');
                    $('#vertical-amount').css('-webkit-text-fill-color', '#f24100');
                    $('#vertical-slider-range > div > div').css('background-color','#f24100');
                    $('#vertical-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#vertical-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                }
            } else {
                if (wanted_vertical_min != 0 || wanted_vertical_max != 10000) {
                    $('#vertical-label').css('color','#f24100');
                    $('#vertical-amount').css('color','#f24100');
                    $('#vertical-amount').css('-webkit-text-fill-color', '#f24100');
                    $('#vertical-slider-range > div > div').css('background-color','#f24100');
                    $('#vertical-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#vertical-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                }
            }

            if (wanted_last_climbed_min != 0 || wanted_last_climbed_max != 11) {
                $('#last-climbed-label').css('color','#f24100');
                $('#last-climbed-amount').css('color','#f24100');
                $('#last-climbed-amount').css('-webkit-text-fill-color', '#f24100');
                $('#last-climbed-slider-range > div > div').css('background-color','#f24100');
                $('#last-climbed-slider-range .noUi-background').css('background-color', '#CCC');
                $('#last-climbed-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
            }

            var counter = 0;
            var strTemp = '';

            $('#ajax-data-loading').css('display', 'inline');
            $('#peak-search-mobile').html('<i class="fa fa-spinner fa-spin"></i>');
            var params = '&challenge_id={{ group.id }}';
            if (keyword.length > 0) params = params + '&q=' + keyword;
            if (near.length > 0) params = params + '&n=' + near;
            if (wanted_elev_min.length > 0) params = params + '&elev_min=' + wanted_elev_min;
            if (wanted_elev_max.length > 0) params = params + '&elev_max=' + wanted_elev_max;
            if (wanted_prom_min.length > 0) params = params + '&prom_min=' + wanted_prom_min;
            if (wanted_prom_max.length > 0) params = params + '&prom_max=' + wanted_prom_max;
            if (wanted_summits_min.length > 0) params = params + '&summits_min=' + wanted_summits_min;
            if (wanted_summits_max.length > 0) params = params + '&summits_max=' + wanted_summits_max;
            if (wanted_difficulty_min.length > 0) params = params + '&difficulty_min=' + wanted_difficulty_min;
            if (wanted_difficulty_max.length > 0) params = params + '&difficulty_max=' + wanted_difficulty_max;
            if (wanted_length_min.length > 0) params = params + '&length_min=' + wanted_length_min;
            if (wanted_length_max.length > 0) params = params + '&length_max=' + wanted_length_max;
            if (wanted_vertical_min.length > 0) params = params + '&vertical_min=' + wanted_vertical_min;
            if (wanted_vertical_max.length > 0) params = params + '&vertical_max=' + wanted_vertical_max;
            if (wanted_last_climbed_min.length > 0) params = params + '&last_climbed_min=' + wanted_last_climbed_min;
            if (wanted_last_climbed_max.length > 0) params = params + '&last_climbed_max=' + wanted_last_climbed_max;
            if (classics.length > 0) params = params + '&classics=' + classics;
            if (in_challenge.length > 0) params = params + '&in_challenge=' + in_challenge;
            if (you_climbed !== undefined) params = params + '&you_climbed=' + you_climbed;
            if (lat.length > 0) params = params + '&lat=' + lat;
            if (lng.length > 0) params = params + '&lng=' + lng;
            params = params + '&bounds=' + bounds.getSouthWest().lat + ',' + bounds.getSouthWest().lng + ',' + bounds.getNorthEast().lat + ',' + bounds.getNorthEast().lng + '&zoom=' + map.getZoom() + '&near=' + $('#hdnNearQuery').val();

            //set mobile title
            if (keyword.length > 0 && near.length > 0) {
                $('#mobile-search-title').html('"' + keyword + '" peaks near ' + near);
            } else if (keyword.length > 0) {
                $('#mobile-search-title').html('"' + keyword + '" peaks');
            } else if (near.length > 0) {
                $('#mobile-search-title').html('Peaks near ' + near);
            } else if (lat.length > 0 && lng.length > 0) {
                $('#mobile-search-title').html('Peaks near you');
            } else {
                $('#mobile-search-title').html('All peaks');
            }

            //update hidden parameters
            $('#hdnKeyword').val(keyword);
            if (keyword.length > 0) {
                $('#q').val(keyword);
                $('#clear_peak_name').html('<i class="fa fa-times"></i>');
                $('#clear_peak_name').show();
            }
            $('#hdnNear').val(near);
            if (near.length > 0) {
                $('#n').val(near);
                $('#clear_near_location').html('<i class="fa fa-times"></i>');
                $('#clear_near_location').show();
            }
            $('#hdnElevMin').val(wanted_elev_min);
            $('#hdnElevMax').val(wanted_elev_max);
            $('#hdnPromMin').val(wanted_prom_min);
            $('#hdnPromMax').val(wanted_prom_max);
            $('#hdnSummitsMin').val(wanted_summits_min);
            $('#hdnSummitsMax').val(wanted_summits_max);
            $('#hdnDifficultyMin').val(wanted_difficulty_min);
            $('#hdnDifficultyMax').val(wanted_difficulty_max);

            $('#hdnLengthMin').val(wanted_length_min);
            $('#hdnLengthMax').val(wanted_length_max);
            $('#hdnVerticalMin').val(wanted_vertical_min);
            $('#hdnVerticalMax').val(wanted_vertical_max);
            $('#hdnLastClimbedMin').val(wanted_last_climbed_min);
            $('#hdnLastClimbedMax').val(wanted_last_climbed_max);

            $('#hdnClassics').val(classics);
            $('#hdnInChallenge').val(in_challenge);
            $('#hdnYouClimbed').val(you_climbed);

            $('#hdnLat').val(lat);
            $('#hdnLng').val(lng);
            $('#hdnBounds').val(bounds.getSouthWest().lat + ',' + bounds.getSouthWest().lng + ',' + bounds.getNorthEast().lat + ',' + bounds.getNorthEast().lng);

            var mobileFilters = '';
            var LatLngList = [];

            if (params.length > 0) params = '?' + params.slice(-1*(params.length-1));
            var byRegion = false;
            var totalPeaks = 0;
            $('#peak-list').empty();
            $('#peak-list').append('<div class="row" id="ajax-data-loading" style="height: 100%;"><div class="col-md-12" style="display: flex; justify-content: center; color: #c0c0c0; flex-direction: column; height: 100%; text-align: center;"><i class="fa fa-spinner fa-spin fa-5x"></i></div></div>');
            $('#peak-list').append('<div id="no-peaks-in-list" style="display: none;">No peaks in map view<br /><br />Adjust map to see</div>');
            $.getJSON('{% url "challenges_map" %}' + params , function(data) {
                $.each( data, function( key, val ) {
                    var currentRequest = true;
                    if (key=='parameters') {
                        //reset near query boolean
                        $('#hdnNearQuery').val('false');
                        $.each( val, function( parameterkey, parameterval ) {
                            if (parameterval.keyword.toLowerCase() != $('#hdnKeyword').val().toLowerCase()) currentRequest = false;
                            if (parameterval.near.toLowerCase() != $('#hdnNear').val().toLowerCase()) currentRequest = false;
                            if (parameterval.elev_min != $('#hdnElevMin').val()) currentRequest = false;
                            if (parameterval.elev_max != $('#hdnElevMax').val()) currentRequest = false;
                            if (parameterval.prom_min != $('#hdnPromMin').val()) currentRequest = false;
                            if (parameterval.prom_max != $('#hdnPromMax').val()) currentRequest = false;
                            if (parameterval.summits_min != $('#hdnSummitsMin').val()) currentRequest = false;
                            if (parameterval.summits_max != $('#hdnSummitsMax').val()) currentRequest = false;
                            if (parameterval.difficulty_min != $('#hdnDifficultyMin').val()) currentRequest = false;
                            if (parameterval.difficulty_max != $('#hdnDifficultyMax').val()) currentRequest = false;

                            if (parameterval.length_min != $('#hdnLengthMin').val()) currentRequest = false;
                            if (parameterval.length_max != $('#hdnLengthMax').val()) currentRequest = false;
                            if (parameterval.vertical_min != $('#hdnVerticalMin').val()) currentRequest = false;
                            if (parameterval.vertical_max != $('#hdnVerticalMax').val()) currentRequest = false;
                            if (parameterval.last_climbed_min != $('#hdnLastClimbedMin').val()) currentRequest = false;
                            if (parameterval.last_climbed_max != $('#hdnLastClimbedMax').val()) currentRequest = false;

                            if (parameterval.classics != $('#hdnClassics').val()) currentRequest = false;
                            if (parameterval.in_challenge != $('#hdnInChallenge').val()) currentRequest = false;
                            if (parameterval.you_climbed != $('#hdnYouClimbed').val()) currentRequest = false;

                            if (parameterval.lat != $('#hdnLat').val()) currentRequest = false;
                            if (parameterval.lng != $('#hdnLng').val()) currentRequest = false;
                            if (parameterval.state_id != '0') {
                                byRegion = true;
                            }

                            //set up filter description for mobile header
                            if ($('#bt_showinmeters').text() == '[ft]') {
                                var elevMinMeters = Math.ceil(parseFloat(parameterval.elev_min) * 0.3048);
                                var elevMaxMeters = Math.ceil(parseFloat(parameterval.elev_max) * 0.3048);
                                if (parameterval.elev_max != '29527') {
                                    mobileFilters = mobileFilters + getRepString(elevMinMeters.toString()) + '-' + getRepString(elevMaxMeters.toString()) + ' m elev &bull; ';
                                } else if (parameterval.elev_min != '0') {
                                    mobileFilters = mobileFilters + getRepString(elevMinMeters.toString()) + '+ m elev &bull; ';
                                }
                            } else {
                                if (parameterval.elev_max != '29500') {
                                    mobileFilters = mobileFilters + getRepString(parameterval.elev_min) + '-' + getRepString(parameterval.elev_max) + ' ft elev &bull; ';
                                } else if (parameterval.elev_min != '0') {
                                    mobileFilters = mobileFilters + getRepString(parameterval.elev_min) + '+ ft elev &bull; ';
                                }
                            }
                            if ($('#prominence_bt_showinmeters').text() == '[ft]') {
                                var elevMinMeters = Math.ceil(parseFloat(parameterval.elev_min) * 0.3048);
                                var elevMaxMeters = Math.ceil(parseFloat(parameterval.elev_max) * 0.3048);
                                if (parameterval.prom_max != '29527') {
                                    mobileFilters = mobileFilters + getRepString(elevMinMeters.toString()) + '-' + getRepString(elevMaxMeters.toString()) + ' m prom &bull; ';
                                } else if (parameterval.prom_min != '0') {
                                    mobileFilters = mobileFilters + getRepString(elevMinMeters.toString()) + '+ m prom &bull; ';
                                }
                            } else {
                                if (parameterval.prom_max != '29500') {
                                    mobileFilters = mobileFilters + getRepString(parameterval.prom_min) + '-' + getRepString(parameterval.prom_max) + ' ft prom &bull; ';
                                } else if (parameterval.prom_min != '0') {
                                    mobileFilters = mobileFilters + getRepString(parameterval.prom_min) + '+ ft prom &bull; ';
                                }
                            }
                            if (parameterval.summits_max != '500') {
                                mobileFilters = mobileFilters + parameterval.summits_min + '-' + parameterval.summits_max + ' climbs &bull; ';
                            } else if (parameterval.summits_min != '0') {
                                mobileFilters = mobileFilters + parameterval.summits_min + '+ climbs &bull; ';
                            }
                            if (parameterval.difficulty_max != '5') {
                                mobileFilters = mobileFilters + 'Class ' + parameterval.difficulty_min + '-' + parameterval.difficulty_max + ' &bull; ';
                            } else if (parameterval.difficulty_min != '1') {
                                mobileFilters = mobileFilters + 'Class ' + parameterval.difficulty_min + '+ &bull; ';
                            }
                        });
                    }

                    if (!currentRequest) {
                        return false;
                    }

                    //set mobile title
                    var mobileTitle = '';
                    if (keyword.length > 0 && near.length > 0) {
                        mobileTitle = '"' + keyword + '" peaks near ' + near;
                    } else if (keyword.length > 0) {
                        mobileTitle = '"' + keyword + '" peaks';
                    } else if (near.length > 0) {
                        mobileTitle = 'Peaks near ' + near;
                    } else if (lat.length > 0 && lng.length > 0) {
                        mobileTitle = 'Peaks near you';
                    } else {
                        mobileTitle = 'All peaks';
                    }
                    if (mobileFilters != '') {
                        mobileTitle = mobileTitle + '<br /><span class="mobile-header-subtitle">' + mobileFilters.slice(0, -8) + '</span>';
                    }
                    $('#mobile-search-title').html(mobileTitle);

                    if (key=='peaks') {

                        var havePeaks = false;
                        var aryPeakListItems = [];

                        $.each( val, function( peakkey, peakval ) {

                            if (!havePeaks) {

                                //first time through, delete highest peak marker and remove any markers not on map
                                deletehighest();
                                //delete markers out of margins
                                delete_old_markers(val);

                            }

                            havePeaks = true;

                            //build country string
                            var country = '';
                            $.each( peakval.country, function( countrykey, countryval ) {
                                country = country + '<div><a href="' + countryval.country_slug + '/">' + countryval.country_name + '</a></div>';
                            });
                            if (country == '') {
                                country = 'Unknown Country';
                            }

                            //build region string
                            var region = '';
                            var mobile_region = '';
                            var region_bull_class = 'hidden-lg hidden-md hidden-sm hidden-xs';
                            $.each( peakval.region, function( regionkey, regionval ) {
                                region_bull_class = '';
                                region = region + '<div style="margin-bottom: 5px;"><a class="truncate" href="' + regionval.region_slug + '/">' + regionval.region_name + ', ' + regionval.country_name + '</a></div>';
                                mobile_region = '<div style="margin-bottom: 5px;"><a class="truncate" href="' + regionval.region_slug + '/">' + regionval.country_name + '</a></div>';
                            });
                            if (region == '') {
                                region = country;
                                mobile_region = country;
                            }

                            //build challenges string
                            var challenges = '';
                            var challenge_count = peakval.challenge_count;
                            if (challenge_count > 0) {
                                challenges = ' &bull; <span>' + challenge_count + ' <i class="fa fa-trophy"></i></span>';
                            }

                            //buld distance string
                            var distance = '';
                            if (near.length > 0) {
                                var miles = Math.round(peakval.miles_away);
                                var km = Math.round(peakval.miles_away*1.60934);
                                distance = miles.toString()+' mi / '+km.toString()+' km';
                            }

                            //build summits string
                            var summits, tooltip_your_summits;
                            if (peakval.summit_count > 0) {
                                summits = '<a href="/' + peakval.slug + '/summits/">' + peakval.summit_count + '</a>';
                            } else {
                                summits = '<a href="/' + peakval.slug + '/summits/">0</a><br /><a href="/' + peakval.slug + '/">Be the first!</a>';
                            }
                            if (peakval.your_summits > 0) {
                                summits = summits + '<br /><span style="color: #3fc663; font-weight: 500;">' + peakval.your_summits + 'x by you</span>';
                                tooltip_your_summits = '&nbsp;<span style="color: #3fc663; font-weight: 500;">(You ' + peakval.your_summits + 'x)</span>';
                            } else {
                                tooltip_your_summits = '';
                            }

                            //tooltip vars
                            var tooltip_html, tooltip_width, tooltip_height, marker_top, marker_left, tooltip_total_width, tooltip_total_height;

                            //show tooltip badges?
                            var showClassic = 'display: none;';
                            var showChallenges = 'display: none;';
                            var showYourSummits = 'display: none;';
                            if (peakval.is_classic == 'True') {
                                showClassic = '';
                            }
                            if (peakval.challenge_count > 0) {
                                showChallenges = '';
                            }
                            if (peakval.your_summits > 0) {
                                showYourSummits = '';
                            }

                            peaklist_html = '';

                            //show feet or meters?
                            var showFeet = 'display: none;';
                            var showMeters = 'display: none;';
                            if ($('#bt_showinmeters').hasClass('meters')) {
                                showMeters = '';
                            } else {
                                showFeet = '';
                            }

                            //build tooltip string
                            if (peakval.thumbnail_url.indexOf('default.png.350x245_q95_crop.jpg') < 0) {
                                tooltip_html = '<img class="map-tooltip-img hover-photos-hover" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px;" src="{{ MEDIA_URL }}' + peakval.thumbnail_url + '"><div class="map-tooltip-peak-badges" style="font-weight:500; top: 115px; width: 220px; position: absolute; background-color: transparent; background-image: linear-gradient(to bottom, transparent 0px, rgba(0, 0, 0, 0.7) 100%); padding-left: 5px;"><div id="peak-classic-badge" style="' + showClassic + ' width: 65px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00a7ee; font-size: 8px; color: #fff; float: left; text-align: center; line-height: 20px;"><i class="fa fa-star" style="float: left; margin-left: 5px; line-height: 18px; filter: drop-shadow(0px 0px 1px #333); color: #feeeb9;"></i> CLASSIC</div><div id="peak-challenges-badge" style="' + showChallenges + ' width: 95px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #5c48e0; font-size: 8px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-trophy" style="filter: drop-shadow(0px 0px 1px #333); color: #feeeb9; float: left; margin-left: 5px; line-height: 18px;"></i> ' + peakval.challenge_count + ' CHALLENGE' + ((peakval.challenge_count != 1) ? 'S' : '') + '</div><div id="peak-your-summits-badge" style="' + showYourSummits + ' width: 40px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00b524; font-size: 9px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-check" style="filter: drop-shadow(0px 0px 1px #333); float: left; margin-left: 5px; line-height: 20px;"></i> ' + peakval.your_summits + 'x</div></div><div class="map-tooltip-info" style="border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; bottom: -35px; height: 100px; background-color: transparent !important;"><div style="margin-left: -8px; margin-top: 43px; height: 55px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; box-shadow: 0 5px 8px 0 rgba(0,0,0,0.30);"><div class="map-tooltip-peak-name ellipsis" style="color: #333; text-align: left; padding-left: 10px; font-size: 15px; width: 210px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; padding-left: 10px;"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' climb' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div></div>';
                                tooltip_width = 220;
                                tooltip_height = 165;
                                tooltip_total_width = 250;
                                tooltip_total_height = 230;
                                peaklist_html = '<a href="/' + peakval.slug + '/"><div id="peak-listitem-' + peakval.id + '" data-peakid="' + peakval.id + '" class="hover-photos hover-minicard" style="float: left; margin-left: 10px; margin-top: 10px; margin-bottom: -60px;"><img class="map-tooltip-img hover-photos-hover" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; position: relative;" src="{{ MEDIA_URL }}' + peakval.thumbnail_url + '"><div class="map-tooltip-peak-badges" style="font-weight:500; top: -52px; width: 220px; position: relative; background-color: transparent; background-image: linear-gradient(to bottom, transparent 0px, rgba(0, 0, 0, 0.7) 100%); padding-left: 5px;"><div id="peak-classic-badge" style="' + showClassic + ' width: 65px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00a7ee; font-size: 8px; color: #fff; float: left; text-align: center; line-height: 20px;"><i class="fa fa-star" style="float: left; margin-left: 5px; line-height: 18px; filter: drop-shadow(0px 0px 1px #333); color: #feeeb9;"></i> CLASSIC</div><div id="peak-challenges-badge" style="' + showChallenges + ' width: 95px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #5c48e0; font-size: 8px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-trophy" style="filter: drop-shadow(0px 0px 1px #333); color: #feeeb9; float: left; margin-left: 5px; line-height: 18px;"></i> ' + peakval.challenge_count + ' CHALLENGE' + ((peakval.challenge_count != 1) ? 'S' : '') + '</div><div id="peak-your-summits-badge" style="' + showYourSummits + ' width: 40px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00b524; font-size: 9px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-check" style="filter: drop-shadow(0px 0px 1px #333); float: left; margin-left: 5px; line-height: 20px;"></i> ' + peakval.your_summits + 'x</div></div><div class="map-tooltip-info" style="border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; position: relative; left: inherit; top: -65px; width: 220px; height: 100px; background-color: transparent !important;"><div class="peak-listitem-footer" style="margin-left: -8px; margin-top: 43px; height: 55px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff;"><div class="map-tooltip-peak-name ellipsis" style="color: #333; text-align: left; padding-left: 10px; font-size: 15px; width: 210px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; padding-left: 10px;"><span style="' + showFeet + '" class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="' + showMeters + '" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' climb' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div></div></div></a>';
                            } else {
                                if (peakval.is_classic == 'True' || peakval.challenge_count > 0 || peakval.your_summits > 0) {
                                    tooltip_html = '<div class="map-tooltip-peak-badges" style="font-weight:500; top: 4px; z-index: 2; width: 220px; position: absolute; background-color: transparent; padding-left: 5px;"><div id="peak-classic-badge" style="' + showClassic + ' width: 65px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00a7ee; font-size: 8px; color: #fff; float: left; text-align: center; line-height: 20px;"><i class="fa fa-star" style="float: left; margin-left: 5px; line-height: 18px; filter: drop-shadow(0px 0px 1px #333); color: #feeeb9;"></i> CLASSIC</div><div id="peak-challenges-badge" style="' + showChallenges + ' width: 95px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #5c48e0; font-size: 8px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-trophy" style="filter: drop-shadow(0px 0px 1px #333); color: #feeeb9; float: left; margin-left: 5px; line-height: 18px;"></i> ' + peakval.challenge_count + ' CHALLENGE' + ((peakval.challenge_count != 1) ? 'S' : '') + '</div><div id="peak-your-summits-badge" style="' + showYourSummits + ' width: 40px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00b524; font-size: 9px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-check" style="filter: drop-shadow(0px 0px 1px #333); float: left; margin-left: 5px; line-height: 20px;"></i> ' + peakval.your_summits + 'x</div></div><div class="map-tooltip-info" style="height: 75px; border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; box-shadow: 0 5px 8px 0 rgba(0,0,0,0.30);"><div class="map-tooltip-peak-name ellipsis" style="margin-top: 25px; color: #333; text-align: left; font-size: 15px; width: 210px; height: 27px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; margin-bottom: -5px;"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' climb' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div>';
                                    tooltip_width = 220;
                                    tooltip_height = 75;
                                    tooltip_total_width = 250;
                                    tooltip_total_height = 105;
                                    peaklist_html = '<a href="/' + peakval.slug + '/"><div id="peak-listitem-' + peakval.id + '" data-peakid="' + peakval.id + '" class="hover-minicard" style="float: left; margin-left: 10px; margin-top: 10px; margin-bottom: 5px;"><div class="map-tooltip-peak-badges" style="font-weight:500; top: 4px; z-index: 2; width: 220px; position: relative; background-color: transparent; padding-left: 5px;"><div id="peak-classic-badge" style="' + showClassic + ' width: 65px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00a7ee; font-size: 8px; color: #fff; float: left; text-align: center; line-height: 20px;"><i class="fa fa-star" style="float: left; margin-left: 5px; line-height: 18px; filter: drop-shadow(0px 0px 1px #333); color: #feeeb9;"></i> CLASSIC</div><div id="peak-challenges-badge" style="' + showChallenges + ' width: 95px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #5c48e0; font-size: 8px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-trophy" style="filter: drop-shadow(0px 0px 1px #333); color: #feeeb9; float: left; margin-left: 5px; line-height: 18px;"></i> ' + peakval.challenge_count + ' CHALLENGE' + ((peakval.challenge_count != 1) ? 'S' : '') + '</div><div id="peak-your-summits-badge" style="' + showYourSummits + ' width: 40px; height: 20px; margin: 5px auto; border-radius: 3px; background-color: #00b524; font-size: 9px; color: #fff; float: left; margin-left: 5px; text-align: center; line-height: 20px;"><i class="fa fa-check" style="filter: drop-shadow(0px 0px 1px #333); float: left; margin-left: 5px; line-height: 20px;"></i> ' + peakval.your_summits + 'x</div></div><div class="map-tooltip-info peak-listitem-footer" style="height: 80px; border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; position: relative;"><div class="map-tooltip-peak-name ellipsis" style="margin-top: 25px; color: #333; text-align: left; font-size: 15px; width: 210px; height: 27px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; margin-bottom: -5px;"><span style="' + showFeet + '" class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="' + showMeters + '" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' climb' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div></div></a>';
                                } else {
                                    tooltip_html = '<div class="map-tooltip-info" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; box-shadow: 0 5px 8px 0 rgba(0,0,0,0.30);"><div class="map-tooltip-peak-name ellipsis" style="color: #333; text-align: left; font-size: 15px; width: 210px; height: 27px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; margin-bottom: -5px;"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div>';
                                    tooltip_width = 220;
                                    tooltip_height = 50;
                                    tooltip_total_width = 250;
                                    tooltip_total_height = 80;
                                    peaklist_html = '<a href="/' + peakval.slug + '/"><div id="peak-listitem-' + peakval.id + '" data-peakid="' + peakval.id + '" class="hover-minicard" style="float: left; margin-left: 10px; margin-top: 10px; margin-bottom: 5px;"><div class="map-tooltip-info peak-listitem-footer" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; position: relative; height: 54px;"><div class="map-tooltip-peak-name ellipsis" style="color: #333; text-align: left; font-size: 15px; width: 210px; height: 27px;">' + peakval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; margin-bottom: -5px;"><span style="' + showFeet + '" class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="' + showMeters + '" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' climb' + ((peakval.summit_count != 1) ? 's' : '') + '</div></div></div></a>';
                                }
                            }

                            aryPeakListItems.push({summits: peakval.summit_count, elevation: peakval.elevation, peaklist_html: peaklist_html});

                            var tooltip_url = '/' + peakval.slug;

                            var latLng = new mapboxgl.LngLat(peakval.lng, peakval.lat);

                            if (counter == 0) {
                                //highest peak gets red icon
                                iconstyle = 'marker_icon_red';
                            } else if (peakval.your_summits > 0) {
                                //if you have summited then green icon
                                iconstyle = 'marker_icon_green';
                            } else if (peakval.your_attempts > 0) {
                                //if you have attempted then yellow icon
                                iconstyle = 'marker_icon_yellow';
                            } else {
                                iconstyle = 'marker_icon';
                            }

                            if (isTouchDevice()) {
                                var is_draggable = false;
                            } else {
                                {% if request.user.is_staff %}
                                var is_draggable = true;
                                {% else %}
                                var is_draggable = false;
                                {% endif %}
                            }

                            //check if already exist so don't put again
                            var exists = false;
                            for (i = markersArray.length-1; i>=0; i--){
                                if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng)){
                                    exists = true ;
                                    //if the highest is in the actual viewport, not as the highest, delete it
                                    if (iconstyle=='marker_icon_red' || iconstyle=='marker_icon_redgreen' || iconstyle=='marker_icon_green') {
                                        markersArray[i].remove();
                                        markersArray.splice(i,1);
                                        exists = false;
                                    }
                                }
                            }

                            if (!exists) {
                                var latLng = [peakval.lng, peakval.lat];
                                //add marker
                                //create an HTML element for the marker
                                var el = document.createElement('div');
                                el.className = iconstyle;
                                el.setAttribute('id', 'peak-marker-'+peakval.id);

                                if (peakval.is_classic == 'True') {
                                    el.setAttribute('data-classic', 'true');
                                }

                                {% if request.user.is_authenticated %}

                                if (peakval.kom_user == '{{ request.user.id }}') {
                                    el.setAttribute('data-kom', 'true');
                                }

                                if (peakval.first_ascent_user == '{{ request.user.id }}') {
                                    el.setAttribute('data-firstascent', 'true');
                                }

                                if (peakval.summit_stewards != '') {
                                    var stewards = $.parseJSON(peakval.summit_stewards.replace(/&quot;/g,'"'));
                                    $.each(stewards, function (key, data) {
                                        if (key == 'summit_stewards') {
                                            for (var i = 0; i < data.length; i++) {
                                                if (data[i] == '{{ request.user.id }}') {
                                                    el.setAttribute('data-steward', 'true');
                                                }
                                            }
                                        }
                                    });
                                }

                                {% endif %}

                                el.addEventListener('click', function(e) {
                                    if (isTouchDevice()) {
                                        hideMapTooltip();
                                        $('#gm-custom-mapdropdown').hide();
                                        $('#gm-custom-maplegend-dropdown').hide();

                                        var bottom = $('#map-canvas').height();
                                        var right = $('#map-canvas').width();
                                        var showClass = 'scale-in-';
                                        var hideClass = 'scale-out-';

                                        var markerX = this.getBoundingClientRect().x + 14;
                                        var markerY = this.getBoundingClientRect().y + 14 - 100;

                                        if (markerY < (bottom / 2)) {
                                            marker_top = markerY;
                                            showClass = showClass + 't';
                                            hideClass = hideClass + 't';
                                        } else {
                                            marker_top = markerY - tooltip_total_height - 15;
                                            showClass = showClass + 'b';
                                            hideClass = hideClass + 'b';
                                        }

                                        if (markerX < (right / 3)) {
                                            marker_left = markerX;
                                            showClass = showClass + 'l';
                                            hideClass = hideClass + 'l';
                                        } else if (markerX >= (right / 3) && markerX < ((right / 3) * 2)) {
                                            marker_left = markerX - (tooltip_total_width / 2);
                                            showClass = showClass + 'm';
                                            hideClass = hideClass + 'm';
                                        } else {
                                            marker_left = markerX - tooltip_total_width;
                                            showClass = showClass + 'r';
                                            hideClass = hideClass + 'r';
                                        }

                                        $('#marker-tooltip').html(marker.properties.tooltipContent).css({
                                            'left': marker_left,
                                            'top': marker_top,
                                            'width': tooltip_width,
                                            'height': tooltip_height
                                        });
                                        $('#marker-tooltip').removeClass('scale-out-tl').removeClass('scale-out-tm').removeClass('scale-out-tr').removeClass('scale-out-bl').removeClass('scale-out-bm').removeClass('scale-out-br');
                                        $('#marker-tooltip').addClass(showClass);
                                        $('#marker-tooltip').data('url', marker.properties.tooltipUrl);
                                        if ($('#bt_showinmeters').hasClass('meters')) {
                                            $('#marker-tooltip').find('.peak-elevation-feet').hide();
                                            $('#marker-tooltip').find('.peak-elevation-meters').show();
                                        } else {
                                            $('#marker-tooltip').find('.peak-elevation-meters').hide();
                                            $('#marker-tooltip').find('.peak-elevation-feet').show();
                                        }
                                    } else {
                                        //console.log(peakval.slug);
                                        location = '/' + peakval.slug + '/';
                                    }
                                    e.stopPropagation();
                                });

                                el.addEventListener('mouseover', function(e) {

                                    if (!isTouchDevice()) {

                                        var bottom = $('#map-canvas').height();
                                        var right = $('#map-canvas').width();
                                        var showClass = 'scale-in-';
                                        var hideClass = 'scale-out-';

                                        var markerX = this.getBoundingClientRect().x + 14;

                                        if ($('#peak-search').css('left') == '0px') {
                                            markerX = markerX - 240;
                                        }

                                        var markerY = this.getBoundingClientRect().y + 14 - 120;

                                        if (markerY < (bottom / 2)) {
                                            marker_top = markerY;
                                            showClass = showClass + 't';
                                            hideClass = hideClass + 't';
                                        } else {
                                            marker_top = markerY - tooltip_total_height - 15;
                                            showClass = showClass + 'b';
                                            hideClass = hideClass + 'b';
                                        }

                                        if (markerX < (right / 3)) {
                                            marker_left = markerX;
                                            showClass = showClass + 'l';
                                            hideClass = hideClass + 'l';
                                        } else if (markerX >= (right / 3) && markerX < ((right / 3) * 2)) {
                                            marker_left = markerX - (tooltip_total_width / 2);
                                            showClass = showClass + 'm';
                                            hideClass = hideClass + 'm';
                                        } else {
                                            marker_left = markerX - tooltip_total_width;
                                            showClass = showClass + 'r';
                                            hideClass = hideClass + 'r';
                                        }

                                        $('#marker-tooltip').html(marker.properties.tooltipContent).css({
                                            'left': marker_left,
                                            'top': marker_top,
                                            'width': tooltip_width,
                                            'height': tooltip_height
                                        }).addClass(showClass).removeClass('scale-out-tl').removeClass('scale-out-tm').removeClass('scale-out-tr').removeClass('scale-out-bl').removeClass('scale-out-bm').removeClass('scale-out-br');
                                        $('#marker-tooltip').data('url', marker.properties.tooltipUrl);
                                        if ($('#bt_showinmeters').hasClass('meters')) {
                                            $('#marker-tooltip').find('.peak-elevation-feet').hide();
                                            $('#marker-tooltip').find('.peak-elevation-meters').show();
                                        } else {
                                            $('#marker-tooltip').find('.peak-elevation-meters').hide();
                                            $('#marker-tooltip').find('.peak-elevation-feet').show();
                                        }
                                    }
                                    e.stopPropagation();

                                });

                                el.addEventListener('mouseout', function(e) {
                                    if (isTouchDevice()) {
                                        //$('#marker-tooltip').hide();
                                    } else {
                                        hideMapTooltip();
                                    }
                                });

                                var marker = new mapboxgl.Marker(el)
                                    .setLngLat(latLng)
                                    .setOffset([-5, -10])
                                    .setDraggable(is_draggable);

                                marker.properties = {};
                                marker.properties.tooltipContent = tooltip_html;
                                marker.properties.tooltipUrl = tooltip_url;
                                marker.properties.iconstyle = iconstyle;
                                marker.properties.peakid = peakval.id;

                                {% if request.user.is_staff %}
                                    //editing functions
                                    marker.on('dragstart', function(e) {
                                        hideMapTooltip();
                                    });
                                    marker.on('dragend', function(e) {
                                        var point = marker.getLngLat();
                                        fix_item_location(peakval.id,point);
                                    });

                                    el.addEventListener('contextmenu', function(e) {
                                        if (confirm("Are you sure you want to delete this peak?")){
                                            delete_peak_from_map(peakval.id);
                                            hideMapTooltip();
                                            for (i = markersArray.length-1; i>=0; i--){
                                                if (markersArray[i].properties.peakid==peakval.id) {
                                                    markersArray[i].remove();
                                                    markersArray.splice(i,1);
                                                }
                                            }
                                        }
                                    });
                                {% endif %}

                                markersArray.push(marker);
                                LatLngList.push(latLng);

                            }

                            counter ++;
                        });

                        //add peak list items
                        aryPeakListItems.sort(function(a, b) {
                            return b.summits - a.summits;
                        });
                        for (var i=0; i < aryPeakListItems.length; i++) {
                            $('#peak-list').append(aryPeakListItems[i].peaklist_html);
                        }

                        if (!havePeaks) {
                            $('#ajax-data-loading').css('display', 'none');
                            $('#peak-search-mobile').html('<i class="fa fa-filter"></i>');
                            //didn't have any peaks, so remove all markers
                            delete_old_markers(val);
                            $('#no-peaks-in-list').css('display', 'flex');
                        } else {
                            //re-center the map on peaks on first load only
                            if (recenterMap) {
                                recenterMap = false;
                                var bounds = new mapboxgl.LngLatBounds();
                                for (var i = 0, LtLgLen = LatLngList.length; i < LtLgLen; i++) {
                                    bounds.extend(LatLngList[i]);
                                }
                                map.fitBounds(bounds, {padding: 50});
                            }
                            $('#ajax-data-loading').css('display', 'none');
                            $('#peak-search-mobile').html('<i class="fa fa-filter"></i>');

                        }
                    }

                    if (key=='photos') {

                        var havePhotos = false;
                        var aryPeakPhotos = [];
                        delete_all_photo_markers(val);

                        $.each( val, function( photokey, photoval ) {

                            if (!havePhotos) {

                                //first time through, delete all photo markers
                                delete_all_photo_markers(val);

                            }

                            havePhotos = true;

                            //tooltip vars
                            var tooltip_html, tooltip_width, tooltip_height, marker_top, marker_left, tooltip_total_width, tooltip_total_height;

                            //build tooltip string
                            tooltip_html = '<div style="width: 100%; height: 100%; border-radius: 8px; background-image: url({{ MEDIA_URL }}' + photoval.thumbnail_url + '); background-size: cover;"></div>';
                            tooltip_width = 165;
                            tooltip_total_width = 205;
                            tooltip_height = 165;
                            tooltip_total_height = 165;

                            var tooltip_url = '/' + photoval.fullsize_url;

                            var latLng = new mapboxgl.LngLat(photoval.photo_lng, photoval.photo_lat);

                            var latLng = [photoval.photo_lng, photoval.photo_lat];
                            //add marker
                            //create an HTML element for the marker
                            var el_div = document.createElement('div');
                            var el = document.createElement('a');

                            el_div.className = 'marker-photo-icon';
                            el.className = 'gallery-link';

                            //hide photos if necessary
                            if ($("#map-photos-toggle").is(':checked')) {
                                el.style.display = 'block';
                            } else {
                                el.style.display = 'none';
                            }

                            el_div.style.backgroundImage = 'url({{ MEDIA_URL }}' + photoval.thumbnail_url + ')';
                            el_div.style.width = '20px';
                            el_div.style.height = '20px';
                            el_div.style.border = '2px solid rgba(255,255,255)';
                            el_div.style.backgroundSize = 'cover';
                            el_div.setAttribute('id', 'photo-marker-'+photoval.id);

                            el.setAttribute('data-user', photoval.username);
                            el.setAttribute('data-gallery', '');
                            el.setAttribute('data-description', encodeDoubleQuotes(photoval.caption));
                            el.setAttribute('data-createdate', photoval.createdate);
                            el.setAttribute('data-peak-slug', photoval.peak_slug);
                            el.setAttribute('data-summit-log-id', photoval.summit_log_id);
                            el.setAttribute('href', '{{ MEDIA_URL }}' + photoval.fullsize_url);

                            el.appendChild(el_div);

                            el.addEventListener('mouseover', function(e) {

                                if (!isTouchDevice()) {

                                    var bottom = $('#map-canvas').height();
                                    var right = $('#map-canvas').width();
                                    var showClass = 'scale-in-';
                                    var hideClass = 'scale-out-';

                                    var markerX = this.getBoundingClientRect().x + 18;

                                    if ($('#peak-search').css('left') == '0px') {
                                        markerX = markerX - 240;
                                    }

                                    var markerY = this.getBoundingClientRect().y + 18 - 50;

                                    if (markerY < (bottom / 2)) {
                                        marker_top = markerY;
                                        showClass = showClass + 't';
                                        hideClass = hideClass + 't';
                                    } else {
                                        marker_top = markerY - tooltip_total_height - 45;
                                        showClass = showClass + 'b';
                                        hideClass = hideClass + 'b';
                                    }

                                    if (markerX < (right / 3)) {
                                        marker_left = markerX;
                                        showClass = showClass + 'l';
                                        hideClass = hideClass + 'l';
                                    } else if (markerX >= (right / 3) && markerX < ((right / 3) * 2)) {
                                        marker_left = markerX - (tooltip_total_width / 2);
                                        showClass = showClass + 'm';
                                        hideClass = hideClass + 'm';
                                    } else {
                                        marker_left = markerX - tooltip_total_width;
                                        showClass = showClass + 'r';
                                        hideClass = hideClass + 'r';
                                    }

                                    $('#photo-tooltip').html(marker.properties.tooltipContent).css({
                                        'left': marker_left,
                                        'top': marker_top,
                                        'width': tooltip_width,
                                        'height': tooltip_height
                                    }).addClass(showClass).removeClass('scale-out-tl').removeClass('scale-out-tm').removeClass('scale-out-tr').removeClass('scale-out-bl').removeClass('scale-out-bm').removeClass('scale-out-br');
                                    $('#photo-tooltip').data('url', marker.properties.tooltipUrl);
                                }
                                e.stopPropagation();

                            });

                            el.addEventListener('mouseout', function(e) {
                                if (isTouchDevice()) {
                                    //$('#marker-tooltip').hide();
                                } else {
                                    hidePhotoTooltip();
                                }
                            });

                            var marker = new mapboxgl.Marker(el)
                                .setLngLat(latLng)
                                .setOffset([-5, -10]);

                            marker.properties = {};
                            marker.properties.tooltipContent = tooltip_html;
                            marker.properties.tooltipUrl = tooltip_url;
                            marker.properties.photoid = photoval.id;
                            marker.properties.lat = photoval.photo_lat;
                            marker.properties.lng = photoval.photo_lng;

                            photoMarkersArray.push(marker);

                        });
                    }

                    if (key=='challenges') {

                        var haveChallenges = false;
                        var aryPeakChallenges = [];
                        delete_all_challenge_markers();

                        $.each( val, function( challengekey, challengeval ) {

                            if (!haveChallenges) {

                                //first time through, delete any markers not on map
                                //delete markers out of margins
                                delete_old_challenge_markers(val);

                            }

                            haveChallenges = true;

                            //tooltip vars
                            var tooltip_html, tooltip_width, tooltip_height, marker_top, marker_left, tooltip_total_width, tooltip_total_height;

                            //build tooltip string
                            tooltip_html = '<img class="map-tooltip-img hover-photos-hover" style="border-top-left-radius: 10px; border-top-right-radius: 10px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px;" src="{{ MEDIA_URL }}images/' + challengeval.thumbnail_url + '"><div class="map-tooltip-info" style="border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; bottom: -35px; height: 100px; background-color: transparent !important;"><div style="margin-left: -8px; margin-top: 43px; height: 55px; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; background-color: #fff; box-shadow: 0 5px 8px 0 rgba(0,0,0,0.30);"><div class="map-tooltip-peak-name ellipsis" style="color: #333; text-align: left; padding-left: 10px; font-size: 15px; width: 210px;">' + challengeval.name + '</div><div class="map-tooltip-peak-stats" style="color: #666; width: 205px; text-align: left; padding-left: 10px;">' + challengeval.peak_count + ' peak' + ((challengeval.peak_count != 1) ? 's' : '') + '</div></div></div>';
                            tooltip_width = 220;
                            tooltip_height = 165;
                            tooltip_total_width = 250;
                            tooltip_total_height = 230;

                            var tooltip_url = '/challenges/' + challengeval.slug + '/';

                            var latLng = new mapboxgl.LngLat(challengeval.challenge_lng, challengeval.challenge_lat);

                            var latLng = [challengeval.challenge_lng, challengeval.challenge_lat];
                            //add marker
                            //create an HTML element for the marker
                            var el_div = document.createElement('div');
                            var el = document.createElement('a');

                            el.className = 'marker-challenge-icon';

                            //hide challenges if necessary
                            if ($("#map-challenges-toggle").is(':checked')) {
                                el.style.display = 'block';
                            } else {
                                el.style.display = 'none';
                            }

                            el.style.backgroundImage = 'url({% static '' %}img/<EMAIL>)';
                            el.style.width = '28px';
                            el.style.height = '28px';
                            //el.style.border = '2px solid rgba(255,255,255)';
                            el.style.backgroundSize = 'cover';
                            el.setAttribute('id', 'challenge-marker-'+challengeval.id);

                            el.addEventListener('click', function(e) {
                                if (isTouchDevice()) {
                                    hideChallengeTooltip();

                                    var bottom = $('#map-canvas').height();
                                    var right = $('#map-canvas').width();
                                    var showClass = 'scale-in-';
                                    var hideClass = 'scale-out-';

                                    var markerX = this.getBoundingClientRect().x + 14;
                                    var markerY = this.getBoundingClientRect().y + 14 - 100;

                                    if (markerY < (bottom / 2)) {
                                        marker_top = markerY;
                                        showClass = showClass + 't';
                                        hideClass = hideClass + 't';
                                    } else {
                                        marker_top = markerY - tooltip_total_height - 15;
                                        showClass = showClass + 'b';
                                        hideClass = hideClass + 'b';
                                    }

                                    if (markerX < (right / 3)) {
                                        marker_left = markerX;
                                        showClass = showClass + 'l';
                                        hideClass = hideClass + 'l';
                                    } else if (markerX >= (right / 3) && markerX < ((right / 3) * 2)) {
                                        marker_left = markerX - (tooltip_total_width / 2);
                                        showClass = showClass + 'm';
                                        hideClass = hideClass + 'm';
                                    } else {
                                        marker_left = markerX - tooltip_total_width;
                                        showClass = showClass + 'r';
                                        hideClass = hideClass + 'r';
                                    }

                                    $('#challenge-tooltip').html(marker.properties.tooltipContent).css({
                                        'left': marker_left,
                                        'top': marker_top,
                                        'width': tooltip_width,
                                        'height': tooltip_height
                                    });
                                    $('#challenge-tooltip').removeClass('scale-out-tl').removeClass('scale-out-tm').removeClass('scale-out-tr').removeClass('scale-out-bl').removeClass('scale-out-bm').removeClass('scale-out-br');
                                    $('#challenge-tooltip').addClass(showClass);
                                    $('#challenge-tooltip').data('url', marker.properties.tooltipUrl);
                                } else {
                                    location = '/challenges/' + challengeval.slug;
                                }
                                e.stopPropagation();
                            });

                            el.addEventListener('mouseover', function(e) {

                                if (!isTouchDevice()) {

                                    var bottom = $('#map-canvas').height();
                                    var right = $('#map-canvas').width();
                                    var showClass = 'scale-in-';
                                    var hideClass = 'scale-out-';

                                    var markerX = this.getBoundingClientRect().x + 14;

                                    if ($('#peak-search').css('left') == '0px') {
                                        markerX = markerX - 240;
                                    }

                                    var markerY = this.getBoundingClientRect().y + 14 - 50;

                                    if (markerY < (bottom / 2)) {
                                        marker_top = markerY;
                                        showClass = showClass + 't';
                                        hideClass = hideClass + 't';
                                    } else {
                                        marker_top = markerY - tooltip_total_height - 15;
                                        showClass = showClass + 'b';
                                        hideClass = hideClass + 'b';
                                    }

                                    if (markerX < (right / 3)) {
                                        marker_left = markerX;
                                        showClass = showClass + 'l';
                                        hideClass = hideClass + 'l';
                                    } else if (markerX >= (right / 3) && markerX < ((right / 3) * 2)) {
                                        marker_left = markerX - (tooltip_total_width / 2);
                                        showClass = showClass + 'm';
                                        hideClass = hideClass + 'm';
                                    } else {
                                        marker_left = markerX - tooltip_total_width;
                                        showClass = showClass + 'r';
                                        hideClass = hideClass + 'r';
                                    }

                                    $('#challenge-tooltip').html(marker.properties.tooltipContent).css({
                                        'left': marker_left,
                                        'top': marker_top,
                                        'width': tooltip_width,
                                        'height': tooltip_height
                                    }).addClass(showClass).removeClass('scale-out-tl').removeClass('scale-out-tm').removeClass('scale-out-tr').removeClass('scale-out-bl').removeClass('scale-out-bm').removeClass('scale-out-br');
                                    $('#challenge-tooltip').data('url', marker.properties.tooltipUrl);
                                }
                                e.stopPropagation();

                            });

                            el.addEventListener('mouseout', function(e) {
                                if (isTouchDevice()) {
                                    //$('#marker-tooltip').hide();
                                } else {
                                    hideChallengeTooltip();
                                }
                            });

                            var marker = new mapboxgl.Marker(el)
                                .setLngLat(latLng)
                                .setOffset([-5, -10]);

                            marker.properties = {};
                            marker.properties.tooltipContent = tooltip_html;
                            marker.properties.tooltipUrl = tooltip_url;
                            marker.properties.photoid = challengeval.id;
                            marker.properties.lat = challengeval.challenge_lat;
                            marker.properties.lng = challengeval.challenge_lng;

                            challengeMarkersArray.push(marker);

                        });
                    }

                    //add photo markers
                    if (NewMapZoom >= 13) {
                        for (var i = photoMarkersArray.length - 1; i >= 0; --i) {
                            photoMarkersArray[i].addTo(map);
                        }
                    }

                    //add challenge markers
                    for (var i = challengeMarkersArray.length - 1; i >= 0; --i) {
                        challengeMarkersArray[i].addTo(map);
                    }

                    //add peak markers to map in reverse order so highest on top
                    for (var i = markersArray.length - 1; i >= 0; --i) {
                        markersArray[i].addTo(map);
                    }

                });

                //show classics if needed
                if ($("#map-classics-toggle").is(':checked')) {
                    $('.mapboxgl-marker[data-classic=true]').addClass('marker_icon_classic');
                }

                //show kom if needed
                if ($("#map-kom-toggle").is(':checked')) {
                    $('.mapboxgl-marker[data-kom=true]').addClass('marker_icon_kom');
                }

                //show first ascents if needed
                if ($("#map-firstascents-toggle").is(':checked')) {
                    $('.mapboxgl-marker[data-firstascent=true]').addClass('marker_icon_firstascent');
                }

                //show summit stewards if needed
                if ($("#map-stewards-toggle").is(':checked')) {
                    $('.mapboxgl-marker[data-steward=true]').addClass('marker_icon_steward');
                }

                //hide unclimbed if needed
                if (!$("#map-unclimbed-toggle").is(':checked')) {
                    $('.marker_icon').hide();
                }

                //hide attempts if needed
                if (!$("#map-attempts-toggle").is(':checked')) {
                    $('.marker_icon_yellow').hide();
                }

                //hide summits if needed
                if (!$("#map-summits-toggle").is(':checked')) {
                    $('.marker_icon_green').hide();
                }

            });

        }

        function initPeaks() {

            var vars = [], hash, keyword, near, wanted_elev_min, wanted_elev_max, wanted_prom_min, wanted_prom_max, wanted_summits_min, wanted_summits_max, wanted_difficulty_min, wanted_difficulty_max, wanted_length_min, wanted_length_max, wanted_vertical_min, wanted_vertical_max, wanted_last_climbed_min, wanted_last_climbed_max, classics, in_challenge, you_climbed, lat, lng;
            var q = document.URL.split('#')[1];
            if(q != undefined){
                q = q.split('&');
                for(var i = 0; i < q.length; i++){
                    hash = q[i].split('=');
                    vars.push(hash[1]);
                    vars[hash[0]] = hash[1];
                }
            }

            if (vars['q'] != undefined) {
                keyword = vars['q'];
            } else {
                keyword = '';
            }

            if (vars['n'] != undefined) {
                near = vars['n'];
            } else {
                near = '';
            }

            if (vars['elev_min'] != undefined) {
                wanted_elev_min = vars['elev_min'];
            } else {
                wanted_elev_min = '0';
            }

            if (vars['elev_max'] != undefined) {
                wanted_elev_max = vars['elev_max'];
            } else {
                //if elevation in meters
                if ($('#bt_showinmeters').text() == '[ft]') {
                    wanted_elev_max = '29527';
                } else {
                    wanted_elev_max = '29500';
                }
            }

            if (vars['prom_min'] != undefined) {
                wanted_prom_min = vars['prom_min'];
            } else {
                wanted_prom_min = '0';
            }

            if (vars['prom_max'] != undefined) {
                wanted_prom_max = vars['prom_max'];
            } else {
                //if prominence in meters
                if ($('#prominence_bt_showinmeters').text() == '[ft]') {
                    wanted_prom_max = '29527';
                } else {
                    wanted_prom_max = '29500';
                }
            }

            if (vars['summits_min'] != undefined) {
                wanted_summits_min = vars['summits_min'];
            } else {
                wanted_summits_min = '0';
            }

            if (vars['summits_max'] != undefined) {
                wanted_summits_max = vars['summits_max'];
            } else {
                wanted_summits_max = '500';
            }

            if (vars['difficulty_min'] != undefined) {
                wanted_difficulty_min = vars['difficulty_min'];
            } else {
                wanted_difficulty_min = '1';
            }

            if (vars['difficulty_max'] != undefined) {
                wanted_difficulty_max = vars['difficulty_max'];
            } else {
                wanted_difficulty_max = '5';
            }

            if (vars['length_min'] != undefined) {
                wanted_length_min = vars['length_min'];
            } else {
                wanted_length_min = '0';
            }

            if (vars['length_max'] != undefined) {
                wanted_length_max = vars['length_max'];
            } else {
                wanted_length_max = '20';
            }

            if (vars['vertical_min'] != undefined) {
                wanted_vertical_min = vars['vertical_min'];
            } else {
                wanted_vertical_min = '0';
            }

            if (vars['vertical_max'] != undefined) {
                wanted_vertical_max = vars['vertical_max'];
            } else {
                wanted_vertical_max = '10000';
            }

            if (vars['last_climbed_min'] != undefined) {
                wanted_last_climbed_min = vars['last_climbed_min'];
            } else {
                wanted_last_climbed_min = '0';
            }

            if (vars['last_climbed_max'] != undefined) {
                wanted_last_climbed_max = vars['last_climbed_max'];
            } else {
                wanted_last_climbed_max = '11';
            }

            if (vars['classics'] != undefined) {
                classics = vars['classics'];
            } else {
                classics = 'false';
            }

            if (vars['in_challenge'] != undefined) {
                in_challenge = vars['in_challenge'];
            } else {
                in_challenge = 'false';
            }

            if (vars['you_climbed'] != undefined) {
                you_climbed = vars['you_climbed'];
            } else {
                you_climbed = undefined;
            }

            if (vars['lat'] != undefined) {
                lat = vars['lat'];
            } else {
                lat = '';
            }

            if (vars['lng'] != undefined) {
                lng = vars['lng'];
            } else {
                lng = '';
            }

            loadPeaks(keyword, near, wanted_elev_min, wanted_elev_max, wanted_prom_min, wanted_prom_max, wanted_summits_min, wanted_summits_max, wanted_difficulty_min, wanted_difficulty_max, wanted_length_min, wanted_length_max, wanted_vertical_min, wanted_vertical_max, wanted_last_climbed_min, wanted_last_climbed_max, classics, in_challenge, you_climbed, lat, lng);

        }

    var markersArray = [];
    var photoMarkersArray = [];
    var challengeMarkersArray = [];
    var initLoad = true;
    var recenterMap = true;

    function initialize() {

        var mapDiv = document.getElementById('map-canvas');

        var latLng = [{{ group.long }}, {{ group.lat }}];
        var initZoom = 6;

        var mapType = readCookie('map_type');
        if (mapType != '' && mapType != null) {
            initMapType(mapType);
        }

        var LatLngList = [];

        if (isTouchDevice()) {
            map = new mapboxgl.Map({
                container: mapDiv, // HTML container id
                style: mapStyle, // style URL
                center: latLng, // starting position as [lng, lat]
                zoom: initZoom
            });
        } else {
            map = new mapboxgl.Map({
                container: mapDiv, // HTML container id
                style: mapStyle, // style URL
                center: latLng, // starting position as [lng, lat]
                zoom: initZoom
            });
            scale = new mapboxgl.ScaleControl({maxWidth: 120, unit: 'imperial'});
            map.addControl(scale, 'bottom-right');
            var nav = new mapboxgl.NavigationControl();
            map.addControl(nav, 'bottom-right');
            // disable map rotation using right click + drag
            map.dragRotate.disable();
            // disable map rotation using touch rotation gesture
            map.touchZoomRotate.disableRotation();
        }

        // Add geolocate control to the map.
        map_position = new mapboxgl.GeolocateControl({positionOptions: {enableHighAccuracy: true}, trackUserLocation: true});
        map.addControl(map_position, 'bottom-right');

        //Add fullscreen control to the map.
        map_fullscreen = new mapboxgl.FullscreenControl();
        map.addControl(map_fullscreen, 'bottom-right');

        map.on('click', function(e) {
            if (isTouchDevice()) {
                hideMapTooltip();
                hidePhotoTooltip();
                hideChallengeTooltip();
                $('#gm-custom-mapdropdown').hide();
                $('#gm-custom-maplegend-dropdown').hide();
            } else {
                hideMapTooltip();
                hidePhotoTooltip();
                hideChallengeTooltip();
                $('#gm-custom-mapdropdown').hide();
                $('#gm-custom-maplegend-dropdown').hide();
            }
        });

        map.on('dragstart', function(e) {
            if (isTouchDevice()) {
                hideMapTooltip();
                hidePhotoTooltip();
                hideChallengeTooltip();
                $('#gm-custom-mapdropdown').hide();
                $('#gm-custom-maplegend-dropdown').hide();
            } else {
                hideMapTooltip();
                hidePhotoTooltip();
                hideChallengeTooltip();
                $('#gm-custom-mapdropdown').hide();
                $('#gm-custom-maplegend-dropdown').hide();
            }
        });

        map.on('moveend', function () {
            if (initLoad) {
                initLoad = false;
                initPeaks();
            }
        });

        map.on('load', function () {
            if (initLoad) {
                initLoad = false;
                initPeaks();
            }
            var mapUnits = readCookie('map_units');
            if (mapUnits != '') {
                toggleMapUnits(mapUnits);
                setSliderUnits(mapUnits);
            }
            //setMapControls();
        });

    }

    function setMapControls() {
        var check = checkIfMapboxStyleIsLoaded();
        if (!check) {
            // It's not safe to manipulate layers yet, so wait 200ms and then check again
            setTimeout(function() {
                setMapControls();
            }, 200);
            return;
        }
        // Whew, now it's safe to manipulate layers!
        var mapUnits = readCookie('map_units');
        if (mapUnits == 'meters') {
            toggleMapUnits(mapUnits);
            //also update sliders
            setSliderUnits(mapUnits);
        }
        //loadPeaksFromHash();
    }

    function addExtraMapLayers(type='') {
        var check = checkIfMapboxStyleIsLoaded();
        if (!check) {
            // It's not safe to manipulate layers yet, so wait 200ms and then check again
            setTimeout(function() {
                addExtraMapLayers(type);
            }, 200);
            return;
        }
        // Whew, now it's safe to manipulate layers!
        // No extra map layers necessary

        if (type == 'satellite_3d') {
            try {
                map.setTerrain(null);
            }
            catch (e) {
                //pass
            }
            // add the DEM source as a terrain layer with exaggerated height
            try {
                map.setTerrain({'source': 'mapbox-dem', 'exaggeration': 1.2});
            }
            catch (e) {
                map.addSource('mapbox-dem', {
                    'type': 'raster-dem',
                    'url': 'mapbox://mapbox.mapbox-terrain-dem-v1',
                    'tileSize': 512,
                    'maxzoom': 14
                });
                map.setTerrain({'source': 'mapbox-dem', 'exaggeration': 1.2});
            }
            // add the sky layer
            try {
                map.addLayer({
                    'id': 'sky',
                    'type': 'sky',
                    'paint': {
                        'sky-type': 'atmosphere',
                        'sky-atmosphere-sun': [0.0, 0.0],
                        'sky-atmosphere-sun-intensity': 15
                    }
                });
            }
            catch (e) {
                //pass
            }
            // enable map rotation using right click + drag
            map.dragRotate.enable();
            // enable map rotation using touch rotation gesture
            map.touchZoomRotate.enableRotation();
            // set pitch to 80 degrees
            //map.setPitch(80);
            map.easeTo({pitch: 80});

        } else {

            //switch to 2d mode
            map.setTerrain(null);
            // disable map rotation using right click + drag
            map.dragRotate.disable();
            // disable map rotation using touch rotation gesture
            map.touchZoomRotate.disableRotation();
            //set pitch back to 0
            map.setPitch(0);
            //map.easeTo({pitch: 0});
            //set bearing back to 0
            map.setBearing(0);

        }
    }

    function setSliderUnits(units) {

        if (units == null) {
            units = 'feet';
        }

        //elevation
        var $this = $('#bt_showinmeters');
        var elevationSlider = document.getElementById('slider-range');
        if (units == 'feet') {
            $('#amount-meters').hide();
            $('#amount').show();

            $this.removeClass('meters');
            $this.text('[m]');
            scale.setUnit('imperial');

            //reset elevation slider
            elevationSlider.noUiSlider.updateOptions({
                range: {
                    'min': 0,
                    '3.3%': 500,
                    '5.0%': 1000,
                    '6.7%': 1500,
                    '8.3%': 2000,
                    '10.0%': 2500,
                    '11.7%': 3000,
                    '13.3%': 3500,
                    '15.0%': 4000,
                    '16.7%': 4500,
                    '18.3%': 5000,
                    '20.0%': 5500,
                    '21.7%': 6000,
                    '23.3%': 6500,
                    '25.0%': 7000,
                    '26.7%': 7500,
                    '28.3%': 8000,
                    '30.0%': 8500,
                    '31.7%': 9000,
                    '33.3%': 9500,
                    '35.0%': 10000,
                    '36.7%': 10500,
                    '38.3%': 11000,
                    '40.0%': 11500,
                    '41.7%': 12000,
                    '43.3%': 12500,
                    '45.0%': 13000,
                    '46.7%': 13500,
                    '48.3%': 14000,
                    '50.0%': 14500,
                    '51.7%': 15000,
                    '53.3%': 15500,
                    '55.0%': 16000,
                    '56.7%': 16500,
                    '58.3%': 17000,
                    '60.0%': 17500,
                    '61.7%': 18000,
                    '63.3%': 18500,
                    '65.0%': 19000,
                    '66.7%': 19500,
                    '68.3%': 20000,
                    '70.0%': 20500,
                    '71.7%': 21000,
                    '73.3%': 21500,
                    '75.0%': 22000,
                    '76.7%': 22500,
                    '78.3%': 23000,
                    '80.0%': 23500,
                    '81.7%': 24000,
                    '83.3%': 24500,
                    '85.0%': 25000,
                    '86.7%': 25500,
                    '88.3%': 26000,
                    '90.0%': 26500,
                    '91.7%': 27000,
                    '93.3%': 27500,
                    '95.0%': 28000,
                    '96.7%': 28500,
                    '98.3%': 29000,
                    'max': 29500
                }
            });
            elevationSlider.noUiSlider.set([0, 29500]);
            $('#elevation-label').css('color','#aaa');
            $('#amount').css('color','#aaa');
            $('#amount').css('-webkit-text-fill-color','#aaa');
            $('#slider-range > div > div').css('background-color','#ccc');
            $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
            $( "#amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29500) + " ft");
            $( "#amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(9000) + " m");
            u = updateURLParameter(window.location.hash, 'elev_min', 0);
            u = updateURLParameter('#'+u, 'elev_max', 29500);
            window.location.hash = u;
            //loadPeaksFromHash();
        } else {
            $('#amount').hide();
            $('#amount-meters').show();

            $this.addClass('meters');
            $this.text('[ft]');
            scale.setUnit('metric');

            //reset elevation slider
            elevationSlider.noUiSlider.updateOptions({
                range: {
                    'min': 0,
                    '4.3%': 200,
                    '6.5%': 400,
                    '8.7%': 600,
                    '10.9%': 800,
                    '13.0%': 1000,
                    '15.2%': 1200,
                    '17.4%': 1400,
                    '19.6%': 1600,
                    '21.7%': 1800,
                    '23.9%': 2000,
                    '26.1%': 2200,
                    '28.3%': 2400,
                    '30.4%': 2600,
                    '32.6%': 2800,
                    '34.8%': 3000,
                    '37.0%': 3200,
                    '39.1%': 3400,
                    '41.3%': 3600,
                    '43.5%': 3800,
                    '45.7%': 4000,
                    '47.8%': 4200,
                    '50.0%': 4400,
                    '52.2%': 4600,
                    '54.3%': 4800,
                    '56.5%': 5000,
                    '58.7%': 5200,
                    '60.9%': 5400,
                    '63.0%': 5600,
                    '65.2%': 5800,
                    '67.4%': 6000,
                    '69.6%': 6200,
                    '71.7%': 6400,
                    '73.9%': 6600,
                    '76.1%': 6800,
                    '78.3%': 7000,
                    '80.4%': 7200,
                    '82.6%': 7400,
                    '84.8%': 7600,
                    '87.0%': 7800,
                    '89.1%': 8000,
                    '91.3%': 8200,
                    '93.5%': 8400,
                    '95.7%': 8600,
                    '97.8%': 8800,
                    'max': 9000
                }
            });
            elevationSlider.noUiSlider.set([0, 9000]);
            $('#elevation-label').css('color','#aaa');
            $('#amount').css('color','#aaa');
            $('#amount').css('-webkit-text-fill-color','#aaa');
            $('#slider-range > div > div').css('background-color','#ccc');
            $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
            $( "#amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29527) + " ft");
            $( "#amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(9000) + " m");
            u = updateURLParameter(window.location.hash, 'elev_min', 0);
            u = updateURLParameter('#'+u, 'elev_max', 29527);
            window.location.hash = u;
            //loadPeaksFromHash();
        }

        //prominence
        var $this = $('#prominence_bt_showinmeters');
        var prominenceSlider = document.getElementById('prominence-slider-range');
        if (units == 'feet') {
            $('#prominence-amount-meters').hide();
            $('#prominence-amount').show();

            $this.removeClass('meters');
            $this.text('[m]');
            scale.setUnit('imperial');

            //reset prominence slider
            prominenceSlider.noUiSlider.updateOptions({
                range: {
                    'min': 0,
                    '3.1%': 50,
                    '4.7%': 100,
                    '6.3%': 200,
                    '7.8%': 300,
                    '9.4%': 500,
                    '10.9%': 1000,
                    '12.5%': 1500,
                    '14.1%': 2000,
                    '15.6%': 2500,
                    '17.2%': 3000,
                    '18.8%': 3500,
                    '20.3%': 4000,
                    '21.9%': 4500,
                    '23.4%': 5000,
                    '25.0%': 5500,
                    '26.6%': 6000,
                    '28.1%': 6500,
                    '29.7%': 7000,
                    '31.3%': 7500,
                    '32.8%': 8000,
                    '34.4%': 8500,
                    '35.9%': 9000,
                    '37.5%': 9500,
                    '39.1%': 10000,
                    '40.6%': 10500,
                    '42.2%': 11000,
                    '43.8%': 11500,
                    '45.3%': 12000,
                    '46.9%': 12500,
                    '48.4%': 13000,
                    '50.0%': 13500,
                    '51.6%': 14000,
                    '53.1%': 14500,
                    '54.7%': 15000,
                    '56.3%': 15500,
                    '57.8%': 16000,
                    '59.4%': 16500,
                    '60.9%': 17000,
                    '62.5%': 17500,
                    '64.1%': 18000,
                    '65.6%': 18500,
                    '67.2%': 19000,
                    '68.8%': 19500,
                    '70.3%': 20000,
                    '71.9%': 20500,
                    '73.4%': 21000,
                    '75.0%': 21500,
                    '76.6%': 22000,
                    '78.1%': 22500,
                    '79.7%': 23000,
                    '81.3%': 23500,
                    '82.8%': 24000,
                    '84.4%': 24500,
                    '85.9%': 25000,
                    '87.5%': 25500,
                    '89.1%': 26000,
                    '90.6%': 26500,
                    '92.2%': 27000,
                    '93.8%': 27500,
                    '95.3%': 28000,
                    '96.9%': 28500,
                    '98.4%': 29000,
                    'max': 29500
                }
            });
            prominenceSlider.noUiSlider.set([0, 29500]);
            $('#prominence-label').css('color','#aaa');
            $('#prominence-amount').css('color','#aaa');
            $('#prominence-amount').css('-webkit-text-fill-color','#aaa');
            $('#prominence-slider-range > div > div').css('background-color','#ccc');
            $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
            $( "#prominence-amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29500) + " ft");
            $( "#prominence-amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(9000) + " m");
            u = updateURLParameter(window.location.hash, 'prom_min', 0);
            u = updateURLParameter('#'+u, 'prom_max', 29500);
            window.location.hash = u;
            //loadPeaksFromHash();
        } else {
            $('#prominence-amount').hide();
            $('#prominence-amount-meters').show();

            $this.addClass('meters');
            $this.text('[ft]');
            scale.setUnit('metric');

            //reset prominence slider
            prominenceSlider.noUiSlider.updateOptions({
                range: {
                    'min': 0,
                    '2.1%': 10,
                    '3.2%': 20,
                    '4.2%': 30,
                    '5.3%': 50,
                    '6.3%': 100,
                    '7.4%': 200,
                    '8.4%': 300,
                    '9.5%': 400,
                    '10.5%': 500,
                    '11.6%': 600,
                    '12.6%': 700,
                    '13.7%': 800,
                    '14.7%': 900,
                    '15.8%': 1000,
                    '16.8%': 1100,
                    '17.9%': 1200,
                    '18.9%': 1300,
                    '20.0%': 1400,
                    '21.1%': 1500,
                    '22.1%': 1600,
                    '23.2%': 1700,
                    '24.2%': 1800,
                    '25.3%': 1900,
                    '26.3%': 2000,
                    '27.4%': 2100,
                    '28.4%': 2200,
                    '29.5%': 2300,
                    '30.5%': 2400,
                    '31.6%': 2500,
                    '32.6%': 2600,
                    '33.7%': 2700,
                    '34.7%': 2800,
                    '35.8%': 2900,
                    '36.8%': 3000,
                    '37.9%': 3100,
                    '38.9%': 3200,
                    '40.0%': 3300,
                    '41.1%': 3400,
                    '42.1%': 3500,
                    '43.2%': 3600,
                    '44.2%': 3700,
                    '45.3%': 3800,
                    '46.3%': 3900,
                    '47.4%': 4000,
                    '48.4%': 4100,
                    '49.5%': 4200,
                    '50.5%': 4300,
                    '51.6%': 4400,
                    '52.6%': 4500,
                    '53.7%': 4600,
                    '54.7%': 4700,
                    '55.8%': 4800,
                    '56.8%': 4900,
                    '57.9%': 5000,
                    '58.9%': 5100,
                    '60.0%': 5200,
                    '61.1%': 5300,
                    '62.1%': 5400,
                    '63.2%': 5500,
                    '64.2%': 5600,
                    '65.3%': 5700,
                    '66.3%': 5800,
                    '67.4%': 5900,
                    '68.4%': 6000,
                    '69.5%': 6100,
                    '70.5%': 6200,
                    '71.6%': 6300,
                    '72.6%': 6400,
                    '73.7%': 6500,
                    '74.7%': 6600,
                    '75.8%': 6700,
                    '76.8%': 6800,
                    '77.9%': 6900,
                    '78.9%': 7000,
                    '80.0%': 7100,
                    '81.1%': 7200,
                    '82.1%': 7300,
                    '83.2%': 7400,
                    '84.2%': 7500,
                    '85.3%': 7600,
                    '86.3%': 7700,
                    '87.4%': 7800,
                    '88.4%': 7900,
                    '89.5%': 8000,
                    '90.5%': 8100,
                    '91.6%': 8200,
                    '92.6%': 8300,
                    '93.7%': 8400,
                    '94.7%': 8500,
                    '95.8%': 8600,
                    '96.8%': 8700,
                    '97.9%': 8800,
                    '98.9%': 8900,
                    'max': 9000
                }
            });
            prominenceSlider.noUiSlider.set([0, 9000]);
            $('#prominence-label').css('color','#aaa');
            $('#prominence-amount').css('color','#aaa');
            $('#prominence-amount').css('-webkit-text-fill-color','#aaa');
            $('#prominence-slider-range > div > div').css('background-color','#ccc');
            $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
            $( "#prominence-amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29527) + " ft");
            $( "#prominence-amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(9000) + " m");
            u = updateURLParameter(window.location.hash, 'prom_min', 0);
            u = updateURLParameter('#'+u, 'prom_max', 29527);
            window.location.hash = u;
            //loadPeaksFromHash();
        }

        //length
        var $this = $('#length_bt_showinmeters');
        var lengthSlider = document.getElementById('length-slider-range');
        if (units == 'feet') {
            $('#length-amount-meters').hide();
            $('#length-amount').show();

            $this.removeClass('meters');
            $this.text('[m]');
            scale.setUnit('imperial');

            //reset length slider
            lengthSlider.noUiSlider.updateOptions({
                range: {
                    'min': 0,
                    '5.0%': 1,
                    '10.0%': 2,
                    '15.0%': 3,
                    '20.0%': 4,
                    '25.0%': 5,
                    '30.0%': 6,
                    '35.0%': 7,
                    '40.0%': 8,
                    '45.0%': 9,
                    '50.0%': 10,
                    '55.0%': 11,
                    '60.0%': 12,
                    '65.0%': 13,
                    '70.0%': 14,
                    '75.0%': 15,
                    '80.0%': 16,
                    '85.0%': 17,
                    '90.0%': 18,
                    '95.0%': 19,
                    'max': 20
                }
            });
            lengthSlider.noUiSlider.set([0, 20]);
            $('#length-label').css('color','#aaa');
            $('#length-amount').css('color','#aaa');
            $('#length-amount').css('-webkit-text-fill-color','#aaa');
            $('#length-slider-range > div > div').css('background-color','#ccc');
            $('#length-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
            $( "#length-amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(20) + "+ mi");
            $( "#length-amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(32) + "+ km");
            u = updateURLParameter(window.location.hash, 'length_min', 0);
            u = updateURLParameter('#'+u, 'length_max', 20);
            window.location.hash = u;
            //loadPeaksFromHash();
        } else {
            $('#length-amount').hide();
            $('#length-amount-meters').show();

            $this.addClass('meters');
            $this.text('[ft]');
            scale.setUnit('metric');

            //reset length slider
            lengthSlider.noUiSlider.updateOptions({
                range: {
                    'min': 0,
                    '3.0%': 1,
                    '6.0%': 2,
                    '9.0%': 3,
                    '12.0%': 4,
                    '15.0%': 5,
                    '18.0%': 6,
                    '21.0%': 7,
                    '24.0%': 8,
                    '27.0%': 9,
                    '30.0%': 10,
                    '33.0%': 11,
                    '36.0%': 12,
                    '39.0%': 13,
                    '42.0%': 14,
                    '45.0%': 15,
                    '48.0%': 16,
                    '51.0%': 17,
                    '54.0%': 18,
                    '57.0%': 19,
                    '60.0%': 20,
                    '63.0%': 21,
                    '66.0%': 22,
                    '69.0%': 23,
                    '72.0%': 24,
                    '75.0%': 25,
                    '78.0%': 26,
                    '81.0%': 27,
                    '84.0%': 28,
                    '87.0%': 29,
                    '90.0%': 30,
                    '95.0%': 31,
                    'max': 32
                }
            });
            lengthSlider.noUiSlider.set([0, 32]);
            $('#length-label').css('color','#aaa');
            $('#length-amount').css('color','#aaa');
            $('#length-amount').css('-webkit-text-fill-color','#aaa');
            $('#length-slider-range > div > div').css('background-color','#ccc');
            $('#length-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
            $( "#length-amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(20) + "+ mi");
            $( "#length-amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(32) + "+ km");
            u = updateURLParameter(window.location.hash, 'length_min', 0);
            u = updateURLParameter('#'+u, 'length_max', 20);
            window.location.hash = u;
            //loadPeaksFromHash();
        }

        //vertical
        var $this = $('#vertical_bt_showinmeters');
        var verticalSlider = document.getElementById('vertical-slider-range');
        if (units == 'feet') {
            $('#vertical-amount-meters').hide();
            $('#vertical-amount').show();

            $this.removeClass('meters');
            $this.text('[m]');
            scale.setUnit('imperial');

            //reset vertical slider
            verticalSlider.noUiSlider.updateOptions({
                range: {
                    'min': 0,
                    '9.0%': 500,
                    '18.0%': 1000,
                    '27.0%': 2000,
                    '36.0%': 3000,
                    '45.0%': 4000,
                    '54.0%': 5000,
                    '63.0%': 6000,
                    '72.0%': 7000,
                    '81.0%': 8000,
                    '90.0%': 9000,
                    'max': 10000
                }
            });
            verticalSlider.noUiSlider.set([0, 10000]);
            $('#vertical-label').css('color','#aaa');
            $('#vertical-amount').css('color','#aaa');
            $('#vertical-amount').css('-webkit-text-fill-color','#aaa');
            $('#vertical-slider-range > div > div').css('background-color','#ccc');
            $('#vertical-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
            $( "#vertical-amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(10000) + "+ ft");
            $( "#vertical-amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(3000) + "+ m");
            u = updateURLParameter(window.location.hash, 'vertical_min', 0);
            u = updateURLParameter('#'+u, 'vertical_max', 10000);
            window.location.hash = u;
            //loadPeaksFromHash();
        } else {
            $('#vertical-amount').hide();
            $('#vertical-amount-meters').show();

            $this.addClass('meters');
            $this.text('[ft]');
            scale.setUnit('metric');

            //reset vertical slider
            verticalSlider.noUiSlider.updateOptions({
                range: {
                    'min': 0,
                    '10.0%': 300,
                    '20.0%': 600,
                    '30.0%': 900,
                    '40.0%': 1200,
                    '50.0%': 1500,
                    '60.0%': 1800,
                    '70.0%': 2100,
                    '80.0%': 2400,
                    '90.0%': 2700,
                    'max': 3000
                }
            });
            verticalSlider.noUiSlider.set([0, 3000]);
            $('#vertical-label').css('color','#aaa');
            $('#vertical-amount').css('color','#aaa');
            $('#vertical-amount').css('-webkit-text-fill-color','#aaa');
            $('#vertical-slider-range > div > div').css('background-color','#ccc');
            $('#vertical-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
            $( "#vertical-amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(9842) + "+ ft");
            $( "#vertical-amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(3000) + "+ m");
            u = updateURLParameter(window.location.hash, 'vertical_min', 0);
            u = updateURLParameter('#'+u, 'vertical_max', 9842);
            window.location.hash = u;
            //loadPeaksFromHash();
        }

    }

    $(document).ready(function(){

        var showPhotos = readCookie('map_photos');
        if (showPhotos == 'true') {
            $('#map-photos-toggle').prop("checked", true);
        } else if (showPhotos === null) {
            createCookie('map_photos',true,365);
            $('#map-photos-toggle').prop("checked", true);
        } else {
            $('#map-photos-toggle').prop( "checked", false );
        }

        var showClassics = readCookie('map_classics');
        if (showClassics == 'true') {
            $('#map-classics-toggle').prop( "checked", true );
        } else if (showClassics === null) {
            createCookie('map_classics',true,365);
            $('#map-classics-toggle').prop("checked", true);
        } else {
            $('#map-classics-toggle').prop( "checked", false );
        }

        var showChallenges = readCookie('map_challenges');
        if (showChallenges == 'true') {
            $('#map-challenges-toggle').prop( "checked", true );
        } else if (showChallenges === null) {
            createCookie('map_challenges',true,365);
            $('#map-challenges-toggle').prop("checked", true);
        } else {
            $('#map-challenges-toggle').prop( "checked", false );
        }

        var showFirstAscents = readCookie('map_firstascents');
        if (showFirstAscents == 'true') {
            $('#map-firstascents-toggle').prop( "checked", true );
        } else if (showFirstAscents === null) {
            createCookie('map_firstascents',true,365);
            $('#map-firstascents-toggle').prop("checked", true);
        } else {
            $('#map-firstascents-toggle').prop( "checked", false );
        }

        var showStewards = readCookie('map_stewards');
        if (showStewards == 'true') {
            $('#map-stewards-toggle').prop( "checked", true );
        } else if (showStewards === null) {
            createCookie('map_stewards',true,365);
            $('#map-stewards-toggle').prop("checked", true);
        } else {
            $('#map-stewards-toggle').prop( "checked", false );
        }

        var showKOM = readCookie('map_kom');
        if (showKOM == 'true') {
            $('#map-kom-toggle').prop( "checked", true );
        } else if (showKOM === null) {
            createCookie('map_kom',true,365);
            $('#map-kom-toggle').prop("checked", true);
        } else {
            $('#map-kom-toggle').prop( "checked", false );
        }

        var showUnclimbed = readCookie('map_unclimbed');
        if (showUnclimbed == 'true') {
            $('#map-unclimbed-toggle').prop( "checked", true );
        } else if (showUnclimbed === null) {
            createCookie('map_unclimbed',true,365);
            $('#map-unclimbed-toggle').prop("checked", true);
        } else {
            $('#map-unclimbed-toggle').prop( "checked", false );
        }

        var showAttempts = readCookie('map_attempts');
        if (showAttempts == 'true') {
            $('#map-attempts-toggle').prop( "checked", true );
        } else if (showAttempts === null) {
            createCookie('map_attempts',true,365);
            $('#map-attempts-toggle').prop("checked", true);
        } else {
            $('#map-attempts-toggle').prop( "checked", false );
        }

        var showSummits = readCookie('map_summits');
        if (showSummits == 'true') {
            $('#map-summits-toggle').prop( "checked", true );
        } else if (showSummits === null) {
            createCookie('map_summits',true,365);
            $('#map-summits-toggle').prop("checked", true);
        } else {
            $('#map-summits-toggle').prop( "checked", false );
        }

        $('#close-gallery').on('click', function (event) {
            $('#blueimp-gallery').data('gallery').close();
        });

        $('#blueimp-gallery').on('open', function (event) {
            $('body,html').css('overflow','visible');
            var gallery = $('#blueimp-gallery').data('gallery');
            //gallery.options['enableKeyboardNavigation'] = false;
            var index = gallery.index;
            var caption = gallery.list[index].getAttribute('data-description'),
                username = gallery.list[index].getAttribute('data-user'),
                credit = gallery.list[index].getAttribute('data-credit'),
                createdate = gallery.list[index].getAttribute('data-createdate'),
                peak_slug = gallery.list[index].getAttribute('data-peak-slug'),
                summit_log_id = gallery.list[index].getAttribute('data-summit-log-id'),
                photo_url = gallery.list[index].getAttribute('data-photo-url'),
                caption_node = gallery.container.find('.description-text-caption');
                username_node = gallery.container.find('.description-text-user');
            caption_node.empty();
            username_node.empty();
            if (caption) {
                caption_node[0].appendChild(document.createTextNode(caption));
            }
            if (username) {
                var newdiv = document.createElement('div');
                if (credit) {
                    newdiv.innerHTML = '<a style="color: #fff;" target="_blank" href="' + photo_url + '/"><i style="margin-right: 5px;" class="fa fa-camera" aria-hidden="true"></i> ' + username + ' ' + credit + '</a>&nbsp;&bull;&nbsp;<time class="timeago" datetime="' + createdate + 'T00:00:00">' + createdate + '</time>';
                } else {
                    if (summit_log_id != '0') {
                        newdiv.innerHTML = '<a style="color: #fff;" href="/members/' + username + '/"><i style="margin-right: 5px;" class="fa fa-camera" aria-hidden="true"></i> ' + username + '</a>&nbsp;&bull;&nbsp;<a style="color: #fff;" href="/' + peak_slug + '/summits/' + summit_log_id + '/"><time class="timeago" datetime="' + createdate + 'T00:00:00">' + createdate + '</time>';
                    } else {
                        newdiv.innerHTML = '<a style="color: #fff;" href="/members/' + username + '/"><i style="margin-right: 5px;" class="fa fa-camera" aria-hidden="true"></i> ' + username + '</a>&nbsp;&bull;&nbsp;<time class="timeago" datetime="' + createdate + 'T00:00:00">' + createdate + '</time>';
                    }
                }
                username_node[0].appendChild(newdiv);
            }
            $("time.timeago").timeago();
        });

        $("#blueimp-gallery").on('slide', function (event, index, slide) {
            var gallery = $('#blueimp-gallery').data('gallery');
            //gallery.options['enableKeyboardNavigation'] = false;
            var index = gallery.index;
            var caption = gallery.list[index].getAttribute('data-description'),
                username = gallery.list[index].getAttribute('data-user'),
                credit = gallery.list[index].getAttribute('data-credit'),
                createdate = gallery.list[index].getAttribute('data-createdate'),
                peak_slug = gallery.list[index].getAttribute('data-peak-slug'),
                summit_log_id = gallery.list[index].getAttribute('data-summit-log-id'),
                photo_url = gallery.list[index].getAttribute('data-photo-url'),
                caption_node = gallery.container.find('.description-text-caption');
                username_node = gallery.container.find('.description-text-user');
            caption_node.empty();
            username_node.empty();
            if (caption) {
                caption_node[0].appendChild(document.createTextNode(caption));
            }
            if (username) {
                var newdiv = document.createElement('div');
                if (credit) {
                    newdiv.innerHTML = '<a style="color: #fff;" target="_blank" href="' + photo_url + '/"><i style="margin-right: 5px;" class="fa fa-camera" aria-hidden="true"></i> ' + username + ' ' + credit + '</a>&nbsp;&bull;&nbsp;<time class="timeago" datetime="' + createdate + 'T00:00:00">' + createdate + '</time>';
                } else {
                    if (summit_log_id != '0') {
                        newdiv.innerHTML = '<a style="color: #fff;" href="/members/' + username + '/"><i style="margin-right: 5px;" class="fa fa-camera" aria-hidden="true"></i> ' + username + '</a>&nbsp;&bull;&nbsp;<a style="color: #fff;" href="/' + peak_slug + '/summits/' + summit_log_id + '/"><time class="timeago" datetime="' + createdate + 'T00:00:00">' + createdate + '</time>';
                    } else {
                        newdiv.innerHTML = '<a style="color: #fff;" href="/members/' + username + '/"><i style="margin-right: 5px;" class="fa fa-camera" aria-hidden="true"></i> ' + username + '</a>&nbsp;&bull;&nbsp;<time class="timeago" datetime="' + createdate + 'T00:00:00">' + createdate + '</time>';
                    }
                }
                username_node[0].appendChild(newdiv);
            }
            $("time.timeago").timeago();

        });

        $("#map-photos-toggle").change(function(){
            if ($(this).is(':checked')) {
                $('.gallery-link').show();
                createCookie('map_photos',true,365);
            } else {
                $('.gallery-link').hide();
                createCookie('map_photos',false,365);
            }
        });

        $("#map-classics-toggle").change(function(){
            if ($(this).is(':checked')) {
                $('.mapboxgl-marker[data-classic=true]').addClass('marker_icon_classic');
                createCookie('map_classics',true,365);
            } else {
                $('.mapboxgl-marker[data-classic=true]').removeClass('marker_icon_classic');
                createCookie('map_classics',false,365);
            }
        });

        $("#map-challenges-toggle").change(function(){
            if ($(this).is(':checked')) {
                $('.marker-challenge-icon').show();
                createCookie('map_challenges',true,365);
            } else {
                $('.marker-challenge-icon').hide();
                createCookie('map_challenges',false,365);
            }
        });

        $("#map-stewards-toggle").change(function(){
            if ($(this).is(':checked')) {
                $('.mapboxgl-marker[data-steward=true]').addClass('marker_icon_steward');
                createCookie('map_stewards',true,365);
            } else {
                $('.mapboxgl-marker[data-steward=true]').removeClass('marker_icon_steward');
                createCookie('map_stewards',false,365);
            }
        });

        $("#map-kom-toggle").change(function(){
            if ($(this).is(':checked')) {
                $('.mapboxgl-marker[data-kom=true]').addClass('marker_icon_kom');
                createCookie('map_kom',true,365);
            } else {
                $('.mapboxgl-marker[data-kom=true]').removeClass('marker_icon_kom');
                createCookie('map_kom',false,365);
            }
        });

        $("#map-firstascents-toggle").change(function(){
            if ($(this).is(':checked')) {
                $('.mapboxgl-marker[data-firstascent=true]').addClass('marker_icon_firstascent');
                createCookie('map_firstascents',true,365);
            } else {
                $('.mapboxgl-marker[data-firstascent=true]').removeClass('marker_icon_firstascent');
                createCookie('map_firstascents',false,365);
            }
        });

        $("#map-unclimbed-toggle").change(function(){
            if ($(this).is(':checked')) {
                $('.marker_icon').show();
                createCookie('map_unclimbed',true,365);
            } else {
                $('.marker_icon').hide();
                createCookie('map_unclimbed',false,365);
            }
        });

        $("#map-attempts-toggle").change(function(){
            if ($(this).is(':checked')) {
                $('.marker_icon_yellow').show();
                createCookie('map_attempts',true,365);
            } else {
                $('.marker_icon_yellow').hide();
                createCookie('map_attempts',false,365);
            }
        });

        $("#map-summits-toggle").change(function(){
            if ($(this).is(':checked')) {
                $('.marker_icon_green').show();
                createCookie('map_summits',true,365);
            } else {
                $('.marker_icon_green').hide();
                createCookie('map_summits',false,365);
            }
        });

        var vars = [], hash, keyword, near, wanted_elev_min, wanted_elev_max, wanted_prom_min, wanted_prom_max, wanted_summits_min, wanted_summits_max, wanted_difficulty_min, wanted_difficulty_max, wanted_length_min, wanted_length_max, wanted_vertical_min, wanted_vertical_max, wanted_last_climbed_min, wanted_last_climbed_max, classics, in_challenge, you_climbed, lat, lng;
        var q = document.URL.split('#')[1];
        if(q != undefined){
            q = q.split('&');
            for(var i = 0; i < q.length; i++){
                hash = q[i].split('=');
                vars.push(hash[1]);
                vars[hash[0]] = hash[1];
            }
        }

        if (vars['q'] != undefined) {
            keyword = vars['q'];
        } else {
            keyword = '';
        }

        if (vars['n'] != undefined) {
            near = vars['n'];
        } else {
            near = '';
        }

        if (vars['elev_min'] != undefined) {
            wanted_elev_min = vars['elev_min'];
        } else {
            wanted_elev_min = '0';
        }

        if (vars['elev_max'] != undefined) {
            wanted_elev_max = vars['elev_max'];
        } else {
            //if elevation in meters
            if ($('#bt_showinmeters').text() == '[ft]') {
                wanted_elev_max = '29527';
            } else {
                wanted_elev_max = '29500';
            }
        }

        if (vars['prom_min'] != undefined) {
            wanted_prom_min = vars['prom_min'];
        } else {
            wanted_prom_min = '0';
        }

        if (vars['prom_max'] != undefined) {
            wanted_prom_max = vars['prom_max'];
        } else {
            //if prominence in meters
            if ($('#prominence_bt_showinmeters').text() == '[ft]') {
                wanted_prom_max = '29527';
            } else {
                wanted_prom_max = '29500';
            }
        }

        if (vars['summits_min'] != undefined) {
            wanted_summits_min = vars['summits_min'];
        } else {
            wanted_summits_min = '0';
        }

        if (vars['summits_max'] != undefined) {
            wanted_summits_max = vars['summits_max'];
        } else {
            wanted_summits_max = '500';
        }

        if (vars['difficulty_min'] != undefined) {
            wanted_difficulty_min = vars['difficulty_min'];
        } else {
            wanted_difficulty_min = '1';
        }

        if (vars['difficulty_max'] != undefined) {
            wanted_difficulty_max = vars['difficulty_max'];
        } else {
            wanted_difficulty_max = '5';
        }

        if (vars['length_min'] != undefined) {
            wanted_length_min = vars['length_min'];
        } else {
            wanted_length_min = '0';
        }

        if (vars['length_max'] != undefined) {
            wanted_length_max = vars['length_max'];
        } else {
            wanted_length_max = '20';
        }

        if (vars['vertical_min'] != undefined) {
            wanted_vertical_min = vars['vertical_min'];
        } else {
            wanted_vertical_min = '0';
        }

        if (vars['vertical_max'] != undefined) {
            wanted_vertical_max = vars['vertical_max'];
        } else {
            wanted_vertical_max = '10000';
        }

        if (vars['last_climbed_min'] != undefined) {
            wanted_last_climbed_min = vars['last_climbed_min'];
        } else {
            wanted_last_climbed_min = '0';
        }

        if (vars['last_climbed_max'] != undefined) {
            wanted_last_climbed_max = vars['last_climbed_max'];
        } else {
            wanted_last_climbed_max = '11';
        }

        if (vars['classics'] != undefined) {
            classics = vars['classics'];
        } else {
            classics = 'false';
        }

        if (vars['in_challenge'] != undefined) {
            in_challenge = vars['in_challenge'];
        } else {
            in_challenge = 'false';
        }

        if (vars['you_climbed'] != undefined) {
            you_climbed = vars['you_climbed'];
        } else {
            you_climbed = undefined;
        }

        if (vars['lat'] != undefined) {
            lat = vars['lat'];
        } else {
            lat = '';
        }

        if (vars['lng'] != undefined) {
            lng = vars['lng'];
        } else {
            lng = '';
        }

        var window_width = $(window).width();
        var height_pad = 120;
        var width_pad = 480;
        if (window_width < 1024) {
            height_pad = 170;
            width_pad = 0;
        }
        if (window_width < 768) {
            height_pad = 149;
            width_pad = 0;
        }
        var max_height = $(window).height()- height_pad;
        var max_width = $(window).width()- width_pad;
        $("div#map-canvas").height(max_height);
        $("div#map-canvas").width(max_width);
        $("#peak-search").height($(window).height());
        $("#explore .leftCol").height($("#explore .rightCol").height());
        $("div.peak_list_cont").css("margin-bottom","0");
        initialize();
        init = true;

        //switch map units
        $("#gm-custom-mapunits").click(function(){
            //toggle map units
            if ($('#gm-custom-mapunitsbutton-label-meters').hasClass('gm-custom-mapunits-selected')) {
                toggleMapUnits('feet');
                setSliderUnits('feet');
            } else {
                toggleMapUnits('meters');
                setSliderUnits('meters');
            }
            loadPeaksFromHash();
        });

        $("div#highest-peak").click(function(){
            url = $(this).attr("title");
            if (url!=""){
                location = url;
            }
        });

        $('#map-canvas').mousemove(function(e) {
            var offset = $(this).offset();
            pageX = e.pageX;
            pageY = e.pageY;
            mapX = (e.pageX - offset.left);
            mapY = (e.pageY - offset.top);
        });

        $('#map-canvas').on('touchstart', function(e) {
            var offset = $(this).offset();
            pageX = e.originalEvent.touches[0].pageX;
            pageY = e.originalEvent.touches[0].pageY;
            mapX = (pageX - offset.left);
            mapY = (pageY - offset.top);
        });

        $(window).resize(function() {
            var window_width = $(window).width();
            if (window_width < 1024) {
                $('#peaks-map').css({left: '0px'})
            } else {
                if ($('#peak-search').css('left') == '0px') {
                    $('#peaks-map').css({left: '240px'})
                } else {
                    $('#peaks-map').css({left: '0px'})
                }
            }
            var height_pad = 120;
            var width_pad = 480;
            if ($('#peak-search').css('left') != '0px') {
                width_pad = 0;
            }
            if (window_width < 1024) {
                height_pad = 170;
                width_pad = 0;
            }
            if (window_width < 768) {
                height_pad = 149;
                width_pad = 0;
            }
            var max_height = $(window).height()- height_pad;
            var max_width = $(window).width()- width_pad;
            $("div#map-canvas").height(max_height);
            $("div#map-canvas").width(max_width);
            $("#peak-search").height($(window).height());
            map.resize();
        });

        //Set up the sliders
        var elevationSlider = document.getElementById('slider-range');

        noUiSlider.create(elevationSlider, {
            start: [ 0, 29500 ],
            snap: true,
            connect: true,
            start: [wanted_elev_min, wanted_elev_max],
            range: {
                'min': 0,
                '3.3%': 500,
                '5.0%': 1000,
                '6.7%': 1500,
                '8.3%': 2000,
                '10.0%': 2500,
                '11.7%': 3000,
                '13.3%': 3500,
                '15.0%': 4000,
                '16.7%': 4500,
                '18.3%': 5000,
                '20.0%': 5500,
                '21.7%': 6000,
                '23.3%': 6500,
                '25.0%': 7000,
                '26.7%': 7500,
                '28.3%': 8000,
                '30.0%': 8500,
                '31.7%': 9000,
                '33.3%': 9500,
                '35.0%': 10000,
                '36.7%': 10500,
                '38.3%': 11000,
                '40.0%': 11500,
                '41.7%': 12000,
                '43.3%': 12500,
                '45.0%': 13000,
                '46.7%': 13500,
                '48.3%': 14000,
                '50.0%': 14500,
                '51.7%': 15000,
                '53.3%': 15500,
                '55.0%': 16000,
                '56.7%': 16500,
                '58.3%': 17000,
                '60.0%': 17500,
                '61.7%': 18000,
                '63.3%': 18500,
                '65.0%': 19000,
                '66.7%': 19500,
                '68.3%': 20000,
                '70.0%': 20500,
                '71.7%': 21000,
                '73.3%': 21500,
                '75.0%': 22000,
                '76.7%': 22500,
                '78.3%': 23000,
                '80.0%': 23500,
                '81.7%': 24000,
                '83.3%': 24500,
                '85.0%': 25000,
                '86.7%': 25500,
                '88.3%': 26000,
                '90.0%': 26500,
                '91.7%': 27000,
                '93.3%': 27500,
                '95.0%': 28000,
                '96.7%': 28500,
                '98.3%': 29000,
                'max': 29500
            }
        });

        elevationSlider.noUiSlider.on('slide', function(values, handle, unencoded, tap, positions){
            //if meters
            if ($('#amount-meters').is(':visible')) {
                if (unencoded[0] == unencoded[1]) {
                    $("#amount").val(numberWithCommas(unencoded[1]) + " ft");
                    $("#amount-meters").val(numberWithCommas(Math.floor(unencoded[1])) + " m");
                } else {
                    $("#amount").val(numberWithCommas(unencoded[0]) + "  to  " + numberWithCommas(unencoded[1]) + " ft");
                    $("#amount-meters").val(numberWithCommas(Math.floor(unencoded[0])) + "  to  " + numberWithCommas(Math.floor(unencoded[1])) + " m");
                }
                if (0 != unencoded[0] || 9000 != unencoded[1]) {
                    $('#elevation-label').css('color', '#f24100');
                    $('.ammount').css('color', '#f24100');
                    $('.ammount').css('-webkit-text-fill-color', '#f24100');
                    $('#slider-range > div > div').css('background-color', '#f24100');
                    $('#slider-range .noUi-background').css('background-color', '#CCC');
                    $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                } else {
                    $('#elevation-label').css('color', '#aaa');
                    $('.ammount').css('color', '#aaa');
                    $('.ammount').css('-webkit-text-fill-color', '#aaa');
                    $('#slider-range > div > div').css('background-color', '#ccc');
                    $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
                }
            } else {
                if (unencoded[0] == 29500) {
                    $("#amount").val(numberWithCommas(unencoded[1]) + " ft");
                    $("#amount-meters").val(numberWithCommas(Math.floor(unencoded[1])) + " m");
                } else {
                    $("#amount").val(numberWithCommas(unencoded[0]) + "  to  " + numberWithCommas(unencoded[1]) + " ft");
                    $("#amount-meters").val(numberWithCommas(Math.floor(unencoded[0])) + "  to  " + numberWithCommas(Math.floor(unencoded[1])) + " m");
                }
                if (0 != unencoded[0] || 29500 != unencoded[1]) {
                    $('#elevation-label').css('color', '#f24100');
                    $('.ammount').css('color', '#f24100');
                    $('.ammount').css('-webkit-text-fill-color', '#f24100');
                    $('#slider-range > div > div').css('background-color', '#f24100');
                    $('#slider-range .noUi-background').css('background-color', '#CCC');
                    $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                } else {
                    $('#elevation-label').css('color', '#aaa');
                    $('.ammount').css('color', '#aaa');
                    $('.ammount').css('-webkit-text-fill-color', '#aaa');
                    $('#slider-range > div > div').css('background-color', '#ccc');
                    $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
                }
            }
        });
        elevationSlider.noUiSlider.on('end', function(values, handle, unencoded, tap, positions){
            //if meters
            if ($('#bt_showinmeters').text() == '[ft]') {
                delayShowDataelevation("#amount", [unencoded[0]/0.3048,unencoded[1]/0.3048]);
            } else {
                delayShowDataelevation("#amount", unencoded);
            }
        });
        var elevationValues = elevationSlider.noUiSlider.get();
        if ($('#amount-meters').is(':visible')) {
            if (elevationValues[0] == elevationValues[1]) {
                $("#amount").val(numberWithCommas(parseInt(elevationValues[1])) + " ft");
                $("#amount-meters").val(numberWithCommas(parseInt(Math.floor(elevationValues[1]))) + " m");
            } else {
                $("#amount").val(numberWithCommas(parseInt(elevationValues[0])) + "  to  " + numberWithCommas(parseInt(elevationValues[1])) + " ft");
                $("#amount-meters").val(numberWithCommas(Math.floor(parseInt(elevationValues[0]))) + "  to  " + numberWithCommas(parseInt(Math.floor(elevationValues[1]))) + " m");
            }
        } else {
            if (elevationValues[0] == elevationValues[1]) {
                $("#amount").val(numberWithCommas(parseInt(elevationValues[1])) + " ft");
                $("#amount-meters").val(numberWithCommas(parseInt(Math.floor(elevationValues[1]))) + " m");
            } else {
                $("#amount").val(numberWithCommas(parseInt(elevationValues[0])) + "  to  " + numberWithCommas(parseInt(elevationValues[1])) + " ft");
                $("#amount-meters").val(numberWithCommas(Math.floor(parseInt(elevationValues[0]))) + "  to  " + numberWithCommas(parseInt(Math.floor(elevationValues[1]))) + " m");
            }
        }


        var prominenceSlider = document.getElementById('prominence-slider-range');

        noUiSlider.create(prominenceSlider, {
            start: [ 0, 29500 ],
            snap: true,
            connect: true,
            start: [wanted_prom_min, wanted_prom_max],
            range: {
                'min': 0,
                '3.1%': 50,
                '4.7%': 100,
                '6.3%': 200,
                '7.8%': 300,
                '9.4%': 500,
                '10.9%': 1000,
                '12.5%': 1500,
                '14.1%': 2000,
                '15.6%': 2500,
                '17.2%': 3000,
                '18.8%': 3500,
                '20.3%': 4000,
                '21.9%': 4500,
                '23.4%': 5000,
                '25.0%': 5500,
                '26.6%': 6000,
                '28.1%': 6500,
                '29.7%': 7000,
                '31.3%': 7500,
                '32.8%': 8000,
                '34.4%': 8500,
                '35.9%': 9000,
                '37.5%': 9500,
                '39.1%': 10000,
                '40.6%': 10500,
                '42.2%': 11000,
                '43.8%': 11500,
                '45.3%': 12000,
                '46.9%': 12500,
                '48.4%': 13000,
                '50.0%': 13500,
                '51.6%': 14000,
                '53.1%': 14500,
                '54.7%': 15000,
                '56.3%': 15500,
                '57.8%': 16000,
                '59.4%': 16500,
                '60.9%': 17000,
                '62.5%': 17500,
                '64.1%': 18000,
                '65.6%': 18500,
                '67.2%': 19000,
                '68.8%': 19500,
                '70.3%': 20000,
                '71.9%': 20500,
                '73.4%': 21000,
                '75.0%': 21500,
                '76.6%': 22000,
                '78.1%': 22500,
                '79.7%': 23000,
                '81.3%': 23500,
                '82.8%': 24000,
                '84.4%': 24500,
                '85.9%': 25000,
                '87.5%': 25500,
                '89.1%': 26000,
                '90.6%': 26500,
                '92.2%': 27000,
                '93.8%': 27500,
                '95.3%': 28000,
                '96.9%': 28500,
                '98.4%': 29000,
                'max': 29500
            }
        });

        prominenceSlider.noUiSlider.on('slide', function(values, handle, unencoded, tap, positions){
            //if meters
            if ($('#prominence-amount-meters').is(':visible')) {
                if (unencoded[0] == unencoded[1]) {
                    $( "#prominence-amount" ).val(numberWithCommas(unencoded[1]) + " ft");
                    $( "#prominence-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[1])) + " m");
                } else {
                    $( "#prominence-amount" ).val( numberWithCommas(unencoded[0]) + "  to  " + numberWithCommas(unencoded[1]) + " ft");
                    $( "#prominence-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[0])) + "  to  " + numberWithCommas(Math.floor(unencoded[1])) + " m");
                }
                if (0 != unencoded[0] || 9000 != unencoded[1]) {
                    $('#prominence-label').css('color', '#f24100');
                    $('.prominence-ammount').css('color', '#f24100');
                    $('.prominence-ammount').css('-webkit-text-fill-color', '#f24100');
                    $('#prominence-slider-range > div > div').css('background-color', '#f24100');
                    $('#prominence-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                } else {
                    $('#prominence-label').css('color', '#aaa');
                    $('.prominence-ammount').css('color', '#aaa');
                    $('.prominence-ammount').css('-webkit-text-fill-color', '#aaa');
                    $('#prominence-slider-range > div > div').css('background-color', '#ccc');
                    $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
                }
            } else {
                if (unencoded[0] == unencoded[1]) {
                    $( "#prominence-amount" ).val(numberWithCommas(unencoded[1]) + " ft");
                    $( "#prominence-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[1])) + " m");
                } else {
                    $( "#prominence-amount" ).val( numberWithCommas(unencoded[0]) + "  to  " + numberWithCommas(unencoded[1]) + " ft");
                    $( "#prominence-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[0])) + "  to  " + numberWithCommas(Math.floor(unencoded[1])) + " m");
                }
                if (0 != unencoded[0] || 29500 != unencoded[1]) {
                    $('#prominence-label').css('color', '#f24100');
                    $('.prominence-ammount').css('color', '#f24100');
                    $('.prominence-ammount').css('-webkit-text-fill-color', '#f24100');
                    $('#prominence-slider-range > div > div').css('background-color', '#f24100');
                    $('#prominence-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                } else {
                    $('#prominence-label').css('color', '#aaa');
                    $('.prominence-ammount').css('color', '#aaa');
                    $('.prominence-ammount').css('-webkit-text-fill-color', '#aaa');
                    $('#prominence-slider-range > div > div').css('background-color', '#ccc');
                    $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
                }
            }
        });
        prominenceSlider.noUiSlider.on('end', function(values, handle, unencoded, tap, positions){
            //if meters
            if ($('#prominence_bt_showinmeters').text() == '[ft]') {
                delayShowDataprominence("#prominence-amount", [unencoded[0]/0.3048,unencoded[1]/0.3048]);
            } else {
                delayShowDataprominence("#prominence-amount", unencoded);
            }
        });
        var prominenceValues = prominenceSlider.noUiSlider.get();
        if ($('#prominence-amount-meters').is(':visible')) {
            if (prominenceValues[0] == prominenceValues[1]) {
                $( "#prominence-amount" ).val(numberWithCommas(parseInt(prominenceValues[1])) + " ft");
                $( "#prominence-amount-meters" ).val(numberWithCommas(parseInt(Math.floor(prominenceValues[1]))) + " m");
            } else {
                $( "#prominence-amount" ).val( numberWithCommas(parseInt(prominenceValues[0])) + "  to  " + numberWithCommas(parseInt(prominenceValues[1])) + " ft");
                $( "#prominence-amount-meters" ).val(numberWithCommas(Math.floor(parseInt(prominenceValues[0]))) + "  to  " + numberWithCommas(parseInt(Math.floor(prominenceValues[1]))) + " m");
            }
        } else {
            if (prominenceValues[0] == prominenceValues[1]) {
                $( "#prominence-amount" ).val(numberWithCommas(parseInt(prominenceValues[1])) + " ft");
                $( "#prominence-amount-meters" ).val(numberWithCommas(parseInt(Math.floor(prominenceValues[1]))) + " m");
            } else {
                $( "#prominence-amount" ).val( numberWithCommas(parseInt(prominenceValues[0])) + "  to  " + numberWithCommas(parseInt(prominenceValues[1])) + " ft");
                $( "#prominence-amount-meters" ).val(numberWithCommas(Math.floor(parseInt(prominenceValues[0]))) + "  to  " + numberWithCommas(parseInt(Math.floor(prominenceValues[1]))) + " m");
            }
        }


        var summitsSlider = document.getElementById('summits-slider-range');

        noUiSlider.create(summitsSlider, {
            start: [ 0, 500 ],
            snap: true,
            connect: true,
            start: [wanted_summits_min, wanted_summits_max],
            range: {
                'min': 0,
                '12.5%': 1,
                '18.8%': 2,
                '25.0%': 3,
                '31.3%': 4,
                '37.5%': 5,
                '43.8%': 10,
                '50.0%': 20,
                '56.3%': 30,
                '62.5%': 40,
                '68.8%': 50,
                '75.0%': 100,
                '81.3%': 200,
                '87.5%': 300,
                '93.8%': 400,
                'max': 500
            }
        });

        summitsSlider.noUiSlider.on('slide', function(values, handle, unencoded, tap, positions){
            if (unencoded[0] == 500) {
                $( "#summits-amount" ).val( unencoded[1] + "+ climbs" );
            } else if (unencoded[0] == unencoded[1]) {
                $( "#summits-amount" ).val( unencoded[1] + " climbs" );
            } else {
                if (unencoded[1] == 500) {
                    $("#summits-amount").val(unencoded[0] + "  to  " + unencoded[1] + "+ climbs");
                } else {
                    $("#summits-amount").val(unencoded[0] + "  to  " + unencoded[1] + " climbs");
                }
            }
            if (0 != unencoded[0] || 500 != unencoded[1]) {
                $('#summits-label').css('color','#f24100');
                $('.summits-ammount').css('color', '#f24100');
                $('.summits-ammount').css('-webkit-text-fill-color', '#f24100');
                $('#summits-slider-range > div > div').css('background-color','#f24100');
                $('#summits-slider-range .noUi-background').css('background-color', '#CCC');
                $('#summits-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
            } else {
                $('#summits-label').css('color','#aaa');
                $('.summits-ammount').css('color', '#aaa');
                $('.summits-ammount').css('-webkit-text-fill-color', '#aaa');
                $('#summits-slider-range > div > div').css('background-color','#ccc');
                $('#summits-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
            }
        });
        summitsSlider.noUiSlider.on('end', function(values, handle, unencoded, tap, positions){
            delayShowDataSummits("#summits-amount", unencoded);
        });
        var summitsValues = summitsSlider.noUiSlider.get();
        if (summitsValues[0] == 500) {
            $( "#summits-amount" ).val( parseInt(summitsValues[1]) + "+ climbs");
        } else if (summitsValues[0] == summitsValues[1]) {
            $( "#summits-amount" ).val( parseInt(summitsValues[1]) + " climbs");
        } else {
            if (summitsValues[1] == 500) {
                $( "#summits-amount" ).val( parseInt(summitsValues[0]) + "  to  " + parseInt(summitsValues[1]) + "+ climbs");
            } else {
                $( "#summits-amount" ).val( parseInt(summitsValues[0]) + "  to  " + parseInt(summitsValues[1]) + " climbs");
            }
        }


        var difficultySlider = document.getElementById('difficulty-slider-range');

        noUiSlider.create(difficultySlider, {
            start: [ 1, 5 ],
            snap: true,
            connect: true,
            start: [wanted_difficulty_min, wanted_difficulty_max],
            range: {
                'min': 1,
                '40.0%': 2,
                '60.0%': 3,
                '80.0%': 4,
                'max': 5
            }
        });

        difficultySlider.noUiSlider.on('slide', function(values, handle, unencoded, tap, positions){
            if (unencoded[0] == unencoded[1]) {
                $("#difficulty-amount").val("Class " + unencoded[0]);
            } else {
                $("#difficulty-amount").val("Class " + unencoded[0] + "  to  " + unencoded[1]);
            }
            if (1 != unencoded[0] || 5 != unencoded[1]) {
                $('#difficulty-label').css('color','#f24100');
                $('.difficulty-ammount').css('color', '#f24100');
                $('.difficulty-ammount').css('-webkit-text-fill-color', '#f24100');
                $('#difficulty-slider-range > div > div').css('background-color','#f24100');
                $('#difficulty-slider-range .noUi-background').css('background-color', '#CCC');
                $('#difficulty-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
            } else {
                $('#difficulty-label').css('color','#aaa');
                $('.difficulty-ammount').css('color', '#aaa');
                $('.difficulty-ammount').css('-webkit-text-fill-color', '#aaa');
                $('#difficulty-slider-range > div > div').css('background-color','#ccc');
                $('#difficulty-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
            }
        });
        difficultySlider.noUiSlider.on('end', function(values, handle, unencoded, tap, positions){
            delayShowDataDifficulty("#difficulty-amount", unencoded);
        });
        var difficultyValues = difficultySlider.noUiSlider.get();
        if (difficultyValues[0] == difficultyValues[1]) {
            $("#difficulty-amount").val("Class " + parseInt(difficultyValues[0]));
        } else  {
            $("#difficulty-amount").val("Class " + parseInt(difficultyValues[0]) + "  to  " + parseInt(difficultyValues[1]));
        }




        var lengthSlider = document.getElementById('length-slider-range');

        noUiSlider.create(lengthSlider, {
            start: [ 0, 20 ],
            snap: true,
            connect: true,
            start: [wanted_length_min, wanted_length_max],
            range: {
                'min': 0,
                '5.0%': 1,
                '10.0%': 2,
                '15.0%': 3,
                '20.0%': 4,
                '25.0%': 5,
                '30.0%': 6,
                '35.0%': 7,
                '40.0%': 8,
                '45.0%': 9,
                '50.0%': 10,
                '55.0%': 11,
                '60.0%': 12,
                '65.0%': 13,
                '70.0%': 14,
                '75.0%': 15,
                '80.0%': 16,
                '85.0%': 17,
                '90.0%': 18,
                '95.0%': 19,
                'max': 20
            }
        });

        lengthSlider.noUiSlider.on('slide', function(values, handle, unencoded, tap, positions){
            //if meters
            if ($('#length-amount-meters').is(':visible')) {
                if (unencoded[0] == 32) {
                    $( "#length-amount" ).val(numberWithCommas(unencoded[1]) + "+ mi");
                    $( "#length-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[1])) + "+ km");
                } else if (unencoded[0] == unencoded[1]) {
                    $( "#length-amount" ).val(numberWithCommas(unencoded[1]) + " mi");
                    $( "#length-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[1])) + " km");
                } else {
                    if (unencoded[1] == 32) {
                        $( "#length-amount" ).val( numberWithCommas(unencoded[0]) + "  to  " + numberWithCommas(unencoded[1]) + "+ mi");
                        $( "#length-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[0])) + "  to  " + numberWithCommas(Math.floor(unencoded[1])) + "+ km");
                    } else {
                        $( "#length-amount" ).val( numberWithCommas(unencoded[0]) + "  to  " + numberWithCommas(unencoded[1]) + " mi");
                        $( "#length-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[0])) + "  to  " + numberWithCommas(Math.floor(unencoded[1])) + " km");
                    }
                }
                if (0 != unencoded[0] || 32 != unencoded[1]) {
                    $('#length-label').css('color', '#f24100');
                    $('.length-ammount').css('color', '#f24100');
                    $('.length-ammount').css('-webkit-text-fill-color', '#f24100');
                    $('#length-slider-range > div > div').css('background-color', '#f24100');
                    $('#length-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#length-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                } else {
                    $('#length-label').css('color', '#aaa');
                    $('.length-ammount').css('color', '#aaa');
                    $('.length-ammount').css('-webkit-text-fill-color', '#aaa');
                    $('#length-slider-range > div > div').css('background-color', '#ccc');
                    $('#length-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
                }
            } else {
                if (unencoded[0] == 20) {
                    $( "#length-amount" ).val(numberWithCommas(unencoded[1]) + "+ mi");
                    $( "#length-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[1])) + "+ km");
                } else if (unencoded[0] == unencoded[1]) {
                    $( "#length-amount" ).val(numberWithCommas(unencoded[1]) + " mi");
                    $( "#length-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[1])) + " km");
                } else {
                    if (unencoded[1] == 20) {
                        $( "#length-amount" ).val( numberWithCommas(unencoded[0]) + "  to  " + numberWithCommas(unencoded[1]) + "+ mi");
                        $( "#length-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[0])) + "  to  " + numberWithCommas(Math.floor(unencoded[1])) + "+ km");
                    } else {
                        $( "#length-amount" ).val( numberWithCommas(unencoded[0]) + "  to  " + numberWithCommas(unencoded[1]) + " mi");
                        $( "#length-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[0])) + "  to  " + numberWithCommas(Math.floor(unencoded[1])) + " km");
                    }
                }
                if (0 != unencoded[0] || 20 != unencoded[1]) {
                    $('#length-label').css('color', '#f24100');
                    $('.length-ammount').css('color', '#f24100');
                    $('.length-ammount').css('-webkit-text-fill-color', '#f24100');
                    $('#length-slider-range > div > div').css('background-color', '#f24100');
                    $('#length-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#length-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                } else {
                    $('#length-label').css('color', '#aaa');
                    $('.length-ammount').css('color', '#aaa');
                    $('.length-ammount').css('-webkit-text-fill-color', '#aaa');
                    $('#length-slider-range > div > div').css('background-color', '#ccc');
                    $('#length-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
                }
            }
        });
        lengthSlider.noUiSlider.on('end', function(values, handle, unencoded, tap, positions){
            //if meters
            if ($('#length_bt_showinmeters').text() == '[ft]') {
                delayShowDataLength("#length-amount", [unencoded[0]/1.6,unencoded[1]/1.6]);
            } else {
                delayShowDataLength("#length-amount", unencoded);
            }
        });
        var lengthValues = lengthSlider.noUiSlider.get();
        //if meters
        if ($('#length-amount-meters').is(':visible')) {
            if (lengthValues[0] == '32') {
                $( "#length-amount" ).val(numberWithCommas(parseInt(lengthValues[1])) + "+ mi");
                $( "#length-amount-meters" ).val(numberWithCommas(parseInt(Math.floor(lengthValues[1]))) + "+ km");
            } else if (lengthValues[0] == lengthValues[1]) {
                $( "#length-amount" ).val(numberWithCommas(parseInt(lengthValues[1])) + " mi");
                $( "#length-amount-meters" ).val(numberWithCommas(parseInt(Math.floor(lengthValues[1]))) + " km");
            } else {
                if (lengthValues[1] == '32') {
                    $( "#length-amount" ).val( numberWithCommas(parseInt(lengthValues[0])) + "  to  " + numberWithCommas(parseInt(lengthValues[1])) + "+ mi");
                    $( "#length-amount-meters" ).val(numberWithCommas(Math.floor(parseInt(lengthValues[0]))) + "  to  " + numberWithCommas(parseInt(Math.floor(lengthValues[1]))) + "+ km");
                } else {
                    $( "#length-amount" ).val( numberWithCommas(parseInt(lengthValues[0])) + "  to  " + numberWithCommas(parseInt(lengthValues[1])) + " mi");
                    $( "#length-amount-meters" ).val(numberWithCommas(Math.floor(parseInt(lengthValues[0]))) + "  to  " + numberWithCommas(parseInt(Math.floor(lengthValues[1]))) + " km");
                }
            }
        } else {
            if (lengthValues[0] == '20') {
                $( "#length-amount" ).val(numberWithCommas(parseInt(lengthValues[1])) + "+ mi");
                $( "#length-amount-meters" ).val(numberWithCommas(parseInt(Math.floor(lengthValues[1]))) + "+ km");
            } else if (lengthValues[0] == lengthValues[1]) {
                $( "#length-amount" ).val(numberWithCommas(parseInt(lengthValues[1])) + " mi");
                $( "#length-amount-meters" ).val(numberWithCommas(parseInt(Math.floor(lengthValues[1]))) + " km");
            } else {
                if (lengthValues[1] == '20') {
                    $( "#length-amount" ).val( numberWithCommas(parseInt(lengthValues[0])) + "  to  " + numberWithCommas(parseInt(lengthValues[1])) + "+ mi");
                    $( "#length-amount-meters" ).val(numberWithCommas(Math.floor(parseInt(lengthValues[0]))) + "  to  " + numberWithCommas(parseInt(Math.floor(lengthValues[1]))) + "+ km");
                } else {
                    $( "#length-amount" ).val( numberWithCommas(parseInt(lengthValues[0])) + "  to  " + numberWithCommas(parseInt(lengthValues[1])) + " mi");
                    $( "#length-amount-meters" ).val(numberWithCommas(Math.floor(parseInt(lengthValues[0]))) + "  to  " + numberWithCommas(parseInt(Math.floor(lengthValues[1]))) + " km");
                }
            }
        }


        var verticalSlider = document.getElementById('vertical-slider-range');

        noUiSlider.create(verticalSlider, {
            start: [ 0, 10000 ],
            snap: true,
            connect: true,
            start: [wanted_vertical_min, wanted_vertical_max],
            range: {
                'min': 0,
                '9.0%': 500,
                '18.0%': 1000,
                '27.0%': 2000,
                '36.0%': 3000,
                '45.0%': 4000,
                '54.0%': 5000,
                '63.0%': 6000,
                '72.0%': 7000,
                '81.0%': 8000,
                '90.0%': 9000,
                'max': 10000
            }
        });

        verticalSlider.noUiSlider.on('slide', function(values, handle, unencoded, tap, positions){
            //if meters
            if ($('#vertical-amount-meters').is(':visible')) {
                if (unencoded[0] == 3000) {
                    $( "#vertical-amount" ).val(numberWithCommas(unencoded[1]) + "+ ft");
                    $( "#vertical-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[1])) + "+ m");
                } else if (unencoded[0] == unencoded[1]) {
                    $( "#vertical-amount" ).val(numberWithCommas(unencoded[1]) + " ft");
                    $( "#vertical-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[1])) + " m");
                } else {
                    if (unencoded[1] == 3000) {
                        $( "#vertical-amount" ).val( numberWithCommas(unencoded[0]) + "  to  " + numberWithCommas(unencoded[1]) + "+ ft");
                        $( "#vertical-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[0])) + "  to  " + numberWithCommas(Math.floor(unencoded[1])) + "+ m");
                    } else {
                        $( "#vertical-amount" ).val( numberWithCommas(unencoded[0]) + "  to  " + numberWithCommas(unencoded[1]) + " ft");
                        $( "#vertical-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[0])) + "  to  " + numberWithCommas(Math.floor(unencoded[1])) + " m");
                    }
                }
                if (0 != unencoded[0] || 3000 != unencoded[1]) {
                    $('#vertical-label').css('color', '#f24100');
                    $('.vertical-ammount').css('color', '#f24100');
                    $('.vertical-ammount').css('-webkit-text-fill-color', '#f24100');
                    $('#vertical-slider-range > div > div').css('background-color', '#f24100');
                    $('#vertical-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#vertical-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                } else {
                    $('#vertical-label').css('color', '#aaa');
                    $('.vertical-ammount').css('color', '#aaa');
                    $('.vertical-ammount').css('-webkit-text-fill-color', '#aaa');
                    $('#vertical-slider-range > div > div').css('background-color', '#ccc');
                    $('#vertical-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
                }
            } else {
                if (unencoded[0] == 10000) {
                    $( "#vertical-amount" ).val(numberWithCommas(unencoded[1]) + "+ ft");
                    $( "#vertical-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[1])) + "+ m");
                } else if (unencoded[0] == unencoded[1]) {
                    $( "#vertical-amount" ).val(numberWithCommas(unencoded[1]) + " ft");
                    $( "#vertical-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[1])) + " m");
                } else {
                    if (unencoded[1] == 10000) {
                        $( "#vertical-amount" ).val( numberWithCommas(unencoded[0]) + "  to  " + numberWithCommas(unencoded[1]) + "+ ft");
                        $( "#vertical-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[0])) + "  to  " + numberWithCommas(Math.floor(unencoded[1])) + "+ m");
                    } else {
                        $( "#vertical-amount" ).val( numberWithCommas(unencoded[0]) + "  to  " + numberWithCommas(unencoded[1]) + " ft");
                        $( "#vertical-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[0])) + "  to  " + numberWithCommas(Math.floor(unencoded[1])) + " m");
                    }
                }
                if (0 != unencoded[0] || 10000 != unencoded[1]) {
                    $('#vertical-label').css('color', '#f24100');
                    $('.vertical-ammount').css('color', '#f24100');
                    $('.vertical-ammount').css('-webkit-text-fill-color', '#f24100');
                    $('#vertical-slider-range > div > div').css('background-color', '#f24100');
                    $('#vertical-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#vertical-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                } else {
                    $('#vertical-label').css('color', '#aaa');
                    $('.vertical-ammount').css('color', '#aaa');
                    $('.vertical-ammount').css('-webkit-text-fill-color', '#aaa');
                    $('#vertical-slider-range > div > div').css('background-color', '#ccc');
                    $('#vertical-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
                }
            }
        });
        verticalSlider.noUiSlider.on('end', function(values, handle, unencoded, tap, positions){
            //if meters
            if ($('#vertical_bt_showinmeters').text() == '[ft]') {
                delayShowDataVertical("#prominence-amount", [unencoded[0]/0.3048,unencoded[1]/0.3048]);
            } else {
                delayShowDataVertical("#prominence-amount", unencoded);
            }
        });
        var verticalValues = verticalSlider.noUiSlider.get();
        //if meters
        if ($('#vertical-amount-meters').is(':visible')) {
            if (verticalValues[0] == '3000') {
                $( "#vertical-amount" ).val(numberWithCommas(parseInt(verticalValues[1])) + "+ ft");
                $( "#vertical-amount-meters" ).val(numberWithCommas(parseInt(verticalValues[1])) + "+ m");
            } else if (verticalValues[0] == verticalValues[1]) {
                $( "#vertical-amount" ).val(numberWithCommas(parseInt(verticalValues[1])) + " ft");
                $( "#vertical-amount-meters" ).val(numberWithCommas(parseInt(verticalValues[1])) + " m");
            } else {
                if (verticalValues[1] == '3000') {
                    $( "#vertical-amount" ).val( numberWithCommas(parseInt(verticalValues[0])) + "  to  " + numberWithCommas(parseInt(verticalValues[1])) + "+ ft");
                    $( "#vertical-amount-meters" ).val( numberWithCommas(parseInt(verticalValues[0])) + "  to  " + numberWithCommas(parseInt(verticalValues[1])) + "+ m");
                } else {
                    $( "#vertical-amount" ).val( numberWithCommas(parseInt(verticalValues[0])) + "  to  " + numberWithCommas(parseInt(verticalValues[1])) + " ft");
                    $( "#vertical-amount-meters" ).val( numberWithCommas(parseInt(verticalValues[0])) + "  to  " + numberWithCommas(parseInt(verticalValues[1])) + " m");
                }
            }
        } else {
            if (verticalValues[0] == '10000') {
                $( "#vertical-amount" ).val(numberWithCommas(parseInt(verticalValues[1])) + "+ ft");
                $( "#vertical-amount-meters" ).val(numberWithCommas(parseInt(verticalValues[1])) + "+ m");
            } else if (verticalValues[0] == verticalValues[1]) {
                $( "#vertical-amount" ).val(numberWithCommas(parseInt(verticalValues[1])) + " ft");
                $( "#vertical-amount-meters" ).val(numberWithCommas(parseInt(verticalValues[1])) + " m");
            } else {
                if (verticalValues[1] == '10000') {
                    $( "#vertical-amount" ).val( numberWithCommas(parseInt(verticalValues[0])) + "  to  " + numberWithCommas(parseInt(verticalValues[1])) + "+ ft");
                    $( "#vertical-amount-meters" ).val( numberWithCommas(parseInt(verticalValues[0])) + "  to  " + numberWithCommas(parseInt(verticalValues[1])) + "+ m");
                } else {
                    $( "#vertical-amount" ).val( numberWithCommas(parseInt(verticalValues[0])) + "  to  " + numberWithCommas(parseInt(verticalValues[1])) + " ft");
                    $( "#vertical-amount-meters" ).val( numberWithCommas(parseInt(verticalValues[0])) + "  to  " + numberWithCommas(parseInt(verticalValues[1])) + " m");
                }
            }
        }


        var lastClimbedSlider = document.getElementById('last-climbed-slider-range');

        noUiSlider.create(lastClimbedSlider, {
            start: [ 0, 11 ],
            snap: true,
            connect: true,
            start: [wanted_last_climbed_min, wanted_last_climbed_max],
            range: {
                'min': 0,
                '9.0%': 1,
                '18.0%': 2,
                '27.0%': 3,
                '36.0%': 4,
                '45.0%': 5,
                '54.0%': 6,
                '63.0%': 7,
                '72.0%': 8,
                '81.0%': 9,
                '90.0%': 10,
                'max': 11
            }
        });

        lastClimbedSlider.noUiSlider.on('slide', function(values, handle, unencoded, tap, positions){
            switch(unencoded[0]) {
                case 0:
                    $( "#last-climbed-amount" ).val( "any time" );
                    break;
                case 1:
                    $( "#last-climbed-amount" ).val( "last 10 years" );
                    break;
                case 2:
                    $( "#last-climbed-amount" ).val( "last 5 years" );
                    break;
                case 3:
                    $( "#last-climbed-amount" ).val( "last 2 years" );
                    break;
                case 4:
                    $( "#last-climbed-amount" ).val( "last year" );
                    break;
                case 5:
                    $( "#last-climbed-amount" ).val( "last 90 days" );
                    break;
                case 6:
                    $( "#last-climbed-amount" ).val( "last 60 days" );
                    break;
                case 7:
                    $( "#last-climbed-amount" ).val( "last 30 days" );
                    break;
                case 8:
                    $( "#last-climbed-amount" ).val( "last 7 days" );
                    break;
                case 9:
                    $( "#last-climbed-amount" ).val( "last 3 days" );
                    break;
                case 10:
                    $( "#last-climbed-amount" ).val( "last 2 days" );
                    break;
                case 11:
                    $( "#last-climbed-amount" ).val( "last day" );
                    break;
                default:
                    $( "#last-climbed-amount" ).val( "any time" );
            }
            if (0 != unencoded[0] || 11 != unencoded[1]) {
                $('#last-climbed-label').css('color','#f24100');
                $('.last-climbed-ammount').css('color', '#f24100');
                $('.last-climbed-ammount').css('-webkit-text-fill-color', '#f24100');
                $('#last-climbed-slider-range > div > div').css('background-color','#f24100');
                $('#last-climbed-slider-range .noUi-background').css('background-color', '#CCC');
                $('#last-climbed-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
            } else {
                $('#last-climbed-label').css('color','#aaa');
                $('.last-climbed-ammount').css('color', '#aaa');
                $('.last-climbed-ammount').css('-webkit-text-fill-color', '#aaa');
                $('#last-climbed-slider-range > div > div').css('background-color','#ccc');
                $('#last-climbed-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-grey.png)');
            }
        });
        lastClimbedSlider.noUiSlider.on('end', function(values, handle, unencoded, tap, positions){
            delayShowDataLastClimbed("#last-climbed-amount", unencoded);
        });
        var lastClimbedValues = lastClimbedSlider.noUiSlider.get();
        switch(parseInt(lastClimbedValues[0])) {
            case 0:
                $( "#last-climbed-amount" ).val( "any time" );
                break;
            case 1:
                $( "#last-climbed-amount" ).val( "last 10 years" );
                break;
            case 2:
                $( "#last-climbed-amount" ).val( "last 5 years" );
                break;
            case 3:
                $( "#last-climbed-amount" ).val( "last 2 years" );
                break;
            case 4:
                $( "#last-climbed-amount" ).val( "last year" );
                break;
            case 5:
                $( "#last-climbed-amount" ).val( "last 90 days" );
                break;
            case 6:
                $( "#last-climbed-amount" ).val( "last 60 days" );
                break;
            case 7:
                $( "#last-climbed-amount" ).val( "last 30 days" );
                break;
            case 8:
                $( "#last-climbed-amount" ).val( "last 7 days" );
                break;
            case 9:
                $( "#last-climbed-amount" ).val( "last 3 days" );
                break;
            case 10:
                $( "#last-climbed-amount" ).val( "last 2 days" );
                break;
            case 11:
                $( "#last-climbed-amount" ).val( "last day" );
                break;
            default:
                $( "#last-climbed-amount" ).val( "any time" );
        }

        $('#peak-search-mobile').on('click', function(){
           $('#peak-search').addClass('modal fade right');
           $('#peak-search').attr('style','left: auto; right: 0px; margin-top: 0px; width: 240px;');
           $('#peak-search').removeClass('hidden-xs');
            $('#peak-search').removeClass('hidden-sm');
           $('#peak-search').modal('toggle');
           $('#mobile-collapse-nav').show();
           var height = $(window).height();
           var width = $(window).width() - 240;
           $('#mobile-collapse-nav').width(width);
           $('#mobile-collapse-nav').height(height);
        });

        $('#peak-search-layers').on('click', function(e){
           $('#gm-custom-maplegend-dropdown').hide();
           $('#gm-custom-mapdropdown').toggle();
           $('#gm-custom-mapdropdown').css('border-top-right-radius','8px');
           $('#gm-custom-mapdropdown').css('border-top-left-radius','8px');
           $('#gm-custom-mapoption-terrain').css('border-top-right-radius','8px');
           $('#gm-custom-mapoption-terrain').css('border-top-left-radius','8px');
           var height = $(window).height();
           var height_pad = (height - 480) / 2;
           $('#gm-custom-mapdropdown').css('top',height_pad);
           e.stopPropagation();
        });

        $('#peak-search-legend').on('click', function(e){
           $('#gm-custom-maplegend-dropdown').toggle();
           var width = $(window).width();
           var height = $(window).height();
           var height_pad = (height - 435) / 2;
           if (width <= 400) {
               var height_pad = (height - 585) / 2;
           }
           $('#gm-custom-maplegend-dropdown').css('top',height_pad);
           e.stopPropagation();
        });

        $('#gm-custom-maplegend-close').on('click', function(e){
           $('#gm-custom-maplegend-dropdown').hide();
           e.stopPropagation();
        });

        $('#mobile-collapse-nav').on('click', function(){
            $('#peak-search').modal('hide');
        });

        $('#location-search-mobile').on('click', function(){
            $('#location-search-mobile').html('<i class="fa fa-spinner fa-spin"></i>');
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(showLocalPeaks, showLocationError);
            } else {
                $('#location-search-mobile').html('<i class="fa fa-location-arrow"></i>');
                $('#mobile-search-title').html('Geolocation not supported');
            }
        });

        $('#marker-tooltip').on('click', function(){
            window.location = $(this).data('url');
        });

        $('#challenge-tooltip').on('click', function(){
            window.location = $(this).data('url');
        });

        //Custom Google Map type stuff

        $('#gm-custom-mapbutton').on('mouseenter', function(){
           $('#gm-custom-mapdropdown').show();
           $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
           $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
        });

        $('#gm-custom-mapbutton').on('mouseleave', function(){
           $('#gm-custom-mapdropdown').hide();
           $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
        });

        $('#gm-custom-mapbutton').on('touchstart', function() {
            $('#gm-custom-mapdropdown').toggle();
        });

        function showLocalPeaks(position) {
            $('#location-search-mobile').html('<i class="fa fa-location-arrow"></i>');
            $('#mobile-search-title').html('Peaks near you');
            wanted_lat = position.coords.latitude;
            wanted_lng = position.coords.longitude;
            $('#hdnLat').val(wanted_lat);
            $('#hdnLng').val(wanted_lng);
            u = updateURLParameter('#', 'lat', wanted_lat);
            u = updateURLParameter('#'+u, 'lng', wanted_lng);
            window.location.hash = u;
            //recenter map on lat/lng
            map.panTo([wanted_lng, wanted_lat]);
        }

        function showLocationError(error) {
            $('#location-search-mobile').html('<i class="fa fa-location-arrow"></i>');
            switch(error.code) {
                case error.PERMISSION_DENIED:
                    $('#mobile-search-title').html('Geolocation permission denied');
                    break;
                case error.POSITION_UNAVAILABLE:
                    $('#mobile-search-title').html('Location information unavailable');
                    break;
                case error.TIMEOUT:
                    $('#mobile-search-title').html('Location request timed out');
                    break;
                case error.UNKNOWN_ERROR:
                    $('#mobile-search-title').html('An unknown error occurred');
                    break;
            }
        }

        $('#peak-search').on('hide.bs.modal', function (e) {
          $('#peak-search').removeClass('modal fade right');
        });

        $('#peak-search').on('hidden.bs.modal', function (e) {
          $('#peak-search').attr('style','left: 0px; right: auto; width: 240px; position: absolute; display: inline-block;');
          $('#peak-search').addClass('hidden-xs');
          $('#peak-search').addClass('hidden-sm');
          $('#mobile-collapse-nav').hide();
        })

        $('#explore').on('submit', function(e) {
            e.preventDefault();
            //reset filters
            resetSliders();
            $('#search-peaks-btn').blur();
            if ($('#n').val() != '') {
                $('#hdnNearQuery').val('true');
            }
            $('#peak-search').modal('hide');
            delayShowKeywordNear("#keyword-search", $('#q').val(), $('#n').val());
        });

        document.addEventListener('keyup', function(e) {
            if (e.keyCode == 27) {
                $('#peak-search').modal('hide');
            }
        });

    });

    function getUrlVars()
    {
        var vars = [], hash;
        var hashes = window.location.href.slice(window.location.href.indexOf('?') + 1).split('&');
        for(var i = 0; i < hashes.length; i++)
        {
            hash = hashes[i].split('=');
            vars.push(hash[0]);
            vars[hash[0]] = hash[1];
        }
        return vars;
    }

    function fix_item_location(id,point){
        $.post('{% url "fix_item_location" %}', {id:id, lat:point.lat(), long:point.lng()},
                function(data) {
                    var output = $("#message_map");
                    var output_div = $("#message_map_div");
                    output.html(data);
                    output_div.show();
                    setTimeout(function() {
                        $("#message_map_div").hide();
                    }, 10000)
                }
        );
    }

    function delete_peak_from_map (id){
        $.post('{% url "delete_peak_from_map" %}', {id:id},
                function(data) {
                    var output = $("#message_map");
                    var output_div = $("#message_map_div");
                    output.html(data);
                    output_div.show();
                    setTimeout(function() {
                        $("#message_map_div").hide();
                    }, 10000)
                }
        );
    }

    function check_is_in(marker){
        return map.getBounds().contains(marker.getPosition());
    }

    function delete_out_markers(){
        if (markersArray){
            for (i in markersArray){
                if (!check_is_in(markersArray[i])){
                    markersArray[i].remove();
                    markersArray.splice(i,1);
                }
            }
        }
    }

    function deletehighest(){
        if (markersArray){
            for (i in markersArray){
                if (markersArray[i].properties.iconstyle == 'marker_icon_redgreen' || markersArray[i].properties.iconstyle == 'marker_icon_red'){
                    markersArray[i].remove();
                    markersArray.splice(i,1);
                }
            }
        }
    }

    function limit_number_of_markers(limit){
        if (markersArray.length > limit){
            for (i = markersArray.length-1; i>=limit; i--){
                markersArray[i].remove();
                markersArray.splice(i,1);
            }
        }
    }

    function elevation_range(data){
        if (markersArray){
            for (i = markersArray.length-1; i>=0; i--){
                var inrange = false;
                $.each(data, function(k, v){
                    var latLng1 = new mapboxgl.LngLat(v.lng, v.lat);
                    if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng1)){
                        inrange = true;
                    }
                });
                if (!inrange){
                    markersArray[i].remove();
                    markersArray.splice(i,1);
                }
            }
        }
    }

    function delete_old_markers(data){
        if (markersArray){
            for (i = markersArray.length-1; i>=0; i--){
                var inrange = false;
                $.each(data, function(k, v){
                    var latLng1 = new mapboxgl.LngLat(v.lng, v.lat);
                    if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng1)){
                        inrange = true;
                    }
                });
                if (!inrange){
                    markersArray[i].remove();
                    markersArray.splice(i,1);
                }
            }
        }
    }

    function delete_old_photo_markers(data){
        if (photoMarkersArray){
            for (i = photoMarkersArray.length-1; i>=0; i--){
                var inrange = false;
                $.each(data, function(k, v){
                    var latLng1 = new mapboxgl.LngLat(v.photo_lng, v.photo_lat);
                    if (fromLatLngToString(photoMarkersArray[i].getLngLat()) == fromLatLngToString(latLng1)){
                        inrange = true;
                    }
                });
                if (!inrange){
                    photoMarkersArray[i].remove();
                    photoMarkersArray.splice(i,1);
                }
            }
        }
    }

    function delete_all_photo_markers(){
        if (photoMarkersArray){
            for (i = photoMarkersArray.length-1; i>=0; i--){
                photoMarkersArray[i].remove();
                photoMarkersArray.splice(i,1);
            }
        }
    }

    function delete_old_challenge_markers(data){
        if (challengeMarkersArray){
            for (i = challengeMarkersArray.length-1; i>=0; i--){
                var inrange = false;
                $.each(data, function(k, v){
                    var latLng1 = new mapboxgl.LngLat(v.challenge_lng, v.challenge_lat);
                    if (fromLatLngToString(challengeMarkersArray[i].getLngLat()) == fromLatLngToString(latLng1)){
                        inrange = true;
                    }
                });
                if (!inrange){
                    challengeMarkersArray[i].remove();
                    challengeMarkersArray.splice(i,1);
                }
            }
        }
    }

    function delete_all_challenge_markers(){
        if (challengeMarkersArray){
            for (i = challengeMarkersArray.length-1; i>=0; i--){
                challengeMarkersArray[i].remove();
                challengeMarkersArray.splice(i,1);
            }
        }
    }

    function fromLatLngToString(latLng) {
        return latLng.lat + ',' + latLng.lng;
    }

    </script>

    {% include "mapbox/map_layers.html" %}

{% endblock %}

{% block gallery %}
<div id="blueimp-gallery" class="blueimp-gallery blueimp-gallery-controls" data-use-bootstrap-modal="false" data-slideshow-interval="4000">
    <!-- The container for the modal slides -->
    <div class="slides"></div>
    <!-- Controls for the borderless lightbox -->
    <h3 class="title"></h3>
    <div class="description">
        <div class="description-text">
            <div class="description-text-caption"></div>
            <div class="description-text-user"></div>
        </div>
    </div>
    <a class="prev">‹</a>
    <a class="next">›</a>
    <a class="close">×</a>
    <ol class="indicator"></ol>
    <!-- The modal dialog, which will be used to wrap the lightbox content -->
    <div class="modal fade">
        <div class="modal-dialog" style="width: 80%;">
            <div class="modal-content">
                <div class="modal-header">
                    <button style="color: #fff;" type="button" class="close" aria-hidden="true">&times;</button>
                    <h4 class="modal-title"></h4>
                </div>
                <div class="modal-body next"></div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default pull-left prev">
                        <i class="glyphicon glyphicon-chevron-left"></i>
                        Previous
                    </button>
                    <button type="button" class="btn btn-primary next">
                        Next
                        <i class="glyphicon glyphicon-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="hidden-sm hidden-md hidden-lg"><div class="loading" style="display: none;">Loading&#8230;</div></div>
{% endblock %}