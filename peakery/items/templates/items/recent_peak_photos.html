{% extends "base.html" %}
{% load avatar_tags %}
{% load item_tags %}

{% block title %}Recent photos{% endblock %}
{% block titlemeta %}Recent photos - {% endblock %}
{% block description %}Recent photos on peakery{% endblock %}

{% block content %}

<div id="recent_summits">

    <h1>Recent photos</h1>

{#    <div class="summits">#}
{#        {% autopaginate peak_photos 32 %}#}
{#        {% for p in peak_photos %}#}
{#            <div class="summit">#}
{#                <a href="{{ p.item.get_absolute_url }}">#}
{#                    <img src="{% thumbnail p.image 220x179 crop %}" alt="{{ p.item.name }}" />#}
{#                </a>#}
{#                <div>#}
{#                    <a href="{{ p.item.get_absolute_url }}">{{ p.item.name }}</a>#}
{#                </div>#}
{#            <span>#}
{#                uploaded by <a href="{% url "user_profile" p.user %}">{{ p.user }}</a>#}
{#            </span>#}
{#            </div>#}
{#        {% endfor %}#}
{#    </div>#}
    <ul id="recentSummitsPhotos" class="clearfix">
        {% autopaginate peak_photos 32 %}
        {% for p in peak_photos %}
            {% if p.image %}
                <li class="summit">
                    <a href="{{ p.item.get_absolute_url }}" class="imgWrapper">
                        <img src="{{ p.thumb_url_med }}" alt="{{ p.item.name }}" width="250" height="179" />
                    </a>
                    <h3><a href="{{ p.item.get_absolute_url }}">{{ p.item.name }}</a></h3>
                    <ul class="info">
                        <li>{{ p.item.get_ubication_names_title }}</li>
                        <li>{{ p.item.get_elevation }}</li>
                        <li>added by <a href="{% url "user_profile" p.user.username %}">{{ p.user.username }}</a></li>
                    </ul>
                </li>
            {% endif %}
        {% endfor %}
    </ul>

<style type="text/css">
    #recentSummitsPhotos {
        display: block;
    }
    #recentSummitsPhotos li {
        float: left;
        width: 250px;
        padding: 10px;
        margin: 5px 15px;
    }
    #recentSummitsPhotos li a.imgWrapper {
        display: block;
        margin-bottom: 7px;
    }
    #recentSummitsPhotos li a.imgWrapper img {
        box-shadow: 2px 3px 5px #767676;
        -moz-box-shadow: 2px 3px 5px #767676;
        -webkit-box-shadow: 2px 3px 5px #767676;
    }
    #recentSummitsPhotos li h3 {
        margin-bottom: 3px;
    }
    #recentSummitsPhotos li h3 a {
        color: #404040;
        text-decoration: none;
        font-size: 16px;
    }
    #recentSummitsPhotos li ul.info {

    }
    #recentSummitsPhotos li ul.info li {
        margin: 0;
        padding: 0;
    }
    #recentSummitsPhotos li ul.info li a {
        
    }
</style>

    <div class="pagination">
        {% paginate %}
    </div>

</div><!-- END content_r -->



{% endblock %}

