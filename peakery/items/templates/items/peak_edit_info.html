{% extends "base_no_header_footer.html" %}

{% load static %}
{% load avatar_tags %}
{% load item_tags %}
{% load cache %}
{% load humanize %}

{% block title %}Edit info for {{ peak.name }}{% endblock %}
{% block titlemeta %}Edit info for {{ peak.name }}{% endblock %}
{% block description %}Edit info for {{ peak.name }}{% endblock %}

{% block mapbox %}
{% include "mapbox/mapbox.html" %}
{% endblock %}

{% block body_form %}<form id="peakedit_form" method="POST" action="/peaks/{{ peak.id }}/edit/">{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row" style="border-bottom: none;">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 form-header-bar">
            <div class="hidden-xs form-header-bar-img-div"><img class="form-header-bar-img" src="{{ peak.get_thumbnail_480 }}" /></div>
            <span style="font-size: 24px; font-weight: 600; color: #fff;">
            <div id="breadcrumbs" style="margin-left: 15px;">
                <div class="pull-right cancel-x-button-div">
                    <a class="cancel-x-button" href="/{{ peak.slug_new_text }}"><i class="fa fa-times"></i></a>
                </div>
                <div class="pull-right save-changes-button-div">
                    <button class="btn btn-secondary set2 input save-changes-button" id="edit_peak" disabled>Submit <span class="hidden-xs">changes</span></button>
                </div>
                <div>
                    <div class="form-header-title ellipsis"><span class="hidden-xs">Edit </span><span class="hidden-xs hidden-sm">info for </span><span class="header-peak-name">{{ peak.name }}</span></div>
                </div>
            </div>
            </span>
        </div>
    </div>
    <div id="hidden-geocoder" style="display: none"></div>
{% endblock %}

{% block content %}

<style>


    body.modal-open {
        overflow: visible;
    }

    .ajax-link {
        color: #aaa;
    }
    .ajax-link:hover {
        color: #bbb;
    }

    form input[type='text'], form input[type='number'] {
        border-radius: 12px;
    }

    .edit-peak-country-region:after {
        font-family: 'Font Awesome 5 Free' !important;
        font-weight: 900;
    }

    .country-regions, .edit-peak-country-region {
        border-radius: 12px;
    }

    #gm-custom-mapdropdown, #gm-custom-mapbutton {
        opacity: 1;
        webkit-backdrop-filter: blur(5px);
        backdrop-filter: blur(5px);
    }

    #gm-custom-mapbutton, #gm-custom-mapdropdown {
        border: 2px solid rgba(0,0,0,0.15);
    }

    #gm-custom-mapbutton {
        width: 180px;
        margin-left: 90px;
        border-top-right-radius: 8px;
        border-top-left-radius: 8px;
        border-bottom-right-radius: 8px;
        border-bottom-left-radius: 8px;
    }

    #gm-custom-mapbutton:hover {
        border-bottom-right-radius: 0px;
        border-bottom-left-radius: 0px;
    }

    #gm-custom-mapdropdown {
        width: 179px;
        margin-left: 91px;
        border-bottom-right-radius: 8px;
        border-bottom-left-radius: 8px;
    }

    #gm-custom-mapoption-terrain, #gm-custom-mapoption-natatl, #gm-custom-mapoption-outdoors, #gm-custom-mapoption-streets, #gm-custom-mapoption-topo, #gm-custom-mapoption-satstreets, #gm-custom-mapoption-sat {
        width: 175px;
        margin-left: -2px;
        border-bottom: solid 1px #f2f2f2;
    }

    #gm-custom-mapoption-sat {
        width: 175px;
        margin-left: -2px;
        border-bottom: solid 1px #f2f2f2;
    }

    #gm-custom-mapoption-sat {
        border-bottom-left-radius: 8px;
        border-bottom-right-radius: 8px;
    }

    #gm-custom-mapoption-streets:hover {
        background-color: rgb(235, 235, 235) !important;
        color: rgb(0, 0, 0) !important;
    }

    input::placeholder {
        font-size: 14px;
    }

    @media screen and (min-width: 1024px) {
        .form-header-bar-img {
            margin-top: 0px;
            margin-right: 20px;
            height: 60px;
            border-radius: 4px;
        }
    }

   @media screen and (max-width: 767px) and (min-width: 1px) {
        #content-body {
           margin-top: 0px;
           padding-bottom: 0px;
        }
        html, body {
            letter-spacing: .03125em;
        }
        .content-pane {
           margin-top: 0px;
        }
        #edit_user_mobile {
            font-size: 14px;
        }
        ul#user-files textarea {
            font-size: 14px;
        }
        .row-full-width .col-xs-12 {
            padding-left: 10px;
            padding-right: 10px;
        }
        .field-title {
            font-size: 16px;
            font-weight: 500;
        }
        .form-header-title {
            font-size: 14px;
            font-weight: 500;
        }
        input {
            font-size: 16px;
            font-weight: 300;
        }
        form textarea {
            font-size: 16px;
            font-weight: 300;
        }
        .route-info-section {
            margin-top: 20px;
        }
        .field-title-spacer {
            height: 0px;
        }
        .header-peak-name {
            color: #fff;
        }
        .header-peak {
            font-size: 18px;
            font-weight: 500;
        }
        .close-route-edit {
            margin-right: 0px;
        }
        .header-help {
            color: #999;
            font-size: 11px;
            font-weight: 300;
            margin-left: 10px;
        }
        .field-title {
            font-size: 16px;
            font-weight: 500;
        }
        .summitlog-section {
            margin-top: 20px;
        }
        #edit_peak {
            width: 100px;
        }
        ::-webkit-input-placeholder { font-size: 16px; }
        ::-moz-placeholder { font-size: 16px; } /* firefox 19+ */
        :-ms-input-placeholder { font-size: 16px; } /* ie */
        input:-moz-placeholder { font-size: 16px; }
        input { font-size: 16px; }
    }
    @media screen and (min-width: 768px) {

        #content-body {
           margin-top: 30px;
        }
        .content-pane {
           margin-top: 0px;
        }
        input {
            font-size: 18px;
            font-weight: 300;
        }
        form textarea {
            font-size: 18px;
            font-weight: 300;
        }
        .route-info-section {
            margin-top: 60px;
        }
        .field-title-spacer {
            height: 0px;
        }
        .save-changes-div {
            float: right;
        }
        .summitlog-section {
            margin-top: 30px;
        }
    }
    @media screen and (min-width: 768px) and (max-width: 1023px) {
        #edit_peak {
            width: 180px;
        }
        ul#peakroute-files textarea {
            font-size: 16px;
        }
        .header-peak-name {
            color: #fff;
        }
        .header-peak {
            font-size: 18px;
            font-weight: 500;
        }
        .close-route-edit {
            margin-right: 0px;
        }
        .header-help {
            color: #999;
            font-size: 12px;
            font-weight: 300;
            margin-left: 10px;
        }
        .field-title {
            font-size: 18px;
            font-weight: 500;
        }
        .form-header-title {
            font-size: 18px;
            font-weight: 500;
        }
        ::-webkit-input-placeholder { font-size: 16px; }
        ::-moz-placeholder { font-size: 16px; } /* firefox 19+ */
        :-ms-input-placeholder { font-size: 16px; } /* ie */
        input:-moz-placeholder { font-size: 16px; }
        input { font-size: 16px; }
    }
    @media screen and (min-width: 1024px) {
        #edit_peak {
            width: 210px;
        }
        ul#user-files textarea {
            font-size: 18px;
        }
        .header-peak-name {
            color: #fff;
            font-weight: 500;
        }
        .header-peak {
            font-size: 20px;
            font-weight: 300;
        }
        .close-route-edit {
            margin-right: 20px;
        }
        .header-help {
            color: #999;
            font-size: 14px;
            font-weight: 300;
            margin-left: 10px;
        }
        .field-title {
            font-size: 18px;
            font-weight: 500;
        }
        .form-header-title {
            font-size: 20px;
            font-weight: 300;
        }
        #gm-custom-mapunits {
            right: 142px;
        }
        ::-webkit-input-placeholder { font-size: 18px; }
        ::-moz-placeholder { font-size: 18px; } /* firefox 19+ */
        :-ms-input-placeholder { font-size: 18px; } /* ie */
        input:-moz-placeholder { font-size: 18px; }
        input { font-size: 18px; }
    }
    @media screen and (min-width: 1px) and (max-width: 1279px) {
         .no-header-container {
           padding-left: 0px;
        }
    }
    ::-webkit-input-placeholder { font-weight: 300; }
    ::-moz-placeholder { font-weight: 300; } /* firefox 19+ */
    :-ms-input-placeholder { font-weight: 300; } /* ie */
    input:-moz-placeholder { font-weight: 300; }
    input { font-weight: 300; }

    @-webkit-keyframes markerPulse {
      from { -webkit-filter: brightness(1.2) saturate(1.5); }
      50% { -webkit-filter: brightness(0.9) saturate(1); }
      to { -webkit-filter: brightness(1.2) saturate(1.5); }
    }
    .marker-pulse {
      -webkit-animation-name: markerPulse;
      -webkit-animation-duration: 3s;
      -webkit-animation-iteration-count: infinite;
    }
    .gm-style-mtc {
        opacity: .8;
    }

</style>

    <div class="container" style="padding-left: 0px; padding-right: 0px;">

        <div class="content-pane" style="background-color: #f6f6f6; padding-top: 0px;">

            <input type="hidden" name="item_id" value="{{ peak.id }}">
            {% csrf_token %}

            <div class="row">

                <div class="col-md-4" style="padding-top: 20px;">

                    <div class="row row-full-width">
                        <div class="col-md-12">
                            <span class="field-title">Peak name<span class="header-help" style="margin-top: 15px; padding-bottom: 0px; color: #999;">official or commonly used name</span></span>
                            <div class="field-title-spacer"></div>
                            <fieldset class="peakName">
                                <input value="{{ peak.name }}" type="text" name="peak-name" id="peak-name" style="width: 50%; min-width: 340px; padding: 10px;"></input>
                            </fieldset>
                        </div>
                    </div>

                    <div class="row row-full-width summitlog-section">
                        <div class="col-md-12">
                            <span class="field-title">Elevation</span>
                            <div class="field-title-spacer"></div>
                            <fieldset>
                                <div>
                                    <input value="{% if peak.elevation %}{{ peak.elevation|floatformat:"0" }}{% endif %}" type="text" name="peak-elevation" id="peak-elevation" style="width: 125px; padding: 10px;" placeholder="in feet...">
                                    <input type="hidden" name="peak-elevation-init-feet" id="peak-elevation-init-feet" value="">
                                    <input type="hidden" name="peak-elevation-init-meters" id="peak-elevation-init-meters" value="">
                                    <div class="btn-group" data-toggle="buttons" style="margin-left: 15px;">
                                        <label class="btn active">
                                        <input type="radio" name="peak-elevation-units" id="peak-elevation-ft" value="ft" checked><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i> <span>  ft</span>
                                        </label>
                                        <label class="btn" style="margin-left: 5px;">
                                        <input type="radio" name="peak-elevation-units" id="peak-elevation-m" value="m"><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i><span> m</span>
                                        </label>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                    </div>

                    <div class="row row-full-width summitlog-section">
                        <div class="col-md-12">
                            <span class="field-title">Prominence<span class="header-help" style="margin-top: 15px; padding-bottom: 0px; color: #999;">&nbsp;<a style="cursor: pointer;" data-toggle="modal" data-target="#about-prominence"><i class="fas fa-info-circle" style="font-size: 18px; color: #999;"></i></a></span></span>
                            <div class="field-title-spacer"></div>
                            <fieldset>
                                <div>
                                    <input value="{% if peak.prominence or peak.prominence == 0 %}{{ peak.prominence|floatformat:"0" }}{% endif %}" type="text" name="peak-prominence" id="peak-prominence" style="width: 125px; padding: 10px;" placeholder="in feet...">
                                    <input type="hidden" name="peak-prominence-init-feet" id="peak-prominence-init-feet" value="">
                                    <input type="hidden" name="peak-prominence-init-meters" id="peak-prominence-init-meters" value="">
                                    <div class="btn-group" data-toggle="buttons" style="margin-left: 15px;">
                                        <label class="btn active">
                                        <input type="radio" name="peak-prominence-units" id="peak-prominence-ft" value="ft" checked><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i> <span>  ft</span>
                                        </label>
                                        <label class="btn" style="margin-left: 5px;">
                                        <input type="radio" name="peak-prominence-units" id="peak-prominence-m" value="m"><i class="far fa-circle fa-2x"></i><i class="far fa-dot-circle fa-2x"></i><span> m</span>
                                        </label>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                    </div>

                    <div class="row row-full-width summitlog-section">
                        <div class="col-md-12">
                            <span class="field-title">Range</span>
                            <div class="field-title-spacer"></div>
                            <fieldset class="peakRange">
                                <input value="{% if peak.range %}{{ peak.range }}{% endif %}" type="text" name="peak-range" id="peak-range" style="width: 50%; min-width: 340px; padding: 10px;" placeholder="enter mountain range name..."></input>
                            </fieldset>
                        </div>
                    </div>

                    <div class="row row-full-width summitlog-section">
                        <div class="col-md-12">
                            <fieldset>
                                <div id="peak-alternate-name-div">
                                    <span class="field-title">Other peak names</span>
                                    <div class="field-title-spacer"></div>
                                    <div id="peak-alternate-names">
                                    {% for a in alternate_names %}
                                    <div id="alternate-name-{{ forloop.counter0 }}">
                                        <div class="country-regions" style="background-color: #ddd; display: inline-block; height: 60px; margin-right: 20px; margin-bottom: 20px;">
                                            <span class="alternate-name-value" style="display: inline-block; margin-left: 20px; font-weight: 500; line-height: 60px;">{{ a.name }}</span>
                                            <span class="pull-right" style="padding-top: 17px; padding-right: 15px; padding-left: 20px; font-size: 24px; color: #ddd;">
                                                <a style="display: none;" class="ajax-link" onclick="removeAlternateName({{ forloop.counter0 }});">
                                                    <i class="fa fa-times"></i>
                                                </a>
                                            </span>
                                        </div>
                                    </div>
                                    {% endfor %}
                                    </div>
                                    {% if alternate_names %}
                                    <div>
                                        <input placeholder="add another peak name..." type="text" name="peak-alternate-name" id="peak-alternate-name" style="width: 50%; min-width: 340px; padding: 10px; margin-right: 10px; margin-bottom: 15px;">
                                        <button type="button" id="btnAddAlternateName" class="btn btn-secondary" style="width: 150px; height: 56px; margin-top: -2px;">Add</button>
                                    </div>
                                    {% else %}
                                    <div>
                                        <input placeholder="add another peak name..." type="text" name="peak-alternate-name" id="peak-alternate-name" style="width: 70%; padding: 10px; margin-right: 10px; margin-bottom: 15px;">
                                        <button type="button" id="btnAddAlternateName" class="btn btn-secondary" style="width: 20%; height: 56px; margin-top: -2px;">Add</button>
                                    </div>
                                    {% endif %}
                                </div>
                            </fieldset>
                            <input type="hidden" name="peak_alternate_names" id="peak_alternate_names" value="">
                        </div>
                    </div>

                    <div class="row row-full-width summitlog-section">
                        <div class="col-md-12">
                            <fieldset class="peakOtherChanges">
                                <span class="field-title">Any other changes?</span>
                                <div class="field-title-spacer"></div>
                                <textarea name="peak-other-changes" id="peak-other-changes" style="display: block; font-size: 15px; min-height: 115px; padding: 10px; height: auto; resize: none; width: 100%;"></textarea>
                                <script type="text/javascript">
                                    $(function(){
                                        $('textarea#peak-other-changes').elastic();
                                        $('textarea#peak-other-changes').placeholder();
                                    });
                                </script>
                            </fieldset>
                        </div>
                    </div>

                </div>

                <div class="col-md-8" style="padding-top: 20px; background-color: #f2f2f2;">

                    <div class="row row-full-width summitlog-section" style="margin-top: 0px;">
                        <div class="col-md-12">
                            <span class="field-title">Summit location<span class="header-help" style="margin-top: 15px; padding-bottom: 0px; color: #999;"><span class="hidden-xs">adjust map so the <img class="marker-pulse" style="margin-bottom: 5px;" src="{% static 'img/<EMAIL>' %}"> is on the summit</span><span class="hidden-sm hidden-md hidden-lg">put the <img style="margin-bottom: 5px;" src="{% static 'img/<EMAIL>' %}"> on the summit</span></span></span>
                            <div class="field-title-spacer"></div>
                            <fieldset>
                                <div>
                                    <div style="float: left; margin-right: 20px;"><input placeholder="jump to location or coordinates..." type="text" name="peak-location-search" id="peak-location-search" style="width: 50%; min-width: 340px; padding: 10px;"></div>
                                    <div style="float: left; margin-top: 16px; color: #999; font-size: 14px;">ex: Seattle, WA or 47.601826, -122.332318</div>
                                </div>
                            </fieldset>
                        </div>
                    </div>

                    <div class="row row-full-width">
                        <div class="col-md-12">
                            <div id="peak-map-col" class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="padding-right: 0px; padding-left: 0px; background-image: url('{% static 'img/default.png' %}'); background-size: cover;">
                                <div id="map-canvas" style="width: 100%; height: 100%;">
                                    <div id="gm-custom-maptype" class="gmnoprint" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 180px; right: 50%; top: 0px;">
                                        <div id="gm-custom-mapbutton" class="hidden-xs hidden-sm" draggable="false" style="cursor: pointer; direction: ltr; overflow: hidden; text-align: left; position: relative; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); height: 54px; padding: 8px; -webkit-background-clip: padding-box; background-clip: padding-box; font-weight: 500;">
                                            <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Terrain</div>
                                        </div>
                                        <div id="gm-custom-mapdropdown" style="z-index: 10; padding-left: 2px; padding-right: 2px; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; position: absolute; top: 52px; left: 0px; right: 0px; text-align: left; display: none;">
                                            <div id="gm-custom-mapoption-terrain" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Terrain</div>
                                            </div>
                                            <div id="gm-custom-mapoption-natatl" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Natural Atlas <span style="font-size: 10px;">(US)</span></div>
                                            </div>
                                            <div id="gm-custom-mapoption-outdoors" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">OpenTopoMap</div>
                                            </div>
                                            <div id="gm-custom-mapoption-streets" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Streets</div>
                                            </div>
                                            <div id="gm-custom-mapoption-topo" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>
                                            </div>
                                            <div id="gm-custom-mapoption-satstreets" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite</div>
                                            </div>
                                            <div id="gm-custom-mapoption-sat" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                                                <div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite Topo</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div id="message_map_div" class="gmnoprint gm-style-mtc" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 104px; right: 0px; top: 0px; display: none;">
                                        <div id="message_map" draggable="false" style="direction: ltr; overflow: hidden; text-align: left; position: relative; color: rgb(86, 86, 86); font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 11px; border-bottom-left-radius: 2px; border-top-left-radius: 2px; border-bottom-right-radius: 2px; border-top-right-radius: 2px; -webkit-background-clip: padding-box; background-clip: padding-box; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; font-weight: 500;">

                                        </div>
                                    </div>

                                </div>
                                <div id="center-peak-marker" style="height: 21px; width: 21px; position: absolute; top: 100px; left: 100px;"><img class="marker-pulse" src="{% static 'img/<EMAIL>' %}" style="width: 21px; height: 21px;"></div>
                                <input type="hidden" name="peak-lat" id="peak-lat" value="{{ peak.lat }}">
                                <input type="hidden" name="peak-lng" id="peak-lng" value="{{ peak.long }}">
                                <input type="hidden" name="location-updated" id="location-updated" value="false">
                            </div>
                        </div>
                    </div>

                    <div class="row row-full-width summitlog-section">
                        <div class="col-md-12">
                            <fieldset>
                                <div id="peak-country-region-div">
                                    <span class="field-title">Country/Region</span>
                                    <div class="field-title-spacer"></div>
                                    <div id="peak-countries">
                                    {% for c in country_regions %}
                                    <div id="country-{{ c.country.id }}">
                                        <div class="country-regions" style="background-color: #ddd; display: inline-block; height: 60px; margin-right: 20px; margin-bottom: 20px;">
                                            <span class="country-value" data-country="{{ c.country.id }}" style="display: inline-block; margin-left: 20px; font-weight: 500; line-height: 60px;">{{ c.country.name }}</span>
                                            <span class="pull-right" style="padding-top: 17px; padding-right: 15px; padding-left: 20px; font-size: 24px; color: #ddd;">
                                                <a style="display: none;" class="ajax-link remove-country-link" onclick="removeCountry({{ c.country.id }});">
                                                    <i class="fa fa-times"></i>
                                                </a>
                                            </span>
                                        </div>
                                        <div id="country-{{ c.country.id }}-regions">
                                        {% for cr in c.country_regions %}
                                            <div id="region-{{ cr.id }}">
                                                <div style="width: 100%;"></div>
                                                <div style="float: left; margin-left: 15px; margin-right: 15px; height: 60px;"><i class="fa fa-level-up fa-rotate-90" style="margin-top: 20px;" aria-hidden="true"></i></div>
                                                <div class="country-regions" style="background-color: #ddd; display: inline-block; height: 60px; margin-right: 20px; margin-bottom: 20px;">
                                                    <span class="region-value" data-region="{{ cr.id }}" id="region-{{ cr.id }}-name" style="display: inline-block; margin-left: 20px; font-weight: 500; line-height: 60px;">{{ cr.name }}</span>
                                                    <span class="pull-right" style="padding-top: 17px; padding-right: 15px; padding-left: 20px; font-size: 24px; color: #ddd;">
                                                        <a style="display: none;" class="ajax-link remove-region-link" onclick="removeRegion({{ cr.id }},{{ c.country.id }});">
                                                            <i class="fa fa-times"></i>
                                                        </a>
                                                    </span>
                                                </div>
                                            </div>
                                        {% endfor %}
                                        </div>
                                        <div style="width: 100%;"></div>
                                        <div style="float: left; margin-left: 15px; margin-right: 15px; height: 60px;"><i class="fa fa-level-up fa-rotate-90" style="margin-top: 20px;" aria-hidden="true"></i></div>
                                        <div class="edit-peak-country-region">
                                            <select class="add-region-select" id="country-{{ c.country.id }}-add-region" data-country="{{ c.country.id }}">
                                                <option value="0" selected="selected">add another region</option>
                                                {% for r in c.country_all_regions %}
                                                <option value="{{ r.id }}">{{ r.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div style="height: 30px;"></div>
                                    </div>
                                    {% endfor %}
                                    </div>
                                    <div id="add-new-country" style="display: none;">
                                        <fieldset class="peakNewCountry">
                                            <div id="peak-new-country-div" style="float: left; width: 50%; min-width: 280px;">
                                                <input placeholder="country name..." type="text" name="peak-new-country" id="peak-new-country" style="width: 100%; padding: 10px;"></input>
                                            </div>
                                            <div id="search-country-progress" style="display: none; float: left; padding-top: 20px; padding-right: 20px; margin-left: -41px;font-size: 20px; color: #ddd;">
                                                <i class="fa fa-spinner fa-spin"></i>
                                            </div>
                                            <div style="float: left; padding-top: 17px; padding-right: 15px; padding-left: 20px; font-size: 24px; color: #ddd;">
                                                <a id="reset-add-new-country" class="ajax-link">
                                                    <i class="fa fa-times"></i>
                                                </a>
                                            </div>
                                        </fieldset>
                                        <div style="height: 30px;"></div>
                                    </div>
                                    <div id="add-new-country-link">if on border <a style="cursor: pointer;" onclick="addNewCountry();">add another country</a></div>
                                </div>
                            </fieldset>
                            <input type="hidden" name="peak_countries" id="peak_countries" value="">
                            <input type="hidden" name="peak_regions" id="peak_regions" value="">
                            <input type="hidden" name="peak_countries_to_remove" id="peak_countries_to_remove" value="">
                            <input type="hidden" name="peak_regions_to_remove" id="peak_regions_to_remove" value="">
                        </div>
                    </div>

                </div>

            </div>

        </div>

    </div>

<div class="about-prominence-modal modal fade" id="about-prominence" tabindex="-1" role="dialog" aria-labelledby="about-prominence-label" style="display: none;">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
              <h4 class="modal-title" id="about-prominence-label">A note on <span style="color: #f24100;">prominence</span></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12" style="margin-bottom: 20px; font-size: 16px; line-height: 32px;">
                        <p style="line-height: 32px; margin-bottom: 30px;">A peak’s prominence, also known as topographic prominence or relative height, is a measure of how distinct a peak is from other peaks. It’s defined as the vertical distance between a peak and the lowest contour line surrounding that peak and no higher peak. Prominence is a popular metric for peaks for two reasons: 1) it’s objective and relatively easy to calculate, and 2) higher prominence peaks are more likely to be interesting with higher independence vs. peaks with lower prominence.</p>
                        <p style="line-height: 32px;">Note that prominence is not the same thing as a peak’s vertical drop, which is usually extremely difficult to calculate because a peak’s base elevation can be highly subjective. Instead, peakery shows vertical gain for specific routes up peaks.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="thanks-modal modal fade" id="thanks-modal" tabindex="-1" role="dialog" aria-labelledby="thanks-modal-label" style="display: none;">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button>
              <h4 class="modal-title" id="thanks-modal-label">Thanks!</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12" style="margin-bottom: 20px; font-size: 16px; line-height: 32px;">
                        <p style="line-height: 32px; margin-bottom: 30px;">Thanks for your help!</p>
                        <p style="line-height: 32px; margin-bottom: 30px;">We'll review the changes and email you when they are approved.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="savetext-modal modal fade" id="savetext-modal" tabindex="-1" role="dialog" aria-labelledby="savetext-modal-label">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
              <h4 class="modal-title" id="savetext-modal-label"></h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div id="savetext-modal-body" class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="margin-bottom: 5px; font-size: 16px;"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<style type="text/css">
    .ui-autocomplete {
        max-height: 100px;
        overflow-y: auto;
        /* prevent horizontal scrollbar */
        overflow-x: hidden;
        /* add padding to account for vertical scrollbar */
        padding-right: 20px;
    }
        /* IE 6 doesn't support max-height
       * we use height instead, but this forces the menu to always be this tall
       */
    * html .ui-autocomplete {
        height: 100px;
    }
</style>

<script type="text/javascript">

var map;
var viewer;
var topo;
var outdoors;
var center = null;
var map_bounds;
var init = false;
var photos_displayed = 0;
var photos_page = 1;
var photos = [];
var pageX, pageY, mapX, mapY;
var iconstyle;

var markersArray = [];


const geocoder = new MapboxGeocoder({
    accessToken: mapboxgl.accessToken,
    types: 'country, region, place'
});

geocoder.addTo('#hidden-geocoder'); // MapBoxGeocoder doesn't work unless we attach it to something

function loadPeaks(peak_id) {

    if (!init) {

        //get map bounds
        var bounds = map.getBounds();

        var counter = 0;
        var strTemp = '';
        var LatLngList = [];

        var params = '';
        params = params + '&q=';
        params = params + '&n=';
        params = params + '&elev_min=0';
        params = params + '&elev_max=29500';
        params = params + '&prom_min=0';
        params = params + '&prom_max=29500';
        params = params + '&summits_min=0';
        params = params + '&summits_max=500';
        params = params + '&difficulty_min=1';
        params = params + '&difficulty_max=5';
        params = params + '&lat=';
        params = params + '&lng=';
        params = params + '&bounds=' + bounds.getSouthWest().lat + ',' + bounds.getSouthWest().lng + ',' + bounds.getNorthEast().lat + ',' + bounds.getNorthEast().lng;

        //update hidden parameters
        map_bounds = bounds.getSouthWest().lat + ',' + bounds.getSouthWest().lng + ',' + bounds.getNorthEast().lat + ',' + bounds.getNorthEast().lng;

        if (params.length > 0) params = '?' + params.slice(-1 * (params.length - 1));
        var byRegion = false;
        var totalPeaks = 0;
        $.getJSON('{% url "peaks_map" %}' + params, function (data) {
            $.each(data, function (key, val) {
                var currentRequest = true;
                if (key == 'parameters') {
                    $.each(val, function (parameterkey, parameterval) {
                        if (parameterval.bounds != map_bounds) currentRequest = false;
                    });
                }

                if (!currentRequest) {
                    return false;
                }

                if (key == 'peaks') {

                    var havePeaks = false;

                    $.each(val, function (peakkey, peakval) {

                        if (!havePeaks) {

                            //first time through, delete highest peak marker and remove any markers not on map
                            deletehighest();
                            //delete markers out of margins
                            delete_old_markers(val);

                        }

                        havePeaks = true;

                        //build country string
                        var country = '';
                        $.each(peakval.country, function (countrykey, countryval) {
                            country = country + '<div><a href="' + countryval.country_slug + '/">' + countryval.country_name + '</a></div>';
                        });
                        if (country == '') {
                            country = 'Unknown Country';
                        }

                        //build region string
                        var region = '';
                        var mobile_region = '';
                        var region_bull_class = 'hidden-lg hidden-md hidden-sm hidden-xs';
                        $.each(peakval.region, function (regionkey, regionval) {
                            region_bull_class = '';
                            region = region + '<div style="margin-bottom: 5px;"><a class="truncate" href="' + regionval.region_slug + '/">' + regionval.region_name + ', ' + regionval.country_name + '</a></div>';
                            mobile_region = '<div style="margin-bottom: 5px;"><a class="truncate" href="' + regionval.region_slug + '/">' + regionval.country_name + '</a></div>';
                        });
                        if (region == '') {
                            region = country;
                            mobile_region = country;
                        }

                        //build summits string
                        var summits, tooltip_your_summits;
                        if (peakval.summit_count > 0) {
                            summits = '<a href="/' + peakval.slug + '/summits/">' + peakval.summit_count + '</a>';
                        } else {
                            summits = '<a href="/' + peakval.slug + '/summits/">0</a><br /><a href="/' + peakval.slug + '/">Be the first!</a>';
                        }
                        if (peakval.your_summits > 0) {
                            summits = summits + '<br /><span style="color: #3fc663; font-weight: 500;">' + peakval.your_summits + 'x by you</span>';
                            tooltip_your_summits = '&nbsp;<span style="color: #3fc663; font-weight: 500;">(You ' + peakval.your_summits + 'x)</span>';
                        } else {
                            tooltip_your_summits = '';
                        }

                        //build tooltip string
                        var tooltip_html, tooltip_width, tooltip_height, marker_top, marker_left;
                        if (peakval.thumbnail_url.indexOf('default.png.350x245_q95_crop.jpg') < 0) {
                            tooltip_html = '<img class="map-tooltip-img hover-photos-hover" src="{{ MEDIA_URL }}' + peakval.thumbnail_url + '"><div class="map-tooltip-info"><div class="map-tooltip-peak-name ellipsis">' + peakval.name + '</div><div class="map-tooltip-peak-stats"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + tooltip_your_summits + challenges + '</div></div>';
                            tooltip_width = 220;
                            tooltip_height = 165;
                        } else {
                            tooltip_html = '<div class="map-tooltip-info"><div class="map-tooltip-peak-name ellipsis">' + peakval.name + '</div><div class="map-tooltip-peak-stats"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + tooltip_your_summits + challenges + '</div></div>';
                            tooltip_width = 220;
                            tooltip_height = 50;
                        }
                        var tooltip_url = '/' + peakval.slug;

                        var latLng = new mapboxgl.LngLat(peakval.lng, peakval.lat);

                        if (peakval.id == '{{ peak.id }}') {
                            //peak page peak gets special icon
                            iconstyle = 'marker_icon_peak';
                        } else if (counter == 0) {
                            //highest peak gets red icon
                            iconstyle = 'marker_icon_red';
                        } else if (peakval.your_summits > 0) {
                            //if you have summited then green icon
                            iconstyle = 'marker_icon_green';
                        } else if (peakval.your_attempts > 0) {
                            //if you have attempted then yellow icon
                            iconstyle = 'marker_icon_yellow';
                        } else {
                            iconstyle = 'marker_icon';
                        }

                        var is_draggable = false;

                        //check if already exist so don't put again
                        var exists = false;
                        for (i = markersArray.length-1; i>=0; i--){
                            if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng)){
                                exists = true ;
                                //if the highest is in the actual viewport, not as the highest, delete it
                                if (iconstyle=='marker_icon_red' || iconstyle=='marker_icon_redgreen' || iconstyle=='marker_icon_green') {
                                    markersArray[i].remove();
                                    markersArray.splice(i,1);
                                    exists = false;
                                }
                            }
                        }

                        //if we are only showing one peak_id, assume other peaks already exist so don't show them
                        if (peak_id == null) {
                            //do nothing for now
                        } else {
                            if (peakval.id != peak_id) {
                                //exists = true;
                            }
                        }

                        if (!exists) {
                            var latLng = [peakval.lng, peakval.lat];
                            //add marker
                            //create an HTML element for the marker
                            var el = document.createElement('div');
                            el.className = iconstyle;

                            el.addEventListener('click', function(e) {
                                if (isTouchDevice()) {

                                    var bottom = $('#map-canvas').height();
                                    var right = $('#map-canvas').width();

                                    if (mapY < (bottom/2)) {
                                        marker_top = mapY;
                                    } else {
                                        marker_top = mapY - tooltip_height - 45;
                                    }

                                    if (mapX < (right/3)) {
                                        marker_left = mapX;
                                    } else if (mapX >= (right/3) && mapX < ((right/3)*2)) {
                                        marker_left = mapX - (tooltip_width/2) - 15;
                                    } else {
                                        marker_left = mapX - tooltip_width - 30;
                                    }

                                    $('#marker-tooltip').html(marker.properties.tooltipContent).css({
                                        'left': marker_left,
                                        'top': marker_top,
                                        'width': tooltip_width,
                                        'height': tooltip_height
                                    }).show();
                                    $('#marker-tooltip').data('url',marker.properties.tooltipUrl);
                                } else {
                                    //console.log(peakval.slug);
                                    //location = '/' + peakval.slug + '/';;
                                }
                            });

                            el.addEventListener('mouseover', function(e) {

                                var bottom = $('#map-canvas').height();
                                var right = $('#map-canvas').width();

                                if (mapY < (bottom/2)) {
                                    marker_top = mapY;
                                } else {
                                    marker_top = mapY - tooltip_height - 45;
                                }

                                if (mapX < (right/3)) {
                                    marker_left = mapX;
                                } else if (mapX >= (right/3) && mapX < ((right/3)*2)) {
                                    marker_left = mapX - (tooltip_width/2) - 15;
                                } else {
                                    marker_left = mapX - tooltip_width - 30;
                                }

                                $('#marker-tooltip').html(marker.properties.tooltipContent).css({
                                    'left': marker_left,
                                    'top': marker_top,
                                    'width': tooltip_width,
                                    'height': tooltip_height
                                }).show();
                                $('#marker-tooltip').data('url',marker.properties.tooltipUrl);

                            });

                            el.addEventListener('mouseout', function(e) {
                                if (isTouchDevice()) {
                                    //$('#marker-tooltip').hide();
                                } else {
                                    $('#marker-tooltip').hide();
                                }
                            });

                            var marker = new mapboxgl.Marker(el)
                                .setLngLat(latLng)
                                .setOffset([-5, -10])
                                .setDraggable(is_draggable);

                            marker.properties = {};
                            marker.properties.tooltipContent = tooltip_html;
                            marker.properties.tooltipUrl = tooltip_url;
                            marker.properties.iconstyle = iconstyle;
                            marker.properties.peakid = peakval.id;

                            markersArray.push(marker);
                            LatLngList.push(latLng);

                        }

                        counter++;
                    });

                    //add markers to map in reverse order so highest on top
                    for (var i = markersArray.length - 1; i >= 0; --i) {
                        markersArray[i].addTo(map);
                    }

                    if (!havePeaks) {
                        //didn't have any peaks, so remove all markers
                        delete_old_markers(val);
                    }
                }
            });
        });

    }

    init = true;

}

function initialize() {

    var mapDiv = document.getElementById('map-canvas');
    var latLng = new mapboxgl.LngLat({{ peak.long }}, {{ peak.lat }});
    var LatLngList = [];

    var mapZoom = 14;

    if (isTouchDevice()) {
        map = new mapboxgl.Map({
            container: mapDiv, // HTML container id
            style: mapStyle, // style URL
            center: latLng, // starting position as [lng, lat]
            zoom: mapZoom,
            scrollZoom: false
        });
    } else {
        map = new mapboxgl.Map({
            container: mapDiv, // HTML container id
            style: mapStyle, // style URL
            center: latLng, // starting position as [lng, lat]
            zoom: mapZoom,
            scrollZoom: false
        });
        scale = new mapboxgl.ScaleControl({maxWidth: 120, unit: 'imperial'});
        map.addControl(scale, 'bottom-right');
        var nav = new mapboxgl.NavigationControl({showCompass: false});
        map.addControl(nav, 'bottom-right');
        // disable map rotation using right click + drag
        map.dragRotate.disable();
        // disable map rotation using touch rotation gesture
        map.touchZoomRotate.disableRotation();
    }

    document.getElementById('hidden-geocoder').appendChild(geocoder.onAdd(map));

    function calculateCenter() {
      center = map.getCenter();
    }

    map.on('resize', function(e) {
      map.setCenter(center);
    });

    map.on('moveend', function () {
        if (center.lat != map.getCenter().lat || center.lng != map.getCenter().lng) {
            $('#location-updated').val('true');
        }
        calculateCenter();
        //recenter main peak marker
        var mapCenter = map.getCenter();
        var latitude = mapCenter.lat;
        var longitude = mapCenter.lng;
        $('#peak-lat').val(latitude);
        $('#peak-lng').val(longitude);
        //markersArray[0].setPosition(mapCenter);
        $('#edit_peak').prop('disabled', false);
    });

    map.on('click', function(e) {
        if (isTouchDevice()) {
            $('#marker-tooltip').hide();
        } else {
            $('#marker-tooltip').hide();
        }
    });

    map.on('dragstart', function(e) {
        if (isTouchDevice()) {
            $('#marker-tooltip').hide();
        } else {
            $('#marker-tooltip').hide();
        }
    });

    map.on('load', function () {
        calculateCenter();
        var mapUnits = readCookie('map_units');
        if (mapUnits != '') {
            toggleMapUnits(mapUnits);
        }
        setMapControls();
    });

    map.on('click', function () {
        map.scrollZoom.enable();
    });

}

function setMapControls() {
    var check = checkIfMapboxStyleIsLoaded();
    if (!check) {
        // It's not safe to manipulate layers yet, so wait 200ms and then check again
        setTimeout(function() {
            setMapControls();
        }, 200);
        return;
    }
    // Whew, now it's safe to manipulate layers!
    var mapUnits = readCookie('map_units');
    if (mapUnits == 'meters') {
        toggleMapUnits(mapUnits);
    }
}

function addExtraMapLayers() {
    var check = checkIfMapboxStyleIsLoaded();
    if (!check) {
        // It's not safe to manipulate layers yet, so wait 200ms and then check again
        setTimeout(function() {
            addExtraMapLayers();
        }, 200);
        return;
    }
    // Whew, now it's safe to manipulate layers!
    // No extra map layers necessary
}

$(document).ready(function(){

    //pre-select meters?
    {% if not peak.is_usa %}
    $('#peak-elevation-m').click();
    $('#peak-prominence-m').click();
    setElevationMeters();
    setProminenceMeters();
    {% endif %}

    //load any alternate names to hidden input
    {% if alternate_names %}
    var temp_alternate_name = '';
    {% for a in alternate_names %}
        temp_alternate_name = $('#peak_alternate_names').val();
        $('#peak_alternate_names').val(temp_alternate_name+'{{ a.name }}|');
    {% endfor %}
    {% endif %}

    //load any regions/countries to hidden input
    {% if country_regions %}
    var temp_cr = '';
    var region_counter = 0;
    var country_counter = 0;
    {% for c in country_regions %}
        country_counter ++;
        temp_cr = $('#peak_countries').val();
        $('#peak_countries').val(temp_cr+'{{ c.country.id }}|');
        {% for cr in c.country_regions %}
            region_counter ++;
            temp_cr = $('#peak_regions').val();
            $('#peak_regions').val(temp_cr+'{{ cr.id }}|');
            {% endfor %}
    {% endfor %}
    {% endif %}
    if (region_counter > 1) {
        $('.remove-region-link').show();
    }
    if (country_counter > 1) {
        $('.remove-country-link').show();
    }

    //populate initial elevation/prominence values
    {% if peak.elevation %}
    var initElevationFeet = {{ peak.elevation|floatformat:"0" }};
    var initElevationMeters = Math.round(initElevationFeet * .3048);
    $('#peak-elevation-init-feet').val(initElevationFeet);
    $('#peak-elevation-init-meters').val(initElevationMeters);
    {% endif %}
    {% if peak.prominence or peak.prominence == 0 %}
    var initProminenceFeet = {{ peak.prominence|floatformat:"0" }};
    var initProminenceMeters = Math.round(initProminenceFeet * .3048);
    $('#peak-prominence-init-feet').val(initProminenceFeet);
    $('#peak-prominence-init-meters').val(initProminenceMeters);
    {% endif %}

    //$('#map-canvas').height(500);
    var window_width = $(window).width();
    if (window_width < 768) {
        $('#map-canvas').height(350);
    } else {
        $('#map-canvas').height(500);
    }
    //center orange peak marker
    var centerMarkerTop = ($('#map-canvas').height() / 2) - 22;
    var centerMarkerLeft = ($('#map-canvas').width() / 2) - 13;
    $('#center-peak-marker').css({'top':centerMarkerTop});
    $('#center-peak-marker').css({'left':centerMarkerLeft});

    $('#map-canvas').mousemove(function(e) {
        var offset = $(this).offset();
        pageX = e.pageX;
        pageY = e.pageY;
        mapX = (e.pageX - offset.left);
        mapY = (e.pageY - offset.top);
    });

    $('#map-canvas').on('touchstart', function(e) {
        var offset = $(this).offset();
        pageX = e.originalEvent.touches[0].pageX;
        pageY = e.originalEvent.touches[0].pageY;
        mapX = (pageX - offset.left);
        mapY = (pageY - offset.top);
    });

    $(window).resize(function() {
        var window_width = $(window).width();
        if (window_width < 768) {
            $('#map-canvas').height(350);
        } else {
            $('#map-canvas').height(500);
        }
        //re-center orange peak marker
        var centerMarkerTop = ($('#map-canvas').height() / 2) - 22;
        var centerMarkerLeft = ($('#map-canvas').width() / 2) - 13;
        $('#center-peak-marker').css({'top':centerMarkerTop});
        $('#center-peak-marker').css({'left':centerMarkerLeft});
        map.resize();
    });

    initialize();

    //switch map units
    $("#gm-custom-mapunits").click(function(){
        if ($('#gm-custom-mapunitsbutton-label-meters').hasClass('gm-custom-mapunits-selected')) {
            toggleMapUnits('feet');
            scale.setUnit('imperial');
        } else {
            toggleMapUnits('meters');
            scale.setUnit('metric');
        }
    });

    //Disable scrollZoom
    $('#map-canvas').on('mouseleave', function() {
        map.scrollZoom.disable();
    });

    //Custom Google Map type stuff

    $('#gm-custom-mapbutton').on('mouseenter', function(){
       $('#gm-custom-mapdropdown').show();
       $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
       $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapbutton').on('mouseleave', function(){
       $('#gm-custom-mapdropdown').hide();
       $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    $('#gm-custom-mapbutton').on('touchstart', function() {
        $('#gm-custom-mapdropdown').toggle();
    });

    var flag = false;
    $('#gm-custom-mapoption-satstreets').on('touchstart click', function(e) {
        if (!flag) {
            flag = true;
            setTimeout(function(){ flag = false; }, 500);
            $('#gm-custom-mapoption-satstreets').css("background-color", "#0ae");
            $('#gm-custom-mapoption-satstreets').css("color", "#fff");
            $('#gm-custom-mapoption-topo').css("background-color", "#fff");
            $('#gm-custom-mapoption-topo').css("color", "#333");
            $('#gm-custom-mapoption-sat').css("background-color", "#fff");
            $('#gm-custom-mapoption-sat').css("color", "#333");
            $('#gm-custom-mapoption-3d').css("background-color", "#fff");
            $('#gm-custom-mapoption-3d').css("color", "#333");
            $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
            $('#gm-custom-mapoption-terrain').css("color", "#333");
            $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
            $('#gm-custom-mapoption-outdoors').css("color", "#333");
            $('#gm-custom-mapoption-streets').css("background-color", "#fff");
            $('#gm-custom-mapoption-streets').css("color", "#333");
            $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
            $('#gm-custom-mapoption-natatl').css("color", "#333");
            var mapButton = $('#gm-custom-mapbutton').html();
            if (mapButton.includes('3D Map')) {
                exit3dCamera('satellite');
            } else {
                toggleMapType('satellite');
                $('#gm-custom-mapdropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();
            }
            $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite</div>');
        }
        return false
    });

    $('#gm-custom-mapoption-satstreets').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapoption-satstreets').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    var flag = false;
    $('#gm-custom-mapoption-topo').on('touchstart click', function() {
        if (!flag) {
            flag = true;
            setTimeout(function(){ flag = false; }, 500);
            $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
            $('#gm-custom-mapoption-satstreets').css("color", "#333");
            $('#gm-custom-mapoption-topo').css("background-color", "#0ae");
            $('#gm-custom-mapoption-topo').css("color", "#fff");
            $('#gm-custom-mapoption-sat').css("background-color", "#fff");
            $('#gm-custom-mapoption-sat').css("color", "#333");
            $('#gm-custom-mapoption-3d').css("background-color", "#fff");
            $('#gm-custom-mapoption-3d').css("color", "#333");
            $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
            $('#gm-custom-mapoption-terrain').css("color", "#333");
            $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
            $('#gm-custom-mapoption-outdoors').css("color", "#333");
            $('#gm-custom-mapoption-streets').css("background-color", "#fff");
            $('#gm-custom-mapoption-streets').css("color", "#333");
            $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
            $('#gm-custom-mapoption-natatl').css("color", "#333");
            var mapButton = $('#gm-custom-mapbutton').html();
            if (mapButton.includes('3D Map')) {
                exit3dCamera('caltopo');
            } else {
                toggleMapType('caltopo');
                $('#gm-custom-mapdropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();
            }
            $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Govt Topo <span style="font-size: 10px;">(as avail)</span></div>');
        }
        return false
    });

    $('#gm-custom-mapoption-topo').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapoption-topo').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    var flag = false;
    $('#gm-custom-mapoption-sat').on('touchstart click', function() {
        if (!flag) {
            flag = true;
            setTimeout(function(){ flag = false; }, 500);
            $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
            $('#gm-custom-mapoption-satstreets').css("color", "#333");
            $('#gm-custom-mapoption-topo').css("background-color", "#fff");
            $('#gm-custom-mapoption-topo').css("color", "#333");
            $('#gm-custom-mapoption-sat').css("background-color", "#0ae");
            $('#gm-custom-mapoption-sat').css("color", "#fff");
            $('#gm-custom-mapoption-3d').css("background-color", "#fff");
            $('#gm-custom-mapoption-3d').css("color", "#333");
            $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
            $('#gm-custom-mapoption-terrain').css("color", "#333");
            $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
            $('#gm-custom-mapoption-outdoors').css("color", "#333");
            $('#gm-custom-mapoption-streets').css("background-color", "#fff");
            $('#gm-custom-mapoption-streets').css("color", "#333");
            $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
            $('#gm-custom-mapoption-natatl').css("color", "#333");
            var mapButton = $('#gm-custom-mapbutton').html();
            if (mapButton.includes('3D Map')) {
                exit3dCamera('sat_topo');
            } else {
                toggleMapType('sat_topo');
                $('#gm-custom-mapdropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();
            }
            $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Satellite Topo</div>');
        }
        return false
    });

    $('#gm-custom-mapoption-sat').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapoption-sat').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    $('#gm-custom-mapoption-3d').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapoption-3d').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    var flag = false;
    $('#gm-custom-mapoption-terrain').on('touchstart click', function() {
        if (!flag) {
            flag = true;
            setTimeout(function(){ flag = false; }, 500);
            $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
            $('#gm-custom-mapoption-satstreets').css("color", "#333");
            $('#gm-custom-mapoption-topo').css("background-color", "#fff");
            $('#gm-custom-mapoption-topo').css("color", "#333");
            $('#gm-custom-mapoption-sat').css("background-color", "#fff");
            $('#gm-custom-mapoption-sat').css("color", "#333");
            $('#gm-custom-mapoption-3d').css("background-color", "#fff");
            $('#gm-custom-mapoption-3d').css("color", "#333");
            $('#gm-custom-mapoption-terrain').css("background-color", "#0ae");
            $('#gm-custom-mapoption-terrain').css("color", "#fff");
            $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
            $('#gm-custom-mapoption-outdoors').css("color", "#333");
            $('#gm-custom-mapoption-streets').css("background-color", "#fff");
            $('#gm-custom-mapoption-streets').css("color", "#333");
            $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
            $('#gm-custom-mapoption-natatl').css("color", "#333");
            var mapButton = $('#gm-custom-mapbutton').html();
            if (mapButton.includes('3D Map')) {
                exit3dCamera('terrain');
            } else {
                toggleMapType('terrain');
                $('#gm-custom-mapdropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();
            }
            $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Terrain</div>');
        }
        return false
    });

    $('#gm-custom-mapoption-terrain').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapoption-terrain').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    var flag = false;
    $('#gm-custom-mapoption-outdoors').on('touchstart click', function() {
        if (!flag) {
            flag = true;
            setTimeout(function(){ flag = false; }, 500);
            $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
            $('#gm-custom-mapoption-satstreets').css("color", "#333");
            $('#gm-custom-mapoption-topo').css("background-color", "#fff");
            $('#gm-custom-mapoption-topo').css("color", "#333");
            $('#gm-custom-mapoption-sat').css("background-color", "#fff");
            $('#gm-custom-mapoption-sat').css("color", "#333");
            $('#gm-custom-mapoption-3d').css("background-color", "#fff");
            $('#gm-custom-mapoption-3d').css("color", "#333");
            $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
            $('#gm-custom-mapoption-terrain').css("color", "#333");
            $('#gm-custom-mapoption-outdoors').css("background-color", "#0ae");
            $('#gm-custom-mapoption-outdoors').css("color", "#fff");
            $('#gm-custom-mapoption-streets').css("background-color", "#fff");
            $('#gm-custom-mapoption-streets').css("color", "#333");
            $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
            $('#gm-custom-mapoption-natatl').css("color", "#333");
            var mapButton = $('#gm-custom-mapbutton').html();
            if (mapButton.includes('3D Map')) {
                exit3dCamera('outdoors');
            } else {
                toggleMapType('outdoors');
                $('#gm-custom-mapdropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();
            }
            $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">OpenTopoMap</div>');
        }
        return false
    });

    $('#gm-custom-mapoption-outdoors').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapoption-outdoors').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    var flag = false;
    $('#gm-custom-mapoption-streets').on('touchstart click', function() {
        if (!flag) {
            flag = true;
            setTimeout(function(){ flag = false; }, 500);
            $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
            $('#gm-custom-mapoption-satstreets').css("color", "#333");
            $('#gm-custom-mapoption-topo').css("background-color", "#fff");
            $('#gm-custom-mapoption-topo').css("color", "#333");
            $('#gm-custom-mapoption-sat').css("background-color", "#fff");
            $('#gm-custom-mapoption-sat').css("color", "#333");
            $('#gm-custom-mapoption-3d').css("background-color", "#fff");
            $('#gm-custom-mapoption-3d').css("color", "#333");
            $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
            $('#gm-custom-mapoption-terrain').css("color", "#333");
            $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
            $('#gm-custom-mapoption-outdoors').css("color", "#333");
            $('#gm-custom-mapoption-streets').css("background-color", "#0ae");
            $('#gm-custom-mapoption-streets').css("color", "#fff");
            $('#gm-custom-mapoption-natatl').css("background-color", "#fff");
            $('#gm-custom-mapoption-natatl').css("color", "#333");
            var mapButton = $('#gm-custom-mapbutton').html();
            if (mapButton.includes('3D Map')) {
                exit3dCamera('streets');
            } else {
                toggleMapType('streets');
                $('#gm-custom-mapdropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();
            }
            $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Streets</div>');
        }
        return false
    });

    $('#gm-custom-mapoption-streets').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapoption-streets').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    //Natural atlas stuff
    var flag = false;
    $('#gm-custom-mapoption-natatl').on('touchstart click', function() {
        if (!flag) {
            flag = true;
            setTimeout(function(){ flag = false; }, 500);
            $('#gm-custom-mapoption-satstreets').css("background-color", "#fff");
            $('#gm-custom-mapoption-satstreets').css("color", "#333");
            $('#gm-custom-mapoption-topo').css("background-color", "#fff");
            $('#gm-custom-mapoption-topo').css("color", "#333");
            $('#gm-custom-mapoption-sat').css("background-color", "#fff");
            $('#gm-custom-mapoption-sat').css("color", "#333");
            $('#gm-custom-mapoption-3d').css("background-color", "#fff");
            $('#gm-custom-mapoption-3d').css("color", "#333");
            $('#gm-custom-mapoption-terrain').css("background-color", "#fff");
            $('#gm-custom-mapoption-terrain').css("color", "#333");
            $('#gm-custom-mapoption-outdoors').css("background-color", "#fff");
            $('#gm-custom-mapoption-outdoors').css("color", "#333");
            $('#gm-custom-mapoption-streets').css("background-color", "#fff");
            $('#gm-custom-mapoption-streets').css("color", "#333");
            $('#gm-custom-mapoption-natatl').css("background-color", "#0ae");
            $('#gm-custom-mapoption-natatl').css("color", "#fff");
            var mapButton = $('#gm-custom-mapbutton').html();
            if (mapButton.includes('3D Map')) {
                exit3dCamera('natural_atlas');
            } else {
                toggleMapType('natural_atlas');
                $('#gm-custom-mapdropdown').toggle();

                $('#search-peaks-btn').prop('disabled', false);
                $('#q').prop('disabled', false);
                $('#n').prop('disabled', false);
                $(".mapboxgl-canvas-container").show();
                $('.mapboxgl-ctrl-bottom-left').show();
                $('.mapboxgl-ctrl-bottom-right').show();
            }
            $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
            $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
            $('#gm-custom-mapbutton').html('<div style="float: left;"><img style="border-radius: 4px; box-shadow: 0 0 2px 0 rgba(0,0,0,0.70); width: 34px;" src="{% static '' %}img/<EMAIL>"></div><div style="line-height: 34px; margin-left: 44px; font-size: 12px;">Natural Atlas <span style="font-size: 10px;">(US)</span>/div>');
        }
        return false
    });

    $('#gm-custom-mapoption-natatl').on('mouseenter', function() {
        $('#gm-custom-mapdropdown').show();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','0px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','0px');
    });

    $('#gm-custom-mapoption-natatl').on('mouseleave', function() {
        $('#gm-custom-mapdropdown').hide();
        $('#gm-custom-mapbutton').css('border-bottom-right-radius','8px');
        $('#gm-custom-mapbutton').css('border-bottom-left-radius','8px');
    });

    $("#peak-elevation-m").on('change', function() {
        setElevationMeters();
    });

    $("#peak-elevation-ft").on('change', function() {
        setElevationFeet();
    });

    $("#peak-prominence-m").on('change', function() {
        setProminenceMeters();
    });

    $("#peak-prominence-ft").on('change', function() {
        setProminenceFeet();
    });

    $('#peak-countries').on('change', '.add-region-select', function() {
        var new_region_id = $(this).val();
        var country_id = $(this).data('country');
        var new_region_name = $("option:selected", this).text();
        var regionHtml = '<div id="region-'+new_region_id+'"><div style="width: 100%;"></div><div style="float: left; margin-left: 15px; margin-right: 15px; height: 60px;"><i class="fa fa-level-up fa-rotate-90" style="margin-top: 20px;" aria-hidden="true"></i></div><div class="country-regions" style="background-color: #ddd; display: inline-block; height: 60px; margin-right: 20px; margin-bottom: 20px;"><span class="region-value" data-region="'+new_region_id+'" id="region-'+new_region_id+'-name" style="display: inline-block; margin-left: 20px; font-weight: 500; line-height: 60px;">'+new_region_name+'</span><span class="pull-right" style="padding-top: 17px; padding-right: 15px; padding-left: 20px; font-size: 24px; color: #ddd;"><a class="ajax-link" onclick="removeRegion('+new_region_id+','+country_id+');"><i class="fa fa-times"></i></a></span></div></div>';
        var country_region_div = '#country-'+country_id+'-regions';
        $(country_region_div).append(regionHtml);
        //remove option that was chosen
        $("option:selected", this).remove();
        $(this).val(0);
        var dropdown_hint = '#country-'+country_id+'-add-region option[value="0"]';
        $(dropdown_hint).text('add another region');
        updateRegionValues();
        $('#edit_peak').prop('disabled', false);
    });

    $('#reset-add-new-country').click( function() {
        $('#add-new-country').fadeOut(300, function() {
            $('#add-new-country-link').fadeIn(300);
        });
    });

    $('#btnAddAlternateName').click( function() {
        var new_alternate_name_index = $('#peak-alternate-names').children().length;
        var new_alternate_name = $('#peak-alternate-name').val();
        var new_alternate_name_html = '<div id="alternate-name-'+new_alternate_name_index+'"><div class="country-regions" style="background-color: #ddd; display: inline-block; height: 60px; margin-right: 20px; margin-bottom: 20px;"><span class="alternate-name-value" style="display: inline-block; margin-left: 20px; font-weight: 500; line-height: 60px;">'+new_alternate_name+'</span><span class="pull-right" style="padding-top: 17px; padding-right: 15px; padding-left: 20px; font-size: 24px; color: #ddd;"><a class="ajax-link" onclick="removeAlternateName('+new_alternate_name_index+');"><i class="fa fa-times"></i></a></span></div></div>';
        $('#peak-alternate-names').append(new_alternate_name_html);
        $('#peak-alternate-name').attr('placeholder', 'add another peak name');
        $('#peak-alternate-name').blur();
        $('#peak-alternate-name').val('');
        $('#peak-alternate-name').focus();
        $('#edit_peak').prop('disabled', false);
        updateAlternateNameValues();
    });

    $('#peak-name').on('input', function() {
        $('#edit_peak').prop('disabled', false);
    });

    $('#peak-elevation').on('input', function() {
        $('#edit_peak').prop('disabled', false);
    });

    $('#peak-prominence').on('input', function() {
        $('#edit_peak').prop('disabled', false);
    });

    $('#peak-range').on('input', function() {
        $('#edit_peak').prop('disabled', false);
    });

    $('#peak-other-changes').on('input', function() {
        $('#edit_peak').prop('disabled', false);
    });

    $('#peak-location-search').bind("enterKey",function(e){
       geoCode();
    });

    $('#edit_peak').on('click', function(e) {
        e.preventDefault();
        validateForm();
    });

    $(document).on("keydown", ":input:not(textarea)", function(event) {
        if (event.keyCode == 13) {
            return false;
        } else {
            return event.keyCode;
        }
    });

    $('#peak-location-search').keyup(function(e){
        if(e.keyCode == 13)
        {
            $(this).trigger("enterKey");
        }
    });

    //on close the thanks modal
    $('#thanks-modal').on('hidden.bs.modal', function () {
        window.location.href = '/{{ peak.slug_new_text }}/';
    });

    //country searchbox on peak edit page
    $('#peak-new-country').autocomplete({
      serviceUrl: '/api/countries/suggestions',
      minChars: 3,
      appendTo: $('#peak-new-country-div'),
      triggerSelectOnValidInput: false,
      showNoSuggestionNotice: true,
      onSelect: function (suggestion) {
        $('#peak-new-country').val('');
        var regions_arr = suggestion.regions;
        countryHtml = '<div id="country-'+suggestion.data+'"><div class="country-regions" style="background-color: #ddd; display: inline-block; height: 60px; margin-right: 20px; margin-bottom: 20px;"><span class="country-value" data-country="'+suggestion.data+'" style="display: inline-block; margin-left: 20px; font-weight: 500; line-height: 60px;">'+suggestion.value+'</span><span class="pull-right" style="padding-top: 17px; padding-right: 15px; padding-left: 20px; font-size: 24px; color: #ddd;"><a class="ajax-link remove-country-link" onclick="removeCountry('+suggestion.data+');"><i class="fas fa-times"></i></a></span></div><div id="country-'+suggestion.data+'-regions"></div><div style="width: 100%;"></div><div style="float: left; margin-left: 15px; margin-right: 15px; height: 60px;"><i class="fa fa-level-up fa-rotate-90" style="margin-top: 20px;" aria-hidden="true"></i></div><div class="edit-peak-country-region"><select class="add-region-select" id="country-'+suggestion.data+'-add-region" data-country="'+suggestion.data+'"><option value="0" selected="selected">add a region</option></select></div><div style="height: 30px;"></div></div>';
        $('#peak-countries').append(countryHtml);
        $('#add-new-country').fadeOut(300, function() {
            $('#add-new-country-link').html('if on border <a style="cursor: pointer;" onclick="addNewCountry();">add another country</a>');
            $('#add-new-country-link').fadeIn(300);
        });
        var region_dropdown = '#country-'+suggestion.data+'-add-region';
        for (var i=0, len=regions_arr.length; i < len; i++) {
          $(region_dropdown).append('<option value="'+regions_arr[i]['id']+'">'+regions_arr[i]['name']+'</option>');
        }
        updateCountryValues();
      },
      onSearchStart: function (query) {
        $('#search-country-progress').show();
      },
      onSearchComplete: function (query, suggestions) {
        $('#search-country-progress').hide();
      },
      onHide: function (container) {
        $('#peak-new-country').val('');
      }
    });

});

function setElevationMeters() {
    if ($('#peak-elevation').val() != '') {
        var elevation_feet = parseFloat($('#peak-elevation').val());
        var elevation_meters = Math.round(elevation_feet * .3048);
        $('#peak-elevation').val(elevation_meters);
        $('#edit_peak').prop('disabled', false);
    }
    $('#peak-elevation').attr('placeholder','in meters...');
}

function setElevationFeet() {
    if ($('#peak-elevation').val() != '') {
        var elevation_meters = parseFloat($('#peak-elevation').val());
        var elevation_feet = Math.round(elevation_meters / .3048);
        $('#peak-elevation').val(elevation_feet);
        $('#edit_peak').prop('disabled', false);
    }
    $('#peak-elevation').attr('placeholder','in feet...');
}

function setProminenceMeters() {
    if ($('#peak-prominence').val() != '') {
        var prominence_feet = parseFloat($('#peak-prominence').val());
        var prominence_meters = Math.round(prominence_feet * .3048);
        $('#peak-prominence').val(prominence_meters);
        $('#edit_peak').prop('disabled', false);
    }
    $('#peak-prominence').attr('placeholder','in meters...');
}

function setProminenceFeet() {
    if ($('#peak-prominence').val() != '') {
        var prominence_meters = parseFloat($('#peak-prominence').val());
        var prominence_feet = Math.round(prominence_meters / .3048);
        $('#peak-prominence').val(prominence_feet);
        $('#edit_peak').prop('disabled', false);
    }
    $('#peak-prominence').attr('placeholder', 'in feet...');
}

function validateForm() {

    //any text left un-added in input fields?
    if ($('#peak-alternate-name').val() != '') {
        var unsavedText = '';
        if ($('#txtAddActivityTag').val() != '') {
            unsavedText = unsavedText + '<p>Other peak name: <b>' + $('#peak-alternate-name').val() + '</b></p>';
        }
        $('#savetext-modal-label').html('Save your changes?');
        $('#savetext-modal-body').html('<div style="margin-top: 20px; text-align: left;"><p>You entered the following info but didn’t click the "Add" button next to it. Save this too?</p><p>' + unsavedText + '</p><p style="text-align: center;"><a onclick="saveUnaddedText();" class="btn btn-primary" style="width: 100px;">Save</a><button type="button" class="btn btn-default" data-dismiss="modal" style="margin-left: 20px; width: 100px;">Cancel</button></p></div>');
        $('#savetext-modal').modal('show');
    } else {
        //form validation
        $('#peakedit_form').formValidation({
            // I am validating Bootstrap form
            framework: 'bootstrap',
            message: '',
            // List of fields and their validation rules
            fields: {
                'peak-name': {
                    validators: {
                        notEmpty: {
                            message: 'Please enter the peak name'
                        }
                    }
                },
                'peak-elevation': {
                    validators: {
                        notEmpty: {
                            message: 'Please enter the peak elevation'
                        },
                        numeric: {
                            message: 'Elevation must be numeric'
                        }
                    }
                }
            }
        })
        .on('success.form.fv', function(e) {
            // Prevent form submission
            e.preventDefault();
            $('#edit_peak').html('<i class="fa fa-spinner fa-spin"></i>');
            $('#edit_peak').prop('disabled', true);

            // Some instances you can use are
            var $form = $(e.target),        // The form instance
                fv    = $(e.target).data('formValidation'); // FormValidation instance

            // Use Ajax to submit form data
            $.ajax({
                url: $form.attr('action'),
                type: 'POST',
                data: $form.serialize(),
                success: function(result) {
                    $('#edit_peak').html('Submit <span class="hidden-xs">changes</span>');
                    //show thanks modal
                    $('#thanks-modal').modal('show');
                }
            });
        })
        .formValidation('validate');
    }
}

function removeCountry(country_id) {

    $('.remove-country-link').hide();
    var tempCountriesToRemove = $('#peak_countries_to_remove').val();
    $('#peak_countries_to_remove').val(tempCountriesToRemove+country_id+'|');

    var country_div = '#country-'+country_id;
    $(country_div).fadeOut(500, function() {
        $(this).remove();
        if ($('#peak-countries').children().length == 0) {
            $('#add-new-country-link').html('<a style="cursor: pointer;" onclick="addNewCountry();">add a country</a>');
        } else {
            $('#add-new-country-link').html('if on border <a style="cursor: pointer;" onclick="addNewCountry();">add another country</a>');
        }
        updateCountryValues();
    });
    $('#edit_peak').prop('disabled', false);

}

function updateAlternateNameValues() {
    var temp_alternate_names = '';
    $('#peak_alternate_names').val('');
    $('.alternate-name-value').each(function(i, obj) {
        temp_alternate_names = temp_alternate_names + $(this).html() + '|';
    });
    $('#peak_alternate_names').val(temp_alternate_names);
}

function updateCountryValues() {
    var temp_cr = '';
    var countryCounter = 0;
    $('#peak_countries').val('');
    $('.country-value').each(function(i, obj) {
        temp_cr = temp_cr + $(this).data('country') + '|';
        countryCounter ++;
    });
    $('#peak_countries').val(temp_cr);
    if (countryCounter > 1) {
        $('.remove-country-link').show();
    }
}

function updateRegionValues() {
    var temp_cr = '';
    var regionCounter = 0;
    $('#peak_regions').val('');
    $('.region-value').each(function(i, obj) {
        temp_cr = temp_cr + $(this).data('region') + '|';
        regionCounter ++;
    });
    $('#peak_regions').val(temp_cr);
    if (regionCounter > 1) {
        $('.remove-region-link').show();
    }
}

function removeAlternateName(index) {

    var alternate_name_div = '#alternate-name-'+index;
    $(alternate_name_div).fadeOut(500, function() {
        $(this).remove();
        if ($('#peak-alternate-names').children().length == 0) {
            $('#peak-alternate-name').attr('placeholder', 'add an alternate name');
        } else {
            $('#peak-alternate-name').attr('placeholder', 'add another alternate name');
        }
        updateAlternateNameValues();
    });
    $('#edit_peak').prop('disabled', false);

}

function removeRegion(region_id, country_id) {

    $('.remove-region-link').hide();
    var tempRegionsToRemove = $('#peak_regions_to_remove').val();
    $('#peak_regions_to_remove').val(tempRegionsToRemove+region_id+'|');

    var region_div = '#region-'+region_id;
    $(region_div).fadeOut(500, function() {
        $(this).remove();
        var dropdown_hint = '#country-'+country_id+'-add-region option[value="0"]';
        if ($(country_regions_div).children().length == 0) {
            $(dropdown_hint).text('add a region');
        } else {
            $(dropdown_hint).text('add another region');
        }
        updateRegionValues();
    });
    //add region back to dropdown
    var dropdown = '#country-'+country_id+'-add-region';
    var dropdown_options = '#country-'+country_id+'-add-region option';
    var region_span = '#region-'+region_id+'-name';
    var region_name = $(region_span).text();
    var country_regions_div = '#country-'+country_id+'-regions';
    $(dropdown).append('<option value="'+region_id+'">'+region_name+'</option>');
    $(dropdown).html($(dropdown_options).sort(function (a, b) {
        return a.text == b.text ? 0 : a.text < b.text ? -1 : 1
    }));
    $('#edit_peak').prop('disabled', false);

}

function addNewCountry() {

    $('#add-new-country-link').fadeOut(300, function() {
        $('#add-new-country').fadeIn(300);
    });
    $('#edit_peak').prop('disabled', false);

}

function saveUnaddedText() {
    //when click the button to save unadded text
    if ($('#peak-alternate-name').val() != '') {
        $("#btnAddAlternateName").click();
    }
    $('#savetext-modal').modal('hide');
    validateForm();
}

function check_is_in(marker){
    return map.getBounds().contains(marker.getPosition());
}

function delete_out_markers(){
    if (markersArray){
        for (i in markersArray){
            if (!check_is_in(markersArray[i])){
                markersArray[i].remove();
                markersArray.splice(i,1);
            }
        }
    }
}

function deletehighest(){
    if (markersArray){
        for (i in markersArray){
            if (markersArray[i].properties.iconstyle == 'marker_icon_redgreen' || markersArray[i].properties.iconstyle == 'marker_icon_red'){
                markersArray[i].remove();
                markersArray.splice(i,1);
            }
        }
    }
}

function limit_number_of_markers(limit){
    if (markersArray.length > limit){
        for (i = markersArray.length-1; i>=limit; i--){
            markersArray[i].remove();
            markersArray.splice(i,1);
        }
    }
}

function elevation_range(data){
    if (markersArray){
        for (i = markersArray.length-1; i>=0; i--){
            var inrange = false;
            $.each(data, function(k, v){
                var latLng1 = new mapboxgl.LngLat(v.lng, v.lat);
                if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng1)){
                    inrange = true;
                }
            });
            if (!inrange){
                markersArray[i].remove();
                markersArray.splice(i,1);
            }
        }
    }
}

function delete_old_markers(data){
    if (markersArray){
        for (i = markersArray.length-1; i>=0; i--){
            var inrange = false;
            $.each(data, function(k, v){
                var latLng1 = new mapboxgl.LngLat(v.lng, v.lat);
                if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng1)){
                    inrange = true;
                }
            });
            if (!inrange){
                markersArray[i].remove();
                markersArray.splice(i,1);
            }
        }
    }
}

function fromLatLngToString(latLng) {
    return latLng.lat + ',' + latLng.lng;
}

function numberWithCommas(x) {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

function processGeocodeResult(result) {
    let data = result._typeahead.data;
    console.log("yes")
    if (data && data.features.length > 0) {
        var coords = data.features[0].center;
        var searchArr = location.split(',');

        if (!isNaN(parseFloat(searchArr[0])) && !isNaN(parseFloat(searchArr[1]))) {
            map.setZoom(15);
        } else {
            map.setZoom(10);
        }
        console.log("setting center")
        map.setCenter(coords);

        // Update latitude and longitude inputs
        document.getElementById('peak-lat').value = coords[1];
        document.getElementById('peak-lng').value = coords[0];

        // Update main marker position
        if (markersArray[0]) {
            markersArray[0].setLngLat(coords);
        }

        document.getElementById('edit_peak').disabled = false;
    }
}

function areCoordinates(str) {
    var regex = /^-?([1-8]?\d(\.\d+)?|90(\.0+)?),\s*-?(180(\.0+)?|1?[0-7]?\d(\.\d+)?)$/;
    return regex.test(str);
}

function geoCode() {
    var location = document.getElementById("peak-location-search").value;
    if (areCoordinates(location)) {
        coords = location.split(",")

        map.setCenter([coords[1], coords[0]]);
        map.setZoom(15);

        // Update latitude and longitude inputs
        document.getElementById('peak-lat').value = coords[0];
        document.getElementById('peak-lng').value = coords[1];

        // Update main marker position
        if (markersArray[0]) {
            markersArray[0].setLngLat(coords);
        }
    } else {
        let result = geocoder.query(location);
        setTimeout(function () {
            processGeocodeResult(result);
        }, 300);
    }

}

    function gmapsCallback() {
        // Empty function needed for the google maps callback
    }

</script>

{% load item_tags %}{% get_gmaps_lib %}
{% block gmaps_lib %}{% endblock %}

{% endblock %}

{% block end_body_form %}</form>{% endblock %}