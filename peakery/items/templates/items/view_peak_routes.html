{% extends "base.html" %}

{% load avatar_tags item_tags favorite_tags truncatechars verbatim %}
{% load json_filters %}
{% load humanize %}

{% block title %}{{ peak.get_peakname_title }} trails - {{ peak.get_peaklocation_title }}{% endblock %}
{% block titlemeta %}{{ peak.get_peakname_title }} trails - {{ peak.get_peaklocation_title }}{% endblock %}
{% block description %}{{ meta_description }}{% endblock %}
{#{% block image_rel %}{% thumbnail peak.thumbnail "120x120" as im %}{{ im.url }}{% endthumbnail %}{% endblock %}#}
{% block image_rel %}thumbnail peak.thumbnail 120x120{% endblock %}

{% block mapbox %}
{% include "mapbox/mapbox.html" %}
{% endblock %}

{% block meta_facebook %}
    <meta property="og:title" content="{{ peak.name }}"/>
    <meta property="og:site_name" content="peakery.com"/>
    <meta property="og:type" content="article"/>
    <meta property="og:url" content="{{ peak.get_absolute_url }}"/>
{#    <meta property="og:image" content="{% thumbnail peak.get_thumbnail "120x120" as im %}{% endthumbnail %}"/>#}
    <meta property="og:image" content="thumbnail peak.get_thumbnail 120x120"/>
    <meta property="og:description" content="{{ meta_description }}"/>
{% endblock %}

{% block js_globals %}
    var peak_id = {{ peak.id }};
    var peakObject = '{"id": "{{ peak.id }}", "name": {{ peak.name|jsonify|escape_single_quotes }}, "slug": "{{ peak.slug_new_text }}", "is_classic": "{{ peak.is_classic }}", "lat": "{{ peak.lat }}", "lng": "{{ peak.long }}", "summit_count": "{{ peak.summitlog_count }}", "your_summits": "{{ your_summits_count }}", "your_attempts": "{{ your_attempts_count }}", "challenge_count": "{{ challenge_count }}", "elevation": "{{ peak.elevation }}", "prominence": "{{ peak.prominence }}", "thumbnail_url": "{{ peak.get_thumbnail_480 }}", "region": [{% if peak.region %}{% for r in peak.region.all %}{"region_id": "{{ r.id }}", "region_name": "{{ r.name }}", "region_slug": "{{ r.get_absolute_url }}", "country_name": "{{ r.country.name }}"}{% if not forloop.last %},{% endif %}{% endfor %}{% endif %}], "country": [{% if peak.country %}{% for c in peak.country.all %}{"country_id": "{{ c.id }}", "country_name": "{{ c.name }}", "country_slug": "{{ c.get_absolute_url }}"}{% if not forloop.last %},{% endif %}{% endfor %}{% endif %}]}';
{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row hidden-xs">
        <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 65px;"><span style="font-size: 24px; font-weight: 600;">
            <div id="breadcrumbs">
                <div class="pull-right hidden-xs">
                    <a style="{{ subnav_info_style }}" class="region-header-sub-links" href="/{{ peak.slug_new_text }}/">Info</a>
                    <a style="{{ subnav_map_style }}" class="region-header-sub-links" href="/{{ peak.slug_new_text }}/map/">Map</a>
                    <a style="{{ subnav_routes_style }}" class="region-header-sub-links" href="/{{ peak.slug_new_text }}/routes/">Routes</a>
                    <a style="{{ subnav_summits_style }}" class="region-header-sub-links" href="/{{ peak.slug_new_text }}/summits/">Summits</a>
                    {% if your_summit_count != 0 %}
                        <a href="/{{ peak.slug_new_text }}/summits/#month=&member=you&route=&sort=most_recent&page=1" class="youve-climbed" style="margin-left: 0px;">(You {{ your_summit_count }}x)</a>
                    {% endif %}
                </div>
                {% if alternate_names_list %}
                <div class="ellipsis" style="line-height: 55px;overflow: hidden;">
                    <h1 class="peak-title">{{ peak.name }}<br><div class="ellipsis" style="font-size: 11px;color: #999999;margin-top: -32px;font-weight: 100;">also known as {% for a in alternate_names_list %}{{ a.name }}{% if not forloop.last %}, {% endif %}{% endfor %}</div></h1>
                </div>
                {% else %}
                <div class="ellipsis" style="line-height: 70px; overflow: hidden;">
                     <h1 class="peak-title">{{ peak.name }}</h1>
                </div>
                {% endif %}
            </div>
        </span></div>
    </div>
{% endblock %}

{% block content %}

    <style type="text/css">

        #navbar-link-peaks > a:hover {
            color: #ffffff;
        }
        @media screen and (max-width: 767px) and (min-width: 1px) {
            #log-your-climb {
                margin-left: 0px;
            }
        }
        @media screen and (max-width: 1023px) and (min-width: 768px) {
            #log-your-climb {
                margin-left: 20px;
                padding: 15px 10px;
                width: 120px;
            }
            .peak-title {
                font-size: 16px;
                font-weight: 600;
            }
        }
        @media screen and (min-width: 1024px) {
            #log-your-climb {
                margin-left: 50px;
                width: 160px;
            }
            .peak-title {
                font-size: 20px;
                font-weight: 600;
            }
            #gm-custom-mapunits {
                right: 142px;
            }
        }

        @media screen and (max-width: 767px) and (min-width: 1px) {
           #content-body {
               margin-top: 0px;
           }
           .content-pane {
               margin-top: 100px;
           }
           .peak-title {
                font-size: 12px;
                font-weight: 500;
            }
           .mobile-regions-subnav-fixed {
               margin-top: 50px;
           }
           #selectMobileRoutefilter {
               margin-top: 7px;
           }
           #route-title {
                height: 50px;
                line-height: 50px;
                background-color: #fff;
            }
           #no-routes-spacer {
               height: 100px;
           }
           .table > thead > tr > th, .table > tbody > tr > td {
                height: 70px;
               vertical-align: middle;
               font-size: 12px;
               padding: 5px;
            }
           #add-summit-route, #num-summit-routes {
                font-size: 12px;
            }
       }
       @media screen and (max-width: 1023px) and (min-width: 768px) {
           .content-pane {
               margin-top: 50px;
           }
           #selectMobileRoutefilter {
               margin-top: 7px;
           }
           #route-title {
                height: 60px;
                line-height: 60px;
                background-color: #f2f2f2;
            }
           #no-routes-spacer {
               height: 50px;
           }
           .table > thead > tr > th, .table > tbody > tr > td {
                height: 70px;
                vertical-align: middle;
               font-size: 14px;
               padding: 5px;
            }
           #add-summit-route, #num-summit-routes {
                font-size: 14px;
            }
       }
        @media screen and (min-width: 1024px) {
            .content-pane {
               margin-top: 50px;
           }
            .mobile-regions-subnav-fixed {
               margin-top: 0px;
           }
            #selectMobileRoutefilter {
               margin-top: 0px;
           }
            #peak-title {
               font-size: 22px;
           }
            #no-routes-spacer {
               height: 50px;
           }
            .table > thead > tr > th, .table > tbody > tr > td {
                height: 70px;
                vertical-align: middle;
                font-size: 14px;
                padding: 5px;
            }
            #add-summit-route, #num-summit-routes {
                font-size: 14px;
            }
        }
        #chart_div {cursor: pointer;}
        .table > thead > tr > th {
            border-bottom: none;
        }
        .table > tbody > tr > td, .table > tfoot > tr > td {
            border-top: none;
        }
        .gm-style-mtc {
            opacity: .8;
        }
        .table {
            margin-bottom: 0px;
        }

    </style>

<div class="container">
    <!-- Mobile header -->
    <div class="row sub-header-row hidden-lg hidden-md hidden-sm" style="height: 50px; position: fixed; width: 100%; z-index: 999;">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999; display: flex; justify-content: space-between;">
            <span><a style="{{ subnav_info_style }}" class="mobile-header-sub-links" href="/{{ peak.slug_new_text }}/">Info</a></span>
            {% if IS_MOBILE_APP_ACCESS == 'True' %}
            <span><a id="mobile-app-map-link" style="{{ subnav_map_style }}" class="mobile-header-sub-links">Map</a></span>
            {% else %}
            <span><a style="{{ subnav_map_style }}" class="mobile-header-sub-links" href="/{{ peak.slug_new_text }}/map/">Map</a></span>
            {% endif %}
            <span><a style="{{ subnav_summits_style }}" class="mobile-header-sub-links" href="/{{ peak.slug_new_text }}/summits/">Summits</a>
            {% if your_summit_count != 0 %}
                <a class="mobile-header-sub-links youve-climbed" href="/{{ peak.slug_new_text }}/summits/#type=you" style="margin-left: 0px;">(You {{ your_summit_count }}x)</a></span>
                {% if IS_MOBILE_APP_ACCESS == 'True' %}
                <span><a id="log-your-climb" class="mobile-header-sub-links" href="javascript: Android.logClimb(peakObject);" style="color: #00b1f2;">Log climb</a></span>
                {% else %}
                <span><a id="log-your-climb" class="mobile-header-sub-links" href="{% url "log_climb_multi_simple" %}?peak={{ peak.id }}" style="color: #00b1f2;">Log climb</a></span>
                {% endif %}
            {% else %}
                {% if user.is_authenticated %}
                    {% if IS_MOBILE_APP_ACCESS == 'True' %}
                    </span><span><a id="log-your-climb" class="mobile-header-sub-links" href="javascript: Android.logClimb(peakObject);" style="color: #00b1f2;">Log climb</a></span>
                    {% else %}
                    </span><span><a id="log-your-climb" class="mobile-header-sub-links" href="{% url "log_climb_multi_simple" %}?peak={{ peak.id }}" style="color: #00b1f2;">Log climb</a></span>
                    {% endif %}
                {% else %}
                </span><span><a id="log-your-climb" class="mobile-header-sub-links" style="color: #00b1f2; cursor: pointer;" data-toggle="modal" data-target="#accounts-login">Log climb</a></span>
                {% endif %}
            {% endif %}
        </div>
    </div>
    <!-- End mobile header -->

    <div class="row sub-header-row regions-subnav-fixed" style="height: 50px; padding-right: 0px;">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999;">
            <span id="num-summit-routes" style="float: left;"><span id="summit-count" style="color: #f24100;">{{ routes|length }}</span> summit route{{ routes|length|pluralize:"s" }}</span>
            {% if user.is_authenticated %}
            <span class="pull-right"><a id="add-summit-route" class="main-header-sub-links ajax-link" style="color: #00B1F2;" href="/peaks/{{ peak.id }}/routes/add">add a summit route</a></span>
            {% else %}
            <span class="pull-right"><a id="add-summit-route" class="main-header-sub-links ajax-link join-peakery" style="color: #00B1F2; cursor: pointer;">add a summit route</a></span>
            {% endif %}
        </div>
    </div>
    {% if routes %}
    <div id="routes-table" class="row sub-header-row content-pane" style="border-bottom: 1px solid #c0c0c0;">
        <table class="route-list-table table table-striped">
            <thead class="hidden-xs">
                <tr>
                    <th style="background-color: #00B1F2; color: #fff; border-right: 1px solid #fff;">
                        Route name
                    </th>
                    <th style="background-color: #00B1F2; color: #fff; border-right: 1px solid #fff;">
                        Distance up
                    </th>
                    <th style="background-color: #00B1F2; color: #fff; border-right: 1px solid #fff;">
                        Elevation gain
                    </th>
                    <th style="background-color: #00B1F2; color: #fff; border-right: 1px solid #fff;">
                        Time up
                    </th>
                    <th style="background-color: #00B1F2; color: #fff; border-right: 1px solid #fff;">
                        Difficulty
                    </th>
                    <th style="background-color: #00B1F2; color: #fff;">
                        Summits
                    </th>
                </tr>
            </thead>
            <tbody>
            {% for r in routes %}
                <tr class="hover-row" style="cursor: pointer;" onclick="openUrl('/{{ peak.slug_new_text }}/routes/{{ r.id }}/');">
                    <td class="hidden-sm hidden-md hidden-lg" style="border-right: 1px solid #fff; padding-left: 10px;">
                        <div><a class="route-rank-{{ forloop.counter }}" style="background-color: transparent;" href="/{{ peak.slug_new_text }}/routes/{{ r.id }}/"><h2>{{ r.name }}</h2></a></div>
                        <div style="margin-top: 5px;">{% if r.distance_to_summit %}{% mi_km_by_mi r.distance_to_summit %} up {% if r.get_elevation_gain or r.avg_time_to_summit > 0 or r.difficulty or r.summit_count > 0 %} &bull; {% endif %}{% endif %}{% if r.get_elevation_gain %}{{ r.get_elevation_gain }} elev gain{% if r.avg_time_to_summit > 0 or r.difficulty or r.summit_count > 0 %} &bull; {% endif %}{% endif %}{% if r.avg_time_to_summit > 0 %}{% sec_to_hrs_min r.avg_time_to_summit %} up{% if r.difficulty or r.summit_count > 0 %} &bull; {% endif %}{% endif %}{% if r.difficulty %}{{ r.difficulty }}{% if r.summit_count > 0 %} &bull; {% endif %}{% endif %}{% if r.summit_count > 0 %}{{ r.summit_count }} summit{{ r.summit_count|pluralize:"s" }}{% endif %}</div>
                    </td>
                    <td class="hidden-xs" style="border-right: 1px solid #fff; display: block;">
                        <div style="float: left; margin-left: 9px; height: 100%; display: inline-table;">
                            <div style="display: table-cell; vertical-align: middle;">
                                <div id="route-{{ r.id }}-circle" class="memberNumberCircle route-rank-{{ forloop.counter }}" style="margin-right: 10px;">
                                    <span>{{forloop.counter}}</span>
                                </div>
                            </div>
                        </div>
                        <div style="display: inline-table; vertical-align: middle; height: 100%; max-width: 80%;">
                            <a class="route-rank-{{ forloop.counter }}" style="background-color: transparent; display: table-cell; vertical-align: middle;" href="/{{ peak.slug_new_text }}/routes/{{ r.id }}/"><h2>{{ r.name }}</h2></a>
                        </div>
                    </td>
                    <td class="hidden-xs" style="border-right: 1px solid #fff; white-space: nowrap;">
                        {% if r.distance_to_summit %}
                        {% mi_km_by_mi r.distance_to_summit %}
                        {% endif %}
                    </td>
                    <td class="hidden-xs" style="border-right: 1px solid #fff; white-space: nowrap;">
                        {% if r.elevation_gain %}
                        {% ft_m_by_ft r.elevation_gain %}
                        {% endif %}
                    </td>
                    <td class="hidden-xs" style="border-right: 1px solid #fff; white-space: nowrap;">
                        {% if r.avg_time_to_summit > 0 %}
                        {% sec_to_hrs_min r.avg_time_to_summit %}
                        {% endif %}
                    </td>
                    <td class="hidden-xs" style="border-right: 1px solid #fff; white-space: nowrap;">
                        {% if r.difficulty %}
                        {{ r.difficulty }}
                        {% endif %}
                    </td>
                    <td class="hidden-xs" style="white-space: nowrap;">
                        {% if r.summit_count > 0 %}
                        {{ r.summit_count }} summit{{ r.summit_count|pluralize:"s" }}
                        {% endif %}
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div id="routes-table"></div>
    <div id="no-routes-spacer"></div>
    {% endif %}
    <div class="row" style="background-color: #fff; border-bottom: solid 1px #cfcfcf;">
        <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" id="peak-map-col" style="padding-right: 0px; padding-left: 0px;">
            <div id="map-canvas" style="width: 100%; height: 450px;">
                <div id="gm-custom-mapunits" class="gmnoprint gm-style-mtc" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 100px; top: 0px;">
                    <div id="gm-custom-mapunitsbutton" draggable="false" title="Change map units" style="cursor: pointer; direction: ltr; overflow: hidden; text-align: center; position: relative; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 11px; -webkit-background-clip: padding-box; background-clip: padding-box; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; font-weight: 500;">
                        <span id="gm-custom-mapunitsbutton-label-feet" class="gm-custom-mapunits-selected">Feet</span> | <span id="gm-custom-mapunitsbutton-label-meters" class="gm-custom-mapunits-unselected">Meters</span>
                    </div>
                </div>
                <div id="gm-custom-maptype" class="gmnoprint gm-style-mtc" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 104px; right: 0px; top: 0px;">
                    <div id="gm-custom-mapbutton" draggable="false" title="Change map style" style="cursor: pointer; direction: ltr; overflow: hidden; text-align: left; position: relative; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 11px; -webkit-background-clip: padding-box; background-clip: padding-box; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; font-weight: 500;">
                        <span id="gm-custom-mapbutton-label">{% if peak.is_usa_but_not_alaska %}Natural Atlas (US){% else %}OpenTopoMap{% endif %}</span><img src="https://maps.gstatic.com/mapfiles/arrow-down.png" draggable="false" style="-webkit-user-select: none; border: 0px; padding: 0px; margin: -2px 0px 0px; position: absolute; right: 6px; top: 50%; width: 7px; height: 4px;">
                    </div>
                    <div id="gm-custom-mapdropdown" style="background-color: white; z-index: -1; padding-left: 2px; padding-right: 2px; border-bottom-left-radius: 2px; border-bottom-right-radius: 2px; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; position: absolute; top: 100%; left: 0px; right: 0px; text-align: left; display: none;">
                        <div id="gm-custom-mapoption-terrain" draggable="false" title="Show street map with terrain" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            Terrain
                        </div>
                        {% if peak.is_usa_but_not_alaska %}
                        <div id="gm-custom-mapoption-natatl" draggable="false" title="Show natural atlas" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            Natural Atlas (US)
                        </div>
                        {% endif %}
                        <div id="gm-custom-mapoption-outdoors" draggable="false" title="Show open topo map" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            OpenTopoMap
                        </div>
                        {% if peak.is_usa_canada_or_new_zealand %}
                        <div id="gm-custom-mapoption-topo" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            Topo Govt (as avail)
                        </div>
                        {% endif %}
                        <div id="gm-custom-mapoption-satstreets" draggable="false" title="Show satellite imagery with street map" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            Satellite
                        </div>
                        <div id="gm-custom-mapoption-sat" draggable="false" title="Show satellite imagery" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            Satellite Topo
                        </div>
                    </div>
                </div>

                <div id="message_map_div" class="gmnoprint gm-style-mtc" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 104px; right: 0px; top: 0px; display: none;">
                    <div id="message_map" draggable="false" style="direction: ltr; overflow: hidden; text-align: left; position: relative; color: rgb(86, 86, 86); font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 11px; border-bottom-left-radius: 2px; border-top-left-radius: 2px; border-bottom-right-radius: 2px; border-top-right-radius: 2px; -webkit-background-clip: padding-box; background-clip: padding-box; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; font-weight: 500;">

                    </div>
                </div>

            </div>
            <div id="marker-tooltip" data-url="" data-index="" style="cursor: pointer; display: none; position:absolute; width: 200px; height: 150px; background: rgba(0, 0, 0, 0.3); margin: 15px;"></div>
        </div>
    </div>

    <div class="row hidden-lg hidden-md hidden-sm">
        <div style="height: 180px;"></div>
    </div>
    <div class="row hidden-xs">
        <div style="height: 56px;"></div>
    </div>

</div>

<script type="text/javascript">

    var map;
    var marker;
    var topo;
    var outdoors;
    var center = null;
    var map_bounds;
    var latLng = null;
    var init = false;
    var photos_displayed = 0;
    var photos_page = 1;
    var photos = [];
    var route_bounds;
    var pageX, pageY, mapX, mapY;
    var iconstyle;
    var startMarkersArray = [];

    function numberWithCommas(x) {
        return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    function openUrl(url) {
        window.location.href = url;
    }

    function set_route_gpx(gpx_url, gpx_start_index, gpx_end_index, route_color, route_name, route_id, route_summits, fit_bounds) {
        var points = [];
        var linePoints = [];
        $.ajax({
            type: "GET",
            url: gpx_url,
            dataType: "xml",
            success: function(xml) {
            $(xml).find("trkpt").each(function() {
                var lat = $(this).attr("lat");
                var lon = $(this).attr("lon");
                var p = new mapboxgl.LngLat(lon, lat);
                points.push(p);
                linePoints.push([p.lng, p.lat]);
            });
            $(xml).find("rtept").each(function() {
                var lat = $(this).attr("lat");
                var lon = $(this).attr("lon");
                var p = new mapboxgl.LngLat(lon, lat);
                points.push(p);
                linePoints.push([p.lng, p.lat]);
            });

            var startStep = gpx_start_index;
            var endStep = gpx_end_index;
            var totalPoints = points.length;
            var pointStep = totalPoints / 500;
            var newPoints = [];
            var addedStartMarker = false;
            for (var i = 0; i < totalPoints; i++) {
                if (i >= (pointStep * startStep) && i <= (pointStep * endStep)) {
                    //add start marker
                    if (!addedStartMarker) {
                        var alreadyAdded = false;
                        for (j = startMarkersArray.length-1; j>=0; j--){
                            if (startMarkersArray[j].properties.routeid==route_id) {
                                alreadyAdded = true;
                            }
                        }
                        if (!alreadyAdded) {
                            //build tooltip string
                            var tooltip_html, tooltip_width, tooltip_height, marker_top, marker_left;
                            tooltip_html = '<div class="map-tooltip-info"><div class="map-tooltip-peak-name ellipsis">' + route_name + '</div><div class="map-tooltip-peak-stats">' + route_summits + ' summit' + ((route_summits != 1) ? 's' : '') + '</div></div>';
                            tooltip_width = 220;
                            tooltip_height = 50;
                            var tooltip_url = '/{{ peak.slug_new_text }}/routes/' + route_id + '/';

                            var el = document.createElement('div');
                            iconstyle = 'route_marker_' + route_color;
                            el.className = iconstyle;

                            el.addEventListener('click', function (e) {
                                if (isTouchDevice()) {
                                    //check data opened
                                    if ($('#marker-tooltip').data('opened') == 'true') {
                                        location = '/{{ peak.slug_new_text }}/routes/' + marker.properties.route_id + '/';
                                    }
                                    //set data opened
                                    $('#marker-tooltip').data('opened', 'true');
                                } else {
                                    location = '/{{ peak.slug_new_text }}/routes/' + marker.properties.route_id + '/';
                                }
                            });

                            el.addEventListener('mouseover', function (e) {

                                var bottom = $('#map-canvas').height();
                                var right = $('#map-canvas').width();

                                if (mapY < (bottom / 2)) {
                                    marker_top = mapY;
                                } else {
                                    marker_top = mapY - tooltip_height - 45;
                                }

                                if (mapX < (right / 3)) {
                                    marker_left = mapX;
                                } else if (mapX >= (right / 3) && mapX < ((right / 3) * 2)) {
                                    marker_left = mapX - (tooltip_width / 2) - 15;
                                } else {
                                    marker_left = mapX - tooltip_width - 30;
                                }

                                $('#marker-tooltip').html(marker.properties.tooltipContent).css({
                                    'left': marker_left,
                                    'top': marker_top,
                                    'width': tooltip_width,
                                    'height': tooltip_height
                                }).show();
                                $('#marker-tooltip').data('url', marker.properties.tooltipUrl);

                            });

                            el.addEventListener('mouseout', function (e) {
                                $('#marker-tooltip').hide();
                                //set data not opened
                                $('#marker-tooltip').data('opened', 'false');
                            });

                            var marker = new mapboxgl.Marker(el)
                                    .setLngLat(points[i])
                                    .setOffset([-5, -10])
                                    .setDraggable(false)
                                    .addTo(map);

                            marker.properties = {};
                            marker.properties.tooltipContent = tooltip_html;
                            marker.properties.tooltipUrl = tooltip_url;
                            marker.properties.iconstyle = iconstyle;
                            marker.properties.routeid = route_id;

                            startMarkersArray.push(marker);

                            addedStartMarker = true;
                        }
                    }
                    newPoints.push(points[i]);
                    route_bounds.extend(points[i]);
                }
            }
            var strokeColor = '#'+route_color;
            var routeLayerId = 'route_'+route_id;

            if (map.getLayer(routeLayerId)) {
                map.removeLayer(routeLayerId);
            }
            map.addLayer({
            "id": routeLayerId,
            "type": "line",
            "source": {
                "type": "geojson",
                "data": {
                    "type": "Feature",
                    "properties": {},
                    "geometry": {
                        "type": "LineString",
                        "coordinates": linePoints
                    }
                }
            },
            "layout": {
                "line-join": "round",
                "line-cap": "round"
            },
            "paint": {
                "line-color": strokeColor,
                "line-width": 4
            }
            });

            if (fit_bounds) {
                // fit bounds to track
                map.fitBounds(route_bounds, {padding: 50});
            }

            }
        });

    }

    function loadPeaks(peak_id) {

        //get map bounds
        var bounds = map.getBounds();

        var counter = 0;
        var strTemp = '';

        var params = '';
        params = params + '&q=';
        params = params + '&n=';
        params = params + '&elev_min=0';
        params = params + '&elev_max=29500';
        params = params + '&prom_min=0';
        params = params + '&prom_max=29500';
        params = params + '&summits_min=0';
        params = params + '&summits_max=500';
        params = params + '&difficulty_min=1';
        params = params + '&difficulty_max=5';
        params = params + '&lat=';
        params = params + '&lng=';
        params = params + '&bounds=' + bounds.getSouthWest().lat + ',' + bounds.getSouthWest().lng + ',' + bounds.getNorthEast().lat + ',' + bounds.getNorthEast().lng;

        //update hidden parameters
        map_bounds = bounds.getSouthWest().lat + ',' + bounds.getSouthWest().lng + ',' + bounds.getNorthEast().lat + ',' + bounds.getNorthEast().lng;

        var LatLngList = [];

        if (params.length > 0) params = '?' + params.slice(-1*(params.length-1));
        var byRegion = false;
        var totalPeaks = 0;
        $.getJSON('{% url "peaks_map" %}' + params , function(data) {
            $.each( data, function( key, val ) {
                var currentRequest = true;
                if (key=='parameters') {
                    $.each( val, function( parameterkey, parameterval ) {
                        if (parameterval.bounds != map_bounds) currentRequest = false;
                    });
                }

                if (!currentRequest) {
                    return false;
                }

                if (key=='peaks') {

                    var havePeaks = false;

                    $.each( val, function( peakkey, peakval ) {

                        if (!havePeaks) {

                            //first time through, delete highest peak marker and remove any markers not on map
                            deletehighest();
                            //delete markers out of margins
                            delete_old_markers(val);

                        }

                        havePeaks = true;

                        //build country string
                        var country = '';
                        $.each( peakval.country, function( countrykey, countryval ) {
                            country = country + '<div><a href="' + countryval.country_slug + '/">' + countryval.country_name + '</a></div>';
                        });
                        if (country == '') {
                            country = 'Unknown Country';
                        }

                        //build region string
                        var region = '';
                        var mobile_region = '';
                        var region_bull_class = 'hidden-lg hidden-md hidden-sm hidden-xs';
                        $.each( peakval.region, function( regionkey, regionval ) {
                            region_bull_class = '';
                            region = region + '<div style="margin-bottom: 5px;"><a class="truncate" href="' + regionval.region_slug + '/">' + regionval.region_name + ', ' + regionval.country_name + '</a></div>';
                            mobile_region = '<div style="margin-bottom: 5px;"><a class="truncate" href="' + regionval.region_slug + '/">' + regionval.country_name + '</a></div>';
                        });
                        if (region == '') {
                            region = country;
                            mobile_region = country;
                        }

                        //build challenges string
                        var challenges = '';
                        var challenge_count = peakval.challenge_count;
                        if (challenge_count > 0) {
                            challenges = ' &bull; <span>' + challenge_count + ' <i class="fa fa-trophy"></i></span>';
                        }

                        //build summits string
                        var summits, tooltip_your_summits;
                        if (peakval.summit_count > 0) {
                            summits = '<a href="/' + peakval.slug + '/summits/">' + peakval.summit_count + '</a>';
                        } else {
                            summits = '<a href="/' + peakval.slug + '/summits/">0</a><br /><a href="/' + peakval.slug + '/">Be the first!</a>';
                        }
                        if (peakval.your_summits > 0) {
                            summits = summits + '<br /><span style="color: #3fc663; font-weight: 500;">' + peakval.your_summits + 'x by you</span>';
                            tooltip_your_summits = '&nbsp;<span style="color: #3fc663; font-weight: 500;">(You ' + peakval.your_summits + 'x)</span>';
                        } else {
                            tooltip_your_summits = '';
                        }

                        //build tooltip string
                        var tooltip_html, tooltip_width, tooltip_height, marker_top, marker_left;
                        if (peakval.thumbnail_url.indexOf('default.png.350x245_q95_crop.jpg') < 0) {
                            tooltip_html = '<img class="map-tooltip-img hover-photos-hover" src="{{ MEDIA_URL }}' + peakval.thumbnail_url + '"><div class="map-tooltip-info"><div class="map-tooltip-peak-name ellipsis">' + peakval.name + '</div><div class="map-tooltip-peak-stats"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + tooltip_your_summits + challenges + '</div></div>';
                            tooltip_width = 220;
                            tooltip_height = 165;
                        } else {
                            tooltip_html = '<div class="map-tooltip-info"><div class="map-tooltip-peak-name ellipsis">' + peakval.name + '</div><div class="map-tooltip-peak-stats"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + tooltip_your_summits + challenges + '</div></div>';
                            tooltip_width = 220;
                            tooltip_height = 50;
                        }
                        var tooltip_url = '/' + peakval.slug;

                        var latLng = new mapboxgl.LngLat(peakval.lng, peakval.lat);

                        if (peakval.id == '{{ peak.id }}') {
                            //peak page peak gets special icon
                            iconstyle = 'marker_icon_peak';
                        } else if (counter == 0) {
                            //highest peak gets red icon
                            iconstyle = 'marker_icon_red';
                        } else if (peakval.your_summits > 0) {
                            //if you have summited then green icon
                            iconstyle = 'marker_icon_green';
                        } else if (peakval.your_attempts > 0) {
                            //if you have attempted then yellow icon
                            iconstyle = 'marker_icon_yellow';
                        } else {
                            iconstyle = 'marker_icon';
                        }

                        if (isTouchDevice()) {
                            var is_draggable = false;
                        } else {
                            {% if request.user.is_staff %}
                            var is_draggable = true;
                            {% else %}
                            var is_draggable = false;
                            {% endif %}
                        }

                        //check if already exist so don't put again
                        var exists = false;
                        for (i = markersArray.length-1; i>=0; i--){
                            if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng)){
                                exists = true ;
                                //if the highest is in the actual viewport, not as the highest, delete it
                                if (iconstyle=='marker_icon_red' || iconstyle=='marker_icon_redgreen' || iconstyle=='marker_icon_green') {
                                    markersArray[i].remove();
                                    markersArray.splice(i,1);
                                    exists = false;
                                }
                            }
                        }

                        //if we are only showing one peak_id, assume other peaks already exist so don't show them
                        if (peak_id == null) {
                            //do nothing for now
                        } else {
                            if (peakval.id != peak_id) {
                                exists = true ;
                            }
                        }

                        if (!exists) {
                            var latLng = [peakval.lng, peakval.lat];
                            //add marker
                            //create an HTML element for the marker
                            var el = document.createElement('div');
                            el.className = iconstyle;

                            el.addEventListener('click', function(e) {
                                if (isTouchDevice()) {

                                    var bottom = $('#map-canvas').height();
                                    var right = $('#map-canvas').width();

                                    if (mapY < (bottom/2)) {
                                        marker_top = mapY;
                                    } else {
                                        marker_top = mapY - tooltip_height - 45;
                                    }

                                    if (mapX < (right/3)) {
                                        marker_left = mapX;
                                    } else if (mapX >= (right/3) && mapX < ((right/3)*2)) {
                                        marker_left = mapX - (tooltip_width/2) - 15;
                                    } else {
                                        marker_left = mapX - tooltip_width - 30;
                                    }

                                    $('#marker-tooltip').html(marker.properties.tooltipContent).css({
                                        'left': marker_left,
                                        'top': marker_top,
                                        'width': tooltip_width,
                                        'height': tooltip_height
                                    }).show();
                                    $('#marker-tooltip').data('url',marker.properties.tooltipUrl);
                                } else {
                                    //console.log(peakval.slug);
                                    location = '/' + peakval.slug + '/';;
                                }
                            });

                            el.addEventListener('mouseover', function(e) {

                                var bottom = $('#map-canvas').height();
                                var right = $('#map-canvas').width();

                                if (mapY < (bottom/2)) {
                                    marker_top = mapY;
                                } else {
                                    marker_top = mapY - tooltip_height - 45;
                                }

                                if (mapX < (right/3)) {
                                    marker_left = mapX;
                                } else if (mapX >= (right/3) && mapX < ((right/3)*2)) {
                                    marker_left = mapX - (tooltip_width/2) - 15;
                                } else {
                                    marker_left = mapX - tooltip_width - 30;
                                }

                                $('#marker-tooltip').html(marker.properties.tooltipContent).css({
                                    'left': marker_left,
                                    'top': marker_top,
                                    'width': tooltip_width,
                                    'height': tooltip_height
                                }).show();
                                $('#marker-tooltip').data('url',marker.properties.tooltipUrl);

                            });

                            el.addEventListener('mouseout', function(e) {
                                if (isTouchDevice()) {
                                    //$('#marker-tooltip').hide();
                                } else {
                                    $('#marker-tooltip').hide();
                                }
                            });

                            var marker = new mapboxgl.Marker(el)
                                .setLngLat(latLng)
                                .setOffset([-5, -10])
                                .setDraggable(is_draggable);

                            marker.properties = {};
                            marker.properties.tooltipContent = tooltip_html;
                            marker.properties.tooltipUrl = tooltip_url;
                            marker.properties.iconstyle = iconstyle;
                            marker.properties.peakid = peakval.id;

                            {% if request.user.is_staff %}
                                //editing functions
                                marker.on('dragstart', function(e) {
                                    $('#marker-tooltip').hide();
                                });
                                marker.on('dragend', function(e) {
                                    var point = marker.getLngLat();
                                    fix_item_location(peakval.id,point);
                                });

                                el.addEventListener('contextmenu', function(e) {
                                    if (confirm("Are you sure you want to delete this peak?")){
                                        delete_peak_from_map(peakval.id);
                                        $('#marker-tooltip').hide();
                                        for (i = markersArray.length-1; i>=0; i--){
                                            if (markersArray[i].properties.peakid==peakval.id) {
                                                markersArray[i].remove();
                                                markersArray.splice(i,1);
                                            }
                                        }
                                    }
                                });
                            {% endif %}

                            markersArray.push(marker);
                            LatLngList.push(latLng);

                        }

                        counter ++;
                    });

                    //add markers to map in reverse order so highest on top
                    for (var i = markersArray.length - 1; i >= 0; --i) {
                        markersArray[i].addTo(map);
                    }

                    if (!havePeaks) {
                        //didn't have any peaks, so remove all markers
                        delete_old_markers(val);
                    }
                }
            });
        });

        init = true;

    }

    function initPeaks() {

        loadPeaks();

    }

    var markersArray = [];

    function initialize() {

        var mapDiv = document.getElementById('map-canvas');
        var latLng = new mapboxgl.LngLat({{ peak.long }}, {{ peak.lat }});
        route_bounds = new mapboxgl.LngLatBounds();
        var LatLngList = [];

        {% if peak.is_usa_but_not_alaska %}
        mapStyle = mapStyleNaturalAtlas;
        {% else %}
        mapStyle = mapStyleOutdoors;
        {% endif %}

        var initZoom = 12;

        if (isTouchDevice()) {
            map = new mapboxgl.Map({
                container: mapDiv, // HTML container id
                style: mapStyle, // style URL
                center: latLng, // starting position as [lng, lat]
                zoom: initZoom
            });
        } else {
            map = new mapboxgl.Map({
                container: mapDiv, // HTML container id
                style: mapStyle, // style URL
                center: latLng, // starting position as [lng, lat]
                zoom: initZoom
            });
            scale = new mapboxgl.ScaleControl({maxWidth: 120, unit: 'imperial'});
            map.addControl(scale, 'bottom-right');
            var nav = new mapboxgl.NavigationControl({showCompass: false});
            map.addControl(nav, 'bottom-right');
            // disable map rotation using right click + drag
            map.dragRotate.disable();
            // disable map rotation using touch rotation gesture
            map.touchZoomRotate.disableRotation();
        }

        function calculateCenter() {
          center = map.getCenter();
        }

        map.on('resize', function(e) {
          map.setCenter(center);
        });

        map.on('load', function () {
            initPeaks();
            calculateCenter();
            var mapUnits = readCookie('map_units');
            if (mapUnits != '') {
                toggleMapUnits(mapUnits);
            }
            {% for r in routes %}
            {% if r.gpx_file %}
            route_color = $('#route-{{ r.id }}-circle').css('background-color').replace('#','');
            set_route_gpx('{{ S3_MEDIA_URL }}{{ r.gpx_file }}', {{ r.gpx_start_index }}, {{ r.gpx_end_index }}, route_color, '{{ r.name }}', '{{ r.id }}', '{{ r.summit_count }}', true);
            {% endif %}
            {% endfor %}
            setMapControls();
        });

        map.on('dragstart', function(e) {
            if (isTouchDevice()) {
                $('#marker-tooltip').hide();
            } else {
                $('#marker-tooltip').hide();
            }
        });

        map.on('moveend', function () {
            initPeaks();
            calculateCenter();
        });

    }

    function setMapControls() {
        var check = checkIfMapboxStyleIsLoaded();
        if (!check) {
            // It's not safe to manipulate layers yet, so wait 200ms and then check again
            setTimeout(function() {
                setMapControls();
            }, 200);
            return;
        }
        // Whew, now it's safe to manipulate layers!
        var mapUnits = readCookie('map_units');
        if (mapUnits == 'meters') {
            toggleMapUnits(mapUnits);
        }
    }

    function addExtraMapLayers() {
        var check = checkIfMapboxStyleIsLoaded();
        if (!check) {
            // It's not safe to manipulate layers yet, so wait 200ms and then check again
            setTimeout(function() {
                addExtraMapLayers();
            }, 200);
            return;
        }
        // Whew, now it's safe to manipulate layers!
        addPolylines();
    }

    function addPolylines() {
        var route_color = '';
        {% for r in routes %}
        {% if r.gpx_file %}
        route_color = $('#route-{{ r.id }}-circle').css('background-color').replace('#','');
        set_route_gpx('{{ S3_MEDIA_URL }}{{ r.gpx_file }}', {{ r.gpx_start_index }}, {{ r.gpx_end_index }}, route_color, '{{ r.name }}', '{{ r.id }}', '{{ r.summit_count }}', false);
        {% endif %}
        {% endfor %}
    }

    $(document).ready(function(){

        $("#mobile-app-map-link").on('click', function() {
            //Android.peakCoordinates('{{ peak.lat }},{{ peak.long }}');
            //Android.peakId('{{ peak.id }}');
            Android.peakInfo(peakObject);
        });

        //add log this climb option to log climb dropdown
        $('#log-climb-log-manually .navbar-primary .log-climb-dropdown-label-div').html('Log another peak');
        $('#log-climb-log-this-peak .navbar-primary').attr('href','/peaks/log_climb/?peak={{ peak.id }}');
        $('#log-climb-log-this-peak .navbar-primary .log-climb-dropdown-label-div').html('Log {{ peak.name }}');
        $('#log-climb-log-this-peak').show();

        initialize();
        resize_map();

        $('#map-canvas').mousemove(function(e) {
            var offset = $(this).offset();
            pageX = e.pageX;
            pageY = e.pageY;
            mapX = (e.pageX - offset.left);
            mapY = (e.pageY - offset.top);
        });

        $('#map-canvas').on('touchstart', function(e) {
            var offset = $(this).offset();
            pageX = e.originalEvent.touches[0].pageX;
            pageY = e.originalEvent.touches[0].pageY;
            mapX = (pageX - offset.left);
            mapY = (pageY - offset.top);
        });

        //switch map units
        $("#gm-custom-mapunits").click(function(){
            if ($('#gm-custom-mapunitsbutton-label-meters').hasClass('gm-custom-mapunits-selected')) {
                toggleMapUnits('feet');
                scale.setUnit('imperial');
            } else {
                toggleMapUnits('meters');
                scale.setUnit('metric');
            }
        });

        //Custom Google Map type stuff

        $('#gm-custom-mapbutton').on('mouseenter', function(){
           $('#gm-custom-mapdropdown').show();
        });

        $('#gm-custom-mapbutton').on('mouseleave', function(){
           $('#gm-custom-mapdropdown').hide();
        });

        $('#gm-custom-mapbutton').on('touchstart', function() {
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-satstreets').on('click', function() {
            toggleMapType('satellite');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-satstreets').on('touchstart', function() {
            toggleMapType('satellite');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-satstreets').on('mouseenter', function() {
            $('#gm-custom-mapdropdown').show();
        });

        $('#gm-custom-mapoption-satstreets').on('mouseleave', function() {
            $('#gm-custom-mapdropdown').hide();
        });

        $('#gm-custom-mapoption-topo').on('click', function() {
            toggleMapType('caltopo');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-topo').on('touchstart', function() {
            toggleMapType('caltopo');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-topo').on('mouseenter', function() {
            $('#gm-custom-mapdropdown').show();
        });

        $('#gm-custom-mapoption-topo').on('mouseleave', function() {
            $('#gm-custom-mapdropdown').hide();
        });

        $('#gm-custom-mapoption-sat').on('click', function() {
            toggleMapType('sat_topo');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-sat').on('touchstart', function() {
            toggleMapType('sat_topo');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-sat').on('mouseenter', function() {
            $('#gm-custom-mapdropdown').show();
        });

        $('#gm-custom-mapoption-sat').on('mouseleave', function() {
            $('#gm-custom-mapdropdown').hide();
        });

        $('#gm-custom-mapoption-terrain').on('click', function() {
            toggleMapType('terrain');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-terrain').on('touchstart', function() {
            toggleMapType('terrain');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-terrain').on('mouseenter', function() {
            $('#gm-custom-mapdropdown').show();
        });

        $('#gm-custom-mapoption-terrain').on('mouseleave', function() {
            $('#gm-custom-mapdropdown').hide();
        });

        $('#gm-custom-mapoption-outdoors').on('click', function() {
            toggleMapType('outdoors');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-outdoors').on('touchstart', function() {
            toggleMapType('outdoors');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-outdoors').on('mouseenter', function() {
            $('#gm-custom-mapdropdown').show();
        });

        $('#gm-custom-mapoption-outdoors').on('mouseleave', function() {
            $('#gm-custom-mapdropdown').hide();
        });

        //Natural atlas stuff
        $('#gm-custom-mapoption-natatl').on('click', function() {
            toggleMapType('natural_atlas');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-natatl').on('touchstart', function() {
            toggleMapType('natural_atlas');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-natatl').on('mouseenter', function() {
            $('#gm-custom-mapdropdown').show();
        });

        $('#gm-custom-mapoption-natatl').on('mouseleave', function() {
            $('#gm-custom-mapdropdown').hide();
        });

        $(window).resize(function() {
            resize_map();
        });

    });

    $.cssHooks.backgroundColor = {
        get: function(elem) {
            if (elem.currentStyle)
                var bg = elem.currentStyle["backgroundColor"];
            else if (window.getComputedStyle)
                var bg = document.defaultView.getComputedStyle(elem,
                    null).getPropertyValue("background-color");
            if (bg.search("rgb") == -1)
                return bg;
            else {
                bg = bg.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
                function hex(x) {
                    return ("0" + parseInt(x).toString(16)).slice(-2);
                }
                return "#" + hex(bg[1]) + hex(bg[2]) + hex(bg[3]);
            }
        }
    }

    function resize_map() {
        var tableHeight = $('#routes-table').height();
        var window_height = $(window).height();
        var window_width = $(window).width();
        var header_footer = 230;
        var padding = 0;
        if (window_width < 768) {
            header_footer = 210;
        }
        if (window_width < 1280) {
            padding = 7;
        }

        if (window_height - header_footer - tableHeight > 600) {
            var map_height = window_height - header_footer;
            $("#map-canvas").height(map_height + padding + 70);
            map.resize();
        }
    }

    function fix_item_location(id,point){
        $.post('{% url "fix_item_location" %}', {id:id, lat:point.lat(), long:point.lng()},
                function(data) {
                    var output = $("#message_map");
                    var output_div = $("#message_map_div");
                    output.html(data);
                    output_div.show();
                    setTimeout(function() {
                        $("#message_map_div").hide();
                    }, 10000)
                }
        );
    }

    function delete_peak_from_map (id){
        $.post('{% url "delete_peak_from_map" %}', {id:id},
                function(data) {
                    var output = $("#message_map");
                    var output_div = $("#message_map_div");
                    output.html(data);
                    output_div.show();
                    setTimeout(function() {
                        $("#message_map_div").hide();
                    }, 10000)
                }
        );
    }

    function check_is_in(marker){
        return map.getBounds().contains(marker.getPosition());
    }

    function delete_out_markers(){
        if (markersArray){
            for (i in markersArray){
                if (!check_is_in(markersArray[i])){
                    markersArray[i].remove();
                    markersArray.splice(i,1);
                }
            }
        }
    }

    function deletehighest(){
        if (markersArray){
            for (i in markersArray){
                if (markersArray[i].properties.iconstyle == 'marker_icon_redgreen' || markersArray[i].properties.iconstyle == 'marker_icon_red'){
                    markersArray[i].remove();
                    markersArray.splice(i,1);
                }
            }
        }
    }

    function limit_number_of_markers(limit){
        if (markersArray.length > limit){
            for (i = markersArray.length-1; i>=limit; i--){
                markersArray[i].remove();
                markersArray.splice(i,1);
            }
        }
    }

    function elevation_range(data){
        if (markersArray){
            for (i = markersArray.length-1; i>=0; i--){
                var inrange = false;
                $.each(data, function(k, v){
                    var latLng1 = new mapboxgl.LngLat(v.lng, v.lat);
                    if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng1)){
                        inrange = true;
                    }
                });
                if (!inrange){
                    markersArray[i].remove();
                    markersArray.splice(i,1);
                }
            }
        }
    }

    function delete_old_markers(data){
        if (markersArray){
            for (i = markersArray.length-1; i>=0; i--){
                var inrange = false;
                $.each(data, function(k, v){
                    var latLng1 = new mapboxgl.LngLat(v.lng, v.lat);
                    if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng1)){
                        inrange = true;
                    }
                });
                if (!inrange){
                    markersArray[i].remove();
                    markersArray.splice(i,1);
                }
            }
        }
    }

    function fromLatLngToString(latLng) {
        return latLng.lat + ',' + latLng.lng;
    }

</script>

{% endblock %}