from items.models import Item
from django.core.management.base import BaseCommand
from django.db.models import Q

class Command(BaseCommand):
    args = ''
    help = ''

    def handle(self, *args, **options):
        self.stdout.write('Resolving...\n')

        items = Item.objects.filter(~Q(region = None), country=None)
        for i in items:
            i.country = i.region.country
            i.country
            i.save()
        print 'resolved %i countries' % len(items)

        self.stdout.write('Done\n')
