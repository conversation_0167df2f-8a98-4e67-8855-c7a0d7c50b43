from tempitems.models import TempItem
from peakery.cities.models import Country, Region
from django.core.management.base import BaseCommand
from django.db import connection
import sys
from peakery.utils.utils import dictfetchall

class Command(BaseCommand):
    help = 'Geocodes OSM items by country code.'
    args = '<country_code ...>'

    def handle(self, *args, **options):
        reload(sys)
        sys.setdefaultencoding('utf8')
        for country_code in args:

            country_code = country_code.upper()
            self.stdout.write('Geocoding OSM Items for '+country_code+'...\n')

            import googlemaps
            import json
            from django.contrib.gis.geos import Point

            gmaps = googlemaps.Client(key="AIzaSyAjV7dkmG1WYHeHE0EnQvOV6YnPWpRxIzg")

            sql = "select a.osm_id, a.name, a.lat, a.long " + \
                "from items_item_osm a where a.item_id is null and a.active = true and a.country_code = %s "

            cursor = connection.cursor()
            cursor.execute(sql, [country_code])
            peaks = dictfetchall(cursor)

            total_peak_count = len(peaks)

            processed = 1
            for p in peaks:
                self.stdout.write('Processing: %s (ID: %s - %s of %s)' % (p['name'], p['osm_id'], processed, total_peak_count))
                point = Point(float(p['long']),float(p['lat']))
                result = gmaps.reverse_geocode(("%s, %s" % (point.y, point.x)))
                for r in result:
                    region_name = None
                    country_name = None
                    region_match = False
                    if r['geometry']['location_type'] == "GEOMETRIC_CENTER" or r['geometry']['location_type'] == "APPROXIMATE":
                        for a in r['address_components']:
                            for t in a['types']:
                                if t == "country":
                                    if country_name != a['short_name']:
                                        country_name = a['short_name']
                                        countries = Country.objects.filter(code=country_name)
                                        #delete existing countries
                                        sql = "delete from items_item_osm_country where osm_id = %s "
                                        cursor = connection.cursor()
                                        cursor.execute(sql, [p['osm_id']])
                                        for country in countries:
                                            #add new country
                                            sql = "insert into items_item_osm_country (osm_id, country_id) values (%s, %s)"
                                            cursor = connection.cursor()
                                            cursor.execute(sql, [p['osm_id'], country.id])
                        for a in r['address_components']:
                            for t in a['types']:
                                if t == "administrative_area_level_1":
                                    if region_name != a['long_name']:
                                        region_name = a['long_name']
                                        regions = Region.objects.filter(name=region_name, country_id=country.id)
                                        if regions:
                                            region_match = True
                                            #delete existing regions
                                            sql = "delete from items_item_osm_region where osm_id = %s "
                                            cursor = connection.cursor()
                                            cursor.execute(sql, [p['osm_id']])
                                            for region in regions:
                                                #add new region
                                                sql = "insert into items_item_osm_region (osm_id, region_id) values (%s, %s)"
                                                cursor = connection.cursor()
                                                cursor.execute(sql, [p['osm_id'], region.id])
                                        if not region_match:
                                            #try matching "google region name"
                                            regions = Region.objects.filter(google_region_name=region_name, country_id=country.id)
                                            if regions:
                                                region_match = True
                                                #delete existing regions
                                                sql = "delete from items_item_osm_region where osm_id = %s "
                                                cursor = connection.cursor()
                                                cursor.execute(sql, [p['osm_id']])
                                                for region in regions:
                                                    #add new region
                                                    sql = "insert into items_item_osm_region (osm_id, region_id) values (%s, %s)"
                                                    cursor = connection.cursor()
                                                    cursor.execute(sql, [p['osm_id'], region.id])
                                        #log geocoder response
                                        sql = "delete from items_item_osm_geocode where osm_id = %s"
                                        cursor = connection.cursor()
                                        cursor.execute(sql, [p['osm_id']])
                                        sql = "insert into items_item_osm_geocode (osm_id, region_name, region_match, geocoder_response, country_name) values (%s, %s, %s, %s, %s)"
                                        cursor = connection.cursor()
                                        cursor.execute(sql, [p['osm_id'], region_name, region_match, json.dumps(result), country_name])
                processed += 1

        self.stdout.write('END\n')
