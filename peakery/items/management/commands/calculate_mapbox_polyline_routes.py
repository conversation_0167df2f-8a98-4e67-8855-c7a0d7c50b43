from django.core.management.base import BaseCommand
from django.db import connection
from concurrent.futures import ThreadPoolExecutor, as_completed

from peakery.main.services import mapbox_thumbnail_service
from peakery.utils.utils import dictfetchall


class Command(BaseCommand):
    args = ''
    help = 'Update Route encoded polyline field with mapbox compatible polyline'

    def handle(self, *args, **options):
        print("Starting calculate mapbox polyline for routes task")

        sql = """
            SELECT 
                id,
                gpx_file
            FROM items_peakroute
            WHERE 
                status = 1
                and gpx_file IS NOT NULL
                and encoded_polyline IS NULL
                and created > now() - interval '2 day'
        """

        with connection.cursor() as cursor:
            cursor.execute(sql)
            routes = dictfetchall(cursor)

        routes_count = len(routes)

        if routes_count > 0:
            print("Found", routes_count, "route(s) without encoded_polyline")
            self.process_routes_in_threads(routes)

        print("Finished calculate mapbox polyline for routes task")


    def process_routes_in_threads(self, routes):
        def process_route(route):
            rid = route["id"]
            print("Processing", rid)
            encoded_mapbox_polyline = mapbox_thumbnail_service.get_encoded_polyline_for_mapbox(route["gpx_file"])
            if encoded_mapbox_polyline:
                update_sql = "update items_peakroute set encoded_polyline = %s where id = %s"
                with connection.cursor() as cursor:
                    cursor.execute(update_sql, [encoded_mapbox_polyline, rid])
            else:
                print("Couldn't encode polyline for route with id", rid)

        # Using ThreadPoolExecutor to process routes in threads
        with ThreadPoolExecutor(max_workers=6) as executor:
            future_to_route = {executor.submit(process_route, route): route for route in routes}
            for future in as_completed(future_to_route):
                route = future_to_route[future]
                try:
                    future.result()
                except Exception as exc:
                    print(f"Route ID {route['id']} generated an exception: {exc}")