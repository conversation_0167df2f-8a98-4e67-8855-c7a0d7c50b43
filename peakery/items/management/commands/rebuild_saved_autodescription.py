from django.core.management.base import BaseCommand
from django.db import connection, transaction
from django.db.models.query_utils import Q


class Command(BaseCommand):
    args = ''
    help = ''

    def handle(self, *args, **options):
        from items.models import Item
        self.stdout.write('Rebuild Saved Autodescription...(%s)\n' %(1))
        items = Item.objects.filter(saved_autodescription = None).filter(Q(db_description="")).order_by('id')
        #items = []
        remaining = items.count()
        process = 0
        for item in items:
            self.stdout.write("Fixing %s\n"  % (item))
            self.stdout.flush()
            item.save()
            process += 1
            remaining -=1
            self.stdout.write('Proccesed :%s - Remaining: %s\n' %(process,remaining))
        self.stdout.write('Rebuild Done.\n')
        self.stdout.flush()
