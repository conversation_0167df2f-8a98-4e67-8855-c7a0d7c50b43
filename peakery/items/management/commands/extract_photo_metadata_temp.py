from items.models import Item, ItemPhoto
from cities.models import Country, Region
from django.core.management.base import BaseCommand
from django.db import connection
import sys
from django.core.files.storage import default_storage
from PIL import Image, ImageFilter, ExifTags
from PIL.ExifTags import TAGS, GPSTAGS

class Command(BaseCommand):
    help = 'Extracts metadata from photos.'
    args = ''

    def handle(self, *args, **options):
        reload(sys)
        sys.setdefaultencoding('utf8')

        def get_if_exist(data, key):
            if key in data:
                return data[key]
            return None

        def convert_to_degrees(value):
            """Helper function to convert the GPS coordinates
            stored in the EXIF to degrees in float format"""
            d0 = value[0][0]
            d1 = value[0][1]
            d = float(d0) / float(d1)
            m0 = value[1][0]
            m1 = value[1][1]
            m = float(m0) / float(m1)

            s0 = value[2][0]
            s1 = value[2][1]
            s = float(s0) / float(s1)

            return d + (m / 60.0) + (s / 3600.0)

        def get_lat(exif_data):
            """Returns the latitude and longitude, if available, from the
            provided exif_data (obtained through get_exif_data above)"""
            # print(exif_data)
            if 'GPSInfo' in exif_data:
                gps_info = exif_data["GPSInfo"]
                gps_latitude = get_if_exist(gps_info, "GPSLatitude")
                gps_latitude_ref = get_if_exist(gps_info, 'GPSLatitudeRef')
                if gps_latitude and gps_latitude_ref:
                    lat = convert_to_degrees(gps_latitude)
                    if gps_latitude_ref != "N":
                        lat = 0 - lat
                    return lat
                else:
                    return None
            else:
                return None

        def get_lon(exif_data):
            """Returns the latitude and longitude, if available, from the
            provided exif_data (obtained through get_exif_data above)"""
            # print(exif_data)
            if 'GPSInfo' in exif_data:
                gps_info = exif_data["GPSInfo"]
                gps_longitude = get_if_exist(gps_info, 'GPSLongitude')
                gps_longitude_ref = get_if_exist(gps_info, 'GPSLongitudeRef')
                if gps_longitude and gps_longitude_ref:
                    lon = convert_to_degrees(gps_longitude)
                    if gps_longitude_ref != "E":
                        lon = 0 - lon
                    return lon
                else:
                    return None
            else:
                return None

        def get_direction(exif_data):
            if 'GPSInfo' in exif_data:
                gps_info = exif_data["GPSInfo"]
                gps_direction = get_if_exist(gps_info, 'GPSImgDirection')
                if gps_direction:
                    return gps_direction
                else:
                    return None
            else:
                return None

        def get_date_time(exif_data):
            if 'DateTimeOriginal' in exif_data:
                date_and_time = exif_data['DateTimeOriginal']
                return date_and_time

        def get_image_height(exif_data):
            if 'ExifImageHeight' in exif_data:
                image_height = exif_data['ExifImageHeight']
                return image_height

        def get_image_width(exif_data):
            if 'ExifImageWidth' in exif_data:
                image_width = exif_data['ExifImageWidth']
                return image_width

        def get_altitude(exif_data):
            if 'GPSInfo' in exif_data:
                gps_info = exif_data["GPSInfo"]
                gps_altitude = get_if_exist(gps_info, 'GPSAltitude')
                if gps_altitude:
                    return gps_altitude
                else:
                    return None
            else:
                return None

        self.stdout.write('Extracting metadata from photos...\n')

        sql = "select a.id, a.image " + \
            "from items_itemphoto a " + \
            "where a.id > 190514 and a.category_id = 2 order by a.id "

        photos = ItemPhoto.objects.raw(sql)

        processed = 1
        for p in photos:
            self.stdout.write('Processing: %s' % p.image)

            upload_path = str(p.image)
            f = default_storage.open(upload_path, 'r')
            image = Image.open(f)

            exif_data = {}
            info = image._getexif()
            if info:
                for tag, value in info.items():
                    decoded = TAGS.get(tag, tag)
                    if decoded == "GPSInfo":
                        gps_data = {}
                        for t in value:
                            sub_decoded = GPSTAGS.get(t, t)
                            gps_data[sub_decoded] = value[t]

                        exif_data[decoded] = gps_data
                    else:
                        exif_data[decoded] = value
            date_time_original = get_date_time(exif_data)
            image_height = get_image_height(exif_data)
            image_width = get_image_width(exif_data)
            altitude = get_altitude(exif_data)
            image_direction = get_direction(exif_data)
            lat = get_lat(exif_data)
            lon = get_lon(exif_data)

            sql = "update items_itemphoto set photo_lat = %s, photo_lng = %s, image_height = %s, image_width = %s " + \
                "where id = %s "

            cursor = connection.cursor()
            cursor.execute(sql, [lat, lon, image_height, image_width, p.id])

            processed += 1

        self.stdout.write('END\n')
