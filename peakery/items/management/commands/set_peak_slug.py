from peakery.items.models import Item
from peakery.cities.models import Country, Region
from django.core.management.base import BaseCommand
from django.db import connection
import sys
from peakery.utils.utils import dictfetchall

class Command(BaseCommand):
    help = 'Sets peak slug based on current rules.'
    args = '<item_id_digit ...>'

    def handle(self, *args, **options):
        reload(sys)
        sys.setdefaultencoding('utf8')
        for item_id_digit in args:
            self.stdout.write('Setting peak slugs...\n')

            sql = "select a.id, a.name, slugify(a.name, False) as name_slugged, default_name, cast(ceil(a.elevation) as text) as elevation, cast(ceil(a.elevation*.3048) as text) as elevation_in_meters, " + \
                "case when exists (select 1 from items_item_country b where b.country_id = 231 and b.item_id = a.id) then true else false end as is_usa, " + \
                "case when exists (select 1 from items_item_country b where b.country_id = 38 and b.item_id = a.id) then true else false end as is_canada " + \
                "from items_item a where right(cast(a.id as text),1) = %s "

            cursor = connection.cursor()
            cursor.execute(sql, [item_id_digit])
            peaks = dictfetchall(cursor)

            total_peak_count = len(peaks)

            processed = 1
            for p in peaks:
                self.stdout.write('Processing: %s (%s of %s)' % (p['name'], processed, total_peak_count))
                #figure out what the new slug should be
                if p['is_usa']:
                    #we'll use region name(s) for US peak slugs
                    region_slug = ""
                    sql = "select c.id, c.name, c.slug " + \
                        "from items_item a, items_item_region b, cities_region c " + \
                        "where a.id = %s and a.id = b.item_id and b.region_id = c.id and c.country_id = 231 " + \
                        "order by c.name asc "

                    cursor = connection.cursor()
                    cursor.execute(sql, [p['id']])
                    regions = dictfetchall(cursor)
                    if regions:
                        for r in regions:
                            if region_slug == "":
                                region_slug = region_slug + '-' + r['slug']

                    new_slug = p['name_slugged'] + region_slug

                    #check if any other peaks have the same slug
                    sql = "select count(a.id) as peak_count from items_item a where a.id != %s and a.slug_new_text = %s "
                    cursor = connection.cursor()
                    cursor.execute(sql, [p['id'], new_slug])
                    result = dictfetchall(cursor)

                    if result[0]['peak_count'] > 0:
                        #if not a default named peak, add elevation to see if we get a unique slug
                        if not p['default_name']:
                            new_slug = new_slug + '-' + p['elevation'] + 'ft'

                        #check if any other peaks have the same slug
                        sql = "select count(a.id) as peak_count from items_item a where a.id != %s and a.slug_new_text = %s "
                        cursor = connection.cursor()
                        cursor.execute(sql, [p['id'], new_slug])
                        result = dictfetchall(cursor)

                        if result[0]['peak_count'] > 0:
                            #try adding digits until we get a unique slug
                            test_digit = 2
                            base_slug = new_slug
                            while test_digit < 1000:
                                new_slug = base_slug + '-' + str(test_digit)
                                #check if any other peaks have the same slug
                                sql = "select count(a.id) as peak_count from items_item a where a.id != %s and a.slug_new_text = %s "
                                cursor = connection.cursor()
                                cursor.execute(sql, [p['id'], new_slug])
                                result = dictfetchall(cursor)
                                if result[0]['peak_count'] == 0:
                                    break
                                test_digit += 1
                elif p['is_canada']:
                    #we'll use region name(s) for CA peak slugs
                    region_slug = ""
                    sql = "select c.id, c.name, c.slug " + \
                        "from items_item a, items_item_region b, cities_region c " + \
                        "where a.id = %s and a.id = b.item_id and b.region_id = c.id and c.country_id = 38 " + \
                        "order by c.name asc "

                    cursor = connection.cursor()
                    cursor.execute(sql, [p['id']])
                    regions = dictfetchall(cursor)
                    if regions:
                        for r in regions:
                            if region_slug == "":
                                region_slug = region_slug + '-' + r['slug']

                    new_slug = p['name_slugged'] + region_slug

                    #check if any other peaks have the same slug
                    sql = "select count(a.id) as peak_count from items_item a where a.id != %s and a.slug_new_text = %s "
                    cursor = connection.cursor()
                    cursor.execute(sql, [p['id'], new_slug])
                    result = dictfetchall(cursor)

                    if result[0]['peak_count'] > 0:
                        #if not a default named peak, add elevation to see if we get a unique slug
                        if not p['default_name']:
                            new_slug = new_slug + '-' + p['elevation_in_meters'] + 'm'

                        #check if any other peaks have the same slug
                        sql = "select count(a.id) as peak_count from items_item a where a.id != %s and a.slug_new_text = %s "
                        cursor = connection.cursor()
                        cursor.execute(sql, [p['id'], new_slug])
                        result = dictfetchall(cursor)

                        if result[0]['peak_count'] > 0:
                            #try adding digits until we get a unique slug
                            test_digit = 2
                            base_slug = new_slug
                            while test_digit < 1000:
                                new_slug = base_slug + '-' + str(test_digit)
                                #check if any other peaks have the same slug
                                sql = "select count(a.id) as peak_count from items_item a where a.id != %s and a.slug_new_text = %s "
                                cursor = connection.cursor()
                                cursor.execute(sql, [p['id'], new_slug])
                                result = dictfetchall(cursor)
                                if result[0]['peak_count'] == 0:
                                    break
                                test_digit += 1
                else:
                    #we'll use country name(s) for non-US/CA peak slugs
                    region_slug = ""
                    sql = "select c.id, c.name, c.slug " + \
                        "from items_item a, items_item_country b, cities_country c " + \
                        "where a.id = %s and a.id = b.item_id and b.country_id = c.id " + \
                        "order by c.name asc "

                    cursor = connection.cursor()
                    cursor.execute(sql, [p['id']])
                    regions = dictfetchall(cursor)
                    if regions:
                        for r in regions:
                            if region_slug == "":
                                region_slug = region_slug + '-' + r['slug']

                    new_slug = p['name_slugged'] + region_slug

                    #check if any other peaks have the same slug
                    sql = "select count(a.id) as peak_count from items_item a where a.id != %s and a.slug_new_text = %s "
                    cursor = connection.cursor()
                    cursor.execute(sql, [p['id'], new_slug])
                    result = dictfetchall(cursor)

                    if result[0]['peak_count'] > 0:
                        #if not a default named peak, add elevation to see if we get a unique slug
                        if not p['default_name']:
                            new_slug = new_slug + '-' + p['elevation_in_meters'] + 'm'

                        #check if any other peaks have the same slug
                        sql = "select count(a.id) as peak_count from items_item a where a.id != %s and a.slug_new_text = %s "
                        cursor = connection.cursor()
                        cursor.execute(sql, [p['id'], new_slug])
                        result = dictfetchall(cursor)

                        if result[0]['peak_count'] > 0:
                            #try adding digits until we get a unique slug
                            test_digit = 2
                            base_slug = new_slug
                            while test_digit < 1000:
                                new_slug = base_slug + '-' + str(test_digit)
                                #check if any other peaks have the same slug
                                sql = "select count(a.id) as peak_count from items_item a where a.id != %s and a.slug_new_text = %s "
                                cursor = connection.cursor()
                                cursor.execute(sql, [p['id'], new_slug])
                                result = dictfetchall(cursor)
                                if result[0]['peak_count'] == 0:
                                    break
                                test_digit += 1

                #now update the peak with the new slug and flag as having been checked
                sql = "update items_item set old_slug = slug_new_text, slug_new_text = %s where id = %s "
                cursor = connection.cursor()
                cursor.execute(sql, [new_slug, p['id']])

                processed += 1

        self.stdout.write('END\n')
