from items.models import Item
from cities.models import Country, Region
from django.core.management.base import BaseCommand
from django.db import connection
import sys

class Command(BaseCommand):
    help = 'Imports items from OSM.'
    args = '<country_code ...>'

    def dictfetchall(cursor):
        "Return all rows from a cursor as a dict"
        columns = [col[0] for col in cursor.description]
        return [
            dict(zip(columns, row))
            for row in cursor.fetchall()
        ]

    def handle(self, *args, **options):
        reload(sys)
        sys.setdefaultencoding('utf8')
        for country_code in args:
            import overpy
            import math
            api = overpy.Overpass()

            country_code = country_code.upper()
            self.stdout.write('Importing OSM Items for '+country_code+'...\n')

            result = api.query('[timeout:900];area["ISO3166-1:alpha2"="'+country_code+'"];node[natural=peak](area);out;')

            processed = 1
            total_peak_count = len(result.nodes)
            for peak in result.nodes:
                peak_name = ""
                peak_name_en = ""
                peak_elevation = 0
                osm_id = peak.id
                peak_lat = peak.lat
                peak_long = peak.lon
                if 'name' in peak.tags:
                    peak_name = peak.tags['name']
                if 'name:en' in peak.tags:
                    peak_name_en = peak.tags['name:en']
                if 'ele' in peak.tags:
                    peak_elevation = peak.tags['ele']
                    try:
                        peak_elevation = float(peak_elevation)
                        peak_elevation = math.floor(peak_elevation * 3.28084)
                    except:
                        peak_elevation = 0


                self.stdout.write('Processing: %s (%s of %s)' % (peak_name, processed, total_peak_count))

                sql = "delete from items_item_osm where osm_id = %s"
                cursor = connection.cursor()
                cursor.execute(sql, [osm_id])

                sql = "insert into items_item_osm (osm_id, name, name_en, lat, long, elevation, country_code) values (%s, %s, %s, %s, %s, %s, %s)"
                cursor = connection.cursor()
                cursor.execute(sql, [osm_id, peak_name, peak_name_en, peak_lat, peak_long, peak_elevation, country_code])
                processed += 1

        self.stdout.write('END\n')
