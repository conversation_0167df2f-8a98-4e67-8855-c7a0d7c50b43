from cities.models import Country, Region
from items.models import Item, SummitLog, PeakRoute
from django.contrib.auth.models import User
from items.models import STATUS_SUMMIT_ACTIVE, STATUS_SUMMIT_PENDING, STATUS_ROUTE_ACTIVE
from django.core.management.base import BaseCommand
from django.db import connection
import sys
import json
from utils.utils import dictfetchall

class Command(BaseCommand):
    help = 'Creates new summit logs from records in temp_peakbagger_import working table.'
    args = ''

    def handle(self, *args, **options):
        reload(sys)
        sys.setdefaultencoding('utf8')

        self.stdout.write('Importing summit logs...\n')

        sql = "select a.* from temp_peakbagger_import a where a.processed = false"

        cursor = connection.cursor()
        cursor.execute(sql)
        summitlogs = dictfetchall(cursor)

        total_summitlogs = len(summitlogs)
        user_id = 11206

        processed = 1
        for s in summitlogs:
            self.stdout.write('Processing: %s (%s of %s)' % (s['peakpointname'], processed, total_summitlogs))

            #get peak
            peak = Item.objects.get(id=s['item_id'])

            #successful summit?
            if s['ascenttype'] == 'Suc':
                attempt = False
            else:
                attempt = True

            #existing route?
            if s['routeupname'] != '':
                route_up, created = PeakRoute.objects.get_or_create(name=s['routeupname'], item=peak, user_id=user_id)
                peak_route_id = route_up.id
            else:
                peak_route_id = None

            distance_to_summit = s['distmi']
            total_distance = s['tripmi']
            elevation_gain = s['tripupft']

            time_to_summit = 0
            if s['minup'] != 0:
                time_to_summit = 60*float(s['minup'])
            if s['hourup'] != 0:
                time_to_summit = time_to_summit + (3600*float(s['hourup']))
            if time_to_summit > 0:
                timetosummit = time_to_summit
            else:
                timetosummit = None

            total_trip_time = 0
            totaltripmin = s['minup'] + s['mindn']
            totaltriphours = s['hourup'] + s['hourdn']
            if totaltripmin != 0:
                total_trip_time = 60*float(totaltripmin)
            if totaltriphours != 0:
                total_trip_time = total_trip_time + (3600*float(totaltriphours))
            if total_trip_time > 0:
                totaltriptime = total_trip_time
            else:
                totaltriptime = None

            activites_metadata = '['
            challenges_metadata = '['
            gear_metadata = '['

            if s['bushwhack']:
                challenges_metadata += '"bushwhacking", '
            if s['streamford']:
                challenges_metadata += '"stream crossing", '
            if s['snowonground']:
                challenges_metadata += '"snow on route", '
            if s['scramble']:
                activites_metadata += '"scrambling", '
            if s['iceaxe']:
                gear_metadata += '"ice axe", '
            if s['crampons']:
                gear_metadata += '"crampons", '
            if s['snowshoes']:
                gear_metadata += '"snowshoes", '

            if challenges_metadata != '[':
                challenges_metadata = challenges_metadata[:-2] + ']'
            else:
                challenges_metadata += ']'

            if activites_metadata != '[':
                activites_metadata = activites_metadata[:-2] + ']'
            else:
                activites_metadata += ']'

            if gear_metadata != '[':
                gear_metadata = gear_metadata[:-2] + ']'
            else:
                gear_metadata += ']'

            trip_metadata = '{"type": [], "activities": %s, "challenges": %s, "gear": %s}' % (activites_metadata, challenges_metadata, gear_metadata)

            summit = SummitLog(user_id=user_id, item=peak, date=s['ascentdate'], date_entered=True, status = STATUS_SUMMIT_ACTIVE, log=s['tripreport'], attempt=attempt, peak_route_id=peak_route_id, distance_to_summit=distance_to_summit, total_distance=total_distance, elevation_gain=elevation_gain, time_to_summit=timetosummit, total_trip_time=totaltriptime, trip_metadata=trip_metadata)
            summit.quick_save()

            #update summitlog count for item
            summitlog_count = SummitLog.objects.filter(item=peak, attempt=False, status=STATUS_SUMMIT_ACTIVE).count()
            peak.summitlog_count = summitlog_count
            peak.quick_save()

            nonmember_baggers = s['othersinparty']
            if nonmember_baggers:
                nonmember_baggers = nonmember_baggers.split(',')
                baggers_to_add = nonmember_baggers
                for bagger in baggers_to_add:
                    if bagger:
                        sql = "insert into items_summitfellowbagger (summit_log_id, name, resolved_relation) values (%s, %s, False) "
                        cursor = connection.cursor()
                        cursor.execute(sql, [summit.id, bagger[1:-1]])

            sql = "update temp_peakbagger_import set processed = true where id = %s "
            cursor = connection.cursor()
            cursor.execute(sql, [s['id']])

            processed += 1

        self.stdout.write('END\n')
