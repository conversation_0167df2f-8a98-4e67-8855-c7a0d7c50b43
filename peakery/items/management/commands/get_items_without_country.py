from django.core.management.base import BaseCommand

class Command(BaseCommand):
    args = ''
    help = 'get items without country'

    def handle(self,*args,**options):
        from items.models import Item
        from django.utils.encoding import smart_str
        import csv

        items = Item.objects.filter(has_country=False).order_by('id')

        writer = csv.writer(open('static/items_without_country.csv', 'w'))

        for item in items:

            writer.writerow([item.id, smart_str(item.name), smart_str(item.slug)])
