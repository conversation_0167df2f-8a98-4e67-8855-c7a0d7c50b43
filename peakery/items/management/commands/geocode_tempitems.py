from tempitems.models import TempItem
from cities.models import Country, Region
from django.core.management.base import BaseCommand
from django.db import connection
import sys

class Command(BaseCommand):
    help = 'Geocodes temp items.'
    args = ''

    def dictfetchall(cursor):
        "Return all rows from a cursor as a dict"
        columns = [col[0] for col in cursor.description]
        return [
            dict(zip(columns, row))
            for row in cursor.fetchall()
        ]

    def handle(self, *args, **options):
        reload(sys)
        sys.setdefaultencoding('utf8')

        self.stdout.write('Geocoding Temp Items...\n')

        import googlemaps
        import json
        from django.contrib.gis.geos import Point

        gmaps = googlemaps.Client(key="AIzaSyAjV7dkmG1WYHeHE0EnQvOV6YnPWpRxIzg")

        sql = "select a.id, a.name, a.lat, a.long " + \
            "from tempitems_tempitem a "

        peaks = TempItem.objects.raw(sql)

        processed = 1
        for p in peaks:
            self.stdout.write('Processing: %s (ID: %s - %s of ??)' % (p.name, p.id, processed))
            point = Point(float(p.long),float(p.lat))
            result = gmaps.reverse_geocode(("%s, %s" % (point.y, point.x)))
            for r in result:
                region_name = None
                country_name = None
                region_match = False
                if r['geometry']['location_type'] == "GEOMETRIC_CENTER" or r['geometry']['location_type'] == "APPROXIMATE":
                    for a in r['address_components']:
                        for t in a['types']:
                            if t == "country":
                                if country_name != a['short_name']:
                                    country_name = a['short_name']
                                    countries = Country.objects.filter(code=country_name)
                                    #delete existing countries
                                    sql = "delete from tempitems_tempitem_country where tempitem_id = %s "
                                    cursor = connection.cursor()
                                    cursor.execute(sql, [p.id])
                                    for country in countries:
                                        #add new country
                                        sql = "insert into tempitems_tempitem_country (tempitem_id, country_id) values (%s, %s)"
                                        cursor = connection.cursor()
                                        cursor.execute(sql, [p.id, country.id])
                    for a in r['address_components']:
                        for t in a['types']:
                            if t == "administrative_area_level_1":
                                if region_name != a['long_name']:
                                    region_name = a['long_name']
                                    regions = Region.objects.filter(name=region_name, country_id=country.id)
                                    if regions:
                                        region_match = True
                                        #delete existing regions
                                        sql = "delete from tempitems_tempitem_region where tempitem_id = %s "
                                        cursor = connection.cursor()
                                        cursor.execute(sql, [p.id])
                                        for region in regions:
                                            #add new region
                                            sql = "insert into tempitems_tempitem_region (tempitem_id, region_id) values (%s, %s)"
                                            cursor = connection.cursor()
                                            cursor.execute(sql, [p.id, region.id])
                                    if not region_match:
                                        #try matching "google region name"
                                        regions = Region.objects.filter(google_region_name=region_name, country_id=country.id)
                                        if regions:
                                            region_match = True
                                            #delete existing regions
                                            sql = "delete from tempitems_tempitem_region where tempitem_id = %s "
                                            cursor = connection.cursor()
                                            cursor.execute(sql, [p.id])
                                            for region in regions:
                                                #add new region
                                                sql = "insert into tempitems_tempitem_region (tempitem_id, region_id) values (%s, %s)"
                                                cursor = connection.cursor()
                                                cursor.execute(sql, [p.id, region.id])
                                    #log geocoder response
                                    sql = "delete from tempitems_tempitem_geocode where tempitem_id = %s"
                                    cursor = connection.cursor()
                                    cursor.execute(sql, [p.id])
                                    sql = "insert into tempitems_tempitem_geocode (tempitem_id, region_name, region_match, geocoder_response, country_name) values (%s, %s, %s, %s, %s)"
                                    cursor = connection.cursor()
                                    cursor.execute(sql, [p.id, region_name, region_match, json.dumps(result), country_name])
            processed += 1

        self.stdout.write('END\n')
