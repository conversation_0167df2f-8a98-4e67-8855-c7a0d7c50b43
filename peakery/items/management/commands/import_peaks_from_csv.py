from django.core.management.base import BaseCommand
from importer.importer import peaks

class Command(BaseCommand):
    args = ''
    help = 'the place for the csv file is project_dir/importer/csv/'

    def handle(self, *args, **options):
        self.stdout.write('Importing...\n')
        line = 0
        if len(args) < 1:
            line = 0
        else:
            line = args[0]
        peaks()
        self.stdout.write('Done\n')
