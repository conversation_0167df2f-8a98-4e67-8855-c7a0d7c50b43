from django.core.management.base import BaseCommand
from django.db import connection, transaction



class Command(BaseCommand):
    args = ''
    help = ''

    def handle(self, *args, **options):
        self.stdout.write('Rebuild Saved Autodescription...\n')
        with connection.cursor() as cursor:
            # Data modifying operation - commit required
            cursor.execute("update items_item set saved_autodescription = Null where saved_autodescription is not Null;")
            transaction.commit_unless_managed()
            self.stdout.write('Rebuild Done.\n')
