from peakery.items.models import Item
from peakery.cities.models import Country, Region
from django.core.management.base import BaseCommand
from django.db import connection
import sys
from peakery.utils.utils import dictfetchall

class Command(BaseCommand):
    help = 'Caches peak stats. Pass digit 0-9 to work on IDs ending with that digit.'
    args = '<item_id_digit ...>'

    def handle(self, *args, **options):
        reload(sys)
        sys.setdefaultencoding('utf8')
        for item_id_digit in args:
            from django.core.cache import cache

            self.stdout.write('Cacheing peak stats for items ending in '+item_id_digit+'...\n')

            self.stdout.write('Finding items to cache...\n')

            sql = "select a.id, a.name " + \
                "from items_item a " + \
                "where a.summitlog_count > 0 " + \
                "and right(cast(a.id as text),1) = %s "

            cursor = connection.cursor()
            cursor.execute(sql, [item_id_digit])
            peaks = dictfetchall(cursor)

            for p in peaks:

                self.stdout.write('Processing: %s - %s' % (p['id'], p['name']))

                # get elevation, prominence and summits rank
                region_ranks = cache.get('peak_region_ranks_%s' % p['id'])
                if not region_ranks:
                    sql = "select a.id, a.name, c.id as region_id, c.name as region_name, c.slug as region_slug, cc.id as country_id, cc.name as country_name, cc.slug as country_slug, coalesce(d.region_elevation_rank, 1) as region_elevation_rank, coalesce(e.region_prominence_rank, 1) as region_prominence_rank, coalesce(f.region_summits_rank, 1) as region_summits_rank " + \
                          "from items_item a " + \
                          "join items_item_region b on b.item_id = a.id " + \
                          "join cities_region c on c.id = b.region_id " + \
                          "join cities_country cc on cc.id = c.country_id " + \
                          "left join (select y.region_id, count(x.id) + 1 as region_elevation_rank from items_item x, items_item_region y, items_item_region yy, items_item z where z.id = %s and x.elevation > z.elevation and z.id = y.item_id and y.region_id = yy.region_id and yy.item_id = x.id group by y.region_id) d on d.region_id = b.region_id " + \
                          "left join (select y.region_id, count(x.id) + 1 as region_prominence_rank from items_item x, items_item_region y, items_item_region yy, items_item z where z.id = %s and x.prominence > z.prominence and z.id = y.item_id and y.region_id = yy.region_id and yy.item_id = x.id group by y.region_id) e on e.region_id = b.region_id " + \
                          "left join (select y.region_id, count(x.id) + 1 as region_summits_rank from items_item x, items_item_region y, items_item_region yy, items_item z where z.id = %s and x.summitlog_count > z.summitlog_count and z.id = y.item_id and y.region_id = yy.region_id and yy.item_id = x.id group by y.region_id) f on f.region_id = b.region_id " + \
                          "where a.id = %s "

                    cursor = connection.cursor()
                    cursor.execute(sql, [p['id'], p['id'], p['id'], p['id']])
                    region_ranks = dictfetchall(cursor)

                    cache.set('peak_region_ranks_%s' % p['id'], region_ranks, 60 * 60 * 24)

                country_ranks = cache.get('peak_country_ranks_%s' % p['id'])
                if not country_ranks:
                    sql = "select a.id, a.name, c.id as country_id, c.name as country_name, c.slug as country_slug, coalesce(d.country_elevation_rank, 1) as country_elevation_rank, coalesce(e.country_prominence_rank, 1) as country_prominence_rank, coalesce(f.country_summits_rank, 1) as country_summits_rank " + \
                          "from items_item a " + \
                          "join items_item_country b on b.item_id = a.id " + \
                          "join cities_country c on c.id = b.country_id " + \
                          "left join (select y.country_id, count(x.id) + 1 as country_elevation_rank from items_item x, items_item_country y, items_item_country yy, items_item z where z.id = %s and x.elevation > z.elevation and z.id = y.item_id and y.country_id = yy.country_id and yy.item_id = x.id group by y.country_id) d on d.country_id = b.country_id " + \
                          "left join (select y.country_id, count(x.id) + 1 as country_prominence_rank from items_item x, items_item_country y, items_item_country yy, items_item z where z.id = %s and x.prominence > z.prominence and z.id = y.item_id and y.country_id = yy.country_id and yy.item_id = x.id group by y.country_id) e on e.country_id = b.country_id " + \
                          "left join (select y.country_id, count(x.id) + 1 as country_summits_rank from items_item x, items_item_country y, items_item_country yy, items_item z where z.id = %s and x.summitlog_count > z.summitlog_count and z.id = y.item_id and y.country_id = yy.country_id and yy.item_id = x.id group by y.country_id) f on f.country_id = b.country_id " + \
                          "where a.id = %s "

                    cursor = connection.cursor()
                    cursor.execute(sql, [p['id'], p['id'], p['id'], p['id']])
                    country_ranks = dictfetchall(cursor)

                    cache.set('peak_country_ranks_%s' % p['id'], country_ranks, 60 * 60 * 24)

        self.stdout.write('END\n')
