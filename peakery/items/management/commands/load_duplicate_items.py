#update items_item set description=NULL, saved_autodescription=NULL where "description" LIKE '%Contribute a summit log entry%'

from django.core.management.base import BaseCommand


class Command(BaseCommand):
    args = ''
    help = ''

    def handle(self, *args, **options):
        from importer.importer import fill_item_duplicates
        self.stdout.write('This is gonna take a while , be pacient...\n')
        fill_item_duplicates(writer=self.stdout)
        self.stdout.write('Finish')
