from django.core.management.base import BaseCommand
from django.db import connection
import sys
from peakery.utils.utils import dictfetchall

class Command(BaseCommand):
    help = 'Counts peaks near cities.'
    args = ''

    def handle(self, *args, **options):
        reload(sys)
        sys.setdefaultencoding('utf8')
        self.stdout.write('Counting peaks near cities...\n')

        sql = "select a.id, a.name from cities_city a " + \
            "where a.summitlog_count < 0 "

        cursor = connection.cursor()
        cursor.execute(sql)
        cities = dictfetchall(cursor)

        total_cities_count = len(cities)

        processed = 1

        for c in cities:

            self.stdout.write('Processing: %s (%s of %s)' % (c['name'], processed, total_cities_count))

            #get count of peaks near city
            sql = "select coalesce(sum(b.summitlog_count),0) as summit_count from cities_city a, items_item b " + \
                "where ST_DWithin(b.location,ST_SetSRID(ST_MakePoint(a.location_y, a.location_x),4326)::geography,60 * 1609.34) " + \
                "and a.id = %s "
            cursor = connection.cursor()
            cursor.execute(sql, [c['id']])
            result = dictfetchall(cursor)
            if result[0]['summit_count'] >= 0:
                sql = "update cities_city set summitlog_count = %s where id = %s "
                cursor = connection.cursor()
                cursor.execute(sql, [result[0]['summit_count'], c['id']])

            processed += 1

        self.stdout.write('END\n')
