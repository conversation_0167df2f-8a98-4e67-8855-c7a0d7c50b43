from items.models import Item
from cities.models import Country, Region
from django.core.management.base import BaseCommand
from django.db import connection
import sys
from utils.utils import dictfetchall

class Command(BaseCommand):
    help = 'Rebuilds peak photos. Pass digit 0-9 to work on IDs ending with that digit.'
    args = '<item_photo_id_digit ...>'

    def handle(self, *args, **options):
        reload(sys)
        sys.setdefaultencoding('utf8')

        for item_photo_id_digit in args:
            self.stdout.write('Rebuilding missing peak photos ending in '+item_photo_id_digit+'...\n')

            from PIL import Image, ImageFilter, ExifTags
            import cStringIO
            import os.path
            from django.core.files.storage import default_storage

            self.stdout.write('Finding user photos to rebuild...\n')

            path_string = 'items/users/%'

            sql = "select a.id, a.image, replace(a.image,'items/users/','') as basefile " + \
                "from items_itemphoto a " + \
                "where a.image like %s and a.missing = true " + \
                "and right(cast(a.id as text),1) = %s "

            cursor = connection.cursor()
            cursor.execute(sql, [path_string, item_photo_id_digit])
            photos = dictfetchall(cursor)

            for p in photos:

                upload_path = p['image']
                #upload_path = 'images/items/users/cache/%s.910x680_q95.jpg' % (p['basefile'])
                thumb_file_path = 'images/items/users/cache/%s' % (p['basefile'])

                self.stdout.write('Processing: %s' % (p['image']))

                extension = os.path.splitext(p['basefile'])[1][1:].lower()
                if extension == 'png':
                    extension = 'PNG'
                else:
                    extension = 'JPEG'

                try:
                    f = default_storage.open(upload_path, 'r')
                    image = Image.open(f)

                    try:
                        for orientation in ExifTags.TAGS.keys():
                            if ExifTags.TAGS[orientation]=='Orientation':
                                break
                        exif=dict(image._getexif().items())

                        if exif[orientation] == 3:
                            image=image.rotate(180, expand=True)
                        elif exif[orientation] == 6:
                            image=image.rotate(270, expand=True)
                        elif exif[orientation] == 8:
                            image=image.rotate(90, expand=True)

                    except (AttributeError, KeyError, IndexError):
                        # cases: image don't have getexif
                        pass

                    width, height = image.size

                    # 480x360 thumbnail
                    if width >= height:
                        # landscape orientation photo
                        if float(width) / float(height) > 1.333333:
                            delta = width - (1.333333 * height)
                            left = int(delta / 2)
                            upper = 0
                            right = width - left
                            lower = height
                        else:
                            delta = height - (width / 1.333333)
                            left = 0
                            upper = int(delta / 2)
                            right = width
                            lower = height - upper
                    else:
                        # portrait orientation photo
                        if float(height) / float(width) > 1.333333:
                            delta = height - (1.333333 * width)
                            left = 0
                            upper = int(delta / 2)
                            right = width
                            lower = height - upper
                        else:
                            delta = width - (height / 1.333333)
                            left = int(delta / 2)
                            upper = 0
                            right = width - left
                            lower = height

                    tmp_image = image.crop((left, upper, right, lower))
                    if width >= height:
                        new_width = 480
                        new_height = 360
                    else:
                        new_width = 360
                        new_height = 480
                    tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
                    tmp_image = tmp_image.filter(ImageFilter.SHARPEN)
                    f_thumb = default_storage.open('%s.%s' % (thumb_file_path, '480x360_q95.jpg'), "w")
                    # NOTE, we're saving the image into a cStringIO object to avoid writing to disk
                    out_img = cStringIO.StringIO()
                    # You MUST specify the file type because there is no file name to discern it from
                    tmp_image.save(out_img, format=extension, subsampling=0, quality=95)
                    f_thumb.write(out_img.getvalue())
                    f_thumb.close()

                    sql = "update items_itemphoto set missing = false where id = %s"
                    cursor = connection.cursor()
                    cursor.execute(sql, [p['id']])

                except Exception as e:
                    pass

            self.stdout.write('Finding main photos to rebuild...\n')

            path_string = 'items/main/%'

            sql = "select a.id, a.image, replace(a.image,'items/main/','') as basefile " + \
                "from items_itemphoto a " + \
                "where a.image like %s and a.missing = true " + \
                "and right(cast(a.id as text),1) = %s "

            cursor = connection.cursor()
            cursor.execute(sql, [path_string, item_photo_id_digit])
            photos = dictfetchall(cursor)

            for p in photos:

                upload_path = p['image']
                #upload_path = 'images/items/main/cache/%s.910x680_q95.jpg' % (p['basefile'])
                thumb_file_path = 'images/items/main/cache/%s' % (p['basefile'])

                self.stdout.write('Processing: %s' % (p['image']))

                extension = os.path.splitext(p['basefile'])[1][1:].lower()
                if extension == 'png':
                    extension = 'PNG'
                else:
                    extension = 'JPEG'

                try:
                    f = default_storage.open(upload_path, 'r')
                    image = Image.open(f)

                    try:
                        for orientation in ExifTags.TAGS.keys():
                            if ExifTags.TAGS[orientation]=='Orientation':
                                break
                        exif=dict(image._getexif().items())

                        if exif[orientation] == 3:
                            image=image.rotate(180, expand=True)
                        elif exif[orientation] == 6:
                            image=image.rotate(270, expand=True)
                        elif exif[orientation] == 8:
                            image=image.rotate(90, expand=True)

                    except (AttributeError, KeyError, IndexError):
                        # cases: image don't have getexif
                        pass

                    width, height = image.size

                    # 480x360 thumbnail
                    if width >= height:
                        # landscape orientation photo
                        if float(width) / float(height) > 1.333333:
                            delta = width - (1.333333 * height)
                            left = int(delta / 2)
                            upper = 0
                            right = width - left
                            lower = height
                        else:
                            delta = height - (width / 1.333333)
                            left = 0
                            upper = int(delta / 2)
                            right = width
                            lower = height - upper
                    else:
                        # portrait orientation photo
                        if float(height) / float(width) > 1.333333:
                            delta = height - (1.333333 * width)
                            left = 0
                            upper = int(delta / 2)
                            right = width
                            lower = height - upper
                        else:
                            delta = width - (height / 1.333333)
                            left = int(delta / 2)
                            upper = 0
                            right = width - left
                            lower = height

                    tmp_image = image.crop((left, upper, right, lower))
                    if width >= height:
                        new_width = 480
                        new_height = 360
                    else:
                        new_width = 360
                        new_height = 480
                    tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
                    tmp_image = tmp_image.filter(ImageFilter.SHARPEN)
                    f_thumb = default_storage.open('%s.%s' % (thumb_file_path, '480x360_q95.jpg'), "w")
                    # NOTE, we're saving the image into a cStringIO object to avoid writing to disk
                    out_img = cStringIO.StringIO()
                    # You MUST specify the file type because there is no file name to discern it from
                    tmp_image.save(out_img, format=extension, subsampling=0, quality=95)
                    f_thumb.write(out_img.getvalue())
                    f_thumb.close()

                    sql = "update items_itemphoto set missing = false where id = %s"
                    cursor = connection.cursor()
                    cursor.execute(sql, [p['id']])

                except Exception as e:
                    pass

        self.stdout.write('END\n')
