from items.models import Item
from cities.models import Country, Region
from django.core.management.base import BaseCommand
from django.db import connection
import sys
from utils.utils import dictfetchall

class Command(BaseCommand):
    help = 'Imports new peakery peaks from OSM peaks.  Pass digit 0-9 to work on IDs ending with that digit.'
    args = '<item_id_digit ...>'

    def handle(self, *args, **options):
        reload(sys)
        sys.setdefaultencoding('utf8')
        for item_id_digit in args:
            self.stdout.write('Importing OSM peaks ending in '+item_id_digit+'...\n')

            sql = "select a.osm_id, a.nearest_peak_id, a.country_code, case when a.country_code = 'US' then true else false end as is_usa, a.name, a.peakery_name, slugify(a.peakery_name, False) as name_slugged, a.lat, a.long, case when a.elevation > 0 then a.elevation else a.google_elevation end as elevation, ST_SetSRID(ST_MakePoint(a.long, a.lat), 4326) as peak_location, " + \
                "case when exists (select 1 from items_item_osm_region b where b.osm_id = a.osm_id) then true else false end as osm_has_region " + \
                "from items_item_osm a " + \
                "where a.active = true " + \
                "and a.item_id is null " + \
                "and a.new_peak_to_add = true " + \
                "and not exists (select 1 from items_item x where x.osm_id = a.osm_id) " + \
                "and right(cast(a.osm_id as text),1) = %s "

            cursor = connection.cursor()
            cursor.execute(sql, [item_id_digit])
            peaks = dictfetchall(cursor)

            total_peak_count = len(peaks)

            processed = 1

            for p in peaks:

                self.stdout.write('Processing: %s (%s of %s)' % (p['peakery_name'], processed, total_peak_count))

                #get countries for peak
                sql = "select c.id, c.name, c.slug " + \
                    "from cities_country c " + \
                    "where c.code = %s " + \
                    "order by c.name asc "

                cursor = connection.cursor()
                cursor.execute(sql, [p['country_code']])
                countries = dictfetchall(cursor)

                #get regions for peak
                if p['osm_has_region']:
                    sql = "select c.id, c.name, c.slug " + \
                        "from items_item_osm a, items_item_osm_region b, cities_region c " + \
                        "where a.osm_id = %s and a.osm_id = b.osm_id and b.region_id = c.id " + \
                        "order by c.name asc "
                    cursor = connection.cursor()
                    cursor.execute(sql, [p['osm_id']])
                    regions = dictfetchall(cursor)
                else:
                    sql = "select c.id, c.name, c.slug " + \
                        "from items_item a, items_item_region b, cities_region c " + \
                        "where a.id = %s and a.id = b.item_id and b.region_id = c.id " + \
                        "order by c.name asc "
                    cursor = connection.cursor()
                    cursor.execute(sql, [p['nearest_peak_id']])
                    regions = dictfetchall(cursor)

                #figure out what the new slug should be
                if p['is_usa']:
                    #we'll use region name(s) for US peak slugs
                    region_slug = ""
                    if regions:
                        for r in regions:
                            region_slug = region_slug + '-' + r['slug']

                    range_slug = ""

                    new_slug = p['name_slugged'] + range_slug + region_slug

                else:
                    #we'll use country name(s) for non-US peak slugs
                    region_slug = ""
                    if countries:
                        for c in countries:
                            region_slug = region_slug + '-' + c['slug']

                    range_slug = ""

                    new_slug = p['name_slugged'] + range_slug + region_slug

                #check if any other peaks have the same slug
                sql = "select count(a.id) as peak_count from items_item a where a.slug_new_text = %s "
                cursor = connection.cursor()
                cursor.execute(sql, [new_slug])
                result = dictfetchall(cursor)
                if result[0]['peak_count'] > 0:
                    #try adding digits until we get a unique slug
                    test_digit = 2
                    base_slug = new_slug
                    while test_digit < 1000:
                        new_slug = base_slug + '-' + str(test_digit)
                        #check if any other peaks have the same slug
                        sql = "select count(a.id) as peak_count from items_item a where a.slug_new_text = %s "
                        cursor = connection.cursor()
                        cursor.execute(sql, [new_slug])
                        result = dictfetchall(cursor)
                        if result[0]['peak_count'] == 0:
                            break
                        test_digit += 1

                #now add the peak
                sql = "insert into items_item (user_id, name, name_unaccented, featured_summitlog_count, summitlog_count, lat, long, elevation, location, slug, slug_new_text, has_region, has_country, near_city_from_correction, created, modified, osm_id) " + \
                    "values (1, %s, unaccent(%s), 0, 0, %s, %s, %s, %s, %s, %s, true, true, false, now(), now(), %s) "
                cursor = connection.cursor()
                cursor.execute(sql, [p['peakery_name'], p['peakery_name'], p['lat'], p['long'], p['elevation'], p['peak_location'], new_slug, new_slug, p['osm_id']])

                #now get the new peak id
                sql = "select id from items_item where slug_new_text = %s "
                cursor = connection.cursor()
                cursor.execute(sql, [new_slug])
                result = dictfetchall(cursor)
                new_peak_id = result[0]['id']

                #add country(s)
                if countries:
                    for c in countries:
                        sql = "insert into items_item_country (item_id, country_id) values (%s, %s) "
                        cursor = connection.cursor()
                        cursor.execute(sql, [new_peak_id, c['id']])

                #add region(s)
                if regions:
                    for r in regions:
                        sql = "insert into items_item_region (item_id, region_id) values (%s, %s) "
                        cursor = connection.cursor()
                        cursor.execute(sql, [new_peak_id, r['id']])

                #add altername name if necessary
                if p['name'] != p['peakery_name'] and p['name'] != '':
                    sql = "insert into items_alternatename (item_id, name) values (%s, %s) "
                    cursor = connection.cursor()
                    cursor.execute(sql, [new_peak_id, p['name']])

                processed += 1

        self.stdout.write('END\n')
