from django.core.management.base import BaseCommand

from peakery.items.models import Item


class Command(BaseCommand):
    args = ''
    help = 'Fix South Korea peak names'

    def handle(self, *args, **options):
        print("Starting Fix South Korea peak names task")

        korea_peaks = Item.objects.filter(country__code="KR")

        peaks_to_update = []
        for peak in korea_peaks:
            need_update = False
            # name, slug, slug_new_text
            print(peak.name)
            if "-" in peak.name:
                peak.name = peak.name.replace("-", "", 1)
                peak.slug = peak.slug.replace("-", "", 1)
                peak.slug_new_text = peak.slug_new_text.replace("-", "", 1)
                need_update = True
            if "'" in peak.name:
                peak.name = peak.name.replace("'", "", 1)
                peak.slug = peak.slug.replace("'", "", 1)
                peak.slug_new_text = peak.slug_new_text.replace("'", "", 1)
                need_update = True

            if need_update:
                peaks_to_update.append(peak)

        print(len(peaks_to_update), "peaks will be updated")
        Item.objects.bulk_update(peaks_to_update, ['name', 'slug', 'slug_new_text'])

        print("Finished Fix South Korea peak names task")
