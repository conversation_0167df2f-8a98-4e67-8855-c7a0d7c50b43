from django.db.models import Max
from django.db.models import Q
from django.contrib.gis.measure import D
from django.contrib.gis.db import models
from django.contrib.gis.geos.point import Point
from django.core.cache import cache
from django.conf import settings
from django.utils.encoding import smart_str


class SummitLogManager(models.Manager):
    def qs(self):
        return self.get_query_set()

class PeakRouteManager(models.Manager):
    def qs(self):
        return self.get_query_set()

class PeakRouteArchiveManager(models.Manager):
    def qs(self):
        return self.get_query_set()

class ItemManager(models.Manager):
    """Returns items with photos."""
    def with_photo(self):
        return self.get_query_set().filter(~Q(thumbnail = ''))

    """ Return highest item """
    def highest(self):
        items = self.get_query_set()
        elevation = items.aggregate(Max('elevation')).values()[0]
        return items.get(elevation=str(elevation))

    """ Return the nearest item from given point """
    """ if as_queryset is True the distance annotation of raw query will be deleted """
    def old_nearest_to(self, lat, long, distance_limit=1000, limit=200, as_queryset=False, exclude_first=False, name=None):
        import geoutils

        if exclude_first:
            limit = limit + 1



        query = geoutils.nearest_raw(lat, long, distance_limit, limit, name)

        if as_queryset:
            query = self.get_query_set().filter(id__in = [q.id for q in query])

        if exclude_first:
            return query[1:limit]

        return query

    def nearest_to(self, lat, lon,distance_limit=1000,limit=6,exclude_first=False):
        p = Point(float(lon), float(lat))
        if exclude_first and limit:
            limit = limit + 1
        valor = self.nearest_to_point(p,distance_limit = distance_limit,limit=limit,exclude_first=exclude_first)

        return valor

    def nearest_to_point(self, point,distance_limit=1000,limit=6,exclude_first = False):
        distance_limit = (point,D(mi=distance_limit))

        nearest = self.filter(location__distance_lt = distance_limit).distance(point).order_by('distance')
        if limit and exclude_first:
            nearest = nearest[1:limit]
        elif limit:
            nearest = nearest[0:limit]
        return nearest

    def qs(self):
        return self.get_query_set()
