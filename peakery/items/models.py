import re
import sys, os, urllib
lower = str.lower
from datetime import datetime
from operator import itemgetter
from django.db.models.query_utils import Q
from django.template.defaultfilters import floatformat
from django.template import Context
from django.contrib.humanize.templatetags.humanize import ordinal
from django.contrib.gis.measure import Distance, D
from peakery.django_extensions.db.fields import AutoSlugField, CreationDateTimeField, ModificationDateTimeField
from django.contrib.gis.db import models
from django.conf import settings
from django.urls import reverse
from peakery.cache import cache_manager
from django.template.loader import render_to_string
from django.contrib.gis.geos import Point
from django.template.defaultfilters import slugify
from django.contrib.auth.models import User
from django.contrib.sites.models import Site
from peakery.items.utils import commify
from peakery.favorites.models import Favorite
from django.core.mail import EmailMessage
from peakery.cities.models import Region, Country,City
from peakery.items.managers import <PERSON>emManager, SummitLog<PERSON>anager, PeakRouteManager, PeakRouteArchiveManager
from peakery.accounts.models import UserRelation
from django.db.models import Manager as GeoManager
from django.utils.safestring import mark_safe
from django.db import connection
from peakery.main.redis_queue_singleton import redis_queue

PEAK_NEAR_CITY_MAX_RADIUS = settings.PEAK_NEAR_CITY_MAX_RADIUS
ITEMNEAREST_NOT_USED = 0
ITEMNEAREST_ELEVATION = 1
ITEMNEAREST_NEAR = 2

CATEGORY_IN_ITEMNEAREST_CHOICES = (
    (ITEMNEAREST_NOT_USED, 'Not Used'),
    (ITEMNEAREST_ELEVATION,'Elevation'),
    (ITEMNEAREST_NEAR, 'Near')
    )






def unique_slugify(instance, value, slug_field_name='slug', queryset=None,
                   slug_separator='-'):
    """
    Calculates and stores a unique slug of ``value`` for an instance.

    ``slug_field_name`` should be a string matching the name of the field to
    store the slug in (and the field to check against for uniqueness).

    ``queryset`` usually doesn't need to be explicitly provided - it'll default
    to using the ``.all()`` queryset from the model's default manager.
    """
    slug_field = instance._meta.get_field(slug_field_name)

    slug = getattr(instance, slug_field.attname)
    slug_len = slug_field.max_length

    # Sort out the initial slug, limiting its length if necessary.
    slug = slugify(value)
    if slug_len:
        slug = slug[:slug_len]
    slug = _slug_strip(slug, slug_separator)
    original_slug = slug

    # Create the queryset if one wasn't explicitly provided and exclude the
    # current instance from the queryset.
    if queryset is None:
        queryset = instance.__class__._default_manager.all()
    if instance.pk:
        queryset = queryset.exclude(pk=instance.pk)

    # Find a unique slug. If one matches, at '-2' to the end and try again
    # (then '-3', etc).
    next = 2
    while not slug or queryset.filter(**{slug_field_name: slug}):
        slug = original_slug
        end = '%s%s' % (slug_separator, next)
        if slug_len and len(slug) + len(end) > slug_len:
            slug = slug[:slug_len-len(end)]
            slug = _slug_strip(slug, slug_separator)
        slug = '%s%s' % (slug, end)
        next += 1

    setattr(instance, slug_field.attname, slug)


def _slug_strip(value, separator='-'):
    """
    Cleans up a slug by removing slug separator characters that occur at the
    beginning or end of a slug.

    If an alternate separator is used, it will also replace any instances of
    the default '-' separator with the new separator.
    """
    separator = separator or ''
    if separator == '-' or not separator:
        re_sep = '-'
    else:
        re_sep = '(?:-|%s)' % re.escape(separator)
    # Remove multiple instances and if an alternate separator is provided,
    # replace the default '-' separator.
    if separator != re_sep:
        value = re.sub('%s+' % re_sep, separator, value)
    # Remove separator from the beginning and end of the slug.
    if separator:
        if separator != '-':
            re_sep = re.escape(separator)
        value = re.sub(r'^%s+|%s+$' % (re_sep, re_sep), '', value)
    return value


class Item(models.Model):
    user = models.ForeignKey(User, related_name='items', on_delete=models.SET_DEFAULT, default=1)
    name = models.CharField(max_length=255)
    name_unaccented = models.CharField(max_length=255)
    db_description = models.TextField(null = True, blank = True,verbose_name="description",db_column="description")
    wikipedia_source = models.URLField(null = True, blank = True, verbose_name="Description source")
    slug = AutoSlugField(populate_from=('name',), max_length=255, editable=False, unique=True, overwrite=True, db_index=True)
    slug_new_text = models.CharField(max_length=255, blank=True, db_index=True)
    old_slug = models.CharField(max_length=255, blank=True, db_index=True)
    range = models.CharField(max_length=500, null = True, blank = True)
    location = models.PointField(blank = True)
    lat = models.FloatField(blank=True, null=True, verbose_name='Latitude', db_index=True)
    long = models.FloatField(blank=True, null=True, verbose_name='Longitude', db_index=True)
    elevation = models.FloatField(blank=True, null=True, verbose_name='Elevation', help_text='ft', db_index=True)
    region = models.ManyToManyField(Region, blank=True,through='ItemRegion',help_text='If dont know the region just select the country below', related_name='region_items')
    country = models.ManyToManyField(Country, blank = True,through='ItemCountry', help_text='Only if dont know the region', related_name='country_items', db_index=True)
    has_region = models.BooleanField(default=False,verbose_name='Has Region',help_text='Indicate if a Item has a associated Region')
    has_country = models.BooleanField(default=False,verbose_name='Has Country',help_text='Indicate if a Item has a associated Country')

    prominence = models.FloatField(blank=True, null=True, verbose_name='Prominence' , help_text='ft', db_index=True)
    alternate_names = models.TextField(blank=True, null=True)

    ###Thumbnail Change
    #thumbnail = ThumbnailerImageField(upload_to='items/main', null = True, blank = True)
    #thumbnail = models.TextField(null = True, blank = True)
    thumbnail = models.FileField(upload_to='items/main', null = True, blank = True)
    thumbnail_credit = models.CharField(max_length=50, null = True, blank = True)
    thumbnail_source = models.URLField(null = True, blank = True)
    saved_autodescription = models.TextField(null=True,blank=True)
    flag_for_cache_deletion = models.BooleanField(default=True)
    db_meta_description = models.CharField(max_length=255,null=True,blank=True)

    featured_summitlog_count = models.IntegerField(default=0)
    summitlog_count = models.IntegerField(default=0)

    created = CreationDateTimeField()
    modified = ModificationDateTimeField()

    old_id = models.IntegerField(null = True, blank = True, editable=False)
    txt_location = models.CharField(null = True, blank = True, max_length=512)

    near_city_from_correction = models.BooleanField(default=False)
    default_name = models.BooleanField(default=False)
    location_edited = models.BooleanField(default=False)
    elevation_edited = models.BooleanField(default=False)
    is_classic = models.BooleanField(default=False)
    kom_user = models.IntegerField(null=True, blank=True)
    first_ascent_user = models.IntegerField(null=True, blank=True)
    summit_stewards = models.TextField(default='{}', blank=True)

    active = models.BooleanField(default=True)
    dupe_names = models.BooleanField(default=False)
    deleted = models.BooleanField(default=False)

    objects = ItemManager()

    class Meta:
        permissions = (
            ("change_peak_loc_in_map","Can change peak location in map"),
            )

    def __unicode__(self):
        return self.name

    def __str__(self):
        return self.name

    def delete_summits_latest_cities(self, country_id, region_id):
        # Wildcard city, so it deletes all the cities from cache for a given country and region.
        cache_manager.delete_pattern_summits_latest_json(country_id, region_id, '*')
        cache_manager.delete_pattern_summits_latest_json('0', '0', '*')

    def refresh_latest_cache_for_peak(self):
        # enqueue page loads to refresh cache
        from peakery.items.utils import cache_url
        from threading import Thread
        regions = self.region.all()
        for r in regions:
            # Clear cache
            cache_manager.delete_summits_latest_json('0', str(r.id), '0')
            Thread(target=self.delete_summits_latest_cities, args=(str(r.country.id), str(r.id))).start()
            cache_manager.delete_summits_latest_json(str(r.country.id), '0', '0')

            # Refresh Cache
            link_url = '%sapi/summits/latest/?country_id=&region_id=%s&user_id=&page=1' % (settings.SITE_URL, str(r.id))
            redis_queue.enqueue(cache_url, link_url)
            link_url = '%sapi/summits/latest/?country_id=%s&region_id=&user_id=&page=1' % (settings.SITE_URL, str(r.country.id))
            redis_queue.enqueue(cache_url, link_url)
            link_url = '%sapi/summits/latest/?country_id=%s&region_id=%s&user_id=&page=1' % (settings.SITE_URL, str(r.country.id), str(r.id))
            redis_queue.enqueue(cache_url, link_url)

        # Also delete global cache
        cache_manager.delete_summits_latest_json('0', '0', '0')
        # Refresh global cache
        link_url = '%sapi/summits/latest/?country_id=&region_id=&user_id=&page=1' % settings.SITE_URL
        redis_queue.enqueue(cache_url, link_url)

    def set_peak_slug(self):
        from peakery.items.utils import dictfetchall
        from django.db import connection

        #get item in case name has changed
        peak = Item.objects.get(id=self.id)

        #figure out what the new slug should be
        if self.is_usa():
            #we'll use region name(s) for US peak slugs
            region_slug = ""
            sql = "select c.id, c.name, c.slug " + \
                "from items_item a, items_item_region b, cities_region c " + \
                "where a.id = %s and a.id = b.item_id and b.region_id = c.id and c.country_id = 231 " + \
                "order by c.name asc "

            with connection.cursor() as cursor:
                cursor.execute(sql, [self.id])
                regions = dictfetchall(cursor)
            if regions:
                for r in regions:
                    if region_slug == "":
                        region_slug = region_slug + '-' + r['slug']

            new_slug = slugify(peak.name) + region_slug

            #check if any other peaks have the same slug
            sql = "select count(a.id) as peak_count from items_item a where a.id != %s and a.slug_new_text = %s "
            with connection.cursor() as cursor:
                cursor.execute(sql, [self.id, new_slug])
                result = dictfetchall(cursor)

            if result[0]['peak_count'] > 0:
                #if not a default named peak, add elevation to see if we get a unique slug
                if not self.default_name:
                    new_slug = new_slug + '-' + self.get_elevation_in_feet() + 'ft'

                #check if any other peaks have the same slug
                sql = "select count(a.id) as peak_count from items_item a where a.id != %s and a.slug_new_text = %s "
                with connection.cursor() as cursor:
                    cursor.execute(sql, [self.id, new_slug])
                    result = dictfetchall(cursor)

                if result[0]['peak_count'] > 0:
                    #try adding digits until we get a unique slug
                    test_digit = 2
                    base_slug = new_slug
                    while test_digit < 1000:
                        new_slug = base_slug + '-' + str(test_digit)
                        #check if any other peaks have the same slug
                        sql = "select count(a.id) as peak_count from items_item a where a.id != %s and a.slug_new_text = %s "
                        with connection.cursor() as cursor:
                            cursor.execute(sql, [self.id, new_slug])
                            result = dictfetchall(cursor)
                        if result[0]['peak_count'] == 0:
                            break
                        test_digit += 1
        elif self.is_canada():
            #we'll use region name(s) for CA peak slugs
            region_slug = ""
            sql = "select c.id, c.name, c.slug " + \
                "from items_item a, items_item_region b, cities_region c " + \
                "where a.id = %s and a.id = b.item_id and b.region_id = c.id and c.country_id = 38 " + \
                "order by c.name asc "

            with connection.cursor() as cursor:
                cursor.execute(sql, [self.id])
                regions = dictfetchall(cursor)
            if regions:
                for r in regions:
                    if region_slug == "":
                        region_slug = region_slug + '-' + r['slug']

            new_slug = slugify(peak.name) + region_slug

            #check if any other peaks have the same slug
            sql = "select count(a.id) as peak_count from items_item a where a.id != %s and a.slug_new_text = %s "
            with connection.cursor() as cursor:
                cursor.execute(sql, [self.id, new_slug])
                result = dictfetchall(cursor)

            if result[0]['peak_count'] > 0:
                #if not a default named peak, add elevation to see if we get a unique slug
                if not self.default_name:
                    new_slug = new_slug + '-' + self.get_elevation_in_meters() + 'm'

                #check if any other peaks have the same slug
                sql = "select count(a.id) as peak_count from items_item a where a.id != %s and a.slug_new_text = %s "
                with connection.cursor() as cursor:
                    cursor.execute(sql, [self.id, new_slug])
                    result = dictfetchall(cursor)

                if result[0]['peak_count'] > 0:
                    #try adding digits until we get a unique slug
                    test_digit = 2
                    base_slug = new_slug
                    while test_digit < 1000:
                        new_slug = base_slug + '-' + str(test_digit)
                        #check if any other peaks have the same slug
                        sql = "select count(a.id) as peak_count from items_item a where a.id != %s and a.slug_new_text = %s "
                        with connection.cursor() as cursor:
                            cursor.execute(sql, [self.id, new_slug])
                            result = dictfetchall(cursor)
                        if result[0]['peak_count'] == 0:
                            break
                        test_digit += 1
        else:
            #we'll use country name(s) for non-US/CA peak slugs
            region_slug = ""
            sql = "select c.id, c.name, c.slug " + \
                "from items_item a, items_item_country b, cities_country c " + \
                "where a.id = %s and a.id = b.item_id and b.country_id = c.id " + \
                "order by c.name asc "

            with connection.cursor() as cursor:
                cursor.execute(sql, [self.id])
                regions = dictfetchall(cursor)
            if regions:
                for r in regions:
                    if region_slug == "":
                        region_slug = region_slug + '-' + r['slug']

            new_slug = slugify(peak.name) + region_slug

            #check if any other peaks have the same slug
            sql = "select count(a.id) as peak_count from items_item a where a.id != %s and a.slug_new_text = %s "
            with connection.cursor() as cursor:
                cursor.execute(sql, [self.id, new_slug])
                result = dictfetchall(cursor)

            if result[0]['peak_count'] > 0:
                #if not a default named peak, add elevation to see if we get a unique slug
                if not self.default_name:
                    new_slug = new_slug + '-' + self.get_elevation_in_meters() + 'm'

                #check if any other peaks have the same slug
                sql = "select count(a.id) as peak_count from items_item a where a.id != %s and a.slug_new_text = %s "
                with connection.cursor() as cursor:
                    cursor.execute(sql, [self.id, new_slug])
                    result = dictfetchall(cursor)

                if result[0]['peak_count'] > 0:
                    #try adding digits until we get a unique slug
                    test_digit = 2
                    base_slug = new_slug
                    while test_digit < 1000:
                        new_slug = base_slug + '-' + str(test_digit)
                        #check if any other peaks have the same slug
                        sql = "select count(a.id) as peak_count from items_item a where a.id != %s and a.slug_new_text = %s "
                        with connection.cursor() as cursor:
                            cursor.execute(sql, [self.id, new_slug])
                            result = dictfetchall(cursor)
                        if result[0]['peak_count'] == 0:
                            break
                        test_digit += 1

        #now update the peak with the new slug and flag as having been checked
        sql = "update items_item set old_slug = slug_new_text, slug_new_text = %s where id = %s "
        with connection.cursor() as cursor:
            cursor.execute(sql, [new_slug, self.id])

    def set_dupe_names_flag(self):
        from peakery.items.utils import dictfetchall
        from django.db import connection

        #get item in case name has changed
        peak = Item.objects.get(id=self.id)

        #check if any other peaks have the same name
        sql = "select count(a.id) as peak_count from items_item a where a.id != %s and a.name = %s "
        with connection.cursor() as cursor:
            cursor.execute(sql, [self.id, peak.name])
            result = dictfetchall(cursor)
            if result[0]['peak_count'] > 0:
                #set dupe_names flag to true
                sql = "update items_item set dupe_names = true where id = %s "
                with connection.cursor() as cur:
                    cur.execute(sql, [self.id])

    def set_default_name_flag(self):
        from peakery.items.utils import dictfetchall
        from django.db import connection
        #set default name flag to true
        sql = "update items_item set default_name = case when name like 'Peak %% ft' then true when name like 'Peak %% m' then true else false end " + \
            "where id = %s "
        with connection.cursor() as cursor:
            cursor.execute(sql, [self.id])

    def get_featured_summitlog_count(self, save=True):
        if self.featured_summitlog_count == -1:
            count = self.summits.filter(featured=True).count()
            self.featured_summitlog_count = count
            if save:
                self.save()
        return self.featured_summitlog_count

    def get_summitlog_count(self,save = True):
        if self.summitlog_count == -1:
            count = self.summits.all().count()
            self.summitlog_count = count
            if save:
                if count == 0:
                    self.featured_summitlog_count = 0
                else:
                    self.get_featured_summitlog_count(save=False)
                self.save()
        return self.summitlog_count

    def refresh_summitlog_count(self):
        self.summitlog_count = -1
        self.featured_summitlog_count = -1
        self.get_featured_summitlog_count(save=False)
        self.get_summitlog_count(save=False)
        self.save()

    @property
    def rank(self):
        ranks = []
        if self.has_region:
            for itemregion in self.itemregion_set.all():
                ranks.append((itemregion.get_rank,itemregion.region))
            for itemcountry in self.itemcountry_set.all():
                ranks.append((itemcountry.get_rank,itemcountry.country))
            return ranks
        elif self.has_country:
            for itemcountry in self.itemcountry_set.all():
                ranks.append((itemcountry.get_rank,itemcountry.country))
            return ranks
        return False

    @property
    def prominence_rank(self):
        ranks = []
        if self.has_region:
            for itemregion in self.itemregion_set.all():
                ranks.append((itemregion.get_prominence_rank,itemregion.region))
            for itemcountry in self.itemcountry_set.all():
                ranks.append((itemcountry.get_prominence_rank,itemcountry.country))
            return ranks
        elif self.has_country:
            for itemcountry in self.itemcountry_set.all():
                ranks.append((itemcountry.get_prominence_rank,itemcountry.country))
            return ranks
        return False

    @property
    def description(self):
        if self.db_description:
            return self.db_description
        else:
            return self.autodescription

    @property
    def meta_description(self):
        return self.db_meta_description

    @property
    def autodescription(self):
        if not self.saved_autodescription:
            self.save(autodescription_in_hot=True)
        return self.saved_autodescription

    @property
    def is_wikipedia_source(self):
        ret = False
        if self.wikipedia_source:
            if 'wikipedia.org' in self.wikipedia_source:
                ret = True
        return ret

    @property
    def description_source(self):
        return self.wikipedia_source

    @property
    def description_source_domain(self):
        from urlparse import urlparse
        ret = ''
        if self.wikipedia_source:
            parse = urlparse(self.wikipedia_source)
            ret = parse.netloc
        return ret

    def create_meta_description(self):
        if self.range:
            text = """
            %(username)s %(date)s climb of %(peak_name)s, a %(peak_elevation)s mountain in the %(peak_range)s of %(peak_location)s.
            """ % ({'username':self.user.username, 'date': self.created, 'peak_name':self.name,'peak_elevation':self.get_elevation(),'peak_range':self.range,'peak_location': self.get_ubication_names_title()})
        else:
            text = """
            %(username)s %(date)s climb of %(peak_name)s, a %(peak_elevation)s mountain.
            """ % ({'username':self.user.username, 'date': self.created, 'peak_name':self.name,'peak_elevation':self.get_elevation(),'peak_range':self.range,'peak_location': self.get_ubication_names_title()})
        text = text.strip()
        if (self.country.filter(name='United States')) and len(text) < 160:
            text = text.replace(', United States','')
        text = text[:160]
        return text

    def fix_fields_with(self,field=None,value = None):
        if field == 'elevation':
            self.elevation = Distance(ft=value).ft
            self.elevation_edited = True
        if field == 'prominence':
            self.prominence = Distance(ft=value).ft
        elif field == 'coords':
            self.lat = value.y
            self.long = value.x
            self.location_edited = True
        elif field == 'range':
            self.range = value
        elif field == 'name':
            self.name = value
        elif field == 'nearest_city':
            self.saved_autodescription = self.generate_autodescription(nearest_city=value)
            self.near_city_from_correction = True
            cache_manager.set_peak_nearest_city(str(self.id), value)
            self.quick_save()
        if field in ['lat','long']:
            point = Point(self.lat,self.long)
            self.location = point
            self.location_edited = True
        if field not in ['other', 'coords']:
            self.quick_save()
        if field in ['coords']:
            self.quick_save()

    def send_approved_email(self, tempitem=None, item=None):
        subject = '%(name)s has been added to peakery' % self.__dict__
        body = render_to_string('emails/approved_item.html', {'peak':item,'user':self.user,'tempitem':tempitem,'site_url':settings.SITE_URL})
        #email_message(subject, body, to=[self.user.email], from_email=settings.DEFAULT_FROM_EMAIL_FEEDBACK)
        recipients = []
        recipients.append(self.user.email)
        msg = EmailMessage(subject,body,settings.DEFAULT_FROM_EMAIL_NOTIFICATIONS,recipients)
        msg.content_subtype = "html"
        msg.send()

    def fix_rank_for_autodescription(self,rank):
        rank0 = ordinal(rank[0].replace(',',''))
        rank1 = rank[1]
        return (rank0,rank1)

    def get_peak_rank_text(self):
        peak_ranks = map(self.fix_rank_for_autodescription,self.all_ranks())
        peak_rank_text = ""
        for index in range(0,len(peak_ranks)):
            if index > 0 and index < len(peak_ranks) - 1:
                peak_rank_text = "%s , " % peak_rank_text
            elif index >0 and index == len(peak_ranks) -1:
                peak_rank_text = "%s and the " % peak_rank_text
            if (isinstance(peak_ranks[index][1],Country) and
                (peak_ranks[index][1].name == "United States" or peak_ranks[index][1].name == "United Kingdom" )):
                peak_rank_text = "%s%s highest mountain in the %s" % (peak_rank_text,peak_ranks[index][0],peak_ranks[index][1].name)
            else:
                peak_rank_text = "%s%s highest mountain in %s" % (peak_rank_text,peak_ranks[index][0],peak_ranks[index][1].name)
        return peak_rank_text

    def get_prominence_region_rank(self):
        return "test"

    def get_peak_nearest_items_text(self):
        nearest_items = Item.objects.nearest_to(self.lat, self.long, limit=6,exclude_first=True)
        text = ""
        for index in range(0,len(nearest_items)):
            if index > 0 and index < len(nearest_items) - 1:
                text = "%s , " % text
            elif index > 0 and index == len(nearest_items) -1:
                text = "%s and " % text
            text = "%s%s" % (text,nearest_items[index].name)
        return text

    def get_items_nearby(self):
        return ""

    #Roll back changes 12/03/2012
    def get_nearest_city(self):
        if self.id:
            cache_val = cache_manager.get_peak_nearest_city(str(self.id))
            if cache_val:
                return cache_val
        distance_limit = D(mi=PEAK_NEAR_CITY_MAX_RADIUS)
        if self.has_region and self.region.all():
            region = self.region.all()[0]
            point = Point(float(self.lat), float(self.long))
            from django.contrib.gis.db.models.functions import Distance
            nearest_city = region.cities.filter(
                location__distance_lte=(point, distance_limit)
            ).annotate(distance=Distance('location', point)).order_by('distance')[:1]
            if nearest_city:
                nearest_city = nearest_city[0]
            else:
                nearest_city = None
        else:
            nearest_city = City.objects.nearest_to(self.lat,self.long, limit= distance_limit)
        cache_val = nearest_city
        cache_manager.set_peak_nearest_city(str(self.id), cache_val)
        return cache_val

    def generate_autodescription(self, nearest_city = None):
        nearest_items = Item.objects.nearest_to(self.lat, self.long, limit=6,exclude_first=True)
        #print nearest_items
        peak_location = self.get_ubication_names_title()
        # if field ends in word Range or Ranges or Mountains or Ridge or Highlands or ends in a word with the letter s,
        # dont display text Range at end of field, else display word Range at end of field.
        show_range_word = True
        words = ["range","ranges","mountains","ridge","highlands","s"]
        if self.range:
            temprange = self.range.lower()
            for word in words:
                if temprange.endswith(word):
                    show_range_word = False
                    break
        if peak_location == 'United States' or peak_location == 'United Kingdom':
            peak_location = 'the %s' % peak_location
        if nearest_city:
            peak_nearest_city = nearest_city
        else:
            peak_nearest_city = self.get_nearest_city()
        context = Context({'peak':self,'peak_name':self.name,'peak_elevation':self.get_elevation(),'peak_range':self.range,'peak_location': peak_location
            ,'peak_rank':self.get_peak_rank_text(),'peak_name_mountain': self.name.replace('Mount ','Mt. ') ,
                           'peak_nearest_items':nearest_items,'peak_lists' : ItemGroup.objects.filter(items=self),
                           'peak_nearest_city':peak_nearest_city,
                           'show_range_word': show_range_word
        })
        return render_to_string("items/peak_autodescription.html",context_instance=context)

    def get_absolute_path(self):
        try:
            path = reverse('peak_view',args=[self.slug_new_text])
        except:
            path = ""
        return path


    def get_absolute_url(self):
        val = '%s%s' % (settings.SITE_URL[:-1], reverse('peak_view', args=[self.slug_new_text]))
        return val

    def get_region_url(self):
        domain = Site.objects.get_current().domain
        return 'http://%s/%s' % (domain, (self.name + '-mountains'))

    def fix_item_name(self):
        changes = {u"\u2019":"'" , "`":"'",u"\u00B4":"'"}
        temp = self.name
        for key in changes.keys():
            if key in temp:
                temp = temp.replace(key,changes[key])
        if temp != self.name:
            self.name = temp

    def quick_save(self, *args, **kwargs):
        #self.slug_new_text = self.get_new_style_slug
        #unique_slugify(self, self.slug_new_text, slug_field_name='slug_new_text')
        super(Item, self).save(*args, **kwargs)
        self.set_default_name_flag()
        self.set_peak_slug()
        self.set_dupe_names_flag()

    def save_summitlog_count(self, *args, **kwargs):
        super(Item, self).save(*args, **kwargs)

    def save(self, *args, **kwargs):
        is_task = kwargs.pop("is_task", False)
        autodescription_in_hot = kwargs.pop("autodescription_in_hot", False)
        metadescription_in_hot = kwargs.pop("metadescription_in_hot", False)
        audit = kwargs.pop("audit",False)

        old_location = self.location
        new_location = Point(self.long, self.lat)
        self.fix_item_name()

        self.db_meta_description = self.create_meta_description()

        #For querys with unaccent
        #self.name_unaccented = unaccent_name(self.name)

        if audit:
            itemaudit = Itemauditlogentry(location = new_location, location_old = old_location ,
                                          peak_id = self.id, action_user = audit, name = self.name, action_type="U",
                                          action_date = datetime.now()
            )
            itemaudit.save()

        if old_location != new_location:
            self.location = new_location

        #self.slug_new_text = self.get_new_style_slug
        #unique_slugify(self, self.slug_new_text, slug_field_name='slug_new_text')
        super(Item, self).save(*args, **kwargs)
        #Hack : In Prod Server the upload_to field appears not to work
        if self.thumbnail and not self.thumbnail.name.startswith('items/'):
            self.thumbnail = 'items/main/%s' % self.thumbnail
            super(Item, self).save(*args, **kwargs)
        self.set_default_name_flag()
        self.set_peak_slug()
        self.set_dupe_names_flag()

    def save_location(self, *args, **kwargs):
        audit = kwargs.pop("audit",False)

        #Item.
        old_location = self.location
        new_location = Point(self.long, self.lat)

        if audit:
            itemaudit = Itemauditlogentry(location = new_location, location_old = old_location ,
                                          peak_id = self.id, action_user = audit, name = self.name, action_type="U",
                                          action_date = datetime.now()
            )
            itemaudit.save()

        change_location = False
        if old_location != new_location:
            self.location = new_location
            self.location_edited = True
            change_location = True

        super(Item, self).save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        from django.db import connection
        #Ticket #1774: Admin: get error when try to delete a peak that has summit logs
        for correction in ItemCorrection.objects.filter(item=self):
            correction.delete()
        summits = self.summits.all()
        for summit in summits:
            notices = summit.notice_set.all().delete()
        #delete any item_geocode data
        sql = "delete from items_item_geocode where item_id = %s "
        with connection.cursor() as cursor:
            cursor.execute(sql, [self.id])
        #nearest_items = Item.objects.nearest_to(self.lat, self.long, limit=10, exclude_first=True)
        self.delete_ubications_cache()
        super(Item, self).delete(*args, **kwargs)


    def delete_full_cache(self):
        try:
            self.delete_ubications_cache()
        except:
            pass

    def delete_ubications_cache(self):
        pass

    def get_big_thumbnail(self):
        if self.thumbnail:
            ext = self.thumbnail.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return settings.MEDIA_URL+'images/'+self.thumbnail.name.replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.1250x600_q95_crop--0%2C0_upscale.'+ext
        else:
            return settings.S3_URL+'img/default.png'

    def get_thumbnail_85_crop_top(self):
        if self.thumbnail:
            ext = self.thumbnail.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return settings.MEDIA_URL+'images/'+self.thumbnail.name.replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.85x85_q95_crop-top.'+ext
        else:
            return settings.S3_URL+'img/default.png'

    def get_thumbnail_160_crop(self):
        if self.thumbnail:
            ext = self.thumbnail.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return settings.MEDIA_URL+'images/'+self.thumbnail.name.replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.160x150_q95_crop.'+ext
        else:
            return settings.S3_URL+'img/default.png'

    def get_thumbnail_240(self):
        if self.thumbnail:
            ext = self.thumbnail.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return settings.MEDIA_URL+'images/'+self.thumbnail.name.replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.240x180_q95_crop.'+ext
        else:
            return settings.S3_URL+'img/default.png'


    def get_thumbnail_path_240(self):
        if self.thumbnail:
            ext = self.thumbnail.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return 'images/'+self.thumbnail.name.replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.240x180_q95_crop.'+ext
        else:
            return 'img/default.png'

    def get_thumbnail_290_top(self):
        if self.thumbnail:
            ext = self.thumbnail.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return settings.MEDIA_URL+'images/'+self.thumbnail.name.replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.290x220_q95_crop-top.'+ext
        else:
            return settings.S3_URL+'img/default.png'

    def get_thumbnail_320(self):
        if self.thumbnail:
            ext = self.thumbnail.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return settings.MEDIA_URL+'images/'+self.thumbnail.name.replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.320x240_q95.'+ext
        else:
            return settings.S3_URL+'img/default.png'

    def get_thumbnail_path_320(self):
        if self.thumbnail:
            ext = self.thumbnail.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return 'images/'+self.thumbnail.name.replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.320x240_q95.'+ext
        else:
            return 'img/default.png'

    def get_thumbnail_480(self):
        if self.thumbnail:
            ext = self.thumbnail.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return settings.MEDIA_URL+'images/'+self.thumbnail.name.replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.480x360_q95.'+ext
        else:
            return settings.S3_URL+'img/default.png'

    def get_thumbnail_path_480(self):
        if self.thumbnail:
            ext = self.thumbnail.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return 'images/'+self.thumbnail.name.replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.480x360_q95.'+ext
        else:
            return 'img/default.png'

    def get_thumbnail_745(self):
        if self.thumbnail:
            ext = self.thumbnail.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return settings.MEDIA_URL+'images/'+self.thumbnail.name.replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.745x500_q95_crop-top.'+ext
        else:
            return settings.S3_URL+'img/default.png'

    def thumbnail_745(thumbnail_name):
        if thumbnail_name:
            ext = thumbnail_name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return settings.MEDIA_URL+'images/'+thumbnail_name.replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.745x500_q95_crop-top.'+ext
        else:
            return settings.S3_URL+'img/default.png'

    def get_nearest_peak_thumbnail(self):
        if self.thumbnail:
            ext = self.thumbnail.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return settings.MEDIA_URL+'images/'+self.thumbnail.name.replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.480x360_q95.'+ext
        else:
            return settings.S3_URL+'img/default.png'

    def get_thumbnail_path_745(self):
        if self.thumbnail:
            ext = self.thumbnail.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return 'images/'+self.thumbnail.name.replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.745x500_q95_crop-top.'+ext
        else:
            return 'img/default.png'

    def get_thumbnail_910(self):
        if self.thumbnail:
            ext = self.thumbnail.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return settings.MEDIA_URL+'images/'+self.thumbnail.name.replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.910x680_q95.'+ext
        else:
            return settings.S3_URL+'img/default.png'

    def get_thumbnail_1920(self):
        if self.thumbnail:
            ext = self.thumbnail.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return settings.MEDIA_URL+'images/'+self.thumbnail.name.replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.1920x1440_q95_crop.'+ext
        else:
            return settings.S3_URL+'img/default.png'

    def get_thumbnail_1920_image(thumb_name):
        if thumb_name:
            ext = thumb_name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return settings.MEDIA_URL+'images/'+thumb_name.replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.1920x1440_q95_crop.'+ext
        else:
            return settings.S3_URL+'img/default.png'

    def get_thumbnail_65(self):
        if self.thumbnail:
            ext = self.thumbnail.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return settings.MEDIA_URL+'images/'+self.thumbnail.name.replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.65x49_q95_crop.'+ext
        else:
            return settings.S3_URL+'img/default.png'

    def get_thumbnail(self):
        if self.thumbnail:
            ext = self.thumbnail.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return settings.MEDIA_URL+'images/'+self.thumbnail.name.replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.320x190_q95_crop.'+ext
        else:
            return settings.S3_URL+'img/default.png'

    def get_thumbnail_size(self, width, height):
        if self.thumbnail:
            thumbnail_options = dict(size=(width, height), crop=True)
            try:
                image = get_thumbnailer(self.thumbnail).get_thumbnail(thumbnail_options)
            except:
                return '/static/img/default.png'
            return '/static/%s' % image
        else:
            return 'img/default.png'

    def get_thumbnail_50px(self):
        if self.thumbnail:
            thumbnail_options = dict(size=(50, 50), crop=True)
            try:
                image = get_thumbnailer(self.thumbnail).get_thumbnail(thumbnail_options)
            except:
                return '/static/img/default.png'
            return '/static/%s' % image
        else:
            return '/static/img/default.png'

    def get_thumbnail_120px(self):
        if self.thumbnail:
            thumbnail_options = dict(size=(120, 90), crop=True)
            try:
                image = get_thumbnailer(self.thumbnail).get_thumbnail(thumbnail_options)
            except:
                return '/static/img/default.png'
            return '/static/%s' % image
        else:
            return '/static/img/default.png'

    def get_thumbnail_data_class(self):
        if self.thumbnail:
            return 'user-photo-info'
        else:
            return 'empty-photo-info'

    def get_thumbnail_hover_class(self):
        if self.thumbnail:
            return 'hover-photos'
        else:
            return ''

    def has_thumbnail(self):
        if self.thumbnail == '':
            return False
        return True

    def get_main_photo(self):
        photos = self.photos.filter(category__name='peak').order_by('-created')
        if photos:
            for photo in photos:
                if photo.image and self.thumbnail:
                    if photo.image.name and self.thumbnail:
                        if str(photo.image.name) == str(self.thumbnail):
                            return photo
        return False

    def get_main_photo_user(self):
        user = self.get_main_photo().user
        if user:
            return user
        return False

    def get_last_bag(self):
        #if self.get_featured_summitlog_count() > 0:
            #summit = self.summits.filter(date_entered=True, attempt=False).order_by('-date_entered','-date')[:1]
            #if summit:
                #return summit[0]
        #return False
        summit = self.summits.filter(attempt=False).order_by('-date_entered','-date')[:1]
        if summit:
            return summit[0]
        else:
            return False

    def get_last_bag_unfeatured(self):
        summit = self.summits.filter(attempt=False).order_by('-date')[:1]
        if summit:
            return summit[0]
        return False

    def get_ubication_names(self):
        ubications = cache_manager.get_ubication_names(self.id)
        if ubications is None:
            ubication_regions = self.region.all()
            ubication_countries = self.country.all()
            ubication_countries = [country for country in ubication_countries if country not in map(lambda x:x.country,ubication_regions)]
            ubications = list(ubication_regions) + list(ubication_countries)
            cache_manager.set_ubication_names(self.id, ubications)
        return ubications

    def get_ubication_names_title(self):
        ubications = self.get_ubication_names()
        ubication_names = ' , '.join(map(lambda x: x.get_ubication_onlyname_title(),ubications))
        return ubication_names

    def get_explore_absolute_url(self):
        if self.region:
            return self.region.get_absolute_url_map()
        else:
            return self.country.get_absolute_url_map()

    def get_ubicacion_onlyname(self):
        if self.region:
            return self.region.name
        else:
            return self.country.name

    def get_ubicacion_onlycountryname(self):
        country = self.country.all().first()
        if country:
            return country.name

    def get_ubicacion_onlyname_title(self):
        raise KeyError

    def get_peakname_title(self):
        if self.name[:6] == 'Mount ':
            return 'Mt %s' % self.name[6:]
        else:
            return self.name

    def get_peaklocation_title(self):
        if self.is_usa() or self.is_canada() or self.is_unitedkingdom():
            region = self.region.all().first()
            if region:
                return region.name
            else:
                return ''
        else:
            country = self.country.all().first()
            if country:
                return country.name
            else:
                return ''

    def get_peak_elevation_for_meta(self):
        f = self.elevation
        if self.is_usa():
            if f:
                return '%s ft' % (commify(int(f)))
            else:
                return ''
        else:
            if f:
                #m = floatformat(f * 0.3048, 0)
                m = floatformat(round(f * 0.3048))
                return '%s m' % (commify(m))
            else:
                return ''

    def get_peak_title_for_meta(self):
        if self.range:
            range = " the %s of" % self.range
        else:
            range = ""
        peaktitle = "%s (%s) in%s %s" % (self.name, self.get_peak_elevation_for_meta(), range, self.get_peaklocation_title())
        return peaktitle

    def get_peak_meta_description(self):
        if self.summitlog_count > 1:
            trip_reports = "%s trip reports" % self.summitlog_count
        else:
            trip_reports = "trip reports"
        description = "Hiking info, trail maps, and %s from %s" % (trip_reports, self.get_peak_title_for_meta())
        return description

    def get_peak_summits_meta_description(self):
        if self.summitlog_count > 1:
            trip_reports = "%s trip reports" % self.summitlog_count
        else:
            trip_reports = "Trip reports"
        description = "%s from %s" % (trip_reports, self.get_peak_title_for_meta())
        return description

    def get_peak_routes_meta_description(self, routes):
        if routes:
            most_popular_route = routes[0]['name']
            if len(routes) > 1:
                description = "%s trails and routes up %s including %s" % (len(routes), self.get_peak_title_for_meta(), most_popular_route)
            else:
                description = "Trails and routes up %s including %s" % (self.get_peak_title_for_meta(), most_popular_route)
        else:
            description = "Trails and routes up %s" % self.get_peak_title_for_meta()
        return description

    def get_elevation(self):
        f = self.elevation
        if f:
            #m = floatformat(f * 0.3048, 0)
            m = floatformat(round(f * 0.3048))
            return '%s ft / %s m' % (commify(int(f)), commify(m))
        return ''

    def get_elevation_formatted(elevation):
        if elevation:
            m = floatformat(round(elevation * 0.3048))
            return '%s ft / %s m' % (commify(int(elevation)), commify(m))
        return ''

    def get_elevation_in_feet(self):
        f = self.elevation
        if f:
            return '%s' % (int(f))
        return ''

    def get_elevation_in_meters(self):
        f = self.elevation
        if f:
            #m = floatformat(f * 0.3048, 0)
            m = floatformat(round(f * 0.3048))
            return '%s' % (int(m))
        return ''

    def get_prominence(self):
        f = self.prominence
        if f or f == 0:
            #m = floatformat(f * 0.3048, 0)
            m = floatformat(round(f * 0.3048))
            return '%s ft / %s m' % (commify(int(f)), commify(m))
        return ''

    def get_prominence_in_feet(self):
        f = self.prominence
        if f:
            return '%s' % (int(f))
        return ''

    def get_prominence_in_meters(self):
        f = self.prominence
        if f:
            #m = floatformat(f * 0.3048, 0)
            m = floatformat(round(f * 0.3048))
            return '%s' % (int(m))
        return ''

    def has_summits(self):
        res = False
        count = self.summits.count()
        if count > 0:
            res = True
        return res

    def regions_count(self):
        count = self.region.count()
        return count

    def countries_count(self):
        count = self.country.count()
        return count

    def is_usa(self):
        count = ItemCountry.objects.filter(country__code='US', item=self).count()
        if count > 0:
            return True
        else:
            return False

    def is_usa_but_not_alaska(self):
        country_count = ItemCountry.objects.filter(country__code='US', item=self).count()
        region_count = ItemRegion.objects.filter(region__code='US-AK', item=self).count()
        if country_count > 0 and region_count == 0:
            return True
        else:
            return False

    def is_canada(self):
        count = ItemCountry.objects.filter(country__code='CA', item=self).count()
        if count > 0:
            return True
        else:
            return False

    def is_unitedkingdom(self):
        count = ItemCountry.objects.filter(country__code='UK', item=self).count()
        if count > 0:
            return True
        else:
            return False

    def is_usa_canada_or_new_zealand(self):
        count = ItemCountry.objects.filter(country__code__in=['US','CA','NZ'], item=self).count()
        if count > 0:
            return True
        else:
            return False

    def support_topo_govt_map(self):
        count = ItemCountry.objects.filter(country__code__in=['US','CA','NZ','MX','IS','NO','SE','FI'], item=self).count()
        if count > 0:
            return True
        else:
            return False

    def was_summited_by(self, user):
        if user.id:
            return SummitLog.objects.filter(item=self, user=user).exists()
        return False

    def other_baggers(self):
        summits = SummitLog.objects.select_related('user').filter(item=self, log=None).order_by('?')
        return summits

    def get_group(self):
        group = self.groups.all()
        if group:
            return group[0]
        return False

    def get_group_list(self):
        group = self.groups.all()
        return group

    def get_group_url(self):
        group = self.get_group()
        if group:
            return group.get_absolute_url()
        return ''

    def get_group_name(self):
        group = self.get_group()
        if group:
            return group.name

    def get_summits_routes_list(self):
        val = cache_manager.get_peak_summits_routes_list(self.pk)
        if val is not False:
            summits = self.summits.select_related('route_up','route_down').all()
            if summits:
                list = []
                for s in summits:
                    if s.route_up:
                        list.append(s.route_up)
                    if s.route_down:
                        list.append(s.route_down)
                total = len(list) * 1.0
                result = [(x,list.count(x)) for x in set(list)]
                final = []
                for r in result:
                    route = r[0]
                    cant = r[1]
                    percent = (cant/total)*100
                    final.append( (route, int(percent), percent*3) )
                final = sorted(final, key=itemgetter(1), reverse=True)
            else:
                final = False
            cache_manager.set_peak_summits_routes_list(self.pk, final)
        return val

    def highest_within_250_miles(self):
        near = self.items_nearest.filter(category = ITEMNEAREST_ELEVATION)
        if not near:
            near = ItemNearest.objects.create(item = self, category = ITEMNEAREST_ELEVATION)
        else:
            near = near[0]
        return near.get_nearest()

    def get_nearest_to(self):
        near = self.items_nearest.filter(category = ITEMNEAREST_NEAR)
        if not near:
            near = ItemNearest.objects.create(item = self, category = ITEMNEAREST_NEAR)
        else:
            near = near[0]
        return near.get_nearest()

    def has_thumbnail_credit(self):
        if self.thumbnail_credit or self.thumbnail_source:
            return True
        return False

    def all_ranks(self):
        ranks = []
        if self.has_region:
            for region in self.region.all():
                items = Item.objects.filter(region=region).order_by('-elevation').values_list('id', flat=True)
                items = list(items)
                ranks.append((commify(items.index(self.id) + 1),region))
        if self.has_country:
            for country in self.country.all():
                items = Item.objects.filter(country=country).order_by('-elevation').values_list('id',flat=True)
                items= list(items)
                ranks.append((commify(items.index(self.id)+1),country))
        return ranks

    def get_html_tooltip(self):
        return render_to_string('items/ajax/tooltip.html', {'i':self})

    def get_html_tooltip_small(self):
        return render_to_string('items/ajax/tooltip_small.html', {'i':self})

    def can_be_homepage(self):
        if self.has_thumbnail():
            if self.thumbnail.width > 990 and self.thumbnail.height > 610:
                return True
        return False

    def fix_elevation_from_point(self):
        from importer import importer
        elevation = importer.resolve_elevation(self.lat,self.long)
        if elevation:
            self.elevation = elevation
            self.save()

    def fix_region_from_geonames(self):
        from importer import importer
        resolved = False
        #print self.slug
        if not self.has_region:
            response = importer.resolve_region_from_geonames(self.lat, self.long, return_country=True)
            if response:
                resolved = True
                if isinstance(response, Region):
                    try:
                        item_region = ItemRegion.objects.create(item=self, region=response)
                        if not self.country.filter(id=response.country.id).exists():
                            item_country = ItemCountry.objects.create(item=self, country=response.country)
                    except:
                        print(sys.exc_info()[0])

                elif isinstance(response, Country):
                    if not self.country.filter(id=response.id).exists():
                        item_country = ItemCountry.objects.create(item=self, country=response)
                    else:
                        pass
            else:
                pass
        else:
            pass

    def summit_times_by_user(self, user):
        if user:
            return SummitLog.objects.filter(item=self, user=user).count()
        return None

    def fix_item_without_country(self):
        from importer import importer
        try:
            location = importer.resolve_only_country_from_geonames(self.lat,self.long)
            if isinstance(location,Country):
                self.fix_country_item(location)
        except:
            pass

    def fix_country_item(self, country):
        item_country = ItemCountry.objects.create(item=self, country=country)
        item_country.save()

    @property
    def get_new_style_slug(self):

        sluggy_name = slugify(self.name)

        if sluggy_name.startswith('point-'):
            sluggy_name = '-'.join(sluggy_name.split('-')[:3])

        try:
            if self.range:
                return '%s-%s' % (sluggy_name, slugify(self.range))
            else:
                if (231,) in self.country.values_list('pk'):
                    return '%s-%s' % (sluggy_name, slugify(self.region.all()[0].name))
                else:
                    return '%s-%s' % (sluggy_name, slugify(self.country.all()[0].name))
        except:
            return slugify(self.name)

class ItemNearest(models.Model):
    item = models.ForeignKey(Item, related_name='items_nearest', on_delete=models.DO_NOTHING)
    near1 = models.ForeignKey(Item, related_name='items_nearest_1', null=True, blank=True, default=None, on_delete=models.DO_NOTHING)
    near1_distance = models.FloatField(null=True,blank=True, default = 0)
    near2 = models.ForeignKey(Item, related_name='items_nearest_2', null=True, blank=True, default=None, on_delete=models.DO_NOTHING)
    near2_distance = models.FloatField(null=True,blank=True, default = 0)
    near3 = models.ForeignKey(Item, related_name='items_nearest_3', null=True, blank=True, default=None, on_delete=models.DO_NOTHING)
    near3_distance = models.FloatField(null=True,blank=True, default = 0)
    near4 = models.ForeignKey(Item, related_name='items_nearest_4', null=True, blank=True, default=None, on_delete=models.DO_NOTHING)
    near4_distance = models.FloatField(null=True,blank=True, default = 0)
    near5 = models.ForeignKey(Item, related_name='items_nearest_5', null=True, blank=True, default=None, on_delete=models.DO_NOTHING)
    near5_distance = models.FloatField(null=True,blank=True, default = 0)
    near6 = models.ForeignKey(Item, related_name='items_nearest_6', null=True, blank=True, default=None, on_delete=models.DO_NOTHING)
    near6_distance = models.FloatField(null=True,blank=True, default = 0)
    category = models.SmallIntegerField(choices=CATEGORY_IN_ITEMNEAREST_CHOICES, default=0, blank=False, null=False)

    def save(self, *args, **kwargs):
        if self.category == ITEMNEAREST_ELEVATION:
            self.highest_within_250_miles()
        elif self.category == ITEMNEAREST_NEAR:
            self.get_nearest_items()
        super(ItemNearest, self).save(*args, **kwargs)

    # TODO: Delete method for invalidate.
    def delete(self, *args, **kwargs):
        super(ItemNearest, self).delete(*args, **kwargs)
        query_filter = Q(near1 = self.item) | Q(near2 = self.item) | Q(near3 = self.item) | Q(near4 = self.item) | Q(near5 = self.item) | Q(near6 = self.item)
        ItemNearest.objects.filter(query_filter, category = self.category).delete()

    def get_nearest(self):
        nears = []
        if self.category == ITEMNEAREST_ELEVATION:
            nears = [self.near1,self.near2,self.near3,self.near4]
        elif self.category == ITEMNEAREST_NEAR:
            if self.near1:
                self.near1.distance = self.near1_distance
            if self.near2:
                self.near2.distance = self.near2_distance
            if self.near3:
                self.near3.distance = self.near3_distance
            if self.near4:
                self.near4.distance = self.near4_distance
            if self.near5:
                self.near5.distance = self.near5_distance
            if self.near6:
                self.near6.distance = self.near6_distance
            nears =  [self.near1,self.near2,self.near3,self.near4, self.near5, self.near6]
        nears = [x for x in nears if x is not None]
        return nears

    def highest_within_250_miles(self):
        area = (self.item.location,Distance(mi=250))
        nearest = list(Item.objects.filter(location__distance_lte = area).order_by('-elevation')[:4])
        nearest+= [None]*4
        nearest = nearest[:4]
        self.near1, self.near2, self.near3, self.near4 = nearest
        self.near5 = None
        self.near6 = None

    def get_nearest_items(self):
        nearest_items = list(Item.objects.nearest_to(self.item.lat, self.item.long, limit=6, exclude_first=True))
        nearest_items+= [None]*6
        nearest_items = nearest_items[:6]
        self.near1,self.near2,self.near3,self.near4,self.near5,self.near6 = nearest_items
        self.near1_distance, self.near2_distance, self.near3_distance,  self.near4_distance, self.near5_distance, self.near6_distance = [0]*6
        try:
            self.near1_distance = self.near1.distance.mi
            self.near2_distance = self.near2.distance.mi
            self.near3_distance = self.near3.distance.mi
            self.near4_distance = self.near4.distance.mi
            self.near5_distance = self.near5.distance.mi
            self.near6_distance = self.near6.distance.mi
        except:
            pass


class ItemRegion(models.Model):
    item = models.ForeignKey(Item, on_delete=models.DO_NOTHING)
    region = models.ForeignKey(Region, on_delete=models.DO_NOTHING)
    rank = models.IntegerField(blank=True, null=True, default=0)
    prominence_rank = models.IntegerField(blank=True, null=True, default=0)

    class Meta:
        db_table='items_item_region'
        verbose_name_plural = 'Regions'

    def __unicode__(self):
        str = 'Item:%s at Region: %s' % (self.item.__unicode__(),self.region.__unicode__())
        return str

    def save(self,*args,**kwargs):
        invalidate = kwargs.pop('invalidate',True)
        super(ItemRegion, self).save(*args, **kwargs)
        if not self.item.has_region:
            self.item.has_region = True
            self.item.quick_save()
        if invalidate:
            ItemRegion.objects.filter(region = self.region).update(rank = 0)

    def delete(self,*args,**kwargs):
        invalidate = kwargs.pop('invalidate',True)
        regions = self.item.regions_count() - 1
        super(ItemRegion,self).delete(*args,**kwargs)
        print("change has_region",regions)
        if regions <= 0:
            self.item.has_region = False
            self.item.save()
        if invalidate:
            ItemRegion.objects.filter(region = self.region).update(rank = 0)

    @property
    def get_rank(self):
        if self.rank == 0:
            self.rank_region()
        return self.rank

    def rank_region(self):
        items = Item.objects.filter(region=self.region).order_by('-elevation').values_list('id', flat=True)
        rank_list = list(items)
        self.rank = rank_list.index(self.item_id) + 1
        self.save(invalidate = False)

    def get_prominence_rank(self):
        if self.prominence_rank == 0:
            self.prominence_rank_region()
        return self.prominence_rank

    def prominence_rank_region(self):
        items = Item.objects.filter(region=self.region, prominence__isnull=False).order_by('-prominence').values_list('id', flat=True)
        rank_list = list(items)
        self.prominence_rank = rank_list.index(self.item_id) + 1
        self.save(invalidate = False)


class ItemCountry(models.Model):
    item = models.ForeignKey(Item, on_delete=models.DO_NOTHING)
    country = models.ForeignKey(Country, on_delete=models.DO_NOTHING)
    rank = models.IntegerField(blank=True, null=True, default = 0)
    prominence_rank = models.IntegerField(blank=True, null=True, default = 0)

    def __unicode__(self):
        str = 'Item:%s at Region: %s' % (self.item.__unicode__(),self.country.__unicode__())
        return str

    class Meta:
        db_table='items_item_country'
        verbose_name_plural = 'Countries'

    def save(self,*args,**kwargs):
        invalidate = kwargs.pop('invalidate',True)
        super(ItemCountry, self).save(*args, **kwargs)
        if not self.item.has_country:
            self.item.has_country = True
            self.item.quick_save()
        if invalidate:
            ItemCountry.objects.filter(country = self.country).update(rank = 0)

    def quick_save(self,*args,**kwargs):
        super(ItemCountry, self).save(*args, **kwargs)

    def delete(self,*args,**kwargs):
        invalidate = kwargs.pop('invalidate',True)
        countries = self.item.countries_count() - 1
        super(ItemCountry,self).delete(*args,**kwargs)
        if countries <= 0:
            self.item.has_country = False
            self.item.save()
        if invalidate:
            ItemCountry.objects.filter(country = self.country).update(rank = 0)

    @property
    def get_rank(self):
        if self.rank == 0:
            self.rank_country()
        return self.rank

    def rank_country(self):
        items = Item.objects.filter(country=self.country).order_by('-elevation').values_list('id',flat=True)
        rank_list = list(items)
        self.rank = rank_list.index(self.item_id) + 1
        self.save(invalidate = False)

    @property
    def get_prominence_rank(self):
        if self.prominence_rank == 0:
            self.prominence_rank_country()
        return self.prominence_rank

    def prominence_rank_country(self):
        items = Item.objects.filter(country=self.country, prominence__isnull=False).order_by('-prominence').values_list('id',flat=True)
        rank_list = list(items)
        self.prominence_rank = rank_list.index(self.item_id) + 1
        self.save(invalidate = False)

    def do_elevation_rank_country(self):
        items = Item.objects.filter(country=self.country).order_by('-elevation').values_list('id',flat=True)
        rank_list = list(items)
        self.rank = rank_list.index(self.item_id) + 1
        self.quick_save()


class ItemGroupCategory(models.Model):
    name = models.CharField(max_length=255)
    order = models.IntegerField(null=True, blank = True)

    def __unicode__(self):
        return self.name

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "Item Group Categories"



class ItemGroupManager(models.Manager):
    use_for_related_fields = True

    def eastern(self):
        from django.db.models import Count
        print('eastern')
        return self.get_query_set().filter(category__name='Eastern US').annotate(count=Count('items'))

    def western(self):
        from django.db.models import Count
        print('western')
        return self.get_query_set().filter(category__name='Western US').annotate(count=Count('items'))

    def default(self):
        from django.db.models import Count
        print('others')
        return self.get_query_set().exclude(category__name='Western US').exclude(category__name='Eastern US').annotate(count=Count('items'))


ITEM_GROUP_NO = 1
ITEM_GROUP_NA = 2
ITEM_GROUP_AS = 3
ITEM_GROUP_EU = 4
ITEM_GROUP_AF = 5
ITEM_GROUP_AN = 6
ITEM_GROUP_SA = 7
ITEM_GROUP_OC = 8

PEAK_LIST_CONTINENT_CHOICES =   (
    (ITEM_GROUP_NO,'None'),
    (ITEM_GROUP_NA,'North America'),
    (ITEM_GROUP_AS,'Asia'),
    (ITEM_GROUP_EU,'Europe'),
    (ITEM_GROUP_AF,'Africa'),
    (ITEM_GROUP_AN,'Antarctica'),
    (ITEM_GROUP_SA,'South America'),
    (ITEM_GROUP_OC,'Oceania'),
    )

class ItemGroup(models.Model):
    user = models.ForeignKey(User, related_name='item_groups', on_delete=models.SET_DEFAULT, default=1)
    name = models.CharField(max_length=255)
    slug = AutoSlugField(populate_from=("name",), max_length=255, editable=True)
    description = models.TextField(null = True, blank = True)
    items = models.ManyToManyField(Item, related_name='groups', through='ItemGroupItem')
    category = models.ForeignKey(ItemGroupCategory, null=True,blank=True, on_delete=models.DO_NOTHING)
    #thumbnail = ThumbnailerImageField(upload_to='items/lists', null = True, blank = True)
    thumbnail = models.FileField(upload_to='items/lists', null = True, blank = True)
    country = models.ForeignKey(Country, null=True, blank=True, related_name='groups', on_delete=models.DO_NOTHING)
    region = models.ForeignKey(Region, null=True, blank=True, related_name='groups', on_delete=models.DO_NOTHING)
    order = models.IntegerField(default=0)

    continent = models.SmallIntegerField(choices=PEAK_LIST_CONTINENT_CHOICES)

    created = CreationDateTimeField()
    modified = ModificationDateTimeField()

    old_id = models.IntegerField(null = True, blank = True)

    show_in_world = models.BooleanField(default=False,verbose_name='Show In World Challenges',help_text='Indicate if a challenge should display on World Challenges view.')
    show_in_continent = models.BooleanField(default=False,verbose_name='Show In Continent Challenges',help_text='Indicate if a challenge should display on Continent Challenges view.')
    show_in_country = models.BooleanField(default=True,verbose_name='Show In Country Challenges',help_text='Indicate if a challenge should display on Country Challenges view.')
    show_in_region = models.BooleanField(default=True,verbose_name='Show In Region Challenges',help_text='Indicate if a challenge should display on Region Challenges view.')

    lat = models.FloatField(blank=True, null=True, verbose_name='Latitude', db_index=True)
    long = models.FloatField(blank=True, null=True, verbose_name='Longitude', db_index=True)

    show_on_geochart = models.BooleanField(default=True,verbose_name='Show On Geochart',help_text='Indicate if a challenge should display on Region Challenges geochart.')

    objects = ItemGroupManager()

    class Meta:
        ordering = ('-order',)

    def __unicode__(self):
        return self.name

    def __str__(self):
        return self.name

    def get_absolute_path(self):
        try:
            path = reverse('peak_list_view', args=[self.slug])
        except:
            path = ""
        return path

    def get_absolute_url(self):
        return reverse('peak_list_view', args=[self.slug])

    def get_absolute_url_from_group_slug(slug):
        return reverse('peak_list_view', args=[slug])

    def set_image(self, external_url):
        print("downloading image: %s" % external_url)
        try:
            image_extension = os.path.splitext( external_url )[1]
            file_name = ''
            if self.slug == '' or None:
                file_name = slugify(self.name)
            else:
                file_name = self.slug
            name = 'items/lists/%s%s' % (file_name, image_extension)
            path = '%s/%s' % ( settings.MEDIA_ROOT, name )
            if os.path.exists(path) == False:
                image = urllib.URLopener()
                copy = image.retrieve(external_url,  path)
                file = ThumbnailFile(copy[0])
            self.thumbnail = name
            self.save()
        except:
            print("Error saving image - Unexpected error: %s" % sys.exc_info()[0])
            pass

    def get_thumbnail(self):
        if self.thumbnail:
            return self.thumbnail
        else:
            return 'img/default.png'

    def get_thumbnail_910(self):
        if self.thumbnail:
            return settings.MEDIA_URL+'images/'+self.thumbnail.name.replace('/lists/','/lists/cache/').replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.910x680_q95.jpg'
        else:
            return settings.MEDIA_URL+'img/default.png'

    def get_thumbnail_1920(self):
        if self.thumbnail:
            return settings.MEDIA_URL+'images/'+self.thumbnail.name.replace('/lists/','/lists/cache/').replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.1920x1440_q95_crop.jpg'
        else:
            return settings.MEDIA_URL+'img/default.png'

    def get_thumbnail_480(self):
        if self.thumbnail:
            return settings.MEDIA_URL + 'images/'+self.thumbnail.name.replace('/lists/','/lists/cache/').replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.480x360_q95.jpg'
        else:
            items = self.get_top_four_peaks()
            for i in items:
                return i.get_thumbnail_480()
            return settings.MEDIA_URL+'img/default.png'

    def get_thumbnail_480_or_default(self):
        if self.thumbnail:
            return settings.MEDIA_URL + 'images/'+self.thumbnail.name.replace('/lists/','/lists/cache/').replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.480x360_q95.jpg'
        else:
            return settings.MEDIA_URL+'img/default.png'

    def get_thumbnail_320(self):
        if self.thumbnail:
            return 'images/'+self.thumbnail.name.replace('/lists/','/lists/cache/').replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.320x240_q95.jpg'
        else:
            return settings.MEDIA_URL+'img/default.png'

    def get_user_bagging_stats(self, ids_list):
        total_all = self.items.all().count()
        total_bagged = self.items.filter(id__in = ids_list).count() * 1.0
        percent = int( (total_bagged / total_all) * 100 )
        return total_all, int(total_bagged), percent

    def get_featured_items(self):
        return self.items.all().order_by('-itemgroupitem__order')[:5]

    def get_items_order_by_elevation(self):
        return self.items.all().order_by('-elevation')

    def get_top_four_peaks(self):
        return self.items.raw("select a.id, a.thumbnail as thumbnail_orig from items_item a, items_itemgroupitem b where a.id = b.item_id and b.group_id = %s order by elevation desc limit 4", [self.id])

    def get_user_recent_summit_details(self, group_id):
        return self.items.raw("select c.id, a.id as peak_id, a.name as peak_name, a.slug_new_text as peak_slug, b.id as summitlog_id, b.date as summitlog_date, extract(day from now()-b.date) as days_since_last_summit from items_item a, items_summitlog b, items_itemgroupitem c where c.group_id = %s and c.item_id = b.item_id and b.user_id = %s and b.status = 1 and b.attempt = false and b.item_id = a.id order by b.date desc limit 1;", [group_id, self.id])

    def get_challenge_recent_summit_details(self, user_id):
        return self.items.raw("select c.id, a.id as peak_id, a.name as peak_name, a.slug_new_text as peak_slug, b.id as summitlog_id, b.date as summitlog_date, extract(day from now()-b.date) as days_since_last_summit from items_item a, items_summitlog b, items_itemgroupitem c where c.group_id = %s and c.item_id = b.item_id and b.user_id = %s and b.status = 1 and b.attempt = false and b.item_id = a.id order by b.date desc limit 1;", [self.id, user_id])

    def get_summitlog_count_by_month(self):
        return self.items.raw("select extract(month from b.date) as summit_month, to_char(b.date, 'Mon') as summit_month_abbr, count(b.id) as id from items_item a, items_summitlog b, items_itemgroupitem c where c.group_id = %s and c.item_id = b.item_id and b.status = 1 and b.attempt = false and b.item_id = a.id group by extract(month from b.date), to_char(b.date, 'Mon') order by extract(month from b.date);", [self.id])

    def get_pursuers(self):
        return self.items.raw("select b.user_id as id, count(distinct b.item_id) as peak_count from items_itemgroupitem a, items_summitlog b where a.group_id = %s and a.item_id = b.item_id and b.status = 1 and b.attempt = False group by b.user_id order by peak_count desc", [self.id])

    def get_finishers(self, peak_count):
        return self.items.raw("select b.user_id as id, count(distinct b.item_id) as peak_count from items_itemgroupitem a, items_summitlog b where a.group_id = %s and a.item_id = b.item_id and b.status = 1 and b.attempt = False group by b.user_id having count(distinct b.item_id) >= %s order by peak_count desc", [self.id, peak_count])

    def recent_summits(self):
        ids = self.items.all().values_list('id', flat=True)
        summits = SummitLog.objects.filter(item__id__in=ids).order_by('-date')[:4]
        list = []
        for s in summits:
            user_stat = s.user.profile.get_list_stats(self)
            list.append( (s, user_stat ) )
        return list

    def all_summits(self):
        ids = self.items.all().values_list('id', flat=True)
        summits = SummitLog.objects.filter(item__id__in=ids).order_by('-date')
        list = []
        for s in summits:
            user_stat = s.user.profile.get_list_stats(self)
            list.append( (s, user_stat ) )
        return list

    def in_progress_leaders(self):
        ids = self.items.all().values_list('id', flat=True)
        summits = SummitLog.objects.filter(item__id__in=ids)
        list = []
        for s in summits:
            user_stat = s.user.profile.get_list_stats(self)
            list.append( (s.user, user_stat[3] ) )
        list = sorted(list, key=itemgetter(1), reverse=True)
        list = [x for x in set(list)]
        return list[0:4]

    def pursuers_count(self):
        pursuers = User.objects.raw("select distinct aa.id from auth_user aa, items_itemgroupitem a, items_summitlog b where a.group_id = %s and a.item_id = b.item_id and b.attempt = False and b.status = 1 and b.user_id = aa.id", [self.id])
        return len(pursuers)

    def finishers_count(self):
        finishers_count = 0

        group_items = self.items.all().values_list('id', flat=True)
        group_count = len(group_items)
        users_ids = SummitLog.objects.filter(item__id__in=group_items).values_list('user', flat=True).order_by('user').distinct()

        if users_ids:
            # This query gets all the peaks for the group_items, and then it counts the summitlogs for every user in users_ids
            sql = """
                SELECT COUNT(*) as user_summited_count
                FROM (
                    SELECT DISTINCT i.user_id, i.item_id
                    FROM items_summitlog i
                    WHERE (i.item_id IN (
                            SELECT ii.id
                            FROM items_item ii, items_itemgroupitem iig
                            WHERE ii.id = iig.item_id AND iig.group_id = %s
                        ) AND i.user_id IN %s)
                ) AS distinct_user_rows
                GROUP BY user_id;
            """

            with connection.cursor() as cursor:
                cursor.execute(sql, [self.id, tuple(users_ids)])
                for row in cursor.fetchall():
                    user_summited_count = row[0]
                    if user_summited_count >= group_count:
                        finishers_count += 1

        return finishers_count

    def finishers_count_new(self):
        group_items = self.items.all().values_list('id', flat=True)
        group_count = len(group_items)
        finishers = User.objects.raw("select aa.id, count(distinct b.item_id) as peak_count from items_itemgroupitem a, items_summitlog b, auth_user aa where a.group_id = %s and a.item_id = b.item_id and b.attempt = False and b.status = 1 and b.user_id = aa.id group by aa.id having count(distinct b.item_id) >= %s order by peak_count desc", [self.id, group_count])
        list = []
        for f in finishers:
            list.append(f)
        finishers_count = len(list)
        return finishers_count


class ItemGroupItem(models.Model):
    item = models.ForeignKey(Item, on_delete=models.DO_NOTHING)
    group = models.ForeignKey(ItemGroup, on_delete=models.DO_NOTHING)
    order = models.IntegerField(default=0)


class ItemGroupHighlight(models.Model):
    user = models.ForeignKey(User, related_name='item_group_highlights', on_delete=models.SET_DEFAULT, default=1)
    group = models.ForeignKey(ItemGroup, related_name='highlights', on_delete=models.DO_NOTHING)
    highlight = models.TextField()
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()

    def __unicode__(self):
        return str(self.highlight)


class ItemGroupHighlightLogGroup(models.Model):
    user = models.ForeignKey(User, related_name='item_group_highlights_log_group', on_delete=models.SET_DEFAULT, default=1)
    group = models.ForeignKey(ItemGroup, related_name='highlights_log_group', on_delete=models.DO_NOTHING)
    log_date = CreationDateTimeField()

    def __unicode__(self):
        return str(self.log_date)


class ItemGroupHighlightLog(models.Model):
    log_group_id = models.ForeignKey(ItemGroupHighlightLogGroup, related_name='item_group_highlights_log', on_delete=models.DO_NOTHING)
    highlight = models.TextField()

    def __unicode__(self):
        return str(self.highlight)


class ItemHighlight(models.Model):
    user = models.ForeignKey(User, related_name='item_highlights', on_delete=models.SET_DEFAULT, default=1)
    item = models.ForeignKey(Item, related_name='highlights', on_delete=models.DO_NOTHING)
    highlight = models.TextField()
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()

    def __unicode__(self):
        return str(self.highlight)

class ItemHighlightLogGroup(models.Model):
    user = models.ForeignKey(User, related_name='item_highlights_log_group', on_delete=models.SET_DEFAULT, default=1)
    item = models.ForeignKey(Item, related_name='highlights_log_group', on_delete=models.DO_NOTHING)
    log_date = CreationDateTimeField()

    def __unicode__(self):
        return str(self.log_date)

class ItemHighlightLog(models.Model):
    log_group_id = models.ForeignKey(ItemHighlightLogGroup, related_name='item_highlights_log', on_delete=models.DO_NOTHING)
    highlight = models.TextField()

    def __unicode__(self):
        return str(self.highlight)

class ItemCorrectionLogGroup(models.Model):
    user = models.ForeignKey(User, related_name='item_correction_log_group', on_delete=models.SET_DEFAULT, default=1)
    item = models.ForeignKey(Item, related_name='correction_log_group', on_delete=models.DO_NOTHING)
    log_date = CreationDateTimeField()

    def __unicode__(self):
        return str(self.log_date)

class AlternateName(models.Model):
    name = models.CharField(max_length=255)
    item = models.ForeignKey(Item, related_name='alternate_name_field', on_delete=models.DO_NOTHING)
    #    approved = models.BooleanField(default=False)

    def __unicode__(self):
        return self.name

    def __str__(self):
        return self.name


class ItemErrorReport(models.Model):
    user = models.ForeignKey(User, related_name='item_error_reports', on_delete=models.SET_DEFAULT, default=1)
    item = models.ForeignKey(Item, related_name='error_reports', on_delete=models.DO_NOTHING)
    message = models.TextField(help_text='Please describe the error you noticed below', null=False, blank=False)
    revised = models.BooleanField(default=False)
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()

    def __unicode__(self):
        return '%s ERROR REPORT (peak: %s / %s)' % (self.user.username, self.item.name, self.message)

    def send_email(self):
        #todo send email to managers
        return True

    def get_email(self):
        return self.user.email

class GpxTrack(models.Model):
    user = models.ForeignKey(User, related_name='gpx_track', on_delete=models.SET_DEFAULT, default=1)
    date = models.DateField()
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()
    gpx_file = models.URLField(null=True, blank=True)
    total_distance = models.FloatField(blank=True, null=True)
    start_elevation = models.FloatField(blank=True, null=True)
    max_elevation = models.FloatField(blank=True, null=True)
    elevation_gain = models.FloatField(blank=True, null=True)
    elevation_loss = models.FloatField(blank=True, null=True)
    total_trip_time = models.FloatField(blank=True, null=True)
    moving_time = models.FloatField(blank=True, null=True)
    gpx_geom = models.GeometryField(null=True, blank=True)
    gpx_source = models.TextField(null=True, blank=True)
    encoded_polyline = models.TextField(null=True, blank=True)

    class Meta:
        ordering = ('-date',)

    def __unicode__(self):
        return '%s uploaded %s' % (self.user.username, self.gpx_file)

    def __str__(self):
        return '%s uploaded %s' % (self.user.username, self.gpx_file)

    @mark_safe
    def gpx_file_url(self):
        return '<a href="%s%s">%s</a>' % ('https://s3-us-west-1.amazonaws.com/peakery-media/', self.gpx_file, self.gpx_file)

    gpx_file_url.allow_tags = True
    gpx_file_url.short_description = 'GPX File'

class SummitRoute(models.Model):
    name = models.CharField(max_length=255)
    slug = AutoSlugField(populate_from=("name",), max_length=255)
    created_by = models.ForeignKey(User, related_name='routes_created', on_delete=models.SET_DEFAULT, default=1)
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()

    def __unicode__(self):
        return self.name

    def __str__(self):
        return self.name


STATUS_ROUTE_ACTIVE = 1
STATUS_ROUTE_PENDING = 2

STATUS_IN_ROUTE_CHOICES = (
    (STATUS_ROUTE_ACTIVE,'Active'),
    (STATUS_ROUTE_PENDING, 'Pending'),
    )


class PeakRoute(models.Model):
    user = models.ForeignKey(User, related_name='peak_route', on_delete=models.SET_DEFAULT, default=1)
    item = models.ForeignKey(Item, related_name='peak_route', on_delete=models.DO_NOTHING)
    name = models.CharField(max_length=255)
    slug = AutoSlugField(populate_from=('name',), max_length=255, editable=False, unique=True, overwrite=True, db_index=True)
    difficulty = models.TextField(null = True, blank = True)
    start_location = models.TextField(null = True, blank = True)
    status = models.SmallIntegerField(choices=STATUS_IN_ROUTE_CHOICES, default = STATUS_ROUTE_ACTIVE)
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()
    gpx_file = models.URLField(null=True, blank=True)
    gpx_start_index = models.IntegerField(blank=True, null=True)
    gpx_end_index = models.IntegerField(blank=True, null=True)
    gpx_start_lat = models.FloatField(blank=True, null=True)
    gpx_start_lon = models.FloatField(blank=True, null=True)
    gpx_end_lat = models.FloatField(blank=True, null=True)
    gpx_end_lon = models.FloatField(blank=True, null=True)
    distance_to_summit = models.FloatField(blank=True, null=True)
    total_distance = models.FloatField(blank=True, null=True)
    start_elevation = models.FloatField(blank=True, null=True)
    max_elevation = models.FloatField(blank=True, null=True)
    elevation_gain = models.FloatField(blank=True, null=True)
    elevation_loss = models.FloatField(blank=True, null=True)
    route_metadata = models.TextField(default='{}')
    getting_there = models.TextField(null = True, blank = True)
    red_tape = models.TextField(null = True, blank = True)
    gpx_geom = models.GeometryField(null=True, blank=True)
    default_name = models.BooleanField(default=False)
    one_way = models.BooleanField(default=False)
    encoded_polyline = models.TextField(null=True, blank=True)
    objects = PeakRouteManager()

    class Meta:
        ordering = ('-created',)

    def __unicode__(self):
        return '%s route for %s' % (self.name, self.item.name)

    def get_absolute_url(self):
        val = '%s%s' % (settings.SITE_URL[:-1], reverse('peak_view_route', args=[self.item.slug_new_text, self.id]))
        return val

    def modify_date(self):
        if not self.modified:
            return self.created
        else:
            return self.modified

    def get_elevation_gain(self):
        f = self.elevation_gain
        if f:
            #m = floatformat(f * 0.3048, 0)
            m = floatformat(round(f * 0.3048))
            return '%s ft / %s m' % (commify(int(f)), commify(m))
        return ''

    def get_route_name(self):
        if self.one_way:
            return '%s (1-way)' % self.name
        else:
            return self.name

    def summits_count(self):
        return PeakRoute.get_summits_count(self.item_id, self.id)

    def get_summits_count(item_id, route_id):
        sql = "select count(*) from items_summitlog where item_id = %s and peak_route_id = %s"
        count = 0
        with connection.cursor() as cursor:
            cursor.execute(sql, [item_id, route_id])
            for row in cursor.fetchall():
                count = row[0]
        return count

    def summits(self):
        return PeakRoute.get_summits(self.item_id, self.id)

    def get_summits(item_id, route_id):
        from peakery.items.utils import dictfetchall
        sql = "select i.id, i.date, au.username from items_summitlog i, auth_user au where au.id = i.user_id and i.item_id = %s and i.peak_route_id = %s order by i.date desc"
        with connection.cursor() as cursor:
            cursor.execute(sql, [item_id, route_id])
            summits = dictfetchall(cursor)
        return summits

    def avg_time_to_summit(self):
        return PeakRoute.get_avg_time_to_summit(self.item_id, self.id)

    def get_avg_time_to_summit(item_id, route_id):
        sql = "select a.id, sum(c.time_to_summit) / count(c.id) as avg_time_to_summit " + \
            "from items_peakroute a " + \
            "left join (select c.id, c.peak_route_id, c.time_to_summit from items_summitlog c where c.item_id = %s and c.status = 1 and c.time_to_summit > 0) as c on c.peak_route_id = a.id " + \
            "where a.id = %s " + \
            "group by a.id "
        route = PeakRoute.objects.raw(sql, [item_id, route_id])
        avg_item_to_summit = None
        for r in route:
            avg_item_to_summit = r.avg_time_to_summit
        return avg_item_to_summit

    def avg_total_trip_time(self):
        return PeakRoute.get_avg_total_trip_time(self.item_id, self.id)

    def get_avg_total_trip_time(item_id, route_id):
        sql = "select a.id, sum(c.total_trip_time) / count(c.id) as avg_total_trip_time " + \
            "from items_peakroute a " + \
            "left join (select c.id, c.peak_route_id, c.total_trip_time from items_summitlog c where c.item_id = %s and c.status = 1 and c.total_trip_time > 0) as c on c.peak_route_id = a.id " + \
            "where a.id = %s " + \
            "group by a.id "
        route = PeakRoute.objects.raw(sql, [item_id, route_id])
        avg_total_trip_time = None
        for r in route:
            avg_total_trip_time = r.avg_total_trip_time
        return avg_total_trip_time


    def save(self, *args, **kwargs):
        super(PeakRoute, self).save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        self.delete_related()
        super(PeakRoute, self).delete(*args, **kwargs)

class PeakRouteArchive(models.Model):
    route = models.ForeignKey(PeakRoute, related_name='peak_route_archive', on_delete=models.CASCADE)
    user = models.ForeignKey(User, related_name='peak_route_archive', on_delete=models.SET_DEFAULT, default=1)
    item = models.ForeignKey(Item, related_name='peak_route_archive', on_delete=models.DO_NOTHING)
    name = models.CharField(max_length=255)
    difficulty = models.TextField(null = True, blank = True)
    start_location = models.TextField(null = True, blank = True)
    status = models.SmallIntegerField(choices=STATUS_IN_ROUTE_CHOICES, default = STATUS_ROUTE_ACTIVE)
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()
    gpx_file = models.URLField(null=True, blank=True)
    gpx_start_index = models.IntegerField(blank=True, null=True)
    gpx_end_index = models.IntegerField(blank=True, null=True)
    gpx_start_lat = models.FloatField(blank=True, null=True)
    gpx_start_lon = models.FloatField(blank=True, null=True)
    gpx_end_lat = models.FloatField(blank=True, null=True)
    gpx_end_lon = models.FloatField(blank=True, null=True)
    distance_to_summit = models.FloatField(blank=True, null=True)
    start_elevation = models.FloatField(blank=True, null=True)
    elevation_gain = models.FloatField(blank=True, null=True)
    route_metadata = models.TextField(default='{}')
    getting_there = models.TextField(null = True, blank = True)
    red_tape = models.TextField(null = True, blank = True)
    objects = PeakRouteArchiveManager()

    class Meta:
        ordering = ('-created',)

    def __unicode__(self):
        return 'route archive for %s' % (self.route.name)

    def save(self, *args, **kwargs):
        super(PeakRouteArchive, self).save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        self.delete_related()
        super(PeakRouteArchive, self).delete(*args, **kwargs)

class PeakRouteRelatedLink(models.Model):
    peak_route = models.ForeignKey(PeakRoute, related_name='related_links', on_delete=models.DO_NOTHING)
    related_url = models.URLField()
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()

    def __unicode__(self):
        return self.related_url

class PeakRouteRelatedLinkArchive(models.Model):
    route_archive = models.ForeignKey(PeakRouteArchive, related_name='related_links_archive', on_delete=models.CASCADE)
    related_url = models.URLField()
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()

    def __unicode__(self):
        return self.related_url

class PeakRouteHighlight(models.Model):
    user = models.ForeignKey(User, related_name='peak_route_highlights', on_delete=models.SET_DEFAULT, default=1)
    peak_route = models.ForeignKey(PeakRoute, related_name='peak_route_highlights', on_delete=models.DO_NOTHING)
    highlight = models.TextField()
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()

    def __unicode__(self):
        return str(self.highlight)

class PeakRouteHighlightLogGroup(models.Model):
    user = models.ForeignKey(User, related_name='peak_route_highlights_log_group', on_delete=models.SET_DEFAULT, default=1)
    peak_route = models.ForeignKey(PeakRoute, related_name='highlights_log_group', on_delete=models.DO_NOTHING)
    log_date = CreationDateTimeField()

    def __unicode__(self):
        return str(self.log_date)

class PeakRouteHighlightLog(models.Model):
    log_group_id = models.ForeignKey(PeakRouteHighlightLogGroup, related_name='peak_route_highlights_log', on_delete=models.DO_NOTHING)
    highlight = models.TextField()

    def __unicode__(self):
        return str(self.highlight)

STATUS_SUMMIT_ACTIVE = 1
STATUS_SUMMIT_PENDING = 2

STATUS_IN_SUMMITLOG_CHOICES =   (
    (STATUS_SUMMIT_ACTIVE,'Active'),
    (STATUS_SUMMIT_PENDING, 'Pending'),
    )

class SummitLogGroup(models.Model):
    user = models.ForeignKey(User, related_name='summit_log_group', on_delete=models.CASCADE)
    created = CreationDateTimeField()

    def __unicode__(self):
        return 'Summit log group created by %s on %s' % (self.user.username, self.created)

class SummitLog(models.Model):
    user = models.ForeignKey(User, related_name='summit_log', on_delete=models.CASCADE)
    item = models.ForeignKey(Item, related_name='summits', on_delete=models.DO_NOTHING)
    date = models.DateField()
    date_entered = models.BooleanField(default=False)
    log = models.TextField(null = True, blank = True)
    route_up = models.ForeignKey(SummitRoute, null = True, blank = True, related_name='routes_up', on_delete=models.DO_NOTHING)
    route_down = models.ForeignKey(SummitRoute, null = True, blank = True, related_name='routes_down', on_delete=models.DO_NOTHING)
    status = models.SmallIntegerField(choices=STATUS_IN_SUMMITLOG_CHOICES, default = STATUS_SUMMIT_ACTIVE)
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()
    featured = models.BooleanField(default=False)
    attempt = models.BooleanField(default=False)
    gpx_file = models.URLField(null=True, blank=True)
    distance_to_summit = models.FloatField(blank=True, null=True)
    total_distance = models.FloatField(blank=True, null=True)
    start_elevation = models.FloatField(blank=True, null=True)
    max_elevation = models.FloatField(blank=True, null=True)
    elevation_gain = models.FloatField(blank=True, null=True)
    elevation_loss = models.FloatField(blank=True, null=True)
    time_to_summit = models.FloatField(blank=True, null=True)
    total_trip_time = models.FloatField(blank=True, null=True)
    moving_time = models.FloatField(blank=True, null=True)
    trip_metadata = models.TextField(default='{}')
    peak_route = models.ForeignKey(PeakRoute, null = True, blank = True, related_name='peak_route', on_delete=models.DO_NOTHING)
    multi_peak_summit = models.BooleanField(default=False)
    summitlog_group = models.ForeignKey(SummitLogGroup, null = True, blank = True, related_name='summit_log_group', on_delete=models.DO_NOTHING)
    gpx_geom = models.GeometryField(null=True, blank=True)
    gpx_source = models.TextField(null=True, blank=True)
    log_source = models.TextField(null=True, blank=True)
    peak_route_name = models.TextField(null=True, blank=True)
    encoded_polyline = models.TextField(null=True, blank=True)
    objects = SummitLogManager()

    class Meta:
        ordering = ('-date',)

    def __unicode__(self):
        try:
            user = None
            item_name = None

            try:
                user = self.user.username
            except:
                pass

            try:
                item_name = self.item.name
            except:
                pass

            return '%s summited %s' % (user, item_name)
        except Exception as e:
            return ''

    def __str__(self):
        try:
            user = None
            item_name = None

            try:
                user = self.user.username
            except:
                pass

            try:
                item_name = self.item.name
            except:
                pass

            return '%s summited %s' % (user, item_name)
        except Exception as e:
            return ''

    def save(self, *args, **kwargs):
        self.featured = False
        if len(self.log) >= 100:
            self.featured = True
        if self.id and self.summit_photos.all().count() > 1:
            self.featured = True

        super(SummitLog, self).save(*args, **kwargs)
        if self.date_entered:
            self.user.person.fix_last_summit_date()
        self.item.refresh_summitlog_count()
        self.item.refresh_latest_cache_for_peak()

    def quick_save(self, *args, **kwargs):
        if self.log:
            if len(self.log) >= 100:
                self.featured = True
                self.item.refresh_latest_cache_for_peak()
                print('updating cache')
        if self.id and self.summit_photos.all().count() > 1:
            self.featured = True
            self.item.refresh_latest_cache_for_peak()
            print('updating cache')

        self.user.person.refresh_latest_cache_for_user()

        super(SummitLog, self).save(*args, **kwargs)

        # update summitlog count for item
        summitlog_count = SummitLog.objects.filter(item_id=self.item_id, attempt=False, status=STATUS_SUMMIT_ACTIVE).count()
        summitlog_item = Item.objects.filter(id=self.item_id)
        item_to_update = None
        for item in summitlog_item:
            item_to_update = item
        if item_to_update:
            from django.db import connection
            sql = "update items_item set summitlog_count = %s where id = %s "

            with connection.cursor() as cursor:
                cursor.execute(sql, [summitlog_count, item_to_update.id])

    def delete(self, *args, **kwargs):
        self.delete_related()
        super(SummitLog, self).delete(*args, **kwargs)

    def get_absolute_url(self):
        domain = Site.objects.get_current().domain
        return 'http://%s%s' % (domain, reverse('peak_view_summit', args=[self.item.slug_new_text, self.id]))

    def get_relative_url(self):
        return '%s' % (reverse('peak_view_summit', args=[self.item.slug_new_text, self.id]))

    def get_activity_type(self):
        if self.attempt:
            return 'attempt'
        else:
            return 'summit'

    def get_user_photo(self):
        photo = ItemPhoto.objects.filter(category__name='summit', user=self.user, item=self.item)
        if photo:
            return photo[0].image
        return ''

    def get_notification_photo_url(self):
        photo = ItemPhoto.objects.filter(category__name='summit', user=self.user, item=self.item, summit_log_id=self.id)
        if photo:
            #return settings.MEDIA_URL+'images/'+photo[0].image.replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.320x190_q95_crop.jpg'
            return 'https://s3-us-west-1.amazonaws.com/peakery-media/images/'+photo[0].image.name.replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.910x680_q95.jpg'
        else:
            if self.item.thumbnail:
                #return settings.MEDIA_URL+'images/'+self.item.thumbnail.replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.320x190_q95_crop.jpg'
                return 'https://s3-us-west-1.amazonaws.com/peakery-media/images/'+self.item.thumbnail.name.replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.910x680_q95.jpg'
            else:
                #return settings.S3_URL+'img/default.png'
                return 'https://s3-us-west-1.amazonaws.com/peakery-static/img/default.png'

    def get_addl_summits(self):
        sql = "select a.id, a.name from items_item a, items_summitloggroupsummit b, items_summitlog c, (select x.summitlog_group_id from items_summitlog x where x.id = %s) d where b.group_id = d.summitlog_group_id and b.summit_id != %s and b.summit_id = c.id and c.item_id = a.id "
        addl_summits = SummitLog.objects.raw(sql, [self.id, self.id])
        if addl_summits:
            return addl_summits
        else:
            return None

    def get_addl_summit_peaks(self):
        sql = "select a.id, a.name, a.elevation, get_thumb(a.thumbnail, 1920) as thumbnail_url, a.summitlog_count, c.attempt from items_item a, items_summitloggroupsummit b, items_summitlog c, (select x.summitlog_group_id from items_summitlog x where x.id = %s) d where b.group_id = d.summitlog_group_id and b.summit_id != %s and b.summit_id = c.id and c.item_id = a.id "
        addl_summits = Item.objects.raw(sql, [self.id, self.id])
        if addl_summits:
            return addl_summits
        else:
            return None

    def get_summit_images(self):
        summit_photos = self.summit_photos.filter(item_id__gt=0).order_by('photo_index', 'created')
        if summit_photos:
            return summit_photos
        else:
            return None

    def get_summit_images_incl_nopeak(self):
        summit_photos = self.summit_photos.order_by('photo_index', 'created')
        if summit_photos:
            return summit_photos
        else:
            return None

    def get_nopeak_summit_images(self):
        summit_photos = self.summit_photos.order_by('photo_index', 'created')
        if summit_photos:
            return summit_photos
        else:
            return None

    def get_comma_separated_companions(self):
        companions = self.companions.all()
        if companions:
            list = []
            for companion in companions:
                if companion.user_relation.to_user:
                    list.append(companion.user_relation.to_user.username)
                else:
                    list.append(companion.user_relation.first_name)
            return ', '.join(list)
        return False

    def get_companions(self):
        companions = self.companions.all()
        return companions

    def get_fellow_baggers(self):
        fellow_baggers = self.fellow_baggers.all()
        return fellow_baggers

    def has_routes(self):
        if self.route_up or self.route_down:
            return True
        return False

    def delete_related(self):
        photos = self.summit_photos.all()
        for p in photos:
            p.delete()
        fellows = self.fellow_baggers.all()
        for f in fellows:
            f.delete()
        notices = self.notice_set.all().delete()

    def reset_fellows(self):
        fellows = self.fellow_baggers.all()
        for f in fellows:
            f.delete()

    def is_homepage_background(self):
        bg = self.background.filter()
        if bg:
            return "Yes"
        return "-"

    def summit_comments(self):
        return self.comments.select_related('user').all().order_by('created')

    def summit_comments_for_api(self):
        return self.comments.all().order_by('created')

    def favorite_count(self):
        sql = "select a.* from favorites_favorite a where a.content_type_id = 18 and cast(a.object_id as integer) = %s "
        favorites = Favorite.objects.raw(sql, [self.id])
        list = []
        for f in favorites:
            list.append(f)
        return len(list)

    def your_favorite_count(self, request_user_id):
        sql = "select a.* from favorites_favorite a where a.user_id = %s and a.content_type_id = 18 and cast(a.object_id as integer) = %s "
        favorites = Favorite.objects.raw(sql, [request_user_id, self.id])
        list = []
        for f in favorites:
            list.append(f)
        return len(list)

    def summit_times(self):
        return SummitLog.objects.filter(user=self.user, item=self.item).count()

    def get_companions_as_user_relations(self):
        urs = list()
        for companion in self.companions.all():
            urs.append(companion.user_relation)

        return urs

    @mark_safe
    def gpx_file_url(self):
        return '<a href="%s%s">%s</a>' % ('https://s3-us-west-1.amazonaws.com/peakery-media/', self.gpx_file, self.gpx_file)

    def has_gpx_file(self):
        if self.gpx_file:
            return 'Yes'
        else:
            return 'No'

    gpx_file_url.allow_tags = True
    gpx_file_url.short_description = 'GPX File'

    has_gpx_file.short_description = 'Has GPX'

class SummitLogGroupSummit(models.Model):
    summit = models.ForeignKey(SummitLog, on_delete=models.CASCADE)
    group = models.ForeignKey(SummitLogGroup, on_delete=models.DO_NOTHING)

class SummitLogComment(models.Model):
    user = models.ForeignKey(User, related_name='summit_comments', on_delete=models.CASCADE)
    summit_log = models.ForeignKey(SummitLog, related_name='comments', on_delete=models.CASCADE)
    comment = models.TextField()
    #This is a field to filter the comments in comments_by_user on user profile page
    summit_comment = True
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()

    def __unicode__(self):
        return '%s comment (summit: %i / %s)' % (self.user.username, self.summit_log.id, self.comment)

class SummitLogVideo(models.Model):
    summit_log = models.ForeignKey(SummitLog, related_name='videos', on_delete=models.CASCADE)
    video_url = models.URLField()
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()
    title = models.CharField(max_length=1000, null=True, blank=True)
    description = models.CharField(max_length=1000, null=True, blank=True)
    image = models.CharField(max_length=1000, null=True, blank=True)
    processed = models.BooleanField(default=False)

    def __unicode__(self):
        return self.video_url

class SummitLogRelatedLink(models.Model):
    summit_log = models.ForeignKey(SummitLog, related_name='related_links', on_delete=models.CASCADE)
    related_url = models.URLField()
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()
    title = models.CharField(max_length=1000, null=True, blank=True)
    description = models.CharField(max_length=1000, null=True, blank=True)
    image = models.CharField(max_length=1000, null=True, blank=True)
    processed = models.BooleanField(default=False)

    def __unicode__(self):
        return self.related_url


class ItemPhotoCategory(models.Model):
    name = models.CharField(max_length=50)
    slug = AutoSlugField(populate_from=("name",), max_length=255)
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()

    class Meta:
        verbose_name_plural = 'Item photo categories'

    def __unicode__(self):
        return self.name

    def __str__(self):
        return self.name


from sorl.thumbnail.fields import ImageField


class ItemPhoto(models.Model):
    user = models.ForeignKey(User, related_name='item_photos', on_delete=models.SET_DEFAULT, default=1)
    item = models.ForeignKey(Item, related_name='photos', on_delete=models.DO_NOTHING)
    #image = ThumbnailerImageField(upload_to='items/users', null = True, blank = True)
    image = ImageField(upload_to='items/users', null = True, blank = True)
    caption = models.CharField(max_length=255, null=True, blank=True)
    category = models.ForeignKey(ItemPhotoCategory, related_name='photos', on_delete=models.DO_NOTHING)
    summit_log = models.ForeignKey(SummitLog, null = True, blank = True, related_name='summit_photos', on_delete=models.CASCADE)
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()
    author_photo = models.CharField(max_length=255, null=True, blank=True)
    uuid = models.CharField(max_length=255, null=True, blank=True)
    photo_index = models.IntegerField(null=True, blank=True)
    photo_lat = models.FloatField(blank=True, null=True, verbose_name='Photo Latitude')
    photo_lng = models.FloatField(blank=True, null=True, verbose_name='Photo Longitude')
    photo_time = models.DateTimeField(verbose_name="Photo Capture Time", blank=True, null = True)
    image_height = models.FloatField(blank=True, null=True, verbose_name='Image Height')
    image_width = models.FloatField(blank=True, null=True, verbose_name='Image Width')

    def __unicode__(self):
        return '%s photo (peak: %s / %s)' % (self.user.username, self.item.name, self.category.name)

    def can_be_homepage(self):
        if self.image.width > 990 and self.image.height > 610:
            return True
        return False

    def thumb_url_med(self):
        if self.image:
            ext = self.image.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return self.image.name.replace('/static/items/', '/static/images/items/').replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.250x179_q95_crop.'+ext
        else:
            return 'img/default.png'

    def thumb_url_lrg(self):
        if self.image:
            ext = self.image.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return self.image.name.replace('/static/items/', '/static/images/items/').replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.320x190_q95_crop.'+ext
        else:
            return 'img/default.png'

    def thumb_url_xlrg(self):
        if self.image:
            ext = self.image.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return self.image.name.replace('/static/items/', '/static/images/items/').replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.910x680_q95_crop.'+ext
        else:
            return 'img/default.png'

    def thumb_320(self):
        if self.image:
            ext = self.image.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return self.image.name.replace('/static/items/', '/static/images/items/').replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.320x240_q95.'+ext
        else:
            return 'img/default.png'

    def thumb_480(self):
        if self.image:
            ext = self.image.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return self.image.name.replace('/static/items/', '/static/images/items/').replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.480x360_q95.'+ext
        else:
            return 'img/default.png'

    def get_thumb_480(image_name):
        if image_name:
            ext = image_name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return image_name.replace('/static/items/', '/static/images/items/').replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.480x360_q95.'+ext
        else:
            return 'img/default.png'

    def thumb_650(self):
        if self.image:
            ext = self.image.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return self.image.name.replace('/static/items/', '/static/images/items/').replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.650x410_q95_crop-top.'+ext
        else:
            return 'img/default.png'

    def thumb_745(self):
        if self.image:
            ext = self.image.name.split(".")[-1].lower()
            return self.image.name.replace('/static/items/', '/static/images/items/').replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.745x500_q95_crop-top.'+ext
        else:
            return 'img/default.png'

    def thumb_910(self):
        if self.image:
            ext = self.image.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return self.image.name.replace('/static/items/', '/static/images/items/').replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.910x680_q95.'+ext
        else:
            return 'img/default.png'

    def thumb_910x680(self):
        if self.image:
            ext = self.image.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return self.image.name.replace('/static/items/', '/static/images/items/').replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.910x680_q95.'+ext
        else:
            return 'img/default.png'

    def get_thumb_910x680(image):
        if image:
            ext = image.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return image.replace('/static/items/', '/static/images/items/').replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.910x680_q95.'+ext
        else:
            return 'img/default.png'

    def thumb_1920x1440(self):
        if self.image:
            ext = self.image.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return self.image.name.replace('/static/items/', '/static/images/items/').replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.1920x1440_q95_crop.'+ext
        else:
            return 'img/default.png'


    def get_thumb_1920x1440(image_name):
        if image_name:
            ext = image_name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return image_name.replace('/static/items/', '/static/images/items/').replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.1920x1440_q95_crop.'+ext
        else:
            return 'img/default.png'

    def thumb_480x360(self):
        if self.image:
            ext = self.image.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return self.image.name.replace('/static/items/', '/static/images/items/').replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.480x360_q95.'+ext
        else:
            return 'img/default.png'

    def get_thumbnail_910(self):
        if self.image:
            ext = self.image.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return settings.MEDIA_URL+'images/'+self.image.name.replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.910x680_q95.'+ext
        else:
            return settings.S3_URL+'img/default.png'

    def get_thumbnail_1920(self):
        if self.image:
            ext = self.image.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return settings.MEDIA_URL+'images/'+self.image.name.replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.1920x1440_q95_crop.'+ext
        else:
            return settings.S3_URL+'img/default.png'

class PeakRouteStep(models.Model):
    route = models.ForeignKey(PeakRoute, related_name='photos', on_delete=models.CASCADE)
    step_number = models.IntegerField()
    #image = ThumbnailerImageField(upload_to='items/users', null = True, blank = True)
    image = ImageField(upload_to='items/routes', null = True, blank = True)
    description = models.CharField(max_length=1000, null=True, blank=True)
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()

    def __unicode__(self):
        return '%s photo' % (self.route.name)

    def thumb_url_med(self):
        if self.image:
            ext = self.image.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return self.image.name.replace('/static/routes/', '/static/images/routes/').replace('/routes/','/routes/cache/')+'.250x179_q95_crop.'+ext
        else:
            return 'img/default.png'

    def thumb_url_lrg(self):
        if self.image:
            ext = self.image.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return self.image.name.replace('/static/routes/', '/static/images/routes/').replace('/routes/','/routes/cache/')+'.320x190_q95_crop.'+ext
        else:
            return 'img/default.png'

    def thumb_url_xlrg(self):
        if self.image:
            ext = self.image.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return self.image.name.replace('/static/routes/', '/static/images/routes/').replace('/routes/','/routes/cache/')+'.910x680_q95_crop.'+ext
        else:
            return 'img/default.png'

    def thumb_320(self):
        if self.image:
            ext = self.image.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return self.image.name.replace('/static/routes/', '/static/images/routes/').replace('/routes/','/routes/cache/')+'.320x190_q95.'+ext
        else:
            return 'img/default.png'

    def thumb_480(self):
        if self.image:
            ext = self.image.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return self.image.name.replace('/static/items/', '/static/images/items/').replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.480x360_q95.'+ext
        else:
            return 'img/default.png'

    def thumb_650(self):
        if self.image:
            ext = self.image.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return self.image.name.replace('/static/routes/', '/static/images/routes/').replace('/routes/','/routes/cache/')+'.650x410_q95_crop-top.'+ext
        else:
            return 'img/default.png'

    def thumb_745(self):
        if self.image:
            ext = self.image.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return self.image.name.replace('items/routes/', 'images/items/routes/cache/')+'.745x500_q95_crop-top.'+ext
        else:
            return 'img/default.png'

    def thumb_910(self):
        if self.image:
            ext = self.image.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return self.image.name.replace('items/routes/', 'images/items/routes/cache/')+'.910x680_q95.'+ext
        else:
            return 'img/default.png'

    def get_thumbnail_910(self):
        if self.image:
            ext = self.image.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return settings.MEDIA_URL+'images/'+self.image.name.replace('items/routes/', 'items/routes/cache/')+'.910x680_q95.'+ext
        else:
            return ''

    def get_thumbnail_1920(self):
        if self.image:
            ext = self.image.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return settings.MEDIA_URL+'images/'+self.image.name.replace('items/routes/', 'items/routes/cache/')+'.1920x1440_q95_crop.'+ext
        else:
            return ''

    def thumb_480x360(self):
        if self.image:
            ext = self.image.name.split(".")[-1].lower()
            if ext == 'jpeg' or ext == 'bmp':
                ext = 'jpg'
            return self.image.name.replace('/static/items/', '/static/images/items/').replace('/main/','/main/cache/').replace('/users/','/users/cache/')+'.480x360_q95.'+ext
        else:
            return 'img/default.png'

class PeakRouteStepArchive(models.Model):
    route_archive = models.ForeignKey(PeakRouteArchive, related_name='photos_archive', on_delete=models.CASCADE)
    step_number = models.IntegerField()
    #image = ThumbnailerImageField(upload_to='items/users', null = True, blank = True)
    image = ImageField(upload_to='items/routes', null = True, blank = True)
    description = models.CharField(max_length=1000, null=True, blank=True)
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()

    def __unicode__(self):
        return '%s photo' % (self.route_archive.name)


class Companions(models.Model):
    summit_log = models.ForeignKey(SummitLog, related_name="companions", on_delete=models.CASCADE)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    user_relation = models.ForeignKey(UserRelation, on_delete=models.CASCADE)

    def __unicode__(self):
        if self.user_relation.to_user:
            message = u"%s of %s in %s" % (self.user_relation.to_user.username, self.user.username,self.summit_log.item.name)
        else:
            message = u"%s of %s in %s" % (self.user_relation.first_name, self.user.username,self.summit_log.item.name)
        return message

    def save(self, *args, **kwargs):
        if not Companions.objects.filter(summit_log = self.summit_log,user = self.user, user_relation = self.user_relation).exists():
            super(Companions, self).save(*args, **kwargs)

    class Meta:
        verbose_name_plural = 'companions'


#deprecated
class SummitFellowBagger(models.Model):
    summit_log = models.ForeignKey(SummitLog, related_name='fellow_baggers', on_delete=models.CASCADE)
    name = models.CharField(max_length=255)
    email = models.EmailField(null = True, blank = True)
    resolved_relation = models.BooleanField(default=False)

    def __unicode__(self):
        return self.name


class ItemKmlData(models.Model):
    item = models.ForeignKey(Item, related_name='peaks', on_delete=models.DO_NOTHING)
    url = models.URLField(null=False)

    created = CreationDateTimeField()
    modified = ModificationDateTimeField()

    def __unicode__(self):
        return self.url


FIELDS_ELEVATION = 1
FIELDS_COORDS = 2
FIELDS_RANGE = 3
FIELDS_PROMINENCE = 4
FIELDS_ALTERNATE_NAMES = 5
FIELDS_PHOTO = 6
FIELDS_NAME = 7
FIELDS_NEAREST_CITY = 8
FIELDS_REGION = 9
FIELDS_COUNTRY = 10
FIELDS_DELETE = 11
FIELDS_OTHER = 50

FIELDS_IN_ITEM_CORRECTION_CHOICES = (
    (FIELDS_ELEVATION,'Elevation'),
    (FIELDS_PROMINENCE,'Prominence'),
    (FIELDS_COORDS,'Coords'),
    (FIELDS_RANGE,'Range'),
    (FIELDS_ALTERNATE_NAMES,'Alternate Names'),
    (FIELDS_PHOTO, 'Photo'),
    (FIELDS_NAME,'Name'),
    (FIELDS_NEAREST_CITY, 'Nearest_city'),
    (FIELDS_REGION, 'Region'),
    (FIELDS_COUNTRY, 'Country'),
    (FIELDS_DELETE, 'Delete'),
    (FIELDS_OTHER,'Other'),
    )

STATUS_NEW = 1
STATUS_APPROVED = 2
STATUS_REJECTED = 3
STATUS_PROCESSING = 4

STATUS_IN_ITEM_CORRECTION_CHOICES =   (
    (STATUS_NEW,'New'),
    (STATUS_PROCESSING, 'Processing'),
    (STATUS_APPROVED,'Approved'),
    (STATUS_REJECTED,'Rejected'),
    )

class ItemCorrection(models.Model):
    status = models.SmallIntegerField(choices = STATUS_IN_ITEM_CORRECTION_CHOICES,default = 1,blank=False,null = False)
    user = models.ForeignKey(User, related_name='items_corrections', on_delete=models.SET_DEFAULT, default=1)
    item = models.ForeignKey(Item,related_name="corrections", on_delete=models.DO_NOTHING)
    field = models.SmallIntegerField(choices = FIELDS_IN_ITEM_CORRECTION_CHOICES , blank = False,null = False)
    original_value = models.TextField(verbose_name="Original Value" , blank=True,null=True)
    new_value = models.TextField(null = True, blank = True,verbose_name="New Value")
    original_photo = models.ForeignKey(ItemPhoto, null=True, related_name="original_photo", on_delete=models.CASCADE)
    new_photo = models.ForeignKey(ItemPhoto, null=True, related_name="new_photo", on_delete=models.CASCADE)
    location = models.PointField(blank=True,null=True)
    created = CreationDateTimeField()
    decision_date = models.DateTimeField(verbose_name="Decision Date",blank=True, null = True)
    systemMessages = models.TextField(blank=True,null=True,editable=False)

    objects = ItemManager()
    def __unicode__(self):
        return 'Correction in Item: %s' % (self.item.name)

    def __str__(self):
        return 'Correction in Item: %s' % (self.item.name)

    class Meta:
        get_latest_by = 'created'

    def save(self,*args,**kwargs):
        current_location = self.item.location
        if (not isinstance(self.field, int)):
            for key,value in FIELDS_IN_ITEM_CORRECTION_CHOICES:
                if lower(self.field) == lower(value):
                    self.field = key
                    break
        if self.field == 2:
            if self.location == None:
                lat = float( self.new_value.split(",")[1] )
                lon = float( self.new_value.split(",")[0] )
                point = Point(lat, lon)
                self.location = point
        super(ItemCorrection, self).save(*args, **kwargs)

    def new_as_height(self):
      ft = int(float(self.new_value))
      m = int(ft * 0.3048)
      return '%s ft / %s m' % (ft, m)
    
    def original_as_height(self):
      ft = int(float(self.original_value))
      m = int(ft * 0.3048)
      return '%s ft / %s m' % (ft, m)

    def original_photo_image(self):
        return '<img src="%s%s" width="800" />' % (settings.MEDIA_URL, self.original_photo.image)
    original_photo_image.allow_tags = True

    def new_photo_image(self):
        return '<img src="%s%s" width="800" />' % (settings.MEDIA_URL, self.new_photo.image)
    new_photo_image.allow_tags = True

    def send_approved_email(self):
        return self._send_approved_email()

    def _send_approved_email(self):
        new_photo_url = None
        if self.field == 6:
            #get thumbnail for new peak photo
            new_photo = ItemPhoto.objects.get(id=self.new_photo_id)
            new_photo_url = new_photo.get_thumbnail_910()
        #get the new peak
        new_item = Item.objects.get(id=self.item.id)
        subject = 'your %(name)s correction is now on peakery' % ({'name':new_item.name})
        body = render_to_string('emails/approved_item_correction.html', {'correction':self,'new_photo_url':new_photo_url,'peak':new_item,'user':self.user,'options':FIELDS_IN_ITEM_CORRECTION_CHOICES})
        #email_message(subject, body, to=[self.user.email], from_email=settings.DEFAULT_FROM_EMAIL_FEEDBACK)
        recipients = []
        recipients.append(self.user.email)
        msg = EmailMessage(subject,body,settings.DEFAULT_FROM_EMAIL_NOTIFICATIONS,recipients)
        msg.content_subtype = "html"
        msg.send()

    def send_rejected_email(self):
        subject = 'your %(name)s correction was rejected' % ({'name':self.item.name})
        body = render_to_string('emails/rejected_item_correction.html', {'correction':self,'peak':self.item,'user':self.user})
        #email_message(subject, body, to=[self.user.email], from_email=settings.DEFAULT_FROM_EMAIL_FEEDBACK)
        recipients = []
        recipients.append(self.user.email)
        msg = EmailMessage(subject,body,settings.DEFAULT_FROM_EMAIL_NOTIFICATIONS,recipients)
        msg.content_subtype = "html"
        msg.send()

class AlternateNameItemCorrection(models.Model):
    name = models.CharField(max_length=255)
    item = models.ForeignKey(ItemCorrection, related_name='alternate_name_field_ic', on_delete=models.DO_NOTHING)

    def __unicode__(self):
        return self.name

class CountryItemCorrection(models.Model):
    country = models.ForeignKey(Country, on_delete=models.DO_NOTHING)
    item = models.ForeignKey(ItemCorrection, related_name='country_field_ic', on_delete=models.DO_NOTHING)

    def __unicode__(self):
        return str(self.country)

class RegionItemCorrection(models.Model):
    region = models.ForeignKey(Region, on_delete=models.DO_NOTHING)
    item = models.ForeignKey(ItemCorrection, related_name='region_field_ic', on_delete=models.DO_NOTHING)

    def __unicode__(self):
        return str(self.region)

class ItemCorrectionRejectText(models.Model):
    text = models.TextField()
    itemcorrection = models.ForeignKey(ItemCorrection, related_name='reject_text', on_delete=models.DO_NOTHING)

    def __unicode__(self):
        return u'%s Reject Text' % self.itemcorrection.item.name

class ItemDuplicate(models.Model):
    status = models.SmallIntegerField(choices = STATUS_IN_ITEM_CORRECTION_CHOICES,default = 1,blank=False,null = False)
    user = models.ForeignKey(User, related_name='items_duplicates',blank=True,null = True, on_delete=models.SET_DEFAULT, default=1)
    item = models.ForeignKey(Item,related_name="duplicates",verbose_name="peak", on_delete=models.DO_NOTHING)
    item_dup = models.ForeignKey(Item,related_name="duplicates2",verbose_name="duplicate peak", on_delete=models.DO_NOTHING)
    distance = models.FloatField(verbose_name="Distance",editable=True,help_text="Distance in ft")
    created = CreationDateTimeField()
    decision_date = models.DateTimeField(verbose_name="Decision Date",blank=True, null = True)
    systemMessages = models.TextField(blank=True,null=True,editable=False)

    def resolve_duplicate(self,user=None):
        try:
            dup = self.item_dup
            summits = dup.summits.all()
            if summits:
                for summit in summits:
                    summit.item = self.item
                    summit.save()
            photos = dup.photos.all()
            if photos:
                for photo in photos:
                    photo.item = self.item
                    photo.save()
            comments = dup.comments.all()
            if comments:
                for comment in comments:
                    comment.item = self.item
                    comment.save()
            self.item_dup.delete()
        except:
            pass


class Itemauditlogentry(models.Model):
    peak_id = models.IntegerField(verbose_name="Peak Id")
    action_date = models.DateTimeField()
    action_user = models.ForeignKey(User, on_delete=models.SET_DEFAULT, default=1)
    action_type = models.CharField(max_length=1)
    name = models.CharField(max_length=255)
    location_old = models.PointField(verbose_name="Old Location",null=True,blank=True)
    location = models.PointField(null=True,blank=True)

    objects = GeoManager()

    class Meta:
        #db_table = u'items_itemauditlogentry'
        verbose_name_plural = u'item audit log entries'

    def __unicode__(self):
        message = u"%(user)s %(action)s %(peak)s"
        if self.action_type == "U":
            action = "Changed"
        elif self.action_type =="D":
            action = "Deleted"
        else:
            action = "Changed"
        message = message % ({'user': self.action_user if self.action_user else 'Auto' , 'action':action, 'peak': self.name})
        return message


class ItemRegionRanking(models.Model):
    item_id = models.IntegerField(null=False, blank=False, db_index=True)
    region_name = models.CharField(max_length=300)
    region_slug = models.CharField(max_length=300)
    country_slug = models.CharField(max_length=300)
    region_elevation_rank = models.IntegerField(null=False, blank=False)
    region_prominence_rank = models.IntegerField(null=False, blank=False)
    region_summits_rank = models.IntegerField(null=False, blank=False)
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()


class ItemCountryRanking(models.Model):
    item_id = models.IntegerField(null=False, blank=False, db_index=True)
    country_name = models.CharField(max_length=300)
    country_slug = models.CharField(max_length=300)
    country_elevation_rank = models.IntegerField(null=False, blank=False)
    country_prominence_rank = models.IntegerField(null=False, blank=False)
    country_summits_rank = models.IntegerField(null=False, blank=False)
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()
