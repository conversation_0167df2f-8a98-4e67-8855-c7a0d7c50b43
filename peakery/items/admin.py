from django.http import HttpResponseRedirect
from peakery.main.models import HomepageBackground
from django.contrib.gis import admin
from peakery.main.osmgeo_inline import OSMGeoTabularInline
from peakery.items.models import *
from peakery.background.tasks import task_approve_item_correction, task_reject_item_correction
from django import forms
from django.db import connection

class GoogleAdmin(admin.OSMGeoAdmin):
    default_lon = -8228293
    default_lat = 508764
    default_zoom = 5

class ItemPhotoInline(admin.TabularInline):
    model = ItemPhoto
    extra = 0
    raw_id_fields = ('user','category','summit_log')

    def queryset(self, request):
        from datetime import timedelta
        qs = super(ItemPhotoInline, self).queryset(request)
        if qs.count() > 10:
            return qs.filter(created__gt = datetime.today()-timedelta(90))
        else:
            return qs


class SummitLogCommentAdmin(admin.ModelAdmin):
    raw_id_fields = ('user','summit_log',)
    list_display = ('id', 'comment','created',)

class SummitLogInline(admin.TabularInline):
    model = SummitLog
    extra = 0
    raw_id_fields = ('user','route_up','route_down','peak_route')


class ItemGroupItemInline(admin.TabularInline):
    model = ItemGroupItem
    extra = 1

class ItemRegionAdmin(admin.TabularInline):
    model = ItemRegion
    raw_id_fields = ('region',)
    extra = 0

class ItemCountryAdmin(admin.TabularInline):
    model = ItemCountry
    raw_id_fields = ('country',)
    extra = 1
    max_num = 9999

class AlternateNameInline(admin.TabularInline):
    model = AlternateName
    extra = 1

class ItemAdmin(admin.ModelAdmin):
    list_display = ('name','lat','long', 'elevation')
    #inlines = [AlternateNameInline, ItemCountryAdmin,ItemRegionAdmin,SummitLogInline, ItemPhotoInline, ItemCommentInline]
    inlines = [AlternateNameInline, ItemCountryAdmin, ItemRegionAdmin]
    search_fields = ('id','name',)
    raw_id_fields = ('user','region',)
    exclude = ['slug','old_slug','slug_new_text','alternate_names','name_unaccented','location','has_region','has_country','db_meta_description','txt_location', 'featured_summitlog_count', 'summitlog_count','db_description','wikipedia_source','saved_autodescription','flag_for_cache_deletion','near_city_from_correction']
    actions = ["delete_photo", ]

    def save_model(self, request, obj, form, change):
        obj.save()
        if form is not None and 'thumbnail' in form.changed_data:
            #if we have thumbnail data then a new file was uploaded
            if form.cleaned_data['thumbnail']:
                #new image uploaded via django admin, need to create thumbnails
                from PIL import Image, ImageFilter, ExifTags
                import cStringIO
                import os.path
                from django.core.files.storage import default_storage
                upload_path = 'items/main/%s' % (form.cleaned_data['thumbnail'].name)
                basefile = upload_path.split("/")[-1]
                extension = os.path.splitext(basefile)[1][1:].lower()
                if extension == 'png':
                    extension = 'PNG'
                else:
                    extension = 'JPEG'
                thumb_file_path = 'images/items/main/cache/%s' % (basefile)

                try:
                    f = default_storage.open(upload_path, 'r')
                    image = Image.open(f)

                    try:
                        for orientation in ExifTags.TAGS.keys():
                            if ExifTags.TAGS[orientation]=='Orientation':
                                break
                        exif=dict(image._getexif().items())

                        if exif[orientation] == 3:
                            image=image.rotate(180, expand=True)
                        elif exif[orientation] == 6:
                            image=image.rotate(270, expand=True)
                        elif exif[orientation] == 8:
                            image=image.rotate(90, expand=True)

                    except (AttributeError, KeyError, IndexError):
                        # cases: image don't have getexif
                        pass

                    width, height = image.size

                    #1250x600 thumbnail
                    if width >= height:
                        #landscape orientation photo
                        if float(width) / float(height) > 1.333333:
                            # possible panoramic photo, let's keep aspect ratio intact
                            left = 0
                            upper = 0
                            right = width
                            lower = height
                        else:
                            delta = height - (width / 1.333333)
                            left = 0
                            upper = int(delta/2)
                            right = width
                            lower = height - upper
                    else:
                        #portrait orientation photo
                        if float(height)/float(width) > 1.333333:
                            delta = height - (1.333333 * width)
                            left = 0
                            upper = int(delta/2)
                            right = width
                            lower = height - upper
                        else:
                            delta = width - (height / 1.333333)
                            left = int(delta/2)
                            upper = 0
                            right = width - left
                            lower = height

                    tmp_image = image.crop((left, upper, right, lower))
                    if width >= height:
                        new_width = 1250
                        if float(width) / float(height) > 1.333333:
                            # possible panoramic photo, let's keep aspect ratio intact
                            aspect_ratio = float(width) / float(height)
                            new_height = int(1250 / aspect_ratio)
                        else:
                            new_height = 600
                    else:
                        new_width = 600
                        new_height = 1250
                    tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
                    f_thumb = default_storage.open('%s.%s' % (thumb_file_path,'1250x600_q95_crop--0,0_upscale.jpg'), "w")
                    #NOTE, we're saving the image into a cStringIO object to avoid writing to disk
                    out_img = cStringIO.StringIO()
                    #You MUST specify the file type because there is no file name to discern it from
                    tmp_image.save(out_img, 'JPEG')
                    f_thumb.write(out_img.getvalue())
                    f_thumb.close()

                    # 480x360 thumbnail
                    if width >= height:
                        # landscape orientation photo
                        if float(width) / float(height) > 1.333333:
                            delta = width - (1.333333 * height)
                            left = int(delta / 2)
                            upper = 0
                            right = width - left
                            lower = height
                        else:
                            delta = height - (width / 1.333333)
                            left = 0
                            upper = int(delta / 2)
                            right = width
                            lower = height - upper
                    else:
                        # portrait orientation photo
                        if float(height) / float(width) > 1.333333:
                            delta = height - (1.333333 * width)
                            left = 0
                            upper = int(delta / 2)
                            right = width
                            lower = height - upper
                        else:
                            delta = width - (height / 1.333333)
                            left = int(delta / 2)
                            upper = 0
                            right = width - left
                            lower = height

                    tmp_image = image.crop((left, upper, right, lower))
                    if width >= height:
                        new_width = 480
                        new_height = 360
                    else:
                        new_width = 360
                        new_height = 480
                    tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
                    tmp_image = tmp_image.filter(ImageFilter.SHARPEN)
                    f_thumb = default_storage.open('%s.%s' % (thumb_file_path, '480x360_q95.jpg'), "w")
                    # NOTE, we're saving the image into a cStringIO object to avoid writing to disk
                    out_img = cStringIO.StringIO()
                    # You MUST specify the file type because there is no file name to discern it from
                    tmp_image.save(out_img, format=extension, subsampling=0, quality=95)
                    f_thumb.write(out_img.getvalue())
                    f_thumb.close()

                    #320x240 thumbnail
                    if width >= height:
                        #landscape orientation photo
                        if float(width)/float(height) > 1.333333:
                            delta = width - (1.333333 * height)
                            left = int(delta/2)
                            upper = 0
                            right = width - left
                            lower = height
                        else:
                            delta = height - (width / 1.333333)
                            left = 0
                            upper = int(delta/2)
                            right = width
                            lower = height - upper
                    else:
                        #portrait orientation photo
                        if float(height)/float(width) > 1.333333:
                            delta = height - (1.333333 * width)
                            left = 0
                            upper = int(delta/2)
                            right = width
                            lower = height - upper
                        else:
                            delta = width - (height / 1.333333)
                            left = int(delta/2)
                            upper = 0
                            right = width - left
                            lower = height

                    tmp_image = image.crop((left, upper, right, lower))
                    if width >= height:
                        new_width = 320
                        new_height = 240
                    else:
                        new_width = 240
                        new_height = 320
                    tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
                    tmp_image = tmp_image.filter(ImageFilter.SHARPEN)
                    f_thumb = default_storage.open('%s.%s' % (thumb_file_path,'320x240_q95.jpg'), "w")
                    #NOTE, we're saving the image into a cStringIO object to avoid writing to disk
                    out_img = cStringIO.StringIO()
                    #You MUST specify the file type because there is no file name to discern it from
                    tmp_image.save(out_img, format=extension, subsampling=0, quality=95)
                    f_thumb.write(out_img.getvalue())
                    f_thumb.close()

                    #120x90 thumbnail
                    if width >= height:
                        #landscape orientation photo
                        if float(width)/float(height) > 1.333333:
                            delta = width - (1.333333 * height)
                            left = int(delta/2)
                            upper = 0
                            right = width - left
                            lower = height
                        else:
                            delta = height - (width / 1.333333)
                            left = 0
                            upper = int(delta/2)
                            right = width
                            lower = height - upper
                    else:
                        #portrait orientation photo
                        if float(height)/float(width) > 1.333333:
                            delta = height - (1.333333 * width)
                            left = 0
                            upper = int(delta/2)
                            right = width
                            lower = height - upper
                        else:
                            delta = width - (height / 1.333333)
                            left = int(delta/2)
                            upper = 0
                            right = width - left
                            lower = height

                    tmp_image = image.crop((left, upper, right, lower))
                    if width >= height:
                        new_width = 120
                        new_height = 90
                    else:
                        new_width = 90
                        new_height = 120
                    tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
                    f_thumb = default_storage.open('%s.%s' % (thumb_file_path,'120x90_q95_crop.jpg'), "w")
                    #NOTE, we're saving the image into a cStringIO object to avoid writing to disk
                    out_img = cStringIO.StringIO()
                    #You MUST specify the file type because there is no file name to discern it from
                    tmp_image.save(out_img, extension)
                    f_thumb.write(out_img.getvalue())
                    f_thumb.close()

                    #70x50 thumbnail
                    if width >= height:
                        #landscape orientation photo
                        if float(width)/float(height) > 1.4:
                            delta = width - (1.4 * height)
                            left = int(delta/2)
                            upper = 0
                            right = width - left
                            lower = height
                        else:
                            delta = height - (width / 1.4)
                            left = 0
                            upper = int(delta/2)
                            right = width
                            lower = height - upper
                    else:
                        #portrait orientation photo
                        if float(height)/float(width) > 1.4:
                            delta = height - (1.4 * width)
                            left = 0
                            upper = int(delta/2)
                            right = width
                            lower = height - upper
                        else:
                            delta = width - (height / 1.4)
                            left = int(delta/2)
                            upper = 0
                            right = width - left
                            lower = height

                    tmp_image = image.crop((left, upper, right, lower))
                    if width >= height:
                        new_width = 70
                        new_height = 50
                    else:
                        new_width = 50
                        new_height = 70
                    tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
                    f_thumb = default_storage.open('%s.%s' % (thumb_file_path,'70x50_q95_crop.jpg'), "w")
                    #NOTE, we're saving the image into a cStringIO object to avoid writing to disk
                    out_img = cStringIO.StringIO()
                    #You MUST specify the file type because there is no file name to discern it from
                    tmp_image.save(out_img, extension)
                    f_thumb.write(out_img.getvalue())
                    f_thumb.close()

                    #910x680 thumbnail
                    if width >= height:
                        # landscape orientation photo
                        if float(width) / float(height) > 1.338235:
                            # possible panoramic photo, let's keep aspect ratio intact
                            left = 0
                            upper = 0
                            right = width
                            lower = height
                        else:
                            delta = height - (width / 1.338235)
                            left = 0
                            upper = int(delta/2)
                            right = width
                            lower = height - upper
                    else:
                        #portrait orientation photo
                        if float(height)/float(width) > 1.338235:
                            delta = height - (1.338235 * width)
                            left = 0
                            upper = int(delta/2)
                            right = width
                            lower = height - upper
                        else:
                            delta = width - (height / 1.338235)
                            left = int(delta/2)
                            upper = 0
                            right = width - left
                            lower = height

                    tmp_image = image.crop((left, upper, right, lower))
                    if width >= height:
                        new_width = 910
                        if float(width) / float(height) > 1.338235:
                            # possible panoramic photo, let's keep aspect ratio intact
                            aspect_ratio = float(width) / float(height)
                            new_height = int(910 / aspect_ratio)
                        else:
                            new_height = 680
                    else:
                        new_width = 680
                        new_height = 910
                    tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
                    tmp_image = tmp_image.filter(ImageFilter.SHARPEN)
                    f_thumb = default_storage.open('%s.%s' % (thumb_file_path,'910x680_q95.jpg'), "w")
                    #NOTE, we're saving the image into a cStringIO object to avoid writing to disk
                    out_img = cStringIO.StringIO()
                    #You MUST specify the file type because there is no file name to discern it from
                    tmp_image.save(out_img, format=extension, subsampling=0, quality=95)
                    f_thumb.write(out_img.getvalue())
                    f_thumb.close()

                    #745x500 thumbnail
                    if width >= height:
                        #landscape orientation photo
                        if float(width)/float(height) > 1.49:
                            delta = width - (1.49 * height)
                            left = int(delta/2)
                            upper = 0
                            right = width - left
                            lower = height
                        else:
                            delta = height - (width / 1.49)
                            left = 0
                            upper = int(delta/2)
                            right = width
                            lower = height - upper
                    else:
                        #portrait orientation photo
                        if float(height)/float(width) > 1.49:
                            delta = height - (1.49 * width)
                            left = 0
                            upper = int(delta/2)
                            right = width
                            lower = height - upper
                        else:
                            delta = width - (height / 1.49)
                            left = int(delta/2)
                            upper = 0
                            right = width - left
                            lower = height

                    tmp_image = image.crop((left, upper, right, lower))
                    if width >= height:
                        new_width = 745
                        new_height = 500
                    else:
                        new_width = 500
                        new_height = 745
                    tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)

                    f_thumb = default_storage.open('%s.%s' % (thumb_file_path,'745x500_q95_crop-top.jpg'), "w")
                    #NOTE, we're saving the image into a cStringIO object to avoid writing to disk
                    out_img = cStringIO.StringIO()
                    #You MUST specify the file type because there is no file name to discern it from
                    tmp_image.save(out_img, extension)
                    f_thumb.write(out_img.getvalue())
                    f_thumb.close()

                except Exception as e:
                    width,height = [-1,-1]

    def delete_photo(modeladmin, request, queryset):
        item = queryset.get()
        item.thumbnail = None
        item.thumbnail_source = None
        item.thumbnail_credit = None
        item.save()
        try:
            photos = ItemPhoto.objects.filter(category__name='peak', item=item)
            print(photos)
            for photo in photos:
                if photo.summit_log:
                    category = ItemPhotoCategory.objects.get(name='summit')
                    photo.category = category
                    photo.save()
                    modeladmin.message_user(request,"The photo was moved to the summitlog")
                else:
                    photo.delete()
                    modeladmin.message_user(request, "The photo was deleted")
            if not photos:
                modeladmin.message_user(request, "The photo was moved to the summitlog")
        except ItemPhoto.DoesNotExist:
            modeladmin.message_user(request, "No ItemPhoto object related... Done.")

class ItemGroupAdmin(admin.ModelAdmin):
    raw_id_fields = ("user","region",)
    list_display = ('name', 'created', 'modified', 'continent', 'country', 'order','category','lat','long','show_on_geochart')
    list_editable = ['order', 'continent', 'country','category','lat','long','show_on_geochart']
    list_filter = ('country',)
    actions = ["center_geochart"]

    def center_geochart(modeladmin, request, queryset):
        for challenge in queryset:
            sql = "update items_itemgroup " + \
                "set lat = b.lat, " + \
                "long = b.lng " + \
                "from ( " + \
                "select a.id, ST_Y(ST_Centroid(ST_Union(st_makepoint(c.long, c.lat)))) as lat, ST_X(ST_Centroid(ST_Union(st_makepoint(c.long, c.lat)))) as lng " + \
                "from items_itemgroup a, items_itemgroupitem b, items_item c " + \
                "where a.id = b.group_id " + \
                "and b.item_id = c.id " + \
                "group by a.id) b " + \
                "where items_itemgroup.id = %s " + \
                "and items_itemgroup.id = b.id "
            with connection.cursor() as cursor:
                cursor.execute(sql, [challenge.id])

    def save_model(self, request, obj, form, change):
        obj.save()
        if form is not None and 'thumbnail' in form.changed_data:
            #new image uploaded via django admin, need to create thumbnails
            from PIL import Image, ImageFilter, ExifTags
            from io import BytesIO
            import os.path
            from django.core.files.storage import default_storage
            if form.cleaned_data['thumbnail']:
                upload_path = str(obj.thumbnail)
                basefile = upload_path.split("/")[-1]
                extension = os.path.splitext(basefile)[1][1:].lower()
                if extension == 'png':
                    extension = 'PNG'
                else:
                    extension = 'JPEG'
                thumb_file_path = 'images/items/lists/cache/%s' % (basefile)

                try:
                    f = default_storage.open(upload_path, 'rb')
                    image = Image.open(f)

                    try:
                        for orientation in ExifTags.TAGS.keys():
                            if ExifTags.TAGS[orientation]=='Orientation':
                                break
                        exif=dict(image._getexif().items())

                        if exif[orientation] == 3:
                            image=image.rotate(180, expand=True)
                        elif exif[orientation] == 6:
                            image=image.rotate(270, expand=True)
                        elif exif[orientation] == 8:
                            image=image.rotate(90, expand=True)

                    except (AttributeError, KeyError, IndexError):
                        # cases: image don't have getexif
                        pass

                    width, height = image.size

                    #910x680 thumbnail
                    if width >= height:
                        #landscape orientation photo
                        if float(width)/float(height) > 1.338235:
                            delta = width - (1.338235 * height)
                            left = int(delta/2)
                            upper = 0
                            right = width - left
                            lower = height
                        else:
                            delta = height - (width / 1.338235)
                            left = 0
                            upper = int(delta/2)
                            right = width
                            lower = height - upper
                    else:
                        #portrait orientation photo
                        if float(height)/float(width) > 1.338235:
                            delta = height - (1.338235 * width)
                            left = 0
                            upper = int(delta/2)
                            right = width
                            lower = height - upper
                        else:
                            delta = width - (height / 1.338235)
                            left = int(delta/2)
                            upper = 0
                            right = width - left
                            lower = height

                    tmp_image = image.crop((left, upper, right, lower))
                    if width >= height:
                        new_width = 910
                        new_height = 680
                    else:
                        new_width = 680
                        new_height = 910
                    tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
                    tmp_image = tmp_image.filter(ImageFilter.SHARPEN)
                    f_thumb = default_storage.open('%s.%s' % (thumb_file_path,'910x680_q95.jpg'), "w")
                    #NOTE, we're saving the image into a cStringIO object to avoid writing to disk
                    with BytesIO() as out_img:
                        #You MUST specify the file type because there is no file name to discern it from
                        tmp_image.save(out_img, format=extension, subsampling=0, quality=95)
                        f_thumb.write(out_img.getvalue())
                        f_thumb.close()
                        tmp_image.close()

                    #350x245 thumbnail
                    if width >= height:
                        #landscape orientation photo
                        if float(width)/float(height) > 1.429:
                            delta = width - (1.429 * height)
                            left = int(delta/2)
                            upper = 0
                            right = width - left
                            lower = height
                        else:
                            delta = height - (width / 1.429)
                            left = 0
                            upper = int(delta/2)
                            right = width
                            lower = height - upper
                    else:
                        #portrait orientation photo
                        if float(height)/float(width) > 1.429:
                            delta = height - (1.429 * width)
                            left = 0
                            upper = int(delta/2)
                            right = width
                            lower = height - upper
                        else:
                            delta = width - (height / 1.429)
                            left = int(delta/2)
                            upper = 0
                            right = width - left
                            lower = height

                    tmp_image = image.crop((left, upper, right, lower))
                    if width >= height:
                        new_width = 350
                        new_height = 245
                    else:
                        new_width = 245
                        new_height = 350
                    tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
                    f_thumb = default_storage.open('%s.%s' % (thumb_file_path,'350x245_q95_crop.jpg'), "w")
                    #NOTE, we're saving the image into a cStringIO object to avoid writing to disk
                    with BytesIO() as out_img:
                        #You MUST specify the file type because there is no file name to discern it from
                        tmp_image.save(out_img, extension)
                        f_thumb.write(out_img.getvalue())
                        f_thumb.close()
                        tmp_image.close()

                    #340x262 thumbnail
                    if width >= height:
                        #landscape orientation photo
                        if float(width)/float(height) > 1.300:
                            delta = width - (1.300 * height)
                            left = int(delta/2)
                            upper = 0
                            right = width - left
                            lower = height
                        else:
                            delta = height - (width / 1.300)
                            left = 0
                            upper = int(delta/2)
                            right = width
                            lower = height - upper
                    else:
                        #portrait orientation photo
                        if float(height)/float(width) > 1.300:
                            delta = height - (1.300 * width)
                            left = 0
                            upper = int(delta/2)
                            right = width
                            lower = height - upper
                        else:
                            delta = width - (height / 1.300)
                            left = int(delta/2)
                            upper = 0
                            right = width - left
                            lower = height

                    tmp_image = image.crop((left, upper, right, lower))
                    if width >= height:
                        new_width = 340
                        new_height = 262
                    else:
                        new_width = 262
                        new_height = 340
                    tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
                    f_thumb = default_storage.open('%s.%s' % (thumb_file_path,'340x262_q95_crop.jpg'), "w")
                    #NOTE, we're saving the image into a cStringIO object to avoid writing to disk
                    with BytesIO() as out_img:
                        #You MUST specify the file type because there is no file name to discern it from
                        tmp_image.save(out_img, extension)
                        f_thumb.write(out_img.getvalue())
                        f_thumb.close()
                        tmp_image.close()

                    # 480x360 thumbnail
                    if width >= height:
                        # landscape orientation photo
                        if float(width) / float(height) > 1.333333:
                            delta = width - (1.333333 * height)
                            left = int(delta / 2)
                            upper = 0
                            right = width - left
                            lower = height
                        else:
                            delta = height - (width / 1.333333)
                            left = 0
                            upper = int(delta / 2)
                            right = width
                            lower = height - upper
                    else:
                        # portrait orientation photo
                        if float(height) / float(width) > 1.333333:
                            delta = height - (1.333333 * width)
                            left = 0
                            upper = int(delta / 2)
                            right = width
                            lower = height - upper
                        else:
                            delta = width - (height / 1.333333)
                            left = int(delta / 2)
                            upper = 0
                            right = width - left
                            lower = height

                    tmp_image = image.crop((left, upper, right, lower))
                    if width >= height:
                        new_width = 480
                        new_height = 360
                    else:
                        new_width = 360
                        new_height = 480
                    tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
                    tmp_image = tmp_image.filter(ImageFilter.SHARPEN)
                    f_thumb = default_storage.open('%s.%s' % (thumb_file_path, '480x360_q95.jpg'), "w")
                    # NOTE, we're saving the image into a cStringIO object to avoid writing to disk
                    with BytesIO() as out_img:
                        # You MUST specify the file type because there is no file name to discern it from
                        tmp_image.save(out_img, format=extension, subsampling=0, quality=95)
                        f_thumb.write(out_img.getvalue())
                        f_thumb.close()
                        tmp_image.close()

                    #320x240 thumbnail
                    if width >= height:
                        #landscape orientation photo
                        if float(width)/float(height) > 1.333333:
                            delta = width - (1.333333 * height)
                            left = int(delta/2)
                            upper = 0
                            right = width - left
                            lower = height
                        else:
                            delta = height - (width / 1.333333)
                            left = 0
                            upper = int(delta/2)
                            right = width
                            lower = height - upper
                    else:
                        #portrait orientation photo
                        if float(height)/float(width) > 1.333333:
                            delta = height - (1.333333 * width)
                            left = 0
                            upper = int(delta/2)
                            right = width
                            lower = height - upper
                        else:
                            delta = width - (height / 1.333333)
                            left = int(delta/2)
                            upper = 0
                            right = width - left
                            lower = height

                    tmp_image = image.crop((left, upper, right, lower))
                    if width >= height:
                        new_width = 320
                        new_height = 240
                    else:
                        new_width = 240
                        new_height = 320
                    tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
                    tmp_image = tmp_image.filter(ImageFilter.SHARPEN)
                    f_thumb = default_storage.open('%s.%s' % (thumb_file_path,'320x240_q95.jpg'), "w")
                    #NOTE, we're saving the image into a cStringIO object to avoid writing to disk
                    with BytesIO() as out_img:
                        #You MUST specify the file type because there is no file name to discern it from
                        tmp_image.save(out_img, format=extension, subsampling=0, quality=95)
                        f_thumb.write(out_img.getvalue())
                        f_thumb.close()
                        tmp_image.close()

                    #100x80 thumbnail
                    if width >= height:
                        #landscape orientation photo
                        if float(width)/float(height) > 1.25:
                            delta = width - (1.25 * height)
                            left = int(delta/2)
                            upper = 0
                            right = width - left
                            lower = height
                        else:
                            delta = height - (width / 1.25)
                            left = 0
                            upper = int(delta/2)
                            right = width
                            lower = height - upper
                    else:
                        #portrait orientation photo
                        if float(height)/float(width) > 1.25:
                            delta = height - (1.25 * width)
                            left = 0
                            upper = int(delta/2)
                            right = width
                            lower = height - upper
                        else:
                            delta = width - (height / 1.25)
                            left = int(delta/2)
                            upper = 0
                            right = width - left
                            lower = height

                    tmp_image = image.crop((left, upper, right, lower))
                    if width >= height:
                        new_width = 100
                        new_height = 80
                    else:
                        new_width = 80
                        new_height = 100
                    tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
                    f_thumb = default_storage.open('%s.%s' % (thumb_file_path,'100x80_q95_crop.jpg'), "w")
                    #NOTE, we're saving the image into a cStringIO object to avoid writing to disk
                    with BytesIO() as out_img:
                        #You MUST specify the file type because there is no file name to discern it from
                        tmp_image.save(out_img, extension)
                        f_thumb.write(out_img.getvalue())
                        f_thumb.close()
                        tmp_image.close()

                    #70x70 thumbnail
                    if width >= height:
                        #landscape orientation photo
                        if float(width)/float(height) > 1.0:
                            delta = width - (1.0 * height)
                            left = int(delta/2)
                            upper = 0
                            right = width - left
                            lower = height
                        else:
                            delta = height - (width / 1.0)
                            left = 0
                            upper = int(delta/2)
                            right = width
                            lower = height - upper
                    else:
                        #portrait orientation photo
                        if float(height)/float(width) > 1.0:
                            delta = height - (1.0 * width)
                            left = 0
                            upper = int(delta/2)
                            right = width
                            lower = height - upper
                        else:
                            delta = width - (height / 1.0)
                            left = int(delta/2)
                            upper = 0
                            right = width - left
                            lower = height

                    tmp_image = image.crop((left, upper, right, lower))
                    if width >= height:
                        new_width = 70
                        new_height = 70
                    else:
                        new_width = 70
                        new_height = 70
                    tmp_image = tmp_image.resize((new_width, new_height), Image.LANCZOS)
                    f_thumb = default_storage.open('%s.%s' % (thumb_file_path,'70x70_q95_crop.jpg'), "w")
                    #NOTE, we're saving the image into a cStringIO object to avoid writing to disk
                    with BytesIO() as out_img:
                        #You MUST specify the file type because there is no file name to discern it from
                        tmp_image.save(out_img, extension)
                        f_thumb.write(out_img.getvalue())
                        f_thumb.close()
                        tmp_image.close()

                except Exception as e:
                    width,height = [-1,-1]

class ItemGroupItemAdmin(admin.ModelAdmin):
    list_display = ('item', 'group', 'order',)
    list_editable = ['order']
    search_fields = ('group__name','item__name',)
    list_filter = ('group',)
    raw_id_fields = ('item',)

class ItemGroupCategoryAdmin(admin.ModelAdmin):
    ordering = ('order',)

class SummitLogAdmin(admin.ModelAdmin):
    list_display = ('id', 'item', 'date', 'user', 'created', 'log_source', 'gpx_file_url')
    ordering = ('-created',)
    raw_id_fields = ('item','user', 'route_up', 'route_down','peak_route')
    readonly_fields = ['trip_metadata','gpx_file','summitlog_group']
    actions = ["set_as_homepage_background"]
    search_fields = ('user__username','item__name','log_source')
    list_filter = ('log_source',)
    date_hierarchy = 'created'

    def save_model(self, request, obj, form, change):
        obj.save(update_fields=form.changed_data)

    def queryset(self, request):
        qs = super(SummitLogAdmin, self).queryset(request)
        qs = qs.filter(status=1)
        return qs

    def set_as_homepage_background(modeladmin, request, queryset):
        summit = queryset.get()
        if summit.item.can_be_homepage():
            bg, created = HomepageBackground.objects.get_or_create(id=1)
            bg.summit_log = summit
            bg.summit_log_user_photo = None
            bg.save()
            modeladmin.message_user(request, "Done.")
        else:
            modeladmin.message_user(request, "ERROR: Aborted: The image doesnt have the required size to be a homepage background")

class ItemErrorReportAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'item', 'get_email', 'revised', 'created')
    ordering = ('-created',)
    raw_id_fields = ('item','user',)

class PeakRouteAdmin(admin.ModelAdmin):
    list_display = ('id', 'item', 'name', 'user', 'gpx_file', 'created', 'modified')
    search_fields = ('user__username', 'item__name', 'name')
    ordering = ('-created',)
    raw_id_fields = ('item', 'user',)

class ItemPhotoAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'item', 'category', 'created')
    list_filter = ('category',)
    ordering = ('-created',)
    raw_id_fields = ('item','summit_log','user')
    actions = ["set_as_homepage_background"]
    search_fields = ('user__username','item__name', 'id')

    def set_as_homepage_background(modeladmin, request, queryset):
        photo = queryset.get()
        try:
            if photo.can_be_homepage():
                bg, created = HomepageBackground.objects.get_or_create(id=1)
                bg.summit_log_user_photo = photo
                bg.summit_log = None
                bg.save()
                modeladmin.message_user(request, "Done.")
            else:
                modeladmin.message_user(request, "ERROR: Aborted: The image doesnt have the required size to be a homepage background")
        except:
            modeladmin.message_user(request, "ERROR: Aborted: The image doesnt have the required size to be a homepage background")

'''
' ITEM CORRECTIONS
'''
class AlternateNameItemCorrectionInline(OSMGeoTabularInline):
    model = AlternateNameItemCorrection
    extra = 1
    template = "tabular.html"

class ItemCorrectionRejectTextInline(OSMGeoTabularInline):
    extra = 0
    model = ItemCorrectionRejectText
    raw_id_fields = ('itemcorrection',)
    template = "tabular.html"

from peakery.items.views import approve_item_correction, reject_item_correction

def approve(modeladmin, request, queryset):
    for item in queryset:
        print('approving item : %s' % item)
        task_approve_item_correction(item.id)
    queryset.update(status = STATUS_PROCESSING)
approve.short_description = "Mark as approved"

def reject(modeladmin, request, queryset):
    for item in queryset:
        print('rejecting item : %s' % item)
        task_reject_item_correction(item.id)
    queryset.update(status = STATUS_PROCESSING)

reject.short_description = "Mark as rejected"


class ItemCorrectionAdminForm(forms.ModelForm):
    new_value_in_meters = forms.FloatField(required=False, widget=forms.TextInput(attrs={'disabled':'disabled'}), help_text="Only applicable for corrections to Elevation values.")
    location_lat_lng = forms.CharField(required=False)

    class Meta:
        model = ItemCorrection
        fields = ['status', 'user', 'item', 'field', 'original_value', 'new_value', 'new_value_in_meters', 'location']

    def __init__(self, *args, **kwargs):
        super(ItemCorrectionAdminForm, self).__init__(*args, **kwargs)

        # Set the form fields based on the model object
        if 'instance' in kwargs:
            instance = kwargs['instance']
            if instance.field == FIELDS_ELEVATION:
                self.initial['new_value_in_meters'] = float(instance.new_value) * 0.3048



class ItemCorrectionAdmin(admin.ModelAdmin):

    list_display = ('item','status','user','field','original_value','new_value','created','decision_date')
    inlines = [ItemCorrectionRejectTextInline]
    readonly_fields = ('original_value','original_photo_image', 'new_photo_image')
    fields = ('status','user','item','field','original_value','new_value', 'new_value_in_meters', 'location_lat_lng', 'original_photo_image', 'new_photo_image')
    list_filter = ('field','status',)
    raw_id_fields = ('user','item',)
    ordering = ('status',)
    exclude = ('original_photo', 'new_photo')
    actions = [approve, reject]
    form = ItemCorrectionAdminForm

    def changelist_view(self, request, extra_context=None):
        if request.method == 'GET' and not request.GET.keys():
            redirect = reverse("admin:items_itemcorrection_changelist") + "?status__exact=1"
            return HttpResponseRedirect(redirect)
        else:
            result = super(ItemCorrectionAdmin, self).changelist_view(request, extra_context)
            return result

    def location_lat_lng(self):
        return self.new_value

'''
' ITEM DUPLICATES
'''

class ItemDuplicateAdmin(admin.ModelAdmin):

    list_display = ('item','item_dup','distance','status')
    list_display_links = ('item','item_dup')

    list_filter = ('status',)
    raw_id_fields = ('user','item','item_dup',)
    ordering = ('status',)
    exclude = ['decision_date','user']

class TopCompanionsAdmin(admin.ModelAdmin):
    search_fields = ('user__username',)
    list_display = ('__unicode__','user'    )
    raw_id_fields = ('user','summit_log','user_relation')

class SummitFellowBaggersAdmin(admin.ModelAdmin):
    search_fields = ('summit_log__id',)
    raw_id_fields = ('summit_log',)


class ItemsItemauditlogentryAdmin(admin.ModelAdmin):
    list_display = ('__unicode__','action_user',)
    search_fields = ('name','action_user__username')
    #exclude = ()

class GpxTrackAdmin(admin.ModelAdmin):
    list_display = ('id', 'date', 'user', 'created', 'gpx_file_url')
    ordering = ('-created',)
    raw_id_fields = ('user',)
    readonly_fields = ['gpx_file']
    search_fields = ('user__username',)
    date_hierarchy = 'created'

    def save_model(self, request, obj, form, change):
        obj.save(update_fields=form.changed_data)


admin.site.register(Item, ItemAdmin)
admin.site.register(ItemPhotoCategory)
admin.site.register(ItemPhoto, ItemPhotoAdmin)
admin.site.register(PeakRoute, PeakRouteAdmin)
admin.site.register(GpxTrack, GpxTrackAdmin)
admin.site.register(SummitLog, SummitLogAdmin)
admin.site.register(SummitLogComment, SummitLogCommentAdmin)
admin.site.register(ItemGroup, ItemGroupAdmin)
admin.site.register(ItemGroupItem, ItemGroupItemAdmin)
admin.site.register(ItemGroupCategory, ItemGroupCategoryAdmin)
#admin.site.register(ItemErrorReport, ItemErrorReportAdmin)
admin.site.register(ItemCorrection,ItemCorrectionAdmin)
#admin.site.register(ItemDuplicate,ItemDuplicateAdmin)
admin.site.register(Companions,TopCompanionsAdmin)
admin.site.register(Itemauditlogentry,ItemsItemauditlogentryAdmin)
#admin.site.register(AlternateName)
#admin.site.register(AlternateNameItemCorrection)


# admin.site.register(SummitFellowBagger,SummitFellowBaggersAdmin)
