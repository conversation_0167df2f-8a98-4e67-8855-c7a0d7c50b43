from django import template
import re

register = template.Library()
regex = re.compile(r'(([a-zA-Z]+)://[^ \t\n\r]+)', re.MULTILINE)

def linkify(value, arg=''):
    def _spacify(s, chars=40):
        if len(s) <= chars:
            return s
        for k in range(len(s) / chars):
            pos = (k + 1) * chars
            s = s[0:pos] + ' ' + s[pos:]
        return s
    def _replace(match):
        href = match.group(0)
        cls = arg and (' class="%s"' % arg) or ''
        return '<a href="%s"%s>%s</a>' % (href, cls, _spacify(href))
    return regex.sub(_replace, value)

register.filter('linkify', linkify)