from django.template.loader import render_to_string
from django.template.defaultfilters import floatformat
from django.contrib.gis.measure import Distance
from django import template
import re
from django.template.defaultfilters import stringfilter
from peakery.items.models import SummitLog

register = template.Library()
from peakery.items.utils import commify as commify_helper
from django.conf import settings
import os
try:
    from PIL import Image
except ImportError:
    import Image
from django.utils.translation import gettext as _

CONSONANT_SOUND = re.compile(r'''
one(![ir])
''', re.IGNORECASE|re.VERBOSE)
VOWEL_SOUND = re.compile(r'''
[aeio]|
u([aeiou]|[^n][^aeiou]|ni[^dmnl]|nil[^l])|
h(ier|onest|onou?r|ors\b|our(!i))|
[fhlmnrsx]\b
''', re.IGNORECASE|re.VERBOSE)
register = template.Library()

@register.filter
@stringfilter
def an(text):
    """
    Guess "a" vs "an" based on the phonetic value of the text.

    "An" is used for the following words / derivatives with an unsounded "h":
    heir, honest, hono[u]r, hors (d'oeuvre), hour

    "An" is used for single consonant letters which start with a vowel sound.

    "A" is used for appropriate words starting with "one".

    An attempt is made to guess whether "u" makes the same sound as "y" in
    "you".
    """
    if not CONSONANT_SOUND.match(text) and VOWEL_SOUND.match(text):
        return 'an'
    return 'a'

def get_summits_from_user(user, limit=10):
    return user.summit_log.all().order_by('-date')[0:limit]
register.simple_tag(get_summits_from_user)

def mi_km_by_mi(mi):
    if isinstance(mi,Distance):
        mi = mi.mi
    if mi != '':
        mi = float(mi)
        km = mi * 1.609344

        distance = '%s mi / %s km' % (floatformat(mi, 1), floatformat(km, 1))
        return distance
    else:
        return 0
register.simple_tag(mi_km_by_mi)


def mi_into_mi(mi):
    if isinstance(mi,Distance):
        mi = mi.mi
    if mi != '':
        mi = float(mi)

        distance = '%s mi' % (floatformat(mi, 1))
        return distance
    else:
        return 0
register.simple_tag(mi_into_mi)


def mi_into_km(mi):
    if isinstance(mi,Distance):
        mi = mi.mi
    if mi != '':
        mi = float(mi)
        km = mi * 1.609344

        distance = '%s km' % (floatformat(km, 1))
        return distance
    else:
        return 0
register.simple_tag(mi_into_km)

def mi_km_by_mi_int(mi):
    if isinstance(mi,Distance):
        mi = mi.mi
    if mi != '':
        mi = float(mi)
        km = mi * 1.609344

        distance = '%s mi / %s km' % (floatformat(mi, 0), floatformat(km, 0))
        return distance
    else:
        return 0
register.simple_tag(mi_km_by_mi_int)

def ft_m_by_ft(ft):
    if isinstance(ft,Distance):
        ft = ft.ft
    if ft != '':
        ft = float(ft)
        m = ft * 0.3048

        distance = '%s ft / %s m' % (commify_helper(floatformat(ft, 0)), commify_helper(floatformat(m, 0)))
        return distance
    else:
        return 0
register.simple_tag(ft_m_by_ft)

def ft_into_ft(ft):
    if isinstance(ft,Distance):
        ft = ft.ft
    if ft != '':
        ft = float(ft)
        m = ft * 0.3048

        distance = '%s ft' % (commify_helper(floatformat(ft, 0)))
        return distance
    else:
        return 0
register.simple_tag(ft_into_ft)

def ft_into_m(ft):
    if isinstance(ft,Distance):
        ft = ft.ft
    if ft != '':
        ft = float(ft)
        m = ft * 0.3048

        distance = '%s m' % (commify_helper(floatformat(m, 0)))
        return distance
    else:
        return 0
register.simple_tag(ft_into_m)

def sec_to_hrs_min(sec):
    if sec != '':
        sec = float(sec)
        hrs = int(sec / 3600)
        min = int((sec - (3600 * hrs))/60)
        if hrs > 0:
            time = '%s hr %s min' % (hrs, min)
        else:
            time = '%s min' % (min)
        return time
    else:
        return 0
register.simple_tag(sec_to_hrs_min)

def get_distance_from_mi(miles):
    pass

def mi2ft(miles):
    return commify_helper(int(miles * 5280))
register.filter('mi2ft', mi2ft)

def get_gmaps_lib():
    return render_to_string('gmaps/gmaps_lib.html')
register.simple_tag(get_gmaps_lib)

def change_peak_name(peak):
    peak_name = peak.name
    if "Mount" in peak_name:
        peak_name = peak_name.replace("Mount","Mnt.")

def commify(value):
    return commify_helper(value)
register.filter('commify', commify)

def get_image_width(path):
    try:
        path = path.split('static/')[1]
        complete_path = os.path.join(settings.MEDIA_ROOT, path)
        img = Image.open(complete_path)
        return img.size[0]
    except:
        pass
register.simple_tag(get_image_width)

def time_summited(my_summits, summit):
    #my_summits = my_summits.order_by('-date_entered','date')
    from_first = True
    val = "<p class='timeSummited'><span>%s time summited</span></p>"
    if not my_summits:
        my_summits = SummitLog.objects.filter(user = summit.user, item = summit.item).order_by('-date_entered','date')
        from_first = False
    for i, s in enumerate(my_summits):
        if i >= 0:
            if s == summit:
                if from_first or (i > 0 and not from_first):
                    return val % ordinal(i + 1)
                else:
                    return ""
        
register.simple_tag(time_summited)

def ordinal(value):
    """
    Converts an integer to its ordinal as a string. 1 is '1st', 2 is '2nd',
    3 is '3rd', etc. Works for any integer.
    """
    try:
        value = int(value)
    except (TypeError, ValueError):
        return value
    t = (_('th'), _('st'), _('nd'), _('rd'), _('th'), _('th'), _('th'), _('th'), _('th'), _('th'))
    if value % 100 in (11, 12, 13): # special case
        return u"%d%s" % (value, t[0])
    return u'%d<span class="ordinal">%s</span>' % (value, t[value % 10])

def url_target_blank(value):
    return re.sub("<a([^>]+)(?<!target=)>",'<a target="_blank"\\1>',value)

url_target_blank.is_safe = True
url_target_blank = register.filter(url_target_blank)


# Checks if a variable was set in the template
@register.simple_tag(takes_context=True)
def var_exists(context, name):
    dicts = context.dicts
    if dicts:
        for d in dicts:
            if name in d:
                return True
    return False
