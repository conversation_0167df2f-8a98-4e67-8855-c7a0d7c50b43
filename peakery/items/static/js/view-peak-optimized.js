/**
 * Optimized Peak View JavaScript
 * Consolidated and optimized functionality for peak view page
 */

var PeakView = (function() {
    'use strict';
    
    var config = {};
    var state = {
        uploaderIdle: true,
        photosDisplayed: 0,
        photosPage: 1,
        photos: [],
        initialHighlights: []
    };
    
    var cache = {
        $window: $(window),
        $document: $(document),
        $body: $('body')
    };
    
    // Utility functions
    var utils = {
        debounce: function(func, wait) {
            var timeout;
            return function executedFunction() {
                var context = this;
                var args = arguments;
                var later = function() {
                    timeout = null;
                    func.apply(context, args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },
        
        numberWithCommas: function(x) {
            return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        },
        
        openUrl: function(url) {
            window.location.href = url;
        },
        
        readCookie: function(name) {
            var nameEQ = name + "=";
            var ca = document.cookie.split(';');
            for(var i = 0; i < ca.length; i++) {
                var c = ca[i];
                while (c.charAt(0) === ' ') c = c.substring(1, c.length);
                if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
            }
            return null;
        }
    };
    
    // Photo management
    var photoManager = {
        init: function() {
            this.bindEvents();
            this.loadPhotos(1);
        },
        
        bindEvents: function() {
            // Photo hover effects - optimized with event delegation
            $("#photos-list, #more-photos-list").on({
                'mouseenter': function() {
                    $(this).children('.user-photo-info').fadeIn(200);
                },
                'mouseleave': function() {
                    $(this).children('.user-photo-info').fadeOut(200);
                }
            }, 'div');
            
            // See more photos
            $('#see-more-photos').on('click', function() {
                photoManager.loadPhotos(state.photosPage);
            });
            
            // Add photo functionality
            $('.add-photo').on('click', function() {
                var windowWidth = cache.$window.width();
                if (windowWidth < 768) {
                    $('.qq-upload-button-selector:eq(1)').find('input').trigger('click');
                } else {
                    $('#add-photo').modal('show');
                }
                return false;
            });
        },
        
        loadPhotos: function(page) {
            var self = this;
            
            if (page === 1) {
                $('#ajax-data-loading').show();
                $('#photos-list').empty();
                $('#more-photos-list').empty();
            } else {
                $('#more-ajax-data-loading').show();
            }
            
            var photoCount = config.photos_count;
            
            $.getJSON('/api/peak-photos/', {
                peak_id: config.peak_id,
                page: page
            })
            .done(function(data) {
                self.renderPhotos(data, page);
                self.updatePhotosUI(photoCount);
                state.photosPage++;
            })
            .fail(function() {
                console.error('Failed to load photos');
            })
            .always(function() {
                $('#ajax-data-loading, #more-ajax-data-loading').hide();
            });
        },
        
        renderPhotos: function(data, page) {
            var photoIndex = 1;
            var targetContainer = page === 1 ? '#photos-list' : '#more-photos-list';
            
            if (data.photos) {
                data.photos.forEach(function(photo) {
                    var photoHtml = self.buildPhotoHtml(photo, photoIndex);
                    $(targetContainer).append(photoHtml);
                    photoIndex++;
                    state.photosDisplayed++;
                    state.photos.push(photo.fullsize_url);
                });
            }
            
            // Update timeago
            $("time.timeago").timeago();
        },
        
        buildPhotoHtml: function(photo, index) {
            var photoCaption = photo.caption === 'None' ? '' : photo.caption;
            var adminMode = utils.readCookie('admin-mode');
            var removePhotoStyle = adminMode === 'true' ? '' : 'display: none;';
            
            return `
                <a id="gallery-photo-${photo.photo_id}" class="gallery-link" 
                   data-user="${photo.username}" data-description="${photoCaption}" 
                   data-gallery href="${photo.fullsize_url}">
                    <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-grid-${index}" 
                         style="cursor: pointer; padding: 0; background-image: url('${photo.thumbnail_url}'); 
                                background-size: cover; background-position: center; overflow: hidden;">
                        <div class="top-photos">
                            <div class="hover-photos">
                                <img src="/static/img/spacer.png" class="img-responsive peakeryPhoto photography peakimg-responsive">
                            </div>
                            <div class="user-photo-info peak-photo-with-caption" style="display: none;">
                                <span class="data photo-caption" style="position: absolute; bottom: -5px; left: 10px; color: #fff;">
                                    <p class="bagger" style="white-space: normal; font-size: 10px;">${photoCaption}</p>
                                    <p class="bagger" style="font-size: 10px;">${photo.username} • 
                                        <time class="timeago" datetime="${photo.created}T00:00:00">${photo.created}</time>
                                    </p>
                                </span>
                            </div>
                            <div style="${removePhotoStyle}" data-photoid="${photo.photo_id}" 
                                 class="admin-delete-photo remove-photo desktop-admin-link hidden-xs hidden-sm hidden-md">
                                <i class="fa fa-times"></i>
                            </div>
                        </div>
                    </div>
                </a>
            `;
        },
        
        updatePhotosUI: function(photoCount) {
            if (state.photosDisplayed < photoCount) {
                $('#see-more-photos-div').show();
                $('#photos-footer').show();
            } else {
                $('#see-more-photos-div').hide();
                $('#photos-footer').hide();
            }
        }
    };
    
    // Highlights management
    var highlightsManager = {
        init: function() {
            this.bindEvents();
        },
        
        bindEvents: function() {
            if (config.is_authenticated) {
                $('#edit-highlights-link-div').on('click', 'a', this.showEditForm);
                $('#edit-highlights-cancel').on('click', this.cancelEdit);
                $('#edit-highlights-save').on('click', this.saveHighlights);
                $('#peak-highlights-fieldset').on('keyup', 'textarea', this.handleTextareaKeyup);
            }
        },
        
        showEditForm: function() {
            $('#highlights-header').hide();
            $('#highlights-content').hide();
            $('#edit-highlights-header').fadeIn(300);
            $('#edit-highlights-form').fadeIn(300, function() {
                autosize($('.peak-highlight-input'));
                autosize.update($('.peak-highlight-input'));
            });
            return false;
        },
        
        cancelEdit: function() {
            highlightsManager.resetForm();
            highlightsManager.showViewMode();
            return false;
        },
        
        saveHighlights: function(event) {
            event.preventDefault();
            
            var $saveBtn = $('#edit-highlights-save');
            $saveBtn.html('<i class="fa fa-spinner fa-spin fa-fw"></i>').prop("disabled", true);
            
            $.ajax({
                type: "POST",
                url: `/peaks/edit_highlights/${config.peak_id}/`,
                data: $("#edithighlights_form").serialize(),
                success: function(data) {
                    highlightsManager.updateHighlights(data);
                    highlightsManager.showViewMode();
                    $saveBtn.html('Save highlights').prop("disabled", false);
                },
                error: function() {
                    alert('Error saving highlights. Please try again.');
                    $saveBtn.html('Save highlights').prop("disabled", false);
                }
            });
        },
        
        handleTextareaKeyup: function() {
            var index = $(this).data('index');
            var numFields = $('.peak-highlight-input').length;
            
            if (index === numFields && $(this).val().length > 0) {
                var newIndex = index + 1;
                if ($('#peak-highlight-' + newIndex).length === 0) {
                    $('#peak-highlights-fieldset').append(
                        `<div><textarea class="peak-highlight-input" name="peak-highlight-${newIndex}" 
                         data-index="${newIndex}" id="peak-highlight-${newIndex}" 
                         style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" 
                         placeholder="write a 1-2 sentence highlight about ${config.peak_name}..."></textarea></div>`
                    );
                }
            }
            
            autosize($('.peak-highlight-input'));
            autosize.update($('.peak-highlight-input'));
        },
        
        resetForm: function() {
            $('#peak-highlights-fieldset').empty();
            var newIndex = 1;
            
            state.initialHighlights.forEach(function(highlight) {
                $('#peak-highlights-fieldset').append(
                    `<div><textarea class="peak-highlight-input" name="peak-highlight-${newIndex}" 
                     data-index="${newIndex}" id="peak-highlight-${newIndex}" 
                     style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" 
                     placeholder="write a 1-2 sentence highlight about ${config.peak_name}...">${highlight}</textarea></div>`
                );
                newIndex++;
            });
            
            $('#peak-highlights-fieldset').append(
                `<div><textarea class="peak-highlight-input" name="peak-highlight-${newIndex}" 
                 data-index="${newIndex}" id="peak-highlight-new" 
                 style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" 
                 placeholder="write a 1-2 sentence highlight about ${config.peak_name}..."></textarea></div>`
            );
        },
        
        updateHighlights: function(data) {
            $('#peak-highlights-fieldset').empty();
            $('#highlights-list').empty();
            
            var newIndex = 1;
            state.initialHighlights = [];
            
            if (data.highlights) {
                data.highlights.forEach(function(highlight) {
                    $('#peak-highlights-fieldset').append(
                        `<div><textarea class="peak-highlight-input" name="peak-highlight-${newIndex}" 
                         data-index="${newIndex}" id="peak-highlight-${newIndex}" 
                         style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" 
                         placeholder="write a 1-2 sentence highlight about ${config.peak_name}...">${highlight}</textarea></div>`
                    );
                    $('#highlights-list').append(`<li style="list-style: initial; margin-bottom: 20px;">${highlight}</li>`);
                    state.initialHighlights.push(highlight);
                    newIndex++;
                });
            }
            
            $('#peak-highlights-fieldset').append(
                `<div><textarea class="peak-highlight-input" name="peak-highlight-${newIndex}" 
                 data-index="${newIndex}" id="peak-highlight-new" 
                 style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" 
                 placeholder="write a 1-2 sentence highlight about ${config.peak_name}..."></textarea></div>`
            );
        },
        
        showViewMode: function() {
            $('#edit-highlights-header').hide();
            $('#edit-highlights-form').hide();
            $('#highlights-header').fadeIn(300);
            
            var hasHighlights = state.initialHighlights.length > 0;
            if (hasHighlights) {
                $('#highlights-content').fadeIn(300);
                $('#edit-highlights-link').html('edit highlights');
            } else {
                $('#highlights-content').fadeOut(300);
                $('#edit-highlights-link').html('add a highlight!');
            }
        }
    };
    
    // Map management
    var mapManager = {
        init: function() {
            this.updateMapCanvasSize();
            this.bindEvents();
        },
        
        bindEvents: function() {
            cache.$window.on('resize', utils.debounce(this.updateMapCanvasSize, 250));
            
            $('#map-canvas').on('mousemove touchstart', function(e) {
                var offset = $(this).offset();
                var pageX = e.pageX || (e.originalEvent && e.originalEvent.touches[0].pageX);
                var pageY = e.pageY || (e.originalEvent && e.originalEvent.touches[0].pageY);
                // Store coordinates for potential use
            });
        },
        
        updateMapCanvasSize: function() {
            setTimeout(function() {
                var $mapCol = $('#peak-map-col');
                var $mapCanvas = $('#map-canvas');
                var $photoCol = $('#peak-photo-col');
                
                var mapHeight = $photoCol.height();
                $mapCanvas.height(mapHeight);
                
                // Update background size
                var minWidth = 1260;
                var minHeight = 945;
                var divWidth = $mapCol.width();
                var divHeight = $mapCol.height();
                
                if (divWidth >= minWidth && divHeight >= minHeight) {
                    $mapCol.css('background-size', 'cover');
                } else {
                    $mapCol.css('background-size', `${minWidth}px ${minHeight}px`);
                }
                
                // Center peak marker
                var centerMarkerTop = ($mapCanvas.height() / 2) - 10;
                var centerMarkerLeft = ($mapCanvas.width() / 2) - 10;
                $('#center-peak-marker').css({
                    'top': centerMarkerTop,
                    'left': centerMarkerLeft
                });
            }, 100);
        }
    };
    
    // Main initialization
    function init(options) {
        config = $.extend({}, options);
        state.initialHighlights = config.initial_highlights || [];
        
        // Cache frequently used elements
        cache.$photosList = $('#photos-list');
        cache.$morePhotosList = $('#more-photos-list');
        
        // Initialize modules
        photoManager.init();
        highlightsManager.init();
        mapManager.init();
        
        // Initialize slideshow
        $('#slideshow1').cycle({
            fx: 'fade'
        });
        
        // Bind global events
        bindGlobalEvents();
        
        console.log('PeakView initialized');
    }
    
    function bindGlobalEvents() {
        // Facebox events
        cache.$document.bind('beforeReveal.facebox', function() {
            $('.content').show();
            $('.close').show();
        });
        
        cache.$document.bind('loading.facebox', function() {
            $('.content').hide();
            $('.close').hide();
        });
        
        // Gallery events
        $('#blueimp-gallery').on('open', function() {
            $('body,html').css('overflow', 'visible');
        });
        
        // Peak row clicks
        cache.$document.on('click', '.peak-row', function() {
            var url = $(this).data('url');
            if (url) {
                utils.openUrl(url);
            }
        });
        
        // Mobile app integration
        $("#mobile-app-map-link").on('click', function() {
            if (typeof Android !== 'undefined') {
                Android.peakInfo(JSON.stringify(config));
            }
        });
    }
    
    // Public API
    return {
        init: init,
        utils: utils,
        photoManager: photoManager,
        highlightsManager: highlightsManager,
        mapManager: mapManager
    };
})();

// Global utility functions for backward compatibility
function openUrl(url) {
    PeakView.utils.openUrl(url);
}

function numberWithCommas(x) {
    return PeakView.utils.numberWithCommas(x);
}
