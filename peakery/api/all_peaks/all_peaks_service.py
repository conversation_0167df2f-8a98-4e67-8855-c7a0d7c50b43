'''
    This module's purpose is to load all peaks from the database, format the data, store it on disk, gzip the file, and then cache it on redis to be served restfully.
'''

import os
import json
import gzip
import codecs
import tempfile
from django.db import connection
from django.core.serializers.json import DjangoJSONEncoder
from peakery.cache import cache_manager

PASSCODE = "hRAlntXQqmJUnmVtoYgsVLs8OulWGmzTOoqT96GhkPA="

ALL_PEAKS_QUERY = """
        SELECT 
            a.id as peak_id,
            a.name,
            a.is_classic,
            a.lat::numeric as lat,
            a.long::numeric as lng,
            a.summitlog_count as summit_count,
            a.elevation,
            get_thumb(a.thumbnail, 480) as thumb_url,
            COALESCE(a.prominence, 0) as prominence,
            COALESCE(g.challenge_count, 0) as challenge_count,
            d.last_climbed,
              ARRAY(SELECT CONCAT(iir.region_id::text, ':', cr.name, ':', cc.name)
                  FROM cities_country cc, cities_region cr, items_item_region iir 
                  WHERE cc.id = cr.country_id AND cr.id = iir.region_id AND iir.item_id = a.id) as region_country_data
        FROM 
            items_item a
        LEFT JOIN 
            (SELECT g.item_id, count(g.id) as challenge_count 
             FROM items_itemgroupitem g 
             GROUP BY g.item_id) g ON g.item_id = a.id
        LEFT JOIN 
            (SELECT d.item_id, MAX(d.date) as last_climbed 
             FROM items_summitlog d 
             WHERE d.status = 1 AND d.attempt = false AND d.date_entered = True 
             GROUP BY d.item_id) d ON d.item_id = a.id
        WHERE 
            a.name != 'Activity' -- This is a special peak -  it’s where we put climbs that have a GPX track that didn’t include going to a peak summit. it’s a hacky solution. We want to filter this out
            AND a.active = true
    """
ALL_PEAKS_FILENAME = "all_peaks.json"
ALL_PEAKS_GZIPPED_FILENAME = "all_peaks.json.gz"
ALL_PEAKS_FILE_PATH = tempfile.gettempdir() + os.sep + ALL_PEAKS_FILENAME
ALL_PEAKS_GZIPPED_FILE_PATH = tempfile.gettempdir() + os.sep + ALL_PEAKS_GZIPPED_FILENAME


def trunc(num):
    if num:
        return round(float(num), 5)
    else:
        return 0


def get_peak_data_from_row(row):
    elevation = int(row[6]) if row[6] else 0
    prominence = int(row[8]) if row[8] else 0
    peak_data = {'id': str(row[0]), 'name': row[1], 'is_classic': row[2], 'lat': trunc(row[3]), 'lng': trunc(row[4]),
                 'summit_count': row[5], 'elevation': elevation, 'thumb_url': row[7], 'prominence': prominence,
                 'challenge_count': row[9], 'last_climbed': row[10], 'region_id': "", 'region_name': "", 'country_name': ""}

    if row[11]: # This contains all the regions and countries for the current peak, however, for all_peaks endpoint, we will only keep the first region/country
        region_id, region_name, country_name = row[11][0].split(":")
        peak_data['region_id'] = str(region_id)
        peak_data['region_name'] = region_name
        peak_data['country_name'] = country_name

    return peak_data


def load_and_cache_all_peaks():
    print("Starting all peaks caching process")
    with codecs.open(ALL_PEAKS_FILE_PATH, "w", encoding='utf-8') as f:
        f.write('{"data": {"peaks": [')

    first_iteration = True
    peak_count = 0
    with codecs.open(ALL_PEAKS_FILE_PATH, "a", encoding="utf-8") as f:
        with connection.cursor() as cursor:
            cursor.execute(ALL_PEAKS_QUERY)
            while True:
                if not first_iteration:
                    f.write(str(",").rstrip('\n'))
                rows = cursor.fetchmany(50000)
                if not rows:
                    break

                for row in rows:
                    peak_count += 1
                    peak_data = get_peak_data_from_row(row)
                    json.dump(peak_data, f, cls=DjangoJSONEncoder, ensure_ascii=False)
                    f.write(str(",").rstrip('\n'))

                del rows  # Clear some memory

        # Remove last comma
        f.seek(f.tell() - 1, os.SEEK_SET)
        f.truncate()

        f.write(']},"status": "success","message": ""}')
    compress_all_peaks_file()
    print("All peaks loaded, persisted and compressed to", ALL_PEAKS_GZIPPED_FILE_PATH)
    cache_compressed_file(peak_count)
    print("All peaks have been cached")


def compress_all_peaks_file():
    with open(ALL_PEAKS_FILE_PATH, 'rb') as f_in, gzip.open(ALL_PEAKS_GZIPPED_FILE_PATH, 'wb') as f_out:
        f_out.writelines(f_in)


def cache_compressed_file(peak_count):
    with open(ALL_PEAKS_GZIPPED_FILE_PATH, 'rb') as file:
        data = file.read()
        cache_manager.set_all_peaks_compressed_json(data, peak_count)
