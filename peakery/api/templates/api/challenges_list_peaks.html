{% load json_filters %}
{"peaks": [
    {% for p in peaks %}
        {"id": "{{ p.peak.id }}", "name": {{ p.peak.name|jsonify }}, "slug": "{{ p.peak.slug }}", "peak_highlights": {{ p.peak.peak_highlights|jsonify }}, "summit_count": "{{ p.peak.summitlog_count }}", "elevation": "{{ p.peak.elevation }}", "prominence": "{{ p.peak.prominence }}", "thumbnail_url": "{{ p.peak.thumbnail_url }}", "region": [
        {% if p.region %}
            {% for r in p.region %}
                {"region_id": "{{ r.id }}", "region_name": "{{ r.name }}", "country_name": "{{ r.country.name }}"}{% if not forloop.last %},{% endif %}
            {% endfor %}
        {% endif %}
        ], "country": [
            {% if p.country %}
                {% for c in p.country %}
                    {"country_id": "{{ c.id }}", "country_name": "{{ c.name }}"}{% if not forloop.last %},{% endif %}
                {% endfor %}
            {% endif %}
        ]}{% if not forloop.last %},{% endif %}
    {% endfor %}
]}