{% load json_filters %}
{"challenges": [
    {% for c in challenges %}
        {"id": "{{ c.challenge.id }}", "name": "{{ c.challenge.name }}", "slug": "{{ c.challenge.slug }}", "lat": "{{ c.challenge.lat }}", "lng": "{{ c.challenge.lng }}", "show_on_geochart": "{{ c.challenge.show_on_geochart }}", "description": {{ c.challenge.description|jsonify }}, "peak_count": "{{ c.challenge.peak_count }}", "pursuers": "{{ c.pursuers_count }}", "finishers": "{{ c.finishers_count }}", "peaks": [
            {% if c.peaks %}
                {% for p in c.peaks %}
                    {"peak_id": "{{ p.id }}", "peak_name": "{{ p.name }}", "peak_slug": "{{ p.slug }}", "thumbnail_url": "{{ p.get_thumbnail_745 }}"}{% if not forloop.last %},{% endif %}
                {% endfor %}
            {% endif %}
        ]}{% if not forloop.last %},{% endif %}
    {% endfor %}
]}