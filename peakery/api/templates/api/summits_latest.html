{% load json_filters %}
{% load avatar_tags %}
{"summits_count": "{{ summits_count }}", "summits": [
    {% for s in summits %}
        {"id": "{{ s.summit.id }}", "summitlog_date": "{{ s.summit.summitlog_date|date:"Y-m-j" }}", "log": {{ s.summit.log|jsonify }}, "num_summits_in_group": {{ s.summit.num_summits_in_group }}, "gpx_file": "{{ s.summit.gpx_file }}", "total_distance": "{{ s.summit.total_distance }}", "elevation_gain": "{{ s.summit.elevation_gain }}", "total_trip_time": "{{ s.summit.total_trip_time }}", "username": "{{ s.summit.username }}", "attempt": "{{ s.summit.attempt }}", "avatar_url": "{{ s.summit.avatar_url|urlencode }}", "peak_id": "{{ s.summit.peak_id }}", "peak_name": "{{ s.summit.peak_name }}", "peak_slug": "{{ s.summit.peak_slug }}", "peak_lat": "{{ s.summit.lat }}", "peak_long": "{{ s.summit.long }}", "peak_fullsize_photo_url": "{{ s.summit.peak_fullsize_photo_url }}", "peak_thumbnail_url": "{{ s.summit.peak_thumbnail_url }}", "has_peak_thumbnail": "{{ s.summit.has_peak_thumbnail }}", "elevation": "{{ s.summit.elevation }}", "prominence": "{{ s.summit.prominence }}", "liked": "{{ s.summit.liked }}", "photos": [
            {% if s.photos %}
                {% for p in s.photos %}
                    {"photo_id": "{{ p.id }}", "username": "{{ p.user.username }}", "caption": {% if p.caption %}{{ p.caption|jsonify }}{% else %}""{% endif %}, "thumbnail_url": "{{ p.thumb_480x360 }}", "fullsize_url": "{{ p.thumb_1920x1440 }}"}{% if not forloop.last %},{% endif %}
                {% endfor %}
            {% endif %}
        ], "addl_summits": [
            {% if s.addl_summits %}
                {% for a in s.addl_summits %}
                    {"peak_name": "{{ a.name }}"}{% if not forloop.last %},{% endif %}
                {% endfor %}
            {% endif %}
        ], "region": [
            {% if s.region %}
                {% for r in s.region %}
                    {"region_id": "{{ r.id }}", "region_name": "{{ r.name }}", "country_name": "{{ r.country.name }}"}{% if not forloop.last %},{% endif %}
                {% endfor %}
            {% endif %}
        ], "country": [
            {% if s.country %}
                {% for c in s.country %}
                    {"country_id": "{{ c.id }}", "country_name": "{{ c.name }}"}{% if not forloop.last %},{% endif %}
                {% endfor %}
            {% endif %}
        ], "favorites": [
            {% if s.favorites %}
                {% for f in s.favorites %}
                    {"user": "{{ f.user.username }}"}{% if not forloop.last %},{% endif %}
                {% endfor %}
            {% endif %}
        ], "comments": [
            {% if s.comments %}
                {% for c in s.comments %}
                    {"user": "{{ c.user.username }}"}{% if not forloop.last %},{% endif %}
                {% endfor %}
            {% endif %}
        ]}{% if not forloop.last %},{% endif %}
    {% endfor %}
]}