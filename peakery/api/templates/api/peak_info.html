{% load json_filters %}
{% for p in peaks %}
    {"id": "{{ p.peak.id }}", "name": {{ p.peak.name|jsonify }}, "slug": "{{ p.peak.slug }}", "peak_range": "{{ p.peak.range }}", "summit_count": "{{ p.peak.summitlog_count }}", "elevation": "{{ p.peak.elevation }}", "elevation_edited": "{{ p.peak.elevation_edited }}", "location_edited": "{{ p.peak.location_edited }}", "prominence": "{{ p.peak.prominence }}", "is_usa": "{{ p.is_usa }}", "thumbnail_url": "{{ p.peak.thumbnail_url }}", "alternate_names": [
    {% if p.alternate_names %}
        {% for n in p.alternate_names %}
            {"alternate_name": "{{ n.name }}"}{% if not forloop.last %},{% endif %}
        {% endfor %}
    {% endif %}
    ], "region": [
    {% if p.region %}
        {% for r in p.region %}
            {"region_id": "{{ r.id }}", "region_name": "{{ r.name }}", "country_name": "{{ r.country.name }}"}{% if not forloop.last %},{% endif %}
        {% endfor %}
    {% endif %}
    ], "country": [
        {% if p.country %}
            {% for c in p.country %}
                {"country_id": "{{ c.id }}", "country_code": "{{ c.code }}", "country_name": "{{ c.name }}"}{% if not forloop.last %},{% endif %}
            {% endfor %}
        {% endif %}
    ]}{% if not forloop.last %},{% endif %}
{% endfor %}