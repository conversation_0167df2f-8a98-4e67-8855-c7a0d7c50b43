{% load json_filters %}
{
    "status": "{{ response_status }}",
    "message": "{{ response_message }}",
    "data": {
        "parameters": [{
            "bounds": "{{ bounds }}", "elev_min": "{{ elev_min }}", "elev_max": "{{ elev_max }}", "prom_min": "{{ prom_min }}", "prom_max": "{{ prom_max }}", "last_climbed": "{{ last_climbed }}", "summits_min": "{{ summits_min }}", "summits_max": "{{ summits_max }}", "difficulty_min": "{{ difficulty_min }}", "difficulty_max": "{{ difficulty_max }}", "route_distance_min": "{{ route_distance_min }}", "route_distance_max": "{{ route_distance_max }}", "vertical_gain_min": "{{ vertical_gain_min }}", "vertical_gain_max": "{{ vertical_gain_max }}"
        }],
        "peaks": [
            {% for p in peaks %}
                {"id": "{{ p.peak.id }}", "name": {{ p.peak.name|jsonify }}, "slug": "{{ p.peak.slug }}", "is_classic": "{{ p.peak.is_classic }}", "lat": "{{ p.peak.lat }}", "lng": "{{ p.peak.lng }}", "peak_highlights": "", "summit_count": "{{ p.peak.summitlog_count }}", "elevation": "{{ p.peak.elevation }}", "prominence": "{{ p.peak.prominence }}", "miles_away": "{{ p.peak.miles_away }}", "your_summits": "{{ p.peak.your_summits }}", "your_attempts": "{{ p.peak.your_attempts }}", "challenge_count": "{{ p.peak.challenge_count }}", "thumbnail_url": "{{ MEDIA_URL }}{{ p.peak.thumbnail_url }}", "region": [
                {% if p.region %}
                    {% for r in p.region %}
                        {"region_id": "{{ r.id }}", "region_name": "{{ r.name }}", "region_slug": "{{ r.absolute_url }}", "country_name": "{{ r.country_name }}"}{% if not forloop.last %},{% endif %}
                    {% endfor %}
                {% endif %}
                ], "country": [
                {% if p.country %}
                    {% for c in p.country %}
                        {"country_id": "{{ c.id }}", "country_name": "{{ c.name }}", "country_slug": "{{ c.absolute_url }}"}{% if not forloop.last %},{% endif %}
                    {% endfor %}
                {% endif %}
                ], "group": []}
            {% if not forloop.last %},{% endif %}
            {% endfor %}
        ]
    }
}
