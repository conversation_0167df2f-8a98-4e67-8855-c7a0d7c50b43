{% load json_filters %}
{"peaks": [
    {% for p in peaks %}
        {"peak_id": "{{ p.item_id }}", "name": "{{ p.name }}", "slug": "{{ p.slug }}", "thumbnail_url": "{{ p.thumbnail_url }}", "fullsize_url": "{{ p.fullsize_url }}", "elevation": "{{ p.elevation }}", "latitude": "{{ p.lat }}", "winter_date": "{{ p.winter_date|date:"M j, Y" }}", "spring_date": "{{ p.spring_date|date:"M j, Y" }}", "summer_date": "{{ p.summer_date|date:"M j, Y" }}", "fall_date": "{{ p.fall_date|date:"M j, Y" }}", "winter_summit_id": "{{ p.winter_summit_id }}", "spring_summit_id": "{{ p.spring_summit_id }}", "summer_summit_id": "{{ p.summer_summit_id }}", "fall_summit_id": "{{ p.fall_summit_id }}"}{% if not forloop.last %},{% endif %}
    {% endfor %}
]}