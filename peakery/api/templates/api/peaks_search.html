{% load json_filters %}
{"parameters": [
    {"status": "{{ status }}", "keyword": "{{ keyword }}", "near": "", "state_id": "{{ state_id }}", "elev_min": "{{ elev_min }}", "elev_max": "{{ elev_max }}", "prom_min": "{{ prom_min }}", "prom_max": "{{ prom_max }}", "summits_min": "{{ summits_min }}", "summits_max": "{{ summits_max }}", "difficulty_min": "{{ difficulty_min }}", "difficulty_max": "{{ difficulty_max }}", "length_min": "{{ length_min }}", "length_max": "{{ length_max }}", "vertical_min": "{{ vertical_min }}", "vertical_max": "{{ vertical_max }}", "last_climbed_min": "{{ last_climbed_min }}", "last_climbed_max": "{{ last_climbed_max }}", "classics": "{{ classics }}", "in_challenge": "{{ in_challenge }}", "you_climbed": "{{ you_climbed }}", "has_gps": "{{ has_gps }}", "lat": "{{ lat }}", "lng": "{{ lng }}", "sort": "{{ sort }}", "sort_dir": "{{ sort_dir }}", "page": "{{ page }}", "peak_count": "{{ peak_count }}"}
], "peaks": [
    {% for p in peaks %}
        {"id": "{{ p.peak.id }}", "name": {{ p.peak.name|jsonify }}, "slug": "{{ p.peak.slug }}", "is_classic": "{{ p.peak.is_classic }}", "peak_highlights": "", "summit_count": "{{ p.peak.summitlog_count }}", "elevation": "{{ p.peak.elevation }}", "prominence": "{{ p.peak.prominence }}", "range": "{{ p.peak.range }}", "last_summit_date": "{{ p.peak.last_summit_date|date:"M j, Y" }}", "last_summit_id": "{{ p.peak.last_summit_id }}", "miles_away": "{{ p.peak.miles_away }}", "your_summits": "{{ p.peak.your_summits }}", "thumbnail_url": "{{ p.thumbnail_url }}", "region": [
        {% if p.region %}
            {% for r in p.region %}
                {"region_id": "{{ r.id }}", "region_name": "{{ r.name }}", "region_slug": "{{ r.absolute_url }}", "country_name": "{{ r.country_name }}"}{% if not forloop.last %},{% endif %}
            {% endfor %}
        {% endif %}
        ], "country": [
            {% if p.country %}
                {% for c in p.country %}
                    {"country_id": "{{ c.id }}", "country_name": "{{ c.name }}", "country_slug": "{{ c.absolute_url }}"}{% if not forloop.last %},{% endif %}
                {% endfor %}
            {% endif %}
        ], "group": [
            {% if p.group %}
                {% for g in p.group %}
                    {"group_id": "{{ g.id }}", "group_name": "{{ g.name }}", "group_slug": "{{ g.absolute_url }}"}{% if not forloop.last %},{% endif %}
                {% endfor %}
            {% endif %}
        ]}{% if not forloop.last %},{% endif %}
    {% endfor %}
]}