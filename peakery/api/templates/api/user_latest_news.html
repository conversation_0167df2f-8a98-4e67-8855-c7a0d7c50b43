{% load json_filters %}
{% load avatar_tags item_tags %}
{"news": [
    {% for n in news %}
        {"notice_id": "{{ n.notice_id }}", "notice_type": "{{ n.notice_type }}", "comment": {% if 'comment' in n %} {{ n.comment|urlize|url_target_blank|jsonify }} {% else %}""{% endif %}, "sender_id": "{{ n.sender_id }}", "sender": "{{ n.sender }}", "sender_avatar_url": {% if 'sender_avatar_url' in n %}"{{ n.sender_avatar_url|urlencode }}"{% else %}""{% endif %}, "sender_avatar_url_big": "{{ n.sender_avatar_url_big|urlencode }}", "following": {{ n.following }}, "receiver_id": "{{ n.receiver_id }}", "receiver": "{{ n.receiver }}", "receiver_avatar_url": "{{ n.receiver_avatar_url|urlencode }}", "receiver_avatar_url_big": "{{ n.receiver_avatar_url_big|urlencode }}", "summitlog_id": "{{ n.summitlog_id }}", "summitlog_date": {% if 'summitlog_date' in n %}"{{ n.summitlog_date|date:"Y-m-j" }}"{% else %}""{% endif %}, "username": {% if 'username' in n %}"{{ n.username }}"{% else %}""{% endif %}, "avatar_url": {% if 'avatar_url' in n %}"{{ n.avatar_url|urlencode }}"{% else %}""{% endif %}, "peak_id": {% if 'peak_id' in n %}"{{ n.peak_id }}"{% else %}""{% endif %}, "peak_name": {% if 'peak_name' in n %}"{{ n.peak_name }}"{% else %}""{% endif %}, "peak_slug": {% if 'peak_slug' in n %}"{{ n.peak_slug }}"{% else %}""{% endif %}, "peak_thumbnail_url": {% if 'peak_thumbnail_url' in n %} "{{ n.peak_thumbnail_url }}"{% else %}""{% endif %}} {% if not forloop.last %},{% endif %}
    {% endfor %}
]}