{% load json_filters %}
{
    "status": "{{ response_status }}",
    "message": "{{ response_message }}",
    "data": {
        "parameters": [{
            "keyword": "{{ keyword }}"
        }],
        "suggestions": [
            {% for s in suggestions %}
            {"id": "{{ s.id }}", "item_type": "{{ s.item_type }}", "name": {{ s.name|jsonify }}, "url": "{{ s.url }}", "thumbnail_url": "{% if s.thumbnail_url != '' %}{{ MEDIA_URL }}{{ s.thumbnail_url }}{% endif %}", "lat": "{{ s.lat }}", "lng": "{{ s.lng }}", "zoom_level": "{{ s.zoom_level }}", "elevation": "{{ s.elevation }}", "summit_count": "{{ s.summit_count }}", "region": [
            {% if s.region %}
                {% for r in s.region %}
                    {"region_id": "{{ r.id }}", "region_name": "{{ r.name }}", "region_slug": "{{ r.absolute_url }}", "country_name": "{{ r.country_name }}"}{% if not forloop.last %},{% endif %}
                {% endfor %}
            {% endif %}
            ], "country": [
                {% if s.country %}
                    {% for c in s.country %}
                        {"country_id": "{{ c.id }}", "country_name": "{{ c.name }}", "country_slug": "{{ c.absolute_url }}"}{% if not forloop.last %},{% endif %}
                    {% endfor %}
                {% endif %}
            ]}{% if not forloop.last %},{% endif %}
            {% endfor %}
        ]
    }
}