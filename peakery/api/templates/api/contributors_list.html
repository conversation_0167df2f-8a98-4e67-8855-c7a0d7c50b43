{"contributors": [
    {% for c in contributors %}
        {"id": "{{ c.contributor.id }}", "username": "{{ c.contributor.username }}", "avatar_url": "{{ MEDIA_URL }}{{ c.contributor.avatar_url|urlencode }}", "contributions_count": "{{ c.contributor.contributions }}", "contributions": [
            {% if c.contributions_list %}
                {% for cl in c.contributions_list %}
                    {"contrib_type": "{{ cl.contribution.contrib_type }}", "peak_id": "{{ cl.peak.id }}", "peak_name": "{{ cl.peak.name }}", "peak_slug": "{{ cl.peak.slug_new_text }}", "region": [
                    {% if cl.region %}
                        {% for r in cl.region %}
                            {"region_id": "{{ r.id }}", "region_name": "{{ r.name }}", "region_slug": "{{ r.slug }}", "country_name": "{{ r.country.name }}", "country_slug": "{{ r.country.slug }}"}{% if not forloop.last %},{% endif %}
                        {% endfor %}
                    {% endif %}
                    ], "country": [
                    {% if cl.country %}
                        {% for ctry in cl.country %}
                            {"country_id": "{{ ctry.id }}", "country_name": "{{ ctry.name }}", "country_slug": "{{ ctry.slug }}"}{% if not forloop.last %},{% endif %}
                        {% endfor %}
                    {% endif %}
                    ]}{% if not forloop.last %},{% endif %}
                {% endfor %}
            {% endif %}
        ]}{% if not forloop.last %},{% endif %}
    {% endfor %}
]}