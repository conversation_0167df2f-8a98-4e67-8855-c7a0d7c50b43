{% load json_filters %}
{% load avatar_tags %}
{
    "status": "{{ response_status }}",
    "message": "{{ response_message }}",
    "data": {"ui_sections": [
        {% for s in summits %}
            {
                "section_type": "climb_log",
                "header": {
                    "section_type": "{{ section_type }}",
                    "title": "{{ s.summit.peak_name }}",
                    "subtitle": "summit_subtitle",
                    "user_name": "{{ s.summit.username }}",
                    "user_url": "{{ s.user_url }}",
                    "timestamp": "1 d ago",
                },
                "photo_urls": [],
                "gps_photo_url": "",
                "length": "20.3 mi",
                "vert_gain": "6,250 ft gain",
                "time": "14 hr 41 min",
                "description": "A nice day for a first time....",
                "liked_profile_photo_urls":[],
                "liked_profile_urls": []
                "likedByYou": "false",
                "likes": "0",
                "comments": []
            }{% if not forloop.last %},{% endif %}
        {% endfor %}
    ]}
}