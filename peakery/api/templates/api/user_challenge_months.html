{% load json_filters %}
{"peaks": [
    {% for p in peaks %}
        {"peak_id": "{{ p.item_id }}", "name": "{{ p.name }}", "slug": "{{ p.slug }}", "thumbnail_url": "{{ p.thumbnail_url }}", "fullsize_url": "{{ p.fullsize_url }}", "elevation": "{{ p.elevation }}", "latitude": "{{ p.lat }}", "jan_date": "{{ p.jan_date|date:"n/j/y" }}", "feb_date": "{{ p.feb_date|date:"n/j/y" }}", "mar_date": "{{ p.mar_date|date:"n/j/y" }}", "apr_date": "{{ p.apr_date|date:"n/j/y" }}", "may_date": "{{ p.may_date|date:"n/j/y" }}", "jun_date": "{{ p.jun_date|date:"n/j/y" }}", "jul_date": "{{ p.jul_date|date:"n/j/y" }}", "aug_date": "{{ p.aug_date|date:"n/j/y" }}", "sep_date": "{{ p.sep_date|date:"n/j/y" }}", "oct_date": "{{ p.oct_date|date:"n/j/y" }}", "nov_date": "{{ p.nov_date|date:"n/j/y" }}", "dec_date": "{{ p.dec_date|date:"n/j/y" }}", "jan_summit_id": "{{ p.jan_summit_id }}", "feb_summit_id": "{{ p.feb_summit_id }}", "mar_summit_id": "{{ p.mar_summit_id }}", "apr_summit_id": "{{ p.apr_summit_id }}", "may_summit_id": "{{ p.may_summit_id }}", "jun_summit_id": "{{ p.jun_summit_id }}", "jul_summit_id": "{{ p.jul_summit_id }}", "aug_summit_id": "{{ p.aug_summit_id }}", "sep_summit_id": "{{ p.sep_summit_id }}", "oct_summit_id": "{{ p.oct_summit_id }}", "nov_summit_id": "{{ p.nov_summit_id }}", "dec_summit_id": "{{ p.dec_summit_id }}"}{% if not forloop.last %},{% endif %}
    {% endfor %}
]}