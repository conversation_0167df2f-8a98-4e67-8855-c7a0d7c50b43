{% load json_filters %}
{
    "status": "{{ response_status }}",
    "message": "{{ response_message }}",
    "data": {
        "peaks": [
            {% for s in peaks %}
            {"id": "{{ s.id }}", "name": {{ s.name|jsonify }}, "slug": "{{ s.url }}", "is_classic": "{{ s.is_classic }}", "thumbnail_url": "{% if s.thumbnail_url != '' %}{{ MEDIA_URL }}{{ s.thumbnail_url }}{% endif %}", "lat": "{{ s.lat }}", "lng": "{{ s.lng }}", "elevation": "{{ s.elevation }}", "prominence": "{{ s.prominence }}", "summit_count": "{{ s.summit_count }}", "your_summits": "{{ s.your_summits }}", "your_attempts": "{{ s.your_attempts }}", "challenge_count": "{{ s.challenge_count }}", "region": [
            {% if s.region %}
                {% for r in s.region %}
                    {"region_id": "{{ r.id }}", "region_name": "{{ r.name }}", "region_slug": "{{ r.get_absolute_url }}", "country_name": "{{ r.country.name }}"}{% if not forloop.last %},{% endif %}
                {% endfor %}
            {% endif %}
            ], "country": [
                {% if s.country %}
                    {% for c in s.country %}
                        {"country_id": "{{ c.id }}", "country_name": "{{ c.name }}", "country_slug": "{{ c.get_absolute_url }}"}{% if not forloop.last %},{% endif %}
                    {% endfor %}
                {% endif %}
            ]}{% if not forloop.last %},{% endif %}
            {% endfor %}
        ]
    }
}