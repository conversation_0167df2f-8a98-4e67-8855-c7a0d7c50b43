from django.urls import path, re_path
from django.views.decorators.csrf import csrf_exempt

from peakery.api.views import api_home, mobile_peaks_map, mobile_peaks_search, mobile_list_suggestions, \
    mobile_log_climb
from peakery.api.views import mobile_gpx_upload, mobile_photo_upload, create_presigned_s3_put_url
from peakery.api.views import mobile_gpx_parse, badges_list, countries_list, world_list_countries, contributors_list
from peakery.api.views import regions_list, peaks_list, peaks_search, peaks_list_suggestions
from peakery.api.views import peaks_map
from peakery.api.peaks_map_views_web.index import peaks_map_get_peaks, get_peaks_by_name, peaks_map_get_photos, peaks_map_get_challenges
from peakery.api.views import climbed_peaks_list_suggestions, climbed_peaks_list_init, climbed_peaks_list_autofill
from peakery.api.views import peaks_summits_list, peak_photos_list, peak_routes_list, peak_info, summit_photos_list
from peakery.api.views import summit_update_gpx_stats, summits_list, summits_latest, summits_latest_likes_comments, members_list, members_list_suggestions
from peakery.api.views import companions_list_suggestions, user_challenges_list, user_challenge_peaks, user_challenge_seasons
from peakery.api.views import user_challenge_months, user_map, user_summits_list, user_news_list, user_summits_news_list
from peakery.api.views import user_latest_news_list, user_reset_news, user_photos_list, user_awards_list, user_followers_list
from peakery.api.views import user_contributor_stats, user_edit_stats, user_navdata, challenges_list, region_challenges_list
from peakery.api.views import challenges_map, challenges_list_peaks, challenges_list_members, challenges_list_summits
from peakery.api.views import countries_list_suggestions, countries_list_highlights, regions_list_suggestions, cities_list_suggestions
from peakery.api.views import route_remove_gpx, route_update_gpx_stats, s3signature, s3success
from peakery.api.views import mobile_summits_edit_v2_3, homepage_list_suggestions

from peakery.api.views import mobile_peaks_map_v2_2, mobile_peaks_search_v2_2
from peakery.api.views import mobile_log_climb_v2_2, mobile_gpx_upload_v2_2, mobile_photo_upload_v2_2
from peakery.api.views import create_presigned_s3_url_v2_2, create_presigned_s3_put_url_v2_2, mobile_gpx_parse_v2_2
from peakery.api.views import mobile_check_username_v2_2, mobile_signup_v2_2, mobile_login_v2_2
from peakery.api.views import mobile_update_user_v2_3, mobile_forgot_password_v2_2, mobile_summits_user_v2_2, mobile_summits_delete_v2_2
from peakery.api.views import mobile_signup_v2_3, mobile_login_v2_3, mobile_summits_latest_v2_2, mobile_summits_latest_v2_3, mobile_get_user_v2_2, mobile_get_user_v2_3
from peakery.api.views import mobile_reset_password_v2_3, mobile_summits_user_v2_3, mobile_summits_peak_v2_3
from peakery.api.views import mobile_summits_save_v2_2, mobile_summits_unsave_v2_2, mobile_gpxtracks_save_v2_3, mobile_gpxtracks_unsave_v2_3, mobile_gpxtracks_saved_v2_3, mobile_gpxtracks_upload_v2_3
from peakery.api.views import mobile_delete_user_v2_3, mobile_summits_like_v2_2, mobile_summits_unlike_v2_2, mobile_summits_comment_v2_2, mobile_summits_comment_edit_v2_2, mobile_summits_comment_delete_v2_2
from peakery.api.views import mobile_summits_user_v2_4, mobile_summits_peak_v2_4, mobile_summits_latest_v2_4
from peakery.api.views import mobile_summit_v2_4, mobile_summit_comments_v2_4
from peakery.api.views import top_three_climbing_months_for_country_code, top_three_ranges_for_country_code, most_summited_peak_for_country_code, featured_summit_logs_for_country_code
from peakery.api.views import all_peaks

urlpatterns = [
    path('', api_home, name='api_home'),

    # Mobile API endpoints

    # Peaks map
    path('mobile/peaks/map/', csrf_exempt(mobile_peaks_map), name='mobile_peaks_map'),
    re_path(r'^mobile/v2.2/peaks/map/$', csrf_exempt(mobile_peaks_map_v2_2), name='mobile_peaks_map_v2_2'),

    # Peaks search
    path('mobile/peaks/search/', csrf_exempt(mobile_peaks_search), name='mobile_peaks_search'),
    re_path(r'^mobile/v2.2/peaks/search/$', csrf_exempt(mobile_peaks_search_v2_2), name='mobile_peaks_search_v2_2'),

    # Offline Peaks
    re_path(r'^mobile/v2.3/peaks/all/$', csrf_exempt(all_peaks), name='all_peaks'),

    # Search suggestions
    path('mobile/search/suggestions/', csrf_exempt(mobile_list_suggestions), name='mobile_list_suggestions'),
    re_path(r'^mobile/v2.2/search/suggestions/$', csrf_exempt(mobile_list_suggestions), name='mobile_list_suggestions'),

    # Upload climb
    path('mobile/upload/climb/', csrf_exempt(mobile_log_climb), name='mobile_log_climb'),
    re_path(r'^mobile/v2.2/upload/climb/$', csrf_exempt(mobile_log_climb_v2_2), name='mobile_log_climb_v2_2'),

    # Upload track
    path('mobile/upload/track/', csrf_exempt(mobile_gpx_upload), name='mobile_gpx_upload'),
    re_path(r'^mobile/v2.2/upload/track/$', csrf_exempt(mobile_gpx_upload_v2_2), name='mobile_gpx_upload_v2_2'),

    # Upload photo
    path('mobile/upload/photo/', csrf_exempt(mobile_photo_upload), name='mobile_photo_upload'),
    re_path(r'^mobile/v2.2/upload/photo/$', csrf_exempt(mobile_photo_upload_v2_2), name='mobile_photo_upload_v2_2'),

    # Get presigned url
    re_path(r'^mobile/v2.2/upload/get_presigned_url/$', csrf_exempt(create_presigned_s3_url_v2_2), name='create_presigned_s3_url_v2_2'),

    # Get presigned put url
    path('mobile/upload/get_presigned_put_url/', csrf_exempt(create_presigned_s3_put_url), name='create_presigned_s3_put_url'),
    re_path(r'^mobile/v2.2/upload/get_presigned_put_url/$', csrf_exempt(create_presigned_s3_put_url_v2_2), name='create_presigned_s3_put_url_v2_2'),

    # Parse gpx
    path('mobile/peaks/parse_gpx/', csrf_exempt(mobile_gpx_parse), name='mobile_gpx_parse'),
    re_path(r'^mobile/v2.2/peaks/parse_gpx/$', csrf_exempt(mobile_gpx_parse_v2_2), name='mobile_gpx_parse_v2_2'),

    # Users/auth flow v2.2
    re_path(r'^mobile/v2.2/users/check_username/$', csrf_exempt(mobile_check_username_v2_2), name='mobile_check_username_v2_2'),
    re_path(r'^mobile/v2.2/users/signup/$', csrf_exempt(mobile_signup_v2_2), name='mobile_signup_v2_2'),
    re_path(r'^mobile/v2.2/users/login/$', csrf_exempt(mobile_login_v2_2), name='mobile_login_v2_2'),
    re_path(r'^mobile/v2.2/users/forgot_password/$', csrf_exempt(mobile_forgot_password_v2_2), name='mobile_forgot_password_v2_2'),
    re_path(r'^mobile/v2.2/users/get_user/$', csrf_exempt(mobile_get_user_v2_2), name='mobile_get_user_v2_2'),

    # Users/auth flow v2.3
    re_path(r'^mobile/v2.3/users/signup/$', csrf_exempt(mobile_signup_v2_3), name='mobile_signup_v2_3'),
    re_path(r'^mobile/v2.3/users/login/$', csrf_exempt(mobile_login_v2_3), name='mobile_login_v2_3'),
    re_path(r'^mobile/v2.3/users/reset_password/$', csrf_exempt(mobile_reset_password_v2_3), name='mobile_reset_password_v2_3'),
    re_path(r'^mobile/v2.3/users/delete/$', csrf_exempt(mobile_delete_user_v2_3), name='mobile_delete_user_v2_3'),
    re_path(r'^mobile/v2.3/users/update_user/$', csrf_exempt(mobile_update_user_v2_3), name='mobile_update_user_v2_3'),
    re_path(r'^mobile/v2.3/users/get_user/$', csrf_exempt(mobile_get_user_v2_3), name='mobile_update_user_v2_3'),

    # Summits latest and user and peak
    re_path(r'^mobile/v2.2/summits/latest/$', csrf_exempt(mobile_summits_latest_v2_2), name='mobile_summits_latest_v2_2'),
    re_path(r'^mobile/v2.3/summits/latest/$', csrf_exempt(mobile_summits_latest_v2_3), name='mobile_summits_latest_v2_3'),
    re_path(r'^mobile/v2.2/summits/user/$', csrf_exempt(mobile_summits_user_v2_2), name='mobile_summits_user_v2_2'),
    re_path(r'^mobile/v2.3/summits/user/$', csrf_exempt(mobile_summits_user_v2_3), name='mobile_summits_user_v2_3'),
    re_path(r'^mobile/v2.3/summits/peak/$', csrf_exempt(mobile_summits_peak_v2_3), name='mobile_summits_peak_v2_3'),
    re_path(r'^mobile/v2.3/gpxtracks/saved/$', csrf_exempt(mobile_gpxtracks_saved_v2_3), name='mobile_gpxtracks_saved_v2_3'),
    re_path(r'^mobile/v2.4/summits/user/$', csrf_exempt(mobile_summits_user_v2_4), name='mobile_summits_user_v2_4'),
    re_path(r'^mobile/v2.4/summits/peak/$', csrf_exempt(mobile_summits_peak_v2_4), name='mobile_summits_peak_v2_4'),
    re_path(r'^mobile/v2.4/summits/latest/$', csrf_exempt(mobile_summits_latest_v2_4), name='mobile_summits_latest_v2_4'),
    re_path(r'^mobile/v2.4/summit/$', csrf_exempt(mobile_summit_v2_4), name='mobile_summit_v2_4'),
    re_path(r'^mobile/v2.4/summit/comments/$', csrf_exempt(mobile_summit_comments_v2_4), name='mobile_summit_comments_v2_4'),

    # like, unlike, save, unsave, comment, delete and edit
    re_path(r'^mobile/v2.2/summits/delete/$', csrf_exempt(mobile_summits_delete_v2_2), name='mobile_summits_delete_v2_2'),
    re_path(r'^mobile/v2.3/summits/edit/$', csrf_exempt(mobile_summits_edit_v2_3), name='mobile_summits_edit_v2_3'),
    re_path(r'^mobile/v2.2/summits/save/$', csrf_exempt(mobile_summits_save_v2_2), name='mobile_summits_save_v2_2'),
    re_path(r'^mobile/v2.2/summits/unsave/$', csrf_exempt(mobile_summits_unsave_v2_2), name='mobile_summits_unsave_v2_2'),
    re_path(r'^mobile/v2.2/summits/like/$', csrf_exempt(mobile_summits_like_v2_2), name='mobile_summits_like_v2_2'),
    re_path(r'^mobile/v2.2/summits/unlike/$', csrf_exempt(mobile_summits_unlike_v2_2), name='mobile_summits_unlike_v2_2'),
    re_path(r'^mobile/v2.2/summits/comment/$', csrf_exempt(mobile_summits_comment_v2_2), name='mobile_summits_comment_v2_2'),
    re_path(r'^mobile/v2.2/summits/comment/edit/$', csrf_exempt(mobile_summits_comment_edit_v2_2), name='mobile_summits_comment_edit_v2_2'),
    re_path(r'^mobile/v2.2/summits/comment/delete/$', csrf_exempt(mobile_summits_comment_delete_v2_2), name='mobile_summits_comment_delete_v2_2'),

    # Upload gpx track to save it, not create a summit log
    re_path(r'^mobile/v2.3/gpxtracks/upload/$', csrf_exempt(mobile_gpxtracks_upload_v2_3), name='mobile_gpxtracks_upload_v2_3'),
    re_path(r'^mobile/v2.3/gpxtracks/save/$', csrf_exempt(mobile_gpxtracks_save_v2_3), name='mobile_gpxtracks_save_v2_3'),
    re_path(r'^mobile/v2.3/gpxtracks/unsave/$', csrf_exempt(mobile_gpxtracks_unsave_v2_3), name='mobile_gpxtracks_unsave_v2_3'),

    path('badges/list/', badges_list, name='badges_list'),
    path('countries/list/', countries_list, name='countries_list'),
    path('world/list/', world_list_countries, name='world_list_countries'),
    path('contributors/list/', contributors_list, name='contributors_list'),
    path('regions/list/', regions_list, name='regions_list'),
    path('peaks/list/', peaks_list, name='peaks_list'),
    path('peaks/search/', peaks_search, name='peaks_search'),
    path('peaks/map/', peaks_map, name='peaks_map'),
    path('peaks/peak_by_name/', get_peaks_by_name, name='get_peaks_by_name'),
    path('peaks/map/peaks/', peaks_map_get_peaks, name='peaks_map_get_peaks'),
    path('peaks/map/photos/', peaks_map_get_photos, name='peaks_map_get_photos'),
    path('peaks/map/challenges/', peaks_map_get_challenges, name='peaks_map_get_challenges'),
    path('peaks/suggestions/', peaks_list_suggestions, name='peaks_list_suggestions'),
    path('peaks/climb_suggestions/', climbed_peaks_list_suggestions, name='climbed_peaks_list_suggestions'),
    path('peaks/climb_init/', climbed_peaks_list_init, name='climbed_peaks_list_init'),
    path('peaks/climb_autofill/', climbed_peaks_list_autofill, name='climbed_peaks_list_autofill'),
    path('peaks/summits/', peaks_summits_list, name='peaks_summits_list'),
    path('peak/photos/', peak_photos_list, name='peak_photos_list'),
    path('peak/routes/', peak_routes_list, name='peak_routes_list'),
    path('peak/info/', peak_info, name='peak_info'),
    path('summit/photos/', summit_photos_list, name='summit_photos_list'),
    path('summit/updategpxstats/', summit_update_gpx_stats, name='summit_update_gpx_stats'),
    path('summits/list/', summits_list, name='summits_list'),
    path('summits/latest/', summits_latest, name='summits_latest'),
    path('summits/latest_likes_comments/', summits_latest_likes_comments, name='summits_latest_likes_comments'),
    path('members/list/', members_list, name='members_list'),
    path('members/suggestions/', members_list_suggestions, name='members_list_suggestions'),
    path('companions/suggestions/', companions_list_suggestions, name='companions_list_suggestions'),
    path('homepage/suggestions/', homepage_list_suggestions, name='homepage_list_suggestions'),
    path('user/challenges/list/', user_challenges_list, name='user_challenges_list'),
    path('user/challenge/peaks/', user_challenge_peaks, name='user_challenge_peaks'),
    path('user/challenge/seasons/', user_challenge_seasons, name='user_challenge_seasons'),
    path('user/challenge/months/', user_challenge_months, name='user_challenge_months'),
    path('user/map/', user_map, name='user_map'),
    path('user/summits/', user_summits_list, name='user_summits_list'),
    path('user/news/', user_news_list, name='user_news_list'),
    path('user/summits_news/', user_summits_news_list, name='user_summits_news_list'),
    path('user/latest_news/', user_latest_news_list, name='user_latest_news_list'),
    path('user/reset_news/', user_reset_news, name='user_reset_news'),
    path('user/photos/', user_photos_list, name='user_photos_list'),
    path('user/awards/', user_awards_list, name='user_awards_list'),
    path('user/followers/', user_followers_list, name='user_followers_list'),
    path('user/contributor_stats/', user_contributor_stats, name='user_contributor_stats'),
    path('user/edit_stats/', user_edit_stats, name='user_edit_stats'),
    path('user/navdata/', user_navdata, name='user_navdata'),
    path('challenges/list/', challenges_list, name='challenges_list'),
    path('region_challenges/list/', region_challenges_list, name='region_challenges_list'),
    path('challenges/map/', challenges_map, name='challenges_map'),
    path('challenges/listpeaks/', challenges_list_peaks, name='challenges_list_peaks'),
    path('challenges/listmembers/', challenges_list_members, name='challenges_list_members'),
    path('challenges/listsummits/', challenges_list_summits, name='challenges_list_summits'),
    path('countries/suggestions/', countries_list_suggestions, name='countries_list_suggestions'),
    path('countries/highlights/', countries_list_highlights, name='countries_list_highlights'),
    path('regions/suggestions/', regions_list_suggestions, name='regions_list_suggestions'),
    path('cities/suggestions/', cities_list_suggestions, name='cities_list_suggestions'),
    path('route/remove_gpx/', route_remove_gpx, name='route_remove_gpx'),
    path('route/updategpxstats/', route_update_gpx_stats, name='route_update_gpx_stats'),
    path('s3signature/', csrf_exempt(s3signature), name='s3signature'),
    path('s3success/', s3success, name='s3success'),
    path('top_three_climbing_months/', csrf_exempt(top_three_climbing_months_for_country_code), name='top_three_climbing_months_for_country_code'),
    path('top_three_ranges/', csrf_exempt(top_three_ranges_for_country_code), name='top_three_ranges_for_country_code'),
    path('most_summited_peak/', csrf_exempt(most_summited_peak_for_country_code), name='most_summited_peak_for_country_code'),
    path('featured_summit_logs/', csrf_exempt(featured_summit_logs_for_country_code), name='featured_summit_logs_for_country_code'),
]