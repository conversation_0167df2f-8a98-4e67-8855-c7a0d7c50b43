from jose import jwt


# TODO Refactor all the other mobile endpoints to use this auth token logic
def process_auth_token(request):
    result = 'success'
    decoded_data = {}

    auth_token = request.GET.get('auth_token', None)

    if not auth_token:
        result = 'Missing auth token.'
    else:
        try:
            decoded_data = jwt.decode(auth_token, '7il1oZvu8BnzMtDrrGtXJPZd53VZ57fe', algorithms=['HS256'])
        except Exception as e:
            result = 'Invalid auth token.'
            print(result, e)

    return result, decoded_data
