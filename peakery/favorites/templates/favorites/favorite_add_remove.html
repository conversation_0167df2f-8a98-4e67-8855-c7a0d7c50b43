<nobr>
  <span class="favorites-add-remove">
    <a {% if is_favorite %}class="has-favorite"{% endif %} id="favorite-{{ content_type_id }}-{{ object_id }}" href="#">
      <span class="favorites-count"></span>
    </a>
    &nbsp;<span id="count-{{ content_type_id }}-{{ object_id }}">{{ count }}</span>
  </span>
</nobr>

<script type="text/javascript">
$(document).ready(function(){
    $("#favorite-{{ content_type_id }}-{{ object_id }}").click(function(){
        if($(this).hasClass("has-favorite")){
            $.ajax({
                url: "{% url "favorite_ajax_remove" %}",
                type: "POST",
                data: {
                    content_type_id: {{ content_type_id }},
                    object_id: {{ object_id }}
                },
                dataType: 'json',
                success: function(data){
                    $("#favorite-{{ content_type_id }}-{{ object_id }}").removeClass("has-favorite");
                    $('#count-{{ content_type_id }}-{{ object_id }}').text(data.count);
                }
            });
        }else{
            $.ajax({
                url: "{% url "favorite_ajax_add" %}",
                type: "POST",
                data: {
                    content_type_id: {{ content_type_id }},
                    object_id: {{ object_id }}
                },
                dataType: 'json',
                success: function(data){
                    $("#favorite-{{ content_type_id }}-{{ object_id }}").addClass("has-favorite");
                    $('#count-{{ content_type_id }}-{{ object_id }}').text(data.count);
                }
            });
        }
        return false;
    })
})
</script>
