from django.urls import re_path
from django.views.decorators.csrf import csrf_exempt

from peakery.favorites.views import *

urlpatterns = [
    re_path(r'^add/$', csrf_exempt(ajax_add_favorite), name="favorite_ajax_add"),
    re_path(r'^remove/$', csrf_exempt(ajax_remove_favorite), name="favorite_ajax_remove"),
    re_path(r'^delete/(?P<object_id>\d+)/$', csrf_exempt(drop_favorite), name="favorite_drop"),
]