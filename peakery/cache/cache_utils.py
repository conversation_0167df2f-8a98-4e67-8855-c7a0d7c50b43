from django.conf import settings
from django.core.cache import cache
from django.http import HttpRequest
from django.utils.cache import get_cache_key


def invalidate_cache(path=''):
    ''' this function uses Django's caching function get_cache_key(). Since 1.7,
        Django has used more variables from the request object (scheme, host,
        path, and query string) in order to create the MD5 hashed part of the
        cache_key. Additionally, Django will use your server's timezone and
        language as properties as well. If internationalization is important to
        your application, you will most likely need to adapt this function to
        handle that appropriately.
    '''

    # Bootstrap request:
    #   request.path should point to the view endpoint you want to invalidate
    #   request.META must include the correct SERVER_NAME and SERVER_PORT as django uses these in order
    #   to build a MD5 hashed value for the cache_key. Similarly, we need to artificially set the
    #   language code on the request to 'en-us' to match the initial creation of the cache_key.
    #   YMMV regarding the language code.
    request = HttpRequest()
    request.META = {'SERVER_NAME': settings.SERVER_NAME, 'SERVER_PORT': 8000}
    request.LANGUAGE_CODE = 'en-us'
    request.path = path

    try:
        cache_key = get_cache_key(request)
        if cache_key:
            if cache.has_key(cache_key):
                cache.delete(cache_key)
                return True, 'successfully invalidated'
            else:
                return False, 'cache_key does not exist in cache'
        else:
            raise ValueError('failed to create cache_key')
    except (ValueError, Exception) as e:
        return False, e


def expire_view_cache(path, key_prefix=None):
    """
    This function allows you to invalidate any item from the per-view cache.
    It probably won't work with things cached using the per-site cache
    middleware (because that takes account of the Vary: Cookie header).
    This assumes you're using the Sites framework.
    Arguments:
        * path: The URL of the view to invalidate, like `/blog/posts/1234/`.
        * key prefix: The same as that used for the cache_page()
          function/decorator (if any).
    """


    # Prepare metadata for our fake request.
    # I'm not sure how 'real' this data needs to be, but still:

    request_meta = {"SERVER_NAME": settings.SERVER_NAME}
    request_meta["SERVER_PORT"] = "8000"

    # Create a fake request object

    request = HttpRequest()
    request.method = "GET"
    request.META = request_meta
    request.path = path

    if settings.USE_I18N:
        request.LANGUAGE_CODE = settings.LANGUAGE_CODE

    # If this key is in the cache, delete it:

    try:
        cache_key = get_cache_key(request, key_prefix=key_prefix)
        if cache_key:
            if cache.get(cache_key):
                cache.delete(cache_key)
                return True, "Successfully invalidated"
            else:
                return False, "Cache_key does not exist in cache"
        else:
            raise ValueError("Failed to create cache_key")
    except (ValueError, Exception) as e:
        return False, e