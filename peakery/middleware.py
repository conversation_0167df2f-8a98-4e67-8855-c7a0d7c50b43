import traceback

from django.conf import settings
from django import http
from django.utils.http import quote
from django.urls import Resolver404, resolve
from django.core.exceptions import PermissionDenied

from peakery.activity_log.activity import Activity
from peakery.activity_log.activity_database_service import ActivityDatabaseService
from peakery.activity_log.activity_queue import ActivityQueue
from user_agents import parse as parse_user_agent


def is_mobile_app_access(request):
    return request.META.get('HTTP_X_REQUESTED_WITH', None) == 'com.peakery.android'


def get_request_source(request):
    ua_string = request.META.get('HTTP_USER_AGENT', '')
    ua = parse_user_agent(ua_string)
    if ua.is_mobile or ua.is_tablet:
        if 'android' in ua_string.lower():
            return 'Android'
        elif 'iphone' in ua_string.lower() or 'ipad' in ua_string.lower():
            return 'IOS'
    return 'Web'


class BlockedIpMiddleware(object):

    def __init__(self, get_response):
        self.get_response = get_response

    # One-time configuration and initialization.

    def __call__(self, request):
        # Code to be executed for each request before
        # the view (and later middleware) are called.

        response = self.get_response(request)

        ip = request.META.get('HTTP_X_FORWARDED_FOR', None)
        if ip in settings.BLOCKED_IPS:
            return http.HttpResponseForbidden('<h1>Forbidden</h1>')
        else:
            return response


class ActivityLogMiddleware(object):
    activity_database_service = None
    activity_queue = None

    # One-time configuration and initialization.
    def __init__(self, get_response):
        self.get_response = get_response
        self.activity_queue = ActivityQueue()
        self.activity_database_service = ActivityDatabaseService(self.activity_queue)
        self.activity_database_service.initialize_service()

    def __call__(self, request):
        # Code to be executed for each request before
        # the view (and later middleware) are called.

        response = self.get_response(request)

        try:
            ip = request.META.get('HTTP_X_FORWARDED_FOR')
            if ip:
                ip = ip.split(',')[0].strip()
            else:
                ip = request.META.get('REMOTE_ADDR')
            if ip:
                if request.user and request.user.id:
                    user_id = request.user.id
                    if user_id > 0:
                        request_source = get_request_source(request)
                        if request_source:
                            activity = Activity(user_id, ip, request_source)
                            self.activity_queue.put(activity)
        except Exception as e:
            print("Exception occurred in middleware")
            print(traceback.format_exc())
        return response


class UrlMiddleware(object):
    """
	Middleware for removing the WWW from a URL if the users sets settings.REMOVE_WWW.
	Based on Django CommonMiddleware.
	"""

    def __init__(self, get_response):
        self.get_response = get_response

    # One-time configuration and initialization.

    def __call__(self, request):
        # Code to be executed for each request before
        # the view (and later middleware) are called.

        response = self.get_response(request)

        # First check if we are restricting access by IP
        if settings.RESTRICT_IP == 'True':
            from ipware.ip import get_client_ip
            ip = get_client_ip(request)
            if ip is not None:
                if not ip in settings.ALLOWED_IPS:  # ip check
                    raise PermissionDenied
            else:
                raise PermissionDenied

        host = request.get_host()
        old_url = [host, request.path]
        new_url = old_url[:]

        if (settings.REMOVE_WWW and old_url[0] and old_url[0].startswith('www.')):
            new_url[0] = old_url[0][4:]

        if new_url != old_url:
            try:
                resolve(new_url[1])
            except Resolver404:
                pass
            else:
                if new_url[0]:
                    newurl = "%s://%s%s" % (
                        request.is_secure() and 'https' or 'http',
                        new_url[0], quote(new_url[1]))
                else:
                    newurl = quote(new_url[1])
                if request.GET:
                    newurl += '?' + request.GET.urlencode()
                return http.HttpResponsePermanentRedirect(newurl)
        return response


class AjaxMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        def is_ajax(self):
            return request.META.get('HTTP_X_REQUESTED_WITH') == 'XMLHttpRequest'

        request.is_ajax = is_ajax.__get__(request)
        response = self.get_response(request)
        return response
