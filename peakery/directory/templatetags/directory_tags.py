from peakery.items.models import Item, Country
from django import template
from peakery.items.utils import commify
from django.core.cache import cache
from django.conf import settings

register = template.Library()

@register.simple_tag
def get_continent_item_count(continent):

    if continent == 'ALL':
        key = 'all_peaks_count'
        total = cache.get(key)
        if not total:
            total = Item.objects.all().count()
            cache.set(key, total, settings.DIRECTORY_TOTAL_ITEMS_IN_CONTINENT)
        return commify(total)

    key = 'total_peaks_continent_%s' % continent
    total = cache.get(key)
    if not total:
        countries = Country.objects.filter(continent=continent)
        total = Item.objects.filter(country__in = countries).count()
        #total = 0
        #for c in countries:
        #    total = total + c.itemcountry_set.count()
        cache.set(key, total, settings.DIRECTORY_TOTAL_ITEMS_IN_CONTINENT)

    return commify(total)

@register.filter
def remove_near_word(value):
    return value.replace('near', '')