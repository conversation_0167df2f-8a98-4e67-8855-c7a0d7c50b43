{% extends "base.html" %}
{% load static %}
{% load humanize %}

{% block title %}{{ region.name }} mountains climbs{% endblock %}
{% block titlemeta_overwrite %}{{ region.name }} mountains climbs{% endblock %}
{% block description %}{{ meta_description }}{% endblock %}

{% block extrajs %}
    <script type="text/javascript">
    {% include "mapbox/polyline.js" %}
    </script>
    <script src="{% static 'js/GPXParser.js'%}"></script>
{% endblock %}

{% block directory_active %}active{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row hidden-xs">
        <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 65px; border-radius: 12px 12px 0 0;"><span style="font-size: 24px; font-weight: 600;">
            <div id="breadcrumbs">
                <ul>
                    <li class="hidden-sm hidden-xs"><a style="font-weight: 500;" href="/region/{{ region.country.slug }}-mountains/">{{ region.country.name }}</a> &raquo; </li>
                    <li><h1 class="regions-title">{{ region.name }}</h1></li>
                </ul>
                <div class="pull-right hidden-xs" style="margin-top: -5px;">
                    <a style="{{ subnav_info_style }}" class="region-header-sub-links" href="/{{ region.country.slug }}-mountains/{{ region.slug }}/">Info</a><a style="{{ subnav_map_style }}" class="region-header-sub-links" href="/{{ region.country.slug }}-mountains/{{ region.slug }}/map/">Map</a><a style="{{ subnav_peaks_style }}" class="region-header-sub-links" href="/{{ region.country.slug }}-mountains/{{ region.slug }}/peaks/">Peaks</a><a style="{{ subnav_summits_style }}" class="region-header-sub-links" href="/{{ region.country.slug }}-mountains/{{ region.slug }}/summits/">Climbs</a><a style="{{ subnav_challenges_style }}" class="region-header-sub-links" href="/{{ region.country.slug }}-mountains/{{ region.slug }}/challenges/">Challenges</a>
                </div>
            </div>
        </span></div>
    </div>
{% endblock %}

{% block content %}

<style>

    .summit-card-header {
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
    }

   @media screen and (max-width: 767px) and (min-width: 1px) {
       #content-body {
           margin-top: 0px;
       }
       .content-pane {
           margin-top: 100px;
       }
       #main {
           margin-top: 20px;
       }
       .regions-subnav-fixed {
            top: 120px !important;
        }
       .filter-bar {
           top: 120px;
       }
       .summit-card-peak-title {
            position: absolute;
            top: 0px;
            left: 16px;
            font-size: 13px !important;
            color: #333;
        }
        .summit-card-mobile-stats {
            position: absolute;
            top: 40px;
            left: 10px;
            margin-right: 10px;
            font-size: 12px !important;
            line-height: 20px;
        }
   }
    @media screen and (min-width: 768px) {
        .content-pane {
           margin-top: 50px;
       }
    }
    @media screen and (min-width: 768px) and (max-width: 1279px) {
        .regions-subnav-fixed {
            top: 141px !important;
        }
    }
    @media screen and (min-width: 1280px) {
        .regions-subnav-fixed {
            top: 141px !important;
        }
    }
    @media screen and (max-width: 1023px) and (min-width: 768px) {
        .filter-bar {
            top: 141px;
        }
    }
</style>

<div class="container">
    <!-- Mobile header -->
    <div class="row sub-header-row hidden-lg hidden-md hidden-sm" style="height: 50px; position: fixed; width: 100%; z-index: 2;">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999; display: flex; justify-content: space-between;">
            <a style="{{ subnav_info_style }} margin-left: 0px;" class="mobile-header-sub-links" href="/{{ region.country.slug }}-mountains/{{ region.slug }}/">Info</a><a style="{{ subnav_map_style }}" class="mobile-header-sub-links" href="/{{ region.country.slug }}-mountains/{{ region.slug }}/map/">Map</a><a style="{{ subnav_peaks_style }}" class="mobile-header-sub-links" href="/{{ region.country.slug }}-mountains/{{ region.slug }}/peaks/">Peaks</a><a style="{{ subnav_summits_style }}" class="mobile-header-sub-links" href="/{{ region.country.slug }}-mountains/{{ region.slug }}/summits/">Summits</a><a style="{{ subnav_challenges_style }}" class="mobile-header-sub-links" href="/{{ region.country.slug }}-mountains/{{ region.slug }}/challenges/">Challenges</a>
        </div>
    </div>
    <!-- End mobile header -->

    <div class="row sub-header-row hidden-xs hidden-sm regions-subnav-fixed" style="height: 50px; padding-right: 0px; top: 141px;">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999;">
            Sort climbs by:<a style="margin-left: 10px; color: rgb(153, 153, 153); font-weight: 300;" id="type-all" class="region-header-sub-links ajax-link" onclick="loadSummits('all', '1');">All</a><a style="margin-left: 35px; color: rgb(153, 153, 153); font-weight: 300;" id="type-successes" class="region-header-sub-links ajax-link" onclick="loadSummits('successes', '1');">Summits</a><a style="margin-left: 35px; color: rgb(153, 153, 153); font-weight: 300;" id="type-attempts" class="region-header-sub-links ajax-link" onclick="loadSummits('attempts', '1');">Attempts</a><span class="pull-right page-subtitle hidden-xs hidden-sm" style="font-size: 12px;">{{ summit_count|intcomma }} <span style="color: #999; font-weight: 400;">climb{{ summit_count_raw|pluralize:"s" }} in {{ region.name }}</span></span>
        </div>
    </div>
    <div class="row sub-header-row hidden-md hidden-lg regions-subnav-fixed filter-bar" style="height: 50px; padding-right: 0px;">
        <div id="ranked-by-button-div" class="col-xs-12 col-sm-12 col-md-12 col-lg-12" style="height: 50px; line-height: 26px; cursor: pointer;">
            <div>
                <div class="select" id="selectRankedBy" style="margin-top: 8px; font-size: 12px; text-align: center;">
                    <span style="color: #999;">Sort {{ summit_count|intcomma }} climb{{ summit_count_raw|pluralize:"s" }} by: </span>
                    <input type="hidden" id="hdnRankedBy" value="">
                    <button class="btn btn-default ranked-by-button" style="margin-bottom: 3px; border: none; padding-top: 7px; padding-bottom: 7px; margin-left: 1px; color: #00b1f2; font-size: 12px; padding-left: 5px; padding-right: 5px; background-color: #f2f2f2;" data-toggle="dropdown" aria-expanded="false">
                        <span id="ranked-by-title">All</span>
                        <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu ranked-by-list" style="cursor: pointer; height: 140px; left: 50%; overflow: auto; top: 50px;">
                        <li style="float: none; height: 30px; margin: 10px 0 0;" data-value="all"><a class="ranked-by-item" onclick="loadSummits('all', '1');">All</a></li>
                        <li style="float: none; height: 30px; margin: 10px 0 0;" data-value="successes"><a class="ranked-by-item" onclick="loadSummits('successes', '1');">Summits</a></li>
                        <li style="float: none; height: 30px; margin: 10px 0 0;" data-value="attempts"><a class="ranked-by-item" onclick="loadSummits('attempts', '1');">Attempts</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="row content-pane">
        <div class="col-md-12">
            <div class="row" id="ajax-data-loading" style="display: none;">
              <div class="col-md-12 data-loading" style="text-align: center; color: #666;">
                <i class="fa fa-spinner fa-spin fa-5x"></i>
              </div>
            </div>
            <div class="row" id="summits-list">
                <div class="col-md-12">

                </div>
            </div>
        </div>
    </div>
    <div class="row dark-background-row" id="pagination" style="border-bottom: none; color: #ccc; margin-top: -1px; height: 80px; margin-left: 0px; display: none;">
        <div class="col-md-12">
            <div class="row" style="padding-left: 15px; margin-left: 5px; text-align: center;">
                <span id="pagination-pages" class="step-links"></span>
            </div>
        </div>
    </div>
    <div class="row dark-header-row hidden-lg hidden-md hidden-sm">
        <div class="row">
            <div style="height: 28px;"></div>
        </div>
    </div>
    <div class="row dark-header-row hidden-xs">
        <div class="row">
            <div style="height: 53px;"></div>
        </div>
    </div>
</div>

<script type="text/javascript">

    var summitSort;

    function loadSummits(type, page) {

        summitSort = type;
        $('#pagination').css('display','none');
        window.scrollTo(0, 0);

        switch(type) {
            case 'all':
                $('#type-all').css('color', '#F24100');
                $('#type-all').css('font-weight', '500');
                $('#type-successes').css('color', '#999');
                $('#type-successes').css('font-weight', '300');
                $('#type-attempts').css('color', '#999');
                $('#type-attempts').css('font-weight', '300');
                $('#ranked-by-title').html('All');
                window.location.hash = 'type=all&page='+page;
                break;
            case 'successes':
                $('#type-all').css('color', '#999');
                $('#type-all').css('font-weight', '300');
                $('#type-successes').css('color', '#F24100');
                $('#type-successes').css('font-weight', '500');
                $('#type-attempts').css('color', '#999');
                $('#type-attempts').css('font-weight', '300');
                $('#ranked-by-title').html('Successful summits');
                window.location.hash = 'type=successes&page='+page;
                break;
            case 'attempts':
                $('#type-all').css('color', '#999');
                $('#type-all').css('font-weight', '300');
                $('#type-successes').css('color', '#999');
                $('#type-successes').css('font-weight', '300');
                $('#type-attempts').css('color', '#F24100');
                $('#type-attempts').css('font-weight', '500');
                $('#ranked-by-title').html('Attempts');
                window.location.hash = 'type=attempts&page='+page;
                break;
            default:
                $('#type-all').css('color', '#F24100');
                $('#type-all').css('font-weight', '500');
                $('#type-successes').css('color', '#999');
                $('#type-successes').css('font-weight', '300');
                $('#type-attempts').css('color', '#999');
                $('#type-attempts').css('font-weight', '300');
                $('#ranked-by-title').html('All');
                window.location.hash = 'type=all&page='+page;
        }

        $('#summits-list').empty();
        $('#ajax-data-loading').css('display', 'inline');
        var summitCount = '{{ summit_count }}'.replace(',', '');
        var totalPages = Math.floor(parseInt(summitCount)/40);
        var counter = 1;
        $.getJSON('{% url "summits_list" %}?country_code={{ region.country.code }}&region_slug={{ region.slug }}&type='+type+'&page='+page , function(data) {

            $.each( data, function( key, val ) {
                if (key=='summits') {
                    $.each( val, function( summitkey, summitval ) {
                        var summitdate = new Date(summitval.summitlog_date);
                        var today = new Date();
                        var timeDiff = Math.abs(today.getTime() - summitdate.getTime());
                        var diffDays = Math.ceil(timeDiff / (1000 * 3600 * 24));
                        var photo_arr = summitval.photos;
                        var avatar;
                        var isUsa = false;
                        if (summitval.avatar_url != 'None') {
                            avatar = '<img src="{{MEDIA_URL}}' + summitval.avatar_url + '" class="summit-card-user-avatar">&nbsp;&nbsp;';
                        } else {
                            avatar = '';
                        }

                        //build country string
                        var country = '';
                        $.each( summitval.country, function( countrykey, countryval ) {
                            country = country + countryval.country_name + ' / ';
                            if (countryval.country_name == 'United States') {
                                isUsa = true;
                            }
                        });
                        country = country.substr(0, country.length-3);
                        if (country == '') {
                            country = 'Unknown Country';
                        }

                        //build region string
                        var region = '';
                        $.each( summitval.region, function( regionkey, regionval ) {
                            region = region + regionval.region_name + ', ' + regionval.country_name + ' / ';
                        });
                        region = region.substr(0, region.length-3);
                        if (region == '') {
                            region = country;
                        }

                        var dividerDiv = '';
                        if (counter > 1) {
                            dividerDiv = '<div class="row dark-background-row"><div class="sp-60"></div></div>';
                        }

                        if (summitval.date_entered == 'False') {
                            var desktop_date = 'date unknown';
                            var mobile_date = 'date unknown';
                        } else {
                            var display_date = $.format.date(summitval.summitlog_date + 'T00:00:00', 'MMM d, yyyy');
                            var desktop_date = display_date + '&nbsp;/&nbsp;<time class="timeago" datetime="' + summitval.summitlog_date + 'T00:00:00">' + summitval.summitlog_date + '</time>';
                            var mobile_date = '<time class="timeago" datetime="' + summitval.summitlog_date + 'T00:00:00">' + summitval.summitlog_date + '</time>';
                        }

                        var header_border = '';
                        if (photo_arr.length == 0 && (summitval.gpx_file == 'None' || summitval.gpx_file == '') && summitval.log != null) {
                            header_border = 'border-bottom: 1px solid #c0c0c0; ';
                        }

                        var attempt = '';
                        var mobile_stats = '';

                        var max_username_length = 15;
                        var max_region_length = 27;
                        var trimmed_username = summitval.username.length > max_username_length ? summitval.username.substring(0, max_username_length - 3) + "..." : summitval.username;
                        var trimmed_region = region.length > max_region_length ? region.substring(0, max_region_length - 3) + "..." : region;

                        if (summitval.attempt == 'True') {
                            attempt = '<span class="summit-attempt">Attempt</span>&nbsp;&nbsp;&bull;&nbsp;&nbsp;';
                            mobile_stats = '<span class="summit-attempt">Attempt</span>&nbsp;&bull;&nbsp;'  + '<a href="/members/' + summitval.username + '/">' + trimmed_username + '</a>&nbsp;&bull;&nbsp;' + mobile_date;
                        } else {
                            mobile_stats = trimmed_region + '&nbsp;&bull;&nbsp;<a href="/members/' + summitval.username + '/">' + trimmed_username + '</a>&nbsp;&bull;&nbsp;' + mobile_date;
                        }

                        $('#summits-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">' + dividerDiv + '<div class="row" style="' + header_border + 'cursor: pointer;" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');"><div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 summit-card-header" style="background-color: #fff; border-right: 1px solid #c0c0c0; letter-spacing: 1.0px;"><div class="summit-list-stats summit-card-user-stats hidden-xs hidden-sm" style="float: right;">&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + avatar + '<a href="/members/' + summitval.username + '/">' + summitval.username + '</a>&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + desktop_date + '</div><div class="summit-list-stats summit-card-user-stats hidden-lg hidden-md hidden-xs" style="float: right;">&nbsp;&nbsp;&bull;&nbsp;&nbsp;<a href="/members/' + summitval.username + '/">' + summitval.username + '</a>&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + mobile_date + '</div><div class="summit-list-stats summit-card-peak-stats hidden-lg hidden-md hidden-xs" style="float: right;">' + attempt + region + '</div><div class="stats-header summit-card-peak-title" style="height: 70px; margin-top: 0px; margin-bottom: 0px;"><div class="summit-list-stats summit-card-peak-stats hidden-xs hidden-sm" style="float: right;">' + attempt + numberWithCommas(Math.floor(summitval.elevation)) + ' ft / ' + numberWithCommas(Math.floor(summitval.elevation * .3048)) + ' m&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + region + '</div><div class="ellipsis" style="overflow: hidden;"><a style="color: #000000;" href="/' + summitval.peak_slug + '/">' + summitval.peak_name + '</a></div></div><div class="summit-list-stats summit-card-mobile-stats hidden-lg hidden-md hidden-sm">' + mobile_stats + '</div></div></div>');

                        //get favorites count
                        var favorites_arr = summitval.favorites;
                        var favorites_count = favorites_arr.length;
                        if (favorites_count == '0') {
                            favorites_count = '&nbsp;';
                        }

                        //get comments count
                        var comments_arr = summitval.comments;
                        var comments_count = comments_arr.length;
                        if (comments_count == '0') {
                            comments_count = '&nbsp;';
                        }

                        var staticMapZoom = '/' + summitval.peak_long + ',' + summitval.peak_lat + ',10,0.00,0.00';
                        var twoPhotoMapClass = 'col-lg-6 col-md-6 col-sm-4 hidden-xs map-card-web-2 map-card-tablet-2 map-card-mobile-2';
                        //gpx file?
                        if (summitval.gpx_file != 'None' && summitval.gpx_file != '') {
                            staticMapZoom = '/auto';
                            twoPhotoMapClass = 'col-lg-6 col-md-6 col-sm-4 col-xs-12 map-card-web-2 map-card-tablet-2 map-card-mobile-2';
                        }

                        //add photos
                        var photo_index = 0;
                        var photo_arr = summitval.photos;
                        var photos_count = photo_arr.length;
                        var photos_style = '';
                        if (photos_count == '0') {
                            photos_count = '&nbsp;';
                            photos_style = 'display: none;'
                        }
                        var staticMapUrl = summitval.map_mapbox_thumbnail;

    // if we have a gpx file
                        if (summitval.gpx_file != 'None' && summitval.gpx_file != '') {

                            //build stats overlay
                            var statsOverlayText = '';
                            var mobileStatsOverlayText = '';
                            var gpxStatsOverlay = '';
                            var mobileStatsOverlay = '';
                            var divStyle = '';
                            if (summitval.total_distance > 0 || summitval.total_trip_time > 0 || summitval.elevation_gain > 0) {
                                if (isUsa) {
                                    if (summitval.total_distance > 0) {
                                        gpxStatsOverlay = gpxStatsOverlay + parseFloat(summitval.total_distance).toFixed(1) + ' mi &bull; ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="fas fa-arrows-alt-h" style="margin-right: 5px;"></i>' + parseFloat(summitval.total_distance).toFixed(1) + ' mi</span><div style="width: 1px; height: 30px; background-color: #ccc; margin-top: 10px;"></div> ';
                                    }
                                    if (summitval.elevation_gain > 0) {
                                        gpxStatsOverlay = gpxStatsOverlay + parseInt(summitval.elevation_gain).toLocaleString() + ' ft gain &bull; ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="fas fa-long-arrow-alt-up fa-rotate-45" style="margin-right: 5px;"></i>' + parseInt(summitval.elevation_gain).toLocaleString() + ' ft gain</span><div style="width: 1px; height: 30px; background-color: #ccc; margin-top: 10px;"></div> ';
                                    }
                                    if (summitval.total_trip_time > 0) {
                                        totalSeconds = parseFloat(summitval.total_trip_time);
                                        hours = Math.floor(totalSeconds / 3600);
                                        totalSeconds %= 3600;
                                        minutes = Math.floor(totalSeconds / 60);
                                        gpxStatsOverlay = gpxStatsOverlay + hours + 'h ' + minutes + 'm ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="far fa-clock" style="margin-right: 5px;"></i>' + hours + ' hr ' + minutes + ' min </span></div>';
                                    }
                                } else {
                                    if (summitval.total_distance > 0) {
                                        km = parseFloat(summitval.total_distance) * 1.609344
                                        gpxStatsOverlay = gpxStatsOverlay + km.toFixed(1) + ' km &bull; ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="fas fa-arrows-alt-h" style="margin-right: 5px;"></i>' + km.toFixed(1) + ' km</span><div style="width: 1px; height: 30px; background-color: #ccc; margin-top: 10px;"></div> ';
                                    }
                                    if (summitval.elevation_gain > 0) {
                                        m = parseFloat(summitval.elevation_gain) * 0.3048
                                        gpxStatsOverlay = gpxStatsOverlay + parseInt(m).toLocaleString() + ' m gain &bull; ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="fas fa-long-arrow-alt-up" style="margin-right: 5px;"></i>' + parseInt(m).toLocaleString() + ' m gain</span><div style="width: 1px; height: 30px; background-color: #ccc; margin-top: 10px;"></div> ';
                                    }
                                    if (summitval.total_trip_time > 0) {
                                        totalSeconds = parseFloat(summitval.total_trip_time);
                                        hours = Math.floor(totalSeconds / 3600);
                                        totalSeconds %= 3600;
                                        minutes = Math.floor(totalSeconds / 60);
                                        gpxStatsOverlay = gpxStatsOverlay + hours + 'h ' + minutes + 'm ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="far fa-clock" style="margin-right: 5px;"></i>' + hours + ' hr ' + minutes + ' min </span>';
                                    }
                                }
                            }

                            statsOverlayText = '<div class="user-photo-info hidden-xs" style="display: block; height: 60px;"><span class="data photo-caption" style="position: absolute; bottom: -5px; right: 10px; color: #fff;"><p class="bagger" style="font-size: 16px; margin: 0 10px 15px 0;">' + gpxStatsOverlay + '</p></span></div>';
                            mobileStatsOverlayText = '<div class="hidden-lg hidden-md hidden-sm col-xs-12" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; background-color: #fff; border-bottom: 1px solid #c0c0c0; margin-top: -1px; padding-left: 0px; padding-right: 0px;"><div class="col-md-12" style="height: 50px;line-height: 50px;font-size: 12px;color: #999;display: flex;justify-content: space-between;background-color: #f2f2f2;padding-left: 0px;padding-right: 1px;">' + mobileStatsOverlay + '</div></div>';

                            if (photo_arr.length == 0) {
                                $('#summits-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 map-card-web-0 map-card-tablet-0 map-card-mobile-0" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" id="map-' + summitval.id + '" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="img-responsive peakeryPhoto photography peakimg-responsive" src="{% static 'img/spacer.png' %}"></div>' + statsOverlayText + '</div>');
                            } else if (photo_arr.length == 1) {
                                $.each(summitval.photos, function (photokey, photoval) {
                                    photo_index++;
                                    $('#summits-list').append('<div class="summit-card-photo-' + photo_index + ' col-lg-3 col-md-3 col-sm-4 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                });
                                $('#summits-list').append('<div class="col-lg-9 col-md-9 col-sm-8 col-xs-6 map-card-web-1 map-card-tablet-1 map-card-mobile-1" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png' %}"></div>' + statsOverlayText + '</div>');
                            } else if (photo_arr.length == 2) {
                                $.each(summitval.photos, function (photokey, photoval) {
                                    photo_index++;
                                    $('#summits-list').append('<div class="summit-card-photo-' + photo_index + ' col-lg-3 col-md-3 col-sm-4 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                });
                                $('#summits-list').append('<div class="' + twoPhotoMapClass + '" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png' %}"></div>' + statsOverlayText + '</div>');
                            } else {
                                $.each(summitval.photos, function (photokey, photoval) {
                                    if (photo_index < 3) {
                                        divClass = 'col-lg-3 col-md-3 col-sm-4 col-xs-6';
                                    } else {
                                        divClass = 'col-lg-3 col-md-3 hidden-sm col-xs-6';
                                    }
                                    photo_index++;
                                    $('#summits-list').append('<div class="summit-card-photo-' + photo_index + ' ' + divClass + ' summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                    if (photo_index > 3) {
                                        return false;
                                    }
                                });
                                if (photo_arr.length == 3) {
                                    $('#summits-list').append('<div class="col-lg-3 col-md-3 col-sm-12 col-xs-6 map-card-web-4 map-card-tablet-3" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png' %}"></div>' + statsOverlayText + '</div>');
                                } else {
                                    $('#summits-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 map-card-web-4 map-card-tablet-4 map-card-mobile-4" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png' %}"></div>' + statsOverlayText + '</div>');
                                }
                            }
                            $('#summits-list').append(mobileStatsOverlayText);
                        } else {
    // if no GPX file:
                            if (photo_arr.length == 1) {
                                $.each(summitval.photos, function (photokey, photoval) {
                                    photo_index++;
                                    $('#summits-list').append('<div class="summit-card-photo-' + photo_index + ' col-lg-3 col-md-3 col-sm-4 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                });
                                $('#summits-list').append('<div class="col-lg-9 col-md-9 col-sm-8 col-xs-6  map-card-web-1 map-card-tablet-1 map-card-mobile-1" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png' %}"></div></div>');
                            } else if (photo_arr.length == 2) {
                                $.each(summitval.photos, function (photokey, photoval) {
                                    photo_index++;
                                    $('#summits-list').append('<div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                });
                                $('#summits-list').append('<div class="' + twoPhotoMapClass + '" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png' %}"></div></div>');
                            } else if (photo_arr.length >= 3) {
                                $.each(summitval.photos, function (photokey, photoval) {
                                    if (photo_index < 3) {
                                        divClass = 'col-lg-3 col-md-3 col-sm-4 col-xs-6';
                                    } else {
                                        divClass = 'col-lg-3 col-md-3 hidden-sm col-xs-6';
                                    }
                                    photo_index++;
                                    $('#summits-list').append('<div class="summit-card-photo-' + photo_index + ' ' + divClass + ' summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                    if (photo_index > 3) {
                                        return false;
                                    }
                                });
                                if (photo_arr.length == 3) {
                                    $('#summits-list').append('<div class="col-lg-3 col-md-3 hidden-sm col-xs-6 map-card-web-3 map-card-tablet-3 map-card-mobile-3" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png' %}"></div></div>');
                                }
                            }
                        }

                        //add log
                        if (summitval.log != null) {
                            var log_text = summitval.log.replace(/(?:\r\n|\r|\n)/g, '<br>');
                            if (log_text.length > 9999) {
                                log_text = summitval.log.substring(0, 700)+'...';
                            }
                            $('#summits-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; background-color: #fff; border-bottom: 1px solid #c0c0c0;"><div><p class="summit-log-summary">' + log_text + '</p></div></div>');
                        }
                        //footer
                        if (summitval.liked == '1') {
                            classLiked = 'liked';
                        } else {
                            classLiked = '';
                        }
                        if (summitval.log != null || (summitval.gpx_file != 'None' && summitval.gpx_file != '') || photo_arr.length > 0 || summitval.peak_thumbnail_url != 'images/img/cache/default.png.350x245_q95_crop.jpg') {
                            $('#summits-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');"><div class="row"><div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="height: 70px; background-color: #fff; z-index: 1; border-bottom-left-radius: 4px; border-bottom-right-radius: 4px; box-shadow: 0px 10px 10px rgba(0, 0, 0, 1);"><div><p><span class="summit-list-footer" style="font-weight: 600; line-height: 70px;"><a href="/' + summitval.peak_slug + '/summits/' + summitval.id + '/"><button style="padding-left: 13px; width: 100%; height: 100%; text-align: left; border: none; color: #00B1F2; font-weight: 600; position: absolute; left: -2px; top: -2px; z-index: 1; border-bottom-left-radius: 4px; border-bottom-right-radius: 4px;" class="btn btn-default btn-see-full-log">SEE FULL LOG</button></a></span><span class="summit-list-footer pull-right" style="margin-top: 8px; position: absolute; right: 30px; z-index: 2;"><span style="margin-left: 40px;"><a href="javascript:like_summit(' + summitval.id + ');" class="like_summit{{ is_authenticated }}" id="summitlog-with-' + summitval.id + '"><span class="' + classLiked + '" id="summitlog-like-' + summitval.id + '" style="width: 32px; height: 32px; display: inline-block; color: #ffffff; line-height: 32px; text-align: center; padding-left: 2px; font-size: 10px; font-weight: bold; background-size: 100%; background-image: url({% static 'img/fa-heart_256_0_00b1f2_none.png'%});">' + favorites_count + '</span></a></span><span style="margin-left: 40px;"><a href="/' + summitval.peak_slug + '/summits/' + summitval.id + '/"><span style="width: 32px; height: 32px; display: inline-block; color: #ffffff; line-height: 30px; text-align: center; padding-left: 3px; font-size: 10px; font-weight: bold; background-size: 100%; background-image: url({% static 'img/fa-comment_256_0_00b1f2_none.png'%});">' + comments_count + '</span></a></span><span style="margin-left: 40px; ' + photos_style + '"><a href="/' + summitval.peak_slug + '/summits/' + summitval.id + '/"><span style="width: 32px; height: 32px; display: inline-block; color: #ffffff; line-height: 36px; text-align: center; padding-left: 2px; font-size: 10px; font-weight: bold; background-size: 100%; background-image: url({% static 'img/fa-camera_256_0_00b1f2_none.png'%});">' + photos_count + '</span></a></span></span></p><span class="peak-list-highlights"><p></p></span></div></div></div>');
                        }
                        $('#summits-list').append('</div>');

                        if (summitval.gpx_mapbox_thumbnail != '') {
                            $("#map-"+summitval.id).css("background-image", "url('" + summitval.gpx_mapbox_thumbnail + "')");
                        }
                        counter++;

                    });
                    {% if summit_count != '0' %}
                    //show pagination
                    var paginationHtml = '';
                    if (parseInt(page) == totalPages && totalPages > 1) {
                        paginationHtml = '<div style="display: inline-block; height: 55px;"><a onclick="loadSummits(\''+summitSort+'\', ' + (parseInt(page)-1).toString() + ');"><img src="{% static 'img/font-awesome_4-7-0_arrow-circle-left_256_0_00b1f2_none.png' %}" style="height: 26px; margin-bottom: 5px;"></a></div><div class="current" style="margin-left: 20px; margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">summits ' + ((parseInt(page)-1)*25+1).toString() + ' - {{ summit_count|intcomma }} of {{ summit_count|intcomma }}</div>';
                    } else if (parseInt(page) > 1) {
                        paginationHtml = '<div style="display: inline-block; height: 55px;"><a onclick="loadSummits(\''+summitSort+'\', ' + (parseInt(page)-1).toString() + ');"><img src="{% static 'img/font-awesome_4-7-0_arrow-circle-left_256_0_00b1f2_none.png' %}" style="height: 26px; margin-bottom: 5px;"></a></div><div class="current" style="margin-left: 20px; margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">summits ' + ((parseInt(page)-1)*25+1).toString() + ' - ' + ((parseInt(page))*25).toString() + ' of {{ summit_count|intcomma }}</div>';
                    } else if (totalPages > 1) {
                        paginationHtml = '<div class="current" style="margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">summits 1 - 25 of {{ summit_count|intcomma }}</div>';
                    } else {
                        paginationHtml = '<div class="current" style="margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">summits 1 - {{ summit_count|intcomma }} of {{ summit_count|intcomma }}</div>';
                    }
                    if (parseInt(page) < totalPages) {
                        paginationHtml = paginationHtml + '<div style="display: inline-block; height: 55px;"><a onclick="loadSummits(\''+summitSort+'\', ' + (parseInt(page)+1).toString() + ');"><img src="{% static 'img/font-awesome_4-7-0_arrow-circle-right_256_0_00b1f2_none.png'%}" style="height: 26px; margin-bottom: 5px;"></i></a></div>';
                        $('#pagination-pages').html(paginationHtml);
                        $('#pagination').css('display', 'inherit');
                    } else {
                        $('#pagination-pages').html(paginationHtml);
                        $('#pagination').css('display', 'inherit');
                    }
                    {% endif %}
                }
            });

            $("time.timeago").timeago();
            $('#ajax-data-loading').css('display', 'none');
        });

    }

    function numberWithCommas(x) {
        return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    function change_like_count(id,count){
        $('#summitlog-like-'+id).html(count+" like");
    }

    {% if user.is_authenticated %}
    function like_summit(id) {
        var likeButton = $('#summitlog-like-'+id);
        var summitID = id;
        var summitlogID = '18';
        if (likeButton.hasClass("login")){
            $.facebox( {ajax:'/accounts/login/?next=/accounts/login_reload/login/'});
        } else {
            if (likeButton.hasClass("liked")) {
                likeButton.removeClass("liked");
                likeButton.addClass("liking");
                var count = $('#summitlog-like-'+summitID).html();
                count = parseInt(count)-1;
                if (count > 0) {
                    $('#summitlog-like-'+summitID).html(count);
                } else {
                    $('#summitlog-like-'+summitID).html('&nbsp;');
                }
                var request = $.ajax({
                  method: "POST",
                  url: "/favorites/remove/",
                  dataType: "json",
                  data: {object_id: summitID, content_type_id: summitlogID}
                });
                request.done(function( data ) {
                  //$('#summitlog-like-'+summitID).html(data.count);
                  likeButton.removeClass("liking");
                });
            } else {
                likeButton.addClass("liked");
                likeButton.addClass("liking");
                var count = $('#summitlog-like-'+summitID).html();
                if (count == '&nbsp;') {
                    count = 1;
                } else {
                    count = parseInt(count)+1;
                }
                $('#summitlog-like-'+summitID).html(count);
                var request = $.ajax({
                  method: "POST",
                  url: "/favorites/add/",
                  dataType: "json",
                  data: {object_id: summitID, content_type_id: summitlogID}
                });
                request.done(function( data ) {
                  //$('#summitlog-like-'+summitID).html(data.count);
                  likeButton.removeClass("liking");
                });
            }
        }
        return false;
    }
    {% else %}
    function like_summit(id) {
        $('#accounts-login').modal('show');
    }
    {% endif %}

    function get_gpx_distance(lat1, lon1, lat2, lon2) {
        var radlat1 = Math.PI * lat1/180
        var radlat2 = Math.PI * lat2/180
        var theta = lon1-lon2
        var radtheta = Math.PI * theta/180
        var dist = Math.sin(radlat1) * Math.sin(radlat2) + Math.cos(radlat1) * Math.cos(radlat2) * Math.cos(radtheta);
        dist = Math.acos(dist)
        dist = dist * 180/Math.PI
        dist = dist * 60 * 1.1515
        return dist
    }

    function viewSummit(url) {
        window.location.href = url;
    }

    function highlightPhotos(summitid) {
        $('.summitimg-'+summitid).addClass('hover-photos-hover');
    }

    function unhighlightPhotos(summitid) {
        $('.summitimg-'+summitid).removeClass('hover-photos-hover');
    }

    $(document).ready(function() {

        // Hide mobile filter bar on on scroll down
        var didScroll;
        var lastScrollTop = 0;
        var delta = 5;

        $(window).scroll(function(event){
            didScroll = true;
        });

        document.addEventListener("touchstart", ScrollStart, false);

        setInterval(function() {
            if (didScroll) {
                hasScrolled();
                didScroll = false;
            }
        }, 100);

        function ScrollStart() {
            //start of scroll event for iOS
            hasScrolled();
        }

        function hasScrolled() {
            var st = $(this).scrollTop();

            // Make sure they scroll more than delta
            if (Math.abs(lastScrollTop - st) <= delta)
                return;

            // If they scrolled down and are past the filter bar, add class .filter-scrollup.
            // This is necessary so you never see what is "behind" the navbar.
            if (st > lastScrollTop && st > 50) {
                // Scroll Down
                $('.filter-bar').hide();
            } else {
                // Scroll Up
                if (st + $(window).height() < $(document).height()) {
                    $('.filter-bar').show();
                }
            }
            lastScrollTop = st;
        }

        var viewport_height = $(window).height();
        var viewport_width = $(window).width();
        var footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
        var data_loading_margin = (viewport_height - footer_height) / 3;
        $('.data-loading').css('margin-top', data_loading_margin);
        $(window).resize(function() {
            viewport_height = $(window).height();
            viewport_width = $(window).width();
            footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
            data_loading_margin = (viewport_height - footer_height) / 3;
            $('.data-loading').css('margin-top', data_loading_margin);
        });

        var vars = [], hash, sort, page;
        var q = document.URL.split('#')[1];
        if(q != undefined){
            q = q.split('&');
            for(var i = 0; i < q.length; i++){
                hash = q[i].split('=');
                vars.push(hash[1]);
                vars[hash[0]] = hash[1];
            }
        }

        if (vars['type'] != undefined) {
            sort = vars['type'];
        } else {
            sort = 'all';
        }

        if (vars['page'] != undefined) {
            page = vars['page'];
        } else {
            page = '1';
        }

        loadSummits(sort, page);

        $('#ranked-by-button-div').click(function(e){
            $('.ranked-by-list').toggle();
        });

    });

</script>

{% endblock %}
