{% extends "base.html" %}
{% load static %}
{% load humanize %}

{% block title %}{% if continent %}{{ continent.name }}{% else %}World{% endif %} mountains list{% endblock %}

{% block titlemeta_overwrite %}{% if continent %}{{ continent.name }}{% else %}World{% endif %} mountains list{% endblock %}
{% block description %}{{ meta_description }}{% endblock %}

{% block directory_active %}active{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row hidden-xs">
        <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 65px; border-radius: 12px 12px 0 0;"><span style="font-size: 24px; font-weight: 600;">
            <div id="breadcrumbs">
                <ul>
                    <li class="hidden-sm hidden-xs"><a style="font-weight: 500;" href="{% url "world_list" %}">World</a> &raquo; </li>
                    <li><h1 class="regions-title">{{ continent.name }} mountains</h1></li>
                </ul>
                <div class="pull-right hidden-xs" style="margin-top: -5px;">
                    <a style="{{ subnav_info_style }}" class="region-header-sub-links" href="/region/{{ continent_slug }}-mountains/">Info</a><a style="{{ subnav_map_style }}" class="region-header-sub-links" href="/region/{{ continent_slug }}-mountains/map/">Map</a><a style="{{ subnav_peaks_style }}" class="region-header-sub-links" href="/region/{{ continent_slug }}-mountains/peaks/">Peaks</a><a style="{{ subnav_summits_style }}" class="region-header-sub-links" href="/region/{{ continent_slug }}-mountains/summits/">Climbs</a><a style="{{ subnav_challenges_style }}" class="region-header-sub-links" href="/region/{{ continent_slug }}-mountains/challenges/">Challenges</a>
                </div>
            </div>
        </span></div>
    </div>
{% endblock %}

{% block content %}

<style>
   @media screen and (max-width: 767px) and (min-width: 1px) {
       #content-body {
           margin-top: 0px;
       }
       .content-pane {
           margin-top: 100px;
       }
       #main {
           margin-top: 20px;
       }
       .regions-subnav-fixed {
            top: 120px !important;
        }
   }
    @media screen and (min-width: 768px) {
        .content-pane {
           margin-top: 50px;
       }
    }
    @media screen and (min-width: 768px) and (max-width: 1279px) {
        .regions-subnav-fixed {
            top: 141px !important;
        }
    }
    @media screen and (min-width: 1280px) {
        .regions-subnav-fixed {
            top: 141px !important;
        }
    }
</style>

<div class="container">
    <!-- Mobile header -->
    <div class="row sub-header-row hidden-lg hidden-md hidden-sm" style="height: 50px; position: fixed; width: 100%; z-index: 2;">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999; display: flex; justify-content: space-between;">
            <a style="{{ subnav_info_style }} margin-left: 0px;" class="mobile-header-sub-links" href="/region/{{ continent_slug }}-mountains/">Info</a><a style="{{ subnav_map_style }}" class="mobile-header-sub-links" href="/region/{{ continent_slug }}-mountains/map/">Map</a><a style="{{ subnav_peaks_style }}" class="mobile-header-sub-links" href="/region/{{ continent_slug }}-mountains/peaks/">Peaks</a><a style="{{ subnav_summits_style }}" class="mobile-header-sub-links" href="/region/{{ continent_slug }}-mountains/summits/">Climbs</a><a style="{{ subnav_challenges_style }}" class="mobile-header-sub-links" href="/region/{{ continent_slug }}-mountains/challenges/">Challenges</a>
        </div>
    </div>
    <!-- End mobile header -->

    <div class="row sub-header-row hidden-xs hidden-sm regions-subnav-fixed" style="height: 50px; padding-right: 0px; top: 141px;">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999;">
            Sort by:<a style="margin-left: 10px; color: rgb(153, 153, 153); font-weight: 300;" id="sort-elevation" class="region-header-sort-links ajax-link" onclick="loadPeaks('elevation', '1');">Elevation</a><a style="margin-left: 35px; color: rgb(153, 153, 153); font-weight: 300;" id="sort-prominence" class="region-header-sort-links ajax-link" onclick="loadPeaks('prominence', '1');">Prominence</a><a style="margin-left: 35px; color: rgb(153, 153, 153); font-weight: 300;" id="sort-name" class="region-header-sort-links ajax-link" onclick="loadPeaks('name', '1');">Name</a><a style="margin-left: 35px; color: rgb(153, 153, 153); font-weight: 300;" id="sort-most-summits" class="region-header-sort-links ajax-link" onclick="loadPeaks('most_summits', '1');">Most climbs</a><a style="margin-left: 35px; color: rgb(153, 153, 153); font-weight: 300;" id="sort-least-summits" class="region-header-sort-links ajax-link" onclick="loadPeaks('least_summits', '1');">Least climbs</a><span class="pull-right page-subtitle hidden-xs hidden-sm" style="font-size: 12px;">{{ peak_count|intcomma }} <span style="color: #999; font-weight: 400;">mountain{{ peak_count|pluralize:"s" }} in {{ continent.name }}</span></span>
        </div>
    </div>
    <div class="row sub-header-row hidden-md hidden-lg regions-subnav-fixed" style="height: 50px; padding-right: 0px;">
        <div id="ranked-by-button-div" class="col-xs-12 col-sm-12 col-md-12 col-lg-12" style="height: 50px; line-height: 26px; cursor: pointer;">
            <div>
                <div class="select" id="selectRankedBy" style="margin-top: 8px; font-size: 12px; text-align: center;">
                    <span style="color: #999;">Sort {{ peak_count|intcomma }} peak{{ peak_count|pluralize:"s" }} by: </span>
                    <input type="hidden" id="hdnRankedBy" value="">
                    <button id="ranked-by-button" class="btn btn-default ranked-by-button" style="margin-bottom: 3px; border: none; padding-top: 7px; padding-bottom: 7px; margin-left: 1px; color: #00b1f2; font-size: 12px; padding-left: 5px; padding-right: 5px; background-color: #f2f2f2;" data-toggle="dropdown" aria-expanded="false">
                        <span id="ranked-by-title">Elevation</span>
                        <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu ranked-by-list" style="cursor: pointer; height: 215px; left: 50%; overflow: auto; top: 50px;">
                        <li style="float: none; height: 30px; margin: 10px 0 0;" data-value="elevation"><a class="ranked-by-item" onclick="loadPeaks('elevation', '1');">Elevation</a></li>
                        <li style="float: none; height: 30px; margin: 10px 0 0;" data-value="prominence"><a class="ranked-by-item" onclick="loadPeaks('prominence', '1');">Prominence</a></li>
                        <li style="float: none; height: 30px; margin: 10px 0 0;" data-value="name"><a class="ranked-by-item" onclick="loadPeaks('name', '1');">Name</a></li>
                        <li style="float: none; height: 30px; margin: 10px 0 0;" data-value="most_summits"><a class="ranked-by-item" onclick="loadPeaks('most_summits', '1');">Most climbs</a></li>
                        <li style="float: none; height: 30px; margin: 10px 0 0;" data-value="least_summits"><a class="ranked-by-item" onclick="loadPeaks('least_summits', '1');">Least climbs</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="row content-pane">
        <div class="col-md-12">
            <div class="row" id="ajax-data-loading" style="display: none;">
              <div class="col-md-12 data-loading" style="text-align: center; color: #666;">
                <i class="fa fa-spinner fa-spin fa-5x"></i>
              </div>
            </div>
            <div class="row" id="peaks-list">
            </div>
        </div>
    </div>
    <div class="row dark-background-row" id="pagination" style="border-bottom: none; color: #ccc; margin-top: -1px; height: 80px; margin-left: 0px; display: none;">
        <div class="col-md-12">
            <div class="row" style="padding-left: 15px; margin-left: 5px; text-align: center;">
                <span id="pagination-pages" class="step-links"></span>
            </div>
        </div>
    </div>
    <div class="row dark-header-row hidden-lg hidden-md hidden-sm">
        <div class="row">
            <div style="height: 28px;"></div>
        </div>
    </div>
    <div class="row dark-header-row hidden-xs">
        <div class="row">
            <div style="height: 53px;"></div>
        </div>
    </div>
</div>

<script type="text/javascript">

    var peakSort;

    function loadPeaks(sort, page) {

        switch(sort) {
            case 'elevation':
                $('#sort-elevation').css('color', '#F24100');
                $('#sort-elevation').css('font-weight', '500');
                $('#sort-prominence').css('color', '#999');
                $('#sort-prominence').css('font-weight', '300');
                $('#sort-name').css('color', '#999');
                $('#sort-name').css('font-weight', '300');
                $('#sort-most-summits').css('color', '#999');
                $('#sort-most-summits').css('font-weight', '300');
                $('#sort-least-summits').css('color', '#999');
                $('#sort-least-summits').css('font-weight', '300');
                window.location.hash = 'order=elevation&page='+page;
                $('#ranked-by-title').html('Elevation');
                break;
            case 'prominence':
                $('#sort-elevation').css('color', '#999');
                $('#sort-elevation').css('font-weight', '300');
                $('#sort-prominence').css('color', '#F24100');
                $('#sort-prominence').css('font-weight', '500');
                $('#sort-name').css('color', '#999');
                $('#sort-name').css('font-weight', '300');
                $('#sort-most-summits').css('color', '#999');
                $('#sort-most-summits').css('font-weight', '300');
                $('#sort-least-summits').css('color', '#999');
                $('#sort-least-summits').css('font-weight', '300');
                window.location.hash = 'order=prominence&page='+page;
                $('#ranked-by-title').html('Prominence');
                break;
            case 'name':
                $('#sort-elevation').css('color', '#999');
                $('#sort-elevation').css('font-weight', '300');
                $('#sort-prominence').css('color', '#999');
                $('#sort-prominence').css('font-weight', '300');
                $('#sort-name').css('color', '#F24100');
                $('#sort-name').css('font-weight', '500');
                $('#sort-most-summits').css('color', '#999');
                $('#sort-most-summits').css('font-weight', '300');
                $('#sort-least-summits').css('color', '#999');
                $('#sort-least-summits').css('font-weight', '300');
                window.location.hash = 'order=name&page='+page;
                $('#ranked-by-title').html('Name');
                break;
            case 'most_summits':
                $('#sort-elevation').css('color', '#999');
                $('#sort-elevation').css('font-weight', '300');
                $('#sort-prominence').css('color', '#999');
                $('#sort-prominence').css('font-weight', '300');
                $('#sort-name').css('color', '#999');
                $('#sort-name').css('font-weight', '300');
                $('#sort-most-summits').css('color', '#F24100');
                $('#sort-most-summits').css('font-weight', '500');
                $('#sort-least-summits').css('color', '#999');
                $('#sort-least-summits').css('font-weight', '300');
                window.location.hash = 'order=most_summits&page='+page;
                $('#ranked-by-title').html('Most summits');
                break;
            case 'least_summits':
                $('#sort-elevation').css('color', '#999');
                $('#sort-elevation').css('font-weight', '300');
                $('#sort-prominence').css('color', '#999');
                $('#sort-prominence').css('font-weight', '300');
                $('#sort-name').css('color', '#999');
                $('#sort-name').css('font-weight', '300');
                $('#sort-most-summits').css('color', '#999');
                $('#sort-most-summits').css('font-weight', '300');
                $('#sort-least-summits').css('color', '#F24100');
                $('#sort-least-summits').css('font-weight', '500');
                window.location.hash = 'order=least_summits&page='+page;
                $('#ranked-by-title').html('Least summits');
                break;
            default:
                $('#sort-elevation').css('color', '#F24100');
                $('#sort-elevation').css('font-weight', '500');
                $('#sort-prominence').css('color', '#999');
                $('#sort-prominence').css('font-weight', '300');
                $('#sort-name').css('color', '#999');
                $('#sort-name').css('font-weight', '300');
                $('#sort-most-summits').css('color', '#999');
                $('#sort-most-summits').css('font-weight', '300');
                $('#sort-least-summits').css('color', '#999');
                $('#sort-least-summits').css('font-weight', '300');
                window.location.hash = 'order=elevation&page='+page;
                $('#ranked-by-title').html('Elevation');
        }

        peakSort = sort;
        $('#pagination').css('display','none');
        window.scrollTo(0, 0);

        $('#peaks-list').empty();
        $('#ajax-data-loading').css('display', 'inline');
        var peakCount = '{{ peak_count }}'.replace(',', '');
        var totalPages = Math.floor(parseInt(peakCount)/40);
        $.getJSON('{% url "peaks_list" %}?continent={{ continent_code }}&sort='+sort+'&page='+page , function(data) {
            $.each( data, function( key, val ) {
                if (key=='peaks') {
                    $('#ajax-data-loading').css('display', 'none');
                    $.each( val, function( peakkey, peakval ) {

                        //build country string
                        var country = '';
                        $.each( peakval.country, function( countrykey, countryval ) {
                            country = country + countryval.country_name + ' / ';
                        });
                        country = country.substr(0, country.length-3);
                        if (country == '') {
                            country = 'Unknown Country';
                        }

                        //build region string
                        var region = '';
                        var mobile_region = '';
                        var region_bull_class = 'hidden-lg hidden-md hidden-sm hidden-xs';
                        $.each( peakval.region, function( regionkey, regionval ) {
                            region_bull_class = '';
                            region = region + regionval.region_name + ', ' + regionval.country_name + ' / ';
                            if (regionval.country_name == '{{ country.name }}' && mobile_region == '') {
                                mobile_region = regionval.region_name;
                            } else if (mobile_region == '') {
                                mobile_region = regionval.country_name;
                            }
                        });
                        region = region.substr(0, region.length-3);
                        if (region == '') {
                            region = country;
                            mobile_region = country;
                            region_bull_class = '';
                        }

                        var highlights = '';
                        var peak_title_margin = 'peak-list-peak-title-margin';
                        var peak_subtitle_margin = 'peak-list-peak-subtitle-margin';
                        var peak_highlights_display = 'display: none;';
                        if (peakval.peak_highlights != undefined) {
                            peak_title_margin = '';
                            peak_subtitle_margin = '';
                            peak_highlights_display = 'max-height: 125px; overflow: hidden;';
                            highlights = '<div style="margin-left: 6px; color: #666;"><div class="peak-list-highlights-li">&bull; ' + peakval.peak_highlights.replace(/\~~/g, '</div><div class="peak-list-highlights-li">&bull; ');
                            //highlights = highlights.substr(0, highlights.length-4) + '</div>';
                            highlights = highlights + '</div>';
                        }

                        if (isNaN(peakval.prominence)) {
                            $('#peaks-list').append('<div class="col-md-12"><div class="row hover-row" style="cursor: pointer; background-color:#ffffff; border-bottom: solid 1px #e0e0e0;" onclick="openUrl(\'/' + peakval.slug + '\');"><div class="col-lg-3 col-md-3 col-sm-3 col-xs-3" style="padding-right: 0px; padding-left: 0px; margin:4px; border-radius:10px; background-image: url(\'{{ MEDIA_URL }}' + peakval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat;"><div><a href="/' + peakval.slug + '/"><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography leftpeak-responsive"></a></div></div><div class="col-lg-9 col-md-9 col-sm-9 col-xs-9 map-card-web-1 map-card-tablet-3 card-mobile-aspect-ratio-12-3" style="width: calc(75% - 8px) !important;"><div class="leftpeak-responsive" style="position: absolute; top:35%; margin-top:4px; margin-bottom: 4px;"><div class="peak-list-peak-title ' + peak_title_margin + '"><a style="color: #333;" href="/' + peakval.slug + '/">' + peakval.name + '</a></div><div class="peak-list-peak-subtitle ' + peak_subtitle_margin + '"><span class="peak-elevation">' + numberWithCommas(Math.floor(peakval.elevation)) + ' ft / ' + numberWithCommas(Math.floor(peakval.elevation * .3048)) + ' m<span class="hidden-xs"> elevation</span></span><span class="hidden-md hidden-sm hidden-xs"> &bull; </span><span class="peak-prominence hidden-md hidden-sm hidden-xs">Unknown<span class="hidden-xs"> prominence</span></span><span class="hidden-xs"> &bull; </span><span class="peak-summits hidden-xs">' + peakval.summit_count + ' climb' + ((peakval.summit_count != 1) ? 's' : '') + '</span><span class="' + region_bull_class + '"> &bull; </span><span class="peak-region hidden-xs">' + region + '</span><span class="peak-region-mobile hidden-lg hidden-md hidden-sm">' + mobile_region + '</span></div><div class="peak-list-highlights hidden-xs" style="' + peak_highlights_display + '">' + highlights + '</div></div></div></div></div>');
                        } else {
                            $('#peaks-list').append('<div class="col-md-12"><div class="row hover-row" style="cursor: pointer; background-color:#ffffff; border-bottom: solid 1px #e0e0e0;" onclick="openUrl(\'/' + peakval.slug + '\');" ><div class="col-lg-3 col-md-3 col-sm-3 col-xs-3" style="padding-right: 0px; padding-left: 0px; margin:4px; border-radius:10px; background-image: url(\'{{ MEDIA_URL }}' + peakval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat;"><div><a href="/' + peakval.slug + '/"><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography leftpeak-responsive"></a></div></div><div class="col-lg-9 col-md-9 col-sm-9 col-xs-9 map-card-web-1 map-card-tablet-3 card-mobile-aspect-ratio-12-3" style="width: calc(75% - 8px) !important; margin-top:4px; margin-bottom: 4px;"><div class="leftpeak-responsive" style="position: absolute; top:50%; left:51.5%; transform: translate(-50%,-50%); width:100%;"><div class="peak-list-peak-title ' + peak_title_margin + '"><a style="color: #333;" href="/' + peakval.slug + '/">' + peakval.name + '</a></div><div class="peak-list-peak-subtitle ' + peak_subtitle_margin + '"><span class="peak-elevation">' + numberWithCommas(Math.floor(peakval.elevation)) + ' ft / ' + numberWithCommas(Math.floor(peakval.elevation * .3048)) + ' m<span class="hidden-xs"> elevation</span></span><span class="hidden-md hidden-sm hidden-xs"> &bull; </span><span class="peak-prominence hidden-md hidden-sm hidden-xs">' + numberWithCommas(Math.floor(peakval.prominence)) + ' ft / ' + numberWithCommas(Math.floor(peakval.prominence * .3048)) + ' m<span class="hidden-xs"> prominence</span></span><span class="hidden-xs"> &bull; </span><span class="peak-summits hidden-xs">' + peakval.summit_count + ' climb' + ((peakval.summit_count != 1) ? 's' : '') + '</span><span class="' + region_bull_class + '"> &bull; </span><span class="peak-region hidden-xs">' + region + '</span><span class="peak-region-mobile hidden-lg hidden-md hidden-sm">' + mobile_region + '</span></div><div class="peak-list-highlights hidden-xs" style="' + peak_highlights_display + '">' + highlights + '</div></div></div></div></div>');
                        }
                    });
                    //show pagination
                    var paginationHtml = '';
                    if (parseInt(page) == totalPages && totalPages > 1) {
                        paginationHtml = '<div style="display: inline-block; height: 55px;"><a onclick="loadPeaks(\''+peakSort+'\', ' + (parseInt(page)-1).toString() + ');"><img src="{% static 'img/font-awesome_4-7-0_arrow-circle-left_256_0_00b1f2_none.png' %}" style="height: 26px; margin-bottom: 5px;"></a></div><div class="current" style="margin-left: 20px; margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">peaks ' + ((parseInt(page)-1)*40+1).toString() + ' - {{ peak_count|intcomma }} of {{ peak_count|intcomma }}</div>';
                    } else if (parseInt(page) > 1) {
                        paginationHtml = '<div style="display: inline-block; height: 55px;"><a onclick="loadPeaks(\''+peakSort+'\', ' + (parseInt(page)-1).toString() + ');"><img src="{% static 'img/font-awesome_4-7-0_arrow-circle-left_256_0_00b1f2_none.png' %}" style="height: 26px; margin-bottom: 5px;"></a></div><div class="current" style="margin-left: 20px; margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">peaks ' + ((parseInt(page)-1)*40+1).toString() + ' - ' + ((parseInt(page))*40).toString() + ' of {{ peak_count|intcomma }}</div>';
                    } else if (totalPages > 1) {
                        paginationHtml = '<div class="current" style="margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">peaks 1 - 40 of {{ peak_count|intcomma }}</div>';
                    } else {
                        paginationHtml = '<div class="current" style="margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">peaks 1 - {{ peak_count|intcomma }} of {{ peak_count|intcomma }}</div>';
                    }
                    if (parseInt(page) < totalPages) {
                        paginationHtml = paginationHtml + '<div style="display: inline-block; height: 55px;"><a onclick="loadPeaks(\''+peakSort+'\', ' + (parseInt(page)+1).toString() + ');"><img src="{% static 'img/font-awesome_4-7-0_arrow-circle-right_256_0_00b1f2_none.png'%}" style="height: 26px; margin-bottom: 5px;"></i></a></div>';
                        $('#pagination-pages').html(paginationHtml);
                        $('#pagination').css('display', 'inherit');
                    } else {
                        $('#pagination-pages').html(paginationHtml);
                        $('#pagination').css('display', 'inherit');
                    }
                }
            });
            $('.peak-list-highlights-li').trunk8({lines: 5, tooltip: false});
            switch(sort) {
                case 'elevation':
                    //set stat colors
                    $('.peak-elevation').css('color', '#F24100');
                    $('.peak-elevation').removeClass('hidden-xs');
                    $('.peak-elevation').removeClass('hidden-sm');
                    $('.peak-elevation').removeClass('hidden-md');
                    $('.peak-prominence').css('color', '#aaa');
                    $('.peak-prominence').addClass('hidden-xs');
                    $('.peak-prominence').addClass('hidden-sm');
                    $('.peak-prominence').addClass('hidden-md');
                    $('.peak-summits').css('color', '#aaa');
                    $('.peak-summits').addClass('hidden-xs');
                    break;
                case 'prominence':
                    //set stat colors
                    $('.peak-elevation').css('color', '#aaa');
                    $('.peak-elevation').addClass('hidden-xs');
                    $('.peak-elevation').addClass('hidden-sm');
                    $('.peak-elevation').addClass('hidden-md');
                    $('.peak-prominence').css('color', '#F24100');
                    $('.peak-prominence').removeClass('hidden-xs');
                    $('.peak-prominence').removeClass('hidden-sm');
                    $('.peak-prominence').removeClass('hidden-md');
                    $('.peak-summits').css('color', '#aaa');
                    $('.peak-summits').addClass('hidden-xs');
                    break;
                case 'name':
                    //set stat colors
                    $('.peak-elevation').css('color', '#aaa');
                    $('.peak-elevation').removeClass('hidden-xs');
                    $('.peak-elevation').removeClass('hidden-sm');
                    $('.peak-elevation').removeClass('hidden-md');
                    $('.peak-prominence').css('color', '#aaa');
                    $('.peak-prominence').addClass('hidden-xs');
                    $('.peak-prominence').addClass('hidden-sm');
                    $('.peak-prominence').addClass('hidden-md');
                    $('.peak-summits').css('color', '#aaa');
                    $('.peak-summits').addClass('hidden-xs');
                    break;
                case 'most_summits':
                    //set stat colors
                    $('.peak-elevation').css('color', '#aaa');
                    $('.peak-elevation').addClass('hidden-xs');
                    $('.peak-elevation').removeClass('hidden-sm');
                    $('.peak-elevation').removeClass('hidden-md');
                    $('.peak-prominence').css('color', '#aaa');
                    $('.peak-prominence').addClass('hidden-xs');
                    $('.peak-prominence').addClass('hidden-sm');
                    $('.peak-prominence').addClass('hidden-md');
                    $('.peak-summits').css('color', '#F24100');
                    $('.peak-summits').removeClass('hidden-xs');
                    break;
                case 'least_summits':
                    //set stat colors
                    $('.peak-elevation').css('color', '#aaa');
                    $('.peak-elevation').addClass('hidden-xs');
                    $('.peak-elevation').removeClass('hidden-sm');
                    $('.peak-elevation').removeClass('hidden-md');
                    $('.peak-prominence').css('color', '#aaa');
                    $('.peak-prominence').addClass('hidden-xs');
                    $('.peak-prominence').addClass('hidden-sm');
                    $('.peak-prominence').addClass('hidden-md');
                    $('.peak-summits').css('color', '#F24100');
                    $('.peak-summits').removeClass('hidden-xs');
                    break;
                default:
                    //set stat colors
                    $('.peak-elevation').css('color', '#F24100');
                    $('.peak-elevation').removeClass('hidden-xs');
                    $('.peak-elevation').removeClass('hidden-sm');
                    $('.peak-elevation').removeClass('hidden-md');
                    $('.peak-prominence').css('color', '#aaa');
                    $('.peak-prominence').addClass('hidden-xs');
                    $('.peak-prominence').addClass('hidden-sm');
                    $('.peak-prominence').addClass('hidden-md');
                    $('.peak-summits').css('color', '#aaa');
                    $('.peak-summits').addClass('hidden-xs');
            }
        });

    }

    function numberWithCommas(x) {
        return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    function openUrl(url) {
        window.location.href = url;
    }

    $(document).ready(function() {

        var viewport_height = $(window).height();
        var viewport_width = $(window).width();
        var footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
        var data_loading_margin = (viewport_height - footer_height) / 3;
        $('.data-loading').css('margin-top', data_loading_margin);
        $(window).resize(function() {
            viewport_height = $(window).height();
            viewport_width = $(window).width();
            footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
            data_loading_margin = (viewport_height - footer_height) / 3;
            $('.data-loading').css('margin-top', data_loading_margin);
        });

        var vars = [], hash, sort, page;
        var q = document.URL.split('#')[1];
        if(q != undefined){
            q = q.split('&');
            for(var i = 0; i < q.length; i++){
                hash = q[i].split('=');
                vars.push(hash[1]);
                vars[hash[0]] = hash[1];
            }
        }

        if (vars['order'] != undefined) {
            sort = vars['order'];
        } else {
            sort = 'elevation';
        }

        if (vars['page'] != undefined) {
            page = vars['page'];
        } else {
            page = '1';
        }

        loadPeaks(sort, page);

        $(window).resize(function (event) {
          $('.peak-list-highlights').trunk8({lines: 5});
        });

        $('#ranked-by-button-div').click(function(e){
            $('.ranked-by-list').toggle();
        });

    });

</script>

{% endblock %}
