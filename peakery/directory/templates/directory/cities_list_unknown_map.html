{% extends "base_no_footer.html" %}
{% load static %}
{% block jquery_import %}
    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.7.0/jquery.min.js"></script>
{% endblock %}

{% block jquery_form_import %}
    <script type="text/javascript" src="{{MEDIA_URL}}js/jquery.form.2.92.js"></script>
{% endblock %}

{% block title %}{{ country.name }} peak map{% endblock %}
{% block titlemeta %}{{ country.name }} peak map{% endblock %}
{% block description %}{{ meta_description }}{% endblock %}
{% block image_rel %}{% endblock %}

{% block css %}
    <link href="{{ MEDIA_URL }}css/explore.css" rel="stylesheet" type="text/css" media="screen" />
{% endblock %}

{% block extrajs %}
    <style>
        .ui-autocomplete {max-height: 200px; overflow-y: auto; overflow-x: hidden; padding-right: 20px;}
        * html .ui-autocomplete {height: 200px;}
        .ui-autocomplete-loading { background: white url('{{ MEDIA_URL }}img/misc/ajax1.gif') right center no-repeat; }
    </style>

{% endblock %}

{% block mapbox %}
{% include "mapbox/mapbox.html" %}
{% endblock %}

{% block bodyclass %}wrapper_set1 explore{% endblock %}
{% block explore_active %}{% if not showing_user_peaks %}active{% endif %}{% endblock %}
{% block map_active %}{% if not showing_user_peaks %}active{% endif %}{% endblock %}
{% block explored_class %}{% if not showing_user_peaks %}explored{% endif %}{% endblock %}
{% block explore_link %}javascript:void(0){% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row hidden-xs" style="position: absolute; top: 50px; left: 15px; width: 100%;">
        <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 65px; border-radius: 12px 12px 0 0;"><span style="font-size: 24px; font-weight: 600;">
            <div id="breadcrumbs">
                <ul>
                    <li class="hidden-sm hidden-xs"><a style="font-weight: 500;" href="/{{ country.slug }}-mountains/">{{ country.name }}</a> &raquo; </li>
                    <li>Unknown Region</li>
                </ul>
                <div class="pull-right hidden-xs" style="margin-top: -5px;">
                    <a style="{{ subnav_info_style }}" class="region-header-sub-links" href="/region/{{ country.slug }}-mountains/">Info</a><a style="{{ subnav_map_style }}" class="region-header-sub-links" href="/{{ country.slug }}-mountains/unknown-region/map/">Map</a><a style="{{ subnav_peaks_style }}" class="region-header-sub-links" href="/{{ country.slug }}-mountains/unknown-region/peaks/">Peaks</a><a style="{{ subnav_summits_style }}" class="region-header-sub-links" href="/region/{{ country.slug }}-mountains/summits/">Summits</a><a style="{{ subnav_challenges_style }}" class="region-header-sub-links" href="/region/{{ country.slug }}-mountains/challenges/">Challenges</a>
                </div>
            </div>
        </span></div>
    </div>
{% endblock %}

{% block content %}

<style>

   body.explore div#main {
        margin-top: -2px;
    }
    #content-holder {
        background-image: none;
    }
    .gm-style-mtc {
        opacity: .8;
    }
    @media screen and (max-width: 767px) and (min-width: 1px) {
        #peaks-map {
            top: 99px;
        }
    }
   @media screen and (max-width: 1023px) and (min-width: 768px) {
        #peaks-map {
            top: 49px;
        }
    }
   @media screen and (max-width: 767px) and (min-width: 1px) {
       #content-body {
           margin-top: 2px;
       }
       #mobile-peak-search {
           top: 50px;
       }
   }
    @media screen and (min-width: 768px) {
       #mobile-peak-search {
           top: 0px;
       }
        #peak-search {
            top: 14px;
        }
   }

    @media screen and (min-width: 1024px) {
        div#explore .leftCol {
            margin-top: -14px;
        }

        #peaks-map {
            top: 0px;
        }

        #collapse-search {
            top: 0px;
        }

        #gm-custom-mapunits {
            right: 142px;
        }
    }

    .map-tooltip-info {
        background-color: rgba(0,0,0,.6);
    }

</style>

    <div id="explore" class="peak_list_cont clearfix">
        <div id="mobile-collapse-nav" style="display: none; position: absolute; left: 0px; top: -35px; z-index: 9999;"></div>
        <div class="full-width-container" style="width: 100%; height: inherit;">
        <div class="row peak-list-content" style="min-height: 100px;">
        <!-- Mobile header -->
        <div class="row sub-header-row hidden-lg hidden-md hidden-sm" style="height: 50px; position: fixed; width: 100%; z-index: 2; margin-left: 0px;">
            <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999; display: flex; justify-content: space-between;">
                <a style="{{ subnav_info_style }} margin-left: 0px;" class="mobile-header-sub-links" href="/{{ region.country.slug }}-mountains/{{ region.slug }}/">Info</a><a style="{{ subnav_map_style }}" class="mobile-header-sub-links" href="/{{ region.country.slug }}-mountains/{{ region.slug }}/map/">Map</a><a style="{{ subnav_peaks_style }}" class="mobile-header-sub-links" href="/{{ region.country.slug }}-mountains/{{ region.slug }}/peaks/">Peaks</a><a style="{{ subnav_summits_style }}" class="mobile-header-sub-links" href="/{{ region.country.slug }}-mountains/{{ region.slug }}/summits/">Summits</a><a style="{{ subnav_challenges_style }}" class="mobile-header-sub-links" href="/{{ region.country.slug }}-mountains/{{ region.slug }}/challenges/">Challenges</a>
            </div>
        </div>
        <!-- End mobile header -->
        <table class="hidden-lg hidden-md" id="mobile-peak-search" style="width: 100%; position: relative;">
            <tr style="background-color: #f1f1f1;">
                <td id="mobile-search-title" style="padding-left: 80px; height: 49px; width: 80%; vertical-align: middle; text-align: center; font-size: 12px;">{% if q and n %}"{{ q }}" near {{ n }}{% elif q %}"{{ q }}"{% elif n %}Peaks near {{ n }}{% else %}All peaks{% endif %}</td>
                <td style="height: 49px; vertical-align: middle; font-size: 20px; text-align: right; padding-right: 15px;"><a id="peak-search-mobile" style="cursor: pointer;"><i class="fa fa-filter"></i></a></td>
            </tr>
        </table>
        <div id="collapse-search" style="position: absolute; left: 240px; height: 67px; width: 25px; background-color: #EC8157; z-index: 100; text-align: center; padding-top: 25px; cursor: pointer; color: #fff;" onclick="collapseSearch();"><i class="fa fa-chevron-left"></i></div>
        <div id="peak-search" tabindex="-1" class="hidden-xs hidden-sm col_search leftCol" style="position: absolute; width: 240px; height: 100%; z-index: 999">
            <div class="wrp clearfix">
                <div class="block">

                    {% if showing_user_peaks %}
                        <h3 class="blue" style="font-size: 25px; margin: 0px;padding: 10px 10px 0px 10px;text-align: center; height:20px;">
                            <a style="text-decoration: none;" href="{{ userinfo.get_profile.get_absolute_url }}">{{ userinfo.username }}'s</a></h3>
                        <h4 style="text-align: center;margin-top: 5px;">summited peaks</h4>
                    {% endif %}
                    {% if showing_list_peaks %}
                        <h3 class="blue" style="font-size: 18px; margin: 0px;padding: 10px 10px 10px 10px;text-align: center; height:20px;">
                            <a style="text-decoration: none;" href="{{ group.get_absolute_url }}">{{ group.name }}</a></h3>
                    {% endif %}
                    {% if showing_country_peaks %}
                        <h3 class="blue" style="font-size: 18px; margin: 0px;padding: 10px 10px 10px 10px;text-align: center; height:20px;">
                            <a style="text-decoration: none;" href="{{ country.get_absolute_url }}">{% if region_name %}{{region_name}}, {% endif %}{{ country.name }}</a></h3>
                    {% endif %}

                    <form id="explore" method="get" action="/peaks">

                        <meta charset="utf-8">

                        <style>
                            .elevation_slider {float: left; width: 100%; padding: 5%;}
                            #slider-range #prominence-slider-range #summits-slider-range #difficulty-slider-range .ui-widget-header {background: #1f60f6;}
                            #slider-range #prominence-slider-range #summits-slider-range #difficulty-slider-range .ui-slider-range {background: #1f60f6;}
                        </style>

                        <input type="text" id="hdnElevMin" hidden value="">
                        <input type="text" id="hdnElevMax" hidden value="">
                        <input type="text" id="hdnPromMin" hidden value="">
                        <input type="text" id="hdnPromMax" hidden value="">
                        <input type="text" id="hdnSummitsMin" hidden value="">
                        <input type="text" id="hdnSummitsMax" hidden value="">
                        <input type="text" id="hdnDifficultyMin" hidden value="">
                        <input type="text" id="hdnDifficultyMax" hidden value="">
                        <input type="text" id="hdnKeyword" hidden value="">
                        <input type="text" id="hdnNear" hidden value="">
                        <input type="text" id="hdnLat" hidden value="">
                        <input type="text" id="hdnLng" hidden value="">
                        <input type="text" id="hdnBounds" hidden value="">
                        <input type="text" id="hdnNearQuery" hidden value="false">

                        <div id="elevation-slider" class="elevation_slider">
                            <div style="font-size: 14px; margin-left: -8px;">
                                <div id="elevation-label" style="float: left; font-size: 12px; color: #aaa;">Elevation</div>
                                <div class="pull-right" style="margin-top: -6px; margin-right: 5px;">
                                    <input type="text" disabled value="0  to  29,500 ft" class="ammount" id="amount" style="padding-right: 0px; border:0; font-weight:300; width: 120px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                    <input type="text" disabled value="0  to  9,000 m" class="ammount" id="amount-meters" style="display: none; padding-right: 5px; border:0; font-weight:300; width: 120px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                    <a href="#" style="margin-left: 5px; text-decoration: underline; font-size: 12px; float: right; height: 35px; line-height: 3;" id ='bt_showinmeters'>[m]</a>
                                </div>
                            </div>
                            <div id="slider-range" style="margin-left: -9px; margin-top: 5px; width: 218px; float: left;"></div>
                        </div>

                        <div id="prominence-slider" class="elevation_slider">
                            <div style="font-size: 14px; margin-left: -8px;">
                                <div id="prominence-label" style="float: left; font-size: 12px; color: #aaa;">Prominence</div>
                                <div class="pull-right" style="margin-top: -6px; margin-right: 5px;">
                                    <input type="text" disabled value="0  to  29,500 ft" class="prominence-ammount" id="prominence-amount" style="padding-right: 0px; border:0; font-weight:300; width: 120px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                    <input type="text" disabled value="0  to  9,000 m" class="prominence-ammount" id="prominence-amount-meters" style="display: none; padding-right: 5px; border:0; font-weight:300; width: 120px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                    <a href="#" style="margin-left: 5px; text-decoration: underline; font-size: 12px; float: right; height: 35px; line-height: 3;" id ='prominence_bt_showinmeters'>[m]</a></div>
                            </div>
                            <div id="prominence-slider-range" style="margin-left: -9px; margin-top: 5px; width: 218px; float: left;"></div>
                        </div>

                        <div id="summits-slider" class="elevation_slider">
                            <div style="font-size: 14px; margin-left: -8px;">
                                <div id="summits-label" style="float: left; font-size: 12px; color: #aaa;">Summits</div>
                                <div class="pull-right" style="margin-top: -6px; margin-right: 5px;">
                                    <input type="text" disabled value="0  to  500+" class="summits-ammount" id="summits-amount" style="padding-right: 5px; border:0; font-weight:300; width: 120px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                </div>
                            </div>
                            <div id="summits-slider-range" style="margin-left: -9px; margin-top: 5px; width: 218px; float: left;"></div>
                        </div>

                        <div id="difficulty-slider" class="elevation_slider">
                            <div style="font-size: 14px; margin-left: -8px;">
                                <div id="difficulty-label" style="float: left; font-size: 12px; color: #aaa;">Difficulty</div>
                                <div class="pull-right" style="margin-top: -6px; margin-right: 5px;">
                                    <input type="text" disabled value="Class 1  to  5" class="difficulty-ammount" id="difficulty-amount" style="padding-right: 5px; border:0; font-weight:300; width: 120px; text-align: right; background: none; font-size: 11px; color: #aaa; -webkit-text-fill-color: #aaa; opacity: 1;" />
                                </div>
                            </div>
                            <div id="difficulty-slider-range" style="margin-left: -9px; margin-top: 5px; width: 218px; float: left;"></div>
                        </div>

                        <div class="row">
                          <div class="col-md-12" style="text-align: center; color: #c0c0c0; margin-top: 20px; margin-bottom: 20px;">
                            <div style="float: left; margin: 5px; padding-left: 2px;"><img style="width: 20px;" src="{% static 'img/<EMAIL>' %}"></div>
                            <div style="float: left; font-size: 10px; padding-top: 7px; margin-right: 3px;">Highest</div>
                            <div style="float: left; margin: 5px;"><img style="width: 20px;" src="{% static 'img/<EMAIL>' %}"></div>
                            <div style="float: left; font-size: 10px; width: 48px; text-align: left; line-height: 12px; padding-top: 5px;">Your summits</div>
                            <div style="float: left; margin: 5px;"><img style="width: 20px;" src="{% static 'img/<EMAIL>' %}"></div>
                            <div style="float: left; font-size: 10px; width: 48px; text-align: left; line-height: 12px; padding-top: 5px;">Your attempts</div>
                          </div>
                        </div>

                        <div id="addMissingPeak">
                            <h3 class="gray" style="color: #aaa;">Can't find a peak?</h3>
                            {% if user.is_authenticated %}
                            <a href="{% url "item_add" %}" rel="facebox" style="">Add it!</a>
                            {% else %}
                            <a style="cursor: pointer;" class="join-peakery">Add it!</a>
                            {% endif %}
                        </div>

                        <div class="row" id="ajax-data-loading" style="display: none;">
                          <div class="col-md-12" style="text-align: center; color: #c0c0c0; margin-top: 20px; margin-bottom: 20px;">
                            <i class="fa fa-spinner fa-spin fa-5x"></i>
                          </div>
                        </div>

                    </form>

                </div><!-- END block -->
            </div>
        </div><!-- END col_1 -->
        <div id="peaks-map" class="col_data rightCol">
            <div id="map-canvas" style="width: 100%; height: 100%;">
                <div id="gm-custom-mapunits" class="gmnoprint gm-style-mtc" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 100px; top: 0px;">
                    <div id="gm-custom-mapunitsbutton" draggable="false" title="Change map units" style="cursor: pointer; direction: ltr; overflow: hidden; text-align: center; position: relative; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 11px; -webkit-background-clip: padding-box; background-clip: padding-box; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; font-weight: 500;">
                        <span id="gm-custom-mapunitsbutton-label-feet" class="gm-custom-mapunits-selected">Feet</span> | <span id="gm-custom-mapunitsbutton-label-meters" class="gm-custom-mapunits-unselected">Meters</span>
                    </div>
                </div>
                <div id="gm-custom-maptype" class="gmnoprint gm-style-mtc" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 104px; right: 0px; top: 0px;">
                    <div id="gm-custom-mapbutton" draggable="false" title="Change map style" style="cursor: pointer; direction: ltr; overflow: hidden; text-align: left; position: relative; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 11px; -webkit-background-clip: padding-box; background-clip: padding-box; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; font-weight: 500;">
                        <span id="gm-custom-mapbutton-label">Terrain</span><img src="https://maps.gstatic.com/mapfiles/arrow-down.png" draggable="false" style="-webkit-user-select: none; border: 0px; padding: 0px; margin: -2px 0px 0px; position: absolute; right: 6px; top: 50%; width: 7px; height: 4px;">
                    </div>
                    <div id="gm-custom-mapdropdown" style="background-color: white; z-index: -1; padding-left: 2px; padding-right: 2px; border-bottom-left-radius: 2px; border-bottom-right-radius: 2px; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; position: absolute; top: 100%; left: 0px; right: 0px; text-align: left; display: none;">
                        <div id="gm-custom-mapoption-terrain" draggable="false" title="Show street map with terrain" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            Terrain
                        </div>
                        <div id="gm-custom-mapoption-natatl" draggable="false" title="Show natural atlas" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            Natural Atlas (US)
                        </div>
                        <div id="gm-custom-mapoption-outdoors" draggable="false" title="Show open topo map" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            OpenTopoMap
                        </div>
                        <div id="gm-custom-mapoption-topo" draggable="false" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            Topo Govt (as avail)
                        </div>
                        <div id="gm-custom-mapoption-satstreets" draggable="false" title="Show satellite imagery with street map" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            Satellite
                        </div>
                        <div id="gm-custom-mapoption-sat" draggable="false" title="Show satellite imagery" style="cursor: pointer; color: #333; font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 8px; font-weight: 500;">
                            Satellite Topo
                        </div>
                    </div>
                </div>

                <div id="message_map_div" class="gmnoprint gm-style-mtc" style="margin: 10px; z-index: 2; position: absolute; cursor: pointer; text-align: left; width: 104px; right: 0px; top: 0px; display: none;">
                    <div id="message_map" draggable="false" style="direction: ltr; overflow: hidden; text-align: left; position: relative; color: rgb(86, 86, 86); font-family: Roboto, Arial, sans-serif; -webkit-user-select: none; font-size: 11px; background-color: rgb(255, 255, 255); padding: 11px; border-bottom-left-radius: 2px; border-top-left-radius: 2px; border-bottom-right-radius: 2px; border-top-right-radius: 2px; -webkit-background-clip: padding-box; background-clip: padding-box; -webkit-box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; box-shadow: rgba(0, 0, 0, 0.298039) 0px 1px 4px -1px; font-weight: 500;">

                    </div>
                </div>

            </div>
            <div id="marker-tooltip" data-url="" data-index="" style="cursor: pointer; display: none; position:absolute; width: 200px; height: 150px; background: rgba(0, 0, 0, 0.3); margin: 15px;"></div>

        </div><!-- END col_2 -->
        </div>
        </div>
    </div><!-- END paek_list_cont -->

    <script type="text/javascript">

        var map;
        var topo;
        var outdoors;
        var pageX, pageY, mapX, mapY;
        var iconstyle;

        $(function(){
//            Top-left nav button ("find peaks") width to be it same at the width of the leftCol:
            var leftColWidth = $('div#explore .leftCol').width();
            $('li.headlink').css('width', leftColWidth);
//            Peak name input needs to show the remove text icon when the user enter text on it:
            var a = $('a#clear_peak_name');
            var a2 = $('a#clear_near_location');

            var input = $('input#q');
            var input2 = $('input#n');

            if ( input.val() != "" ) {
                a.css('display', 'block');
            }

            if ( input2.val() != "" ) {
                a2.css('display', 'block');
            }

            input.keyup(function(){
                if( $(this).val() != "" ) {
                    a.css('display', 'block');
                } else {
                    a.css('display', 'none');
                }
            });

            input2.keyup(function(){
                if( $(this).val() != "" ) {
                    a2.css('display', 'block');
                } else {
                    a2.css('display', 'none');
                }
            });

            a.click(function(){
                input.val('');
                $(this).css('display', 'none');
            });

            a2.click(function(){
                input2.val('');
                $(this).css('display', 'none');
            });

            $('#peak-search').css({left: '0px'});

        });

        var timers = {};

        function collapseSearch() {
            if ($('#peak-search').css('left') == '0px') {
                $('#peak-search').animate({left: '-240px'});
                $('#collapse-search').animate({left: '0px'});
                $('#collapse-search').html('<i class="fa fa-chevron-right"></i>');
                $('#peaks-map').animate({left: '0px'});
                var window_width = $(window).width();
                $("div#map-canvas").animate({width: window_width},{complete: function() {map.resize();}});
            } else {
                $('#peak-search').animate({left: '0px'});
                $('#collapse-search').animate({left: '240px'});
                $('#collapse-search').html('<i class="fa fa-chevron-left"></i>');
                $('#peaks-map').animate({left: '240px'});
                var window_width = $(window).width();
                window_width = window_width - 240;
                $("div#map-canvas").animate({width: window_width},{complete: function() {map.resize();}});
            }
        }

        function round(value, decimals) {
          return Number(Math.round(value+'e'+decimals)+'e-'+decimals);
        }

        function getRepString (rep) {
          rep = rep+''; // coerce to string
          if (rep < 1000) {
            return rep; // return the same number
          }
          // divide and format
          return (rep/1000).toFixed(rep % 1000 != 0)+'K';
        }

        function updateURLParameter(url, param, paramVal) {

            var newAdditionalURL = "";
            var tempArray = url.split("#");
            var baseURL = tempArray[0];
            var additionalURL = tempArray[1];
            var temp = "";

            if (additionalURL) {
                tempArray = additionalURL.split("&");
                for (i = 0; i < tempArray.length; i++) {
                    if (tempArray[i].split('=')[0] != param) {
                        newAdditionalURL += temp + tempArray[i];
                        temp = "&";
                    }
                }
            }

            if (paramVal === undefined) {
                return newAdditionalURL;
            }

            const rows_txt = temp + "" + param + "=" + paramVal;
            return newAdditionalURL + rows_txt;
        }
        function delayShowKeywordNear(target, keyword, near) {
            clearTimeout(timers[target]);
            timers[target] = setTimeout(function() {
                q = keyword;
                n = near;
                u = updateURLParameter(window.location.hash, 'q', q);
                u = updateURLParameter('#' + u, 'n', n);
                window.location.hash = u;
                loadPeaksFromHash();
            }, 500);
        }
        function delayShowDataelevation(target, values) {
            clearTimeout(timers[target]);
            timers[target] = setTimeout(function() {
                elev_min = ~~values[0];
                elev_max = ~~values[1];
                u = updateURLParameter(window.location.hash, 'elev_min', elev_min);
                u = updateURLParameter('#'+u, 'elev_max', elev_max);
                window.location.hash = u;
                loadPeaksFromHash();
            }, 500);
        }
        function delayShowDataprominence(target, values) {
            clearTimeout(timers[target]);
            timers[target] = setTimeout(function() {
                prominence_min = ~~values[0];
                prominence_max = ~~values[1];
                u = updateURLParameter(window.location.hash, 'prom_min', prominence_min);
                u = updateURLParameter('#'+u, 'prom_max', prominence_max);
                window.location.hash = u;
                loadPeaksFromHash();
            }, 500);
        }
        function delayShowDataSummits(target, values) {
            clearTimeout(timers[target]);
            timers[target] = setTimeout(function() {
                summits_min = ~~values[0];
                summits_max = ~~values[1];
                u = updateURLParameter(window.location.hash, 'summits_min', summits_min);
                u = updateURLParameter('#'+u, 'summits_max', summits_max);
                window.location.hash = u;
                loadPeaksFromHash();
            }, 500);
        }
        function delayShowDataDifficulty(target, values) {
            clearTimeout(timers[target]);
            timers[target] = setTimeout(function() {
                difficulty_min = ~~values[0];
                difficulty_max = ~~values[1];
                u = updateURLParameter(window.location.hash, 'difficulty_min', difficulty_min);
                u = updateURLParameter('#'+u, 'difficulty_max', difficulty_max);
                window.location.hash = u;
                loadPeaksFromHash();
            }, 500);
        }

        function numberWithCommas(x) {
            return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        }

        function resetSliders() {
            var elevationSlider = document.getElementById('slider-range');
            //if elevation in meters
            if ($('#bt_showinmeters').text() == '[ft]') {
                elevationSlider.noUiSlider.set([0, 9000]);
            } else {
                elevationSlider.noUiSlider.set([0, 29500]);
            }
            var prominenceSlider = document.getElementById('prominence-slider-range');
            //if prominence in meters
            if ($('#prominence_bt_showinmeters').text() == '[ft]') {
                prominenceSlider.noUiSlider.set([0, 9000]);
            } else {
                prominenceSlider.noUiSlider.set([0, 29500]);
            }
            var summitsSlider = document.getElementById('summits-slider-range');
            summitsSlider.noUiSlider.set([0, 500]);
            var difficultySlider = document.getElementById('difficulty-slider-range');
            difficultySlider.noUiSlider.set([1, 5]);
            $('#elevation-label').css('color','#aaa');
            $('#amount').css('color','#aaa');
            $('#amount').css('-webkit-text-fill-color','#aaa');
            $('#slider-range > div > div').css('background-color','#00b2f2');
            $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-blue.png)');
            $('#prominence-label').css('color','#aaa');
            $('#prominence-amount').css('color','#aaa');
            $('#prominence-amount').css('-webkit-text-fill-color','#aaa');
            $('#prominence-slider-range > div > div').css('background-color','#00b2f2');
            $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-blue.png)');
            $('#summits-label').css('color','#aaa');
            $('#summits-amount').css('color','#aaa');
            $('#summits-amount').css('-webkit-text-fill-color','#aaa');
            $('#summits-slider-range > div > div').css('background-color','#00b2f2');
            $('#summits-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-blue.png)');
            $('#difficulty-label').css('color','#aaa');
            $('#difficulty-amount').css('color','#aaa');
            $('#difficulty-amount').css('-webkit-text-fill-color','#aaa');
            $('#difficulty-slider-range > div > div').css('background-color','#00b2f2');
            $('#difficulty-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-blue.png)');

            $( "#amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29500) + " ft");
            $( "#amount-meters" ).val(numberWithCommas(Math.floor(0)) + "  to  " + numberWithCommas(Math.floor(9000)) + " m");
            $( "#prominence-amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29500) + " ft");
            $( "#prominence-amount-meters" ).val(numberWithCommas(Math.floor(0)) + "  to  " + numberWithCommas(Math.floor(9000)) + " m");
            $( "#summits-amount" ).val( "0  to  500+" );
            $( "#difficulty-amount" ).val( "Class 1 to 5" );

            window.location.hash = '';
        }

        function loadPeaksFromHash () {

            var vars = [], hash, keyword, near, wanted_elev_min, wanted_elev_max, wanted_prom_min, wanted_prom_max, wanted_summits_min, wanted_summits_max, wanted_difficulty_min, wanted_difficulty_max, lat, lng;
            var q = document.URL.split('#')[1];
            if(q != undefined){
                q = q.split('&');
                for(var i = 0; i < q.length; i++){
                    hash = q[i].split('=');
                    vars.push(hash[1]);
                    vars[hash[0]] = hash[1];
                }
            }

            if (vars['q'] != undefined) {
                keyword = vars['q'];
            } else {
                keyword = '';
            }

            if (vars['n'] != undefined) {
                near = vars['n'];
            } else {
                near = '';
            }

            if (vars['elev_min'] != undefined) {
                wanted_elev_min = vars['elev_min'];
            } else {
                wanted_elev_min = '0';
            }

            if (vars['elev_max'] != undefined) {
                wanted_elev_max = vars['elev_max'];
            } else {
                //if elevation in meters
                if ($('#bt_showinmeters').text() == '[ft]') {
                    wanted_elev_max = '29527';
                } else {
                    wanted_elev_max = '29500';
                }
            }

            if (vars['prom_min'] != undefined) {
                wanted_prom_min = vars['prom_min'];
            } else {
                wanted_prom_min = '0';
            }

            if (vars['prom_max'] != undefined) {
                wanted_prom_max = vars['prom_max'];
            } else {
                //if prominence in meters
                if ($('#prominence_bt_showinmeters').text() == '[ft]') {
                    wanted_prom_max = '29527';
                } else {
                    wanted_prom_max = '29500';
                }
            }

            if (vars['summits_min'] != undefined) {
                wanted_summits_min = vars['summits_min'];
            } else {
                wanted_summits_min = '0';
            }

            if (vars['summits_max'] != undefined) {
                wanted_summits_max = vars['summits_max'];
            } else {
                wanted_summits_max = '500';
            }

            if (vars['difficulty_min'] != undefined) {
                wanted_difficulty_min = vars['difficulty_min'];
            } else {
                wanted_difficulty_min = '1';
            }

            if (vars['difficulty_max'] != undefined) {
                wanted_difficulty_max = vars['difficulty_max'];
            } else {
                wanted_difficulty_max = '5';
            }

            if (vars['lat'] != undefined) {
                lat = vars['lat'];
            } else {
                lat = '';
            }

            if (vars['lng'] != undefined) {
                lng = vars['lng'];
            } else {
                lng = '';
            }

            loadPeaks(keyword, near, wanted_elev_min, wanted_elev_max, wanted_prom_min, wanted_prom_max, wanted_summits_min, wanted_summits_max, wanted_difficulty_min, wanted_difficulty_max, lat, lng);

        }

        function loadPeaks(keyword, near, wanted_elev_min, wanted_elev_max, wanted_prom_min, wanted_prom_max, wanted_summits_min, wanted_summits_max, wanted_difficulty_min, wanted_difficulty_max, lat, lng) {

            //hide search modal
            //$('#peak-search').modal('hide');

            //set hash for peaks page link
            //$('#navbar-link-peaks a').attr('href','/peaks/'+window.location.hash);

            //get map bounds
            var bounds = map.getBounds();

            //if elevation in meters
            if ($('#bt_showinmeters').text() == '[ft]') {
                if (wanted_elev_min != 0 || wanted_elev_max != 29527) {
                    $('#elevation-label').css('color', '#f24100');
                    $('#amount').css('color', '#f24100');
                    $('#amount').css('-webkit-text-fill-color', '#f24100');
                    $('#slider-range > div > div').css('background-color', '#f24100');
                    $('#slider-range .noUi-background').css('background-color', '#CCC');
                    $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                }
            } else {
                if (wanted_elev_min != 0 || wanted_elev_max != 29500) {
                    $('#elevation-label').css('color', '#f24100');
                    $('#amount').css('color', '#f24100');
                    $('#amount').css('-webkit-text-fill-color', '#f24100');
                    $('#slider-range > div > div').css('background-color', '#f24100');
                    $('#slider-range .noUi-background').css('background-color', '#CCC');
                    $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                }
            }

            //if prominence in meters
            if ($('#prominence_bt_showinmeters').text() == '[ft]') {
                if (wanted_prom_min != 0 || wanted_prom_max != 29527) {
                    $('#prominence-label').css('color', '#f24100');
                    $('#prominence-amount').css('color', '#f24100');
                    $('#prominence-amount').css('-webkit-text-fill-color', '#f24100');
                    $('#prominence-slider-range > div > div').css('background-color', '#f24100');
                    $('#prominence-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                }
            } else {
                if (wanted_prom_min != 0 || wanted_prom_max != 29500) {
                    $('#prominence-label').css('color', '#f24100');
                    $('#prominence-amount').css('color', '#f24100');
                    $('#prominence-amount').css('-webkit-text-fill-color', '#f24100');
                    $('#prominence-slider-range > div > div').css('background-color', '#f24100');
                    $('#prominence-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                }
            }

            if (wanted_summits_min != 0 || wanted_summits_max != 500) {
                $('#summits-label').css('color','#f24100');
                $('#summits-amount').css('color','#f24100');
                $('#summits-amount').css('-webkit-text-fill-color', '#f24100');
                $('#summits-slider-range > div > div').css('background-color','#f24100');
                $('#summits-slider-range .noUi-background').css('background-color', '#CCC');
                $('#summits-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
            }

            if (wanted_difficulty_min != 1 || wanted_difficulty_max != 5) {
                $('#difficulty-label').css('color','#f24100');
                $('#difficulty-amount').css('color','#f24100');
                $('#difficulty-amount').css('-webkit-text-fill-color', '#f24100');
                $('#difficulty-slider-range > div > div').css('background-color','#f24100');
                $('#difficulty-slider-range .noUi-background').css('background-color', '#CCC');
                $('#difficulty-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
            }

            //if length in meters
            if ($('#length_bt_showinmeters').text() == '[ft]') {
                if (wanted_length_min != 0 || wanted_length_max != 20) {
                    $('#length-label').css('color','#f24100');
                    $('#length-amount').css('color','#f24100');
                    $('#length-amount').css('-webkit-text-fill-color', '#f24100');
                    $('#length-slider-range > div > div').css('background-color','#f24100');
                    $('#length-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#length-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                }
            } else {
                if (wanted_length_min != 0 || wanted_length_max != 20) {
                    $('#length-label').css('color','#f24100');
                    $('#length-amount').css('color','#f24100');
                    $('#length-amount').css('-webkit-text-fill-color', '#f24100');
                    $('#length-slider-range > div > div').css('background-color','#f24100');
                    $('#length-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#length-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                }
            }

            //if vertical in meters
            if ($('#vertical_bt_showinmeters').text() == '[ft]') {
                if (wanted_vertical_min != 0 || wanted_vertical_max != 9842) {
                    $('#vertical-label').css('color','#f24100');
                    $('#vertical-amount').css('color','#f24100');
                    $('#vertical-amount').css('-webkit-text-fill-color', '#f24100');
                    $('#vertical-slider-range > div > div').css('background-color','#f24100');
                    $('#vertical-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#vertical-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                }
            } else {
                if (wanted_vertical_min != 0 || wanted_vertical_max != 10000) {
                    $('#vertical-label').css('color','#f24100');
                    $('#vertical-amount').css('color','#f24100');
                    $('#vertical-amount').css('-webkit-text-fill-color', '#f24100');
                    $('#vertical-slider-range > div > div').css('background-color','#f24100');
                    $('#vertical-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#vertical-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                }
            }

            if (wanted_last_climbed_min != 0 || wanted_last_climbed_max != 11) {
                $('#last-climbed-label').css('color','#f24100');
                $('#last-climbed-amount').css('color','#f24100');
                $('#last-climbed-amount').css('-webkit-text-fill-color', '#f24100');
                $('#last-climbed-slider-range > div > div').css('background-color','#f24100');
                $('#last-climbed-slider-range .noUi-background').css('background-color', '#CCC');
                $('#last-climbed-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
            }

            var counter = 0;
            var strTemp = '';

            $('#ajax-data-loading').css('display', 'inline');
            $('#peak-search-mobile').html('<i class="fa fa-spinner fa-spin"></i>');
            var params = '&country_id={{ country.id }}&region_id=-1';
            if (keyword.length > 0) params = params + '&q=' + keyword;
            if (near.length > 0) params = params + '&n=' + near;
            if (wanted_elev_min.length > 0) params = params + '&elev_min=' + wanted_elev_min;
            if (wanted_elev_max.length > 0) params = params + '&elev_max=' + wanted_elev_max;
            if (wanted_prom_min.length > 0) params = params + '&prom_min=' + wanted_prom_min;
            if (wanted_prom_max.length > 0) params = params + '&prom_max=' + wanted_prom_max;
            if (wanted_summits_min.length > 0) params = params + '&summits_min=' + wanted_summits_min;
            if (wanted_summits_max.length > 0) params = params + '&summits_max=' + wanted_summits_max;
            if (wanted_difficulty_min.length > 0) params = params + '&difficulty_min=' + wanted_difficulty_min;
            if (wanted_difficulty_max.length > 0) params = params + '&difficulty_max=' + wanted_difficulty_max;
            if (lat.length > 0) params = params + '&lat=' + lat;
            if (lng.length > 0) params = params + '&lng=' + lng;
            params = params + '&bounds=' + bounds.getSouthWest().lat + ',' + bounds.getSouthWest().lng + ',' + bounds.getNorthEast().lat + ',' + bounds.getNorthEast().lng + '&near=' + $('#hdnNearQuery').val();

            //set mobile title
            if (keyword.length > 0 && near.length > 0) {
                $('#mobile-search-title').html('"' + keyword + '" peaks near ' + near);
            } else if (keyword.length > 0) {
                $('#mobile-search-title').html('"' + keyword + '" peaks');
            } else if (near.length > 0) {
                $('#mobile-search-title').html('Peaks near ' + near);
            } else if (lat.length > 0 && lng.length > 0) {
                $('#mobile-search-title').html('Peaks near you');
            } else {
                $('#mobile-search-title').html('All peaks');
            }

            //update hidden parameters
            $('#hdnKeyword').val(keyword);
            if (keyword.length > 0) {
                $('#q').val(keyword);
                $('#clear_peak_name').html('<i class="fa fa-times"></i>');
                $('#clear_peak_name').show();
            }
            $('#hdnNear').val(near);
            if (near.length > 0) {
                $('#n').val(near);
                $('#clear_near_location').html('<i class="fa fa-times"></i>');
                $('#clear_near_location').show();
            }
            $('#hdnElevMin').val(wanted_elev_min);
            $('#hdnElevMax').val(wanted_elev_max);
            $('#hdnPromMin').val(wanted_prom_min);
            $('#hdnPromMax').val(wanted_prom_max);
            $('#hdnSummitsMin').val(wanted_summits_min);
            $('#hdnSummitsMax').val(wanted_summits_max);
            $('#hdnDifficultyMin').val(wanted_difficulty_min);
            $('#hdnDifficultyMax').val(wanted_difficulty_max);
            $('#hdnLat').val(lat);
            $('#hdnLng').val(lng);
            $('#hdnBounds').val(bounds.getSouthWest().lat + ',' + bounds.getSouthWest().lng + ',' + bounds.getNorthEast().lat + ',' + bounds.getNorthEast().lng);

            var mobileFilters = '';
            var LatLngList = [];

            if (params.length > 0) params = '?' + params.slice(-1*(params.length-1));
            var byRegion = false;
            var totalPeaks = 0;
            $.getJSON('{% url "peaks_map" %}' + params , function(data) {
                $.each( data, function( key, val ) {
                    var currentRequest = true;
                    if (key=='parameters') {
                        //reset near query boolean
                        $('#hdnNearQuery').val('false');
                        $.each( val, function( parameterkey, parameterval ) {
                            if (parameterval.keyword.toLowerCase() != $('#hdnKeyword').val().toLowerCase()) currentRequest = false;
                            if (parameterval.near.toLowerCase() != $('#hdnNear').val().toLowerCase()) currentRequest = false;
                            if (parameterval.elev_min != $('#hdnElevMin').val()) currentRequest = false;
                            if (parameterval.elev_max != $('#hdnElevMax').val()) currentRequest = false;
                            if (parameterval.prom_min != $('#hdnPromMin').val()) currentRequest = false;
                            if (parameterval.prom_max != $('#hdnPromMax').val()) currentRequest = false;
                            if (parameterval.summits_min != $('#hdnSummitsMin').val()) currentRequest = false;
                            if (parameterval.summits_max != $('#hdnSummitsMax').val()) currentRequest = false;
                            if (parameterval.difficulty_min != $('#hdnDifficultyMin').val()) currentRequest = false;
                            if (parameterval.difficulty_max != $('#hdnDifficultyMax').val()) currentRequest = false;
                            if (parameterval.lat != $('#hdnLat').val()) currentRequest = false;
                            if (parameterval.lng != $('#hdnLng').val()) currentRequest = false;
                            if (parameterval.bounds != $('#hdnBounds').val()) currentRequest = false;
                            if (parameterval.state_id != '0') {
                                byRegion = true;
                            }

                            //set up filter description for mobile header
                            if ($('#bt_showinmeters').text() == '[ft]') {
                                var elevMinMeters = Math.ceil(parseFloat(parameterval.elev_min) * 0.3048);
                                var elevMaxMeters = Math.ceil(parseFloat(parameterval.elev_max) * 0.3048);
                                if (parameterval.elev_max != '29527') {
                                    mobileFilters = mobileFilters + getRepString(elevMinMeters.toString()) + '-' + getRepString(elevMaxMeters.toString()) + ' m elev &bull; ';
                                } else if (parameterval.elev_min != '0') {
                                    mobileFilters = mobileFilters + getRepString(elevMinMeters.toString()) + '+ m elev &bull; ';
                                }
                            } else {
                                if (parameterval.elev_max != '29500') {
                                    mobileFilters = mobileFilters + getRepString(parameterval.elev_min) + '-' + getRepString(parameterval.elev_max) + ' ft elev &bull; ';
                                } else if (parameterval.elev_min != '0') {
                                    mobileFilters = mobileFilters + getRepString(parameterval.elev_min) + '+ ft elev &bull; ';
                                }
                            }
                            if ($('#prominence_bt_showinmeters').text() == '[ft]') {
                                var elevMinMeters = Math.ceil(parseFloat(parameterval.elev_min) * 0.3048);
                                var elevMaxMeters = Math.ceil(parseFloat(parameterval.elev_max) * 0.3048);
                                if (parameterval.prom_max != '29527') {
                                    mobileFilters = mobileFilters + getRepString(elevMinMeters.toString()) + '-' + getRepString(elevMaxMeters.toString()) + ' m prom &bull; ';
                                } else if (parameterval.prom_min != '0') {
                                    mobileFilters = mobileFilters + getRepString(elevMinMeters.toString()) + '+ m prom &bull; ';
                                }
                            } else {
                                if (parameterval.prom_max != '29500') {
                                    mobileFilters = mobileFilters + getRepString(parameterval.prom_min) + '-' + getRepString(parameterval.prom_max) + ' ft prom &bull; ';
                                } else if (parameterval.prom_min != '0') {
                                    mobileFilters = mobileFilters + getRepString(parameterval.prom_min) + '+ ft prom &bull; ';
                                }
                            }
                            if (parameterval.summits_max != '500') {
                                mobileFilters = mobileFilters + parameterval.summits_min + '-' + parameterval.summits_max + ' summits &bull; ';
                            } else if (parameterval.summits_min != '0') {
                                mobileFilters = mobileFilters + parameterval.summits_min + '+ summits &bull; ';
                            }
                            if (parameterval.difficulty_max != '5') {
                                mobileFilters = mobileFilters + 'Class ' + parameterval.difficulty_min + '-' + parameterval.difficulty_max + ' &bull; ';
                            } else if (parameterval.difficulty_min != '1') {
                                mobileFilters = mobileFilters + 'Class ' + parameterval.difficulty_min + '+ &bull; ';
                            }
                        });
                    }

                    if (!currentRequest) {
                        return false;
                    }

                    //set mobile title
                    var mobileTitle = '';
                    if (keyword.length > 0 && near.length > 0) {
                        mobileTitle = '"' + keyword + '" peaks near ' + near;
                    } else if (keyword.length > 0) {
                        mobileTitle = '"' + keyword + '" peaks';
                    } else if (near.length > 0) {
                        mobileTitle = 'Peaks near ' + near;
                    } else if (lat.length > 0 && lng.length > 0) {
                        mobileTitle = 'Peaks near you';
                    } else {
                        mobileTitle = 'All peaks';
                    }
                    if (mobileFilters != '') {
                        mobileTitle = mobileTitle + '<br /><span class="mobile-header-subtitle">' + mobileFilters.slice(0, -8) + '</span>';
                    }
                    $('#mobile-search-title').html(mobileTitle);

                    if (key=='peaks') {

                        var havePeaks = false;

                        $.each( val, function( peakkey, peakval ) {

                            if (!havePeaks) {

                                //first time through, delete highest peak marker and remove any markers not on map
                                deletehighest();
                                //delete markers out of margins
                                delete_old_markers(val);

                            }

                            havePeaks = true;

                            //build country string
                            var country = '';
                            $.each( peakval.country, function( countrykey, countryval ) {
                                country = country + '<div><a href="' + countryval.country_slug + '/">' + countryval.country_name + '</a></div>';
                            });
                            if (country == '') {
                                country = 'Unknown Country';
                            }

                            //build region string
                            var region = '';
                            var mobile_region = '';
                            var region_bull_class = 'hidden-lg hidden-md hidden-sm hidden-xs';
                            $.each( peakval.region, function( regionkey, regionval ) {
                                region_bull_class = '';
                                region = region + '<div style="margin-bottom: 5px;"><a class="truncate" href="' + regionval.region_slug + '/">' + regionval.region_name + ', ' + regionval.country_name + '</a></div>';
                                mobile_region = '<div style="margin-bottom: 5px;"><a class="truncate" href="' + regionval.region_slug + '/">' + regionval.country_name + '</a></div>';
                            });
                            if (region == '') {
                                region = country;
                                mobile_region = country;
                            }

                            //build challenges string
                            var challenges = '';
                            var challenge_count = peakval.challenge_count;
                            if (challenge_count > 0) {
                                challenges = ' &bull; <span>' + challenge_count + ' <i class="fa fa-trophy"></i></span>';
                            }

                            //build distance string
                            var distance = '';
                            if (near.length > 0) {
                                var miles = Math.round(peakval.miles_away);
                                var km = Math.round(peakval.miles_away*1.60934);
                                distance = miles.toString()+' mi / '+km.toString()+' km';
                            }

                            //build summits string
                            var summits, tooltip_your_summits;
                            if (peakval.summit_count > 0) {
                                summits = '<a href="/' + peakval.slug + '/summits/">' + peakval.summit_count + '</a>';
                            } else {
                                summits = '<a href="/' + peakval.slug + '/summits/">0</a><br /><a href="/' + peakval.slug + '/">Be the first!</a>';
                            }
                            if (peakval.your_summits > 0) {
                                summits = summits + '<br /><span style="color: #3fc663; font-weight: 500;">' + peakval.your_summits + 'x by you</span>';
                                tooltip_your_summits = '&nbsp;<span style="color: #3fc663; font-weight: 500;">(You ' + peakval.your_summits + 'x)</span>';
                            } else {
                                tooltip_your_summits = '';
                            }

                            //build tooltip string
                            var tooltip_html, tooltip_width, tooltip_height, marker_top, marker_left;
                            if (peakval.thumbnail_url.indexOf('default.png.350x245_q95_crop.jpg') < 0) {
                                tooltip_html = '<img class="map-tooltip-img hover-photos-hover" src="{{ MEDIA_URL }}' + peakval.thumbnail_url + '"><div class="map-tooltip-info"><div class="map-tooltip-peak-name ellipsis">' + peakval.name + '</div><div class="map-tooltip-peak-stats"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + tooltip_your_summits + challenges + '</div></div>';
                                tooltip_width = 220;
                                tooltip_height = 165;
                            } else {
                                tooltip_html = '<div class="map-tooltip-info"><div class="map-tooltip-peak-name ellipsis">' + peakval.name + '</div><div class="map-tooltip-peak-stats"><span class="peak-elevation-feet">' + numberWithCommas(Math.round(peakval.elevation)) + ' ft</span><span style="display: none;" class="peak-elevation-meters">' + numberWithCommas(Math.round(peakval.elevation * .3048)) + ' m</span> &bull; ' + peakval.summit_count + ' summit' + ((peakval.summit_count != 1) ? 's' : '') + tooltip_your_summits + challenges + '</div></div>';
                                tooltip_width = 220;
                                tooltip_height = 50;
                            }
                            var tooltip_url = '/' + peakval.slug;

                            var latLng = new mapboxgl.LngLat(peakval.lng, peakval.lat);

                            if (counter == 0) {
                                //highest peak gets red icon
                                iconstyle = 'marker_icon_red';
                            } else if (peakval.your_summits > 0) {
                                //if you have summited then green icon
                                iconstyle = 'marker_icon_green';
                            } else if (peakval.your_attempts > 0) {
                                //if you have attempted then yellow icon
                                iconstyle = 'marker_icon_yellow';
                            } else {
                                iconstyle = 'marker_icon';
                            }

                            if (isTouchDevice()) {
                                var is_draggable = false;
                            } else {
                                {% if request.user.is_staff %}
                                var is_draggable = true;
                                {% else %}
                                var is_draggable = false;
                                {% endif %}
                            }

                            //check if already exist so don't put again
                            var exists = false;
                            for (i = markersArray.length-1; i>=0; i--){
                                if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng)){
                                    exists = true ;
                                    //if the highest is in the actual viewport, not as the highest, delete it
                                    if (iconstyle=='marker_icon_red' || iconstyle=='marker_icon_redgreen' || iconstyle=='marker_icon_green') {
                                        markersArray[i].remove();
                                        markersArray.splice(i,1);
                                        exists = false;
                                    }
                                }
                            }

                            if (!exists) {
                                var latLng = [peakval.lng, peakval.lat];
                                //add marker
                                //create an HTML element for the marker
                                var el = document.createElement('div');
                                el.className = iconstyle;

                                el.addEventListener('click', function(e) {
                                    if (isTouchDevice()) {

                                        var bottom = $('#map-canvas').height();
                                        var right = $('#map-canvas').width();

                                        if (mapY < (bottom/2)) {
                                            marker_top = mapY;
                                        } else {
                                            marker_top = mapY - tooltip_height - 45;
                                        }

                                        if (mapX < (right/3)) {
                                            marker_left = mapX;
                                        } else if (mapX >= (right/3) && mapX < ((right/3)*2)) {
                                            marker_left = mapX - (tooltip_width/2) - 15;
                                        } else {
                                            marker_left = mapX - tooltip_width - 30;
                                        }

                                        $('#marker-tooltip').html(marker.properties.tooltipContent).css({
                                            'left': marker_left,
                                            'top': marker_top,
                                            'width': tooltip_width,
                                            'height': tooltip_height
                                        }).show();
                                        $('#marker-tooltip').data('url',marker.properties.tooltipUrl);
                                        if ($('#bt_showinmeters').hasClass('meters')) {
                                            $('#marker-tooltip').find('.peak-elevation-feet').hide();
                                            $('#marker-tooltip').find('.peak-elevation-meters').show();
                                        } else {
                                            $('#marker-tooltip').find('.peak-elevation-meters').hide();
                                            $('#marker-tooltip').find('.peak-elevation-feet').show();
                                        }
                                    } else {
                                        //console.log(peakval.slug);
                                        location = '/' + peakval.slug + '/';;
                                    }
                                });

                                el.addEventListener('mouseover', function(e) {

                                    var bottom = $('#map-canvas').height();
                                    var right = $('#map-canvas').width();

                                    if (mapY < (bottom/2)) {
                                        marker_top = mapY;
                                    } else {
                                        marker_top = mapY - tooltip_height - 45;
                                    }

                                    if (mapX < (right/3)) {
                                        marker_left = mapX;
                                    } else if (mapX >= (right/3) && mapX < ((right/3)*2)) {
                                        marker_left = mapX - (tooltip_width/2) - 15;
                                    } else {
                                        marker_left = mapX - tooltip_width - 30;
                                    }

                                    $('#marker-tooltip').html(marker.properties.tooltipContent).css({
                                        'left': marker_left,
                                        'top': marker_top,
                                        'width': tooltip_width,
                                        'height': tooltip_height
                                    }).show();
                                    $('#marker-tooltip').data('url',marker.properties.tooltipUrl);
                                    if ($('#bt_showinmeters').hasClass('meters')) {
                                        $('#marker-tooltip').find('.peak-elevation-feet').hide();
                                        $('#marker-tooltip').find('.peak-elevation-meters').show();
                                    } else {
                                        $('#marker-tooltip').find('.peak-elevation-meters').hide();
                                        $('#marker-tooltip').find('.peak-elevation-feet').show();
                                    }

                                });

                                el.addEventListener('mouseout', function(e) {
                                    if (isTouchDevice()) {
                                        //$('#marker-tooltip').hide();
                                    } else {
                                        $('#marker-tooltip').hide();
                                    }
                                });

                                var marker = new mapboxgl.Marker(el)
                                    .setLngLat(latLng)
                                    .setOffset([-5, -10])
                                    .setDraggable(is_draggable);

                                marker.properties = {};
                                marker.properties.tooltipContent = tooltip_html;
                                marker.properties.tooltipUrl = tooltip_url;
                                marker.properties.iconstyle = iconstyle;
                                marker.properties.peakid = peakval.id;

                                {% if request.user.is_staff %}
                                    //editing functions
                                    marker.on('dragstart', function(e) {
                                        $('#marker-tooltip').hide();
                                    });
                                    marker.on('dragend', function(e) {
                                        var point = marker.getLngLat();
                                        fix_item_location(peakval.id,point);
                                    });

                                    el.addEventListener('contextmenu', function(e) {
                                        if (confirm("Are you sure you want to delete this peak?")){
                                            delete_peak_from_map(peakval.id);
                                            $('#marker-tooltip').hide();
                                            for (i = markersArray.length-1; i>=0; i--){
                                                if (markersArray[i].properties.peakid==peakval.id) {
                                                    markersArray[i].remove();
                                                    markersArray.splice(i,1);
                                                }
                                            }
                                        }
                                    });
                                {% endif %}

                                markersArray.push(marker);
                                LatLngList.push(latLng);

                            }

                            counter ++;
                        });

                        //add markers to map in reverse order so highest on top
                        for (var i = markersArray.length - 1; i >= 0; --i) {
                            markersArray[i].addTo(map);
                        }

                        if (!havePeaks) {
                            $('#ajax-data-loading').css('display', 'none');
                            $('#peak-search-mobile').html('<i class="fa fa-filter"></i>');
                            //didn't have any peaks, so remove all markers
                            delete_old_markers(val);
                        } else {
                            $('#ajax-data-loading').css('display', 'none');
                            $('#peak-search-mobile').html('<i class="fa fa-filter"></i>');

                        }
                    }
                });
            });

        }

        function initPeaks() {

            var vars = [], hash, keyword, near, wanted_elev_min, wanted_elev_max, wanted_prom_min, wanted_prom_max, wanted_summits_min, wanted_summits_max, wanted_difficulty_min, wanted_difficulty_max, lat, lng;
            var q = document.URL.split('#')[1];
            if(q != undefined){
                q = q.split('&');
                for(var i = 0; i < q.length; i++){
                    hash = q[i].split('=');
                    vars.push(hash[1]);
                    vars[hash[0]] = hash[1];
                }
            }

            if (vars['q'] != undefined) {
                keyword = vars['q'];
            } else {
                keyword = '';
            }

            if (vars['n'] != undefined) {
                near = vars['n'];
            } else {
                near = '';
            }

            if (vars['elev_min'] != undefined) {
                wanted_elev_min = vars['elev_min'];
            } else {
                wanted_elev_min = '0';
            }

            if (vars['elev_max'] != undefined) {
                wanted_elev_max = vars['elev_max'];
            } else {
                //if elevation in meters
                if ($('#bt_showinmeters').text() == '[ft]') {
                    wanted_elev_max = '29527';
                } else {
                    wanted_elev_max = '29500';
                }
            }

            if (vars['prom_min'] != undefined) {
                wanted_prom_min = vars['prom_min'];
            } else {
                wanted_prom_min = '0';
            }

            if (vars['prom_max'] != undefined) {
                wanted_prom_max = vars['prom_max'];
            } else {
                //if prominence in meters
                if ($('#prominence_bt_showinmeters').text() == '[ft]') {
                    wanted_prom_max = '29527';
                } else {
                    wanted_prom_max = '29500';
                }
            }

            if (vars['summits_min'] != undefined) {
                wanted_summits_min = vars['summits_min'];
            } else {
                wanted_summits_min = '0';
            }

            if (vars['summits_max'] != undefined) {
                wanted_summits_max = vars['summits_max'];
            } else {
                wanted_summits_max = '500';
            }

            if (vars['difficulty_min'] != undefined) {
                wanted_difficulty_min = vars['difficulty_min'];
            } else {
                wanted_difficulty_min = '1';
            }

            if (vars['difficulty_max'] != undefined) {
                wanted_difficulty_max = vars['difficulty_max'];
            } else {
                wanted_difficulty_max = '5';
            }

            if (vars['lat'] != undefined) {
                lat = vars['lat'];
            } else {
                lat = '';
            }

            if (vars['lng'] != undefined) {
                lng = vars['lng'];
            } else {
                lng = '';
            }

            loadPeaks(keyword, near, wanted_elev_min, wanted_elev_max, wanted_prom_min, wanted_prom_max, wanted_summits_min, wanted_summits_max, wanted_difficulty_min, wanted_difficulty_max, lat, lng);

        }

    var markersArray = [];

    //center the map on country
    var region = "{{ region.name }}";
    var geocoder;
    {% if region.map_zoom %}
    var map_zoom_level = {{ region.map_zoom }};
    {% else %}
    var map_zoom_level = {{ country.map_zoom }};
    {% endif %}

    {% if region.lat and region.long %}
    var latLng = [{{ region.long }}, {{ region.lat }}];
    {% else %}
    var latLng = [{{ country.long }}, {{ country.lat }}];
    {% endif %}

    function initialize() {

        var mapDiv = document.getElementById('map-canvas');
        var LatLngList = [];

        if (isTouchDevice()) {
            map = new mapboxgl.Map({
                container: mapDiv, // HTML container id
                style: mapStyle, // style URL
                center: latLng, // starting position as [lng, lat]
                zoom: map_zoom_level
            });
        } else {
            map = new mapboxgl.Map({
                container: mapDiv, // HTML container id
                style: mapStyle, // style URL
                center: latLng, // starting position as [lng, lat]
                zoom: map_zoom_level
            });
            scale = new mapboxgl.ScaleControl({maxWidth: 120, unit: 'imperial'});
            map.addControl(scale, 'bottom-right');
            var nav = new mapboxgl.NavigationControl({showCompass: false});
            map.addControl(nav, 'bottom-right');
            // disable map rotation using right click + drag
            map.dragRotate.disable();
            // disable map rotation using touch rotation gesture
            map.touchZoomRotate.disableRotation();
        }

        map.on('click', function(e) {
            if (isTouchDevice()) {
                $('#marker-tooltip').hide();
            } else {
                $('#marker-tooltip').hide();
            }
        });

        map.on('dragstart', function(e) {
            if (isTouchDevice()) {
                $('#marker-tooltip').hide();
            } else {
                $('#marker-tooltip').hide();
            }
        });

        map.on('moveend', function () {
            initPeaks();
        });

        map.on('load', function () {
            initPeaks();
            var mapUnits = readCookie('map_units');
            if (mapUnits != '') {
                toggleMapUnits(mapUnits);
            }
            setMapControls();
        });

    }

    function setMapControls() {
        var check = checkIfMapboxStyleIsLoaded();
        if (!check) {
            // It's not safe to manipulate layers yet, so wait 200ms and then check again
            setTimeout(function() {
                setMapControls();
            }, 200);
            return;
        }
        // Whew, now it's safe to manipulate layers!
        var mapUnits = readCookie('map_units');
        if (mapUnits == 'meters') {
            toggleMapUnits(mapUnits);
            //also update sliders
            var $this = $('#bt_showinmeters');
            $this.addClass('meters');
            $this.text('[ft]');
            scale.setUnit('metric');

            //reset elevation slider
            var elevationSlider = document.getElementById('slider-range');
            elevationSlider.noUiSlider.updateOptions({
                range: {
                    'min': 0,
                    '4.3%': 200,
                    '6.5%': 400,
                    '8.7%': 600,
                    '10.9%': 800,
                    '13.0%': 1000,
                    '15.2%': 1200,
                    '17.4%': 1400,
                    '19.6%': 1600,
                    '21.7%': 1800,
                    '23.9%': 2000,
                    '26.1%': 2200,
                    '28.3%': 2400,
                    '30.4%': 2600,
                    '32.6%': 2800,
                    '34.8%': 3000,
                    '37.0%': 3200,
                    '39.1%': 3400,
                    '41.3%': 3600,
                    '43.5%': 3800,
                    '45.7%': 4000,
                    '47.8%': 4200,
                    '50.0%': 4400,
                    '52.2%': 4600,
                    '54.3%': 4800,
                    '56.5%': 5000,
                    '58.7%': 5200,
                    '60.9%': 5400,
                    '63.0%': 5600,
                    '65.2%': 5800,
                    '67.4%': 6000,
                    '69.6%': 6200,
                    '71.7%': 6400,
                    '73.9%': 6600,
                    '76.1%': 6800,
                    '78.3%': 7000,
                    '80.4%': 7200,
                    '82.6%': 7400,
                    '84.8%': 7600,
                    '87.0%': 7800,
                    '89.1%': 8000,
                    '91.3%': 8200,
                    '93.5%': 8400,
                    '95.7%': 8600,
                    '97.8%': 8800,
                    'max': 9000
                }
            });
            elevationSlider.noUiSlider.set([0, 9000]);
            $('#elevation-label').css('color','#aaa');
            $('#amount').css('color','#aaa');
            $('#amount').css('-webkit-text-fill-color','#aaa');
            $('#slider-range > div > div').css('background-color','#00b2f2');
            $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-blue.png)');
            $( "#amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29527) + " ft");
            $( "#amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(9000) + " m");
            u = updateURLParameter(window.location.hash, 'elev_min', 0);
            u = updateURLParameter('#'+u, 'elev_max', 29527);
            window.location.hash = u;
            //loadPeaksFromHash();
            $('input.ammount').toggle();

            var $this = $('#prominence_bt_showinmeters');
            $this.addClass('meters');
            $this.text('[ft]');
            scale.setUnit('metric');

            //reset prominence slider
            var prominenceSlider = document.getElementById('prominence-slider-range');
            prominenceSlider.noUiSlider.updateOptions({
                range: {
                    'min': 0,
                    '2.1%': 10,
                    '3.2%': 20,
                    '4.2%': 30,
                    '5.3%': 50,
                    '6.3%': 100,
                    '7.4%': 200,
                    '8.4%': 300,
                    '9.5%': 400,
                    '10.5%': 500,
                    '11.6%': 600,
                    '12.6%': 700,
                    '13.7%': 800,
                    '14.7%': 900,
                    '15.8%': 1000,
                    '16.8%': 1100,
                    '17.9%': 1200,
                    '18.9%': 1300,
                    '20.0%': 1400,
                    '21.1%': 1500,
                    '22.1%': 1600,
                    '23.2%': 1700,
                    '24.2%': 1800,
                    '25.3%': 1900,
                    '26.3%': 2000,
                    '27.4%': 2100,
                    '28.4%': 2200,
                    '29.5%': 2300,
                    '30.5%': 2400,
                    '31.6%': 2500,
                    '32.6%': 2600,
                    '33.7%': 2700,
                    '34.7%': 2800,
                    '35.8%': 2900,
                    '36.8%': 3000,
                    '37.9%': 3100,
                    '38.9%': 3200,
                    '40.0%': 3300,
                    '41.1%': 3400,
                    '42.1%': 3500,
                    '43.2%': 3600,
                    '44.2%': 3700,
                    '45.3%': 3800,
                    '46.3%': 3900,
                    '47.4%': 4000,
                    '48.4%': 4100,
                    '49.5%': 4200,
                    '50.5%': 4300,
                    '51.6%': 4400,
                    '52.6%': 4500,
                    '53.7%': 4600,
                    '54.7%': 4700,
                    '55.8%': 4800,
                    '56.8%': 4900,
                    '57.9%': 5000,
                    '58.9%': 5100,
                    '60.0%': 5200,
                    '61.1%': 5300,
                    '62.1%': 5400,
                    '63.2%': 5500,
                    '64.2%': 5600,
                    '65.3%': 5700,
                    '66.3%': 5800,
                    '67.4%': 5900,
                    '68.4%': 6000,
                    '69.5%': 6100,
                    '70.5%': 6200,
                    '71.6%': 6300,
                    '72.6%': 6400,
                    '73.7%': 6500,
                    '74.7%': 6600,
                    '75.8%': 6700,
                    '76.8%': 6800,
                    '77.9%': 6900,
                    '78.9%': 7000,
                    '80.0%': 7100,
                    '81.1%': 7200,
                    '82.1%': 7300,
                    '83.2%': 7400,
                    '84.2%': 7500,
                    '85.3%': 7600,
                    '86.3%': 7700,
                    '87.4%': 7800,
                    '88.4%': 7900,
                    '89.5%': 8000,
                    '90.5%': 8100,
                    '91.6%': 8200,
                    '92.6%': 8300,
                    '93.7%': 8400,
                    '94.7%': 8500,
                    '95.8%': 8600,
                    '96.8%': 8700,
                    '97.9%': 8800,
                    '98.9%': 8900,
                    'max': 9000
                }
            });
            prominenceSlider.noUiSlider.set([0, 9000]);
            $('#prominence-label').css('color','#aaa');
            $('#prominence-amount').css('color','#aaa');
            $('#prominence-amount').css('-webkit-text-fill-color','#aaa');
            $('#prominence-slider-range > div > div').css('background-color','#00b2f2');
            $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-blue.png)');
            $( "#prominence-amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29527) + " ft");
            $( "#prominence-amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(9000) + " m");
            u = updateURLParameter(window.location.hash, 'prom_min', 0);
            u = updateURLParameter('#'+u, 'prom_max', 29527);
            window.location.hash = u;
            //loadPeaksFromHash();
            $('input.prominence-ammount').toggle();
        }
        loadPeaksFromHash();
    }

    function addExtraMapLayers() {
        var check = checkIfMapboxStyleIsLoaded();
        if (!check) {
            // It's not safe to manipulate layers yet, so wait 200ms and then check again
            setTimeout(function() {
                addExtraMapLayers();
            }, 200);
            return;
        }
        // Whew, now it's safe to manipulate layers!
        // No extra map layers necessary
    }

    $(document).ready(function(){

        var vars = [], hash, keyword, near, wanted_elev_min, wanted_elev_max, wanted_prom_min, wanted_prom_max, wanted_summits_min, wanted_summits_max, wanted_difficulty_min, wanted_difficulty_max, lat, lng;
        var q = document.URL.split('#')[1];
        if(q != undefined){
            q = q.split('&');
            for(var i = 0; i < q.length; i++){
                hash = q[i].split('=');
                vars.push(hash[1]);
                vars[hash[0]] = hash[1];
            }
        }

        if (vars['q'] != undefined) {
            keyword = vars['q'];
        } else {
            keyword = '';
        }

        if (vars['n'] != undefined) {
            near = vars['n'];
        } else {
            near = '';
        }

        if (vars['elev_min'] != undefined) {
            wanted_elev_min = vars['elev_min'];
        } else {
            wanted_elev_min = '0';
        }

        if (vars['elev_max'] != undefined) {
            wanted_elev_max = vars['elev_max'];
        } else {
            //if elevation in meters
            if ($('#bt_showinmeters').text() == '[ft]') {
                wanted_elev_max = '29527';
            } else {
                wanted_elev_max = '29500';
            }
        }

        if (vars['prom_min'] != undefined) {
            wanted_prom_min = vars['prom_min'];
        } else {
            wanted_prom_min = '0';
        }

        if (vars['prom_max'] != undefined) {
            wanted_prom_max = vars['prom_max'];
        } else {
            //if prominence in meters
            if ($('#prominence_bt_showinmeters').text() == '[ft]') {
                wanted_prom_max = '29527';
            } else {
                wanted_prom_max = '29500';
            }
        }

        if (vars['summits_min'] != undefined) {
            wanted_summits_min = vars['summits_min'];
        } else {
            wanted_summits_min = '0';
        }

        if (vars['summits_max'] != undefined) {
            wanted_summits_max = vars['summits_max'];
        } else {
            wanted_summits_max = '500';
        }

        if (vars['difficulty_min'] != undefined) {
            wanted_difficulty_min = vars['difficulty_min'];
        } else {
            wanted_difficulty_min = '1';
        }

        if (vars['difficulty_max'] != undefined) {
            wanted_difficulty_max = vars['difficulty_max'];
        } else {
            wanted_difficulty_max = '5';
        }

        if (vars['lat'] != undefined) {
            lat = vars['lat'];
        } else {
            lat = '';
        }

        if (vars['lng'] != undefined) {
            lng = vars['lng'];
        } else {
            lng = '';
        }

        var window_width = $(window).width();
        var height_pad = 120;
        var width_pad = 240;
        if (window_width < 1024) {
            height_pad = 170;
            width_pad = 0;
        }
        if (window_width < 768) {
            height_pad = 149;
            width_pad = 0;
        }
        var max_height = $(window).height()- height_pad;
        var max_width = $(window).width()- width_pad;
        $("div#map-canvas").height(max_height);
        $("div#map-canvas").width(max_width);
        $("#peak-search").height($(window).height());
        $("#explore .leftCol").height($("#explore .rightCol").height());
        $("div.peak_list_cont").css("margin-bottom","0");
        initialize();
        init = true;

        //switch map units
        $("#gm-custom-mapunits").click(function(){
            //toggle map units
            if ($('#gm-custom-mapunitsbutton-label-meters').hasClass('gm-custom-mapunits-selected')) {
                toggleMapUnits('feet');
            } else {
                toggleMapUnits('meters');
            }
            var $this = $('#bt_showinmeters');
            if($this.hasClass('meters')){
                $this.removeClass('meters');
                $this.text('[m]');
                scale.setUnit('imperial');

                //reset elevation slider
                elevationSlider.noUiSlider.updateOptions({
                    range: {
                        'min': 0,
                        '3.3%': 500,
                        '5.0%': 1000,
                        '6.7%': 1500,
                        '8.3%': 2000,
                        '10.0%': 2500,
                        '11.7%': 3000,
                        '13.3%': 3500,
                        '15.0%': 4000,
                        '16.7%': 4500,
                        '18.3%': 5000,
                        '20.0%': 5500,
                        '21.7%': 6000,
                        '23.3%': 6500,
                        '25.0%': 7000,
                        '26.7%': 7500,
                        '28.3%': 8000,
                        '30.0%': 8500,
                        '31.7%': 9000,
                        '33.3%': 9500,
                        '35.0%': 10000,
                        '36.7%': 10500,
                        '38.3%': 11000,
                        '40.0%': 11500,
                        '41.7%': 12000,
                        '43.3%': 12500,
                        '45.0%': 13000,
                        '46.7%': 13500,
                        '48.3%': 14000,
                        '50.0%': 14500,
                        '51.7%': 15000,
                        '53.3%': 15500,
                        '55.0%': 16000,
                        '56.7%': 16500,
                        '58.3%': 17000,
                        '60.0%': 17500,
                        '61.7%': 18000,
                        '63.3%': 18500,
                        '65.0%': 19000,
                        '66.7%': 19500,
                        '68.3%': 20000,
                        '70.0%': 20500,
                        '71.7%': 21000,
                        '73.3%': 21500,
                        '75.0%': 22000,
                        '76.7%': 22500,
                        '78.3%': 23000,
                        '80.0%': 23500,
                        '81.7%': 24000,
                        '83.3%': 24500,
                        '85.0%': 25000,
                        '86.7%': 25500,
                        '88.3%': 26000,
                        '90.0%': 26500,
                        '91.7%': 27000,
                        '93.3%': 27500,
                        '95.0%': 28000,
                        '96.7%': 28500,
                        '98.3%': 29000,
                        'max': 29500
                    }
                });
                elevationSlider.noUiSlider.set([0, 29500]);
                $('#elevation-label').css('color','#aaa');
                $('#amount').css('color','#aaa');
                $('#amount').css('-webkit-text-fill-color','#aaa');
                $('#slider-range > div > div').css('background-color','#00b2f2');
                $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-blue.png)');
                $( "#amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29500) + " ft");
                $( "#amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(9000) + " m");
                u = updateURLParameter(window.location.hash, 'elev_min', 0);
                u = updateURLParameter('#'+u, 'elev_max', 29500);
                window.location.hash = u;
                //loadPeaksFromHash();
            } else {
                $this.addClass('meters');
                $this.text('[ft]');
                scale.setUnit('metric');

                //reset elevation slider
                elevationSlider.noUiSlider.updateOptions({
                    range: {
                        'min': 0,
                        '4.3%': 200,
                        '6.5%': 400,
                        '8.7%': 600,
                        '10.9%': 800,
                        '13.0%': 1000,
                        '15.2%': 1200,
                        '17.4%': 1400,
                        '19.6%': 1600,
                        '21.7%': 1800,
                        '23.9%': 2000,
                        '26.1%': 2200,
                        '28.3%': 2400,
                        '30.4%': 2600,
                        '32.6%': 2800,
                        '34.8%': 3000,
                        '37.0%': 3200,
                        '39.1%': 3400,
                        '41.3%': 3600,
                        '43.5%': 3800,
                        '45.7%': 4000,
                        '47.8%': 4200,
                        '50.0%': 4400,
                        '52.2%': 4600,
                        '54.3%': 4800,
                        '56.5%': 5000,
                        '58.7%': 5200,
                        '60.9%': 5400,
                        '63.0%': 5600,
                        '65.2%': 5800,
                        '67.4%': 6000,
                        '69.6%': 6200,
                        '71.7%': 6400,
                        '73.9%': 6600,
                        '76.1%': 6800,
                        '78.3%': 7000,
                        '80.4%': 7200,
                        '82.6%': 7400,
                        '84.8%': 7600,
                        '87.0%': 7800,
                        '89.1%': 8000,
                        '91.3%': 8200,
                        '93.5%': 8400,
                        '95.7%': 8600,
                        '97.8%': 8800,
                        'max': 9000
                    }
                });
                elevationSlider.noUiSlider.set([0, 9000]);
                $('#elevation-label').css('color','#aaa');
                $('#amount').css('color','#aaa');
                $('#amount').css('-webkit-text-fill-color','#aaa');
                $('#slider-range > div > div').css('background-color','#00b2f2');
                $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-blue.png)');
                $( "#amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29527) + " ft");
                $( "#amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(9000) + " m");
                u = updateURLParameter(window.location.hash, 'elev_min', 0);
                u = updateURLParameter('#'+u, 'elev_max', 29527);
                window.location.hash = u;
                //loadPeaksFromHash();
            }
            $('input.ammount').toggle();
            var $this = $('#prominence_bt_showinmeters');
            if($this.hasClass('meters')){
                $this.removeClass('meters');
                $this.text('[m]');
                scale.setUnit('imperial');

                //reset prominence slider
                prominenceSlider.noUiSlider.updateOptions({
                    range: {
                        'min': 0,
                        '3.1%': 50,
                        '4.7%': 100,
                        '6.3%': 200,
                        '7.8%': 300,
                        '9.4%': 500,
                        '10.9%': 1000,
                        '12.5%': 1500,
                        '14.1%': 2000,
                        '15.6%': 2500,
                        '17.2%': 3000,
                        '18.8%': 3500,
                        '20.3%': 4000,
                        '21.9%': 4500,
                        '23.4%': 5000,
                        '25.0%': 5500,
                        '26.6%': 6000,
                        '28.1%': 6500,
                        '29.7%': 7000,
                        '31.3%': 7500,
                        '32.8%': 8000,
                        '34.4%': 8500,
                        '35.9%': 9000,
                        '37.5%': 9500,
                        '39.1%': 10000,
                        '40.6%': 10500,
                        '42.2%': 11000,
                        '43.8%': 11500,
                        '45.3%': 12000,
                        '46.9%': 12500,
                        '48.4%': 13000,
                        '50.0%': 13500,
                        '51.6%': 14000,
                        '53.1%': 14500,
                        '54.7%': 15000,
                        '56.3%': 15500,
                        '57.8%': 16000,
                        '59.4%': 16500,
                        '60.9%': 17000,
                        '62.5%': 17500,
                        '64.1%': 18000,
                        '65.6%': 18500,
                        '67.2%': 19000,
                        '68.8%': 19500,
                        '70.3%': 20000,
                        '71.9%': 20500,
                        '73.4%': 21000,
                        '75.0%': 21500,
                        '76.6%': 22000,
                        '78.1%': 22500,
                        '79.7%': 23000,
                        '81.3%': 23500,
                        '82.8%': 24000,
                        '84.4%': 24500,
                        '85.9%': 25000,
                        '87.5%': 25500,
                        '89.1%': 26000,
                        '90.6%': 26500,
                        '92.2%': 27000,
                        '93.8%': 27500,
                        '95.3%': 28000,
                        '96.9%': 28500,
                        '98.4%': 29000,
                        'max': 29500
                    }
                });
                prominenceSlider.noUiSlider.set([0, 29500]);
                $('#prominence-label').css('color','#aaa');
                $('#prominence-amount').css('color','#aaa');
                $('#prominence-amount').css('-webkit-text-fill-color','#aaa');
                $('#prominence-slider-range > div > div').css('background-color','#00b2f2');
                $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-blue.png)');
                $( "#prominence-amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29500) + " ft");
                $( "#prominence-amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(9000) + " m");
                u = updateURLParameter(window.location.hash, 'prom_min', 0);
                u = updateURLParameter('#'+u, 'prom_max', 29500);
                window.location.hash = u;
                //loadPeaksFromHash();
            } else {
                $this.addClass('meters');
                $this.text('[ft]');
                scale.setUnit('metric');

                //reset prominence slider
                prominenceSlider.noUiSlider.updateOptions({
                    range: {
                        'min': 0,
                        '2.1%': 10,
                        '3.2%': 20,
                        '4.2%': 30,
                        '5.3%': 50,
                        '6.3%': 100,
                        '7.4%': 200,
                        '8.4%': 300,
                        '9.5%': 400,
                        '10.5%': 500,
                        '11.6%': 600,
                        '12.6%': 700,
                        '13.7%': 800,
                        '14.7%': 900,
                        '15.8%': 1000,
                        '16.8%': 1100,
                        '17.9%': 1200,
                        '18.9%': 1300,
                        '20.0%': 1400,
                        '21.1%': 1500,
                        '22.1%': 1600,
                        '23.2%': 1700,
                        '24.2%': 1800,
                        '25.3%': 1900,
                        '26.3%': 2000,
                        '27.4%': 2100,
                        '28.4%': 2200,
                        '29.5%': 2300,
                        '30.5%': 2400,
                        '31.6%': 2500,
                        '32.6%': 2600,
                        '33.7%': 2700,
                        '34.7%': 2800,
                        '35.8%': 2900,
                        '36.8%': 3000,
                        '37.9%': 3100,
                        '38.9%': 3200,
                        '40.0%': 3300,
                        '41.1%': 3400,
                        '42.1%': 3500,
                        '43.2%': 3600,
                        '44.2%': 3700,
                        '45.3%': 3800,
                        '46.3%': 3900,
                        '47.4%': 4000,
                        '48.4%': 4100,
                        '49.5%': 4200,
                        '50.5%': 4300,
                        '51.6%': 4400,
                        '52.6%': 4500,
                        '53.7%': 4600,
                        '54.7%': 4700,
                        '55.8%': 4800,
                        '56.8%': 4900,
                        '57.9%': 5000,
                        '58.9%': 5100,
                        '60.0%': 5200,
                        '61.1%': 5300,
                        '62.1%': 5400,
                        '63.2%': 5500,
                        '64.2%': 5600,
                        '65.3%': 5700,
                        '66.3%': 5800,
                        '67.4%': 5900,
                        '68.4%': 6000,
                        '69.5%': 6100,
                        '70.5%': 6200,
                        '71.6%': 6300,
                        '72.6%': 6400,
                        '73.7%': 6500,
                        '74.7%': 6600,
                        '75.8%': 6700,
                        '76.8%': 6800,
                        '77.9%': 6900,
                        '78.9%': 7000,
                        '80.0%': 7100,
                        '81.1%': 7200,
                        '82.1%': 7300,
                        '83.2%': 7400,
                        '84.2%': 7500,
                        '85.3%': 7600,
                        '86.3%': 7700,
                        '87.4%': 7800,
                        '88.4%': 7900,
                        '89.5%': 8000,
                        '90.5%': 8100,
                        '91.6%': 8200,
                        '92.6%': 8300,
                        '93.7%': 8400,
                        '94.7%': 8500,
                        '95.8%': 8600,
                        '96.8%': 8700,
                        '97.9%': 8800,
                        '98.9%': 8900,
                        'max': 9000
                    }
                });
                prominenceSlider.noUiSlider.set([0, 9000]);
                $('#prominence-label').css('color','#aaa');
                $('#prominence-amount').css('color','#aaa');
                $('#prominence-amount').css('-webkit-text-fill-color','#aaa');
                $('#prominence-slider-range > div > div').css('background-color','#00b2f2');
                $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-blue.png)');
                $( "#prominence-amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29527) + " ft");
                $( "#prominence-amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(9000) + " m");
                u = updateURLParameter(window.location.hash, 'prom_min', 0);
                u = updateURLParameter('#'+u, 'prom_max', 29527);
                window.location.hash = u;
                //loadPeaksFromHash();
            }
            $('input.prominence-ammount').toggle();
            loadPeaksFromHash();
        });

        $("div#highest-peak").click(function(){
            url = $(this).attr("title");
            if (url!=""){
                location = url;
            }
        });

        $('#map-canvas').mousemove(function(e) {
            var offset = $(this).offset();
            pageX = e.pageX;
            pageY = e.pageY;
            mapX = (e.pageX - offset.left);
            mapY = (e.pageY - offset.top);
        });

        $('#map-canvas').on('touchstart', function(e) {
            var offset = $(this).offset();
            pageX = e.originalEvent.touches[0].pageX;
            pageY = e.originalEvent.touches[0].pageY;
            mapX = (pageX - offset.left);
            mapY = (pageY - offset.top);
        });

        $(window).resize(function() {
            var window_width = $(window).width();
            if (window_width < 1024) {
                $('#peaks-map').css({left: '0px'})
            } else {
                if ($('#peak-search').css('left') == '0px') {
                    $('#peaks-map').css({left: '240px'})
                } else {
                    $('#peaks-map').css({left: '0px'})
                }
            }
            var height_pad = 120;
            var width_pad = 240;
            if ($('#peak-search').css('left') != '0px') {
                width_pad = 0;
            }
            if (window_width < 1024) {
                height_pad = 170;
                width_pad = 0;
            }
            if (window_width < 768) {
                height_pad = 149;
                width_pad = 0;
            }
            var max_height = $(window).height()- height_pad;
            var max_width = $(window).width()- width_pad;
            $("div#map-canvas").height(max_height);
            $("div#map-canvas").width(max_width);
            $("#peak-search").height($(window).height());
            map.resize();
        });

        //Set up the sliders
        var elevationSlider = document.getElementById('slider-range');

        noUiSlider.create(elevationSlider, {
            start: [ 0, 29500 ],
            snap: true,
            connect: true,
            start: [wanted_elev_min, wanted_elev_max],
            range: {
                'min': 0,
                '3.3%': 500,
                '5.0%': 1000,
                '6.7%': 1500,
                '8.3%': 2000,
                '10.0%': 2500,
                '11.7%': 3000,
                '13.3%': 3500,
                '15.0%': 4000,
                '16.7%': 4500,
                '18.3%': 5000,
                '20.0%': 5500,
                '21.7%': 6000,
                '23.3%': 6500,
                '25.0%': 7000,
                '26.7%': 7500,
                '28.3%': 8000,
                '30.0%': 8500,
                '31.7%': 9000,
                '33.3%': 9500,
                '35.0%': 10000,
                '36.7%': 10500,
                '38.3%': 11000,
                '40.0%': 11500,
                '41.7%': 12000,
                '43.3%': 12500,
                '45.0%': 13000,
                '46.7%': 13500,
                '48.3%': 14000,
                '50.0%': 14500,
                '51.7%': 15000,
                '53.3%': 15500,
                '55.0%': 16000,
                '56.7%': 16500,
                '58.3%': 17000,
                '60.0%': 17500,
                '61.7%': 18000,
                '63.3%': 18500,
                '65.0%': 19000,
                '66.7%': 19500,
                '68.3%': 20000,
                '70.0%': 20500,
                '71.7%': 21000,
                '73.3%': 21500,
                '75.0%': 22000,
                '76.7%': 22500,
                '78.3%': 23000,
                '80.0%': 23500,
                '81.7%': 24000,
                '83.3%': 24500,
                '85.0%': 25000,
                '86.7%': 25500,
                '88.3%': 26000,
                '90.0%': 26500,
                '91.7%': 27000,
                '93.3%': 27500,
                '95.0%': 28000,
                '96.7%': 28500,
                '98.3%': 29000,
                'max': 29500
            }
        });

        elevationSlider.noUiSlider.on('slide', function(values, handle, unencoded, tap, positions){
            $( "#amount" ).val( numberWithCommas(unencoded[0]) + "  to  " + numberWithCommas(unencoded[1]) + " ft");
            $( "#amount-meters" ).val(numberWithCommas(Math.floor(unencoded[0])) + "  to  " + numberWithCommas(Math.floor(unencoded[1])) + " m");
            //if meters
            if ($('#amount-meters').is(':visible')) {
                if (0 != unencoded[0] || 9000 != unencoded[1]) {
                    $('#elevation-label').css('color', '#f24100');
                    $('#amount').css('color', '#f24100');
                    $('#slider-range > div > div').css('background-color', '#f24100');
                    $('#slider-range .noUi-background').css('background-color', '#CCC');
                    $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                } else {
                    $('#elevation-label').css('color', '#aaa');
                    $('#amount').css('color', '#aaa');
                    $('#amount').css('-webkit-text-fill-color','#aaa');
                    $('#slider-range > div > div').css('background-color', '#00b2f2');
                    $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-blue.png)');
                }
            } else {
                if (0 != unencoded[0] || 29500 != unencoded[1]) {
                    $('#elevation-label').css('color', '#f24100');
                    $('#amount').css('color', '#f24100');
                    $('#slider-range > div > div').css('background-color', '#f24100');
                    $('#slider-range .noUi-background').css('background-color', '#CCC');
                    $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                } else {
                    $('#elevation-label').css('color', '#aaa');
                    $('#amount').css('color', '#aaa');
                    $('#amount').css('-webkit-text-fill-color','#aaa');
                    $('#slider-range > div > div').css('background-color', '#00b2f2');
                    $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-blue.png)');
                }
            }
        });
        elevationSlider.noUiSlider.on('end', function(values, handle, unencoded, tap, positions){
            //if meters
            if ($('#bt_showinmeters').text() == '[ft]') {
                delayShowDataelevation("#amount", [unencoded[0]/0.3048,unencoded[1]/0.3048]);
            } else {
                delayShowDataelevation("#amount", unencoded);
            }
        });
        var elevationValues = elevationSlider.noUiSlider.get();
        $( "#amount" ).val( numberWithCommas(parseInt(elevationValues[0])) + "  to  " + numberWithCommas(parseInt(elevationValues[1])) + " ft");
        $( "#amount-meters" ).val(numberWithCommas(Math.floor(parseInt(elevationValues[0]))) + "  to  " + numberWithCommas(parseInt(Math.floor(elevationValues[1]))) + " m");

        $('#bt_showinmeters').click(function(event){
            event.preventDefault();

            //map unit toggle
            if ($('#gm-custom-mapunitsbutton-label-meters').hasClass('gm-custom-mapunits-selected')) {
                toggleMapUnits('feet');
            } else {
                toggleMapUnits('meters');
            }

            var $this = $('#bt_showinmeters');
            if($this.hasClass('meters')){
                $this.removeClass('meters');
                $this.text('[m]');
                scale.setUnit('imperial');

                //reset elevation slider
                elevationSlider.noUiSlider.updateOptions({
                    range: {
                        'min': 0,
                        '3.3%': 500,
                        '5.0%': 1000,
                        '6.7%': 1500,
                        '8.3%': 2000,
                        '10.0%': 2500,
                        '11.7%': 3000,
                        '13.3%': 3500,
                        '15.0%': 4000,
                        '16.7%': 4500,
                        '18.3%': 5000,
                        '20.0%': 5500,
                        '21.7%': 6000,
                        '23.3%': 6500,
                        '25.0%': 7000,
                        '26.7%': 7500,
                        '28.3%': 8000,
                        '30.0%': 8500,
                        '31.7%': 9000,
                        '33.3%': 9500,
                        '35.0%': 10000,
                        '36.7%': 10500,
                        '38.3%': 11000,
                        '40.0%': 11500,
                        '41.7%': 12000,
                        '43.3%': 12500,
                        '45.0%': 13000,
                        '46.7%': 13500,
                        '48.3%': 14000,
                        '50.0%': 14500,
                        '51.7%': 15000,
                        '53.3%': 15500,
                        '55.0%': 16000,
                        '56.7%': 16500,
                        '58.3%': 17000,
                        '60.0%': 17500,
                        '61.7%': 18000,
                        '63.3%': 18500,
                        '65.0%': 19000,
                        '66.7%': 19500,
                        '68.3%': 20000,
                        '70.0%': 20500,
                        '71.7%': 21000,
                        '73.3%': 21500,
                        '75.0%': 22000,
                        '76.7%': 22500,
                        '78.3%': 23000,
                        '80.0%': 23500,
                        '81.7%': 24000,
                        '83.3%': 24500,
                        '85.0%': 25000,
                        '86.7%': 25500,
                        '88.3%': 26000,
                        '90.0%': 26500,
                        '91.7%': 27000,
                        '93.3%': 27500,
                        '95.0%': 28000,
                        '96.7%': 28500,
                        '98.3%': 29000,
                        'max': 29500
                    }
                });
                elevationSlider.noUiSlider.set([0, 29500]);
                $('#elevation-label').css('color','#aaa');
                $('#amount').css('color','#aaa');
                $('#amount').css('-webkit-text-fill-color','#aaa');
                $('#slider-range > div > div').css('background-color','#00b2f2');
                $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-blue.png)');
                $( "#amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29500) + " ft");
                $( "#amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(9000) + " m");
                u = updateURLParameter(window.location.hash, 'elev_min', 0);
                u = updateURLParameter('#'+u, 'elev_max', 29500);
                window.location.hash = u;
                //loadPeaksFromHash();
            } else {
                $this.addClass('meters');
                $this.text('[ft]');
                scale.setUnit('metric');

                //reset elevation slider
                elevationSlider.noUiSlider.updateOptions({
                    range: {
                        'min': 0,
                        '4.3%': 200,
                        '6.5%': 400,
                        '8.7%': 600,
                        '10.9%': 800,
                        '13.0%': 1000,
                        '15.2%': 1200,
                        '17.4%': 1400,
                        '19.6%': 1600,
                        '21.7%': 1800,
                        '23.9%': 2000,
                        '26.1%': 2200,
                        '28.3%': 2400,
                        '30.4%': 2600,
                        '32.6%': 2800,
                        '34.8%': 3000,
                        '37.0%': 3200,
                        '39.1%': 3400,
                        '41.3%': 3600,
                        '43.5%': 3800,
                        '45.7%': 4000,
                        '47.8%': 4200,
                        '50.0%': 4400,
                        '52.2%': 4600,
                        '54.3%': 4800,
                        '56.5%': 5000,
                        '58.7%': 5200,
                        '60.9%': 5400,
                        '63.0%': 5600,
                        '65.2%': 5800,
                        '67.4%': 6000,
                        '69.6%': 6200,
                        '71.7%': 6400,
                        '73.9%': 6600,
                        '76.1%': 6800,
                        '78.3%': 7000,
                        '80.4%': 7200,
                        '82.6%': 7400,
                        '84.8%': 7600,
                        '87.0%': 7800,
                        '89.1%': 8000,
                        '91.3%': 8200,
                        '93.5%': 8400,
                        '95.7%': 8600,
                        '97.8%': 8800,
                        'max': 9000
                    }
                });
                elevationSlider.noUiSlider.set([0, 9000]);
                $('#elevation-label').css('color','#aaa');
                $('#amount').css('color','#aaa');
                $('#amount').css('-webkit-text-fill-color','#aaa');
                $('#slider-range > div > div').css('background-color','#00b2f2');
                $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-blue.png)');
                $( "#amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29527) + " ft");
                $( "#amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(9000) + " m");
                u = updateURLParameter(window.location.hash, 'elev_min', 0);
                u = updateURLParameter('#'+u, 'elev_max', 29527);
                window.location.hash = u;
                //loadPeaksFromHash();
            }
            $('input.ammount').toggle();
            var $this = $('#prominence_bt_showinmeters');
            if($this.hasClass('meters')){
                $this.removeClass('meters');
                $this.text('[m]');
                scale.setUnit('imperial');

                //reset prominence slider
                prominenceSlider.noUiSlider.updateOptions({
                    range: {
                        'min': 0,
                        '3.1%': 50,
                        '4.7%': 100,
                        '6.3%': 200,
                        '7.8%': 300,
                        '9.4%': 500,
                        '10.9%': 1000,
                        '12.5%': 1500,
                        '14.1%': 2000,
                        '15.6%': 2500,
                        '17.2%': 3000,
                        '18.8%': 3500,
                        '20.3%': 4000,
                        '21.9%': 4500,
                        '23.4%': 5000,
                        '25.0%': 5500,
                        '26.6%': 6000,
                        '28.1%': 6500,
                        '29.7%': 7000,
                        '31.3%': 7500,
                        '32.8%': 8000,
                        '34.4%': 8500,
                        '35.9%': 9000,
                        '37.5%': 9500,
                        '39.1%': 10000,
                        '40.6%': 10500,
                        '42.2%': 11000,
                        '43.8%': 11500,
                        '45.3%': 12000,
                        '46.9%': 12500,
                        '48.4%': 13000,
                        '50.0%': 13500,
                        '51.6%': 14000,
                        '53.1%': 14500,
                        '54.7%': 15000,
                        '56.3%': 15500,
                        '57.8%': 16000,
                        '59.4%': 16500,
                        '60.9%': 17000,
                        '62.5%': 17500,
                        '64.1%': 18000,
                        '65.6%': 18500,
                        '67.2%': 19000,
                        '68.8%': 19500,
                        '70.3%': 20000,
                        '71.9%': 20500,
                        '73.4%': 21000,
                        '75.0%': 21500,
                        '76.6%': 22000,
                        '78.1%': 22500,
                        '79.7%': 23000,
                        '81.3%': 23500,
                        '82.8%': 24000,
                        '84.4%': 24500,
                        '85.9%': 25000,
                        '87.5%': 25500,
                        '89.1%': 26000,
                        '90.6%': 26500,
                        '92.2%': 27000,
                        '93.8%': 27500,
                        '95.3%': 28000,
                        '96.9%': 28500,
                        '98.4%': 29000,
                        'max': 29500
                    }
                });
                prominenceSlider.noUiSlider.set([0, 29500]);
                $('#prominence-label').css('color','#aaa');
                $('#prominence-amount').css('color','#aaa');
                $('#prominence-amount').css('-webkit-text-fill-color','#aaa');
                $('#prominence-slider-range > div > div').css('background-color','#00b2f2');
                $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-blue.png)');
                $( "#prominence-amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29500) + " ft");
                $( "#prominence-amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(9000) + " m");
                u = updateURLParameter(window.location.hash, 'prom_min', 0);
                u = updateURLParameter('#'+u, 'prom_max', 29500);
                window.location.hash = u;
                //loadPeaksFromHash();
            } else {
                $this.addClass('meters');
                $this.text('[ft]');
                scale.setUnit('metric');

                //reset prominence slider
                prominenceSlider.noUiSlider.updateOptions({
                    range: {
                        'min': 0,
                        '2.1%': 10,
                        '3.2%': 20,
                        '4.2%': 30,
                        '5.3%': 50,
                        '6.3%': 100,
                        '7.4%': 200,
                        '8.4%': 300,
                        '9.5%': 400,
                        '10.5%': 500,
                        '11.6%': 600,
                        '12.6%': 700,
                        '13.7%': 800,
                        '14.7%': 900,
                        '15.8%': 1000,
                        '16.8%': 1100,
                        '17.9%': 1200,
                        '18.9%': 1300,
                        '20.0%': 1400,
                        '21.1%': 1500,
                        '22.1%': 1600,
                        '23.2%': 1700,
                        '24.2%': 1800,
                        '25.3%': 1900,
                        '26.3%': 2000,
                        '27.4%': 2100,
                        '28.4%': 2200,
                        '29.5%': 2300,
                        '30.5%': 2400,
                        '31.6%': 2500,
                        '32.6%': 2600,
                        '33.7%': 2700,
                        '34.7%': 2800,
                        '35.8%': 2900,
                        '36.8%': 3000,
                        '37.9%': 3100,
                        '38.9%': 3200,
                        '40.0%': 3300,
                        '41.1%': 3400,
                        '42.1%': 3500,
                        '43.2%': 3600,
                        '44.2%': 3700,
                        '45.3%': 3800,
                        '46.3%': 3900,
                        '47.4%': 4000,
                        '48.4%': 4100,
                        '49.5%': 4200,
                        '50.5%': 4300,
                        '51.6%': 4400,
                        '52.6%': 4500,
                        '53.7%': 4600,
                        '54.7%': 4700,
                        '55.8%': 4800,
                        '56.8%': 4900,
                        '57.9%': 5000,
                        '58.9%': 5100,
                        '60.0%': 5200,
                        '61.1%': 5300,
                        '62.1%': 5400,
                        '63.2%': 5500,
                        '64.2%': 5600,
                        '65.3%': 5700,
                        '66.3%': 5800,
                        '67.4%': 5900,
                        '68.4%': 6000,
                        '69.5%': 6100,
                        '70.5%': 6200,
                        '71.6%': 6300,
                        '72.6%': 6400,
                        '73.7%': 6500,
                        '74.7%': 6600,
                        '75.8%': 6700,
                        '76.8%': 6800,
                        '77.9%': 6900,
                        '78.9%': 7000,
                        '80.0%': 7100,
                        '81.1%': 7200,
                        '82.1%': 7300,
                        '83.2%': 7400,
                        '84.2%': 7500,
                        '85.3%': 7600,
                        '86.3%': 7700,
                        '87.4%': 7800,
                        '88.4%': 7900,
                        '89.5%': 8000,
                        '90.5%': 8100,
                        '91.6%': 8200,
                        '92.6%': 8300,
                        '93.7%': 8400,
                        '94.7%': 8500,
                        '95.8%': 8600,
                        '96.8%': 8700,
                        '97.9%': 8800,
                        '98.9%': 8900,
                        'max': 9000
                    }
                });
                prominenceSlider.noUiSlider.set([0, 9000]);
                $('#prominence-label').css('color','#aaa');
                $('#prominence-amount').css('color','#aaa');
                $('#prominence-amount').css('-webkit-text-fill-color','#aaa');
                $('#prominence-slider-range > div > div').css('background-color','#00b2f2');
                $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-blue.png)');
                $( "#prominence-amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29527) + " ft");
                $( "#prominence-amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(9000) + " m");
                u = updateURLParameter(window.location.hash, 'prom_min', 0);
                u = updateURLParameter('#'+u, 'prom_max', 29527);
                window.location.hash = u;
                //loadPeaksFromHash();
            }
            $('input.prominence-ammount').toggle();
            loadPeaksFromHash();
        });

        var prominenceSlider = document.getElementById('prominence-slider-range');

        noUiSlider.create(prominenceSlider, {
            start: [ 0, 29500 ],
            snap: true,
            connect: true,
            start: [wanted_prom_min, wanted_prom_max],
            range: {
                'min': 0,
                '3.1%': 50,
                '4.7%': 100,
                '6.3%': 200,
                '7.8%': 300,
                '9.4%': 500,
                '10.9%': 1000,
                '12.5%': 1500,
                '14.1%': 2000,
                '15.6%': 2500,
                '17.2%': 3000,
                '18.8%': 3500,
                '20.3%': 4000,
                '21.9%': 4500,
                '23.4%': 5000,
                '25.0%': 5500,
                '26.6%': 6000,
                '28.1%': 6500,
                '29.7%': 7000,
                '31.3%': 7500,
                '32.8%': 8000,
                '34.4%': 8500,
                '35.9%': 9000,
                '37.5%': 9500,
                '39.1%': 10000,
                '40.6%': 10500,
                '42.2%': 11000,
                '43.8%': 11500,
                '45.3%': 12000,
                '46.9%': 12500,
                '48.4%': 13000,
                '50.0%': 13500,
                '51.6%': 14000,
                '53.1%': 14500,
                '54.7%': 15000,
                '56.3%': 15500,
                '57.8%': 16000,
                '59.4%': 16500,
                '60.9%': 17000,
                '62.5%': 17500,
                '64.1%': 18000,
                '65.6%': 18500,
                '67.2%': 19000,
                '68.8%': 19500,
                '70.3%': 20000,
                '71.9%': 20500,
                '73.4%': 21000,
                '75.0%': 21500,
                '76.6%': 22000,
                '78.1%': 22500,
                '79.7%': 23000,
                '81.3%': 23500,
                '82.8%': 24000,
                '84.4%': 24500,
                '85.9%': 25000,
                '87.5%': 25500,
                '89.1%': 26000,
                '90.6%': 26500,
                '92.2%': 27000,
                '93.8%': 27500,
                '95.3%': 28000,
                '96.9%': 28500,
                '98.4%': 29000,
                'max': 29500
            }
        });

        prominenceSlider.noUiSlider.on('slide', function(values, handle, unencoded, tap, positions){
            $( "#prominence-amount" ).val( numberWithCommas(unencoded[0]) + "  to  " + numberWithCommas(unencoded[1]) + " ft");
            $( "#prominence-amount-meters" ).val(numberWithCommas(Math.floor(unencoded[0])) + "  to  " + numberWithCommas(Math.floor(unencoded[1])) + " m");
            //if meters
            if ($('#prominence-amount-meters').is(':visible')) {
                if (0 != unencoded[0] || 9000 != unencoded[1]) {
                    $('#prominence-label').css('color', '#f24100');
                    $('#prominence-amount').css('color', '#f24100');
                    $('#prominence-slider-range > div > div').css('background-color', '#f24100');
                    $('#prominence-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                } else {
                    $('#prominence-label').css('color', '#aaa');
                    $('#prominence-amount').css('color', '#aaa');
                    $('#prominence-amount').css('-webkit-text-fill-color','#aaa');
                    $('#prominence-slider-range > div > div').css('background-color', '#00b2f2');
                    $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-blue.png)');
                }
            } else {
                if (0 != unencoded[0] || 29500 != unencoded[1]) {
                    $('#prominence-label').css('color', '#f24100');
                    $('#prominence-amount').css('color', '#f24100');
                    $('#prominence-slider-range > div > div').css('background-color', '#f24100');
                    $('#prominence-slider-range .noUi-background').css('background-color', '#CCC');
                    $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
                } else {
                    $('#prominence-label').css('color', '#aaa');
                    $('#prominence-amount').css('color', '#aaa');
                    $('#prominence-amount').css('-webkit-text-fill-color','#aaa');
                    $('#prominence-slider-range > div > div').css('background-color', '#00b2f2');
                    $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-blue.png)');
                }
            }
        });
        prominenceSlider.noUiSlider.on('end', function(values, handle, unencoded, tap, positions){
            //if meters
            if ($('#prominence_bt_showinmeters').text() == '[ft]') {
                delayShowDataprominence("#prominence-amount", [unencoded[0]/0.3048,unencoded[1]/0.3048]);
            } else {
                delayShowDataprominence("#prominence-amount", unencoded);
            }
        });
        var prominenceValues = prominenceSlider.noUiSlider.get();
        $( "#prominence-amount" ).val( numberWithCommas(parseInt(prominenceValues[0])) + "  to  " + numberWithCommas(parseInt(prominenceValues[1])) + " ft");
        $( "#prominence-amount-meters" ).val(numberWithCommas(Math.floor(parseInt(prominenceValues[0]))) + "  to  " + numberWithCommas(parseInt(Math.floor(prominenceValues[1]))) + " m");

        $('#prominence_bt_showinmeters').click(function(event){
            event.preventDefault();

            //toggle map units
            if ($('#gm-custom-mapunitsbutton-label-meters').hasClass('gm-custom-mapunits-selected')) {
                toggleMapUnits('feet');
            } else {
                toggleMapUnits('meters');
            }

            var $this = $('#prominence_bt_showinmeters');
            if($this.hasClass('meters')){
                $this.removeClass('meters');
                $this.text('[m]');
                scale.setUnit('imperial');

                //reset prominence slider
                prominenceSlider.noUiSlider.updateOptions({
                    range: {
                        'min': 0,
                        '3.1%': 50,
                        '4.7%': 100,
                        '6.3%': 200,
                        '7.8%': 300,
                        '9.4%': 500,
                        '10.9%': 1000,
                        '12.5%': 1500,
                        '14.1%': 2000,
                        '15.6%': 2500,
                        '17.2%': 3000,
                        '18.8%': 3500,
                        '20.3%': 4000,
                        '21.9%': 4500,
                        '23.4%': 5000,
                        '25.0%': 5500,
                        '26.6%': 6000,
                        '28.1%': 6500,
                        '29.7%': 7000,
                        '31.3%': 7500,
                        '32.8%': 8000,
                        '34.4%': 8500,
                        '35.9%': 9000,
                        '37.5%': 9500,
                        '39.1%': 10000,
                        '40.6%': 10500,
                        '42.2%': 11000,
                        '43.8%': 11500,
                        '45.3%': 12000,
                        '46.9%': 12500,
                        '48.4%': 13000,
                        '50.0%': 13500,
                        '51.6%': 14000,
                        '53.1%': 14500,
                        '54.7%': 15000,
                        '56.3%': 15500,
                        '57.8%': 16000,
                        '59.4%': 16500,
                        '60.9%': 17000,
                        '62.5%': 17500,
                        '64.1%': 18000,
                        '65.6%': 18500,
                        '67.2%': 19000,
                        '68.8%': 19500,
                        '70.3%': 20000,
                        '71.9%': 20500,
                        '73.4%': 21000,
                        '75.0%': 21500,
                        '76.6%': 22000,
                        '78.1%': 22500,
                        '79.7%': 23000,
                        '81.3%': 23500,
                        '82.8%': 24000,
                        '84.4%': 24500,
                        '85.9%': 25000,
                        '87.5%': 25500,
                        '89.1%': 26000,
                        '90.6%': 26500,
                        '92.2%': 27000,
                        '93.8%': 27500,
                        '95.3%': 28000,
                        '96.9%': 28500,
                        '98.4%': 29000,
                        'max': 29500
                    }
                });
                prominenceSlider.noUiSlider.set([0, 29500]);
                $('#prominence-label').css('color','#aaa');
                $('#prominence-amount').css('color','#aaa');
                $('#prominence-amount').css('-webkit-text-fill-color','#aaa');
                $('#prominence-slider-range > div > div').css('background-color','#00b2f2');
                $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-blue.png)');
                $( "#prominence-amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29500) + " ft");
                $( "#prominence-amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(9000) + " m");
                u = updateURLParameter(window.location.hash, 'prom_min', 0);
                u = updateURLParameter('#'+u, 'prom_max', 29500);
                window.location.hash = u;
                //loadPeaksFromHash();
            } else {
                $this.addClass('meters');
                $this.text('[ft]');
                scale.setUnit('metric');

                //reset prominence slider
                prominenceSlider.noUiSlider.updateOptions({
                    range: {
                        'min': 0,
                        '2.1%': 10,
                        '3.2%': 20,
                        '4.2%': 30,
                        '5.3%': 50,
                        '6.3%': 100,
                        '7.4%': 200,
                        '8.4%': 300,
                        '9.5%': 400,
                        '10.5%': 500,
                        '11.6%': 600,
                        '12.6%': 700,
                        '13.7%': 800,
                        '14.7%': 900,
                        '15.8%': 1000,
                        '16.8%': 1100,
                        '17.9%': 1200,
                        '18.9%': 1300,
                        '20.0%': 1400,
                        '21.1%': 1500,
                        '22.1%': 1600,
                        '23.2%': 1700,
                        '24.2%': 1800,
                        '25.3%': 1900,
                        '26.3%': 2000,
                        '27.4%': 2100,
                        '28.4%': 2200,
                        '29.5%': 2300,
                        '30.5%': 2400,
                        '31.6%': 2500,
                        '32.6%': 2600,
                        '33.7%': 2700,
                        '34.7%': 2800,
                        '35.8%': 2900,
                        '36.8%': 3000,
                        '37.9%': 3100,
                        '38.9%': 3200,
                        '40.0%': 3300,
                        '41.1%': 3400,
                        '42.1%': 3500,
                        '43.2%': 3600,
                        '44.2%': 3700,
                        '45.3%': 3800,
                        '46.3%': 3900,
                        '47.4%': 4000,
                        '48.4%': 4100,
                        '49.5%': 4200,
                        '50.5%': 4300,
                        '51.6%': 4400,
                        '52.6%': 4500,
                        '53.7%': 4600,
                        '54.7%': 4700,
                        '55.8%': 4800,
                        '56.8%': 4900,
                        '57.9%': 5000,
                        '58.9%': 5100,
                        '60.0%': 5200,
                        '61.1%': 5300,
                        '62.1%': 5400,
                        '63.2%': 5500,
                        '64.2%': 5600,
                        '65.3%': 5700,
                        '66.3%': 5800,
                        '67.4%': 5900,
                        '68.4%': 6000,
                        '69.5%': 6100,
                        '70.5%': 6200,
                        '71.6%': 6300,
                        '72.6%': 6400,
                        '73.7%': 6500,
                        '74.7%': 6600,
                        '75.8%': 6700,
                        '76.8%': 6800,
                        '77.9%': 6900,
                        '78.9%': 7000,
                        '80.0%': 7100,
                        '81.1%': 7200,
                        '82.1%': 7300,
                        '83.2%': 7400,
                        '84.2%': 7500,
                        '85.3%': 7600,
                        '86.3%': 7700,
                        '87.4%': 7800,
                        '88.4%': 7900,
                        '89.5%': 8000,
                        '90.5%': 8100,
                        '91.6%': 8200,
                        '92.6%': 8300,
                        '93.7%': 8400,
                        '94.7%': 8500,
                        '95.8%': 8600,
                        '96.8%': 8700,
                        '97.9%': 8800,
                        '98.9%': 8900,
                        'max': 9000
                    }
                });
                prominenceSlider.noUiSlider.set([0, 9000]);
                $('#prominence-label').css('color','#aaa');
                $('#prominence-amount').css('color','#aaa');
                $('#prominence-amount').css('-webkit-text-fill-color','#aaa');
                $('#prominence-slider-range > div > div').css('background-color','#00b2f2');
                $('#prominence-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-blue.png)');
                $( "#prominence-amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29527) + " ft");
                $( "#prominence-amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(9000) + " m");
                u = updateURLParameter(window.location.hash, 'prom_min', 0);
                u = updateURLParameter('#'+u, 'prom_max', 29527);
                window.location.hash = u;
                //loadPeaksFromHash();
            }
            $('input.prominence-ammount').toggle();
            var $this = $('#bt_showinmeters');
            if($this.hasClass('meters')){
                $this.removeClass('meters');
                $this.text('[m]');
                scale.setUnit('imperial');

                //reset elevation slider
                elevationSlider.noUiSlider.updateOptions({
                    range: {
                        'min': 0,
                        '3.3%': 500,
                        '5.0%': 1000,
                        '6.7%': 1500,
                        '8.3%': 2000,
                        '10.0%': 2500,
                        '11.7%': 3000,
                        '13.3%': 3500,
                        '15.0%': 4000,
                        '16.7%': 4500,
                        '18.3%': 5000,
                        '20.0%': 5500,
                        '21.7%': 6000,
                        '23.3%': 6500,
                        '25.0%': 7000,
                        '26.7%': 7500,
                        '28.3%': 8000,
                        '30.0%': 8500,
                        '31.7%': 9000,
                        '33.3%': 9500,
                        '35.0%': 10000,
                        '36.7%': 10500,
                        '38.3%': 11000,
                        '40.0%': 11500,
                        '41.7%': 12000,
                        '43.3%': 12500,
                        '45.0%': 13000,
                        '46.7%': 13500,
                        '48.3%': 14000,
                        '50.0%': 14500,
                        '51.7%': 15000,
                        '53.3%': 15500,
                        '55.0%': 16000,
                        '56.7%': 16500,
                        '58.3%': 17000,
                        '60.0%': 17500,
                        '61.7%': 18000,
                        '63.3%': 18500,
                        '65.0%': 19000,
                        '66.7%': 19500,
                        '68.3%': 20000,
                        '70.0%': 20500,
                        '71.7%': 21000,
                        '73.3%': 21500,
                        '75.0%': 22000,
                        '76.7%': 22500,
                        '78.3%': 23000,
                        '80.0%': 23500,
                        '81.7%': 24000,
                        '83.3%': 24500,
                        '85.0%': 25000,
                        '86.7%': 25500,
                        '88.3%': 26000,
                        '90.0%': 26500,
                        '91.7%': 27000,
                        '93.3%': 27500,
                        '95.0%': 28000,
                        '96.7%': 28500,
                        '98.3%': 29000,
                        'max': 29500
                    }
                });
                elevationSlider.noUiSlider.set([0, 29500]);
                $('#elevation-label').css('color','#aaa');
                $('#amount').css('color','#aaa');
                $('#amount').css('-webkit-text-fill-color','#aaa');
                $('#slider-range > div > div').css('background-color','#00b2f2');
                $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-blue.png)');
                $( "#amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29500) + " ft");
                $( "#amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(9000) + " m");
                u = updateURLParameter(window.location.hash, 'elev_min', 0);
                u = updateURLParameter('#'+u, 'elev_max', 29500);
                window.location.hash = u;
                //loadPeaksFromHash();
            } else {
                $this.addClass('meters');
                $this.text('[ft]');
                scale.setUnit('metric');

                //reset elevation slider
                elevationSlider.noUiSlider.updateOptions({
                    range: {
                        'min': 0,
                        '4.3%': 200,
                        '6.5%': 400,
                        '8.7%': 600,
                        '10.9%': 800,
                        '13.0%': 1000,
                        '15.2%': 1200,
                        '17.4%': 1400,
                        '19.6%': 1600,
                        '21.7%': 1800,
                        '23.9%': 2000,
                        '26.1%': 2200,
                        '28.3%': 2400,
                        '30.4%': 2600,
                        '32.6%': 2800,
                        '34.8%': 3000,
                        '37.0%': 3200,
                        '39.1%': 3400,
                        '41.3%': 3600,
                        '43.5%': 3800,
                        '45.7%': 4000,
                        '47.8%': 4200,
                        '50.0%': 4400,
                        '52.2%': 4600,
                        '54.3%': 4800,
                        '56.5%': 5000,
                        '58.7%': 5200,
                        '60.9%': 5400,
                        '63.0%': 5600,
                        '65.2%': 5800,
                        '67.4%': 6000,
                        '69.6%': 6200,
                        '71.7%': 6400,
                        '73.9%': 6600,
                        '76.1%': 6800,
                        '78.3%': 7000,
                        '80.4%': 7200,
                        '82.6%': 7400,
                        '84.8%': 7600,
                        '87.0%': 7800,
                        '89.1%': 8000,
                        '91.3%': 8200,
                        '93.5%': 8400,
                        '95.7%': 8600,
                        '97.8%': 8800,
                        'max': 9000
                    }
                });
                elevationSlider.noUiSlider.set([0, 9000]);
                $('#elevation-label').css('color','#aaa');
                $('#amount').css('color','#aaa');
                $('#amount').css('-webkit-text-fill-color','#aaa');
                $('#slider-range > div > div').css('background-color','#00b2f2');
                $('#slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-blue.png)');
                $( "#amount" ).val( numberWithCommas(0) + "  to  " + numberWithCommas(29527) + " ft");
                $( "#amount-meters" ).val(numberWithCommas(0) + "  to  " + numberWithCommas(9000) + " m");
                u = updateURLParameter(window.location.hash, 'elev_min', 0);
                u = updateURLParameter('#'+u, 'elev_max', 29527);
                window.location.hash = u;
                //loadPeaksFromHash();
            }
            $('input.ammount').toggle();
            loadPeaksFromHash();
        });

        var summitsSlider = document.getElementById('summits-slider-range');

        noUiSlider.create(summitsSlider, {
            start: [ 0, 500 ],
            snap: true,
            connect: true,
            start: [wanted_summits_min, wanted_summits_max],
            range: {
                'min': 0,
                '12.5%': 1,
                '18.8%': 2,
                '25.0%': 3,
                '31.3%': 4,
                '37.5%': 5,
                '43.8%': 10,
                '50.0%': 20,
                '56.3%': 30,
                '62.5%': 40,
                '68.8%': 50,
                '75.0%': 100,
                '81.3%': 200,
                '87.5%': 300,
                '93.8%': 400,
                'max': 500
            }
        });

        summitsSlider.noUiSlider.on('slide', function(values, handle, unencoded, tap, positions){
            $( "#summits-amount" ).val( unencoded[0] + "  to  " + unencoded[1] );
            if (unencoded[1] == 500) {
                $( "#summits-amount" ).val( unencoded[0] + "  to  " + unencoded[1] + "+" );
            }
            if (0 != unencoded[0] || 500 != unencoded[1]) {
                $('#summits-label').css('color','#f24100');
                $('#summits-amount').css('color','#f24100');
                $('#summits-slider-range > div > div').css('background-color','#f24100');
                $('#summits-slider-range .noUi-background').css('background-color', '#CCC');
                $('#summits-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
            } else {
                $('#summits-label').css('color','#aaa');
                $('#summits-amount').css('color','#aaa');
                $('#summits-amount').css('-webkit-text-fill-color','#aaa');
                $('#summits-slider-range > div > div').css('background-color','#00b2f2');
                $('#summits-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-blue.png)');
            }
        });
        summitsSlider.noUiSlider.on('end', function(values, handle, unencoded, tap, positions){
            delayShowDataSummits("#summits-amount", unencoded);
        });
        var summitsValues = summitsSlider.noUiSlider.get();
        $( "#summits-amount" ).val( parseInt(summitsValues[0]) + "  to  " + parseInt(summitsValues[1]) );
        if (summitsValues[1] == 500) {
            $( "#summits-amount" ).val( parseInt(summitsValues[0]) + "  to  " + parseInt(summitsValues[1]) + "+");
        }

        var difficultySlider = document.getElementById('difficulty-slider-range');

        noUiSlider.create(difficultySlider, {
            start: [ 1, 5 ],
            snap: true,
            connect: true,
            start: [wanted_difficulty_min, wanted_difficulty_max],
            range: {
                'min': 1,
                '40.0%': 2,
                '60.0%': 3,
                '80.0%': 4,
                'max': 5
            }
        });

        difficultySlider.noUiSlider.on('slide', function(values, handle, unencoded, tap, positions){
            $( "#difficulty-amount" ).val( "Class " + unencoded[0] + "  to  " + unencoded[1] );
            if (1 != unencoded[0] || 5 != unencoded[1]) {
                $('#difficulty-label').css('color','#f24100');
                $('#difficulty-amount').css('color','#f24100');
                $('#difficulty-slider-range > div > div').css('background-color','#f24100');
                $('#difficulty-slider-range .noUi-background').css('background-color', '#CCC');
                $('#difficulty-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-red.png)');
            } else {
                $('#difficulty-label').css('color','#aaa');
                $('#difficulty-amount').css('color','#aaa');
                $('#difficulty-amount').css('-webkit-text-fill-color','#aaa');
                $('#difficulty-slider-range > div > div').css('background-color','#00b2f2');
                $('#difficulty-slider-range > div > div > div').css('background-image', 'url({% static '' %}img/search-drag-handle-blue.png)');
            }
        });
        difficultySlider.noUiSlider.on('end', function(values, handle, unencoded, tap, positions){
            delayShowDataDifficulty("#difficulty-amount", unencoded);
        });
        var difficultyValues = difficultySlider.noUiSlider.get();
        $( "#difficulty-amount" ).val( "Class " + parseInt(difficultyValues[0]) + "  to  " + parseInt(difficultyValues[1]) );

        $('#peak-search-mobile').on('click', function(){
           $('#peak-search').addClass('modal fade right');
           $('#peak-search').attr('style','left: auto; right: 0px; margin-top: 0px; width: 240px;');
           $('#peak-search').removeClass('hidden-xs');
            $('#peak-search').removeClass('hidden-sm');
           $('#peak-search').modal('toggle');
           $('#mobile-collapse-nav').show();
           var height = $(window).height();
           var width = $(window).width() - 240;
           $('#mobile-collapse-nav').width(width);
           $('#mobile-collapse-nav').height(height);
        });

        $('#mobile-collapse-nav').on('click', function(){
            $('#peak-search').modal('hide');
        });

        $('#location-search-mobile').on('click', function(){
            $('#location-search-mobile').html('<i class="fa fa-spinner fa-spin"></i>');
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(showLocalPeaks, showLocationError);
            } else {
                $('#location-search-mobile').html('<i class="fa fa-location-arrow"></i>');
                $('#mobile-search-title').html('Geolocation not supported');
            }
        });

        $('#marker-tooltip').on('click', function(){
            window.location = $(this).data('url');
        });

        //Custom Google Map type stuff

        $('#gm-custom-mapbutton').on('mouseenter', function(){
           $('#gm-custom-mapdropdown').show();
        });

        $('#gm-custom-mapbutton').on('mouseleave', function(){
           $('#gm-custom-mapdropdown').hide();
        });

        $('#gm-custom-mapbutton').on('touchstart', function() {
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-satstreets').on('click', function() {
            toggleMapType('satellite');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-satstreets').on('touchstart', function() {
            toggleMapType('satellite');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-satstreets').on('mouseenter', function() {
            $('#gm-custom-mapdropdown').show();
        });

        $('#gm-custom-mapoption-satstreets').on('mouseleave', function() {
            $('#gm-custom-mapdropdown').hide();
        });

        $('#gm-custom-mapoption-topo').on('click', function() {
            toggleMapType('caltopo');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-topo').on('touchstart', function() {
            toggleMapType('caltopo');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-topo').on('mouseenter', function() {
            $('#gm-custom-mapdropdown').show();
        });

        $('#gm-custom-mapoption-topo').on('mouseleave', function() {
            $('#gm-custom-mapdropdown').hide();
        });

        $('#gm-custom-mapoption-sat').on('click', function() {
            toggleMapType('sat_topo');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-sat').on('touchstart', function() {
            toggleMapType('sat_topo');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-sat').on('mouseenter', function() {
            $('#gm-custom-mapdropdown').show();
        });

        $('#gm-custom-mapoption-sat').on('mouseleave', function() {
            $('#gm-custom-mapdropdown').hide();
        });

        $('#gm-custom-mapoption-terrain').on('click', function() {
            toggleMapType('terrain');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-terrain').on('touchstart', function() {
            toggleMapType('terrain');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-terrain').on('mouseenter', function() {
            $('#gm-custom-mapdropdown').show();
        });

        $('#gm-custom-mapoption-terrain').on('mouseleave', function() {
            $('#gm-custom-mapdropdown').hide();
        });

        $('#gm-custom-mapoption-outdoors').on('click', function() {
            toggleMapType('outdoors');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-outdoors').on('touchstart', function() {
            toggleMapType('outdoors');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-outdoors').on('mouseenter', function() {
            $('#gm-custom-mapdropdown').show();
        });

        $('#gm-custom-mapoption-outdoors').on('mouseleave', function() {
            $('#gm-custom-mapdropdown').hide();
        });

        //Natural atlas stuff
        $('#gm-custom-mapoption-natatl').on('click', function() {
            toggleMapType('natural_atlas');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-natatl').on('touchstart', function() {
            toggleMapType('natural_atlas');
            $('#gm-custom-mapdropdown').toggle();
        });

        $('#gm-custom-mapoption-natatl').on('mouseenter', function() {
            $('#gm-custom-mapdropdown').show();
        });

        $('#gm-custom-mapoption-natatl').on('mouseleave', function() {
            $('#gm-custom-mapdropdown').hide();
        });

        function showLocalPeaks(position) {
            $('#location-search-mobile').html('<i class="fa fa-location-arrow"></i>');
            $('#mobile-search-title').html('Peaks near you');
            wanted_lat = position.coords.latitude;
            wanted_lng = position.coords.longitude;
            $('#hdnLat').val(wanted_lat);
            $('#hdnLng').val(wanted_lng);
            u = updateURLParameter('#', 'lat', wanted_lat);
            u = updateURLParameter('#'+u, 'lng', wanted_lng);
            window.location.hash = u;
            //recenter map on lat/lng
            map.panTo([wanted_lng, wanted_lat]);
        }

        function showLocationError(error) {
            $('#location-search-mobile').html('<i class="fa fa-location-arrow"></i>');
            switch(error.code) {
                case error.PERMISSION_DENIED:
                    $('#mobile-search-title').html('Geolocation permission denied');
                    break;
                case error.POSITION_UNAVAILABLE:
                    $('#mobile-search-title').html('Location information unavailable');
                    break;
                case error.TIMEOUT:
                    $('#mobile-search-title').html('Location request timed out');
                    break;
                case error.UNKNOWN_ERROR:
                    $('#mobile-search-title').html('An unknown error occurred');
                    break;
            }
        }

        $('#peak-search').on('hide.bs.modal', function (e) {
          $('#peak-search').removeClass('modal fade right');
        });

        $('#peak-search').on('hidden.bs.modal', function (e) {
          $('#peak-search').attr('style','left: 0px; right: auto; width: 240px; position: absolute; display: inline-block;');
          $('#peak-search').addClass('hidden-xs');
          $('#peak-search').addClass('hidden-sm');
          $('#mobile-collapse-nav').hide();
        })

        $('#explore').on('submit', function(e) {
            e.preventDefault();
            //reset filters
            resetSliders();
            $('#search-peaks-btn').blur();
            if ($('#n').val() != '') {
                $('#hdnNearQuery').val('true');
            }
            $('#peak-search').modal('hide');
            delayShowKeywordNear("#keyword-search", $('#q').val(), $('#n').val());
        });

        document.addEventListener('keyup', function(e) {
            if (e.keyCode == 27) {
                $('#peak-search').modal('hide');
            }
        });

    });

    function getUrlVars()
    {
        var vars = [], hash;
        var hashes = window.location.href.slice(window.location.href.indexOf('?') + 1).split('&');
        for(var i = 0; i < hashes.length; i++)
        {
            hash = hashes[i].split('=');
            vars.push(hash[0]);
            vars[hash[0]] = hash[1];
        }
        return vars;
    }

    function fix_item_location(id,point){
        $.post('{% url "fix_item_location" %}', {id:id, lat:point.lat(), long:point.lng()},
                function(data) {
                    var output = $("#message_map");
                    var output_div = $("#message_map_div");
                    output.html(data);
                    output_div.show();
                    setTimeout(function() {
                        $("#message_map_div").hide();
                    }, 10000)
                }
        );
    }

    function delete_peak_from_map (id){
        $.post('{% url "delete_peak_from_map" %}', {id:id},
                function(data) {
                    var output = $("#message_map");
                    var output_div = $("#message_map_div");
                    output.html(data);
                    output_div.show();
                    setTimeout(function() {
                        $("#message_map_div").hide();
                    }, 10000)
                }
        );
    }

    function check_is_in(marker){
        return map.getBounds().contains(marker.getPosition());
    }

    function delete_out_markers(){
        if (markersArray){
            for (i in markersArray){
                if (!check_is_in(markersArray[i])){
                    markersArray[i].remove();
                    markersArray.splice(i,1);
                }
            }
        }
    }

    function deletehighest(){
        if (markersArray){
            for (i in markersArray){
                if (markersArray[i].properties.iconstyle == 'marker_icon_redgreen' || markersArray[i].properties.iconstyle == 'marker_icon_red'){
                    markersArray[i].remove();
                    markersArray.splice(i,1);
                }
            }
        }
    }

    function limit_number_of_markers(limit){
        if (markersArray.length > limit){
            for (i = markersArray.length-1; i>=limit; i--){
                markersArray[i].remove();
                markersArray.splice(i,1);
            }
        }
    }

    function elevation_range(data){
        if (markersArray){
            for (i = markersArray.length-1; i>=0; i--){
                var inrange = false;
                $.each(data, function(k, v){
                    var latLng1 = new mapboxgl.LngLat(v.lng, v.lat);
                    if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng1)){
                        inrange = true;
                    }
                });
                if (!inrange){
                    markersArray[i].remove();
                    markersArray.splice(i,1);
                }
            }
        }
    }

    function delete_old_markers(data){
        if (markersArray){
            for (i = markersArray.length-1; i>=0; i--){
                var inrange = false;
                $.each(data, function(k, v){
                    var latLng1 = new mapboxgl.LngLat(v.lng, v.lat);
                    if (fromLatLngToString(markersArray[i].getLngLat()) == fromLatLngToString(latLng1)){
                        inrange = true;
                    }
                });
                if (!inrange){
                    markersArray[i].remove();
                    markersArray.splice(i,1);
                }
            }
        }
    }

    function fromLatLngToString(latLng) {
        return latLng.lat + ',' + latLng.lng;
    }

    </script>

{% endblock %}