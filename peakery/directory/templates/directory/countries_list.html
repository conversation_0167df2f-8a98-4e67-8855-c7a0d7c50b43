{% extends "base.html" %}
{% load directory_tags %}
{% load static %}

{% block title %}{% if continent %}{{ continent.name }}{% else %}World{% endif %} mountains{% endblock %}

{% block titlemeta_overwrite %}{% if continent %}{{ continent.name }}{% else %}World{% endif %} mountains{% endblock %}
{% block description %}{{ meta_description }}{% endblock %}

{% block directory_active %}active{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row hidden-xs">
        <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 65px; border-radius: 12px 12px 0 0;"><span style="font-size: 24px; font-weight: 600;">
            <div id="breadcrumbs">
                <ul>
                    <li class="hidden-sm hidden-xs"><a style="font-weight: 500;" href="{% url "world_list" %}">World</a> &raquo; </li>
                    <li><h1 class="regions-title">{{ continent.name }} mountains</h1></li>
                </ul>
                <div class="pull-right hidden-xs" style="margin-top: -5px;">
                    <a style="{{ subnav_info_style }}" class="region-header-sub-links" href="/region/{{ continent_slug }}-mountains/">Info</a><a style="{{ subnav_map_style }}" class="region-header-sub-links" href="/region/{{ continent_slug }}-mountains/map/">Map</a><a style="{{ subnav_peaks_style }}" class="region-header-sub-links" href="/region/{{ continent_slug }}-mountains/peaks/">Peaks</a><a style="{{ subnav_summits_style }}" class="region-header-sub-links" href="/region/{{ continent_slug }}-mountains/summits/">Climbs</a><a style="{{ subnav_challenges_style }}" class="region-header-sub-links" href="/region/{{ continent_slug }}-mountains/challenges/">Challenges</a>
                </div>
            </div>
        </span></div>
    </div>
{% endblock %}

{% block content %}


<style>

   @media screen and (max-width: 767px) and (min-width: 1px) {
       #content-body {
           margin-top: 20px;
       }
       .content-pane {
           margin-top: 50px;
       }
       .stats-data-bottom, .stats-data-highlight {
            margin-left: -6px;
        }
        .stats-data-highlight {
            margin-bottom: 5px;
            line-height: 20px;
        }
        .stats-data {
            margin-bottom: 5px;
        }
        .stats-header {
           font-size: 14px;
           margin-bottom: 10px;
       }
        .region-list-region {
            margin-bottom: 20px;
        }
        .stats-grid-1 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-2 {
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-3 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-4 {
            border-bottom: 1px solid #c0c0c0;
        }
   }
   @media screen and (max-width: 1023px) and (min-width: 768px) {
        .stats-data {
            margin-bottom: 10px;
        }
       .stats-small-grid-1 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-small-grid-2 {
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-small-grid-3 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-small-grid-4 {
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-small-grid-5 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-small-grid-6 {
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-small-grid-7 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-small-grid-8 {
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-1 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-2 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-3 {
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-4 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-5 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-6 {
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-7 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-8 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
    }
    @media screen and (min-width: 1024px) {
        .stats-data {
            margin-bottom: 10px;
        }
        .stats-small-grid-1 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-small-grid-2 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-small-grid-3 {
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-small-grid-4 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-small-grid-5 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-small-grid-6 {
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-small-grid-7 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-small-grid-8 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-small-grid-9 {
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-1 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-2 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-3 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-4 {
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-5 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-6 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-7 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-8 {
            border-bottom: 1px solid #c0c0c0;
        }
    }
    @media screen and (min-width: 768px) {
        .content-pane {
           margin-top: 0px;
       }
        .region-list-region {
            margin-bottom: 20px;
        }
    }

   .hover-cell:hover, .table-hover-cell:hover {
        background-image: linear-gradient(to bottom,#fde1d6,#fde1d6) !important;
    }

    @media screen and (max-width: 767px) and (min-width: 1px) {
        .featured-logs-thumbnail {
            display: none;
        }
    }

    @media screen and (min-width: 768px) {
        .featured-logs-thumbnail {
            float: left;
            width: 100px;
        }
        .featured-logs-description {
            margin-left: 120px;
        }
    }

   #regions_div {cursor: pointer;}
   #regions_div_tablet {cursor: pointer;}
   #regions_div path[fill="#ffffff"] {cursor: auto}
   #regions_div_tablet path[fill="#ffffff"] {cursor: auto}
   #regions_div path[fill="#f5f5f5"] {cursor: auto}
   #regions_div_tablet path[fill="#f5f5f5"] {cursor: auto}
</style>

<div class="container">
    <!-- Mobile header -->
    <div class="row sub-header-row hidden-lg hidden-md hidden-sm" style="height: 50px; position: fixed; width: 100%; z-index: 2;">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999; display: flex; justify-content: space-between;">
            <a style="{{ subnav_info_style }}" class="mobile-header-sub-links" href="/region/{{ continent_slug }}-mountains/">Info</a><a style="{{ subnav_map_style }}" class="mobile-header-sub-links" href="/region/{{ continent_slug }}-mountains/map/">Map</a><a style="{{ subnav_peaks_style }}" class="mobile-header-sub-links" href="/region/{{ continent_slug }}-mountains/peaks/">Peaks</a><a style="{{ subnav_summits_style }}" class="mobile-header-sub-links" href="/region/{{ continent_slug }}-mountains/summits/">Climbs</a><a style="{{ subnav_challenges_style }}" class="mobile-header-sub-links" href="/region/{{ continent_slug }}-mountains/challenges/">Challenges</a>
        </div>
    </div>
    <!-- End mobile header -->

    <!-- Top photos block -->
    <div class="row content-pane">
        {% for p in top_peaks|slice:"0:1" %}
            <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 {{ p.hover_class }}" onclick="openUrl('/{{ p.slug }}');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ MEDIA_URL }}{{ p.thumbnail_url }}'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #fff;">
                <div>
                    <a href="/{{ p.slug }}/"><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></a>
                    <div class="{{ p.info_class }}">
                        <span class="photo-caption-peakname-only" style="position: absolute; bottom: 5px; left: 10px; color: #fff;">
                            <div class="bagger ellipsis"><a href="/{{ p.slug }}/" style="color: #fff;">{{ p.name }}</a></div>
                        </span>
                    </div>
                </div>
            </div>
        {% endfor %}
        {% for p in top_peaks|slice:"1:2" %}
            <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 {{ p.hover_class }}" onclick="openUrl('/{{ p.slug }}');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ MEDIA_URL }}{{ p.thumbnail_url }}'); background-size: cover; background-position: center center; background-repeat: no-repeat;">
                <div>
                    <a href="/{{ p.slug }}/"><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></a>
                    <div class="{{ p.info_class }}">
                        <span class="photo-caption-peakname-only" style="position: absolute; bottom: 5px; left: 10px; color: #fff;">
                            <div class="bagger ellipsis"><a href="/{{ p.slug }}/" style="color: #fff;">{{ p.name }}</a></div>
                        </span>
                    </div>
                </div>
            </div>
        {% endfor %}
        {% for p in top_peaks|slice:"2:3" %}
            <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 hidden-xs hidden-sm {{ p.hover_class }}" onclick="openUrl('/{{ p.slug }}');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ MEDIA_URL }}{{ p.thumbnail_url }}'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-left: solid 1px #fff;">
                <div>
                    <a href="/{{ p.slug }}/"><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></a>
                    <div class="{{ p.info_class }}">
                        <span class="photo-caption-peakname-only" style="position: absolute; bottom: 5px; left: 10px; color: #fff;">
                            <div class="bagger ellipsis"><a href="/{{ p.slug }}/" style="color: #fff;">{{ p.name }}</a></div>
                        </span>
                    </div>
                </div>
            </div>
        {% endfor %}
        {% for p in top_peaks|slice:"3:4" %}
            <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 hidden-xs {{ p.hover_class }}" onclick="openUrl('/{{ p.slug }}');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ MEDIA_URL }}{{ p.thumbnail_url }}'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-left: solid 1px #fff;">
                <div>
                    <a href="/{{ p.slug }}/"><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></a>
                    <div class="{{ p.info_class }}">
                        <span class="photo-caption-peakname-only" style="position: absolute; bottom: 5px; left: 10px; color: #fff;">
                            <div class="bagger ellipsis"><a href="/{{ p.slug }}/" style="color: #fff;">{{ p.name }}</a></div>
                        </span>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
    <!-- End top photos block -->

    <!-- Tablet geochart -->
    <div class="hidden-lg hidden-md col-sm-12 hidden-xs" style="padding-right: 0px; padding-left: 0px;">
        <div class="row border-bottom">
            <div id="regions_div_tablet" style="width: 100%; height: 100%;"></div>
        </div>
    </div>
    <!-- End tablet geochart -->

    <!-- Regions and stats -->
    <div class="row sub-header-row hidden-xs">
        <div class="col-lg-3 col-md-3 col-sm-4">
            <div class="row" id="ajax-data-loading" style="display: none;">
              <div class="col-md-12" style="text-align: center; color: #c0c0c0; margin-top: 20px; margin-bottom: 20px;">
                <i class="fa fa-spinner fa-spin fa-5x"></i>
              </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div id="countries-list-header"></div>
                </div>
            </div>
            <ul class="vert peaks_w_list" id="countries-list">
            </ul>
        </div>
        <div class="col-lg-9 col-md-9 col-sm-8 border-left">
            <div class="row border-bottom hidden-sm">
                <div id="regions_div" style="width: 100%; height: 100%;"></div>
            </div>
            <div class="row">
                <div onclick="openUrl('/region/{{ continent_slug }}-mountains/peaks/');" class="col-lg-4 col-md-4 col-sm-6 peakimg-responsive hover-cell stats-small-grid-1" style="cursor: pointer; max-height: 200px;">
                    <div>
                        <h2 class="stats-header">{{ continent.name }} peaks</h2>
                        <p class="stats-data-highlight"><a href="/region/{{ continent_slug }}-mountains/peaks/">{{ peak_count }} peak{{ peak_count_raw|pluralize:"s" }}</a></p>
                    </div>
                </div>
                <div onclick="openUrl('/{{ highest_peak.slug }}');" class="col-lg-4 col-md-4 col-sm-6 peakimg-responsive hover-cell stats-small-grid-2" style="cursor: pointer; max-height: 200px;">
                    <div>
                        <h2 class="stats-header">Highest peak</h2>
                        <p class="stats-data-highlight"><a href="/{{ highest_peak.slug }}/">{{ highest_peak.name }}</a></p>
                        <p class="stats-data">{{ highest_peak_elevation }} ft / {{ highest_peak_elevation_in_meters }} m</p>
                    </div>
                </div>
                {% if most_summited_peak_summits != '0' %}
                <div onclick="openUrl('/{{ most_summited_peak.slug }}');" class="col-lg-4 col-md-4 col-sm-6 peakimg-responsive hover-cell stats-small-grid-3" style="cursor: pointer; max-height: 200px;">
                    <div>
                        <h2 class="stats-header">Most climbed peak</h2>
                        <p class="stats-data-highlight"><a href="/{{ most_summited_peak.slug }}/">{{ most_summited_peak.name }}</a></p>
                        <p class="stats-data">{{ most_summited_peak_summits }} climbs</p>
                    </div>
                </div>
                {% else %}
                <div class="col-lg-4 col-md-4 col-sm-6 peakimg-responsive hover-cell stats-small-grid-3" style="cursor: pointer; max-height: 200px;">
                    <div>
                        <h2 class="stats-header">Most climbed peak</h2>
                        <p class="stats-data-missing">no climbs yet</p>
                    </div>
                </div>
                {% endif %}
                {% if most_prominent_peak.slug %}
                <div onclick="openUrl('/{{ most_prominent_peak.slug }}');" class="col-lg-4 col-md-4 col-sm-6 peakimg-responsive hover-cell stats-small-grid-4" style="cursor: pointer; max-height: 200px;">
                    <div>
                        <h2 class="stats-header">Most prominent peak</h2>
                        <p class="stats-data-highlight"><a href="/{{ most_prominent_peak.slug }}/">{{ most_prominent_peak.name }}</a></p>
                        <p class="stats-data">{{ most_prominent_peak_prominence }} ft / {{ most_prominent_peak_prominence_in_meters }} m prom</p>
                    </div>
                </div>
                {% else %}
                <div class="col-lg-4 col-md-4 col-sm-6 peakimg-responsive hover-cell stats-small-grid-4" style="cursor: pointer; max-height: 200px;">
                    <div>
                        <h2 class="stats-header">Most prominent peak</h2>
                        <p class="stats-data-missing">no info yet</p>
                    </div>
                </div>
                {% endif %}
                <div onclick="openUrl('/region/{{ continent_slug }}-mountains/summits/');" class="col-lg-4 col-md-4 col-sm-6 peakimg-responsive hover-cell stats-small-grid-5" style="cursor: pointer; max-height: 200px;">
                    <div>
                        <h2 class="stats-header">{{ continent.name }} climbs</h2>
                        <p class="stats-data-highlight"><a href="/region/{{ continent_slug }}-mountains/summits/">{{ summit_count }} climb{{ summit_count_raw|pluralize:"s" }}</a></p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 peakimg-responsive stats-small-grid-6" style="max-height: 200px; background-color: #fff;">
                    <div>
                        <h2 class="stats-header">Top countries</h2>
                        {% for c in top_three_countries|slice:":3" %}
                            <p class="stats-data"><a href="/region/{{c.slug}}-mountains/">{{ c.name }}</a></p>
                        {% endfor %}
                    </div>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 peakimg-responsive stats-small-grid-7" style="max-height: 200px; background-color: #fff;">
                    <div>
                        <h2 class="stats-header">Top climbing months</h2>
                        {% for m in top_three_months %}
                            <p class="stats-data">{{ m.summitlog_month }} <span style="font-size: 16px; color: #999;">{{ m.pct_total }}%</span></p>
                        {% endfor %}
                    </div>
                </div>
                <div class="col-lg-4 col-md-4 col-sm-6 peakimg-responsive stats-small-grid-8" style="max-height: 200px; background-color: #fff;">
                    <div>
                        <h2 class="stats-header">Top ranges</h2>
                        {% for r in top_three_ranges %}
                            <p class="stats-data"><a href="/peaks/#range={{ r.range|urlencode }}">{{ r.range }}</a></p>
                        {% endfor %}
                    </div>
                </div>
                <div onclick="openUrl('/region/{{ continent_slug }}-mountains/challenges/');" class="col-lg-4 col-md-4 hidden-sm peakimg-responsive hover-cell stats-small-grid-9" style="cursor: pointer; max-height: 200px;">
                    <div>
                        <h2 class="stats-header">Peak challenges</h2>
                        <p class="stats-data-highlight"><a href="/region/{{ continent_slug }}-mountains/challenges/">{{ challenge_count }} challenge{{ challenge_count|pluralize:"s" }}</a></p>
                    </div>
                </div>
            </div>
            <div class="row dark-background-row">
                <div class="sp-60"></div>
            </div>


            <div class="row" id="highlights-header">
                <div class="col-md-12 region-info-section-header" style="height: 70px; background-color: #fff; line-height: 70px; font-weight: 500;">
                    <div style="float: left;">
                        <h2>{{ continent.name }} mountains highlights</h2>
                    </div>
                    <div id="edit-highlights-link-div" class="pull-right">
                        {% if user.is_authenticated %}
                            {% if highlights %}
                                <a id="edit-highlights-link" style="cursor: pointer; color: #00B1F2; font-weight: 500; font-size: 14px;">edit highlights</a>
                            {% else %}
                                <a id="edit-highlights-link" style="cursor: pointer; color: #00b1f2; font-weight: 500; font-size: 14px;">add a highlight!</a>
                            {% endif %}
                        {% else %}
                            {% if highlights %}
                                <a data-toggle="modal" data-target="#accounts-login" style="cursor: pointer; color: #00B1F2; font-weight: 500; font-size: 14px;">edit highlights</a>
                            {% else %}
                                <a data-toggle="modal" data-target="#accounts-login" style="cursor: pointer; color: #00b1f2; font-weight: 500; font-size: 14px;">add a highlight!</a>
                            {% endif %}
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="row" id="highlights-content" style="{% if not highlights %}display: none; {% endif %}background-color: #fff; border-top: 1px solid #c0c0c0; padding-top: 20px; padding-left: 8px;{% if not highlights %} display: none;{% endif %}">
                <div class="col-md-12">
                    <div class="description highlights-info-content">
                        <ul id="highlights-list">
                        {% if highlights %}
                            {% for h in highlights %}
                                <li style="list-style: initial; margin-bottom: 20px; font-size: 16px; line-height: 28px;">{{ h.highlight }}</li>
                            {% endfor %}
                        {% endif %}
                        </ul>
                    </div>
                </div>
            </div>
            <div class="row" id="edit-highlights-header" style="display: none;">
                <div class="col-md-12 region-info-section-header" style="height: 70px; background-color: #feece5; line-height: 70px; font-weight: 500;">
                    <div style="float: left;">
                        <span style="color: #f24100;"><h2>{{ continent.name }} mountains highlights</h2></span>
                    </div>
                </div>
            </div>
            <div class="row" id="edit-highlights-form" style="display: none; background-color: #feece5; border-top: 1px solid #c0c0c0; padding-top: 20px; padding-left: 10px;">
                <div class="col-md-12">
                    <div class="description highlights-info-content" style="margin-left: -10px;">
                        <form id="edithighlights_form" method="POST" action="/region/edit_continent_highlights/{{ continent.id }}/">
                        <fieldset id="peak-highlights-fieldset">
                        {% if highlights %}
                            {% for h in highlights %}
                                <div>
                                    <textarea class="peak-highlight-input" name="peak-highlight-{{ forloop.counter }}"  data-index="{{ forloop.counter }}" id="peak-highlight-{{ forloop.counter }}" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ continent.name }} mountains...">{{ h.highlight }}</textarea>
                                </div>
                                {% if forloop.last %}
                                <div>
                                    <textarea class="peak-highlight-input" name="peak-highlight-{{ forloop.counter|add:1 }}" data-index="{{ forloop.counter|add:1 }}" id="peak-highlight-new" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ continent.name }} mountains..."></textarea>
                                </div>
                                {% endif %}
                            {% endfor %}
                        {% else %}
                            <div>
                                <textarea class="peak-highlight-input" name="peak-highlight-1" data-index="1" id="peak-highlight-1" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ continent.name }} mountains..."></textarea>
                            </div>
                        {% endif %}
                        </fieldset>
                        <button style="float: left; width: 170px; height: 50px; font-size: 16px; padding: 0 20px;" class="btn set2 input" id="edit-highlights-save" type="submit">Save highlights</button>&nbsp;<a style="float: left; margin-left: 30px; margin-top: 17px; cursor: pointer; color: #999;" id="edit-highlights-cancel">Cancel</a>
                        </form>
                    </div>
                </div>
            </div>

            {% if featured_logs %}

            <div class="row dark-background-row">
                <div class="sp-60"></div>
            </div>

            <div class="row" id="featured-logs-header">
                <div class="col-md-12 region-info-section-header" style="height: 70px; background-color: #fff; line-height: 70px; font-weight: 500;">
                    <h2 class="section-header">Latest climbs</h2>
                </div>
            </div>

            <div class="row sub-header-row" style="background-color: #fff; border-top: 1px solid #c0c0c0;">
                <div class="col-md-12" style="padding-left: 0px; padding-right: 0px;">
                    <div class="peak_desc" style="padding: 0px;">
                        <div class="description" style="margin-bottom: 0px; padding-top: 0px;">
                            {% for l in featured_logs %}
                                <div class="hover-cell" onclick="openUrl('/{{ l.peak_slug }}/summits/{{ l.id }}/');" style="display: inline-block; padding: 15px; cursor: pointer; background-image: linear-gradient(to bottom,#fff,#f6f6f6);">
                                    <div class="featured-logs-thumbnail">
                                        <img class="hover-photos" src="{{ MEDIA_URL }}{{ l.thumbnail_url }}" style="width: 100px;">
                                    </div>
                                    <div class="featured-logs-description">"{{ l.log_text }}" &mdash; <i><span style="color: #00b1f2; font-weight: 500;">{{ l.username }} &bull; {{ l.summitlog_date|date:"M j, Y" }}</span></i></div>
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>

            {% endif %}


            <div class="row dark-background-row">
                <div class="sp-60"></div>
            </div>
            <div class="row">
                <div class="col-md-12 region-info-section-header" style="height: 70px; background-color: #fff; line-height: 70px; font-weight: 500;"><h2>Popular {{ continent.name }} mountains</h2></div>
            </div>

            <div class="row" style="margin-left: -16px; margin-right: -16px;">
                {% for p in top_peaks|slice:"4:25" %}
                    <div class="col-lg-4 col-md-4 col-sm-6 col-xs-6 {{ p.hover_class }}" onclick="openUrl('/{{ p.slug }}');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ MEDIA_URL }}{{ p.thumbnail_url }}'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-left: solid 1px #e0e0e0; border-top: solid 1px #e0e0e0;">
                        <div>
                            <a href="/{{ p.slug }}/"><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></a>
                            <div class="{{ p.info_class }}">
                                <span class="photo-caption-peakname-only" style="position: absolute; bottom: 5px; left: 10px; color: #fff;">
                                    <div class="bagger ellipsis"><a href="/{{ p.slug }}/" style="color: #fff;">{{ p.name }}</a></div>
                                </span>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Mobile stats -->
    <div class="row hidden-lg hidden-md hidden-sm">
        <div class="col-xs-6 peakimg-responsive stats-grid-1" style="max-height: 200px; background-color: #fff;">
            <div>
                <h2 class="stats-header">{{ continent.name }} peaks</h2>
                <p class="stats-data-highlight"><a href="/region/{{ continent_slug }}-mountains/peaks/">{{ peak_count }} peaks</a></p>
            </div>
        </div>
        <div class="col-xs-6 peakimg-responsive stats-grid-2" style="max-height: 200px; background-color: #fff;">
            <div>
                <h2 class="stats-header">Highest peak</h2>
                <p class="stats-data-highlight"><a href="/{{ highest_peak.slug }}/">{{ highest_peak.name }}</a></p>
                <p class="stats-data">{{ highest_peak_elevation }} ft / {{ highest_peak_elevation_in_meters }} m</p>
            </div>
        </div>
        <div class="col-xs-6 peakimg-responsive stats-grid-3" style="max-height: 200px; background-color: #fff;">
            <div>
                <h2 class="stats-header">{{ continent.name }} climbs</h2>
                <p class="stats-data-highlight"><a href="/region/{{ continent_slug }}-mountains/summits/">{{ summit_count }} climbs</a></p>
            </div>
        </div>
        <div class="col-xs-6 peakimg-responsive stats-grid-4" style="max-height: 200px; background-color: #fff;">
            <div>
                <h2 class="stats-header">Most climbed peak</h2>
                <p class="stats-data-highlight"><a href="/{{ most_summited_peak.slug }}/">{{ most_summited_peak.name }}</a></p>
                <p class="stats-data">{{ most_summited_peak_summits }} climbs</p>
            </div>
        </div>
    </div>
    <!-- End mobile stats -->

    <!-- Mobile regions list -->
    <div class="row sub-header-row hidden-lg hidden-md hidden-sm">
        <div class="col-xs-12">
            <div class="row" id="ajax-data-loading-mobile" style="display: none;">
              <div class="col-md-12" style="text-align: center; color: #c0c0c0; margin-top: 20px; margin-bottom: 20px;">
                <i class="fa fa-spinner fa-spin fa-5x"></i>
              </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div id="countries-list-header-mobile"></div>
                </div>
            </div>
            <ul class="vert peaks_w_list" id="countries-list-mobile">
            </ul>
        </div>
    </div>
    <!-- End mobile regions list -->

    <div class="row hidden-lg hidden-md hidden-sm">
        <div style="height: 228px;"></div>
    </div>
    <div class="row hidden-xs">
        <div style="height: 103px;"></div>
    </div>
</div>

<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<script type="text/javascript">

    var initial_highlights = [];
    {% for h in highlights %}
        initial_highlights.push('{{ h.highlight }}');
    {% endfor %}

    var countriesArray = [['Country', 'Peaks']];

    window.onresize = function(){
        var chartWidth = $('#regions_div').width();
        var chartHeight = chartWidth * 0.55; // 55%
        $('#regions_div').height(chartHeight);
        drawRegionsMap();
        chartWidth = $('#regions_div_tablet').width();
        chartHeight = chartWidth * 0.55; // 55%
        $('#regions_div_tablet').height(chartHeight);
        drawRegionsMapTablet();
    };

    function slugify(str) {
      str = str.replace(/^\s+|\s+$/g, ''); // trim
      str = str.toLowerCase();

      // remove accents, swap ñ for n, etc
      var from = "àáäâèéëêìíïîòóöôùúüûñç·/_,:;";
      var to   = "aaaaeeeeiiiioooouuuunc------";
      for (var i=0, l=from.length ; i<l ; i++) {
        str = str.replace(new RegExp(from.charAt(i), 'g'), to.charAt(i));
      }

      str = str.replace(/[^a-z0-9 -]/g, '') // remove invalid chars
        .replace(/\s+/g, '-') // collapse whitespace and replace by -
        .replace(/-+/g, '-'); // collapse dashes

      return str;
    }

    function drawRegionsMap() {

        if (countriesArray.length > 1) {

            var data = google.visualization.arrayToDataTable(countriesArray);

            var options = {
                colorAxis: {colors: ['#00B1F2', '#00B1F2']},
                defaultColor: '#00B1F2',
                region: '{{ region_code }}',
                forceIFrame: false
            };

            var chart = new google.visualization.GeoChart(document.getElementById('regions_div'));

            google.visualization.events.addListener(chart, 'select', function () {
                var selectionIdx = chart.getSelection()[0].row;
                var countryName = data.getValue(selectionIdx, 0);
                window.location.href = '/region/' + slugify(countryName) + '-mountains/';
            });

            google.visualization.events.addListener(chart, 'error', function () {
                $('#regions_div').hide();
            });

            chart.draw(data, options);

        }

    }

    function drawRegionsMapTablet() {

        if (countriesArray.length > 1) {

            var data = google.visualization.arrayToDataTable(countriesArray);

            var options = {
                colorAxis: {colors: ['#00B1F2', '#00B1F2']},
                defaultColor: '#00B1F2',
                region: '{{ region_code }}',
                forceIFrame: false
            };

            var chart = new google.visualization.GeoChart(document.getElementById('regions_div_tablet'));

            google.visualization.events.addListener(chart, 'select', function () {
                var selectionIdx = chart.getSelection()[0].row;
                var countryName = data.getValue(selectionIdx, 0);
                window.location.href = '/region/' + slugify(countryName) + '-mountains/';
            });

            google.visualization.events.addListener(chart, 'error', function () {
                $('#regions_div_tablet').hide();
            });

            chart.draw(data, options);

        }

    }

    function loadCountries(startswith, keyword, continent, sort) {

        $('#countries-list').empty();
        $('#countries-list-header').empty();
        $('#ajax-data-loading').css('display', 'inline');
        $('#countries-list-mobile').empty();
        $('#countries-list-header-mobile').empty();
        $('#ajax-data-loading-mobile').css('display', 'inline');
        $.getJSON('{% url "countries_list" %}?startswith='+startswith+'&keyword='+keyword+'&continent='+continent+'&sort='+sort , function(data) {
            $.each( data, function( key, val ) {
                if (key=='countries') {
                    countriesArray = [['Country', 'Peaks']];
                    $('#ajax-data-loading').css('display', 'none');
                    $('#ajax-data-loading-mobile').css('display', 'none');
                    country_count = 0;
                    $.each( val, function( countrykey, countryval ) {
                        var regionLink = '<a class="region-list-item" href="/region/' + countryval.slug + '-mountains/">' + countryval.name + '</a>';
                        if (sort == 'summit_count') {
                            $('#countries-list').append('<div class="row region-list-region"><div class="pull-right" style="width: 100px; text-align: right; margin-right: 15px;"><span class="region-list-item" class="total_items pull-right" style="color: #999;">' + numberWithCommas(countryval.summit_count) + ' climb' + ((countryval.summit_count != 1) ? 's' : '') + '</span></div><div class="ellipsis" style="margin-left: 15px;">' + regionLink + '</div></div>');
                            $('#countries-list-mobile').append('<div class="row region-list-region"><div class="pull-right" style="width: 100px; text-align: right; margin-right: 15px;"><span class="region-list-item" class="total_items pull-right" style="color: #999;">' + numberWithCommas(countryval.summit_count) + ' climb' + ((countryval.summit_count != 1) ? 's' : '') + '</span></div><div class="ellipsis" style="margin-left: 15px;">' + regionLink + '</div></div>');
                        } else if (countryval.peak_count > 0) {
                            $('#countries-list').append('<div class="row region-list-region"><div class="pull-right" style="width: 100px; text-align: right; margin-right: 15px;"><span class="region-list-item" class="total_items pull-right" style="color: #999;">' + numberWithCommas(countryval.peak_count) + ' peak' + ((countryval.peak_count != 1) ? 's' : '') + '</span></div><div class="ellipsis" style="margin-left: 15px;">' + regionLink + '</div></div>');
                            $('#countries-list-mobile').append('<div class="row region-list-region"><div class="pull-right" style="width: 100px; text-align: right; margin-right: 15px;"><span class="region-list-item" class="total_items pull-right" style="color: #999;">' + numberWithCommas(countryval.peak_count) + ' peak' + ((countryval.peak_count != 1) ? 's' : '') + '</span></div><div class="ellipsis" style="margin-left: 15px;">' + regionLink + '</div></div>');
                        }
                        var countryItem = [countryval.name, parseFloat(countryval.peak_count)];
                        countriesArray.push(countryItem);
                        country_count++;
                    });
                    $('#countries-list-header').append('<div class="stats-header">'+country_count+' countries</div>');
                    $('#countries-list-header').append('<div class="region-list-header-sort" style="display: flex; justify-content: space-between;"><div>Sort by:</div><div><a id="sort-name" class="ajax-link sort-link" onclick="loadCountries(\'\',\'\',\''+continent+'\',\'name\');">Name</a></div><div><a id="sort-peak-count" class="ajax-link sort-link" onclick="loadCountries(\'\',\'\',\''+continent+'\',\'peak_count\');">Peaks</a></div><div><a id="sort-summit-count" class="ajax-link sort-link" onclick="loadCountries(\'\',\'\',\''+continent+'\',\'summit_count\');">Climbs</a></div></div>');
                    $('#countries-list-header-mobile').append('<div class="stats-header">'+country_count+' countries</div>');
                    $('#countries-list-header-mobile').append('<div class="region-list-header-sort" style="display: flex; justify-content: space-between;"><div>Sort by:</div><div><a id="sort-name-mobile" class="ajax-link sort-link-mobile" onclick="loadCountries(\'\',\'\',\''+continent+'\',\'name\');">Name</a></div><div><a id="sort-peak-count-mobile" class="ajax-link sort-link-mobile" onclick="loadCountries(\'\',\'\',\''+continent+'\',\'peak_count\');">Peaks</a></div><div><a id="sort-summit-count-mobile" class="ajax-link sort-link-mobile" onclick="loadCountries(\'\',\'\',\''+continent+'\',\'summit_count\');">Climbs</a></div></div>');

                    //draw the map
                    drawRegionsMap();
                    drawRegionsMapTablet();

                    //style sort options
                    switch(sort) {
                        case 'name':
                            $('#sort-name').css('color', '#F24100');
                            $('#sort-name').css('font-weight', '500');
                            $('#sort-peak-count').css('color', '#666');
                            $('#sort-peak-count').css('font-weight', '300');
                            $('#sort-summit-count').css('color', '#666');
                            $('#sort-summit-count').css('font-weight', '300');
                            $('#sort-name-mobile').css('color', '#F24100');
                            $('#sort-name-mobile').css('font-weight', '500');
                            $('#sort-peak-count-mobile').css('color', '#666');
                            $('#sort-peak-count-mobile').css('font-weight', '300');
                            $('#sort-summit-count-mobile').css('color', '#666');
                            $('#sort-summit-count-mobile').css('font-weight', '300');
                            break;
                        case 'peak_count':
                            $('#sort-name').css('color', '#666');
                            $('#sort-name').css('font-weight', '300');
                            $('#sort-peak-count').css('color', '#F24100');
                            $('#sort-peak-count').css('font-weight', '500');
                            $('#sort-summit-count').css('color', '#666');
                            $('#sort-summit-count').css('font-weight', '300');
                            $('#sort-name-mobile').css('color', '#666');
                            $('#sort-name-mobile').css('font-weight', '300');
                            $('#sort-peak-count-mobile').css('color', '#F24100');
                            $('#sort-peak-count-mobile').css('font-weight', '500');
                            $('#sort-summit-count-mobile').css('color', '#666');
                            $('#sort-summit-count-mobile').css('font-weight', '300');
                            break;
                        case 'summit_count':
                            $('#sort-name').css('color', '#666');
                            $('#sort-name').css('font-weight', '300');
                            $('#sort-peak-count').css('color', '#666');
                            $('#sort-peak-count').css('font-weight', '300');
                            $('#sort-summit-count').css('color', '#F24100');
                            $('#sort-summit-count').css('font-weight', '500');
                            $('#sort-name-mobile').css('color', '#666');
                            $('#sort-name-mobile').css('font-weight', '300');
                            $('#sort-peak-count-mobile').css('color', '#666');
                            $('#sort-peak-count-mobile').css('font-weight', '300');
                            $('#sort-summit-count-mobile').css('color', '#F24100');
                            $('#sort-summit-count-mobile').css('font-weight', '500');
                            break;
                        default:
                            $('#sort-name').css('color', '#F24100');
                            $('#sort-name').css('font-weight', '500');
                            $('#sort-peak-count').css('color', '#666');
                            $('#sort-peak-count').css('font-weight', '300');
                            $('#sort-summit-count').css('color', '#666');
                            $('#sort-summit-count').css('font-weight', '300');
                            $('#sort-name-mobile').css('color', '#F24100');
                            $('#sort-name-mobile').css('font-weight', '500');
                            $('#sort-peak-count-mobile').css('color', '#666');
                            $('#sort-peak-count-mobile').css('font-weight', '300');
                            $('#sort-summit-count-mobile').css('color', '#666');
                            $('#sort-summit-count-mobile').css('font-weight', '300');
                    }
                }
            });
        });

    }

    function numberWithCommas(x) {
        return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    function openUrl(url) {
        window.location.href = url;
    }

    function gmapsCallback() {
        google.load("visualization", "1", {packages:["geochart"], callback: function() { geochartReady() }});
    }

    function geochartReady() {
        drawRegionsMap();
        drawRegionsMapTablet();
        loadCountries('', '', '{{ continent_code }}', 'name');

        //highlights stuff
        {% if user.is_authenticated %}
        $('#edit-highlights-link-div').on('click', 'a', function() {
            $('#highlights-header').hide();
            $('#highlights-content').hide();
            $('#edit-highlights-header').fadeIn(300);
            $('#edit-highlights-form').fadeIn(300, function () {
                autosize($('.peak-highlight-input'));
                autosize.update($('.peak-highlight-input'));
            });
            return false;
        });
        {% else %}
        $('#edit-highlights-link-div').on('click', 'a', function() {
            if ($('#navbar-login-link').is(':visible')) {
                $('#navbar-login-link').click();
            } else {
                //window.location.href = '/accounts/login/';
            }
            return false;
        });
        {% endif %}

        $('#edit-highlights-cancel').click(function(){
            $('#peak-highlights-fieldset').empty();
            var new_index = 1;
            var haveHighlights = false;
            for (var i = 0; i < initial_highlights.length; i++) {
                $('#peak-highlights-fieldset').append('<div><textarea class="peak-highlight-input" name="peak-highlight-' + new_index + '"  data-index="' + new_index + '" id="peak-highlight-' + new_index + '" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ continent.name }} mountains...">'+initial_highlights[i]+'</textarea></div>');
                haveHighlights = true;
                new_index++;
            }
            $('#peak-highlights-fieldset').append('<div><textarea class="peak-highlight-input" name="peak-highlight-' + new_index + '" data-index="' + new_index + '" id="peak-highlight-new" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ continent.name }} mountains..."></textarea></div>');
            $('#edit-highlights-header').hide();
            $('#edit-highlights-form').hide();
            $('#highlights-header').fadeIn(300);
            if (haveHighlights) {
                $('#highlights-content').fadeIn(300);
                $('#edit-highlights-link').html('edit highlights');
            } else {
                $('#highlights-content').fadeOut(300);
                $('#edit-highlights-link').html('add a highlight!');
            }
            return false;
        });

        $('#peak-highlights-fieldset').on('keyup', 'textarea', function() {
            var index = $(this).data('index');
            var num_fields = $('.peak-highlight-input').length;
            if (index == num_fields && $(this).val().length > 0) {
                var new_index = index + 1;
                if ($('#peak-highlight-' + new_index).length == 0) {
                    $('#peak-highlights-fieldset').append('<div><textarea class="peak-highlight-input" name="peak-highlight-' + new_index + '"  data-index="' + new_index + '" id="peak-highlight-' + new_index + '" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ continent.name }} mountains..."></textarea></div>');
                }
            }
            autosize($('.peak-highlight-input'));
            autosize.update($('.peak-highlight-input'));
            return false;
        });

        // process the form
        $('#edit-highlights-save').click(function(event) {

            var url = "/region/edit_continent_highlights/{{ continent.id }}/";
            $('#edit-highlights-save').html('<i class="fa fa-spinner fa-spin fa-fw"></i>');
            $('#edit-highlights-save').prop("disabled",true);

            $.ajax({
                type: "POST",
                url: url,
                data: $("#edithighlights_form").serialize(),
                success: function(data)
                {
                    //console.log(data);
                    //update page with new highlights
                    $('#peak-highlights-fieldset').empty();
                    $('#highlights-list').empty();
                    var haveHighlights = false;
                    $.each( data, function( key, val ) {
                        if (key=='highlights') {
                            new_index = 1;
                            initial_highlights = [];
                            $.each( val, function( highlightkey, highlightval ) {
                                //console.log(highlightval);
                                haveHighlights = true;
                                $('#peak-highlights-fieldset').append('<div><textarea class="peak-highlight-input" name="peak-highlight-' + new_index + '"  data-index="' + new_index + '" id="peak-highlight-' + new_index + '" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ continent.name }} mountains...">'+highlightval+'</textarea></div>');
                                $('#highlights-list').append('<li style="list-style: initial; margin-bottom: 20px; font-size: 16px; line-height: 28px;">'+highlightval+'</li>');
                                initial_highlights.push(highlightval);
                                new_index++;
                            });
                        }
                    });
                    $('#peak-highlights-fieldset').append('<div><textarea class="peak-highlight-input" name="peak-highlight-' + new_index + '" data-index="' + new_index + '" id="peak-highlight-new" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ continent.name }} mountains..."></textarea></div>');
                    $('#edit-highlights-link-div').html('<a id="edit-highlights-link" style="cursor: pointer; color: #00B1F2; font-weight: 500;">edit highlights</a>');
                    $('#edit-highlights-save').html('Save highlights');
                    $('#edit-highlights-save').prop("disabled",false);
                    $('#edit-highlights-header').hide();
                    $('#edit-highlights-form').hide();
                    $('#highlights-header').fadeIn(300);
                    if (haveHighlights) {
                        $('#highlights-content').fadeIn(300);
                        $('#edit-highlights-link').html('edit highlights');
                    } else {
                        $('#highlights-content').fadeOut(300);
                        $('#edit-highlights-link').html('add a highlight!');
                    }
                }
            });
            event.preventDefault();
        });

    }

</script>


{% load item_tags %}{% get_gmaps_lib %}
{% block gmaps_lib %}{% endblock %}
{% endblock %}
