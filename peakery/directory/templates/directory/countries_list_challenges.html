{% extends "base.html" %}
{% load static %}
{% load directory_tags %}
{% load humanize %}

{% block title %}{% if continent %}{{ continent.name }}{% else %}World{% endif %} peak challenges{% endblock %}

{% block titlemeta_overwrite %}{% if continent %}{{ continent.name }}{% else %}World{% endif %} peak challenges{% endblock %}
{% block description %}{{ meta_description }}{% endblock %}

{% block directory_active %}active{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row hidden-xs">
        <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 65px; border-radius: 12px 12px 0 0;"><span style="font-size: 24px; font-weight: 600;">
            <div id="breadcrumbs">
                <ul>
                    <li class="hidden-sm hidden-xs"><a style="font-weight: 500;" href="{% url "world_list" %}">World</a> &raquo; </li>
                    <li><h1 class="regions-title">{{ continent.name }} mountains</h1></li>
                </ul>
                <div class="pull-right hidden-xs" style="margin-top: -5px;">
                    <a style="{{ subnav_info_style }}" class="region-header-sub-links" href="/region/{{ continent_slug }}-mountains/">Info</a><a style="{{ subnav_map_style }}" class="region-header-sub-links" href="/region/{{ continent_slug }}-mountains/map/">Map</a><a style="{{ subnav_peaks_style }}" class="region-header-sub-links" href="/region/{{ continent_slug }}-mountains/peaks/">Peaks</a><a style="{{ subnav_summits_style }}" class="region-header-sub-links" href="/region/{{ continent_slug }}-mountains/summits/">Climbs</a><a style="{{ subnav_challenges_style }}" class="region-header-sub-links" href="/region/{{ continent_slug }}-mountains/challenges/">Challenges</a>
                </div>
            </div>
        </span></div>
    </div>
{% endblock %}

{% block content %}

<style>
   @media screen and (max-width: 767px) and (min-width: 1px) {
       #content-body {
           margin-top: 0px;
       }
       .content-pane {
           margin-top: 100px;
       }
       #main {
           margin-top: 20px;
       }
       .regions-subnav-fixed {
            top: 120px !important;
        }
   }
    @media screen and (min-width: 768px) {
        .content-pane {
           margin-top: 50px;
       }
    }
    @media screen and (min-width: 768px) and (max-width: 1279px) {
        .regions-subnav-fixed {
            top: 141px !important;
        }
    }
    @media screen and (min-width: 1280px) {
        .regions-subnav-fixed {
            top: 141px !important;
        }
    }
</style>

<div class="container">
    <!-- Mobile header -->
    <div class="row sub-header-row hidden-lg hidden-md hidden-sm" style="height: 50px; position: fixed; width: 100%; z-index: 2;">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999; display: flex; justify-content: space-between;">
            <a style="{{ subnav_info_style }} margin-left: 0px;" class="mobile-header-sub-links" href="/region/{{ continent_slug }}-mountains/">Info</a><a style="{{ subnav_map_style }}" class="mobile-header-sub-links" href="/region/{{ continent_slug }}-mountains/map/">Map</a><a style="{{ subnav_peaks_style }}" class="mobile-header-sub-links" href="/region/{{ continent_slug }}-mountains/peaks/">Peaks</a><a style="{{ subnav_summits_style }}" class="mobile-header-sub-links" href="/region/{{ continent_slug }}-mountains/summits/">Climbs</a><a style="{{ subnav_challenges_style }}" class="mobile-header-sub-links" href="/region/{{ continent_slug }}-mountains/challenges/">Challenges</a>
        </div>
    </div>
    <!-- End mobile header -->

    <div class="row" style="margin-top: 50px;">
        <!-- Latest summits -->
        <div id="latest_summits_div" class="col-lg-3 col-md-3 hidden-sm hidden-xs border-right" style="display: none; padding-right: 0px; padding-left: 0px; background-color: #f2f2f2;">
            <div id="countries-list-header" style="margin-bottom: 0px; margin-left: 20px;"><strong>Latest climbs</strong></div>
            <div class="peak_desc">
                <div class="description" style="font-size: 14px; line-height: 22px;">
                    {% for l in featured_logs|slice:"0:3" %}
                        <div class="hover-cell {% if forloop.counter > 2 %}hidden-md{% endif %}" onclick="openUrl('/{{ l.peak_slug }}/summits/{{ l.id }}/');" style="padding: 10px; cursor: pointer; background-color: #f2f2f2;">"{{ l.log_text|slice:"0:180" }}" - <i><strong>{{ l.peak_name }}, {{ l.username }}, {{ l.summitlog_date|date:"M j, Y" }}</strong></i></div>
                    {% endfor %}
                </div>
            </div>
        </div>
        <!-- End latest summits -->
        <!-- Geochart -->
        <div class="col-lg-9 col-md-9 col-sm-12 hidden-xs" style="padding-right: 0px; padding-left: 0px;">
            <div id="challenges_div" style="width: 100%; height: 100%;"></div>
        </div>
        <!-- End geochart -->
    </div>

    <div class="row sub-header-row hidden-xs hidden-sm regions-subnav-fixed" style="height: 50px; padding-right: 0px; top: 141px;">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999;">
            Sort by:<a style="margin-left: 10px; color: rgb(153, 153, 153); font-weight: 300;" id="sort-pursuers" class="region-header-sub-links ajax-link" onclick="loadChallenges('pursuers', '1');">Pursuers</a><a style="margin-left: 35px; color: rgb(153, 153, 153); font-weight: 300;" id="sort-finishers" class="region-header-sub-links ajax-link" onclick="loadChallenges('finishers', '1');">Finishers</a><a style="margin-left: 35px; color: rgb(153, 153, 153); font-weight: 300;" id="sort-most-peaks" class="region-header-sub-links ajax-link" onclick="loadChallenges('most_peaks', '1');">Most peaks</a><a style="margin-left: 35px; color: rgb(153, 153, 153); font-weight: 300;" id="sort-least-peaks" class="region-header-sub-links ajax-link" onclick="loadChallenges('least_peaks', '1');">Least peaks</a><span class="pull-right page-subtitle hidden-xs hidden-sm" style="font-size: 12px;">{{ challenge_count }} <span style="color: #999; font-weight: 400;">challenge{{ challenge_count|pluralize:"s" }} in {{ continent.name }}</span></span>
        </div>
    </div>
    <div class="row sub-header-row hidden-md hidden-lg regions-subnav-fixed" style="height: 50px; padding-right: 0px;">
        <div id="ranked-by-button-div" class="col-xs-12 col-sm-12 col-md-12 col-lg-12" style="height: 50px; line-height: 26px; cursor: pointer;">
            <div>
                <div class="select" id="selectRankedBy" style="margin-top: 8px; font-size: 12px; text-align: center;">
                    <span style="color: #999;">Sort {{ challenge_count }} challenge{{ challenge_count|pluralize:"s" }} by: </span>
                    <input type="hidden" id="hdnRankedBy" value="">
                    <button class="btn btn-default ranked-by-button" style="margin-bottom: 3px; border: none; padding-top: 7px; padding-bottom: 7px; margin-left: 1px; color: #00b1f2; font-size: 12px; padding-left: 5px; padding-right: 5px; background-color: #f2f2f2;" data-toggle="dropdown" aria-expanded="false">
                        <span id="ranked-by-title">Pursuers</span>
                        <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu ranked-by-list" style="cursor: pointer; height: 180px; left: 50%; overflow: auto; top: 50px;">
                        <li style="float: none; height: 30px; margin: 10px 0 0;" data-value="name"><a class="ranked-by-item" onclick="loadChallenges('name', '1');">Name</a></li>
                        <li style="float: none; height: 30px; margin: 10px 0 0;" data-value="pursuers"><a class="ranked-by-item" onclick="loadChallenges('pursuers', '1');">Pursuers</a></li>
                        <li style="float: none; height: 30px; margin: 10px 0 0;" data-value="finishers"><a class="ranked-by-item" onclick="loadChallenges('finishers', '1');">Finishers</a></li>
                        <li style="float: none; height: 30px; margin: 10px 0 0;" data-value="most_peaks"><a class="ranked-by-item" onclick="loadChallenges('most_peaks', '1');">Most peaks</a></li>
                        <li style="float: none; height: 30px; margin: 10px 0 0;" data-value="least_peaks"><a class="ranked-by-item" onclick="loadChallenges('least_peaks', '1');">Least peaks</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="row content-pane" style="border-bottom: none;">
        <div class="col-md-12">
            <div class="row" id="ajax-data-loading" style="display: none;">
              <div class="col-md-12 data-loading" style="text-align: center; color: #666;">
                <i class="fa fa-spinner fa-spin fa-5x"></i>
              </div>
            </div>
            <div class="row" id="challenges-list">

            </div>
        </div>
    </div>
    <div class="row dark-background-row" id="pagination" style="border-bottom: none; color: #ccc; margin-top: -1px; height: 80px; margin-left: 0px; display: none;">
        <div class="col-md-12">
            <div class="row" style="padding-left: 15px; margin-left: 5px; text-align: center;">
                <span id="pagination-pages" class="step-links"></span>
            </div>
        </div>
    </div>
    <div class="row dark-background-row hidden-lg hidden-md hidden-sm">
        <div class="row">
            <div style="height: 28px;"></div>
        </div>
    </div>
    <div class="row dark-background-row hidden-xs">
        <div class="row">
            <div style="height: 53px;"></div>
        </div>
    </div>
</div>



<script type="text/javascript">

    var challengeSort;
    var challengesArray = [['Lat', 'Long', 'Peaks']];
    var challengeSlugs = [];

    function decodeHtml(html) {
        return $('<div>').html(html).text();
    }

    function slugify(str) {
      str = str.replace(/^\s+|\s+$/g, ''); // trim
      str = str.toLowerCase();

      // remove accents, swap ñ for n, etc
      var from = "àáäâèéëêìíïîòóöôùúüûñç·/_,:;";
      var to   = "aaaaeeeeiiiioooouuuunc------";
      for (var i=0, l=from.length ; i<l ; i++) {
        str = str.replace(new RegExp(from.charAt(i), 'g'), to.charAt(i));
      }

      str = str.replace(/[^a-z0-9 -]/g, '') // remove invalid chars
        .replace(/\s+/g, '-') // collapse whitespace and replace by -
        .replace(/-+/g, '-'); // collapse dashes

      return str;
    }

    function drawChallengesMap() {

        $('#challenges_div').show();
        $('#challenges_div').height(600);

        var data = google.visualization.arrayToDataTable(challengesArray);

        var options = {
            displayMode: 'markers',
            sizeAxis: {minValue: 7, maxValue: 7, minSize: 7,  maxSize: 7},
            colorAxis: {minValue: 7, maxValue:7,  colors: ['#058dc7']},
            legend: 'none',
            defaultColor: '#058dc7',
            resolution: 'countries',
            region: '{{ region_code }}',
            forceIFrame: false
        };

        var chart = new google.visualization.GeoChart(document.getElementById('challenges_div'));

        google.visualization.events.addListener(chart, 'select', function() {
            var selectionIdx = chart.getSelection()[0].row;
            var challengeSlug = challengeSlugs[selectionIdx];
            window.location.href = '/challenges/' + challengeSlug + "/";
        });

        google.visualization.events.addListener(chart, 'error', function() {
          $('#challenges_div').hide();
        });

        google.visualization.events.addListener(chart, 'ready', function() {
            $('#latest_summits_div').show();
            $('#latest_summits_div').height(600);
        });

        chart.draw(data, options);

    }

    function loadChallenges(sort, page) {

        challengeSort = sort;
        $('#pagination').css('display','none');
        $('#latest_summits_div').hide();
        $('#challenges_div').hide();

        switch(sort) {
            case 'pursuers':
                $('#sort-pursuers').css('color', '#F24100');
                $('#sort-pursuers').css('font-weight', '500');
                $('#sort-finishers').css('color', '#999');
                $('#sort-finishers').css('font-weight', '300');
                $('#sort-most-peaks').css('color', '#999');
                $('#sort-most-peaks').css('font-weight', '300');
                $('#sort-least-peaks').css('color', '#999');
                $('#sort-least-peaks').css('font-weight', '300');
                window.location.hash = 'order=pursuers&page='+page;
                $('#ranked-by-title').html('Pursuers');
                break;
            case 'finishers':
                $('#sort-pursuers').css('color', '#999');
                $('#sort-pursuers').css('font-weight', '300');
                $('#sort-finishers').css('color', '#F24100');
                $('#sort-finishers').css('font-weight', '500');
                $('#sort-most-peaks').css('color', '#999');
                $('#sort-most-peaks').css('font-weight', '300');
                $('#sort-least-peaks').css('color', '#999');
                $('#sort-least-peaks').css('font-weight', '300');
                window.location.hash = 'order=finishers&page='+page;
                $('#ranked-by-title').html('Finishers');
                break;
            case 'most_peaks':
                $('#sort-pursuers').css('color', '#999');
                $('#sort-pursuers').css('font-weight', '300');
                $('#sort-finishers').css('color', '#999');
                $('#sort-finishers').css('font-weight', '300');
                $('#sort-most-peaks').css('color', '#F24100');
                $('#sort-most-peaks').css('font-weight', '500');
                $('#sort-least-peaks').css('color', '#999');
                $('#sort-least-peaks').css('font-weight', '300');
                window.location.hash = 'order=most_peaks&page='+page;
                $('#ranked-by-title').html('Most peaks');
                break;
            case 'least_peaks':
                $('#sort-pursuers').css('color', '#999');
                $('#sort-pursuers').css('font-weight', '300');
                $('#sort-finishers').css('color', '#999');
                $('#sort-finishers').css('font-weight', '300');
                $('#sort-most-peaks').css('color', '#999');
                $('#sort-most-peaks').css('font-weight', '300');
                $('#sort-least-peaks').css('color', '#F24100');
                $('#sort-least-peaks').css('font-weight', '500');
                window.location.hash = 'order=least_peaks&page='+page;
                $('#ranked-by-title').html('Least peaks');
                break;
            default:
                $('#sort-pursuers').css('color', '#F24100');
                $('#sort-pursuers').css('font-weight', '500');
                $('#sort-finishers').css('color', '#999');
                $('#sort-finishers').css('font-weight', '300');
                $('#sort-most-peaks').css('color', '#999');
                $('#sort-most-peaks').css('font-weight', '300');
                $('#sort-least-peaks').css('color', '#999');
                $('#sort-least-peaks').css('font-weight', '300');
                window.location.hash = 'order=pursuers&page='+page;
                $('#ranked-by-title').html('Pursuers');
        }

        challengeSort = sort;
        $('#pagination').css('display','none');
        window.scrollTo(0, 0);

        $('#challenges-list').empty();
        $('#ajax-data-loading').css('display', 'inline');
        var challengeCount = '{{ challenge_count }}'.replace(',', '');
        var totalPages = Math.ceil(parseInt(challengeCount)/25);
        $.getJSON('{% url "region_challenges_list" %}?continent={{ continent_code }}&sort='+sort+'&page='+page , function(data) {
            $.each( data, function( key, val ) {
                if (key=='challenges') {
                    $('#ajax-data-loading').css('display', 'none');
                    $.each( val, function( challengekey, challengeval ) {

                        $('#challenges-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 challengeimg-' + challengeval.id + '" style="background-color: #fff;"><div class="row" onmouseover="highlightPhotos(' + challengeval.id + ');" onmouseout="unhighlightPhotos(' + challengeval.id + ');" style="cursor: pointer;" onclick="openUrl(\'/challenges/' + challengeval.slug + '\');"><div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="height: 80px; border-right: 1px solid #c0c0c0;"><div><p>&nbsp;</p><p><span class="stats-header challenge-list-challenge-title"><a style="color: #000000;" href="/challenges/' + challengeval.slug + '/">' + challengeval.name + '</a></span><span class="summit-list-stats pull-right"><span class="challenge-peaks">' + numberWithCommas(Math.floor(challengeval.peak_count)) + ' peak' + ((challengeval.peak_count!=1)?'s':'') + '</span><span class="hidden-xs"> &bull; </span><span class="challenge-pursuers">' + numberWithCommas(Math.floor(challengeval.pursuers)) + ' pursuer' + ((challengeval.pursuers!=1)?'s':'') + '</span><span class="hidden-xs"> &bull; </span><span class="challenge-finishers">' + numberWithCommas(Math.floor(challengeval.finishers)) + ' finisher' + ((challengeval.finishers!=1)?'s':'') + '</span></span></p></div></div></div></div>');
                        //add peak photos
                        var peak_index = 0;
                        var photoStyle = '';
                        $('#challenges-list').append('<div class="row">');
                        $.each( challengeval.peaks, function( peakkey, peakval ) {
                            if (peak_index == 0) {
                                borderStyle = '';
                            } else if (peak_index == 1) {
                                borderStyle = 'border-left: 1px solid rgb(224, 224, 224);';
                            } else if (peak_index == 2) {
                                photoStyle = 'hidden-xs';
                                borderStyle = 'border-left: 1px solid rgb(224, 224, 224);';
                            } else if (peak_index == 3) {
                                photoStyle = 'hidden-sm hidden-xs';
                                borderStyle = 'border-left: 1px solid rgb(224, 224, 224);';
                            }
                            if (peakval.thumbnail_url.slice(-16) == '/img/default.png') {
                                photo_info_class = 'empty-photo-info';
                            } else {
                                photo_info_class = 'user-photo-info';
                            }
                            $('#challenges-list').append('<div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 ' + photoStyle + ' challengeimg-' + challengeval.id + '" onmouseover="highlightPhotos(' + challengeval.id + ');" onmouseout="unhighlightPhotos(' + challengeval.id + ');" onclick="openUrl(\'/challenges/' + challengeval.slug + '\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + peakval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden; ' + borderStyle + '"><a href="/challenges/' + challengeval.slug + '/"><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></a><div class="' + photo_info_class + '"><span class="photo-caption-peakname-only" style="position: absolute; bottom: 5px; left: 10px; color: #fff;"><div class="bagger ellipsis">' + peakval.peak_name + '</div></span></div></div>');
                            peak_index++;
                            if (peak_index > 3) {
                                return false;
                            }
                        });
                        $('#challenges-list').append('</div>');
                        //add description
                        if (challengeval.description != null) {
                            $('#challenges-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 challengeimg-' + challengeval.id + '" onmouseover="highlightPhotos(' + challengeval.id + ');" onmouseout="unhighlightPhotos(' + challengeval.id + ');" onclick="openUrl(\'/challenges/' + challengeval.slug + '\');" style="cursor: pointer; background-color: #fff; border-right: 1px solid #c0c0c0;"><div><p>&nbsp;</p><p style="font-size: 14px; line-height: 29px;">' + challengeval.description.substring(0, 700) + '</p><p>&nbsp;</p></div></div>');
                        }
                        $('#challenges-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"><div class="row dark-background-row"><div class="sp-60"></div></div></div>');
                    });
                    //show pagination
                    if (totalPages > 0) {
                        var paginationHtml = '';
                        if (parseInt(page) == totalPages && totalPages > 1) {
                            paginationHtml = '<div style="display: inline-block; height: 55px;"><a onclick="loadChallenges(\'' + challengeSort + '\', ' + (parseInt(page) - 1).toString() + ');"><img src="{% static 'img/font-awesome_4-7-0_arrow-circle-left_256_0_00b1f2_none.png' %}" style="height: 26px; margin-bottom: 5px;"></a></div><div class="current" style="margin-left: 20px; margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">challenges ' + ((parseInt(page) - 1) * 25 + 1).toString() + ' - {{ challenge_count|intcomma }} of {{ challenge_count|intcomma }}</div>';
                        } else if (parseInt(page) > 1) {
                            paginationHtml = '<div style="display: inline-block; height: 55px;"><a onclick="loadChallenges(\'' + challengeSort + '\', ' + (parseInt(page) - 1).toString() + ');"><img src="{% static 'img/font-awesome_4-7-0_arrow-circle-left_256_0_00b1f2_none.png' %}" style="height: 26px; margin-bottom: 5px;"></a></div><div class="current" style="margin-left: 20px; margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">challenges ' + ((parseInt(page) - 1) * 25 + 1).toString() + ' - ' + ((parseInt(page)) * 25).toString() + ' of {{ challenge_count|intcomma }}</div>';
                        } else if (totalPages > 1) {
                            paginationHtml = '<div class="current" style="margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">challenges 1 - 25 of {{ challenge_count|intcomma }}</div>';
                        } else {
                            paginationHtml = '<div class="current" style="margin-right: 20px; font-size: 16px; display: inline-block; height: 80px; line-height: 80px;">challenges 1 - {{ challenge_count|intcomma }} of {{ challenge_count|intcomma }}</div>';
                        }
                        if (parseInt(page) < totalPages) {
                            paginationHtml = paginationHtml + '<div style="display: inline-block; height: 55px;"><a onclick="loadChallenges(\'' + challengeSort + '\', ' + (parseInt(page) + 1).toString() + ');"><img src="{% static 'img/font-awesome_4-7-0_arrow-circle-right_256_0_00b1f2_none.png'%}" style="height: 26px; margin-bottom: 5px;"></i></a></div>';
                            $('#pagination-pages').html(paginationHtml);
                            $('#pagination').css('display', 'inherit');
                        } else {
                            $('#pagination-pages').html(paginationHtml);
                            $('#pagination').css('display', 'inherit');
                        }
                    }

                    switch(sort) {
                        case 'pursuers':
                            //set stat colors
                            $('.challenge-pursuers').css('color', '#F24100');
                            $('.challenge-pursuers').removeClass('hidden-xs');
                            $('.challenge-finishers').css('color', '#aaa');
                            $('.challenge-finishers').addClass('hidden-xs');
                            $('.challenge-peaks').css('color', '#aaa');
                            $('.challenge-peaks').addClass('hidden-xs');
                            break;
                        case 'finishers':
                            //set stat colors
                            $('.challenge-pursuers').css('color', '#aaa');
                            $('.challenge-pursuers').addClass('hidden-xs');
                            $('.challenge-finishers').css('color', '#F24100');
                            $('.challenge-finishers').removeClass('hidden-xs');
                            $('.challenge-peaks').css('color', '#aaa');
                            $('.challenge-peaks').addClass('hidden-xs');
                            break;
                        case 'most_peaks':
                            //set stat colors
                            $('.challenge-pursuers').css('color', '#aaa');
                            $('.challenge-pursuers').addClass('hidden-xs');
                            $('.challenge-finishers').css('color', '#aaa');
                            $('.challenge-finishers').addClass('hidden-xs');
                            $('.challenge-peaks').css('color', '#F24100');
                            $('.challenge-peaks').removeClass('hidden-xs');
                            break;
                        case 'least_peaks':
                            //set stat colors
                            $('.challenge-pursuers').css('color', '#aaa');
                            $('.challenge-pursuers').addClass('hidden-xs');
                            $('.challenge-finishers').css('color', '#aaa');
                            $('.challenge-finishers').addClass('hidden-xs');
                            $('.challenge-peaks').css('color', '#F24100');
                            $('.challenge-peaks').removeClass('hidden-xs');
                            break;
                        default:
                            //set stat colors
                            $('.challenge-pursuers').css('color', '#F24100');
                            $('.challenge-pursuers').removeClass('hidden-xs');
                            $('.challenge-finishers').css('color', '#aaa');
                            $('.challenge-finishers').addClass('hidden-xs');
                            $('.challenge-peaks').css('color', '#aaa');
                            $('.challenge-peaks').addClass('hidden-xs');
                    }
                }
                if (key=='all_challenges') {
                    challengesArray = [['LATITUDE', 'LONGITUDE', 'tooltip', 'VALUE', {role: 'tooltip', p:{html:true}}]];
                    $.each( val, function( challengekey, challengeval ) {
                        if (challengeval.show_on_geochart == 'True') {
                            var challengeItem = [parseFloat(challengeval.lat), parseFloat(challengeval.lng), decodeHtml(challengeval.name), parseFloat(challengeval.peak_count), challengeval.peak_count + " peaks"];
                            challengesArray.push(challengeItem);
                            challengeSlugs.push(challengeval.slug);
                        }
                    });
                    //draw the map
                    drawChallengesMap();
                }
            });
        });

    }

    function numberWithCommas(x) {
        return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    function openUrl(url) {
        window.location.href = url;
    }

    function highlightPhotos(challengeid) {
        $('.challengeimg-'+challengeid).addClass('hover-photos-hover');
        $('.challengeimg-'+challengeid).css('background-color', '#fde1d6');
    }

    function unhighlightPhotos(challengeid) {
        $('.challengeimg-'+challengeid).removeClass('hover-photos-hover');
        $('.challengeimg-'+challengeid).css('background-color', '#fff');
    }

    function gmapsCallback() {
        var viewport_height = $(window).height();
        var viewport_width = $(window).width();
        var footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
        var data_loading_margin = (viewport_height - footer_height) / 3;
        $('.data-loading').css('margin-top', data_loading_margin);
        $(window).resize(function() {
            viewport_height = $(window).height();
            viewport_width = $(window).width();
            footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
            data_loading_margin = (viewport_height - footer_height) / 3;
            $('.data-loading').css('margin-top', data_loading_margin);
        });

        var vars = [], hash, sort, page;
        var q = document.URL.split('#')[1];
        if(q != undefined){
            q = q.split('&');
            for(var i = 0; i < q.length; i++){
                hash = q[i].split('=');
                vars.push(hash[1]);
                vars[hash[0]] = hash[1];
            }
        }

        if (vars['order'] != undefined) {
            sort = vars['order'];
        } else {
            sort = 'pursuers';
        }

        if (vars['page'] != undefined) {
            page = vars['page'];
        } else {
            page = '1';
        }

        $('#ranked-by-button-div').click(function(e){
            $('.ranked-by-list').toggle();
        });

        google.load("visualization", "1", {packages:["geochart"], callback: function() { loadChallenges(sort, page) }});
    }

</script>

{% load item_tags %}{% get_gmaps_lib %}
{% block gmaps_lib %}{% endblock %}
{% endblock %}
