{% extends "base.html" %}
{% load static %}
{% block title %}World mountains{% endblock %}
{% block titlemeta_overwrite %}World mountains{% endblock %}
{% block description %}{{ meta_description }}{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row hidden-xs">
        <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 70px; font-weight: 500; border-radius: 12px 12px 0 0;"><span style="font-size: 24px; font-weight: 600;">
            <div id="breadcrumbs">
                <ul>
                    <li class="section-title-li" style="margin-top: 0px !important; width: 100%;">
                        <h1 class="section-title" style="font-weight: 600; float: left;">World mountains</h1>
                        <div class="pull-right section-title-stats" style="margin-left: 15px;">
                            <span style="font-size: 14px; font-weight: 500;"><span id="active-member-count">{{ peak_total }}</span> peaks in the world</span>
                        </div>
                    </li>
                </ul>
            </div>
        </span></div>
    </div>
{% endblock %}

{% block content %}

<style>

    #content-body {
        margin-top: 20px !important;
    }

    @media screen and (min-width: 768px) and (max-width: 1279px) {
        .regions-subnav-fixed {
            top: 141px !important;
        }
    }

    @media screen and (min-width: 1280px) {
        .regions-subnav-fixed {
            top: 141px !important;
        }
    }

    @media screen and (max-width: 767px) and (min-width: 1px) {
       #sort-by-bar {
           position: fixed;
            top: 70px;
            width: 100%;
            z-index: 2;
       }
       .pad-mobile {
           padding-top: 50px;
       }
   }
    @media screen and (max-width: 479px) and (min-width: 1px) {
        .country-title-pad-mobile {
           display: inherit;
       }
    }
    @media screen and (min-width: 480px) {
        .country-title-pad-mobile {
           display: none;
       }
    }

    @media screen and (max-width: 1023px) and (min-width: 1px) {
        #geochart {
           margin-top: 71px;
       }
    }
    @media screen and (min-width: 1024px) {
        #geochart {
           margin-top: 71px;
       }
    }

    @media screen and (min-width: 1px) and (max-width: 320px)  {
       #sort-name {margin-left: 10px;}
       #sort-peak-count {margin-left: 10px;}
       #sort-summit-count {margin-left: 10px;}
   }
    @media screen and (min-width: 321px) and (max-width: 350px)  {
       #sort-name {margin-left: 15px;}
       #sort-peak-count {margin-left: 15px;}
       #sort-summit-count {margin-left: 15px;}
   }
    @media screen and (min-width: 351px) and (max-width: 380px)  {
       #sort-name {margin-left: 20px;}
       #sort-peak-count {margin-left: 20px;}
       #sort-summit-count {margin-left: 20px;}
   }
    @media screen and (min-width: 381px) and (max-width: 410px)  {
       #sort-name {margin-left: 25px;}
       #sort-peak-count {margin-left: 25px;}
       #sort-summit-count {margin-left: 25px;}
   }
    @media screen and (min-width: 411px) and (max-width: 440px)  {
       #sort-name {margin-left: 30px;}
       #sort-peak-count {margin-left: 30px;}
       #sort-summit-count {margin-left: 30px;}
   }
    @media screen and (min-width: 441px) {
       #sort-name {margin-left: 20px;}
       #sort-peak-count {margin-left: 20px;}
       #sort-summit-count {margin-left: 20px;}
   }

   #regions_div {cursor: pointer;}
   #regions_div path[fill="#ffffff"] {cursor: auto}
   #regions_div_tablet path[fill="#ffffff"] {cursor: auto}
   #regions_div path[fill="#f5f5f5"] {cursor: auto}
   #regions_div_tablet path[fill="#f5f5f5"] {cursor: auto}
</style>

<div class="container">
    <div class="row sub-header-row">
        <div class="col-xs-12 col-sm-12 col-md-3 col-lg-3 hidden-xs hidden-sm" style="margin-top: 71px;" id="continents-list-desktop">
            <div class="row" id="continents-list" style="margin-left: -30px; margin-right: -30px;">
                <div id="continent-north-america" class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                    <a href="/region/North-America-mountains/">
                        <img style="width: 100%;" class="img-responsive peakeryPhoto photography" src="{% static 'img/regions_north_america_peaks.png' %}">
                    </a>
                    <div class="continent-link-div" style="cursor: pointer; position: absolute; z-index: 2; top: 0px; right: 15px; width: 320px; height: 65px; line-height: 65px;" onclick="openUrl('/region/North-America-mountains/');">
                        <a style="margin-left: 15px; cursor: pointer;" href="/region/North-America-mountains/" class="continent-link-text"><h2>North America</h2></a>
                    </div>
                </div>
                <div id="continent-europe" class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                    <a href="/region/Europe-mountains/">
                        <img style="width: 100%; border-top: solid 2px #e0e0e0;" class="img-responsive peakeryPhoto photography" src="{% static 'img/regions_europe_peaks.png' %}">
                    </a>
                    <div class="continent-link-div" style="cursor: pointer; position: absolute; z-index: 2; top: 0px; right: 15px; width: 320px; height: 65px; line-height: 65px;" onclick="openUrl('/region/Europe-mountains/');">
                        <a style="margin-left: 15px; cursor: pointer;" href="/region/Europe-mountains/" class="continent-link-text"><h2>Europe</h2></a>
                    </div>
                </div>
                <div id="continent-asia" class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                    <a href="/region/Asia-mountains/">
                        <img style="width: 100%; border-top: solid 2px #e0e0e0;" class="img-responsive peakeryPhoto photography" src="{% static 'img/regions_asia_peaks.png' %}">
                    </a>
                    <div class="continent-link-div" style="cursor: pointer; position: absolute; z-index: 2; top: 0px; right: 15px; width: 320px; height: 65px; line-height: 65px;" onclick="openUrl('/region/Asia-mountains/');">
                        <a style="margin-left: 15px; cursor: pointer;" href="/region/Asia-mountains/" class="continent-link-text"><h2>Asia</h2></a>
                    </div>
                </div>
                <div id="continent-south-america" class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                    <a href="/region/South-America-mountains/">
                        <img style="width: 100%; border-top: solid 2px #e0e0e0;" class="img-responsive peakeryPhoto photography" src="{% static 'img/regions_south_america_peaks.png' %}">
                    </a>
                    <div class="continent-link-div" style="cursor: pointer; position: absolute; z-index: 2; top: 0px; right: 15px; width: 320px; height: 65px; line-height: 65px;" onclick="openUrl('/region/South-America-mountains/');">
                        <a style="margin-left: 15px; cursor: pointer;" href="/region/South-America-mountains/" class="continent-link-text"><h2>South America</h2></a>
                    </div>
                </div>
                <div id="continent-africa" class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                    <a href="/region/Africa-mountains/">
                        <img style="width: 100%; border-top: solid 2px #e0e0e0;" class="img-responsive peakeryPhoto photography" src="{% static 'img/regions_africa_peaks.png' %}">
                    </a>
                    <div class="continent-link-div" style="cursor: pointer; position: absolute; z-index: 2; top: 0px; right: 15px; width: 320px; height: 65px; line-height: 65px;" onclick="openUrl('/region/Africa-mountains/');">
                        <a style="margin-left: 15px; cursor: pointer;" href="/region/Africa-mountains/" class="continent-link-text"><h2>Africa</h2></a>
                    </div>
                </div>
                <div id="continent-oceania" class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                    <a href="/region/Oceania-mountains/">
                        <img style="width: 100%; border-top: solid 2px #e0e0e0;" class="img-responsive peakeryPhoto photography" src="{% static 'img/regions_australia_peaks.png' %}">
                    </a>
                    <div class="continent-link-div" style="cursor: pointer; position: absolute; z-index: 2; top: 0px; right: 15px; width: 320px; height: 65px; line-height: 65px;" onclick="openUrl('/region/Oceania-mountains/');">
                        <a style="margin-left: 15px; cursor: pointer;" href="/region/Oceania-mountains/" class="continent-link-text"><h2>Oceania</h2></a>
                    </div>
                </div>
                <div id="continent-antarctica" class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                    <a href="/region/Antarctica-mountains/">
                        <img style="width: 100%; border-top: solid 2px #e0e0e0;" class="img-responsive peakeryPhoto photography" src="{% static 'img/regions_antarctica_peaks.png' %}">
                    </a>
                    <div class="continent-link-div" style="cursor: pointer; position: absolute; z-index: 2; top: 0px; right: 15px; width: 320px; height: 65px; line-height: 65px;" onclick="openUrl('/region/Antarctica-mountains/');">
                        <a style="margin-left: 15px; cursor: pointer;" href="/region/Antarctica-mountains/" class="continent-link-text"><h2>Antarctica</h2></a>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-9 col-md-9 col-sm-12 hidden-xs col-full-width" id="geochart">
            <div id="regions_div" style="width: 100%; height: 100%;"></div>
        </div>
    </div>
    <div id="sort-by-bar" class="row sub-header-row">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999;">
            <span>Sort countries by:</span>
            <span><a id="sort-name" class="ajax-link" onclick="loadCountries('','','name');">Name</a></span> <span id="sort-name-letters" class="hidden-xs">( <a class="ajax-link" href="#A">A</a> <a class="ajax-link" href="#B">B</a> <a class="ajax-link" href="#C">C</a> <a class="ajax-link" href="#D">D</a> <a class="ajax-link" href="#E">E</a> <a class="ajax-link" href="#F">F</a> <a class="ajax-link" href="#G">G</a> <a class="ajax-link" href="#H">H</a> <a class="ajax-link" href="#I">I</a> <a class="ajax-link" href="#J">J</a> <a class="ajax-link" href="#K">K</a> <a class="ajax-link" href="#L">L</a> <a class="ajax-link" href="#M">M</a> <a class="ajax-link" href="#N">N</a> <a class="ajax-link" href="#O">O</a> <a class="ajax-link" href="#P">P</a> <a class="ajax-link" href="#Q">Q</a> <a class="ajax-link" href="#R">R</a> <a class="ajax-link" href="#S">S</a> <a class="ajax-link" href="#T">T</a> <a class="ajax-link" href="#U">U</a> <a class="ajax-link" href="#V">V</a> <a class="ajax-link" href="#W">W</a> <a class="ajax-link" href="#X">X</a> <a class="ajax-link" href="#Y">Y</a> <a class="ajax-link" href="#Z">Z</a> )</span>
            <span><a id="sort-peak-count" class="ajax-link" onclick="loadCountries('','','peak_count');">Peaks</a></span>
            <span><a id="sort-summit-count" class="ajax-link" onclick="loadCountries('','','summit_count');">Climbs</a></span>
        </div>
    </div>
    <div class="row pad-mobile">
        <div class="row" id="ajax-data-loading" style="display: none;">
          <div class="col-md-12 data-loading" style="text-align: center; color: #666;">
            <i class="fa fa-spinner fa-spin fa-5x"></i>
          </div>
        </div>
        <div id="countries-list" class="col-md-12" style="padding-left: 0px;">
        </div>
    </div>
    <div class="row dark-header-row hidden-lg hidden-md hidden-sm">
        <div class="row">
            <div style="height: 28px;"></div>
        </div>
    </div>
    <div class="row dark-header-row hidden-xs">
        <div class="row">
            <div style="height: 53px;"></div>
        </div>
    </div>
</div>

<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<script type="text/javascript">
    var countriesArray = [['Country', 'Peaks']];

    window.onresize = function(){
        var chartWidth = $('#regions_div').width();
        var chartHeight = $('#continents-list').height();
        if (chartHeight == 0) {
            chartHeight = chartWidth / 2.12;
        }
        $('#regions_div').height(chartHeight);
        drawRegionsMap();
    };

    function slugify(str) {
      str = str.replace(/^\s+|\s+$/g, ''); // trim
      str = str.toLowerCase();

      // remove accents, swap ñ for n, etc
      var from = "àáäâèéëêìíïîòóöôùúüûñç·/_,:;";
      var to   = "aaaaeeeeiiiioooouuuunc------";
      for (var i=0, l=from.length ; i<l ; i++) {
        str = str.replace(new RegExp(from.charAt(i), 'g'), to.charAt(i));
      }

      str = str.replace(/[^a-z0-9 -]/g, '') // remove invalid chars
        .replace(/\s+/g, '-') // collapse whitespace and replace by -
        .replace(/-+/g, '-'); // collapse dashes

      return str;
    }

    function numberWithCommas(x) {
        return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    function drawRegionsMap() {

        var data = google.visualization.arrayToDataTable(countriesArray);

        var options = {
            colorAxis: {colors: ['#aadff3', '#058dc7']},
            defaultColor: '#058dc7'
        };

        var chart = new google.visualization.GeoChart(document.getElementById('regions_div'));

        google.visualization.events.addListener(chart, 'select', function() {
            var selectionIdx = chart.getSelection()[0].row;
            var countryName = data.getValue(selectionIdx, 0);
            window.location.href = '/region/' + slugify(countryName) + '-mountains/';
        });

        google.visualization.events.addListener(chart, 'ready', setChartSize);

        chart.draw(data, options);

    }

    function setChartSize() {
        var chartWidth = $('#regions_div').width();
        var chartHeight = $('#continents-list').height();
        if (chartHeight == 0) {
            chartHeight = chartWidth / 2.12;
        }
        $('#regions_div').height(chartHeight);
    }

    function loadCountries(startswith, keyword, sort) {

        //style sort options
        switch(sort) {
            case 'name':
                $('#sort-name').css('color', '#F24100');
                $('#sort-name').css('font-weight', '500');
                $('#sort-peak-count').css('color', '#999');
                $('#sort-peak-count').css('font-weight', '300');
                $('#sort-summit-count').css('color', '#999');
                $('#sort-summit-count').css('font-weight', '300');
                $('#sort-name-letters').removeClass('hidden-lg');
                $('#sort-name-letters').removeClass('hidden-md');
                break;
            case 'peak_count':
                $('#sort-name').css('color', '#999');
                $('#sort-name').css('font-weight', '300');
                $('#sort-peak-count').css('color', '#F24100');
                $('#sort-peak-count').css('font-weight', '500');
                $('#sort-summit-count').css('color', '#999');
                $('#sort-summit-count').css('font-weight', '300');
                $('#sort-name-letters').addClass('hidden-lg');
                $('#sort-name-letters').addClass('hidden-md');
                break;
            case 'summit_count':
                $('#sort-name').css('color', '#999');
                $('#sort-name').css('font-weight', '300');
                $('#sort-peak-count').css('color', '#999');
                $('#sort-peak-count').css('font-weight', '300');
                $('#sort-summit-count').css('color', '#F24100');
                $('#sort-summit-count').css('font-weight', '500');
                $('#sort-name-letters').addClass('hidden-lg');
                $('#sort-name-letters').addClass('hidden-md');
                break;
            default:
                $('#sort-name').css('color', '#F24100');
                $('#sort-name').css('font-weight', '500');
                $('#sort-peak-count').css('color', '#999');
                $('#sort-peak-count').css('font-weight', '300');
                $('#sort-summit-count').css('color', '#999');
                $('#sort-summit-count').css('font-weight', '300');
                $('#sort-name-letters').removeClass('hidden-lg');
                $('#sort-name-letters').removeClass('hidden-md');
        }

        window.scrollTo(0, 0);
        $('#countries-list').empty();
        $('#ajax-data-loading').css('display', 'inline');
        $.getJSON('{% url "world_list_countries" %}?sort='+sort , function(data) {
            $.each( data, function( key, val ) {
                if (key=='countries') {
                    countriesArray = [['Country', 'Peaks']];
                    $('#ajax-data-loading').css('display', 'none');
                    var lastLetter = '';
                    var currentLetter = '';
                    $.each( val, function( countrykey, countryval ) {
                        currentLetter = countryval.name.substring(0,1).toUpperCase();
                        if (currentLetter != lastLetter) {
                            $('#countries-list').append('<div style="position: relative;"><a style="position: absolute; top: -170px;" name="'+currentLetter+'"></a></div>');
                        }
                        lastLetter = currentLetter;
                        $('#countries-list').append('<div class="row hover-row" style="margin-left: 0px; border-bottom: solid 1px #e0e0e0; background-color:#ffffff; cursor:pointer;" onclick="openUrl(\'/region/' + slugify(countryval.name) + '-mountains\')"><div class="col-lg-3 col-md-3 col-sm-3 col-xs-3 leftpeak-responsive" style="padding-right: 0px; padding-left: 0px; background-image: url(\'' + countryval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden; margin: 10px; border-radius: 12px;"><a style="color: #000000;" href="/region/' + slugify(countryval.name) + '-mountains"><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></a></div><div class="col-md-9 col-sm-9 col-xs-9 leftpeak-responsive" onclick="openUrl(\'/region/' + slugify(countryval.name) + '-mountains\');" style="display: flex; flex-direction: column; justify-content: center; cursor: pointer;  aspect-ratio:12/3; width: calc(75% - 20px) !important; margin-top: 12px;"><div><div class="country-title-pad-mobile" style="height: 16px;"></div><a class="country-title" href="/region/' + slugify(countryval.name) + '-mountains/">' + countryval.name + '</a><p class="country-info">' + numberWithCommas(countryval.peak_count) + ' peak' + ((countryval.peak_count!=1)?'s':'') + ' &bull; ' + numberWithCommas(countryval.summit_count) + ' climb' + ((countryval.summit_count!=1)?'s':'') + '</p></div></div></div>');
                        var countryItem = [countryval.name, parseFloat(countryval.peak_count)];
                        countriesArray.push(countryItem);
                    });
                    //draw the map
                    drawRegionsMap();
                }
            });
        });

    }

    function openUrl(url) {
        window.location.href = url;
    }

    $(document).ready(function() {
        google.load("visualization", "1", {packages:["geochart"], callback: function() { initialization() }});
    });

    function initialization() {
        var chartWidth = $('#regions_div').width();
        var chartHeight = $('#continents-list').height();
        if (chartHeight == 0) {
            chartHeight = chartWidth / 2.12;
        }
        $('#regions_div').height(chartHeight);

        var viewport_height = $(window).height();
        var viewport_width = $(window).width();
        var footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height() + chartHeight;
        var data_loading_margin = (viewport_height - footer_height) / 3;
        $('.data-loading').css('margin-top', data_loading_margin);
        $(window).resize(function() {
            viewport_height = $(window).height();
            viewport_width = $(window).width();
            footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height() + chartHeight;
            data_loading_margin = (viewport_height - footer_height) / 3;
            $('.data-loading').css('margin-top', data_loading_margin);
        });

        loadCountries('', '', 'name');

        $(window).scroll(function () {
        var max_scroll = $('#geochart').height();
        var navbar = $("#sort-by-bar");

        var scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
        if(scrollTop > max_scroll && !navbar.is(".regions-subnav-fixed")) {
                navbar.addClass("regions-subnav-fixed");
                // console.log("go floated");
        }
        else if(scrollTop < max_scroll && navbar.is(".regions-subnav-fixed") ) {
                // console.log("return to normal");
                navbar.removeClass("regions-subnav-fixed");
        }
        });

        $('.continent-link-div').width($('#continents-list').width()-30);
        var continentHeight = $('#continent-north-america').height();
        continentHeight = continentHeight+2;
        $('.continent-link-div').height(continentHeight);
        $('.continent-link-div').css('line-height',continentHeight+'px');

        $( window ).resize(function() {
          $('.continent-link-div').width($('#continents-list').width()-30);
          var continentHeight = $('#continent-north-america').height();
          continentHeight = continentHeight+2;
          $('.continent-link-div').height(continentHeight);
          $('.continent-link-div').css('line-height',continentHeight+'px');
        });

        $( ".continent-link-div" ).hover(
          function() {
            $( this ).css( 'background-color', 'background-color: rgba(0, 0, 0, 0.1)' );
          }, function() {
            $( this ).css( 'background-color', 'background-color: rgba(0, 0, 0, 0.0)' );
          }
        );
    }

</script>

{% endblock %}