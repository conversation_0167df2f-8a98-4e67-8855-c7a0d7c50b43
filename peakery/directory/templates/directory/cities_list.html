{% extends "base.html" %}
{% load static %}
{% load cache %}
{% load humanize %}
{% load directory_tags %}
{% block title %}{{ region.name }} mountains{% endblock %}
{% block titlemeta_overwrite %}{{ region.name }} mountains{% endblock %}
{% block description %}{{ meta_description }}{% endblock %}

{% block directory_active %}active{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row hidden-xs">
        <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 65px; border-radius: 12px 12px 0 0;"><span style="font-size: 24px; font-weight: 600;">
            <div id="breadcrumbs">
                <ul>
                    <li class="hidden-sm hidden-xs"><a style="font-weight: 500;" href="/region/{{ region.country.slug }}-mountains/">{{ region.country.name }}</a> &raquo; </li>
                    <li><h1 class="regions-title">{{ region.name }}</h1></li>
                </ul>
                <div class="pull-right hidden-xs" style="margin-top: -5px;">
                    <a style="{{ subnav_info_style }}" class="region-header-sub-links" href="/{{ region.country.slug }}-mountains/{{ region.slug }}/">Info</a><a style="{{ subnav_map_style }}" class="region-header-sub-links" href="/{{ region.country.slug }}-mountains/{{ region.slug }}/map/">Map</a><a style="{{ subnav_peaks_style }}" class="region-header-sub-links" href="/{{ region.country.slug }}-mountains/{{ region.slug }}/peaks/">Peaks</a><a style="{{ subnav_summits_style }}" class="region-header-sub-links" href="/{{ region.country.slug }}-mountains/{{ region.slug }}/summits/">Climbs</a><a style="{{ subnav_challenges_style }}" class="region-header-sub-links" href="/{{ region.country.slug }}-mountains/{{ region.slug }}/challenges/">Challenges</a>
                </div>
            </div>
        </span></div>
    </div>
{% endblock %}

{% block content %}

{% cache 3600 cities_list_top region.id %}

<style>

   @media screen and (max-width: 767px) and (min-width: 1px) {
       #content-body {
           margin-top: 20px;
       }
       .content-pane {
           margin-top: 50px;
       }
       .stats-data-bottom, .stats-data-highlight {
            margin-left: -6px;
        }
        .stats-data-highlight {
            margin-bottom: 5px;
            line-height: 20px;
        }
        .stats-data {
            margin-bottom: 5px;
        }
        .stats-header {
           font-size: 14px;
           margin-bottom: 10px;
       }
        .stats-grid-1 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-2 {
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-3 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-4 {
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-5 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-6 {
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-7 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-8 {
            border-bottom: 1px solid #c0c0c0;
        }
   }
   @media screen and (max-width: 1023px) and (min-width: 768px) {
        .stats-data {
            margin-bottom: 10px;
        }
       .stats-grid-1 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-2 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-3 {
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-4 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-5 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-6 {
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-7 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-8 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
    }
    @media screen and (min-width: 1024px) {
        .stats-data {
            margin-bottom: 10px;
        }
        .stats-grid-1 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-2 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-3 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-4 {
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-5 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-6 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-7 {
            border-right: 1px solid #c0c0c0;
            border-bottom: 1px solid #c0c0c0;
        }
        .stats-grid-8 {
            border-bottom: 1px solid #c0c0c0;
        }
    }
    @media screen and (min-width: 768px) {
        .content-pane {
           margin-top: 0px;
       }
    }

    .hover-cell:hover, .table-hover-cell:hover {
        background-image: linear-gradient(to bottom,#fde1d6,#fde1d6) !important;
    }

    @media screen and (max-width: 767px) and (min-width: 1px) {
        .featured-logs-thumbnail {
            display: none;
        }
    }

    @media screen and (min-width: 768px) {
        .featured-logs-thumbnail {
            float: left;
            width: 100px;
        }
        .featured-logs-description {
            margin-left: 120px;
        }
    }

</style>

<div class="container">
    <!-- Mobile header -->
    <div class="row sub-header-row hidden-lg hidden-md hidden-sm" style="height: 50px; position: fixed; width: 100%; z-index: 2;">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999; display: flex; justify-content: space-between;">
            <a style="{{ subnav_info_style }}" class="mobile-header-sub-links" href="/{{ region.country.slug }}-mountains/{{ region.slug }}/">Info</a><a style="{{ subnav_map_style }}" class="mobile-header-sub-links" href="/{{ region.country.slug }}-mountains/{{ region.slug }}/map/">Map</a><a style="{{ subnav_peaks_style }}" class="mobile-header-sub-links" href="/{{ region.country.slug }}-mountains/{{ region.slug }}/peaks/">Peaks</a><a style="{{ subnav_summits_style }}" class="mobile-header-sub-links" href="/{{ region.country.slug }}-mountains/{{ region.slug }}/summits/">Climbs</a><a style="{{ subnav_challenges_style }}" class="mobile-header-sub-links" href="/{{ region.country.slug }}-mountains/{{ region.slug }}/challenges/">Challenges</a>
        </div>
    </div>
    <!-- End mobile header -->

    <!-- Top photos block -->
    <div class="row content-pane">
        {% for p in top_peaks|slice:"0:1" %}
            <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 {{ p.hover_class }}" onclick="openUrl('/{{ p.slug }}');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ MEDIA_URL }}{{ p.thumbnail_url }}'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #fff;">
                <div>
                    <a href="/{{ p.slug }}/"><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></a>
                    <div class="{{ p.info_class }}">
                        <span class="photo-caption-peakname-only" style="position: absolute; bottom: 5px; left: 10px; color: #fff;">
                            <div class="bagger ellipsis"><a href="/{{ p.slug }}/" style="color: #fff;">{{ p.name }}</a></div>
                        </span>
                    </div>
                </div>
            </div>
        {% endfor %}
        {% for p in top_peaks|slice:"1:2" %}
            <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 {{ p.hover_class }}" onclick="openUrl('/{{ p.slug }}');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ MEDIA_URL }}{{ p.thumbnail_url }}'); background-size: cover; background-position: center center; background-repeat: no-repeat;">
                <div>
                    <a href="/{{ p.slug }}/"><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></a>
                    <div class="{{ p.info_class }}">
                        <span class="photo-caption-peakname-only" style="position: absolute; bottom: 5px; left: 10px; color: #fff;">
                            <div class="bagger ellipsis"><a href="/{{ p.slug }}/" style="color: #fff;">{{ p.name }}</a></div>
                        </span>
                    </div>
                </div>
            </div>
        {% endfor %}
        {% for p in top_peaks|slice:"2:3" %}
            <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 hidden-xs hidden-sm {{ p.hover_class }}" onclick="openUrl('/{{ p.slug }}');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ MEDIA_URL }}{{ p.thumbnail_url }}'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-left: solid 1px #fff;">
                <div>
                    <a href="/{{ p.slug }}/"><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></a>
                    <div class="{{ p.info_class }}">
                        <span class="photo-caption-peakname-only" style="position: absolute; bottom: 5px; left: 10px; color: #fff;">
                            <div class="bagger ellipsis"><a href="/{{ p.slug }}/" style="color: #fff;">{{ p.name }}</a></div>
                        </span>
                    </div>
                </div>
            </div>
        {% endfor %}
        {% for p in top_peaks|slice:"3:4" %}
            <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 hidden-xs {{ p.hover_class }}" onclick="openUrl('/{{ p.slug }}');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ MEDIA_URL }}{{ p.thumbnail_url }}'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-left: solid 1px #fff;">
                <div>
                    <a href="/{{ p.slug }}/"><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></a>
                    <div class="{{ p.info_class }}">
                        <span class="photo-caption-peakname-only" style="position: absolute; bottom: 5px; left: 10px; color: #fff;">
                            <div class="bagger ellipsis"><a href="/{{ p.slug }}/" style="color: #fff;">{{ p.name }}</a></div>
                        </span>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
    <!-- End top photos block -->

    <div class="row">
        <div onclick="openUrl('/{{ region.country.slug }}-mountains/{{ region.slug }}/peaks/');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell stats-grid-1" style="cursor: pointer; max-height: 200px;">
            <div>
                <h2 class="stats-header">{{ region.name }} peaks</h2>
                <p class="stats-data-highlight"><a href="/{{ region.country.slug }}-mountains/{{ region.slug }}/peaks/">{{ peak_count|intcomma }} peak{{ peak_count_raw|pluralize:"s" }}</a></p>
            </div>
        </div>
        {% if highest_peak.slug %}
        <div onclick="openUrl('/{{ highest_peak.slug }}');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell stats-grid-2" style="cursor: pointer; max-height: 200px;">
            <div>
                <h2 class="stats-header">Highest peak</h2>
                <p class="stats-data-highlight"><a href="/{{ highest_peak.slug }}/">{{ highest_peak.name }}</a></p>
                <p class="stats-data">{{ highest_peak_elevation }} ft / {{ highest_peak_elevation_in_meters }} m</p>
            </div>
        </div>
        {% else %}
        <div onclick="openUrl('/{{ highest_peak.slug }}');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell stats-grid-2" style="cursor: pointer; max-height: 200px;">
            <div>
                <h2 class="stats-header">Highest peak</h2>
                <p class="stats-data-missing">no info yet</p>
            </div>
        </div>
        {% endif %}
        {% if most_summited_peak.slug %}
        <div onclick="openUrl('/{{ most_summited_peak.slug }}');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell stats-grid-3" style="cursor: pointer; max-height: 200px;">
            <div>
                <h2 class="stats-header">Most climbed peak</h2>
                <p class="stats-data-highlight"><a href="/{{ most_summited_peak.slug }}/">{{ most_summited_peak.name }}</a></p>
                <p class="stats-data">{{ most_summited_peak_summits|intcomma }} climb{{ most_summited_peak_summits|pluralize:"s" }}</p>
            </div>
        </div>
        {% else %}
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell stats-grid-3" style="cursor: pointer; max-height: 200px;">
            <div>
                <h2 class="stats-header">Most climbed peak</h2>
                <p class="stats-data-missing">no climbs yet</p>
            </div>
        </div>
        {% endif %}
        {% if most_prominent_peak.slug %}
        <div onclick="openUrl('/{{ most_prominent_peak.slug }}');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell stats-grid-4" style="cursor: pointer; max-height: 200px;">
            <div>
                <h2 class="stats-header">Most prominent peak</h2>
                <p class="stats-data-highlight"><a href="/{{ most_prominent_peak.slug }}/">{{ most_prominent_peak.name }}</a></p>
                <p class="stats-data">{{ most_prominent_peak_prominence }} ft / {{ most_prominent_peak_prominence_in_meters }} m prom</p>
            </div>
        </div>
        {% else %}
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell stats-grid-4" style="cursor: pointer; max-height: 200px;">
            <div>
                <h2 class="stats-header">Most prominent peak</h2>
                <p class="stats-data-missing">no info yet</p>
            </div>
        </div>
        {% endif %}
        <div onclick="openUrl('/{{ region.country.slug }}-mountains/{{ region.slug }}/summits/');" class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive hover-cell stats-grid-5" style="cursor: pointer; max-height: 200px;">
            <div>
                <h2 class="stats-header">{{ region.name }} climbs</h2>
                {% if summit_count != '0' %}
                <p class="stats-data-highlight"><a href="/{{ region.country.slug }}-mountains/{{ region.slug }}/summits/">{{ summit_count|intcomma }} climb{{ summit_count_raw|pluralize:"s" }}</a></p>
                {% else %}
                <p class="stats-data-missing">no climbs yet</p>
                {% endif %}
            </div>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive stats-grid-6" style="max-height: 200px; background-color: #fff;">
            <div>
                <h2 class="stats-header">First Ascent Awards</h2>
                {% if first_ascent_count.first_ascent_count > 0 %}
                <p class="stats-data">{{ first_ascent_count.first_ascent_count }} of {{ peak_count }} peak{{ peak_count_raw|pluralize:"s" }} <span style="color: #999;">{{ first_ascent_count_pct }}%</span></p>
                {% else %}
                <p class="stats-data-missing">no awards yet</p>
                {% endif %}
            </div>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive stats-grid-7" style="max-height: 200px; background-color: #fff;">
            <div>
                <h2 class="stats-header">Top climbing months</h2>
                {% if top_three_months %}
                    {% for m in top_three_months %}
                        <p class="stats-data">{{ m.summitlog_month }} <span style="font-size: 16px; color: #999;">{{ m.pct_total }}%</span></p>
                    {% endfor %}
                {% else %}
                <p class="stats-data-missing">no info yet</p>
                {% endif %}
            </div>
        </div>
        <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 peakimg-responsive stats-grid-8" style="max-height: 200px; background-color: #fff;">
            <div>
                <h2 class="stats-header">Top ranges</h2>
                {% if top_three_ranges %}
                    {% for r in top_three_ranges %}
                        <p class="stats-data"><a href="/peaks/#range={{ r.range|urlencode }}">{{ r.range }}</a></p>
                    {% endfor %}
                {% else %}
                <p class="stats-data-missing">no info yet</p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="row dark-background-row">
        <div class="sp-60"></div>
    </div>

    {% endcache %}

    <div class="row" id="highlights-header">
        <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 70px; font-size: 18px; font-weight: 500;">
            <div style="float: left;">
                <h2 class="regions-title">{{ region.name }} mountains highlights</h2>
            </div>
            <div id="edit-highlights-link-div" class="pull-right">
                {% if request.user.is_authenticated %}
                    {% if highlights %}
                        <a id="edit-highlights-link" style="cursor: pointer; color: #00B1F2; font-weight: 500; font-size: 14px;">edit highlights</a>
                    {% else %}
                        <a id="edit-highlights-link" style="cursor: pointer; color: #00b1f2; font-weight: 500; font-size: 14px;">add a highlight!</a>
                    {% endif %}
                {% else %}
                    {% if highlights %}
                        <a data-toggle="modal" data-target="#accounts-login" style="cursor: pointer; color: #00B1F2; font-weight: 500; font-size: 14px;">edit highlights</a>
                    {% else %}
                        <a data-toggle="modal" data-target="#accounts-login" style="cursor: pointer; color: #00b1f2; font-weight: 500; font-size: 14px;">add a highlight!</a>
                    {% endif %}
                {% endif %}
            </div>
        </div>
    </div>
    <div class="row" id="highlights-content" style="{% if not highlights %}display: none; {% endif %}background-color: #fff; border-top: 1px solid #c0c0c0; padding-top: 20px; padding-left: 8px;{% if not highlights %} display: none;{% endif %}">
        <div class="col-md-12">
            <div class="peak_desc">
                <div class="description highlights-info-content">
                    <ul id="highlights-list">
                    {% if highlights %}
                        {% for h in highlights %}
                            <li style="list-style: initial; margin-bottom: 20px; font-size: 16px; line-height: 28px;">{{ h.highlight }}</li>
                        {% endfor %}
                    {% endif %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="row" id="edit-highlights-header" style="display: none;">
        <div class="col-md-12" style="height: 70px; background-color: #feece5; line-height: 70px; font-size: 18px; font-weight: 500;">
            <div style="float: left;">
                <span style="color: #f24100;"><h2 class="regions-title">{{ region.name }} mountains highlights</h2></span>
            </div>
        </div>
    </div>
    <div class="row" id="edit-highlights-form" style="display: none; background-color: #feece5; border-top: 1px solid #c0c0c0; padding-top: 20px; padding-left: 10px;">
        <div class="col-md-12">
            <div class="peak_desc">
                <div class="description highlights-info-content" style="margin-left: -10px;">
                    <form id="edithighlights_form" method="POST" action="/region/edit_region_highlights/{{ region.id }}/">
                    <fieldset id="peak-highlights-fieldset">
                    {% if highlights %}
                        {% for h in highlights %}
                            <div>
                                <textarea class="peak-highlight-input" name="peak-highlight-{{ forloop.counter }}"  data-index="{{ forloop.counter }}" id="peak-highlight-{{ forloop.counter }}" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ region.name }} mountains...">{{ h.highlight }}</textarea>
                            </div>
                            {% if forloop.last %}
                            <div>
                                <textarea class="peak-highlight-input" name="peak-highlight-{{ forloop.counter|add:1 }}" data-index="{{ forloop.counter|add:1 }}" id="peak-highlight-new" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ region.name }} mountains..."></textarea>
                            </div>
                            {% endif %}
                        {% endfor %}
                    {% else %}
                        <div>
                            <textarea class="peak-highlight-input" name="peak-highlight-1" data-index="1" id="peak-highlight-1" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ region.name }} mountains..."></textarea>
                        </div>
                    {% endif %}
                    </fieldset>
                    <button style="float: left; width: 170px; height: 50px; font-size: 16px; padding: 0 20px;" class="btn set2 input" id="edit-highlights-save" type="submit">Save highlights</button>&nbsp;<a style="float: left; margin-left: 30px; margin-top: 17px; cursor: pointer; color: #999;" id="edit-highlights-cancel">Cancel</a>
                    </form>
                </div>
            </div>
        </div>
    </div>

    {% if featured_logs %}

    <div class="row dark-background-row">
        <div class="sp-60"></div>
    </div>

    <div class="row" id="featured-logs-header">
        <div class="col-md-12 region-info-section-header" style="height: 70px; background-color: #fff; line-height: 70px; font-weight: 500;">
            <h2 class="section-header">Latest climbs</h2>
        </div>
    </div>

    <div class="row sub-header-row" style="background-color: #fff; border-top: 1px solid #c0c0c0;">
        <div class="col-md-12" style="padding-left: 0px; padding-right: 0px;">
            <div class="peak_desc" style="padding: 0px;">
                <div class="description" style="margin-bottom: 0px; padding-top: 0px;">
                    {% for l in featured_logs %}
                        <div class="hover-cell" onclick="openUrl('/{{ l.peak_slug }}/summits/{{ l.id }}/');" style="display: inline-block; padding: 15px; cursor: pointer; background-image: linear-gradient(to bottom,#fff,#f6f6f6);">
                            <div class="featured-logs-thumbnail">
                                <img class="hover-photos" src="{{ MEDIA_URL }}{{ l.thumbnail_url }}" style="width: 100px;">
                            </div>
                            <div class="featured-logs-description">"{{ l.log_text }}" &mdash; <i><span style="color: #00b1f2; font-weight: 500;">{{ l.username }} &bull; {{ l.summitlog_date|date:"M j, Y" }}</span></i></div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    {% endif %}

    {% cache 3600 cities_list_bottom region.id %}

    <div class="row dark-background-row">
        <div class="sp-60"></div>
    </div>

    <div class="row">
        <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 70px; font-size: 18px; font-weight: 500;"><h2 class="regions-title">Popular {{ region.name }} mountains</h2></div>
    </div>
    <div class="row">
        {% for p in top_peaks|slice:"4:28" %}
            <div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 {{ p.hover_class }} photo-grid-{{ forloop.counter }}" onclick="openUrl('/{{ p.slug }}');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ MEDIA_URL }}{{ p.thumbnail_url }}'); background-size: cover; background-position: center center; background-repeat: no-repeat;">
                <div>
                    <a href="/{{ p.slug }}/"><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></a>
                    <div class="{{ p.info_class }}">
                        <span class="photo-caption-peakname-only" style="position: absolute; bottom: 5px; left: 10px; color: #fff;">
                            <div class="bagger ellipsis"><a href="/{{ p.slug }}/" style="color: #fff;">{{ p.name }}</a></div>
                        </span>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>

    <div class="row hidden-lg hidden-md hidden-sm">
        <div style="height: 180px;"></div>
    </div>
    <div class="row hidden-xs">
        <div style="height: 55px;"></div>
    </div>
</div>

<script type="text/javascript">

    var initial_highlights = [];
    {% for h in highlights %}
        initial_highlights.push('{{ h.highlight }}');
    {% endfor %}

    function openUrl(url) {
        window.location.href = url;
    }

    $(document).ready(function() {

        //highlights stuff
        {% if request.user.is_authenticated %}
        $('#edit-highlights-link-div').on('click', 'a', function() {
            $('#highlights-header').hide();
            $('#highlights-content').hide();
            $('#edit-highlights-header').fadeIn(300);
            $('#edit-highlights-form').fadeIn(300, function () {
                autosize($('.peak-highlight-input'));
                autosize.update($('.peak-highlight-input'));
            });
            return false;
        });
        {% else %}
        $('#edit-highlights-link-div').on('click', 'a', function() {
            if ($('#navbar-login-link').is(':visible')) {
                $('#navbar-login-link').click();
            }
            return false;
        });
        {% endif %}

        $('#edit-highlights-cancel').click(function(){
            $('#peak-highlights-fieldset').empty();
            var new_index = 1;
            var haveHighlights = false;
            for (var i = 0; i < initial_highlights.length; i++) {
                $('#peak-highlights-fieldset').append('<div><textarea class="peak-highlight-input" name="peak-highlight-' + new_index + '"  data-index="' + new_index + '" id="peak-highlight-' + new_index + '" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ region.name }} mountains...">'+initial_highlights[i]+'</textarea></div>');
                haveHighlights = true;
                new_index++;
            }
            $('#peak-highlights-fieldset').append('<div><textarea class="peak-highlight-input" name="peak-highlight-' + new_index + '" data-index="' + new_index + '" id="peak-highlight-new" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ region.name }} mountains..."></textarea></div>');
            $('#edit-highlights-header').hide();
            $('#edit-highlights-form').hide();
            $('#highlights-header').fadeIn(300);
            if (haveHighlights) {
                $('#highlights-content').fadeIn(300);
                $('#edit-highlights-link').html('edit highlights');
            } else {
                $('#highlights-content').fadeOut(300);
                $('#edit-highlights-link').html('add a highlight!');
            }
            return false;
        });

        $('#peak-highlights-fieldset').on('keyup', 'textarea', function() {
            var index = $(this).data('index');
            var num_fields = $('.peak-highlight-input').length;
            if (index == num_fields && $(this).val().length > 0) {
                var new_index = index + 1;
                if ($('#peak-highlight-' + new_index).length == 0) {
                    $('#peak-highlights-fieldset').append('<div><textarea class="peak-highlight-input" name="peak-highlight-' + new_index + '"  data-index="' + new_index + '" id="peak-highlight-' + new_index + '" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ region.name }} mountains..."></textarea></div>');
                }
            }
            autosize($('.peak-highlight-input'));
            autosize.update($('.peak-highlight-input'));
            return false;
        });

        // process the form
        $('#edit-highlights-save').click(function(event) {

            var url = "/region/edit_region_highlights/{{ region.id }}/";
            $('#edit-highlights-save').html('<i class="fa fa-spinner fa-spin fa-fw"></i>');
            $('#edit-highlights-save').prop("disabled",true);

            $.ajax({
                type: "POST",
                url: url,
                data: $("#edithighlights_form").serialize(),
                success: function(data)
                {
                    //console.log(data);
                    //update page with new highlights
                    $('#peak-highlights-fieldset').empty();
                    $('#highlights-list').empty();
                    var haveHighlights = false;
                    $.each( data, function( key, val ) {
                        if (key=='highlights') {
                            new_index = 1;
                            initial_highlights = [];
                            $.each( val, function( highlightkey, highlightval ) {
                                //console.log(highlightval);
                                haveHighlights = true;
                                $('#peak-highlights-fieldset').append('<div><textarea class="peak-highlight-input" name="peak-highlight-' + new_index + '"  data-index="' + new_index + '" id="peak-highlight-' + new_index + '" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ region.name }} mountains...">'+highlightval+'</textarea></div>');
                                $('#highlights-list').append('<li style="list-style: initial; margin-bottom: 20px; font-size: 16px; line-height: 28px;">'+highlightval+'</li>');
                                initial_highlights.push(highlightval);
                                new_index++;
                            });
                        }
                    });
                    $('#peak-highlights-fieldset').append('<div><textarea class="peak-highlight-input" name="peak-highlight-' + new_index + '" data-index="' + new_index + '" id="peak-highlight-new" style="width: 100%; font-size: 18px; padding-left: 10px; margin-bottom: 20px;" placeholder="write a 1-2 sentence highlight about {{ region.name }} mountains..."></textarea></div>');
                    $('#edit-highlights-link-div').html('<a id="edit-highlights-link" style="cursor: pointer; color: #00B1F2; font-weight: 500;">edit highlights</a>');
                    $('#edit-highlights-save').html('Save highlights');
                    $('#edit-highlights-save').prop("disabled",false);
                    $('#edit-highlights-header').hide();
                    $('#edit-highlights-form').hide();
                    $('#highlights-header').fadeIn(300);
                    if (haveHighlights) {
                        $('#highlights-content').fadeIn(300);
                        $('#edit-highlights-link').html('edit highlights');
                    } else {
                        $('#highlights-content').fadeOut(300);
                        $('#edit-highlights-link').html('add a highlight!');
                    }
                }
            });
            event.preventDefault();
        });

    });

</script>

{% endcache %}

{% endblock %}
