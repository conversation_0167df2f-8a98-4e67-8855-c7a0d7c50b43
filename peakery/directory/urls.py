from django.urls import path, re_path

from peakery.directory.views import world_list, world_list_challenges, countries_list, regions_list, cities_list

urlpatterns = [
    path('', world_list, name='world_list'),
    path('challenges/', world_list_challenges, name='world_list_challenges'),
    re_path(r'^by_(?P<order>name)/$', countries_list, name='countries_list'),
    re_path(r'^(?P<continent_code>[-\w]+)/$', countries_list, name='countries_list'),
    re_path(r'^(?P<continent_code>[-\w]+)/(?P<country_code>[-\w]+)/$', regions_list, name='regions_list'),
    # url(r'^(?P<continent_code>[-\w]+)/(?P<country_code>[-\w]+)/(?P<page>\d+)/$', regions_list, name='regions_list'),
    re_path(r'^(?P<continent_code>[-\w]+)/(?P<country_code>[-\w]+)/(?P<region_slug>[-\w]+)/$', cities_list, name='cities_list'),
]