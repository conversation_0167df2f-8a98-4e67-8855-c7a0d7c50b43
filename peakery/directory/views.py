from django.http import Http404
from django.http import HttpResponse
from django.shortcuts import render
from django.shortcuts import get_object_or_404
from peakery.cities.models import Continent, Country, Region, ContinentHighlight, ContinentHighlightLogGroup, CountryHighlight, CountryHighlightLogGroup, RegionHighlight, RegionHighlightLogGroup
from peakery.items.models import Item
from django.conf import settings
import locale, datetime
locale.setlocale(locale.LC_ALL, settings.LOCALE)
from django.db import connection
from django.contrib.auth.decorators import login_required
import json
from django.views.decorators.cache import cache_page
from peakery.items.utils import commify
from peakery.cache import cache_manager

CONTINENTS = {
    "North-America":"NA",
    "Asia":"AS",
    "Europe":"EU",
    "Africa":"AF",
    "Antarctica":"AN",
    "South-America":"SA",
    "Oceania":"OC",
}

def dictfetchall(cursor):
    "Return all rows from a cursor as a dict"
    columns = [col[0] for col in cursor.description]
    return [
        dict(zip(columns, row))
        for row in cursor.fetchall()
    ]

def url_resolver(request, slug, slug_region=None, order = 'peaks', page = 1, view = 'info'):
    is_continent = CONTINENTS.get(slug)

    if view == 'peaks':
        if is_continent:
            return countries_list_peaks(request, is_continent, order= order)
        try:
            slug = slug.replace('-mountains','').replace('peaks-','')
            country = Country.objects.get(slug=slug)
            if slug_region is None:
                return regions_list_peaks(request, country.continent, country.code, order = order, page = page)
            else:
                return cities_list_peaks(request, country.continent, country.code, slug_region)
        except Country.DoesNotExist:
            pass

    if view == 'map':
        if is_continent:
            return countries_list_map(request, is_continent, order= order)
        try:
            slug = slug.replace('-mountains','').replace('peaks-','')
            country = Country.objects.get(slug=slug)
            if slug_region is None:
                return regions_list_map(request, country.continent, country.code, order = order, page = page)
            else:
                return cities_list_map(request, country.continent, country.code, slug_region)
        except Country.DoesNotExist:
            pass

    if view == 'summits':
        if is_continent:
            return countries_list_summits(request, is_continent, order= order)
        try:
            slug = slug.replace('-mountains','').replace('peaks-','')
            country = Country.objects.get(slug=slug)
            if slug_region is None:
                return regions_list_summits(request, country.continent, country.code, order = order, page = page)
            else:
                return cities_list_summits(request, country.continent, country.code, slug_region)
        except Country.DoesNotExist:
            pass

    if view == 'challenges':
        if is_continent:
            return countries_list_challenges(request, is_continent)
        try:
            slug = slug.replace('-mountains','').replace('peaks-','')
            country = Country.objects.get(slug=slug)
            if slug_region is None:
                return regions_list_challenges(request, country.continent, country.code, order = order, page = page)
            else:
                return cities_list_challenges(request, country.continent, country.code, slug_region)
        except Country.DoesNotExist:
            pass

    if is_continent:
        return countries_list(request, is_continent, order= order)
    try:
        slug = slug.replace('-mountains','').replace('peaks-','')
        country = Country.objects.get(slug=slug)
        if slug_region is None:
            return regions_list(request, country)
        else:
            return cities_list(request, country.continent, country.code, slug_region)
    except Country.DoesNotExist:
        pass

    raise Http404

#@cache_page(60 * 60)
def world_list(request):

    sql = "select " + \
        "a.id, " + \
        "a.name, " + \
        "a.slug, " + \
        "count(d.id) as peak_count " + \
        "from cities_continent a, cities_country b, items_item_country c, items_item d " + \
        "where a.code = b.continent " + \
        "and b.id = c.country_id " + \
        "and c.item_id = d.id " + \
        "group by " + \
        "a.id, " + \
        "a.name, " + \
        "a.slug " + \
        "order by " + \
        "case when a.id = 1 then '1' when a.id = 2 then '2' when a.id = 3 then '4' when a.id = 4 then '3' when a.id = 5 then '7' when a.id = 6 then '8' when a.id = 7 then '6' when a.id = 8 then '5' else '9' end"
    continents = Country.objects.raw(sql)

    sql = "select " + \
        "b.id, " + \
        "b.name, " + \
        "b.slug, " + \
        "b.code, " + \
        "count(d.id) as peak_count " + \
        "from cities_country b, items_item_country c, items_item d " + \
        "where b.id = c.country_id " + \
        "and c.item_id = d.id " + \
        "group by " + \
        "b.id, " + \
        "b.name, " + \
        "b.slug, " + \
        "b.code " + \
        "order by b.name"
    countries = Country.objects.raw(sql)

    #number of peaks in the world
    sql = "select count(a.id) as peak_count " + \
        "from items_item a "

    with connection.cursor() as cursor:
        cursor.execute(sql)
        if cursor.rowcount > 0:
            world_stats = dictfetchall(cursor)[0]
            peak_total = world_stats.get('peak_count')
        else:
            peak_total = 0


    nav_regions_style = 'color: #00B1F2;'
    nav_page_name = 'Peaks'

    meta_description = "Includes %s mountains around the world! Choose a country to see hiking info, trail maps, and trip reports for every peak." % (commify(peak_total))

    return render(request, 'directory/world_list.html', {
        'continents':continents,
        'countries':countries,
        'peak_total':locale.format_string('%d', peak_total, grouping=True),
        'nav_regions_style':nav_regions_style,
        'nav_page_name':nav_page_name,
        'fixed_subnav_class':'fixed-subnav-header',
        'meta_description':meta_description
    })

@cache_page(60 * 60)
def world_list_challenges(request):

    subnav_challenges_style = 'color: #FF0000; font-weight: 500;'

    #Challenges stats
    sql = "select a.id, " + \
        "count(b.id) as challenge_count " + \
        "from cities_continent a " + \
        "left join items_itemgroup b on b.continent = a.id " + \
        "where a.id = 1 and b.show_in_world = true " + \
        "group by a.id "

    with connection.cursor() as cursor:

        cursor.execute(sql)
        if cursor.rowcount > 0:
            world_stats = dictfetchall(cursor)[0]
            challenge_count = world_stats.get('challenge_count')
        else:
            challenge_count = 0


    nav_page_name = 'Worldwide Challenges'
    nav_regions_style = 'color: #00B1F2;'

    return render(request, 'directory/world_list_challenges.html', {
        'challenge_count': locale.format_string('%d', challenge_count, grouping=True),
        'subnav_challenges_style': subnav_challenges_style,
        'nav_page_name':nav_page_name,
        'nav_regions_style':nav_regions_style
    })

@cache_page(60 * 60)
def countries_list(request, continent_code=None, order='peaks'):

    continent = None

    if continent_code:
        for c in CONTINENTS.items():
            if c[1] == continent_code:
                continent_slug = c[0]
                continent = c[0].replace('-', ' ')

    continent = get_object_or_404(Continent, code=continent_code)

    #Top twentyfive peak photos
    sql = "select a.id, a.name, a.slug_new_text as slug, " + \
        "get_thumb(a.thumbnail, 480) as thumbnail_url, " + \
        "case when length(a.thumbnail) > 0 then 'hover-photos' else '' end as hover_class, " + \
        "case when length(a.thumbnail) > 0 then 'user-photo-info' else 'empty-photo-info' end as info_class " + \
        "from items_item a, items_item_country b, cities_country c " + \
        "where c.continent = %s " + \
        "and c.id = b.country_id " + \
        "and b.item_id = a.id " + \
        "and length(a.thumbnail) > 0 " + \
        "order by a.summitlog_count desc " + \
        "limit 25"
    top_peaks = Item.objects.raw(sql, [continent_code])

    #Peaks/summits stats
    sql = "select " + \
        "a.id, " + \
        "count(d.id) as peak_count, " + \
        "sum(d.summitlog_count) as summit_count " + \
        "from cities_continent a, cities_country b, items_item_country c, items_item d " + \
        "where a.code = b.continent and b.continent = %s and b.id = c.country_id " + \
        "and c.item_id = d.id group by a.id "

    with connection.cursor() as cursor:

        cursor.execute(sql, [continent_code])

        if cursor.rowcount > 0:
            continent_stats = dictfetchall(cursor)[0]
        else:
            continent_stats = None

    #Challenge count
    sql = "select " + \
        "a.id, " + \
        "count(distinct e.id) as challenge_count " + \
        "from cities_continent a " + \
        "left join items_itemgroup e on e.continent = a.id " + \
        "where a.code = %s " + \
        "group by a.id "

    with connection.cursor() as cursor:

        cursor.execute(sql, [continent_code])
        if cursor.rowcount > 0:
            challenge_stats = dictfetchall(cursor)[0]
            challenge_count = challenge_stats.get('challenge_count')
        else:
            challenge_count = 0

    #Highest peak
    sql = "select a.name, a.slug_new_text as slug, a.elevation, floor(a.elevation*.3048) as elevation_in_meters " + \
        "from items_item a, items_item_country b, cities_country c " + \
        "where c.continent = %s " + \
        "and c.id = b.country_id " + \
        "and b.item_id = a.id " + \
        "order by a.elevation desc " + \
        "limit 1 "

    with connection.cursor() as cursor:

        cursor.execute(sql, [continent_code])

        highest_peak = dictfetchall(cursor)[0]

    #Most summited peak
    sql = "select a.name, a.slug_new_text as slug, a.summitlog_count " + \
        "from items_item a, items_item_country b, cities_country c " + \
        "where c.continent = %s " + \
        "and c.id = b.country_id " + \
        "and b.item_id = a.id " + \
        "order by a.summitlog_count desc " + \
        "limit 1 "

    with connection.cursor() as cursor:

        cursor.execute(sql, [continent_code])

        most_summited_peak = dictfetchall(cursor)[0]

    #Most prominent peak
    sql = "select a.name, a.slug_new_text as slug, a.prominence, floor(a.prominence*.3048) as prominence_in_meters " + \
        "from items_item a, items_item_country b, cities_country c " + \
        "where c.continent = %s " + \
        "and c.id = b.country_id " + \
        "and b.item_id = a.id " + \
        "and a.prominence is not null " + \
        "order by a.prominence desc " + \
        "limit 1 "

    with connection.cursor() as cursor:

        cursor.execute(sql, [continent_code])

        most_prominent_peak = dictfetchall(cursor)[0]

    #Top three countries
    sql = "select c.id, c.name, c.slug, count(a.id) as summit_count " + \
        "from items_summitlog a, items_item_country b, cities_country c " + \
        "where c.continent = %s " + \
        "and c.id = b.country_id " + \
        "and b.item_id = a.item_id " + \
        "and a.attempt = false and a.status = 1 " + \
        "group by c.id, c.name, c.slug " + \
        "order by count(a.id) desc " + \
        "limit 3"
    top_three_countries = Country.objects.raw(sql, [continent_code])

    #Top climbing months
    sql = "select to_char(a.date, 'Month') as summitlog_month, count(a.id) as summitlog_count " + \
        "from items_summitlog a, items_item_country b, cities_country c " + \
        "where c.continent = %s " + \
        "and c.id = b.country_id " + \
        "and b.item_id = a.item_id " + \
        "group by to_char(a.date, 'Month') " + \
        "order by count(a.id) desc "

    with connection.cursor() as cursor:

        cursor.execute(sql, [continent_code])

        top_climbing_months = dictfetchall(cursor)

    top_climbing_months_total = 0
    for m in top_climbing_months:
        top_climbing_months_total = top_climbing_months_total + m.get('summitlog_count')
    top_three_months = []
    keys = ['summitlog_month', 'summitlog_count', 'pct_total']
    for m in top_climbing_months[:3]:
        values = [m.get('summitlog_month'), m.get('summitlog_count'), int(100 * float(m.get('summitlog_count'))/float(top_climbing_months_total))]
        top_three_months.append(dict(zip(keys, values)))

    #Top three ranges
    sql = "select a.range, count(a.id) as peak_count " + \
        "from items_item a, items_item_country b, cities_country c " + \
        "where c.continent = %s " + \
        "and c.id = b.country_id " + \
        "and b.item_id = a.id " + \
        "and length(a.range) > 0 " + \
        "group by a.range " + \
        "order by count(a.id) desc " + \
        "limit 3"
    with connection.cursor() as cursor:

        cursor.execute(sql, [continent_code])

        top_three_ranges = dictfetchall(cursor)

    region_code = settings.GEOCHART_REGIONS[continent_code]

    #Highlights
    highlights = ContinentHighlight.objects.filter(continent_id = continent.id)

    #Featured summit logs

    sql = "select a.id, case when length(a.log) > 1200 then concat(left(a.log, 1200),'...') else a.log end as log_text, a.date as summitlog_date, b.username, e.name as peak_name, e.slug_new_text as peak_slug, " + \
        "case when min(g.image) is not null then get_thumb(min(g.image), 480) else coalesce(replace(replace(f.avatar, '/', '/resized/100/'), 'avatars/resized/100/', 'avatars/'),'img/default-user.png') end as thumbnail_url " + \
        "from items_summitlog a " + \
        "join auth_user b on b.id = a.user_id " + \
        "join items_item_country c on c.item_id = a.item_id " + \
        "join cities_country d on d.id = c.country_id " + \
        "join items_item e on e.id = a.item_id " + \
        "left join avatar_avatar f on f.user_id = a.user_id " + \
        "left join items_itemphoto g on g.summit_log_id = a.id " + \
        "where d.continent = %s and a.status = 1 and a.date_entered = true and length(a.log) >= 175 " + \
        "group by a.id, log_text, a.date, b.username, e.name, e.slug_new_text, f.avatar " + \
        "order by a.date desc limit 5 "

    with connection.cursor() as cursor:

        cursor.execute(sql, [continent_code])

        featured_logs = dictfetchall(cursor)


    nav_regions_style = 'color: #00B1F2;'
    subnav_info_style = 'color: #FF0000; font-weight: 500;'
    nav_page_name = continent.name

    meta_description = continent.get_meta_description(continent_stats.get('peak_count'))

    return render(request, 'directory/countries_list.html', {
        'continent':continent,
        'continent_code': continent_code,
        'continent_slug': continent_slug,
        'top_peaks': top_peaks,
        'region_code': region_code,
        'peak_count': locale.format_string('%d', continent_stats.get('peak_count'), grouping=True),
        'peak_count_raw': continent_stats.get('peak_count'),
        'summit_count': locale.format_string('%d', continent_stats.get('summit_count'), grouping=True),
        'summit_count_raw': continent_stats.get('summit_count'),
        'challenge_count': locale.format_string('%d', challenge_count, grouping=True),
        'highest_peak': highest_peak,
        'highest_peak_elevation': locale.format_string('%d', highest_peak.get('elevation'), grouping=True),
        'highest_peak_elevation_in_meters': locale.format_string('%d', highest_peak.get('elevation_in_meters'), grouping=True),
        'most_summited_peak': most_summited_peak,
        'most_summited_peak_summits': locale.format_string('%d', most_summited_peak.get('summitlog_count'), grouping=True),
        'most_prominent_peak': most_prominent_peak,
        'most_prominent_peak_prominence': locale.format_string('%d', most_prominent_peak.get('prominence'), grouping=True),
        'most_prominent_peak_prominence_in_meters': locale.format_string('%d', most_prominent_peak.get('prominence_in_meters'), grouping=True),
        'top_three_countries': top_three_countries,
        'top_three_months': top_three_months,
        'top_three_ranges': top_three_ranges,
        'nav_regions_style': nav_regions_style,
        'subnav_info_style': subnav_info_style,
        'nav_page_name':nav_page_name,
        'highlights':highlights,
        'featured_logs':featured_logs,
        'meta_description':meta_description
    })

@cache_page(60 * 60)
def countries_list_peaks(request, continent_code=None, order='peaks'):

    continent = None

    if continent_code:
        for c in CONTINENTS.items():
            if c[1] == continent_code:
                continent_slug = c[0]
                continent = c[0].replace('-', ' ')

    continent = get_object_or_404(Continent, code=continent_code)

    subnav_peaks_style = 'color: #FF0000; font-weight: 500;'

    #Peaks/summits stats
    sql = "select " + \
        "a.id, " + \
        "count(d.id) as peak_count, " + \
        "sum(d.summitlog_count) as summit_count " + \
        "from cities_continent a, cities_country b, items_item_country c, items_item d " + \
        "where a.code = b.continent and b.continent = %s and b.id = c.country_id " + \
        "and c.item_id = d.id and d.active = true group by a.id "

    with connection.cursor() as cursor:

        cursor.execute(sql, [continent_code])

        continent_stats = dictfetchall(cursor)[0]

    nav_regions_style = 'color: #00B1F2;'
    nav_page_name = continent.name

    meta_description = continent.get_meta_description_peaks(continent_stats.get('peak_count'))

    return render(request, 'directory/countries_list_peaks.html', {
        'continent':continent,
        'continent_code': continent_code,
        'continent_slug': continent_slug,
        'peak_count': continent_stats.get('peak_count'),
        'summit_count': continent_stats.get('summit_count'),
        'subnav_peaks_style': subnav_peaks_style,
        'nav_regions_style':nav_regions_style,
        'nav_page_name':nav_page_name,
        'meta_description':meta_description
    })


def countries_list_map(request, continent_code=None, order='peaks'):

    continent = None

    if continent_code:
        for c in CONTINENTS.items():
            if c[1] == continent_code:
                continent_slug = c[0]
                continent = c[0].replace('-', ' ')

    continent = get_object_or_404(Continent, code=continent_code)

    continent_stats = cache_manager.get_countries_list_map_continent_stats(continent_code)

    if not continent_stats:

        #Peaks/summits stats
        sql = "select " + \
            "a.id, " + \
            "count(d.id) as peak_count, " + \
            "sum(d.summitlog_count) as summit_count " + \
            "from cities_continent a, cities_country b, items_item_country c, items_item d " + \
            "where a.code = b.continent and b.continent = %s and b.id = c.country_id " + \
            "and c.item_id = d.id group by a.id "

        with connection.cursor() as cursor:

            cursor.execute(sql, [continent_code])

            continent_stats = dictfetchall(cursor)[0]

        cache_manager.set_countries_list_map_continent_stats(continent_code, continent_stats)

    subnav_map_style = 'color: #FF0000; font-weight: 500;'
    nav_regions_style = 'color: #00B1F2;'
    nav_page_name = continent.name

    meta_description = continent.get_meta_description_map(continent_stats.get('peak_count'))

    return render(request, 'directory/countries_list_map.html', {
        'continent':continent,
        'continent_code': continent_code,
        'continent_slug': continent_slug,
        'subnav_map_style': subnav_map_style,
        'nav_regions_style':nav_regions_style,
        'nav_page_name':nav_page_name,
        'meta_description':meta_description
    })

@cache_page(60 * 60)
def countries_list_summits(request, continent_code=None, order='peaks'):

    continent = None

    if continent_code:
        for c in CONTINENTS.items():
            if c[1] == continent_code:
                continent_slug = c[0]
                continent = c[0].replace('-', ' ')

    continent = get_object_or_404(Continent, code=continent_code)

    subnav_summits_style = 'color: #FF0000; font-weight: 500;'

    #Peaks/summits stats
    sql = "select " + \
        "a.id, " + \
        "count(d.id) as peak_count, " + \
        "sum(d.summitlog_count) as summit_count " + \
        "from cities_continent a, cities_country b, items_item_country c, items_item d " + \
        "where a.code = b.continent and b.continent = %s and b.id = c.country_id " + \
        "and c.item_id = d.id group by a.id "

    with connection.cursor() as cursor:

        cursor.execute(sql, [continent_code])

        continent_stats = dictfetchall(cursor)[0]

    nav_regions_style = 'color: #00B1F2;'
    nav_page_name = continent.name

    meta_description = continent.get_meta_description_summits(continent_stats.get('summit_count'))

    return render(request, 'directory/countries_list_summits.html', {
        'continent':continent,
        'continent_code': continent_code,
        'continent_slug': continent_slug,
        'peak_count': continent_stats.get('peak_count'),
        'summit_count': continent_stats.get('summit_count'),
        'subnav_summits_style': subnav_summits_style,
        'nav_regions_style':nav_regions_style,
        'nav_page_name':nav_page_name,
        'meta_description':meta_description
    })


@cache_page(60 * 60)
def countries_list_challenges(request, continent_code=None):
    if continent_code:
        for c in CONTINENTS.items():
            if c[1] == continent_code:
                continent_slug = c[0]

    continent = get_object_or_404(Continent, code=continent_code)

    subnav_challenges_style = 'color: #FF0000; font-weight: 500;'

    #Challenges stats
    sql = "select count(distinct b.id) as challenge_count " + \
        "from cities_country a " + \
        "left join items_itemgroup b on b.show_in_continent = true " + \
        "where a.continent = %s " + \
        "and exists (select 1 from items_item_country x, items_itemgroupitem y where a.id = x.country_id and x.item_id = y.item_id and y.group_id = b.id " + \
            "and not exists (select 1 from items_item_country xx where xx.country_id != x.country_id and xx.item_id = y.item_id)) "

    with connection.cursor() as cursor:
        cursor.execute(sql, [continent_code])
        continent_stats = dictfetchall(cursor)
        if continent_stats:
            continent_stats = continent_stats[0]
            challenge_count = continent_stats.get('challenge_count')
        else:
            challenge_count = 0


    #  Featured summit logs
    sql = "select a.id, left(a.log, 600) as log_text, a.date as summitlog_date, b.username, e.name as peak_name, e.slug_new_text as peak_slug " + \
        "from items_summitlog a " + \
        "join auth_user b on b.id = a.user_id " + \
        "join items_item_country c on c.item_id = a.item_id " + \
        "join cities_country d on d.id = c.country_id " + \
        "join items_item e on e.id = a.item_id " + \
        "where d.continent = %s and a.status = 1 and a.date_entered = true and length(a.log) >= 175 " + \
        "order by a.date desc limit 5 "

    with connection.cursor() as cursor:
        cursor.execute(sql, [continent_code])
        featured_logs = dictfetchall(cursor)

    nav_regions_style = 'color: #00B1F2;'
    nav_page_name = continent.name

    meta_description = continent.get_meta_description_challenges(challenge_count)

    region_code = settings.GEOCHART_REGIONS[continent_code]

    return render(request, 'directory/countries_list_challenges.html', {
        'continent':continent,
        'continent_code': continent_code,
        'continent_slug': continent_slug,
        'challenge_count': challenge_count,
        'subnav_challenges_style': subnav_challenges_style,
        'nav_regions_style':nav_regions_style,
        'nav_page_name':nav_page_name,
        'meta_description':meta_description,
        'region_code':region_code,
        'featured_logs':featured_logs
    })


@cache_page(60 * 60)
def regions_list(request, country):
    country_code = country.code

    # Peaks by region
    sql = "select 1 " + \
        "from items_item_region b, cities_region c, cities_country e " + \
        "where e.code = %s " + \
        "and e.id = c.country_id " + \
        "and c.id = b.region_id " + \
        "limit 1"

    with connection.cursor() as cursor:
        cursor.execute(sql, [country_code])
        peaks_by_region = dictfetchall(cursor)

    have_peaks_in_regions = False
    if len(peaks_by_region) > 0:
        have_peaks_in_regions = True

    region_stats = get_region_stats(country_code)

    if not have_peaks_in_regions:
        first_ascent_count, first_ascent_count_pct = get_first_ascents_awarded(country_code, region_stats)
    else:
        first_ascent_count, first_ascent_count_pct = 0, 0

    # Top twentyfive peak photos
    sql = "select a.id, a.name, a.slug_new_text as slug, " + \
        "get_thumb(a.thumbnail, 480) as thumbnail_url, " + \
        "case when length(a.thumbnail) > 0 then 'hover-photos' else '' end as hover_class, " + \
        "case when length(a.thumbnail) > 0 then 'user-photo-info' else 'empty-photo-info' end as info_class " + \
        "from items_item a, items_item_country b, cities_country c " + \
        "where c.code = %s " + \
        "and c.id = b.country_id " + \
        "and b.item_id = a.id " + \
        "order by a.summitlog_count desc " + \
        "limit 25"
    top_peaks = Item.objects.raw(sql, [country_code])

    # Challenges stats
    sql = "select " + \
        "b.id, " + \
        "count(distinct d.id) as challenge_count " + \
        "from cities_country b " + \
        "left join cities_continent a on a.code = b.continent " + \
        "left join items_itemgroup d on d.show_in_country = true " + \
        "where b.code = %s " + \
        "and exists (select 1 from items_item_country x, items_itemgroupitem y where b.id = x.country_id and x.item_id = y.item_id and y.group_id = d.id " + \
            "and not exists (select 1 from items_item_country z where z.country_id != x.country_id and z.item_id = y.item_id))" + \
        "group by b.id "

    with connection.cursor() as cursor:
        cursor.execute(sql, [country_code])
        if cursor.rowcount > 0:
            challenge_stats = dictfetchall(cursor)[0]
            challenge_count = locale.format_string('%d', challenge_stats.get('challenge_count'), grouping=True)
        else:
            challenge_count = 0

    # Highest peak
    sql = "select a.name, a.elevation, a.slug_new_text as slug, floor(a.elevation*.3048) as elevation_in_meters " + \
        "from items_item a, items_item_country b, cities_country c " + \
        "where c.code = %s " + \
        "and c.id = b.country_id " + \
        "and b.item_id = a.id " + \
        "order by a.elevation desc " + \
        "limit 1 "

    with connection.cursor() as cursor:
        cursor.execute(sql, [country_code])

        if cursor.rowcount > 0:
            highest_peak = dictfetchall(cursor)[0]
        else:
            highest_peak = {'name': '', 'elevation': 0, 'slug_new_text': '', 'elevation_in_meters': 0}

    # Most prominent peak
    sql = "select a.name, a.slug_new_text as slug, a.prominence, floor(a.prominence*.3048) as prominence_in_meters " + \
        "from items_item a, items_item_country b, cities_country c " + \
        "where c.code = %s " + \
        "and c.id = b.country_id " + \
        "and b.item_id = a.id " + \
        "and a.prominence is not null " + \
        "order by a.prominence desc " + \
        "limit 1 "

    with connection.cursor() as cursor:
        cursor.execute(sql, [country_code])

        if cursor.rowcount > 0:
            most_prominent_peak = dictfetchall(cursor)[0]
        else:
            most_prominent_peak = {'name': '', 'slug_new_text': '', 'prominence': 0, 'prominence_in_meters': 0}

    # Top three regions
    sql = "select c.id, c.name, c.slug, count(a.id) as summit_count " + \
        "from items_summitlog a, items_item_region b, cities_region c, items_item_country d, cities_country e " + \
        "where e.code = %s " + \
        "and e.id = d.country_id " + \
        "and d.item_id = a.item_id " + \
        "and a.item_id = b.item_id " + \
        "and a.attempt = false and a.status = 1 " + \
        "and b.region_id = c.id " + \
        "and c.country_id = e.id " + \
        "group by c.id, c.name, c.slug " + \
        "order by count(a.id) desc " + \
        "limit 3"
    top_three_regions = Region.objects.raw(sql, [country_code])

    # Highlights
    highlights = CountryHighlight.objects.filter(country_id = country.id)

    nav_page_name = country.name
    meta_description = country.get_meta_description(region_stats.get('peak_count'))

    return render(request, 'directory/regions_list.html', {
        'country':country,
        'have_peaks_in_regions':have_peaks_in_regions,
        'top_peaks': top_peaks,
        'peak_count': locale.format_string('%d', region_stats.get('peak_count'), grouping=True),
        'peak_count_raw': region_stats.get('peak_count'),
        'summit_count': locale.format_string('%d', region_stats.get('summit_count'), grouping=True),
        'summit_count_raw': region_stats.get('summit_count'),
        'challenge_count': challenge_count,
        'highest_peak': highest_peak,
        'highest_peak_elevation': locale.format_string('%d', highest_peak.get('elevation'), grouping=True),
        'highest_peak_elevation_in_meters': locale.format_string('%d', highest_peak.get('elevation_in_meters'), grouping=True),
        'most_prominent_peak': most_prominent_peak,
        'most_prominent_peak_prominence': locale.format_string('%d', most_prominent_peak.get('prominence'), grouping=True),
        'most_prominent_peak_prominence_in_meters': locale.format_string('%d', most_prominent_peak.get('prominence_in_meters'), grouping=True),
        'top_three_regions': top_three_regions,
        'first_ascent_count': first_ascent_count,
        'first_ascent_count_pct': first_ascent_count_pct,
        'nav_regions_style': 'color: #00B1F2;',
        'subnav_info_style': 'color: #FF0000; font-weight: 500;',
        'nav_page_name':nav_page_name,
        'highlights':highlights,
        'meta_description':meta_description,
        'fixed_subnav_class': ""
    })


def get_region_stats(country_code):
    sql = """
        select b.id, d.id as peak_count, d.summitlog_count as summit_count
        from cities_continent a,
             cities_country b,
             items_item_country c,
             items_item d
        where a.code = b.continent
          and b.code = %s
          and b.id = c.country_id
          and c.item_id = d.id
    """
    with connection.cursor() as cursor:
        cursor.execute(sql, [country_code])
        if cursor.rowcount > 0:
            peak_count = cursor.rowcount
            summit_count = 0
            for row in cursor.fetchall():
                id = row[0]
                summit_count += row[2]
            region_stats = {'id': id, 'peak_count': peak_count, 'summit_count': summit_count}
        else:
            region_stats = {'id': 0, 'peak_count': 0, 'summit_count': 0}

    return region_stats


def get_first_ascents_awarded(country_code, region_stats):
    sql = "select a.id as first_ascent_id " + \
          "from items_item a, cities_country c, items_item_country d " + \
          "where c.code = %s " + \
          "and c.id = d.country_id " + \
          "and d.item_id = a.id " + \
          "and a.summitlog_count > 0 "

    with connection.cursor() as cursor:
        cursor.execute(sql, [country_code])

        if cursor.rowcount > 0:
            set_for_counting = set()
            for row in cursor.fetchall():
                set_for_counting.add(row[0])
            first_ascent_count = len(set_for_counting)

            if region_stats:
                if float(region_stats.get('peak_count')) > 0:
                    first_ascent_count_pct = int(100 * float(first_ascent_count) / float(
                        region_stats.get('peak_count')))
                else:
                    first_ascent_count_pct = 0
            else:
                first_ascent_count_pct = 0
        else:
            first_ascent_count = 0
            first_ascent_count_pct = 0
    return first_ascent_count, first_ascent_count_pct


@cache_page(60 * 60)
def regions_list_peaks(request, continent_code, country_code, order = '-elevation', page = 1):

    country = get_object_or_404(Country, code=country_code, continent=continent_code)

    #Peaks/summits stats
    sql = "select " + \
        "b.id, " + \
        "count(d.id) as peak_count, " + \
        "sum(d.summitlog_count) as summit_count " + \
        "from cities_continent a, cities_country b, items_item_country c, items_item d " + \
        "where a.code = b.continent and b.code = %s and b.id = c.country_id " + \
        "and c.item_id = d.id and d.active = true group by b.id "

    with connection.cursor() as cursor:

        cursor.execute(sql, [country_code])

        if cursor.rowcount > 0:
            region_stats = dictfetchall(cursor)[0]
        else:
            region_stats = {'id': 0, 'peak_count': 0, 'summit_count': 0}

    nav_regions_style = 'color: #00B1F2;'
    subnav_peaks_style = 'color: #FF0000; font-weight: 500;'

    nav_page_name = country.name

    meta_description = country.get_meta_description_peaks(region_stats.get('peak_count'))

    return render(request, 'directory/regions_list_peaks.html', {
        'country':country,
        'peak_count': locale.format_string('%d', region_stats.get('peak_count'), grouping=True),
        'peak_count_raw': region_stats.get('peak_count'),
        'summit_count': locale.format_string('%d', region_stats.get('summit_count'), grouping=True),
        'summit_count_raw': region_stats.get('summit_count'),
        'nav_regions_style': nav_regions_style,
        'subnav_peaks_style': subnav_peaks_style,
        'nav_page_name':nav_page_name,
        'meta_description':meta_description
    })


def regions_list_map(request, continent_code, country_code, order = '-elevation', page = 1):

    country = get_object_or_404(Country, code=country_code, continent=continent_code)

    region_stats = cache_manager.get_regions_list_map_region_stats(country_code)

    if not region_stats:

        #Peaks/summits stats
        sql = "select " + \
            "b.id, " + \
            "count(d.id) as peak_count, " + \
            "sum(d.summitlog_count) as summit_count " + \
            "from cities_continent a, cities_country b, items_item_country c, items_item d " + \
            "where a.code = b.continent and b.code = %s and b.id = c.country_id " + \
            "and c.item_id = d.id group by b.id "

        with connection.cursor() as cursor:

            cursor.execute(sql, [country_code])

            if cursor.rowcount > 0:
                region_stats = dictfetchall(cursor)[0]
            else:
                region_stats = {'id': 0, 'peak_count': 0, 'summit_count': 0}

        cache_manager.set_regions_list_map_region_stats(country_code, region_stats)

    nav_regions_style = 'color: #00B1F2;'
    subnav_map_style = 'color: #FF0000; font-weight: 500;'

    nav_page_name = country.name

    meta_description = country.get_meta_description_map(region_stats.get('peak_count'))

    return render(request, 'directory/regions_list_map.html', {
        'country':country,
        'peak_count': locale.format_string('%d', region_stats.get('peak_count'), grouping=True),
        'peak_count_raw': region_stats.get('peak_count'),
        'summit_count': locale.format_string('%d', region_stats.get('summit_count'), grouping=True),
        'summit_count_raw': region_stats.get('summit_count'),
        'nav_regions_style': nav_regions_style,
        'subnav_map_style': subnav_map_style,
        'nav_page_name':nav_page_name,
        'meta_description':meta_description
    })

@cache_page(60 * 60)
def regions_list_summits(request, continent_code, country_code, order = '-elevation', page = 1):

    country = get_object_or_404(Country, code=country_code, continent=continent_code)

    #Peaks/summits stats
    sql = "select " + \
        "b.id, " + \
        "count(d.id) as peak_count, " + \
        "sum(d.summitlog_count) as summit_count " + \
        "from cities_continent a, cities_country b, items_item_country c, items_item d " + \
        "where a.code = b.continent and b.code = %s and b.id = c.country_id " + \
        "and c.item_id = d.id group by b.id "

    with connection.cursor() as cursor:

        cursor.execute(sql, [country_code])

        if cursor.rowcount > 0:
            region_stats = dictfetchall(cursor)[0]
        else:
            region_stats = {'id': 0, 'peak_count': 0, 'summit_count': 0}

    nav_regions_style = 'color: #00B1F2;'
    subnav_summits_style = 'color: #FF0000; font-weight: 500;'

    nav_page_name = country.name

    meta_description = country.get_meta_description_summits(region_stats.get('summit_count'))

    return render(request, 'directory/regions_list_summits.html', {
        'country':country,
        'peak_count': locale.format_string('%d', region_stats.get('peak_count'), grouping=True),
        'peak_count_raw': region_stats.get('peak_count'),
        'summit_count': locale.format_string('%d', region_stats.get('summit_count'), grouping=True),
        'summit_count_raw': region_stats.get('summit_count'),
        'nav_regions_style': nav_regions_style,
        'subnav_summits_style': subnav_summits_style,
        'nav_page_name':nav_page_name,
        'meta_description':meta_description
    })


@cache_page(60 * 60)
def regions_list_challenges(request, continent_code, country_code, order = '-elevation', page = 1):

    country = get_object_or_404(Country, code=country_code, continent=continent_code)

    #Challenges stats
    sql = "select " + \
        "b.id, " + \
        "count(distinct d.id) as challenge_count " + \
        "from cities_country b " + \
        "left join cities_continent a on a.code = b.continent " + \
        "left join items_itemgroup d on d.show_in_country = true " + \
        "where b.code = %s " + \
        "and exists (select 1 from items_item_country x, items_itemgroupitem y where b.id = x.country_id and x.item_id = y.item_id and y.group_id = d.id " + \
            "and not exists (select 1 from items_item_country z where z.country_id != x.country_id and z.item_id = y.item_id))" + \
        "group by b.id "

    with connection.cursor() as cursor:

        cursor.execute(sql, [country_code])

        region_stats = dictfetchall(cursor)
        if region_stats:
            region_stats = region_stats[0]
            challenge_count = region_stats.get('challenge_count')
        else:
            challenge_count = 0

    #Featured summit logs
    sql = "select a.id, left(a.log, 600) as log_text, a.date as summitlog_date, b.username, e.name as peak_name, e.slug_new_text as peak_slug " + \
        "from items_summitlog a " + \
        "join auth_user b on b.id = a.user_id " + \
        "join items_item_country c on c.item_id = a.item_id " + \
        "join cities_country d on d.id = c.country_id " + \
        "join items_item e on e.id = a.item_id " + \
        "where d.code = %s and a.status = 1 and a.date_entered = true and length(a.log) >= 175 " + \
        "order by a.date desc limit 5 "

    with connection.cursor() as cursor:
        cursor.execute(sql, [country_code])
        featured_logs = dictfetchall(cursor)


    nav_regions_style = 'color: #00B1F2;'
    subnav_challenges_style = 'color: #FF0000; font-weight: 500;'

    nav_page_name = country.name

    meta_description = country.get_meta_description_challenges(challenge_count)

    return render(request, 'directory/regions_list_challenges.html', {
        'country':country,
        'challenge_count': challenge_count,
        'nav_regions_style': nav_regions_style,
        'subnav_challenges_style': subnav_challenges_style,
        'nav_page_name':nav_page_name,
        'meta_description':meta_description,
        'featured_logs':featured_logs
    })

@cache_page(60 * 60)
def cities_list(request, continent_code, country_code, region_slug, order = '-elevation', page = 1):

    region = get_object_or_404(Region, slug=region_slug, country__continent=continent_code, country__code=country_code)

    #Top thirty peak photos
    sql = "select a.id, a.name, a.slug_new_text as slug, " + \
        "get_thumb(a.thumbnail, 480) as thumbnail_url, " + \
        "case when length(a.thumbnail) > 0 then 'hover-photos' else '' end as hover_class, " + \
        "case when length(a.thumbnail) > 0 then 'user-photo-info' else 'empty-photo-info' end as info_class " + \
        "from items_item a, cities_country c, cities_region d, items_item_region e " + \
        "where d.slug = %s " + \
        "and d.id = e.region_id " + \
        "and d.country_id = c.id " + \
        "and c.code = %s " + \
        "and c.id = d.country_id " + \
        "and e.item_id = a.id " + \
        "order by a.summitlog_count desc " + \
        "limit 30"
    top_peaks = Item.objects.raw(sql, [region_slug, country_code])

    #Peaks/summits stats
    sql = "select e.id, " + \
        "count(d.id) as peak_count, " + \
        "sum(d.summitlog_count) as summit_count " + \
        "from cities_country b, items_item d, cities_region e, items_item_region f " + \
        "where e.slug = %s " + \
        "and e.id = f.region_id " + \
        "and f.item_id = d.id " + \
        "and e.country_id = b.id " + \
        "and b.code = %s and b.id = e.country_id " + \
        "group by e.id "

    with connection.cursor() as cursor:

        cursor.execute(sql, [region_slug, country_code])

        if cursor.rowcount > 0:
            region_stats = dictfetchall(cursor)[0]
        else:
            region_stats = {'id': 0, 'peak_count': 0, 'summit_count': 0}

    #Highest peak
    sql = "select a.name, a.elevation, a.slug_new_text as slug, floor(a.elevation*.3048) as elevation_in_meters " + \
        "from items_item a, cities_country c, cities_region d, items_item_region e " + \
        "where d.slug = %s " + \
        "and d.id = e.region_id " + \
        "and d.country_id = c.id " + \
        "and c.code = %s " + \
        "and c.id = d.country_id " + \
        "and e.item_id = a.id " + \
        "order by a.elevation desc " + \
        "limit 1 "

    with connection.cursor() as cursor:

        cursor.execute(sql, [region_slug, country_code])

        if cursor.rowcount > 0:
            highest_peak = dictfetchall(cursor)[0]
        else:
            highest_peak = {'name': '', 'elevation': 0, 'slug_new_text': '', 'elevation_in_meters': 0}

    #Most summited peak
    sql = "select a.name, a.slug_new_text as slug, a.summitlog_count " + \
        "from items_item a, cities_country c, cities_region d, items_item_region e " + \
        "where d.slug = %s " + \
        "and d.id = e.region_id " + \
        "and d.country_id = c.id " + \
        "and c.code = %s " + \
        "and c.id = d.country_id " + \
        "and e.item_id = a.id " + \
        "order by a.summitlog_count desc " + \
        "limit 1 "

    with connection.cursor() as cursor:

        cursor.execute(sql, [region_slug, country_code])

        if cursor.rowcount > 0:
            most_summited_peak = dictfetchall(cursor)[0]
        else:
            most_summited_peak = {'name': '', 'slug_new_text': '', 'summitlog_count': 0}

    #Most prominent peak
    sql = "select a.name, a.slug_new_text as slug, a.prominence, floor(a.prominence*.3048) as prominence_in_meters " + \
        "from items_item a, cities_country c, cities_region d, items_item_region e " + \
        "where d.slug = %s " + \
        "and d.id = e.region_id " + \
        "and d.country_id = c.id " + \
        "and c.code = %s " + \
        "and c.id = d.country_id " + \
        "and e.item_id = a.id " + \
        "and a.prominence is not null " + \
        "order by a.prominence desc " + \
        "limit 1 "

    with connection.cursor() as cursor:

        cursor.execute(sql, [region_slug, country_code])

        if cursor.rowcount > 0:
            most_prominent_peak = dictfetchall(cursor)[0]
        else:
            most_prominent_peak = {'name': '', 'slug_new_text': '', 'prominence': 0, 'prominence_in_meters': 0}

    #First ascents awarded
    sql = "select count(distinct a.id) as first_ascent_count " + \
        "from items_item a, cities_country c, cities_region d, items_item_region e " + \
        "where d.slug = %s " + \
        "and d.id = e.region_id " + \
        "and d.country_id = c.id " + \
        "and c.code = %s " + \
        "and c.id = d.country_id " + \
        "and e.item_id = a.id " + \
        "and a.summitlog_count > 0 "

    with connection.cursor() as cursor:

        cursor.execute(sql, [region_slug, country_code])

        if cursor.rowcount > 0:
            first_ascent_count = dictfetchall(cursor)[0]
            if region_stats:
                if float(region_stats.get('peak_count')) > 0:
                    first_ascent_count_pct = int(100 * float(first_ascent_count.get('first_ascent_count'))/float(region_stats.get('peak_count')))
                else:
                    first_ascent_count_pct = 0
            else:
                first_ascent_count_pct = 0
        else:
            first_ascent_count = 0
            first_ascent_count_pct = 0

    #Top climbing months
    sql = "select to_char(a.date, 'Month') as summitlog_month, count(a.id) as summitlog_count " + \
        "from items_summitlog a, cities_country c, cities_region d, items_item_region e " + \
        "where d.slug = %s " + \
        "and d.id = e.region_id " + \
        "and d.country_id = c.id " + \
        "and c.code = %s " + \
        "and c.id = d.country_id " + \
        "and e.item_id = a.item_id " + \
        "group by to_char(a.date, 'Month') " + \
        "order by count(a.id) desc "

    with connection.cursor() as cursor:

        cursor.execute(sql, [region_slug, country_code])

        top_climbing_months = dictfetchall(cursor)

    top_climbing_months_total = 0
    for m in top_climbing_months:
        top_climbing_months_total = top_climbing_months_total + m.get('summitlog_count')
    top_three_months = []
    keys = ['summitlog_month', 'summitlog_count', 'pct_total']
    for m in top_climbing_months[:3]:
        values = [m.get('summitlog_month'), m.get('summitlog_count'), int(100 * float(m.get('summitlog_count'))/float(top_climbing_months_total))]
        top_three_months.append(dict(zip(keys, values)))

    #Top three ranges
    sql = "select a.range, count(a.id) as peak_count " + \
        "from items_item a, cities_country c, cities_region d, items_item_region e " + \
        "where d.slug = %s " + \
        "and d.id = e.region_id " + \
        "and d.country_id = c.id " + \
        "and c.code = %s " + \
        "and c.id = d.country_id " + \
        "and e.item_id = a.id " + \
        "and length(a.range) > 0 " + \
        "group by a.range " + \
        "order by count(a.id) desc " + \
        "limit 3"
    with connection.cursor() as cursor:

        cursor.execute(sql, [region_slug, country_code])

        top_three_ranges = dictfetchall(cursor)

    nav_page_name = region.name

    region_code = settings.GEOCHART_REGIONS[continent_code]

    #Highlights
    highlights = RegionHighlight.objects.filter(region_id = region.id)

    #Featured summit logs
    sql = "select a.id, case when length(a.log) > 1200 then concat(left(a.log, 1200),'...') else a.log end as log_text, a.date as summitlog_date, b.username, e.name as peak_name, e.slug_new_text as peak_slug, " + \
        "case when min(g.image) is not null then get_thumb(min(g.image), 320) else coalesce(replace(replace(f.avatar, '/', '/resized/100/'), 'avatars/resized/100/', 'avatars/'),'img/default-user.png') end as thumbnail_url " + \
        "from items_summitlog a " + \
        "join auth_user b on b.id = a.user_id " + \
        "join items_item_region c on c.item_id = a.item_id " + \
        "join cities_region d on d.id = c.region_id " + \
        "join items_item e on e.id = a.item_id " + \
        "left join avatar_avatar f on f.user_id = a.user_id " + \
        "left join items_itemphoto g on g.summit_log_id = a.id " + \
        "where d.slug = %s and a.status = 1 and a.date_entered = true and length(a.log) >= 175 " + \
        "group by a.id, log_text, a.date, b.username, e.name, e.slug_new_text, f.avatar " + \
        "order by a.date desc limit 5 "

    with connection.cursor() as cursor:
        cursor.execute(sql, [region_slug])
        featured_logs = dictfetchall(cursor)


    nav_regions_style = 'color: #00B1F2;'
    subnav_info_style = 'color: #FF0000; font-weight: 500;'

    meta_description = region.get_meta_description(region_stats.get('peak_count'))

    return render(request, 'directory/cities_list.html', {
        'region':region,
        'top_peaks': top_peaks,
        'peak_count': locale.format_string('%d', region_stats.get('peak_count'), grouping=True),
        'peak_count_raw': region_stats.get('peak_count'),
        'summit_count': locale.format_string('%d', region_stats.get('summit_count'), grouping=True),
        'summit_count_raw': region_stats.get('summit_count'),
        'highest_peak': highest_peak,
        'highest_peak_elevation': locale.format_string('%d', highest_peak.get('elevation'), grouping=True),
        'highest_peak_elevation_in_meters': locale.format_string('%d', highest_peak.get('elevation_in_meters'), grouping=True),
        'most_summited_peak': most_summited_peak,
        'most_summited_peak_summits': locale.format_string('%d', most_summited_peak.get('summitlog_count'), grouping=True),
        'most_prominent_peak': most_prominent_peak,
        'most_prominent_peak_prominence': locale.format_string('%d', most_prominent_peak.get('prominence'), grouping=True),
        'most_prominent_peak_prominence_in_meters': locale.format_string('%d', most_prominent_peak.get('prominence_in_meters'), grouping=True),
        'top_three_months': top_three_months,
        'top_three_ranges': top_three_ranges,
        'first_ascent_count': first_ascent_count,
        'first_ascent_count_pct': first_ascent_count_pct,
        'nav_regions_style': nav_regions_style,
        'subnav_info_style': subnav_info_style,
        'nav_page_name':nav_page_name,
        'highlights':highlights,
        'featured_logs':featured_logs,
        'meta_description':meta_description
    })

@cache_page(60 * 60)
def cities_list_peaks(request, continent_code, country_code, region_slug, order = '-elevation', page = 1):

    country = get_object_or_404(Country, code=country_code, continent=continent_code)

    if region_slug != 'unknown-region':
        region = get_object_or_404(Region, slug=region_slug, country__continent=continent_code, country__code=country_code)

        region_stats = cache_manager.get_cities_list_peaks_region_stats(region_slug)

        if not region_stats:

            #Peaks/summits stats
            sql = "select e.id, " + \
                "count(d.id) as peak_count, " + \
                "sum(d.summitlog_count) as summit_count " + \
                "from cities_country b, items_item d, cities_region e, items_item_region f " + \
                "where e.slug = %s and d.active = true " + \
                "and e.id = f.region_id " + \
                "and f.item_id = d.id " + \
                "and e.country_id = b.id " + \
                "and b.code = %s and b.id = e.country_id " + \
                "group by e.id "

            with connection.cursor() as cursor:

                cursor.execute(sql, [region_slug, country_code])

                if cursor.rowcount > 0:
                    region_stats = dictfetchall(cursor)[0]
                else:
                    region_stats = {'id': 0, 'peak_count': 0, 'summit_count': 0}

            cache_manager.set_cities_list_peaks_region_stats(region_slug, region_stats)

        nav_page_name = region.name

        meta_description = region.get_meta_description_peaks(region_stats.get('peak_count'))

        return render(request, 'directory/cities_list_peaks.html', {
            'region':region,
            'country':country,
            'country_code':country_code,
            'peak_count': locale.format_string('%d', region_stats.get('peak_count'), grouping=True),
            'peak_count_raw': region_stats.get('peak_count'),
            'summit_count': locale.format_string('%d', region_stats.get('summit_count'), grouping=True),
            'summit_count_raw': region_stats.get('summit_count'),
            'nav_regions_style': 'color: #00B1F2;',
            'subnav_peaks_style': 'color: #FF0000; font-weight: 500;',
            'nav_page_name':nav_page_name,
            'meta_description':meta_description
        })

    else:
        region = []

        region_stats = cache_manager.get_cities_list_peaks_region_stats(region_slug)

        if not region_stats:

            #Peaks/summits stats
            sql = "select 0 as region_id, " + \
                "count(d.id) as peak_count, " + \
                "sum(d.summitlog_count) as summit_count " + \
                "from cities_country b, items_item d, items_item_country f " + \
                "where b.code = %s and b.id = f.country_id and f.item_id = d.id " + \
                "and not exists (select 1 from items_item_region e, cities_region f where e.item_id = d.id and f.id = e.region_id and f.country_id = b.id) " + \
                "group by region_id "

            with connection.cursor() as cursor:

                cursor.execute(sql, [country_code])

                if cursor.rowcount > 0:
                    region_stats = dictfetchall(cursor)[0]
                else:
                    region_stats = {'id': 0, 'peak_count': 0, 'summit_count': 0}

            cache_manager.set_cities_list_peaks_region_stats(region_slug, region_stats)

        region_code = settings.GEOCHART_REGIONS[continent_code]

        nav_regions_style = 'color: #00B1F2;'
        subnav_peaks_style = 'color: #FF0000; font-weight: 500;'

        nav_page_name = 'Unknown'

        meta_description = "List of %s mountains in Unknown region. Sort by elevation, prominence, name, or number of climbs." % (commify(region_stats.get('peak_count')))

        return render(request, 'directory/cities_list_unknown_peaks.html', {
            'region':region,
            'country':country,
            'country_code':country_code,
            'peak_count': locale.format_string('%d', region_stats.get('peak_count'), grouping=True),
            'peak_count_raw': region_stats.get('peak_count'),
            'summit_count': locale.format_string('%d', region_stats.get('summit_count'), grouping=True),
            'summit_count_raw': region_stats.get('summit_count'),
            'nav_regions_style': nav_regions_style,
            'subnav_peaks_style': subnav_peaks_style,
            'nav_page_name':nav_page_name,
            'meta_description':meta_description
        })

#@cache_page(60 * 60)
def cities_list_map(request, continent_code, country_code, region_slug, order = '-elevation', page = 1):

    country = get_object_or_404(Country, code=country_code, continent=continent_code)

    if region_slug != 'unknown-region':
        region = get_object_or_404(Region, slug=region_slug, country__continent=continent_code, country__code=country_code)

        region_stats = cache_manager.get_cities_list_map_region_stats(region_slug)

        if not region_stats:

            #Peaks/summits stats
            sql = "select e.id, " + \
                "count(d.id) as peak_count, " + \
                "sum(d.summitlog_count) as summit_count " + \
                "from cities_country b, items_item d, cities_region e, items_item_region f " + \
                "where e.slug = %s " + \
                "and e.id = f.region_id " + \
                "and f.item_id = d.id " + \
                "and e.country_id = b.id " + \
                "and b.code = %s and b.id = e.country_id " + \
                "group by e.id "

            with connection.cursor() as cursor:

                cursor.execute(sql, [region_slug, country_code])

                if cursor.rowcount > 0:
                    region_stats = dictfetchall(cursor)[0]
                else:
                    region_stats = {'id': 0, 'peak_count': 0, 'summit_count': 0}

            cache_manager.set_cities_list_map_region_stats(region_slug, region_stats)

        nav_regions_style = 'color: #00B1F2;'
        subnav_map_style = 'color: #FF0000; font-weight: 500;'

        nav_page_name = region.name

        meta_description = region.get_meta_description_map(region_stats.get('peak_count'))

        return render(request, 'directory/cities_list_map.html', {
            'region':region,
            'country':country,
            'peak_count': locale.format_string('%d', region_stats.get('peak_count'), grouping=True),
            'peak_count_raw': region_stats.get('peak_count'),
            'summit_count': locale.format_string('%d', region_stats.get('summit_count'), grouping=True),
            'summit_count_raw': region_stats.get('summit_count'),
            'nav_regions_style': nav_regions_style,
            'subnav_map_style': subnav_map_style,
            'nav_page_name':nav_page_name,
            'meta_description':meta_description
        })

    else:
        region = []

        region_stats = cache_manager.get_cities_list_map_region_stats(region_slug)

        if not region_stats:

            #Peaks/summits stats
            sql = "select 0 as region_id, " + \
                "count(d.id) as peak_count, " + \
                "sum(d.summitlog_count) as summit_count " + \
                "from cities_country b, items_item d, items_item_country f " + \
                "where b.code = %s and b.id = f.country_id and f.item_id = d.id " + \
                "and not exists (select 1 from items_item_region e, cities_region f where e.item_id = d.id and f.id = e.region_id and f.country_id = b.id) " + \
                "group by region_id "

            with connection.cursor() as cursor:

                cursor.execute(sql, [country_code])

                if cursor.rowcount > 0:
                    region_stats = dictfetchall(cursor)[0]
                else:
                    region_stats = {'id': 0, 'peak_count': 0, 'summit_count': 0}

            cache_manager.set_cities_list_map_region_stats(region_slug, region_stats)

        region_code = settings.GEOCHART_REGIONS[continent_code]

        nav_regions_style = 'color: #00B1F2;'
        subnav_map_style = 'color: #FF0000; font-weight: 500;'

        nav_page_name = 'Unknown'

        meta_description = "Map of %s mountains in Unknown region. Filter by elevation, prominence, summits, and difficulty." % (commify(region_stats.get('peak_count')))

        return render(request, 'directory/cities_list_unknown_map.html', {
            'region':region,
            'country':country,
            'country_code':country_code,
            'peak_count': locale.format_string('%d', region_stats.get('peak_count'), grouping=True),
            'peak_count_raw': region_stats.get('peak_count'),
            'summit_count': locale.format_string('%d', region_stats.get('summit_count'), grouping=True),
            'summit_count_raw': region_stats.get('summit_count'),
            'nav_regions_style': nav_regions_style,
            'subnav_map_style': subnav_map_style,
            'nav_page_name':nav_page_name,
            'meta_description':meta_description
        })


@cache_page(60 * 60)
def cities_list_summits(request, continent_code, country_code, region_slug, order = '-elevation', page = 1):
    region = get_object_or_404(Region, slug=region_slug, country__continent=continent_code, country__code=country_code)

    #Peaks/summits stats
    sql = "select e.id, " + \
        "count(d.id) as peak_count, " + \
        "sum(d.summitlog_count) as summit_count " + \
        "from cities_country b, items_item d, cities_region e, items_item_region f " + \
        "where e.slug = %s " + \
        "and e.id = f.region_id " + \
        "and f.item_id = d.id " + \
        "and e.country_id = b.id " + \
        "and b.code = %s and b.id = e.country_id " + \
        "group by e.id "

    with connection.cursor() as cursor:

        cursor.execute(sql, [region_slug, country_code])

        if cursor.rowcount > 0:
            region_stats = dictfetchall(cursor)[0]
        else:
            region_stats = {'id': 0, 'peak_count': 0, 'summit_count': 0}

    nav_regions_style = 'color: #00B1F2;'
    subnav_summits_style = 'color: #FF0000; font-weight: 500;'

    nav_page_name = region.name

    meta_description = region.get_meta_description_summits(region_stats.get('summit_count'))

    return render(request, 'directory/cities_list_summits.html', {
        'region':region,
        'peak_count': locale.format_string('%d', region_stats.get('peak_count'), grouping=True),
        'peak_count_raw': region_stats.get('peak_count'),
        'summit_count': locale.format_string('%d', region_stats.get('summit_count'), grouping=True),
        'summit_count_raw': region_stats.get('summit_count'),
        'nav_regions_style': nav_regions_style,
        'subnav_summits_style': subnav_summits_style,
        'nav_page_name':nav_page_name,
        'meta_description':meta_description
    })

@cache_page(60 * 60)
def cities_list_challenges(request, continent_code, country_code, region_slug, order = '-elevation', page = 1):
    region = get_object_or_404(Region, slug=region_slug, country__continent=continent_code, country__code=country_code)

    #Peaks/summits stats
    sql = "select e.id, " + \
        "count(distinct g.id) as challenge_count " + \
        "from cities_region e " + \
        "left join cities_country b on b.id = e.country_id " + \
        "left join items_itemgroup g on g.show_in_region = true " + \
        "where e.slug = %s and b.code = %s " + \
        "and exists (select 1 from items_item_region x, items_itemgroupitem y where e.id = x.region_id and x.item_id = y.item_id and y.group_id = g.id " + \
            "and not exists (select 1 from items_item_region xx where xx.region_id != x.region_id and xx.item_id = y.item_id)) " + \
        "group by e.id "

    with connection.cursor() as cursor:

        cursor.execute(sql, [region_slug, country_code])

        region_stats = dictfetchall(cursor)
        if region_stats:
            region_stats = region_stats[0]
            challenge_count = region_stats.get('challenge_count')
        else:
            challenge_count = 0

    #Featured summit logs
    sql = "select a.id, left(a.log, 600) as log_text, a.date as summitlog_date, b.username, e.name as peak_name, e.slug_new_text as peak_slug " + \
        "from items_summitlog a " + \
        "join auth_user b on b.id = a.user_id " + \
        "join items_item_region c on c.item_id = a.item_id " + \
        "join cities_region d on d.id = c.region_id " + \
        "join items_item e on e.id = a.item_id " + \
        "where d.slug = %s and a.status = 1 and a.date_entered = true and length(a.log) >= 175 " + \
        "order by a.date desc limit 5 "

    with connection.cursor() as cursor:
        cursor.execute(sql, [region_slug])
        featured_logs = dictfetchall(cursor)

    nav_regions_style = 'color: #00B1F2;'
    subnav_challenges_style = 'color: #FF0000; font-weight: 500;'

    nav_page_name = region.name

    meta_description = region.get_meta_description_challenges(challenge_count)

    return render(request, 'directory/cities_list_challenges.html', {
        'region':region,
        'challenge_count': challenge_count,
        'nav_regions_style': nav_regions_style,
        'subnav_challenges_style': subnav_challenges_style,
        'nav_page_name':nav_page_name,
        'meta_description':meta_description,
        'featured_logs':featured_logs,
        'subnav_info_style': "",
        'subnav_summits_style': "",
        'subnav_peaks_style': "",
        'subnav_map_style': ""
    })

def antarctica(request):
    return countries_list(request, "AN")

def _get_items_without_region(country, regions):
    items_without_region = country.get_total_items()
    for r in regions:
        items_without_region -= r.count

    if items_without_region < 0:
        items_without_region = 0

    return items_without_region

@login_required
def edit_country_highlights(request, country_id):
    country = get_object_or_404(Country, id=country_id)
    if request.method == 'POST' and request.is_ajax():
        formdata = {}
        for p in request.POST:
            if (request.POST[p] != ''):
                keys = p.split('-')
                key = keys[2]
                val = request.POST[p]
                formdata[key] = val
        #create log entry
        log_group = CountryHighlightLogGroup(user=request.user, country=country, log_date=datetime.datetime.now())
        log_group.save()
        #delete existing highlights
        sql = "delete from cities_countryhighlight where country_id = %s"
        with connection.cursor() as cursor:
            cursor.execute(sql, [country_id])
        #add new highlights
        new_highlights = []
        for k,v in sorted(formdata.items()):
            highlight = CountryHighlight(user=request.user, country=country, highlight=v.replace("\n", " ").replace("\r", " "))
            highlight.save()
            new_highlights.append(v.replace("\n", " ").replace("\r", " "))
        #copy highlights to log
        sql = "insert into cities_countryhighlightlog (log_group_id, highlight) select %s, highlight from cities_countryhighlight where country_id = %s order by id"
        with connection.cursor() as cursor:
            cursor.execute(sql, [log_group.id, country_id])
        data = {}
        data['error'] = False
        data['error_message'] = ''
        data['success'] = True
        data['success_message'] =  'Highlights updated'
        data['highlights'] = new_highlights
        return HttpResponse(json.dumps(data), content_type='application/json')
    else:
        return render(request, 'region/edit_country_highlights.html', {'highlights':highlights, 'country':country})

@login_required
def edit_continent_highlights(request, continent_id):
    continent = get_object_or_404(Continent, id=continent_id)
    if request.method == 'POST' and request.is_ajax():
        formdata = {}
        for p in request.POST:
            if (request.POST[p] != ''):
                keys = p.split('-')
                key = keys[2]
                val = request.POST[p]
                formdata[key] = val
        #create new log entry
        log_group = ContinentHighlightLogGroup(user=request.user, continent=continent, log_date=datetime.datetime.now())
        log_group.save()
        #delete existing highlights
        sql = "delete from cities_continenthighlight where continent_id = %s"
        with connection.cursor() as cursor:
            cursor.execute(sql, [continent_id])
        #add new highlights
        new_highlights = []
        for k,v in sorted(formdata.items()):
            highlight = ContinentHighlight(user=request.user, continent=continent, highlight=v.replace("\n", " ").replace("\r", " "))
            highlight.save()
            new_highlights.append(v.replace("\n", " ").replace("\r", " "))
        #copy highlights to log
        sql = "insert into cities_continenthighlightlog (log_group_id, highlight) select %s, highlight from cities_continenthighlight where continent_id = %s order by id"
        with connection.cursor() as cursor:
            cursor.execute(sql, [log_group.id, continent_id])
        data = {}
        data['error'] = False
        data['error_message'] = ''
        data['success'] = True
        data['success_message'] =  'Highlights updated'
        data['highlights'] = new_highlights
        return HttpResponse(json.dumps(data), content_type='application/json')
    else:
        return render(request, 'region/edit_continent_highlights.html', {'highlights':highlights, 'continent':continent})

@login_required
def edit_region_highlights(request, region_id):
    region = get_object_or_404(Region, id=region_id)
    if request.method == 'POST' and request.is_ajax():
        formdata = {}
        for p in request.POST:
            if (request.POST[p] != ''):
                keys = p.split('-')
                key = keys[2]
                val = request.POST[p]
                formdata[key] = val
        #create log entry
        log_group = RegionHighlightLogGroup(user=request.user, region=region, log_date=datetime.datetime.now())
        log_group.save()
        #delete existing highlights
        sql = "delete from cities_regionhighlight where region_id = %s"
        with connection.cursor() as cursor:
            cursor.execute(sql, [region_id])
        #add new highlights
        new_highlights = []
        for k,v in sorted(formdata.items()):
            highlight = RegionHighlight(user=request.user, region=region, highlight=v.replace("\n", " ").replace("\r", " "))
            highlight.save()
            new_highlights.append(v.replace("\n", " ").replace("\r", " "))
        #copy highlights to log
        sql = "insert into cities_regionhighlightlog (log_group_id, highlight) select %s, highlight from cities_regionhighlight where region_id = %s order by id"
        with connection.cursor() as cursor:
            cursor.execute(sql, [log_group.id, region_id])
        data = {}
        data['error'] = False
        data['error_message'] = ''
        data['success'] = True
        data['success_message'] =  'Highlights updated'
        data['highlights'] = new_highlights
        return HttpResponse(json.dumps(data), content_type='application/json')
    else:
        return render(request, 'region/edit_region_highlights.html', {'highlights':highlights, 'region':region})