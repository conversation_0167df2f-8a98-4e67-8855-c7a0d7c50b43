from django import forms
from django.contrib.gis import admin
from peakery.tempitems.models import *
from peakery.background.tasks import task_approve_temp_item, task_reject_temp_item, task_rename_temp_item

class GoogleAdmin(admin.OSMGeoAdmin):
    map_srid = 4326
    default_lon = -8228293
    default_lat = 508764
    default_zoom = 5


def approve(modeladmin, request, queryset):
    for temp_item in queryset:
        print('approving item : %s' % temp_item.id)
        queryset.update(status=STATUS_PROCESSING)
        task_approve_temp_item(temp_item.id)

approve.short_description = "Mark as approved"

def reject(modeladmin, request, queryset):
    for temp_item in queryset:
        print('rejecting item : %s' % temp_item.id)
        result = task_reject_temp_item(temp_item.id)
        queryset.update(status = STATUS_REJECTED)

reject.short_description = "Mark as rejected"

def rename(modeladmin, request, queryset):
    for temp_item in queryset:
        print('renaming item : %s' % temp_item.id)
        result = task_rename_temp_item(temp_item.id)

rename.short_description = "Change to default name"

class TempItemRelatedInline(admin.TabularInline):
    model = TempItemRelated
    extra = 0
    raw_id_fields = ('item',)
    template = "tabular.html"


class TempItemRegionAdmin(admin.TabularInline):
    model = TempItem.region.through
    raw_id_fields = ('region',)
    extra = 0
    template = "tabular.html"

class TempItemCountryAdmin(admin.TabularInline):
    model = TempItem.country.through
    raw_id_fields = ('country',)
    extra = 0
    template = "tabular.html"

class TempItemRejectTextInline(admin.TabularInline):
    extra = 0
    model = TempItemRejectText
    template = "tabular.html"

class TempItemAdminForm(forms.ModelForm):
    elevation_in_meters = forms.FloatField(required=False)
    location_lat_lng = forms.CharField(required=False)

    class Meta:
        model = TempItem
        fields = ['name', 'user', 'status', 'long', 'lat', 'elevation', 'elevation_in_meters', 'prominence', 'range', 'location','location_lat_lng',]

    def __init__(self, *args, **kwargs):
        super(TempItemAdminForm, self).__init__(*args, **kwargs)

        # Set the form fields based on the model object
        if 'instance' in kwargs:
            instance = kwargs['instance']
            self.initial['elevation_in_meters'] = instance.elevation * 0.3048

class TempItemAdmin(admin.ModelAdmin):
    list_display = ('name','status','user','created','lat','long','get_countries','get_regions','related_item_1','related_item_2','elevation','elevation_in_meters','prominence','range')
    fields = ('name', 'user', 'status', 'long', 'lat', 'elevation', 'elevation_in_meters', 'prominence', 'range', 'location_lat_lng',)
    #readonly_fields = ('location_lat_lng',)
    raw_id_fields = ('user','region','country')
    list_filter = ('status',)
    exclude = ['region','country']
    ordering = ['status']
    inlines = [TempItemRejectTextInline,TempItemRelatedInline,TempItemRegionAdmin,TempItemCountryAdmin]

    actions = [approve, reject, rename]
    form = TempItemAdminForm

    def changelist_view(self, request, extra_context=None):
        if 'status__exact' not in request.GET:
            q = request.GET.copy()
            q['status__exact'] = '1'
            request.GET = q
            request.META['QUERY_STRING'] = request.GET.urlencode()
        return super(TempItemAdmin,self).changelist_view(request, extra_context=extra_context)

    def get_countries(self, obj):
        return "\n".join([p.name for p in obj.country.all()])

    def get_regions(self, obj):
        return "\n".join([p.name for p in obj.region.all()])

    def elevation_in_meters(self, obj):
        if not obj.elevation:
            return None
        return float(obj.elevation) * .3048

    def location_lat_lng(self):
        lat = self.lat
        lng = self.long
        if lat and lng:
            return '%s, %s' % (lat, lng)

    def save_model(self, request, obj, form, change):
        super(TempItemAdmin, self).save_model(request, obj, form, change)
        if round(form.initial['elevation_in_meters'],5) != round(form.cleaned_data['elevation_in_meters'],5):
            #update the elevation based on new elevation in meters
            new_elevation_in_meters = form.cleaned_data['elevation_in_meters']
            new_elevation = float(new_elevation_in_meters) / .3048
            obj.elevation = new_elevation
            if obj.default_name:
                if obj.is_usa():
                    new_peak_name = 'Peak %s ft' % (int(round(float(new_elevation), 0)))
                    obj.name = new_peak_name
                else:
                    new_peak_name = 'Peak %s m' % (int(round(new_elevation_in_meters, 0)))
                    obj.name = new_peak_name
            obj.save()
        elif round(form.initial['elevation'],5) != round(form.cleaned_data['elevation'],5):
            if obj.default_name:
                new_elevation = form.cleaned_data['elevation']
                new_elevation_in_meters = float(new_elevation) * .3048
                if obj.is_usa():
                    new_peak_name = 'Peak %s ft' % (int(round(float(new_elevation), 0)))
                    obj.name = new_peak_name
                else:
                    new_peak_name = 'Peak %s m' % (int(round(new_elevation_in_meters, 0)))
                    obj.name = new_peak_name
                obj.save()
    
admin.site.register(TempItem,TempItemAdmin)
