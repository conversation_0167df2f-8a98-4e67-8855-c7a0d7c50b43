# Create your views here.
from django.http import HttpResponseRedirect
from django.urls import reverse
from peakery.tempitems.models import TempItem
from peakery.items.models import Item
from django.contrib.admin.views.decorators import staff_member_required
from peakery.background.tasks import task_approve_temp_item

@staff_member_required
def approve_temp_item(request,id, return_http_response=True):
    redirect = reverse('admin:tempitems_tempitem_changelist')
    result = task_approve_temp_item(id)
    #if result:
        #tempitem = TempItem.objects.get(pk=id)
        #tempitem.status = 3
        #tempitem.save()

    if return_http_response:
        return HttpResponseRedirect(redirect)
    else:
        return True


from peakery.tempitems.models import STATUS_REJECTED

@staff_member_required
def reject_temp_item(request, id, return_http_response=True):
    try:
        tempitem = TempItem.objects.get(pk=id)
        if tempitem.reject_text.all().count() > 0:
            tempitem.send_rejected_email()
        tempitem.status = 2
        tempitem.save()
    except:
        pass

    if return_http_response:
        redirect = reverse('admin:tempitems_tempitem_changelist')
        return HttpResponseRedirect(redirect)
    else:
        return True

@staff_member_required
def view_tempitem_related(request,related_id = None):
    item = Item.objects.get(pk=related_id)
    redirect = item.get_absolute_url()
    return HttpResponseRedirect(redirect)
