# Generated by Django 3.2 on 2024-01-30 11:04

import datetime
from django.conf import settings
import django.contrib.gis.db.models.fields
from django.db import migrations, models
import django.db.models.deletion
import peakery.django_extensions.db.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('items', '0001_initial'),
        ('cities', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='TempItem',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('status', models.SmallIntegerField(choices=[(3, 'Processing'), (1, 'Pending'), (2, 'Rejected')], default=1)),
                ('long', models.FloatField(blank=True, null=True, verbose_name='Longitude')),
                ('lat', models.FloatField(blank=True, null=True, verbose_name='Latitude')),
                ('elevation', models.FloatField(blank=True, null=True, verbose_name='Elevation (ft)')),
                ('prominence', models.FloatField(blank=True, null=True, verbose_name='Prominence (ft)')),
                ('range', models.CharField(blank=True, max_length=500, null=True)),
                ('location', django.contrib.gis.db.models.fields.PointField(blank=True, null=True, srid=4326)),
                ('created', peakery.django_extensions.db.fields.CreationDateTimeField(blank=True, default=datetime.datetime.now, editable=False)),
                ('modified', peakery.django_extensions.db.fields.ModificationDateTimeField(blank=True, default=datetime.datetime.now, editable=False)),
                ('default_name', models.BooleanField(default=False)),
                ('country', models.ManyToManyField(blank=True, help_text='Only if dont know the region', null=True, related_name='temp_country_items', to='cities.Country')),
                ('region', models.ManyToManyField(blank=True, help_text='If dont know the region just select the country below', null=True, related_name='temp_region_items', to='cities.Region')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='tempitems', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='TempItemRelated',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='items.item')),
                ('parent', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='childs_items', to='tempitems.tempitem')),
            ],
            options={
                'ordering': ('id',),
            },
        ),
        migrations.CreateModel(
            name='TempItemRejectText',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.TextField()),
                ('tempitem', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='reject_text', to='tempitems.tempitem')),
            ],
        ),
    ]
