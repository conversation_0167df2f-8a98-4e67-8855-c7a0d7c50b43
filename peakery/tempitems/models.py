from django.contrib.gis.db import models
from django.db import connection
from django.contrib.gis.measure import D
from django.contrib.auth.models import User
from peakery.django_extensions.db.fields import <PERSON>SlugField, CreationDateTimeField, ModificationDateTimeField
from django.template.loader import render_to_string
#from easy_thumbnails.fields import Thumb<PERSON>er<PERSON><PERSON><PERSON>ield
from peakery.items.managers import *
from peakery.items.models import Item
from django.core.mail import EmailMessage
from peakery.cities.models import Region, Country


from peakery.items.management import *
# Create your models here.

STATUS_PENDING = 1
STATUS_REJECTED = 2
STATUS_PROCESSING = 3


STATUS_IN_ITEM_CHOICES =   (
    (STATUS_PROCESSING, 'Processing'),
    (STATUS_PENDING,'Pending'),
    (STATUS_REJECTED,'Rejected'),
)

def dictfetchall(cursor):
    "Return all rows from a cursor as a dict"
    columns = [col[0] for col in cursor.description]
    return [
        dict(zip(columns, row))
        for row in cursor.fetchall()
    ]

class TempItem(models.Model):
    name = models.CharField(max_length=255)
    user = models.ForeignKey(User, related_name='tempitems', on_delete=models.SET_DEFAULT, default=1)
    status = models.SmallIntegerField(choices = STATUS_IN_ITEM_CHOICES , default = 1)
    long = models.FloatField(blank=True, null=True, verbose_name='Longitude')
    lat = models.FloatField(blank=True, null=True, verbose_name='Latitude')
    elevation = models.FloatField(blank=True, null=True, verbose_name='Elevation (ft)')
    prominence = models.FloatField(blank=True, null=True, verbose_name='Prominence (ft)')
    #db_description = models.TextField(null = True, blank = True,verbose_name="description",db_column="description")
    #wikipedia_source = models.URLField(null = True, blank = True)
    #slug = AutoSlugField(populate_from="name", max_length=255, editable=True)
    range = models.CharField(max_length=500, null = True, blank = True)
    location = models.PointField(blank = True, null=True)

    region = models.ManyToManyField(Region, blank=True,help_text='If dont know the region just select the country below', related_name='temp_region_items')
    country = models.ManyToManyField(Country, blank = True, help_text='Only if dont know the region', related_name='temp_country_items')
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()
    default_name = models.BooleanField(default=False)
    objects = ItemManager()

    def is_usa_but_not_alaska(self):
        sql = "select count(a.id) as country_count from tempitems_tempitem_country a, cities_country b where a.tempitem_id = %s and a.country_id = b.id and b.code = 'US' "
        country_count = 0
        with connection.cursor() as cursor:
            cursor.execute(sql, [self.id])
            countries = dictfetchall(cursor)
        if countries:
            country_count = countries[0]['country_count']

        sql = "select count(a.id) as region_count from tempitems_tempitem_region a, cities_region b where a.tempitem_id = %s and a.region_id = b.id and b.code = 'US-AK' "
        region_count = 0
        with connection.cursor() as cursor:
            cursor.execute(sql, [self.id])
            regions = dictfetchall(cursor)
        if regions:
            region_count = regions[0]['region_count']

        if country_count > 0 and region_count == 0:
            return True
        else:
            return False

    def is_usa(self):
        sql = "select count(a.id) as country_count from tempitems_tempitem_country a, cities_country b where a.tempitem_id = %s and a.country_id = b.id and b.code = 'US' "
        country_count = 0
        with connection.cursor() as cursor:
            cursor.execute(sql, [self.id])
            countries = dictfetchall(cursor)
        if countries:
            country_count = countries[0]['country_count']

        if country_count > 0:
            return True
        else:
            return False

    def populate_related(self):
        nearest_peaks = None
        sql = "select c.id, c.name, " + \
            "coalesce(ACOS( SIN(c.lat*PI()/180)*SIN(ST_Y(a.location)*PI()/180) + COS(c.lat*PI()/180)*COS(ST_Y(a.location)*PI()/180)*COS(ST_X(a.location)*PI()/180-c.long*PI()/180) ) * 6371000,0) as distance_in_meters " + \
            "from tempitems_tempitem a, items_item c " + \
            "where a.id = %s and a.location is not null " + \
            "order by distance_in_meters asc limit 3 "
        nearest_peaks = Item.objects.raw(sql, [self.id])
        for item in nearest_peaks:
            tempitem,created = TempItemRelated.objects.get_or_create(parent= self,item=item)

    def save(self,*args,**kwargs):
        self.location = Point(float(self.long), float(self.lat))
        super(TempItem, self).save(*args, **kwargs)
        self.populate_related()

    def delete(self,*args,**kwargs):
        relateds = self.childs_items.all()
        for related in relateds:
            related.delete()        
        super(TempItem,self).delete(*args,**kwargs)

    def get_url_map_google(self):
        base = "http://maps.google.com/maps?q=%(lat)s,%(long)s&t=p" % self.__dict__
        return base

    def related_item_1(self):
        count = self.childs_items.all().count()
        if count > 0:
            return self.childs_items.all()[0].item
        return ''

    def related_item_2(self):
        count = self.childs_items.all().count()
        if count > 1:
            return self.childs_items.all()[1].item
        return ''

    def related_item_3(self):
        count = self.childs_items.all().count()
        if count > 2:
            return self.childs_items.all()[2].item
        return ''

    def elevation_in_meters(self):
        return float(self.elevation) * .3048
    elevation_in_meters.short_description = 'Elevation (m)'

    def location_lat_lng(self):
        lat = self.lat
        lng = self.long
        if lat and lng:
            return '%s, %s' % (lat, lng)
        return ''
    location_lat_lng.short_description = 'Location'

    def send_rejected_email(self):
        subject = 'your %s addition was rejected' % self.name
        body = render_to_string('emails/rejected_item.html', {'tempitem':self,'user':self.user})
        #email = self.user.email
        #email_message(subject, body, to=[email], from_email=settings.DEFAULT_FROM_EMAIL_FEEDBACK)
        recipients = []
        recipients.append(self.user.email)
        msg = EmailMessage(subject,body,settings.DEFAULT_FROM_EMAIL_NOTIFICATIONS,recipients)
        msg.content_subtype = "html"
        msg.send()

class TempItemRelated(models.Model):
    parent = models.ForeignKey(TempItem,related_name="childs_items", on_delete=models.DO_NOTHING)
    item = models.ForeignKey(Item, on_delete=models.DO_NOTHING)

    def __unicode__(self):
        return u'Related Associated with %s' %(self.parent.name)

    class Meta:
        ordering = ('id',)

class TempItemRejectText(models.Model):
    text = models.TextField()
    tempitem = models.ForeignKey(TempItem, related_name='reject_text', on_delete=models.DO_NOTHING)

    def __unicode__(self):
        return u'%s Reject Text' % self.tempitem.name
