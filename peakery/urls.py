from django.urls import include, path, re_path
from django.views.decorators.csrf import csrf_exempt
from django.views.generic import TemplateView
from django.contrib.auth.models import User
from peakery.items.models import Item,ItemGroup
from peakery.cities.models import Country,Region
from peakery.settings import DEV
from django.contrib import admin

peaks_dict = {
    'queryset': Item.objects,
    'date_field': 'modified',
    'location': lambda x:x.get_absolute_path(),
}

countries_dict = {
    'queryset': Country.objects,
    'location': lambda x:x.get_absolute_url(),
}

regions_dict = {
    'queryset': Region.objects,
    'location': lambda x:x.get_absolute_url(),
}

itemgroup_dict = {
    'queryset': ItemGroup.objects,
    'date_field': 'modified',
    'location': lambda x:x.get_absolute_path(),
}

peakbaggers_dict = {
    'queryset':  User.objects,
}

from peakery.main.views import sitemap_index, sitemap_section, new_index as new_index, homepage_bg, login
from peakery.main.views import about, top_contributors, contest, summits, latest, last_five_summits, new_last_five_summits
from peakery.accounts.views import edit_settings, users_redirect, user_profile, edit_user_profile, upload_user_avatar, check_username_exists
from peakery.accounts.views import user_summits, user_badges, user_summit_badges, mobile_user_badges
from peakery.accounts.views import user_map, user_challenges, user_photos, new_user_news
from peakery.items.views import challenges_view, peak_list_view, peak_list_view_peaks, peak_list_view_map, peak_list_view_members
from peakery.items.views import peak_list_view_summits, challenge_edit_highlights, main_peaks_map, members_list
from peakery.items.views import peak_view_summit, peak_view_summit_badges, peak_view_summits, summit_edit, summit_download_gpx
from peakery.items.views import peak_view_route, peak_view_map, view as peak_view, summit_badge_peak, summit_badge_first_ascent
from peakery.directory.views import edit_country_highlights, edit_continent_highlights, edit_region_highlights, antarctica, url_resolver


admin.autodiscover()
#handler500 = 'main.views.server_error'

# Choose the template for robots.txt based on the DEV setting
if DEV:
    robots_template_name = "robots_dev.txt"
else:
    robots_template_name = "robots_prod.txt"

urlpatterns = [
    path('notifications/', include('peakery.notification.urls')),
    re_path(r'^robots.txt$', TemplateView.as_view(template_name=robots_template_name)),
    path('tempitems/',include('peakery.tempitems.urls')),
    re_path(r'^sitemap\.xml$', sitemap_index, name='sitemap_index'),
    re_path(r'^sitemap-(?P<section>.+)-(?P<page>.+)\.xml$', sitemap_section, name='sitemap_section'),
    path('', new_index, name='main_index'),
    #url(r'^new_index', new_index, name='new_index'),
    path('homepage_bg/', homepage_bg, name="homepage_bg"),

    path('login/', login, name='login'),

    path('about/', about, name='about'),
    path('top-contributors/', top_contributors, name='top_contributors'),
    path('contest/', contest, name='contest'),
    path('summits/', summits, name='summits'),
    path('latest/', latest, name='latest'),

    path('settings/', edit_settings, name='edit_settings'),

    #includes
    path('includes/last_five_summits/', last_five_summits, name='includes_last_five_summits'),
    path('includes/new_last_five_summits/', new_last_five_summits, name='includes_new_last_five_summits'),

    path('accounts/', include('peakery.accounts.urls')),
    re_path(r'^users/(?P<username>[-@.\w]+)/$', users_redirect, name='users_redirect'),
    re_path(r'^members/(?P<username>[-@.\w]+)/$', user_profile, name='user_profile'),
    re_path(r'^members/(?P<username>[-@.\w]+)/edit/$', edit_user_profile, name='edit_user_profile'),
    re_path(r'^members/(?P<username>[-@.\w]+)/exists/$', check_username_exists, name='check_username_exists'),
    re_path(r'^members/(?P<username>[-@.\w]+)/uploadavatar/$', upload_user_avatar, name='upload_user_avatar'),
    re_path(r'^members/(?P<username>[-@.\w]+)/recent-photos/$', user_photos, name='user_photos'),
    re_path(r'^members/(?P<username>[-@.\w]+)/summits/$', user_summits, name='user_summits'),
    re_path(r'^members/(?P<username>[-@.\w]+)/badges/$', user_badges, name='user_badges'),
    re_path(r'^members/(?P<username>[-@.\w]+)/summitbadges/$', user_summit_badges, name='user_summit_badges'),
    re_path(r'^members/(?P<username>[-@.\w]+)/mobilebadges/(?P<summitlog_id>\d+)/$', mobile_user_badges, name='mobile_user_badges'),
    re_path(r'^members/(?P<username>[-@.\w]+)/map/$', user_map, name='user_map'),
    re_path(r'^members/(?P<username>[-@.\w]+)/challenges/$', user_challenges, name='user_challenges'),
    re_path(r'^members/(?P<username>[-@.\w]+)/photos/$', user_photos, name='user_photos'),

    path('challenges/', challenges_view, name='challenges_view'),
    re_path(r'^challenges/(?P<group_slug>[-@.\w]+)/$', peak_list_view, name='peak_list_view'),
    re_path(r'^challenges/(?P<group_slug>[-\w]+)/peaks/$', peak_list_view_peaks, name='peak_list_view_peaks'),
    re_path(r'^challenges/(?P<group_slug>[-\w]+)/map/$', peak_list_view_map, name='peak_list_view_map'),
    re_path(r'^challenges/(?P<group_slug>[-\w]+)/members/$', peak_list_view_members, name='peak_list_view_members'),
    re_path(r'^challenges/(?P<group_slug>[-\w]+)/summits/$', peak_list_view_summits, name='peak_list_view_summits'),
    path('challenges/edit_highlights/<int:group_id>/', csrf_exempt(challenge_edit_highlights), name='challenge_edit_highlights'),

    path('api/', include('peakery.api.urls')),
    path('tools/', include('peakery.tools.urls')),
    path('peaks/', include('peakery.items.urls')),
    path('world-mountains/', include('peakery.directory.urls')),
    path('cities/', include('peakery.cities.urls')),
    path('avatar/', include('avatar.urls')),
    path('admin/doc/', include('django.contrib.admindocs.urls')),
    re_path(r'^admin/', admin.site.urls),
    path('favorites/', include('peakery.favorites.urls')),
    path('map/', main_peaks_map, name='main_peaks_map'),

    path('members/', members_list, name='members'),
    re_path(r'^members/(?P<days>[-\w]+)/$', members_list, name='members'),
    re_path(r'^members/(?P<days>[-\w]+)/(?P<country>[-@.\w]+)/$', members_list, name='members'),
    re_path(r'^members/(?P<days>[-\w]+)/(?P<country>[-@.\w]+)/(?P<region>[-@.\w]+)/$', members_list, name='members'),

    path('news/', new_user_news, name='new_user_news'),

    path('ses/reports/', include('django_ses.urls')),

    re_path(r'^(?P<item_slug>[-\w]+)/summits/(?P<summitlog_id>\d+)/$', peak_view_summit, name='peak_view_summit'),
    re_path(r'^(?P<item_slug>[-\w]+)/summitlog/(?P<summitlog_id>\d+)/$', peak_view_summit_badges, name='peak_view_summit_badges'),
    re_path(r'^(?P<item_slug>[-\w]+)/summits/$', peak_view_summits, name='peak_view_summits'),
    re_path(r'^(?P<item_slug>[-\w]+)/summits/(?P<summitlog_id>\d+)/edit/$', summit_edit, name='summit_edit'),
    re_path(r'^(?P<item_slug>[-\w]+)/summits/(?P<summitlog_id>\d+)/download/$', summit_download_gpx, name='summit_download_gpx'),
    re_path(r'^(?P<item_slug>[-\w]+)/routes/(?P<route_id>\d+)/$', peak_view_route, name='peak_view_route'),
    #no more peak > routes page - 02/14/2019
    #url(r'^(?P<item_slug>[-\w]+)/routes/$', 'items.views.peak_view_routes', name='peak_view_routes'),
    re_path(r'^(?P<item_slug>[-\w]+)/map/$', peak_view_map, name='peak_view_map'),
    re_path(r'^(?P<item_slug>[-\w]+)/$', peak_view, name='peak_view'),
    #splash badges
    re_path(r'^(?P<item_slug>[-\w]+)/summits/(?P<summitlog_id>\d+)/badge_peak/$', summit_badge_peak, name='summit_badge_peak'),
    re_path(r'^(?P<item_slug>[-\w]+)/summits/(?P<summitlog_id>\d+)/badge_first_ascent/$', summit_badge_first_ascent, name='summit_badge_first_ascent'),

    re_path(r'^region/(?P<slug>[-\w]+)(-mountains)/$', url_resolver, name='directory_url_resolver'),
    re_path(r'^region/(?P<slug>[-\w]+)(-mountains)/by_(?P<order>\w+)/$', url_resolver, name='directory_url_resolver'),
    re_path(r'^region/(?P<slug>[-\w]+)(-mountains)/(?P<view>\w+)/$', url_resolver, name='directory_url_resolver'),
    re_path(r'^region/(?P<slug>[-\w]+)(-mountains)/(?P<page>\d+)/$', url_resolver, name='directory_url_resolver'),
    re_path(r'^region/(?P<slug>[-\w]+)(-mountains)/by_(?P<order>\w+)/(?P<page>\d+)/$', url_resolver, name='directory_url_resolver'),

    path('region/edit_country_highlights/<int:country_id>/', csrf_exempt(edit_country_highlights), name='edit_country_highlights'),
    path('region/edit_continent_highlights/<int:continent_id>/', csrf_exempt(edit_continent_highlights), name='edit_continent_highlights'),
    path('region/edit_region_highlights/<int:region_id>/', csrf_exempt(edit_region_highlights), name='edit_region_highlights'),

    path('Antarctica-continent/', antarctica, name='antarctica'),
    re_path(r'^(?P<slug>[-\w]+)/(?P<slug_region>[-\w]+)/$', url_resolver, name='directory_url_resolver_region'),
    re_path(r'^(?P<slug>[-\w]+)/(?P<slug_region>[-\w]+)/(?P<view>\w+)/$', url_resolver, name='directory_url_resolver_region'),
]