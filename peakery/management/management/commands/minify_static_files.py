from django.core.management.base import BaseCommand
from css_html_js_minify import process_single_html_file, process_single_js_file, process_single_css_file
from peakery.settings import STATIC_ROOT
from multiprocessing import Pool
import os


class Command(BaseCommand):
    help = "Minifies the Django static files"

    def handle(self, *args, **options):
        print("Minifying static files")
        results = []
        with Pool(processes=4) as pool:
            for root, dirs, files in os.walk(STATIC_ROOT):
                for file in files:
                    file_path = os.path.join(root, file)
                    if file_path.endswith(".js") and not file_path.endswith("min.js"):
                        results.append(pool.apply_async(process_single_js_file, (file_path,), dict(overwrite=True)))

                    elif file_path.endswith(".css") and not file_path.endswith("min.css"):
                        results.append(pool.apply_async(process_single_css_file, (file_path,), dict(overwrite=True)))

                    #if file_path.endswith(".js"):
                    #    process_single_js_file(file_path, overwrite=False)
                    #elif file_path.endswith(".css"):
                    #    process_single_css_file(file_path, overwrite=False)
                    # elif file_path.endswith(".html"):
                    #    process_single_html_file(file_path, overwrite=False)
            for result in results:
                result.get()
        print("Minifying static files DONE")