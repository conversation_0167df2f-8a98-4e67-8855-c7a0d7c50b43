from django.core.management.base import BaseCommand
from peakery import settings
from botocore.exceptions import ClientError
import os
import boto3
import mimetypes
from multiprocessing import Pool


s3_client = boto3.client('s3', aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                                 aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)


def upload_to_s3(root, file):
    file_path = os.path.join(root, file)
    with open(file_path, "rb") as f:
        s3_path = os.path.relpath(file_path, settings.STATIC_ROOT).replace("\\", "/")
        print(s3_path)
        try:
            file_mime = mimetypes.guess_type(file)[0] or 'binary/octet-stream'
            s3_client.upload_fileobj(f, "peakery-static", s3_path, ExtraArgs={'ContentType': file_mime})
        except ClientError as e:
            print("An error occurred pushing static files to s3", file)
            print(e)


class Command(BaseCommand):
    help = "Minifies the Django static files"

    def handle(self, *args, **options):
        static_root = settings.STATIC_ROOT

        print("Uploading files to S3")
        results = []
        with Pool(processes=10) as pool:
            for root, dirs, files in os.walk(static_root):
                for file in files:
                    results.append(pool.apply_async(upload_to_s3, [root, file]))

            for result in results:
                result.get()
        print("Uploading files to S3 Successful")

        # TODO Add logic to optionally delete files in S3 that are not in the static dir
