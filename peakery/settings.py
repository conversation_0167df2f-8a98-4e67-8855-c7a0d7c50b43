# Django settings for appery project.
import os
from urllib.parse import urlparse
import uuid
uuid._uuid_generate_random = None
PROJECT_DIR = os.path.dirname(__file__).replace('\\','/')

DEBUG = False
if 'ENABLE_DEBUG' in os.environ and os.environ['ENABLE_DEBUG'] == "True":
    DEBUG = True

DEV = os.environ['DEV']
if DEV == "True":
    DEV = True
else:
    DEV = False
    TEMPLATE_DEBUG = False
ALLOWED_HOSTS = ['peakery.com','peakery.herokuapp.com','peakerydev.herokuapp.com']
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SECURE_SSL_REDIRECT = True

EMAIL_SUBJECT_PREFIX = "[peakery.com]"
TO_EMAIL = '<EMAIL>'
SERVER_EMAIL = '<EMAIL>'
DEFAULT_FROM_EMAIL_FEEDBACK = 'peakery <<EMAIL>>'
DEFAULT_FROM_EMAIL_NOTIFICATIONS = 'peakery <<EMAIL>>'
DEFAULT_FROM_EMAIL = 'peakery <<EMAIL>>'

# Local time zone for this installation.
TIME_ZONE = 'America/Los_Angeles'

# Language code for this installation.
LANGUAGE_CODE = 'en-us'

SITE_ID = 1

# If you set this to False, Django will make some optimizations so as not
# to load the internationalization machinery.
USE_I18N = False

# If you set this to False, Django will not format dates, numbers and
# calendars according to the current locale
USE_L10N = True

# Locale for number formatting
LOCALE = 'en_US.UTF-8'

# Absolute path to the directory that holds media.
# Example: "/home/<USER>/media.lawrence.com/"
MEDIA_ROOT = os.path.join(PROJECT_DIR, "media").replace('\\','/') + '/'
MEDIA_URL = 'https://s3-us-west-1.amazonaws.com/peakery-media/'
STATIC_ROOT = os.path.join(PROJECT_DIR, "static_minified").replace('\\','/') + '/'
STATIC_URL = 'https://s3-us-west-1.amazonaws.com/peakery-static/'

S3_URL = 'https://s3-us-west-1.amazonaws.com/peakery-static/'
S3_MEDIA_URL = 'https://s3-us-west-1.amazonaws.com/peakery-media/'
API_URL = 'https://peakery-api.herokuapp.com/1.0/'
SITE_DOMAIN = 'peakery.com'
SITE_URL = os.environ['SITE_URL']
SERVER_NAME = os.environ['SERVER_NAME']

#S3 Boto Config
DEFAULT_FILE_STORAGE = 'storages.backends.s3boto3.S3Boto3Storage'
AWS_PRELOAD_METADATA = False
AWS_ACCESS_KEY_ID = "********************"
AWS_SECRET_ACCESS_KEY = "BQrIkkSJQKKQJ0yE5Czd01SZnbfXVE9dv7NZyUK0"
AWS_STORAGE_BUCKET_NAME = 'peakery-media'
AWS_S3_HOST = "s3-us-west-1.amazonaws.com"
AWS_MAX_SIZE = 15000000
AWS_HEADERS = {'Cache-Control': str('public, max-age=86400')}

NO_PEAK_ID = os.environ['NO_PEAK_ID']
DEFAULT_AUTO_FIELD = 'django.db.models.AutoField'

# Make this unique, and don't share it with anybody.
SECRET_KEY = 'icgf@qm0c*79!6@pz)y!xl7l*83+ks8yzn4*ld+)%xdpwy*h##'

# List of callables that know how to import templates from various sources.
TEMPLATE_LOADERS = (
    ('django.template.loaders.cached.Loader', (
        'django.template.loaders.filesystem.Loader',
        'django.template.loaders.app_directories.Loader',
    )),
)

BLOCKED_IPS = ('**************','**************','**************','************','**************','*************','*************','**************','**************','************','*************','*************','***********','*************','*************','************','*************','************','***********',',************','*************','***********','***********','************','*************','************','************','*************','************','************','************','***********','*************',)

SESSION_SERIALIZER='django.contrib.sessions.serializers.PickleSerializer'

AUTHENTICATION_BACKENDS = ('backends.CaseInsensitiveModelBackend', )

MIDDLEWARE = [
    'django.middleware.gzip.GZipMiddleware',
    'peakery.middleware.UrlMiddleware',
    'peakery.middleware.BlockedIpMiddleware',
    'django.contrib.redirects.middleware.RedirectFallbackMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.contrib.flatpages.middleware.FlatpageFallbackMiddleware',
    'peakery.middleware.ActivityLogMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'peakery.middleware.AjaxMiddleware',
]


TEMPLATE_CONTEXT_PROCESSORS = (
    'django.contrib.auth.context_processors.auth',
    "django.core.context_processors.debug",
    "django.core.context_processors.media",
    "django.core.context_processors.request",
    'django.contrib.messages.context_processors.messages',
    'notification.context_processors.notification',
    'context_processors.IS_MOBILE_APP_ACCESS',
    'context_processors.MOBILE_APP_BACK',
    'context_processors.read_only_helper',
    'context_processors.ssi',
    'context_processors.S3_URL',
    'context_processors.S3_MEDIA_URL',
    'context_processors.SITE_URL',
    'ip2geo.context_processors.add_session',
)

MESSAGE_STORAGE = 'django.contrib.messages.storage.session.SessionStorage'

AUTHENTICATION_BACKENDS = (
    'django.contrib.auth.backends.ModelBackend',
    'peakery.accounts.backends.EmailOrUsernameModelBackend',
)

ROOT_URLCONF = 'peakery.urls'
IS_READ_ONLY = True
TEMPLATE_DIRS = (
    os.path.join(PROJECT_DIR, "templates").replace('\\','/'),
)

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(PROJECT_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                # Insert your TEMPLATE_CONTEXT_PROCESSORS here or use this
                # list if you haven't customized them:
                'django.contrib.auth.context_processors.auth',
                'django.template.context_processors.request',
                'django.template.context_processors.debug',
                'django.template.context_processors.i18n',
                'django.template.context_processors.media',
                'django.template.context_processors.static',
                'django.template.context_processors.tz',
                'django.contrib.messages.context_processors.messages',
                'peakery.context_processors.IS_MOBILE_APP_ACCESS',
                'peakery.context_processors.MOBILE_APP_BACK',
                'peakery.context_processors.read_only_helper',
                'peakery.context_processors.ssi',
                'peakery.context_processors.S3_URL',
                'peakery.context_processors.S3_MEDIA_URL',
                'peakery.context_processors.SITE_URL',
            ],
        },
    },
]

INSTALLED_APPS = (
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.sites',
    'django.contrib.messages',
    'django.contrib.gis',
    'django.contrib.flatpages',
    # 'django.contrib.sitemaps',
    'django.contrib.redirects',
    'django_ses',
    'peakery.django_extensions',
    'django.contrib.humanize',
    'django.contrib.staticfiles',
    'peakery.accounts',
    'peakery.api',
    'peakery.tools',
    'peakery.main',
    'peakery.items',
    'peakery.tempitems',
    'peakery.directory',
    'peakery.management',
    'avatar',
    'django.contrib.admin',
    'django.contrib.admindocs',
    'peakery.cities',
    'sorl.thumbnail',
    'widget_tweaks',
    'gunicorn',
    'peakery.follow',
    'peakery.notification',
    'peakery.favorites',
    'ip2geo',
    'storages',
    'rq',
    'pygments',
    'googlemaps',
    'polyline',
    'mapbox',
)

MAPBOX_ACCESS_TOKEN = 'pk.eyJ1IjoicGVha2VyeSIsImEiOiJjampra3Z0bnAxeTVnM3FteHlybHY3b2p1In0.7a5dEa5-995VUv8ceHHNmw'


REFRESH_CACHE_ITEM_COUNT = 10
REFRESH_CACHE_ITEM_DELAY = 10

LOGIN_URL = '/'
LOGIN_REDIRECT_URL = '/'

EXPLORE_REMOVEWORDS = ['mountain ',' mountain','mt ',' mt','mt. ',' mt.','mount ',' mount','mtn ',' mtn','mtn. ',' mtn.','peak ',' peak','cerro ',' cerro','kuh-e ',' kuh-e','monte ',' monte','jabal ',' jabal','gora ',' gora','gunung ',' gunung','koh-e ',' koh-e','knob ',' knob','butte ',' butte','alto ',' alto','loma ',' loma','mont ',' mont','shan ',' shan','ghar ',' ghar','djebel ',' djebel','pico ',' pico']

GEOCHART_REGIONS = {"NA":"021","AS":"142","EU":"150","AF":"002","AN":"AQ","SA":"005","OC":"009"}

EXPLORE_CHANGEWORDS = {u"\u2019":"'" , "`":"'",u"\u00B4":"'", u" mtn.":"mount "}
EXPLORE_NEARCHANGEWORDS = {
"AL":"Alabama",
"AK":"Alaska",
"AZ":"Arizona",
"AR":"Arkansas",
"CA":"California",
"CO":"Colorado",
"CT":"Connecticut",
"DE":"Delaware",
"FL":"Florida",
"GA":"Georgia",
"HI":"Hawaii",
"ID":"Idaho",
"IL":"Illinois",
"IN":"Indiana",
"IA":"Iowa",
"KS":"Kansas",
"KY":"Kentucky",
"LA":"Louisiana",
"ME":"Maine",
"MD":"Maryland",
"MA":"Massachusetts",
"MI":"Michigan",
"MN":"Minnesota",
"MS":"Mississippi",
"MO":"Missouri",
"MT":"Montana",
"NE":"Nebraska",
"NV":"Nevada",
"NH":"New Hampshire",
"NJ":"New Jersey",
"NM":"New Mexico",
"NY":"New York",
"NC":"North Carolina",
"ND":"North Dakota",
"OH":"Ohio",
"OK":"Oklahoma",
"OR":"Oregon",
"PA":"Pennsylvania",
"RI":"Rhode Island",
"SC":"South Carolina",
"SD":"South Dakota",
"TN":"Tennessee",
"TX":"Texas",
"UT":"Utah",
"VT":"Vermont",
"VA":"Virginia",
"WA":"Washington",
# "WASHINGTON":"State of Washington",
"WV":"West Virginia",
"WI":"Wisconsin",
"WY":"Wyoming",
# Canada
"AB" : "Alberta",
"BC":"ABBritish Columbia" ,
"MB":"Manitoba",
"NB":"New Brunswick",
"NL":"Newfoundland and Labrador" ,
"NT":"Northwest Territories",
"NS":"Nova Scotia",
"NU":"Nunavut",
"ON":"Ontario",
"PE":"Prince Edward Island",
"QC":"Quebec",
"SK":"Saskatchewan",
"YT":"Yukon"

}

PARSE_DESCRIPTION_ITEMS = {}

RECAPTCHA_PUBLIC_KEY = '6Lcjz78SAAAAAHqvg66pXk4WMmOLRK2WJpWCbUGa'
RECAPTCHA_SECRET_KEY = '6Lcjz78SAAAAAMfopkztNvPbCLV14Grxun9mGQ3r'

#THUMBNAIL_SUBDIR = 'thumbs'
THUMBNAIL_BASEDIR = 'images'

SSI_ENABLED = False

# Database Config #
DATABASE_URL = urlparse(os.environ.get('DATABASE_URL'))
DATABASES = {
 'default': {
     'ENGINE': 'django.contrib.gis.db.backends.postgis',
     'NAME': DATABASE_URL.path.lstrip('/'),
     'USER': DATABASE_URL.username,
     'PASSWORD': DATABASE_URL.password,
     'HOST': DATABASE_URL.hostname,
     'PORT': DATABASE_URL.port,
     'DISABLE_SERVER_SIDE_CURSORS': True,  # required when using pgbouncers pool_mode=transaction
     'CONN_MAX_AGE': None,  # Set to None for unlimited persistent connections (PGBouncer takes care of closing them)
  }
}

POSTGIS_VERSION = (2, 2, 1)
# Database Config End #

REMOVE_WWW = True

#Setting to restrict access by IP (staging only)
RESTRICT_IP = os.environ['RESTRICT_IP']
ALLOWED_IPS = ['*************','**********','2601:647:203:6a53:84f2:84af:6bd0:a402','2603:3024:1700:b900:3cc9:754b:ee0:ca5b']

#Cache Configuration
REDIS_URL = urlparse(os.environ.get('REDISCLOUD_URL'))

CACHES = {
        'default': {
            'BACKEND': 'redis_cache.RedisCache',
            'LOCATION': '%s:%s' % (REDIS_URL.hostname, REDIS_URL.port),
            'OPTIONS': {
                'PASSWORD': REDIS_URL.password,
                'DB': 0,
        }
    }
}

RETURN_URL = 'http://test.peakery.com'

#SETTINGS FOR IMPORTING CONTACTS FROM EMAIL
OPENINVITER_USERNAME = 'panchicore'
OPENINVITER_PRIVATE_KEY = '68975d97a8cce2c664f99b33b3f1b1d3'

NOTIFICATION_QUEUE_ALL = False

#AUTOCOMPLETE AVATARS
GMAIL_AVATAR = 'http://aux3.iconpedia.net/uploads/6641326531746650316.png'
INPUT_AVATAR = 'http://www.psdgraphics.com/wp-content/uploads/2009/07/email-icon.gif'

MAINTENANCE_MODE = False

##### End Local Settings Stuff ######

AVATAR_GRAVATAR_BACKUP = False

AVATAR_DEFAULT_URL = '%simg/default-user.png' % MEDIA_URL # if django-avatar crashes some day


MIN_PEAK_IMG_WIDTH = 1000
MIN_PEAK_IMG_HEIGHT = 1000

MIN_SUMMIT_IMG_WIDTH = 1000
MIN_SUMMIT_IMG_HEIGHT = 1000

#PEAKSETTINGS
PEAK_NEAR_CITY_MAX_RADIUS = 30 #in miles

#MAP VIEW SETTINGS
MAP_SELECTED_PEAK_ZOOM = 4

#FOLLOW SETTINGS
MAX_FOLLOWS_PER_PAGE = 10

#AMAZON EMAIL SERVER CONFIG
EMAIL_BACKEND = 'django_ses.SESBackend'
AWS_ACCESS_KEY_ID = '********************'
AWS_SECRET_ACCESS_KEY = 'rxS/OJalD4QWNgdlDGGPeACidEnkV5Nxswssqxa1'
AWS_USERNAME = 'peakeryadmin'

GEOIP_PATH = '%s/ip2geo' % PROJECT_DIR

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'filters': {
        'require_debug_false': {
            '()': 'django.utils.log.RequireDebugFalse'
        }
    },
    'handlers': {
        # Include the default Django email handler for errors
        # This is what you'd get without configuring logging at all.
        'mail_admins': {
            'class': 'django.utils.log.AdminEmailHandler',
            'level': 'ERROR',
            'filters': ['require_debug_false'],
             # But the emails are plain text by default - HTML is nicer
            'include_html': True,
        },
    },
    'loggers': {
        # Again, default Django configuration to email unhandled exceptions
        'django.request': {
            'handlers': ['mail_admins'],
            'level': 'ERROR',
            'propagate': True,
        },
        # Might as well log any errors anywhere else in Django
        'django': {
            'handlers': ['mail_admins'],
            'level': 'ERROR',
            'propagate': False,
        },
    },
}

CACHE_MIDDLEWARE_SECONDS = 60 * 60 * 12

THUMBNAIL_CHECK_CACHE_MISS = True

THUMBNAIL_ALIASES = {
    '': {
        'user_big': {
            'size': (910, 680),
        },
        'user_thumb': {
            'size': (250, 179)
        },
        'peak_1': {'size': (120, 120), 'crop': True},
        'peak_2': {'size': (910, 580)},
        'peak_3': {'size': (745, 500)},
        'peak_4': {'size': (230, 160)},
        'peak_5': {'size': (650, 410)},
        'peak_6': {'size': (100, 80)},
        'peak_8': {'size': (340, 262)},
        'peak_9': {'size': (320, 190)},
    }
}

AUTO_GENERATE_AVATAR_SIZES = (65, 45, 100, 250, 85, 300, 135, 25, 80)

PASSWORD_HASHERS = (
    'django.contrib.auth.hashers.PBKDF2PasswordHasher',
    'django.contrib.auth.hashers.PBKDF2SHA1PasswordHasher',
    'django.contrib.auth.hashers.BCryptPasswordHasher',
    'django.contrib.auth.hashers.SHA1PasswordHasher',
    'django.contrib.auth.hashers.MD5PasswordHasher',
    'django.contrib.auth.hashers.CryptPasswordHasher',
)

NOTIFICATIONS_CONFIG = {
    'exclude': [
        'new_private_message',
        'new_comment_summitlog_me',
        'new_like_summitlog_me',
        'new_follower_me',
        'user_anonymous_added_to_summit_log',
        'user_contact_added_to_summit_log',
        'new_like_own_summitlog',
        'new_comment_me_summitlog_me',
        'avatar_updated',
        'avatar_friend_updated',
        'new_comment_following_summitlog',
        'new_like_following_summitlog',
        'new_following_follows',
        'new_following_summitlog',
        'following_new_kom',
        'following_new_steward',
        'following_new_first_ascent'
    ]
}
