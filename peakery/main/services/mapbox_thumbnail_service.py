"""
The idea of this service is to save money when using mapbox thumbnails.
Every time we generate a thumbnail, mapbox charges us for it.
The idea is to generate the thumbnails once, save them on S3, and save a reference on the database to that saved image.
"""
import string
import random
import time
import gpxpy.gpx
import polyline
import requests
import boto3
import urllib.parse
from django.core.files.storage import default_storage
from io import BytesIO
from peakery import settings
from peakery.main.models import MapboxThumbnail, MapboxThumbnailPending


STYLE_ID_FEET = 'cl45xqr4r000015pdrcq97oe5'
STYLE_ID_METERS = 'cl4513r11000315qxv4hoj449'
DEFAULT_STYLE_ID = 'cjjkkx6qa63mn2rthodesuu8m'
ACCESS_TOKEN = settings.MAPBOX_ACCESS_TOKEN
PEAK_MARKER = "url-https%3A%2F%2Fs3-us-west-1.amazonaws.com%2Fpeakery-static%2Fimg%2Fmarker-orange-on.png(LNG,LAT)/"
PEAK_MARKER_GPX = ",<EMAIL>(LNG,LAT)"

# ------------------------------
# Global HTTP session and S3 client for reusability
# ------------------------------
http_session = requests.Session()
s3_client = boto3.client('s3', aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                             aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY)


def get_mapbox_thumbnail_s3_url_lng_lat(lng, lat, add_marker=True, width=1260, height=945, zoom=14, hi_res=True,
                                        create_if_not_found=True, external_unique_id=None, country_codes=None):
    lng = str(lng)
    lat = str(lat)
    thumbnail = MapboxThumbnail.objects.filter(lat=lat, lng=lng, has_marker=add_marker, width=width, height=height,
                                               zoom=zoom, hi_res=hi_res, external_unique_id=external_unique_id).first()
    if thumbnail:
        return thumbnail.s3link

    if country_codes:
        if "US" in country_codes:
            style = STYLE_ID_FEET
        else:
            style = STYLE_ID_METERS
    else:
        style = STYLE_ID_METERS

    if hi_res:
        hi_res_string = "@2x"
    else:
        hi_res_string = ""

    # Thumbnail doesn't exist
    peak_thumbnail_url = "https://api.mapbox.com/styles/v1/peakery/" + style + "/static/MARKER" + lng + "," + lat + "," + str(
        zoom) + ",0.00,0.00/" + str(width) + "x" + str(height) + hi_res_string + "?access_token=" + ACCESS_TOKEN

    if add_marker:
        peak_thumbnail_url = peak_thumbnail_url.replace("MARKER", PEAK_MARKER.replace("LNG", lng).replace("LAT", lat))
    else:
        peak_thumbnail_url = peak_thumbnail_url.replace("MARKER", "")

    if create_if_not_found:
        create_pending_thumbnail(
            gpx_polyline=None,
            lat=lat,
            lng=lng,
            has_marker=add_marker,
            width=width,
            height=height,
            zoom=zoom,
            hi_res=hi_res,
            external_unique_id=external_unique_id,
            gpx_path=None,
            country_codes=country_codes,
            function="get_mapbox_thumbnail_s3_url_lng_lat"
        )
        return peak_thumbnail_url

    s3_url, _ = download_and_upload_thumb_to_s3(peak_thumbnail_url)

    if s3_url:
        MapboxThumbnail(
            lat=lat,
            lng=lng,
            s3link=s3_url,
            has_marker=add_marker,
            width=width,
            height=height,
            zoom=zoom,
            external_unique_id=external_unique_id,
            hi_res=hi_res
        ).save()

        return s3_url
    else:
        return peak_thumbnail_url


def get_mapbox_thumbnail_s3_url_lng_lat_batched(params_list):

    results = {}
    param_map = {}
    key_list = []
    for params in params_list:
        params['lng'] = str(params['lng'])
        params['lat'] = str(params['lat'])
        params['add_marker'] = params.get('add_marker', True)
        params['width'] = params.get('width', 1260)
        params['height'] = params.get('height', 945)
        params['zoom'] = params.get('zoom', 14)
        params['hi_res'] = params.get('hi_res', True)
        params['country_codes'] = params.get('country_codes', None)

        key = params.get('external_unique_id')
        param_map[key] = params
        key_list.append(key)

    # Query the database once for all matching thumbnails
    existing_thumbnails = MapboxThumbnail.objects.filter(external_unique_id__in=key_list)

    not_found = []
    for params in params_list:
        matched = False
        for thumbnail in existing_thumbnails:
            if (
                    thumbnail.external_unique_id == params['external_unique_id'] and
                    str(thumbnail.lng) == params['lng'] and
                    str(thumbnail.lat) == params['lat'] and
                    thumbnail.has_marker == params['add_marker'] and
                    thumbnail.width == params['width'] and
                    thumbnail.height == params['height'] and
                    thumbnail.zoom == params['zoom'] and
                    thumbnail.hi_res == params['hi_res']
            ):
                # If all keys match, add to results
                results[params['external_unique_id']] = thumbnail.s3link
                matched = True
                break

        if not matched:
            not_found.append(params)

    # Process the missing combinations
    for params in not_found:
        lng = params['lng']
        lat = params['lat']
        add_marker = params['add_marker']
        width = params['width']
        height = params['height']
        zoom = params['zoom']
        hi_res = params['hi_res']
        external_unique_id = params['external_unique_id']
        country_codes = params['country_codes']

        hi_res_string = "@2x" if hi_res else ""

        # Construct the Mapbox URL
        peak_thumbnail_url = (
                "https://api.mapbox.com/styles/v1/peakery/" + DEFAULT_STYLE_ID +
                "/static/MARKER" + lng + "," + lat + "," + str(zoom) + ",0.00,0.00/" +
                str(width) + "x" + str(height) + hi_res_string + "?access_token=" + ACCESS_TOKEN
        )

        if add_marker:
            peak_thumbnail_url = peak_thumbnail_url.replace(
                "MARKER", PEAK_MARKER.replace("LNG", lng).replace("LAT", lat)
            )
        else:
            peak_thumbnail_url = peak_thumbnail_url.replace("MARKER", "")

        results[external_unique_id] = peak_thumbnail_url

        create_pending_thumbnail(
            gpx_polyline=None,
            lat=lat,
            lng=lng,
            has_marker=add_marker,
            width=width,
            height=height,
            zoom=zoom,
            hi_res=hi_res,
            external_unique_id=external_unique_id,
            gpx_path=None,
            country_codes=country_codes,
            function="get_mapbox_thumbnail_s3_url_lng_lat"
        )

    return results


# TODO Improve this method so it also has batched logic like the other two - external_unique_id and async
def get_mapbox_thumbnail_s3_url_gpx(gpx_polyline, width=480, height=480, gpx_path=None, create_if_not_found=True):
    thumbnail = MapboxThumbnail.objects.filter(gpx_polyline=gpx_polyline, lat=None, lng=None, width=width,
                                               height=height).first()
    if thumbnail:
        return thumbnail.s3link
    parsed_gpx_polyline = urllib.parse.quote(gpx_polyline)
    # Thumbnail doesn't exist
    peak_thumbnail_url = 'https://api.mapbox.com/styles/v1/peakery/cjjkkx6qa63mn2rthodesuu8m/static/path-4+FC202E-0.5(' + parsed_gpx_polyline + ')/auto/' + str(
        width) + "x" + str(height) + '@2x?access_token=' + ACCESS_TOKEN

    if create_if_not_found:
        create_pending_thumbnail(
            gpx_polyline=gpx_polyline,
            lat=None,
            lng=None,
            has_marker=False,
            width=width,
            height=height,
            zoom=None,
            hi_res=False,
            external_unique_id=None,
            gpx_path=gpx_path,
            country_codes=None,
            function="get_mapbox_thumbnail_s3_url_gpx"
        )
        return peak_thumbnail_url

    s3_url, status_code = download_and_upload_thumb_to_s3(peak_thumbnail_url)

    # If Entity too large means gpx file is too big to render. We will process it again with lower res.
    if status_code in (413, 401, 414) and gpx_path is not None:
        gpx_polyline = get_encoded_polyline_for_mapbox(gpx_path)
        if gpx_polyline:
            gpx_polyline = urllib.parse.quote(gpx_polyline)
            peak_thumbnail_url = 'https://api.mapbox.com/styles/v1/peakery/cjjkkx6qa63mn2rthodesuu8m/static/path-4+FC202E-0.5(' + gpx_polyline + ')/auto/' + str(
                width) + "x" + str(height) + '@2x?access_token=' + ACCESS_TOKEN
            s3_url, status_code = download_and_upload_thumb_to_s3(peak_thumbnail_url)
            print("Second attempt was ", status_code)

    if s3_url:
        MapboxThumbnail(
            gpx_polyline=gpx_polyline,
            s3link=s3_url,
            width=width,
            height=height
        ).save()

        return s3_url
    else:
        return peak_thumbnail_url


def get_mapbox_thumbnail_s3_url_gpx_lng_lat(gpx_polyline, lng, lat, width=480, height=480, gpx_path=None,
                                            create_if_not_found=True, external_unique_id=None):
    thumbnail = MapboxThumbnail.objects.filter(lat=lat, lng=lng, gpx_polyline=gpx_polyline, width=width,
                                               height=height, external_unique_id=external_unique_id).first()
    if thumbnail:
        return thumbnail.s3link
    parsed_gpx_polyline = urllib.parse.quote(gpx_polyline)
    peak_marker = get_gpx_marker(str(lat),str(lng))

    # Thumbnail doesn't exist
    peak_thumbnail_url = 'https://api.mapbox.com/styles/v1/peakery/cjjkkx6qa63mn2rthodesuu8m/static/path-4+FC202E-0.5(' + parsed_gpx_polyline + ')' + peak_marker + '/auto/' + str(
        width) + "x" + str(height) + '@2x?access_token=' + ACCESS_TOKEN

    if create_if_not_found:
        create_pending_thumbnail(
            gpx_polyline=gpx_polyline,
            lat=lat,
            lng=lng,
            has_marker=False,
            width=width,
            height=height,
            zoom=None,
            hi_res=False,
            external_unique_id=external_unique_id,
            gpx_path=gpx_path,
            country_codes=None,
            function="get_mapbox_thumbnail_s3_url_gpx_lng_lat"
        )
        return peak_thumbnail_url

    s3_url, status_code = download_and_upload_thumb_to_s3(peak_thumbnail_url)

    # If Entity too large means gpx file is too big to render. We will process it again with lower res.
    if status_code in (413, 401, 414) and gpx_path is not None:
        gpx_polyline = get_encoded_polyline_for_mapbox(gpx_path)
        if gpx_polyline:
            gpx_polyline = urllib.parse.quote(gpx_polyline)
            peak_thumbnail_url = 'https://api.mapbox.com/styles/v1/peakery/cjjkkx6qa63mn2rthodesuu8m/static/path-4+FC202E-0.5(' + gpx_polyline + '),' + peak_marker + 'auto/' + str(
                width) + "x" + str(height) + '@2x?access_token=' + ACCESS_TOKEN
            s3_url, status_code = download_and_upload_thumb_to_s3(peak_thumbnail_url)
            print("Second attempt was ", status_code)

    if s3_url:
        MapboxThumbnail(
            gpx_polyline=gpx_polyline,
            lat=lat,
            lng=lng,
            s3link=s3_url,
            width=width,
            height=height,
            external_unique_id=external_unique_id
        ).save()

        return s3_url
    else:
        return peak_thumbnail_url


def get_mapbox_thumbnail_s3_url_gpx_lng_lat_batched(params_list):
    results = {}
    param_map = {}
    key_list = []
    for params in params_list:
        params['lng'] = str(params['lng'])
        params['lat'] = str(params['lat'])
        params['gpx_polyline'] = params.get('gpx_polyline')
        params['width'] = params.get('width', 480)
        params['height'] = params.get('height', 480)
        params['gpx_path'] = params.get('gpx_path', None)

        key = params.get('external_unique_id')
        param_map[key] = params
        key_list.append(key)

    # Query the database once for all matching thumbnails
    existing_thumbnails = MapboxThumbnail.objects.filter(external_unique_id__in=key_list)

    not_found = []
    for params in params_list:
        matched = False
        for thumbnail in existing_thumbnails:
            if (
                    thumbnail.external_unique_id == params['external_unique_id'] and
                    str(thumbnail.lng) == params['lng'] and
                    str(thumbnail.lat) == params['lat'] and
                    thumbnail.width == params['width'] and
                    thumbnail.height == params['height']
            ):
                # If all keys match, add to results
                results[params['external_unique_id']] = thumbnail.s3link
                matched = True
                break

        if not matched:
            not_found.append(params)

    # Process the missing combinations
    for params in not_found:
        lng = params['lng']
        lat = params['lat']
        gpx_polyline = params['gpx_polyline']
        width = params['width']
        height = params['height']
        external_unique_id = params['external_unique_id']

        parsed_gpx_polyline = urllib.parse.quote(gpx_polyline)
        peak_marker = get_gpx_marker(str(lat),str(lng))

        # Thumbnail doesn't exist
        peak_thumbnail_url = 'https://api.mapbox.com/styles/v1/peakery/cjjkkx6qa63mn2rthodesuu8m/static/path-4+FC202E-0.5(' + parsed_gpx_polyline + ')' + peak_marker + '/auto/' + str(
            width) + "x" + str(height) + '@2x?access_token=' + ACCESS_TOKEN

        results[external_unique_id] = peak_thumbnail_url

        create_pending_thumbnail(
            gpx_polyline=params['gpx_polyline'],
            lat=params['lat'],
            lng=params['lng'],
            has_marker=False,
            width=params['width'],
            height=params['height'],
            zoom=None,
            hi_res=False,
            external_unique_id=params['external_unique_id'],
            gpx_path=params['gpx_path'],
            country_codes=None,
            function="get_mapbox_thumbnail_s3_url_gpx_lng_lat"
        )

    return results


def download_and_upload_thumb_to_s3(image_url):
    response = http_session.get(image_url)
    if response.status_code != 200:
        print("Failed to download image. Status code:", response.status_code)
        return None, response.status_code

    filename = ''.join(random.choices(string.ascii_uppercase + string.digits, k=15)) + str(time.time())
    path = 'images/items/mapbox_thumbnails/' + filename
    image_content = BytesIO(response.content)
    s3_client.upload_fileobj(
        image_content,
        "peakery-media",
        path,
        ExtraArgs={'ContentType': response.headers['Content-Type']}  # Keep original content type
    )

    return f"https://peakery-media.s3.amazonaws.com/{path}", response.status_code


def get_encoded_polyline_for_mapbox(gpx_path):
    if not gpx_path:
        return None

    try:
        f = default_storage.open(gpx_path, 'r')
        gpx = gpxpy.parse(f)
        success = reduce_gpx_points_for_mapbox(gpx)

        if success:
            return encode_gpx(gpx)
        else:
            print("Couldn't reduce GPX", gpx_path)
            return None

    except Exception as e:
        print(e)
        return None


def encode_gpx(gpx):
    try:
        points = []
        if gpx.tracks:
            for track in gpx.tracks:
                for segment in track.segments:
                    for point in segment.points:
                        points.append((point.latitude, point.longitude))
        elif gpx.routes:
            for route in gpx.routes:
                for point in route.points:
                    points.append((point.latitude, point.longitude))

        return polyline.encode(points, 5)
    except Exception as e:
        print("Couldn't encode gpx", e)
        return None


def total_distance_from_encoded_polyline(encoded_polyline, is_usa):
    if not encoded_polyline:
        return 0

    division_factor = 1000
    if is_usa:
        division_factor = 1609

    gpx = gpxpy.gpx.GPX()

    decoded_points = polyline.decode(encoded_polyline, precision=5)

    gpx_track = gpxpy.gpx.GPXTrack()
    gpx.tracks.append(gpx_track)

    gpx_segment = gpxpy.gpx.GPXTrackSegment()
    gpx_track.segments.append(gpx_segment)

    for lat, lon in decoded_points:
        gpx_segment.points.append(gpxpy.gpx.GPXTrackPoint(lat, lon))

    length_2d = gpx.length_2d() or 0
    return round(length_2d / division_factor, 2)


def reduce_gpx_points_for_mapbox(gpx):
    """
        Mapbox uses cloudfront, which has a maximum URL length (See https://stackoverflow.com/questions/40244883/cloudfront-responds-with-a-status-of-413-request-entity-too-large)
        Because of that, we have to aggressively shrink down some gpx polylines in order for them to fit in the URL when calling mapbox.

        Returns True if it was able to reduce the polyline below 6000 characters. False otherwise
    """

    # URL length threshold after encoding.
    threshold = 7500
    # Check if the current gpx is already good before compressing it
    poly = encode_gpx(gpx)
    if len(urllib.parse.quote(poly)) <= threshold:
        return True

    MAX_ITERATIONS = 100
    current_iteration = 1

    while current_iteration <= MAX_ITERATIONS:
        current_iteration = current_iteration + 1
        gpx.reduce_points(None, current_iteration)
        poly = encode_gpx(gpx)
        if len(urllib.parse.quote(poly)) <= threshold:
            return True

    return False


def create_pending_thumbnail(gpx_polyline, lat, lng, has_marker, width, height, zoom, hi_res, external_unique_id, gpx_path, country_codes, function):
    try:
        MapboxThumbnailPending.objects.update_or_create(
            gpx_polyline=gpx_polyline,
            lat=lat,
            lng=lng,
            has_marker=has_marker,
            width=width,
            height=height,
            zoom=zoom,
            hi_res=hi_res,
            external_unique_id=external_unique_id,
            function=function,
            country_codes=country_codes,
            gpx_path=gpx_path
        )
    except Exception as e:
        print("create_pending_thumbnail failed", gpx_polyline, lat, lng, has_marker, width, height, zoom, hi_res, external_unique_id, gpx_path, country_codes, function, e)


def get_gpx_marker(lat, lon):
    if lat == "9999.0" and lon == "9999.0":
        return ""
    return PEAK_MARKER_GPX.replace("LNG", str(lon)).replace("LAT", str(lat))