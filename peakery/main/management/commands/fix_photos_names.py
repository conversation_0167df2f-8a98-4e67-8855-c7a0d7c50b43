from django.core.management.base import BaseCommand

class Command(BaseCommand):
    args = ''
    help = ''

    def handle(self, *args, **options):
        from items.models import Item

        #Rename main peak photos
        items = Item.objects.filter(id__gt = 0).order_by('id')

        for item in items:
            print item.id
            try:
                thumbnail_file = item.thumbnail.file
                print "existe %s" % thumbnail_file
            except Exception:
                main_photos = item.photos.filter(category__name='peak')
                if main_photos:
                    item.thumbnail = main_photos[0].image.name
                    item.thumbnail_credit = main_photos[0].user.username
                    item.save()
                    print item.thumbnail
                else:
                    print "No hay fotos"
