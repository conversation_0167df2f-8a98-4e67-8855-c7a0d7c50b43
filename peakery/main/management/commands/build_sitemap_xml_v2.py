from django.core.management.base import BaseCommand
from django.db import connection
import sys
from peakery.utils.utils import dictfetchall
from datetime import datetime, timedelta

class Command(BaseCommand):
    args = ''
    help = ''

    def handle(self, *args, **options):
        from lxml import etree as ET
        from django.core.files.storage import default_storage

        # Check if six months (182 days) have passed since last creation
        # first get a file and check its last modified time
        if default_storage.exists("/sitemap/sitemap_index.xml"):
           # get the time of last modification
           modified_time = default_storage.get_modified_time("/sitemap/sitemap_index.xml")
           if datetime.now() - modified_time < timedelta(days=182):
               return self.stdout.write('Six months have not passed since last creation. Process completed')

        self.stdout.write('Building sitemap files...')

        #init sitemap index file
        sitemapindex = ET.Element("sitemapindex", xmlns="http://www.sitemaps.org/schemas/sitemap/0.9")

        #set mod date
        lastmod = datetime.now().strftime('%Y-%m-%d')

        ###########################
        ##  Peak Pages           ##
        ###########################

        #get peaks
        sql = "select a.id, a.slug_new_text " + \
            "from items_item a where a.active = true " + \
            "order by a.id "

        cursor = connection.cursor()
        cursor.execute(sql)
        items = dictfetchall(cursor)

        item_counter = 0
        item_file_index = 1

        for i in items:

            if item_counter % 50000 == 0:

                self.stdout.write('Processing peak page %s...' % item_file_index)

                #if not the first file, close out the previous files
                if item_file_index > 1:
                    #write item info sitemap
                    tree = ET.ElementTree(item_info_urlset)
                    xml_file = default_storage.open(item_info_filename, "w")
                    tree.write(xml_file, encoding='utf-8', xml_declaration=True, pretty_print=True)
                    xml_file.close()

                #init urlset files
                item_info_urlset = ET.Element("urlset", xmlns="http://www.sitemaps.org/schemas/sitemap/0.9")
                item_info_filename = "/sitemap/sitemap-item_info-%s.xml" % item_file_index
                sitemap = ET.SubElement(sitemapindex, "sitemap")
                ET.SubElement(sitemap, "loc").text = "https://peakery.com/sitemap-item_info-%s.xml" % item_file_index
                ET.SubElement(sitemap, "lastmod").text = lastmod

                item_file_index += 1
          

            #add urls to urlset files
            item_info_url = ET.SubElement(item_info_urlset, "url")
            ET.SubElement(item_info_url, "loc").text = "https://peakery.com/%s/" % i['slug_new_text']
            ET.SubElement(item_info_url, "lastmod").text = lastmod

            item_counter += 1

        # handle any remaining items
        if item_counter % 50000 != 0:
            tree = ET.ElementTree(item_info_urlset)
            xml_file = default_storage.open(item_info_filename, "w")
            tree.write(xml_file, encoding='utf-8', xml_declaration=True, pretty_print=True)
            xml_file.close()

        ###########################
        ##  Regions Pages        ##
        ###########################

        #init urlset files
        region_info_urlset = ET.Element("urlset", xmlns="http://www.sitemaps.org/schemas/sitemap/0.9")
        region_info_filename = "/sitemap/sitemap-region_info-1.xml"
        sitemap = ET.SubElement(sitemapindex, "sitemap")
        ET.SubElement(sitemap, "loc").text = "https://peakery.com/sitemap-region_info-1.xml"
        ET.SubElement(sitemap, "lastmod").text = lastmod

        region_peaks_urlset = ET.Element("urlset", xmlns="http://www.sitemaps.org/schemas/sitemap/0.9")
        region_peaks_filename = "/sitemap/sitemap-region_peaks-1.xml"
        sitemap = ET.SubElement(sitemapindex, "sitemap")
        ET.SubElement(sitemap, "loc").text = "https://peakery.com/sitemap-region_peaks-1.xml"
        ET.SubElement(sitemap, "lastmod").text = lastmod

        #get continents
        sql = "select a.id, a.slug from cities_continent a where a.lat is not null order by a.id "

        cursor = connection.cursor()
        cursor.execute(sql)
        continents = dictfetchall(cursor)

        self.stdout.write('Processing continents...')

        for c in continents:

            #add urls to urlset files
            region_info_url = ET.SubElement(region_info_urlset, "url")
            ET.SubElement(region_info_url, "loc").text = "https://peakery.com/region/%s-mountains/" % c['slug']
            ET.SubElement(region_info_url, "lastmod").text = lastmod

            region_peaks_url = ET.SubElement(region_peaks_urlset, "url")
            ET.SubElement(region_peaks_url, "loc").text = "https://peakery.com/region/%s-mountains/peaks/" % c['slug']
            ET.SubElement(region_peaks_url, "lastmod").text = lastmod

        #get countries
        sql = "select a.id, a.slug from cities_country a order by a.id "

        cursor = connection.cursor()
        cursor.execute(sql)
        countries = dictfetchall(cursor)

        self.stdout.write('Processing countries...')

        for c in countries:

            #add urls to urlset files
            region_info_url = ET.SubElement(region_info_urlset, "url")
            ET.SubElement(region_info_url, "loc").text = "https://peakery.com/region/%s-mountains/" % c['slug']
            ET.SubElement(region_info_url, "lastmod").text = lastmod

            region_peaks_url = ET.SubElement(region_peaks_urlset, "url")
            ET.SubElement(region_peaks_url, "loc").text = "https://peakery.com/region/%s-mountains/peaks/" % c['slug']
            ET.SubElement(region_peaks_url, "lastmod").text = lastmod

        #get regions
        sql = "select a.id, a.slug as region_slug, b.slug as country_slug " + \
            "from cities_region a, cities_country b " + \
            "where a.country_id = b.id and exists (select 1 from items_item_region c where c.region_id = a.id) " + \
            "order by a.id "

        cursor = connection.cursor()
        cursor.execute(sql)
        regions = dictfetchall(cursor)

        self.stdout.write('Processing regions...')

        for r in regions:

            #add urls to urlset files
            region_info_url = ET.SubElement(region_info_urlset, "url")
            ET.SubElement(region_info_url, "loc").text = "https://peakery.com/%s-mountains/%s/" % (r['country_slug'], r['region_slug'])
            ET.SubElement(region_info_url, "lastmod").text = lastmod

            region_peaks_url = ET.SubElement(region_peaks_urlset, "url")
            ET.SubElement(region_peaks_url, "loc").text = "https://peakery.com/%s-mountains/%s/peaks/" % (r['country_slug'], r['region_slug'])
            ET.SubElement(region_peaks_url, "lastmod").text = lastmod

        #close out the region files
        #write region info sitemap
        tree = ET.ElementTree(region_info_urlset)
        xml_file = default_storage.open(region_info_filename, "w")
        tree.write(xml_file, encoding='utf-8', xml_declaration=True, pretty_print=True)
        xml_file.close()

        #write region peaks sitemap
        tree = ET.ElementTree(region_peaks_urlset)
        xml_file = default_storage.open(region_peaks_filename, "w")
        tree.write(xml_file, encoding='utf-8', xml_declaration=True, pretty_print=True)
        xml_file.close()

        ###########################
        ##  Challenges Pages     ##
        ###########################

        #init urlset files
        challenge_info_urlset = ET.Element("urlset", xmlns="http://www.sitemaps.org/schemas/sitemap/0.9")
        challenge_info_filename = "/sitemap/sitemap-challenge_info-1.xml"
        sitemap = ET.SubElement(sitemapindex, "sitemap")
        ET.SubElement(sitemap, "loc").text = "https://peakery.com/sitemap-challenge_info-1.xml"
        ET.SubElement(sitemap, "lastmod").text = lastmod

        #get challenges
        sql = "select a.id, a.slug from items_itemgroup a order by a.id "

        cursor = connection.cursor()
        cursor.execute(sql)
        challenges = dictfetchall(cursor)

        self.stdout.write('Processing challenges...')

        for c in challenges:

            #add urls to urlset files
            challenge_info_url = ET.SubElement(challenge_info_urlset, "url")
            ET.SubElement(challenge_info_url, "loc").text = "https://peakery.com/challenges/%s/" % c['slug']
            ET.SubElement(challenge_info_url, "lastmod").text = lastmod

        #close out the challenges files
        #write challenge info sitemap
        tree = ET.ElementTree(challenge_info_urlset)
        xml_file = default_storage.open(challenge_info_filename, "w")
        tree.write(xml_file, encoding='utf-8', xml_declaration=True, pretty_print=True)
        xml_file.close()

        ###########################
        ##  Members Pages        ##
        ###########################

        #init urlset files
        member_info_urlset = ET.Element("urlset", xmlns="http://www.sitemaps.org/schemas/sitemap/0.9")
        member_info_filename = "/sitemap/sitemap-member_info-1.xml"
        sitemap = ET.SubElement(sitemapindex, "sitemap")
        ET.SubElement(sitemap, "loc").text = "https://peakery.com/sitemap-member_info-1.xml"
        ET.SubElement(sitemap, "lastmod").text = lastmod

        #get members
        sql = "select a.id, a.username from auth_user a order by a.id "

        cursor = connection.cursor()
        cursor.execute(sql)
        members = dictfetchall(cursor)

        self.stdout.write('Processing members...')

        for m in members:

            #add urls to urlset files
            member_info_url = ET.SubElement(member_info_urlset, "url")
            ET.SubElement(member_info_url, "loc").text = "https://peakery.com/members/%s/" % m['username']
            ET.SubElement(member_info_url, "lastmod").text = lastmod

        #close out the members files
        #write member info sitemap
        tree = ET.ElementTree(member_info_urlset)
        xml_file = default_storage.open(member_info_filename, "w")
        tree.write(xml_file, encoding='utf-8', xml_declaration=True, pretty_print=True)
        xml_file.close()

        ###########################
        ##  Miscellaneous Pages  ##
        ###########################

        self.stdout.write('Processing misc pages...')

        #init urlset files
        misc_urlset = ET.Element("urlset", xmlns="http://www.sitemaps.org/schemas/sitemap/0.9")
        misc_filename = "/sitemap/sitemap-misc-1.xml"
        sitemap = ET.SubElement(sitemapindex, "sitemap")
        ET.SubElement(sitemap, "loc").text = "https://peakery.com/sitemap-misc-1.xml"
        ET.SubElement(sitemap, "lastmod").text = lastmod

        #add urls to urlset files
        misc_url = ET.SubElement(misc_urlset, "url")
        ET.SubElement(misc_url, "loc").text = "https://peakery.com/world-mountains/"
        ET.SubElement(misc_url, "lastmod").text = lastmod

        misc_url = ET.SubElement(misc_urlset, "url")
        ET.SubElement(misc_url, "loc").text = "https://peakery.com/map/"
        ET.SubElement(misc_url, "lastmod").text = lastmod

        misc_url = ET.SubElement(misc_urlset, "url")
        ET.SubElement(misc_url, "loc").text = "https://peakery.com/challenges/"
        ET.SubElement(misc_url, "lastmod").text = lastmod

        misc_url = ET.SubElement(misc_urlset, "url")
        ET.SubElement(misc_url, "loc").text = "https://peakery.com/members/"
        ET.SubElement(misc_url, "lastmod").text = lastmod

        misc_url = ET.SubElement(misc_urlset, "url")
        ET.SubElement(misc_url, "loc").text = "https://peakery.com/about/"
        ET.SubElement(misc_url, "lastmod").text = lastmod

        misc_url = ET.SubElement(misc_urlset, "url")
        ET.SubElement(misc_url, "loc").text = "https://peakery.com/top-contributors/"
        ET.SubElement(misc_url, "lastmod").text = lastmod

        #close out the misc files
        #write misc sitemap
        tree = ET.ElementTree(misc_urlset)
        xml_file = default_storage.open(misc_filename, "w")
        tree.write(xml_file, encoding='utf-8', xml_declaration=True, pretty_print=True)
        xml_file.close()

        ###########################
        ##  Sitemap Index File   ##
        ###########################

        #close out the sitemap index file
        tree = ET.ElementTree(sitemapindex)
        xml_file = default_storage.open('/sitemap/sitemap_index.xml', "w")
        tree.write(xml_file, encoding='utf-8', xml_declaration=True, pretty_print=True)
        xml_file.close()

        self.stdout.write('Creation of sitemaps complete.')