# Generated by Django 4.2.11 on 2025-02-05 10:24

import datetime
from django.db import migrations, models
import peakery.django_extensions.db.fields
import peakery.main.models


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0008_mapboxthumbnail_external_unique_id'),
    ]

    operations = [
        migrations.CreateModel(
            name='MapboxThumbnailPending',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('gpx_polyline', models.TextField(blank=True, null=True)),
                ('gpx_path', models.TextField(blank=True, null=True)),
                ('lat', models.FloatField(blank=True, db_index=True, null=True, verbose_name='Latitude')),
                ('lng', models.FloatField(blank=True, db_index=True, null=True, verbose_name='Longitude')),
                ('has_marker', models.BooleanField(default=True)),
                ('width', models.IntegerField(default=1260)),
                ('height', models.IntegerField(default=945)),
                ('zoom', models.IntegerField(blank=True, null=True)),
                ('hi_res', models.BooleanField(default=True)),
                ('external_unique_id', models.TextField(blank=True, db_index=True, null=True)),
                ('function', models.TextField()),
                ('created', peakery.django_extensions.db.fields.CreationDateTimeField(blank=True, default=datetime.datetime.now, editable=False)),
                ('modified', peakery.django_extensions.db.fields.ModificationDateTimeField(blank=True, default=datetime.datetime.now, editable=False)),
            ],
            options={
                'indexes': [models.Index(peakery.main.models.MD5('gpx_polyline'), name='gpx_polyline_md5_idx2')],
            },
        ),
    ]
