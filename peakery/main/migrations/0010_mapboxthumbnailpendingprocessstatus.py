# Generated by Django 4.2.11 on 2025-02-07 09:24

import datetime
from django.db import migrations, models
import peakery.django_extensions.db.fields


class Migration(migrations.Migration):

    dependencies = [
        ('main', '0009_mapboxthumbnailpending'),
    ]

    operations = [
        migrations.CreateModel(
            name='MapboxThumbnailPendingProcessStatus',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('running', models.BooleanField(default=False)),
                ('created', peakery.django_extensions.db.fields.CreationDateTimeField(blank=True, default=datetime.datetime.now, editable=False)),
                ('modified', peakery.django_extensions.db.fields.ModificationDateTimeField(blank=True, default=datetime.datetime.now, editable=False)),
            ],
        ),
    ]
