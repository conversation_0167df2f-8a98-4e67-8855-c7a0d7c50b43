from django.db import models
from django.db.models import Index, Func
from peakery.django_extensions.db.fields import CreationDateTime<PERSON>ield, ModificationDateTimeField
from django.utils.timezone import localtime

class MD5(Func):
    function = 'md5'
    arity = 1

class HomepageBackground(models.Model):
    from peakery.items.models import SummitLog, ItemPhoto
    summit_log = models.ForeignKey(SummitLog, null = True, blank = True, related_name='background', on_delete=models.DO_NOTHING)
    summit_log_user_photo = models.ForeignKey(ItemPhoto, null = True, blank = True, on_delete=models.DO_NOTHING)


class UsefulLink(models.Model):
    name = models.CharField(max_length=255)
    link = models.URLField(null=True, blank=True)


# https://github.com/peakery/peakery-web2/issues/379
class MapboxThumbnail(models.Model):
    gpx_polyline = models.TextField(null=True, blank=True)
    lat = models.FloatField(blank=True, null=True, verbose_name='Latitude', db_index=True)
    lng = models.FloatField(blank=True, null=True, verbose_name='Longitude', db_index=True)
    s3link = models.TextField(null=False, blank=False, max_length=2048)
    has_marker = models.BooleanField(default=True)
    width = models.IntegerField(default=1260)
    height = models.IntegerField(default=945)
    zoom = models.IntegerField(blank=True, null=True)
    hi_res = models.BooleanField(default=True)
    external_unique_id = models.TextField(null=True, blank=True, db_index=True)
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()

    class Meta:
        indexes = [
            Index(MD5('gpx_polyline'), name='gpx_polyline_md5_idx')
        ]


class MapboxThumbnailPending(models.Model):
    gpx_polyline = models.TextField(null=True, blank=True)
    gpx_path = models.TextField(null=True, blank=True)
    lat = models.FloatField(blank=True, null=True, verbose_name='Latitude', db_index=True)
    lng = models.FloatField(blank=True, null=True, verbose_name='Longitude', db_index=True)
    has_marker = models.BooleanField(default=True)
    width = models.IntegerField(default=1260)
    height = models.IntegerField(default=945)
    zoom = models.IntegerField(blank=True, null=True)
    hi_res = models.BooleanField(default=True)
    external_unique_id = models.TextField(null=True, blank=True, db_index=True)
    function = models.TextField(null=False, blank=False)
    country_codes = models.TextField(null=True, blank=True)
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()

    class Meta:
        indexes = [
            Index(MD5('gpx_polyline'), name='gpx_polyline_md5_idx2')
        ]

    def __str__(self):
        return (
            f"ThumbnailPending("
            f"id={self.id}, gpx_polyline='{self.gpx_polyline}', gpx_path='{self.gpx_path}', "
            f"lat={self.lat}, lng={self.lng}, has_marker={self.has_marker}, "
            f"width={self.width}, height={self.height}, zoom={self.zoom}, hi_res={self.hi_res}, "
            f"external_unique_id='{self.external_unique_id}', function='{self.function}', "
            f"country_codes='{self.country_codes}', "
            f"created={localtime(self.created)}, modified={localtime(self.modified)})"
        )


# Table used as a lock so that only one async process is executed
class MapboxThumbnailPendingProcessStatus(models.Model):
    running = models.BooleanField(default=False)
    created = CreationDateTimeField()
    modified = ModificationDateTimeField()

