from django.core.cache import cache
from django.http import HttpResponseNotFound, HttpResponse
import requests
from django.views.decorators.cache import cache_page

from peakery.main.models import *
from django.shortcuts import render
from django.contrib.gis.geoip2 import GeoIP2
from peakery.cities.models import Country, City, Region
from django.template.loader import render_to_string
from django.db import connection
from ipware.ip import get_client_ip


def get_object_or_None(model, *args, **kwargs):
    try:
        return model.objects.get(*args, **kwargs)
    except model.DoesNotExist:
        return None

def dictfetchall(cursor):
    "Return all rows from a cursor as a dict"
    columns = [col[0] for col in cursor.description]
    return [
        dict(zip(columns, row))
        for row in cursor.fetchall()
    ]

def sitemap_index(request):
    from django.core.files.storage import default_storage
    from django.http import HttpResponse
    upload_path = "/sitemap/sitemap_index.xml"
    f = default_storage.open(upload_path, 'r')
    response = HttpResponse(f.read())
    f.close()
    response['Content-Type'] = 'application/xml'
    response['Content-Encoding'] = 'UTF-8'
    return response


def sitemap_section(request, section, page):
    from django.core.files.storage import default_storage
    from django.http import HttpResponse
    upload_path = "/sitemap/sitemap-%s-%s.xml" % (section, page)
    try:
        f = default_storage.open(upload_path, 'r')
        response = HttpResponse(f.read())
        f.close()
    except FileNotFoundError:
        print(upload_path, "not found")
        return HttpResponseNotFound(upload_path + " not found")
    response['Content-Type'] = 'application/xml'
    response['Content-Encoding'] = 'UTF-8'
    return response


def summits(request):
    nav_summits_style = 'color: #00B1F2;'
    nav_page_name = 'Summits'

    ip = ''
    country = None
    region = None
    country_code = None
    load_worldwide = False
    #not loading the world?
    if 'suggest_worldwide' in request.COOKIES:
        if request.COOKIES['suggest_worldwide'] == 'true':
            load_worldwide = True

    if not load_worldwide:
        #have cookie?
        if 'suggest_country_id' in request.COOKIES:
            if request.COOKIES['suggest_country_id'] != '':
                country = get_object_or_None(Country, id=request.COOKIES['suggest_country_id'])
                country_code = country.code
        if 'suggest_region_id' in request.COOKIES:
            if request.COOKIES['suggest_region_id'] != '':
                region = get_object_or_None(Region, id=request.COOKIES['suggest_region_id'])
                country = get_object_or_None(Country, id=region.country_id)
                country_code = country.code
        if country is None and region is None:
            g = GeoIP2()
            ip = get_client_ip(request)

            try:
                country_code = g.country(ip)['country_code']
                country = get_object_or_None(Country, code = country_code)
                ip_region = g.city(ip)['region']
                if country_code == 'US':
                    region = get_object_or_None(Region, code='US.'+ip_region)
            except Exception:
                pass

    return render(request, 'main/summits.html', {
        'nav_summits_style':nav_summits_style,
        'ip':ip,
        'country_code':country_code,
        'country':country,
        'region':region,
        'nav_page_name':nav_page_name
    })


def latest(request):

    if request.user.is_authenticated:
        ip = ''
        country = None
        region = None
        city = None
        country_code = None
        load_worldwide = False
        #not loading the world?
        if 'suggest_worldwide' in request.COOKIES:
            if request.COOKIES['suggest_worldwide'] == 'true':
                load_worldwide = True

        if not load_worldwide:
            #have region, city and/or country cookie?
            if 'latest_suggest_city_id' in request.COOKIES:
                if request.COOKIES['latest_suggest_city_id'] != '':
                    city = get_object_or_None(City, id=request.COOKIES['latest_suggest_city_id'])
            if 'latest_suggest_country_id' in request.COOKIES:
                if request.COOKIES['latest_suggest_country_id'] != '':
                    country = get_object_or_None(Country, id=request.COOKIES['latest_suggest_country_id'])
                    country_code = country.code
            if 'latest_suggest_region_id' in request.COOKIES:
                if request.COOKIES['latest_suggest_region_id'] != '':
                    region = get_object_or_None(Region, id=request.COOKIES['latest_suggest_region_id'])
                    country = get_object_or_None(Country, id=region.country_id)
                    country_code = country.code
            if country is None and region is None and city is None:
                g = GeoIP2()
                ip = get_client_ip(request)

                try:
                    country_code = g.country(ip)['country_code']
                    country = get_object_or_None(Country, code = country_code)
                    ip_region = g.city(ip)['region']
                    if country_code == 'US':
                        region = get_object_or_None(Region, code='US.'+ip_region)
                except Exception:
                    pass

        nav_logo_img = 'navbar-logo-inactive.png'
        nav_page_name = 'Latest'

        #member home page
        return render(request, 'main/member_index.html', {
            'ip':ip,
            'country_code':country_code,
            'country':country,
            'region':region,
            'city': city,
            'nav_logo_img':nav_logo_img,
            'nav_page_name':nav_page_name
        })
    else:
        nav_summits_style = 'color: #00B1F2;'
        nav_page_name = 'Latest'

        ip = ''
        country = None
        region = None
        city = None
        country_code = None
        load_worldwide = False
        #not loading the world?
        if 'suggest_worldwide' in request.COOKIES:
            if request.COOKIES['suggest_worldwide'] == 'true':
                load_worldwide = True

        if not load_worldwide:
            #have cookie?
            if 'latest_suggest_city_id' in request.COOKIES:
                if request.COOKIES['latest_suggest_city_id'] != '':
                    city = get_object_or_None(City, id=request.COOKIES['latest_suggest_city_id'])
            if 'latest_suggest_country_id' in request.COOKIES:
                if request.COOKIES['latest_suggest_country_id'] != '':
                    country = get_object_or_None(Country, id=request.COOKIES['latest_suggest_country_id'])
                    country_code = country.code
            if 'latest_suggest_region_id' in request.COOKIES:
                if request.COOKIES['latest_suggest_region_id'] != '':
                    region = get_object_or_None(Region, id=request.COOKIES['latest_suggest_region_id'])
                    country = get_object_or_None(Country, id=region.country_id)
                    country_code = country.code
            if country is None and region is None and city is None:
                g = GeoIP2()
                ip = get_client_ip(request)

                try:
                    country_code = g.country(ip)['country_code']
                    country = get_object_or_None(Country, code = country_code)
                    ip_region = g.city(ip)['region']
                    if country_code == 'US':
                        region = get_object_or_None(Region, code='US.'+ip_region)
                except Exception:
                    pass

        return render(request, 'main/latest.html', {
            'nav_summits_style':nav_summits_style,
            'ip':ip,
            'country_code':country_code,
            'country':country,
            'region':region,
            'city': city,
            'nav_page_name':nav_page_name
        })

#@cache_page(60 * 60)
def last_five_summits(request):
    sql = "select " + \
        "a.id as peak_id, " + \
        "a.name as peak_name, " + \
        "a.slug_new_text as peak_slug, " + \
        "e.username, " + \
        "get_thumb(a.thumbnail, 1920) as peak_thumbnail_url, " + \
        "c.last_summit_date, " + \
        "max(d.id) as last_summit_id " + \
        "from items_item a " + \
        "join items_summitlog aa on aa.item_id = a.id " + \
        "join (select b.item_id, max(b.date) as last_summit_date from items_summitlog b where b.status = 1 and b.attempt = false group by b.item_id) c on c.item_id = a.id " + \
        "join items_summitlog d on d.item_id = a.id and d.date = c.last_summit_date and d.status = 1 and d.attempt = false " + \
        "join auth_user e on e.id = d.user_id " + \
        "where length(a.thumbnail) > 0 and aa.date > CURRENT_DATE - INTERVAL '7 day' " + \
        "group by a.id, a.name, a.slug_new_text, e.username, peak_thumbnail_url, last_summit_date " + \
        "order by last_summit_date desc limit 5 "

    with connection.cursor() as cursor:

        cursor.execute(sql)

        peaks = dictfetchall(cursor)

    list = []
    for p in peaks:
        list.append(p)

    return render(request, 'main/includes/last_five_summits.html', {
            'peaks':list
        }, content_type='text/plain')

#@cache_page(60 * 60)
def new_last_five_summits(request):
    sql = "select " + \
        "a.id as peak_id, " + \
        "a.name as peak_name, " + \
        "a.slug_new_text as peak_slug, " + \
        "e.username, " + \
        "get_thumb(a.thumbnail, 1920) as peak_thumbnail_url, " + \
        "c.last_summit_date, " + \
        "max(d.id) as last_summit_id " + \
        "from items_item a " + \
        "join items_summitlog aa on aa.item_id = a.id " + \
        "join (select b.item_id, max(b.date) as last_summit_date from items_summitlog b where b.status = 1 and b.attempt = false group by b.item_id) c on c.item_id = a.id " + \
        "join items_summitlog d on d.item_id = a.id and d.date = c.last_summit_date and d.status = 1 and d.attempt = false " + \
        "join auth_user e on e.id = d.user_id " + \
        "where length(a.thumbnail) > 0 and aa.date > CURRENT_DATE - INTERVAL '30 day' " + \
        "group by a.id, a.name, a.slug_new_text, e.username, peak_thumbnail_url, last_summit_date " + \
        "order by last_summit_date desc limit 5 "

    with connection.cursor() as cursor:

        cursor.execute(sql)

        peaks = dictfetchall(cursor)

    list = []
    for p in peaks:
        list.append(p)

    return render(request, 'main/includes/new_last_five_summits.html', {
            'peaks':list
        }, content_type='text/plain')


# TODO This is dead code, could probably be removed
def index(request):
    if request.user.is_authenticated:

        ip = ''
        country = None
        region = None
        country_code = None
        load_worldwide = False
        #not loading the world?
        if 'suggest_worldwide' in request.COOKIES:
            if request.COOKIES['suggest_worldwide'] == 'true':
                load_worldwide = True

        if not load_worldwide:
            #have region and/or country cookie?
            if 'suggest_country_id' in request.COOKIES:
                if request.COOKIES['suggest_country_id'] != '':
                    country = get_object_or_None(Country, id=request.COOKIES['suggest_country_id'])
                    country_code = country.code
            if 'suggest_region_id' in request.COOKIES:
                if request.COOKIES['suggest_region_id'] != '':
                    region = get_object_or_None(Region, id=request.COOKIES['suggest_region_id'])
                    country = get_object_or_None(Country, id=region.country_id)
                    country_code = country.code
            if country is None and region is None:
                g = GeoIP2()
                ip = get_client_ip(request)

                try:
                    country_code = g.country(ip)['country_code']
                    country = get_object_or_None(Country, code = country_code)
                    ip_region = g.city(ip)['region']
                    if country_code == 'US':
                        region = get_object_or_None(Region, code='US.'+ip_region)
                except Exception:
                    pass

        nav_logo_img = 'navbar-logo-inactive.png'
        nav_page_name = 'Latest'

        #member home page
        return render(request, 'main/member_index.html', {
            'ip':ip,
            'country_code':country_code,
            'country':country,
            'region':region,
            'nav_logo_img':nav_logo_img,
            'nav_page_name':nav_page_name
        })
    else:
        nav_logo_img = 'navbar-logo-inactive.png'

        last_five_summits_response = last_five_summits(request)

        peak_count = cache.get('site_stats_peak_count')

        if not peak_count:

            sql = "select count(a.id) as peak_count from items_item a "

            with connection.cursor() as cursor:

                cursor.execute(sql)

                count = dictfetchall(cursor)
            peaks = count[0]

            peak_count = peaks['peak_count']

            cache.set('site_stats_peak_count', peak_count, 60 * 60)

        summit_count = cache.get('site_stats_summit_count')

        if not summit_count:

            sql = "select count(a.id) as summit_count from items_summitlog a where a.status = 1 and a.attempt = false "

            with connection.cursor() as cursor:

                cursor.execute(sql)

                count = dictfetchall(cursor)
            summits = count[0]

            summit_count = summits['summit_count']

            cache.set('site_stats_summit_count', summit_count, 60 * 60)

        user_count = cache.get('site_stats_user_count')

        if not user_count:

            sql = "select count(a.id) as user_count from auth_user a "

            with connection.cursor() as cursor:

                cursor.execute(sql)

                count = dictfetchall(cursor)
            users = count[0]

            user_count = users['user_count']

            cache.set('site_stats_user_count', user_count, 60 * 60)

        return render(request, 'main/index.html', {
            'nav_logo_img':nav_logo_img,
            'last_five_summits':last_five_summits_response.content,
            'fixed_subnav_class':'fixed-subnav-header',
            'peak_count':peak_count,
            'summit_count':summit_count,
            'user_count':user_count
        })

def new_index(request):
    from django.contrib.gis.geoip2 import GeoIP2

    # get last five summits
    sql = "select " + \
          "a.id as peak_id, " + \
          "a.name as peak_name, " + \
          "a.slug_new_text as peak_slug, " + \
          "e.username, " + \
          "get_thumb(a.thumbnail, 1920) as peak_thumbnail_url, " + \
          "c.last_summit_date, " + \
          "max(d.id) as last_summit_id " + \
          "from items_item a " + \
          "join items_summitlog aa on aa.item_id = a.id " + \
          "join (select b.item_id, max(b.date) as last_summit_date from items_summitlog b where b.status = 1 and b.attempt = false group by b.item_id) c on c.item_id = a.id " + \
          "join items_summitlog d on d.item_id = a.id and d.date = c.last_summit_date and d.status = 1 and d.attempt = false " + \
          "join auth_user e on e.id = d.user_id " + \
          "where length(a.thumbnail) > 0 and aa.date > CURRENT_DATE - INTERVAL '30 day' " + \
          "group by a.id, a.name, a.slug_new_text, e.username, peak_thumbnail_url, last_summit_date " + \
          "order by last_summit_date desc limit 5 "

    with connection.cursor() as cursor:
        cursor.execute(sql)
        peaks = dictfetchall(cursor)

    list = []
    for p in peaks:
        list.append(p)

    if request.user.is_authenticated:

        ip = ''
        country = None
        region = None
        country_code = None
        load_worldwide = False
        #not loading the world?
        if 'suggest_worldwide' in request.COOKIES:
            if request.COOKIES['suggest_worldwide'] == 'true':
                load_worldwide = True

        if not load_worldwide:
            #have region and/or country cookie?
            if 'suggest_country_id' in request.COOKIES:
                if request.COOKIES['suggest_country_id'] != '':
                    country = get_object_or_None(Country, id=request.COOKIES['suggest_country_id'])
                    country_code = country.code
            if 'suggest_region_id' in request.COOKIES:
                if request.COOKIES['suggest_region_id'] != '':
                    region = get_object_or_None(Region, id=request.COOKIES['suggest_region_id'])
                    country = get_object_or_None(Country, id=region.country_id)
                    country_code = country.code
            if country is None and region is None:
                g = GeoIP2()
                ip = get_client_ip(request)

                try:
                    country_code = g.country(ip)['country_code']
                    country = get_object_or_None(Country, code = country_code)
                    ip_region = g.city(ip)['region']
                    if country_code == 'US':
                        region = get_object_or_None(Region, code='US.'+ip_region)
                except Exception:
                    pass

        nav_logo_img = 'navbar-logo-inactive.png'
        nav_page_name = 'Latest'

        #member home page
        return render(request, 'main/member_index.html', {
            'ip':ip,
            'country_code':country_code,
            'country':country,
            'region':region,
            'nav_logo_img':nav_logo_img,
            'nav_page_name':nav_page_name,
            'peaks': list,
            'nav_icon_selected':'home'
        })
    else:
        try:
            lon = -122.52341252476621
            lat = 37.98043037073835
            ip = None
            near_city = None
            g = GeoIP2()
            ip = request.META.get('HTTP_X_FORWARDED_FOR', None)
            #ip = '**************'
            ip_list = ip.split(',')
            real_ip = ip_list[0]
            if real_ip:
                city = g.city(real_ip)
                if city['city']:
                    near_city = city['city']
                lon = g.lon_lat(real_ip)[0]
                lat = g.lon_lat(real_ip)[1]
        except Exception:
            pass

        nav_logo_img = 'navbar-logo-inactive.png'

        last_five_summits_response = new_last_five_summits(request)

        return render(request, 'main/new_index.html', {
            'nav_logo_img':nav_logo_img,
            'last_five_summits':last_five_summits_response.content,
            'fixed_subnav_class':'fixed-subnav-header',
            'lat':lat,
            'lon':lon,
            'near_city':near_city,
            'peaks': list,
            'nav_icon_selected': 'home'
        })

#@cache_page(60 * 60)
def about(request):
    from django.contrib.gis.geoip2 import GeoIP2

    # get last five summits
    sql = "select " + \
          "a.id as peak_id, " + \
          "a.name as peak_name, " + \
          "a.slug_new_text as peak_slug, " + \
          "e.username, " + \
          "get_thumb(a.thumbnail, 1920) as peak_thumbnail_url, " + \
          "c.last_summit_date, " + \
          "max(d.id) as last_summit_id " + \
          "from items_item a " + \
          "join items_summitlog aa on aa.item_id = a.id " + \
          "join (select b.item_id, max(b.date) as last_summit_date from items_summitlog b where b.status = 1 and b.attempt = false group by b.item_id) c on c.item_id = a.id " + \
          "join items_summitlog d on d.item_id = a.id and d.date = c.last_summit_date and d.status = 1 and d.attempt = false " + \
          "join auth_user e on e.id = d.user_id " + \
          "where length(a.thumbnail) > 0 and aa.date > CURRENT_DATE - INTERVAL '30 day' " + \
          "group by a.id, a.name, a.slug_new_text, e.username, peak_thumbnail_url, last_summit_date " + \
          "order by last_summit_date desc limit 5 "

    with connection.cursor() as cursor:

        cursor.execute(sql)

        peaks = dictfetchall(cursor)

    list = []
    for p in peaks:
        list.append(p)

    try:
        lon = -122.52341252476621
        lat = 37.98043037073835
        ip = None
        near_city = None
        g = GeoIP2()
        ip = request.META.get('HTTP_X_FORWARDED_FOR', None)
        # ip = '**************'
        ip_list = ip.split(',')
        real_ip = ip_list[0]
        if real_ip:
            city = g.city(real_ip)
            if city['city']:
                near_city = city['city']
            lon = g.lon_lat(real_ip)[0]
            lat = g.lon_lat(real_ip)[1]
    except Exception:
        pass

    nav_logo_img = 'navbar-logo-inactive.png'

    last_five_summits_response = new_last_five_summits(request)

    return render(request, 'main/new_index.html', {
        'nav_logo_img': nav_logo_img,
        'last_five_summits': last_five_summits_response.content,
        'fixed_subnav_class': 'fixed-subnav-header',
        'lat': lat,
        'lon': lon,
        'near_city': near_city,
        'peaks': list,
        'nav_icon_selected': 'home'
    })


def top_contributors(request):
    return render(request, 'main/view_contributors_list.html', {'nav_page_name': 'Top contributors'})


def login(request):
    if request.user.is_authenticated:
        user = request.user
        #Already have a token?
        if 'jwt' in request.session:
            jwt = request.session['jwt']
        else:
            from jose import jws
            import datetime
            expiry = datetime.date.today() + datetime.timedelta(days=50)
            jwt = jws.sign({'username': user.username}, '7il1oZvu8BnzMtDrrGtXJPZd53VZ57fe', algorithm='HS256')
            request.session['jwt'] = jwt
    else:
        user = None
        jwt = None

    return render(request, 'main/login.html', {'user':user, 'jwt':jwt})

def contest(request):
    return render(request, 'main/contest.html', {})

def homepage_bg(request):
    source = "0"
    try:
        bg = HomepageBackground.objects.get(id=1)
        if bg.summit_log:
            source = bg.summit_log.item.get_big_thumbnail()
        elif bg.summit_log_user_photo:
            source = bg.summit_log_user_photo.image
    except:
        pass
    return render(request, 'main/background.html',{'source':source})

def _get_recent_summited_image():
    source = "0"
    try:
        bg = HomepageBackground.objects.get(id=1)
        if bg.summit_log:
            source = render_to_string("main/background.html", {"source":bg.summit_log.item.get_big_thumbnail()})
        elif bg.summit_log_user_photo:
            source = render_to_string("main/background.html", {"source":bg.summit_log_user_photo.image})
    except:
        pass
    return source


@cache_page(60 * 60)
def mapbox_southafrica_ngi_proxy(request, z, x, y):
    """
    Proxy view for South Africa NGI tiles to bypass CORS restrictions.
    Handles TMS coordinate system (inverted Y) conversion.
    """
    try:
        # Convert TMS Y coordinate to standard Y coordinate
        # TMS uses inverted Y: y_tms = (2^z - 1) - y_standard
        # So: y_standard = (2^z - 1) - y_tms
        z_int = int(z)
        y_int = int(y)
        max_y = (2 ** z_int) - 1
        inverted_y = max_y - y_int

        # Construct the URL for the South Africa NGI tile server
        tile_url = f"https://htonl.dev.openstreetmap.org/ngi-tiles/tiles/50k/{z}/{x}/{inverted_y}.png"

        # Fetch the tile from the remote server
        response = requests.get(tile_url, timeout=10)

        if response.status_code == 200:
            # Return the tile with proper headers
            http_response = HttpResponse(response.content, content_type='image/png')
            return http_response
        else:
            # Return 404 if tile not found
            return HttpResponseNotFound("Tile not found")

    except Exception as e:
        # Return 500 for any other errors
        return HttpResponse(f"Error fetching tile: {str(e)}", status=500)
