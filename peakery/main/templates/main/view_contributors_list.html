{% extends "base.html" %}
{% load static %}
{% load item_tags %}
{% load avatar_tags %}
{% block extrajs %}
{% endblock %}
{% block title %}Top contributors{% endblock %}
{% block titlemeta_overwrite %}Top contributors{% endblock %}
{% block description %}Thanks to these top contributors for making peakery better.{% endblock %}
{% block image_rel %}{% endblock %}

{% block peakbaggers_active %}active{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row hidden-xs">
        <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 65px; border-radius: 12px 12px 0 0;"><span style="font-size: 22px; font-weight: 600;">
            <div id="breadcrumbs">
                <ul>
                    <li><span class="section-title">Top Contributors</span>
                    </li>
                </ul>
                <div class="pull-right" style="margin-top: -10px; font-weight: 300; font-size: 12px; line-height: 70px;">
                    <span class="hidden-xs" style="color: #999;">Thanks to these top contributors for making peakery better. <a class="modal-link" style="font-size: 12px;" data-toggle="modal" data-target="#want-to-help">Want to help?</a></span>
                </div>
            </div>
        </span></div>
    </div>
{% endblock %}

{% block content %}

    <style type="text/css">

        @media screen and (max-width: 767px) and (min-width: 1px) {
           #content-body {
               margin-top: 20px;
           }
           .content-pane {
               margin-top: 50px;
           }
           .mobile-regions-subnav-fixed {
               margin-top: 0px;
           }
           #selectMobileContributionfilter {
               margin-top: 7px;
           }
           .contributions-content {
                padding-left: 10px;
            }
           .peak-list-highlights {
               font-size: 14px;
               line-height: 20px;
               margin-top: 3px;
               margin-bottom: 3px;
           }
           .stats-header {
               font-size: 14px;
           }
           .profile-giant-green {
               font-size: 14px;
               font-weight: 500;
           }
           .contributor-header {
                height: 60px;
                line-height: 60px;
            }
       }
       @media screen and (max-width: 1023px) and (min-width: 768px) {
           .content-pane {
               margin-top: 50px;
           }
           .mobile-regions-subnav-fixed {
               top: 141px;
           }
           #selectMobileContributionfilter {
               margin-top: 7px;
           }
           .peak-list-highlights {
                font-size: 14px;
                line-height: 20px;
                margin-top: 15px;
                margin-bottom: 15px;
            }
           .contributions-content {
                padding-left: 25px;
            }
           .profile-giant-green {
               font-size: 16px;
               font-weight: 500;
           }
           .contributor-header {
                height: 70px;
                line-height: 70px;
            }
       }
        @media screen and (min-width: 1024px) {
            .content-pane {
               margin-top: 50px;
           }
            .regions-subnav-fixed {
                top: 141px;
            }
            .mobile-regions-subnav-fixed {
               margin-top: 0px;
           }
            #selectMobileContributionfilter {
               margin-top: 0px;
           }
            .peak-list-highlights {
                font-size: 16px;
                line-height: 22px;
                margin-bottom: 30px;
                margin-top: 20px;
            }
            .contributions-content {
                padding-left: 25px;
            }
            .profile-giant-green {
               font-size: 18px;
               font-weight: 500;
           }
            .contributor-header {
                height: 70px;
                line-height: 70px;
            }
        }

    </style>

<div class="container">

    <div class="row sub-header-row hidden-xs hidden-sm regions-subnav-fixed" style="height: 50px; padding-right: 0px;">
        <div class="col-md-12" style="height: 50px; line-height: 50px; font-size: 12px; color: #999;">
            <a id="type-all" style="margin-left: 0px;" class="region-header-sub-links ajax-link" onclick="loadContributors('all');">All changes</a><a id="type-info-corrections" style="margin-left: 35px;" class="region-header-sub-links ajax-link" onclick="loadContributors('info_corrections');">Peak info</a><a id="type-peak-photos" style="margin-left: 35px;" class="region-header-sub-links ajax-link" onclick="loadContributors('peak_photos');">Peak photos</a><a id="type-peaks-added" style="margin-left: 35px;" class="region-header-sub-links ajax-link" onclick="loadContributors('peaks_added');">New peaks</a><a id="type-routes-added" style="margin-left: 35px;" class="region-header-sub-links ajax-link" onclick="loadContributors('routes_added');">Routes</a>
        </div>
    </div>

    <div class="row sub-header-row hidden-md hidden-lg mobile-regions-subnav-fixed" style="height: 50px; position: fixed; z-index: 999; width: 100%;">
        <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12" style="height: 50px;line-height: 26px;">
            <div style="float: left; width: 100%; height: 100%;">
                <div class="select" id="selectMobileContributionfilter">
                    <button class="btn btn-default contribution-filter-button" style="text-align: left; width: 95%; border: none; padding-top: 7px; padding-bottom: 7px; margin-left: 1px; color: #00b1f2; font-size: 12px; padding-left: 5px; padding-right: 5px; background-color: #f2f2f2;" data-toggle="dropdown" aria-expanded="false">
                        <span id="mobile-contributionfilter-title">All changes</span>
                        <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu contributionfilter-list" style="cursor: pointer; height: auto; left: 5px; overflow: auto; top: 50px;">
                        <li style="float: none; height: 30px; margin: 10px 0 0;"><a class="routefilter-item" data-value="all" href="#">All changes</a></li>
                        <li style="float: none; height: 30px; margin: 10px 0 0;"><a class="routefilter-item" data-value="info_corrections" href="#">Peak info</a></li>
                        <li style="float: none; height: 30px; margin: 10px 0 0;"><a class="routefilter-item" data-value="peak_photos" href="#">Peak photos</a></li>
                        <li style="float: none; height: 30px; margin: 10px 0 0;"><a class="routefilter-item" data-value="peaks_added" href="#">New peaks</a></li>
                        <li style="float: none; height: 30px; margin: 10px 0 0;"><a class="routefilter-item" data-value="routes_added" href="#">Routes</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="row content-pane">
        <div class="col-md-12" style="background-color:#333;">
            <div class="row" id="ajax-data-loading" style="display: none;">
              <div class="col-md-12 data-loading" style="text-align: center; color: #666;">
                <i class="fa fa-spinner fa-spin fa-5x"></i>
              </div>
            </div>
            <div class="row" id="contributors-list"></div>
        </div>
    </div>

    <div class="row dark-background-row hidden-lg hidden-md hidden-sm">
        <div class="row">
            <div style="height: 178px;"></div>
        </div>
    </div>
    <div class="row dark-background-row hidden-xs">
        <div class="row">
            <div style="height: 53px;"></div>
        </div>
    </div>

</div>

<div class="want-to-help-modal modal fade" id="want-to-help" tabindex="-1" role="dialog" aria-labelledby="want-to-help-label">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
              <button type="button" class="close modal-close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
              <h4 class="modal-title" id="want-to-help-label">peakery needs your help!</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12" style="margin-bottom: 20px; font-size: 15px;">
                        <p style="line-height: 20px;">peakery’s long-term vision is to be a comprehensive mountain climbing resource for every peak in the world. Your help is critical to make this happen. Some important ways you can contribute:</p>
                        <div style="margin-left: 20px; margin-right: 20px;">
                            <ul>
                            <li style="list-style: initial; line-height: 20px; margin-bottom: 20px;"><span style="color: #f24100;">Add missing peaks:</span> the goal is to include every mountain in the world. If you know a peak that’s missing, on the <a href="/peaks/">Peaks</a> or <a href="/map/">Map</a> pages, click ‘Can’t find a peak? Add it!’.</li>
                            <li style="list-style: initial; line-height: 20px; margin-bottom: 20px;"><span style="color: #f24100;">Fix peak info:</span> fix errors and fill in missing pieces of info for peaks. Just go to a peak’s page and click ‘edit peak info’</li>
                            <li style="list-style: initial; line-height: 20px; margin-bottom: 20px;"><span style="color: #f24100;">Add Highlights:</span> Highlights are 1-2 sentence snippets about the most remarkable things about a peak, region, or Peak Challenge. You can add them on those pages by clicking ‘add highlight’</li>
                            <li style="list-style: initial; line-height: 20px; margin-bottom: 20px;"><span style="color: #f24100;">Add peak photos:</span> the goal is to feature at least one photo of every peak. Go to a peak’s page and click ‘Add a peak photo’.</li>
                            <li style="list-style: initial; line-height: 20px; margin-bottom: 20px;"><span style="color: #f24100;">Add routes:</span> peakery aims to give you all the info you need to go out and climb a mountain. Critical to this is great route info. You can add a new route or more info to an existing route on any peak’s Routes page. Route GPS tracks are highly desired!</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">

    var contribution_type = 'all';

    function loadContributors(type) {

        contribution_type = type;

        window.scrollTo(0, 0);

        switch(type) {
            case 'all':
                $('#type-all').css('color', '#F24100');
                $('#type-all').css('font-weight', '500');
                $('#type-info-corrections').css('color', '#999');
                $('#type-info-corrections').css('font-weight', '300');
                $('#type-peak-photos').css('color', '#999');
                $('#type-peak-photos').css('font-weight', '300');
                $('#type-peaks-added').css('color', '#999');
                $('#type-peaks-added').css('font-weight', '300');
                $('#type-routes-added').css('color', '#999');
                $('#type-routes-added').css('font-weight', '300');
                $("#mobile-contributionfilter-title").html('All changes');
                break;
            case 'info_corrections':
                $('#type-all').css('color', '#999');
                $('#type-all').css('font-weight', '300');
                $('#type-info-corrections').css('color', '#F24100');
                $('#type-info-corrections').css('font-weight', '500');
                $('#type-peak-photos').css('color', '#999');
                $('#type-peak-photos').css('font-weight', '300');
                $('#type-peaks-added').css('color', '#999');
                $('#type-peaks-added').css('font-weight', '300');
                $('#type-routes-added').css('color', '#999');
                $('#type-routes-added').css('font-weight', '300');
                $("#mobile-contributionfilter-title").html('Peak info corrections');
                break;
            case 'peak_photos':
                $('#type-all').css('color', '#999');
                $('#type-all').css('font-weight', '300');
                $('#type-info-corrections').css('color', '#999');
                $('#type-info-corrections').css('font-weight', '300');
                $('#type-peak-photos').css('color', '#F24100');
                $('#type-peak-photos').css('font-weight', '500');
                $('#type-peaks-added').css('color', '#999');
                $('#type-peaks-added').css('font-weight', '300');
                $('#type-routes-added').css('color', '#999');
                $('#type-routes-added').css('font-weight', '300');
                $("#mobile-contributionfilter-title").html('Peak photos added');
                break;
            case 'peaks_added':
                $('#type-all').css('color', '#999');
                $('#type-all').css('font-weight', '300');
                $('#type-info-corrections').css('color', '#999');
                $('#type-info-corrections').css('font-weight', '300');
                $('#type-peak-photos').css('color', '#999');
                $('#type-peak-photos').css('font-weight', '300');
                $('#type-peaks-added').css('color', '#F24100');
                $('#type-peaks-added').css('font-weight', '500');
                $('#type-routes-added').css('color', '#999');
                $('#type-routes-added').css('font-weight', '300');
                $("#mobile-contributionfilter-title").html('Missing peaks added');
                break;
            case 'routes_added':
                $('#type-all').css('color', '#999');
                $('#type-all').css('font-weight', '300');
                $('#type-info-corrections').css('color', '#999');
                $('#type-info-corrections').css('font-weight', '300');
                $('#type-peak-photos').css('color', '#999');
                $('#type-peak-photos').css('font-weight', '300');
                $('#type-peaks-added').css('color', '#999');
                $('#type-peaks-added').css('font-weight', '300');
                $('#type-routes-added').css('color', '#F24100');
                $('#type-routes-added').css('font-weight', '500');
                $("#mobile-contributionfilter-title").html('Routes added');
                break;
            default:
                $('#type-all').css('color', '#F24100');
                $('#type-all').css('font-weight', '500');
                $('#type-info-corrections').css('color', '#999');
                $('#type-info-corrections').css('font-weight', '300');
                $('#type-peak-photos').css('color', '#999');
                $('#type-peak-photos').css('font-weight', '300');
                $('#type-peaks-added').css('color', '#999');
                $('#type-peaks-added').css('font-weight', '300');
                $('#type-routes-added').css('color', '#999');
                $('#type-routes-added').css('font-weight', '300');
                $("#mobile-contributionfilter-title").html('All changes');
        }

        window.location.hash = 'type='+type;

        $('#contributors-list').empty();
        $('#ajax-data-loading').css('display', 'inline');
        $.getJSON('{% url "contributors_list" %}?type='+type , function(data) {
            $.each( data, function( key, val ) {
                if (key=='contributors') {
                    $('#ajax-data-loading').css('display', 'none');
                    var contributor_index = 1;
                    var dividerDiv = '';
                    var contribClass = '';
                    $.each( val, function( contributorkey, contributorval ) {

                        var contributions_html = '';
                        var contribution_index = 1;
                        //build latest contributions
                        $.each( contributorval.contributions, function( contributionskey, contributionsval ) {

                            //build country string
                            var country = '';
                            var country_slug = '';
                            $.each( contributionsval.country, function( countrykey, countryval ) {
                                if (country == '') {
                                    country = '<a href="/region/' + countryval.country_slug + '-mountains">' + countryval.country_name + '</a>';
                                }
                            });
                            if (country == '') {
                                country = 'Unknown Country';
                            }

                            //build region string
                            var region = '';
                            $.each( contributionsval.region, function( regionkey, regionval ) {
                                if (region == '') {
                                    region = '<a href="/' + regionval.country_slug + '-mountains/' + regionval.region_slug + '/">' + regionval.region_name + ', ' + regionval.country_name + '</a>';
                                }
                            });
                            if (region == '') {
                                region = country;
                            }

                            dividerDiv = '';
                            if (contributor_index > 1) {
                                dividerDiv = '<div class="row dark-background-row"><div style="height: 60px;"></div></div>';
                            }

                            contribClass = '';
                            if (contribution_index > 1) {
                                contribClass = 'hidden-xs';
                            }

                            contributions_html = contributions_html + '<div class="peak-list-highlights contribution-item ' + contribClass + '" style="color: #333;"><span class="hidden-xs">&bull;&nbsp;</span>' + contributionsval.contrib_type + ' <a href="/' + contributionsval.peak_slug + '">' + contributionsval.peak_name + '</a><span class="hidden-xs"> in ' + region + '</span></div>';
                            contribution_index++;

                        });

                        $('#contributors-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">' + dividerDiv + '<div class="row"><div class="col-md-12 contributor-header" style="background-color: #fff;"><div><p class="contributor-header"><span class="memberNumberCircle"><span>' + contributor_index + '</span></span><span class="stats-header" style="margin-left: 10px;"><a style="color: #333;" href="/members/' + contributorval.username + '">' + contributorval.username + '</a></span><span>&nbsp;&nbsp;&nbsp;</span><span class="summit-list-stats pull-right" style="color: #666;"><span class="profile-giant-green">' + contributorval.contributions_count + '</span><span class="peak-list-highlights" style="margin-left: 5px; color: #999;">changes</span></span></div></div></div>');
                        $('#contributors-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"><div class="row"><div class="col-lg-3 col-md-3 col-sm-4 col-xs-4 hover-photos" style="padding-right: 0px; padding-left: 0px; background-image: url(\'' + contributorval.avatar_url + '\'); background-size: cover; overflow: hidden;"><a style="color: #000000;" href="/members/' + contributorval.username + '"><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive leftthird-responsive"></a></div><div class="col-lg-9 col-md-9 col-sm-8 col-xs-8 map-card-web-1 map-card-tablet-1 peakimg-responsive leftthird-responsive contributions-content" style="border-top: solid 1px #e0e0e0 !important; background-color: #fff;"><div class="peakimg-responsive leftthird-responsive" style="display: table-cell; vertical-align: middle;">' + contributions_html + '</div></div></div></div>');
                        contributor_index++;
                    });
                }
            });
        });

    }

    $(document).ready(function() {

        var viewport_height = $(window).height();
        var viewport_width = $(window).width();
        var footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
        var data_loading_margin = (viewport_height - footer_height) / 3;
        $('.data-loading').css('margin-top', data_loading_margin);
        $(window).resize(function() {
            viewport_height = $(window).height();
            viewport_width = $(window).width();
            footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
            data_loading_margin = (viewport_height - footer_height) / 3;
            $('.data-loading').css('margin-top', data_loading_margin);
        });

        var viewport_height = $(window).height();
        var footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
        var data_loading_margin = (viewport_height - footer_height) / 2;
        $('.data-loading').css('margin-top', data_loading_margin);
        $('.data-loading').css('margin-bottom', data_loading_margin);
        $(window).resize(function() {
            viewport_height = $(window).height();
            footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
            data_loading_margin = (viewport_height - footer_height) / 2;
            $('.data-loading').css('margin-top', data_loading_margin);
            $('.data-loading').css('margin-bottom', data_loading_margin);
        });

        $('.routefilter-item').on('click', function(e) {

            e.preventDefault();
            var contribution = $(this).html();
            $("#mobile-contributionfilter-title").html(contribution);
            contribution_type = $(this).data('value');
            loadContributors(contribution_type);

        });

        var vars = [], hash, type;
        var q = document.URL.split('#')[1];
        if(q != undefined){
            q = q.split('&');
            for(var i = 0; i < q.length; i++){
                hash = q[i].split('=');
                vars.push(hash[1]);
                vars[hash[0]] = hash[1];
            }
        }

        if (vars['type'] != undefined) {
            type = vars['type'];
        } else {
            type = 'all';
        }

        loadContributors(type);

    });

</script>

{% endblock %}

