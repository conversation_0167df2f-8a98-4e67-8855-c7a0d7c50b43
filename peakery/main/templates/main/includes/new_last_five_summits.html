<!-- CONTENT BEGIN -->
<script type="text/javascript">

    var summitLinks = [];

    $(document).ready(function() {

        {% for p in peaks %}
            summitLinks[{{ forloop.counter0 }}] = '/{{ p.peak_slug }}/';
        {% endfor %}

        $('#btnGooglePlay').on('click', function(e) {
            window.location.href = 'https://play.google.com/store/apps/details?id=com.peakery.android&utm_source=peakery_web&pcampaignid=pcampaignidMKT-Other-global-all-co-prtnr-py-PartBadge-Mar2515-1';
            e.stopPropagation();
        });

    });

</script>

<!-- Carousel
================================================== -->
<div id="main-carousel" class="carousel slide" data-ride="carousel">
    <div class="carousel-inner" role="listbox" style="margin: 0 auto;">
        <div id="new-home-slideshow" class="item active" style="cursor: pointer;">
            <div id="slideshow" class="width: 100%; height: 100%;">
            {% for p in peaks %}
            <div onclick="openUrl('/{{ p.peak_slug }}/');" style="width: 100%; height: 100%; cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url('{{ MEDIA_URL }}{{ p.peak_thumbnail_url }}'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;">
                <div id="main-caption-info{{ forloop.counter }}" class="carousel-info">
                    <a href="/{{ p.peak_slug }}/" style="color: white; font-weight: bold; text-decoration: none;">{{ p.peak_name }}</a>
                </div>
            </div>
            {% endfor %}
            </div>
            <div class="container">
                <div id="main-caption" class="carousel-caption" style="cursor: pointer;">
                    <div id="climb-that-mountain-link" style="margin-top: -15px;">
                        <div id="climb-that-mountain-link-text">Explore peaks near<span class="hidden-sm hidden-md hidden-lg"> you &#8250;</span><span class="hidden-xs">...</span></div>
                        <div class="hidden-xs" id="explore-peaks-search">
                            <input id="explore-peaks" type="text" style="cursor: pointer; margin-top: 10px; height: 70px; width: 560px; color: #333;">
                            <div id="searchbox-icon-div" style="display: none; position: relative; top: -2px;">
                            <a id="searchbox-icon" class="searchbox-icon navbar-search" style="top: -40px; right: 40px;"><img src="https://peakery-static.s3.amazonaws.com/img/times-circle-solid.png" style="width: 20px;"></img></a>
                            </div>
                            <div id="searchbox-text-div" style="position: absolute; top: 85px; left: 40px;">
                            <a id="searchbox-text" class="searchbox-text navbar-search" style="top: -40px; right: 40px;"><img src="https://peakery-static.s3.amazonaws.com/img/search-solid.png" style="width: 20px; margin-right: 10px;"></img>Search for a location</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div><!-- /.carousel -->