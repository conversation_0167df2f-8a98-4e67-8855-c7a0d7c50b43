{% extends "base.html" %}
{% load static %}
{% load item_tags %}
{% load avatar_tags %}
{% block extrajs %}
<script src="{% static 'js/jquery.cycle.lite.js'%}"></script>
<script type="text/javascript">
{% include "mapbox/polyline.js" %}
</script>
<script src="{% static 'js/GPXParser.js'%}"></script>
{% endblock %}
{% block title %}Latest climbs in {% if region %}{{ region.name }}{% if country %}, {{ country.name }}{% endif %}{% elif country %}{{ country.name }}{% else %}the World{% endif %}{% endblock %}
{% block titlemeta_overwrite %}Latest mountain climbs in {% if region %}{{ region.name }}{% if country %}, {{ country.name }}{% endif %}{% elif country %}{{ country.name }}{% else %}the World{% endif %}{% endblock %}
{% block description %}Your basecamp for the world’s mountains. Find great peaks to climb, tackle Peak Challenges, track your summits, and follow the mountain adventures of friends.{% endblock %}
{% block image_rel %}{% endblock %}

{% block peakbaggers_active %}active{% endblock %}

{% block fixed_page_header %}
    <div class="row main-header-row fixed-page-header subnav">
        <div class="col-md-12" style="height: 70px; background-color: #fff; line-height: 70px; font-weight: 500;border-top-left-radius:12px;border-top-right-radius:12px;"><span style="font-size: 24px; font-weight: 600;">
            <div id="breadcrumbs">
                <ul>
                    <li class="section-title-li" style="margin-top: 0px !important; width: 100%;">
                        <div class="section-title latest-in hidden-xs" style="font-weight: 500; float: left;">Latest in</div>
                        <div class="pull-right" style="font-size: 24px; margin-top: 25px; margin-right: 0px;">
                            <div id="mobile-see-worldwide" class="hidden-sm hidden-md hidden-lg" style="display: none;"></div>
                        </div>
                        <div id="region-pushdown" style="overflow: hidden; height: 70px;">
                            <button id="region-filter-button" class="btn btn-default summits-filter-button">
                                <span id="region-title" class="section-title ellipsis" style="color: #00B1F2; font-weight: 500; width: 100%; overflow: hidden; display: inline-block;"><span class="hidden-xs">the</span><span class="hidden-sm hidden-md hidden-lg">The</span> World</span>
                                <div id="region-title-caret"><i class="fa fa-angle-down"></i></div>
                            </button>
                            <div class="hidden-xs" id="see-worldwide" style="margin-left: 10px; font-size: 14px; font-weight: 500; letter-spacing: 1.0px; position: absolute;"></div>
                            <form method="post" id="region-filter-form" class="region-searchbox" style="width: 100%;">
                                <input type="hidden" id="hdnCountryId" value="">
                                <input type="hidden" id="hdnRegionId" value="">
                                <input type="hidden" id="hdnCityId" value="">
                                <input type="hidden" id="hdnPage" value="2">
                                <input style="border: none; font-weight: 500 !important; display: none; font-size:20px;" type="search" class="region-searchbox-input" name="sq1" id="sq1" placeholder="city, region, or country..." autocomplete="off" autocorrect="off">
                                <span id="region-searchbox-icon" style="display: none;"></span>
                            </form>
                        </div>
                    </li>
                </ul>
            </div>
        </span></div>
    </div>
{% endblock %}

{% block content %}

<style>
    .summit-card-header {
        border-top-left-radius: 12px;
        border-top-right-radius: 12px;
    }
    .user-photo-info {
        z-index: 99;
    }
    .tooltip-inner {
        white-space:pre-wrap;
    }

    .blueimp-gallery > .description {
        position: absolute;
        bottom: 0px;
        width: 100%;
        text-align: center;
        color: #fff;
        margin-bottom: 2%;
        height: auto;
        display: none;
    }
    .blueimp-gallery-controls > .description {
        display: block;
    }
    .blueimp-gallery-controls > .description > .description-text {
        padding: 10px;
        background: rgba(0, 0, 0, 0.5);
    }

    .autocomplete-suggestion {
        padding: 0px 0px;
        white-space: nowrap;
        overflow: hidden;
        cursor: pointer;
        border-bottom: 1px solid #e0e0e0;
    }

/* mobile */
   @media (767px >= width) {
       #bottom-spacer {
           height: 48px;
       }
       .region-searchbox {
            top: 8px;
        }
        .summit-card-peak-title {
            margin-left: 0px;
        }
        .summit-card-mobile-stats {
            top: 38px;
        }
        #region-title-caret {
            font-size: 16px;
            float: right;
            margin-top: -18px;
            color: rgb(0, 177, 242);
        }
        .addl-summits-stats {
            font-size: 12px;
            letter-spacing: 0px;
        }
        .summit-list-stats {
            letter-spacing: 0px;
        }
        #slideshow1 {
            border-bottom-right-radius: 12px !important;
        }
   }

    @media (width >= 768px) {
       #bottom-spacer {
           height: 54px;
       }
        .autocomplete-suggestions {
            margin-left: initial !important;
            max-width: 1000px;
        }
        #region-title-caret {
            font-size: 16px;
            float: right;
            margin-top: -22px;
            color: rgb(0, 177, 242);
        }
        #slideshow1 {
            border-bottom-right-radius: 12px !important;
        }
    }

    @media (768px <= width <= 1023px) {
        .region-searchbox {
            top: 12px;
        }
        input#sq1.region-searchbox-input {
            height: 40px;
            padding: 0px 0px 0px 10px;
            line-height: 40px;
            font-size: 18px;
        }
    }

    @media (width >= 1024px) {
        .summits-filter-button {
           /* margin-top: 10px; */
        }
        .region-searchbox {
            top: 11px;
        }
        input#sq1.region-searchbox-input {
            height: 40px;
            padding: 0px 0px 0px 10px;
            line-height: 40px;
            font-size: 20px;
        }
        #region-searchbox-icon {
            margin-top: -35px;
        }
        .hero-photo-left {
            border-right: solid 1px #e0e0e0;
            border-bottom-left-radius:12px;
        }
        .hero-photo-right {
            border-left: solid 1px #e0e0e0;
            border-bottom-right-radius:12px;
        }
        #slideshow1 {
            border-bottom-right-radius: 0px !important;
        }
    }


</style>

<div class="container">
    <div class="row pad-mobile">
        <div class="col-md-12">
            <div class="row" id="ajax-data-loading" style="display: none;">
              <div class="col-md-12 data-loading" style="text-align: center; color: #666;">
                <i class="fa fa-spinner fa-spin fa-5x"></i>
              </div>
            </div>
            <div class="row" id="summits-list"></div>
            <div class="row" id="more-summits-list"></div>
            <div class="row" id="more-ajax-data-loading" style="display: none;">
              <div class="col-md-12" style="text-align: center; color: #c0c0c0; margin-top: 20px; margin-bottom: 20px;">
                <i class="fa fa-spinner fa-spin fa-5x"></i>
              </div>
            </div>
            <div class="row" id="more-summits" style="display: none;">
              <div class="row dark-background-row">
                <div style="height: 120px; text-align: center; margin-top: 60px;">
                    <button id="more-summits-btn" class="btn btn-secondary" style="display: none; width: 160px; color: #fff;">See more summits</button>
                </div>
              </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div id="bottom-spacer"></div>
    </div>
</div>

<script type="text/javascript">

    var lastRegion = '';
    var lastRegionName = '';

    var tmpLastRegion = readCookie('last_region');
    var tmpLastRegionName = readCookie('last_region_name');

    var is_loading = true;

    if (tmpLastRegion && tmpLastRegionName) {
        lastRegion = tmpLastRegion;
        lastRegionName = tmpLastRegionName;
        $('#see-worldwide').html('<a href="javascript:loadRegionSummits(\'' + lastRegion + '\', \'' + lastRegionName + '\');">see ' + lastRegionName + '</a>');
    }

// load the first batch of logs

    function loadSummits(page) {

        is_loading = true;
        $('#hdnPage').val('2');
        document.activeElement.blur();
        $('#summits-list').empty();
        $('#more-summits-list').empty();
        $('#ajax-data-loading').css('display', 'inline');
        $('#more-summits').css('display', 'none');
        $('#summits-count').html('');
        $('#mobile-summits-count').html('');
        $('#summits-count-desc').html('');

        $('.section-title').css('display','inline');
        $('.section-title-stats').css('display','inline');
        $('#see-worldwide').css('display','inline');

        var country_id = $("#hdnCountryId").val();
        var region_id = $("#hdnRegionId").val();
        var city_id = $("#hdnCityId").val();
        if (region_id == "-1"){
            region_id = "";
        }
        var firstFeaturedSummit = 0;
        var secondFeaturedSummit = 0;
        var counter = 1;
        var summitIds = '';
        $.getJSON('{% url "summits_latest" %}?country_id=' + country_id + '&region_id=' + region_id + '&city_id=' + city_id + '&page=' + page , function(data) {
            $.each( data, function( key, val ) {
                if (key=='summits_count') {
                    $('#summits-count').html(numberWithCommas(val));
                    $('#mobile-summits-count').html(numberWithCommas(val));
                    if (val != '1') {
                        $('#summits-count-desc').html(' summits');
                        $('#mobile-summits-count-desc').html(' summits');
                    } else {
                        $('#summits-count-desc').html(' summit');
                        $('#mobile-summits-count-desc').html(' summit');
                    }
                }
                if (key=='summits') {
                    window.scrollTo(0, 0);
                    var photoCaptionClass = '';
                    var photoIndex = 0;
//first two featured logs with hero peak photos and slideshows
                    $.each( val, function( summitkey, summitval ) {
                        if (summitval.has_peak_thumbnail > 0) {
                            photoCaptionClass = 'hero-user-photo-info';
                            //build photos
                            if (photoIndex == 0) {
                                divClass = 'hero-photo-left col-lg-6 col-md-6 col-sm-12 col-xs-12';
                            } else {
                                divClass = 'hero-photo-right col-lg-6 col-md-6 col-sm-12 col-xs-12 hidden-xs hidden-sm';
                            }
                            imgHeight = '480';
                            slideshowDivIndex = photoIndex+1;

                            //add user photos
                            var userPhotos = '';
                            var photo_arr = summitval.photos;

                            if (photo_arr.length > 0) {
                                $.each(summitval.photos.slice(0, 4), function (photokey, photoval) { // Slice for only showing the first 4 pictures.
                                    userPhotos = userPhotos + '<div style="width: 100%; height: 100%; cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url.replace('480x360_q95','910x680_q95') + '\'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;"></div>';
                                });
                            }

                            slideshowclass = '';
                            hoverclass = 'hover-photos';
                            if (summitval.peak_thumbnail_url.indexOf('default.png.') > 0) {
                                if (userPhotos != '') {
                                    photoHtml = userPhotos;
                                    slideshowdiv = 'slideshow'+slideshowDivIndex;
                                    window['slideshow'+slideshowDivIndex] = true;
                                } else {
                                    photoHtml = '<div style="width: 100%; height: 100%; cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}' + summitval.peak_fullsize_photo_url + '\'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;"></div>';
                                    slideshowdiv = 'no-slideshow';
                                    slideshowclass = 'peakimglrg-responsive';
                                    hoverclass = '';
                                }
                            } else {
                                photoHtml = '<div style="width: 100%; height: 100%; cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}' + summitval.peak_fullsize_photo_url + '\'); overflow: hidden; background-size: cover; background-position: center center; background-repeat: no-repeat;"></div>' + userPhotos;
                                slideshowdiv = 'slideshow'+slideshowDivIndex;
                                window['slideshow'+slideshowDivIndex] = true;
                            }

                            $('#summits-list').append('<div onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" class="' + divClass + '" style="cursor: pointer; padding-right: 0px; padding-left: 0px;"><div class="top-photos peakimglrg-responsive"><div class="' + hoverclass + '"><a href="/' + summitval.peak_slug + '/summits/' + summitval.id + '/"><div class="hero-slideshow ' + slideshowclass + '" id="' + slideshowdiv + '">' + photoHtml + '</div></a></div><div class="' + photoCaptionClass + '" style="display: block; z-index: 99;"><span class="data hero-photo-caption-peakname" style="position: absolute; bottom: 0px; left: 10px; color: #fff;"><p class="bagger ellipsis" style="font-size: 14px;"><a href="/' + summitval.peak_slug + '/summits/' + summitval.id + '" style="color: #fff;">' + summitval.peak_name + '</a></p></span><span class="data hidden-xs hero-photo-caption-username" style="position: absolute; bottom: 0px; right: 10px; color: #fff;"><p class="bagger ellipsis" style="font-size: 14px; text-align: right;"><a href="/' + summitval.peak_slug + '/summits/' + summitval.id + '" style="color: #fff;">' + summitval.username + '&nbsp;&bull;&nbsp;<time class="timeago" datetime="' + summitval.summitlog_date + 'T00:00:00">' + summitval.summitlog_date + '</time></a></p></span></div></div></div>');
                            photoIndex++;
                            if (firstFeaturedSummit == 0) {
                                firstFeaturedSummit = summitval.id;
                            } else if (secondFeaturedSummit == 0) {
                                secondFeaturedSummit = summitval.id;
                                return false;
                            }
                        }
                    });
                }
            });

            $.each( data, function( key, val ) {
                if (key=='summits') {
                    $.each( val, function( summitkey, summitval ) {
                        var summitdate = new Date(summitval.summitlog_date);
                        var today = new Date();
                        var timeDiff = Math.abs(today.getTime() - summitdate.getTime());
                        var diffDays = Math.ceil(timeDiff / (1000 * 3600 * 24));
                        var photo_arr = summitval.photos;
                        var avatar;
                        var isUsa = false;
                        if (summitval.avatar_url != 'None') {
                            avatar = '<img src="{{MEDIA_URL}}' + summitval.avatar_url + '" class="summit-card-user-avatar">&nbsp;&nbsp;';
                        } else {
                            avatar = '';
                        }

                        //build country string
                        var country = '';
                        $.each( summitval.country, function( countrykey, countryval ) {
                            country = country + countryval.country_name + ' / ';
                            if (countryval.country_name == 'United States') {
                                isUsa = true;
                            }
                        });
                        country = country.substr(0, country.length-3);
                        if (country == '') {
                            country = 'Unknown Country';
                        }

                        //build region string
                        var region = '';
                        $.each( summitval.region, function( regionkey, regionval ) {
                            region = region + regionval.region_name + ', ' + regionval.country_name + ' / ';
                        });
                        region = region.substr(0, region.length-3);
                        if (region == '') {
                            region = country;
                        }

                        var dividerDiv = '';
                        if (counter > 0) {
                            dividerDiv = '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"><div class="row dark-background-row"><div class="sp-60"></div></div></div>';
                        }

                        if (summitval.date_entered == 'False') {
                            var desktop_date = 'date unknown';
                            var mobile_date = 'date unknown';
                        } else {
                            var display_date = $.format.date(summitval.summitlog_date + 'T00:00:00', 'MMM d, yyyy');
                            var desktop_date = display_date + '&nbsp;/&nbsp;<time class="timeago" datetime="' + summitval.summitlog_date + 'T00:00:00">' + summitval.summitlog_date + '</time>';
                            var mobile_date = '<time class="timeago" datetime="' + summitval.summitlog_date + 'T00:00:00">' + summitval.summitlog_date + '</time>';
                        }

                        var header_border = '';
                        if (photo_arr.length == 0 && (summitval.gpx_file == 'None' || summitval.gpx_file == '') && summitval.log != null) {
                            header_border = 'border-bottom: 1px solid #c0c0c0; ';
                        }

                        var attempt = '';
                        var mobile_stats = '';

                        var max_username_length = 15;
                        var max_region_length = 27;
                        var trimmed_username = summitval.username.length > max_username_length ? summitval.username.substring(0, max_username_length - 3) + "..." : summitval.username;
                        var trimmed_region = region.length > max_region_length ? region.substring(0, max_region_length - 3) + "..." : region;

                        if (summitval.attempt == 'True') {
                            attempt = '<span class="summit-attempt">Attempt</span>&nbsp;&nbsp;&bull;&nbsp;&nbsp;';
                            mobile_stats = '<span class="summit-attempt">Attempt</span>&nbsp;&bull;&nbsp;'  + '<a href="/members/' + summitval.username + '/">' + trimmed_username + '</a>&nbsp;&bull;&nbsp;' + mobile_date;
                        } else {
                            mobile_stats = trimmed_region + '&nbsp;&bull;&nbsp;<a href="/members/' + summitval.username + '/">' + trimmed_username + '</a>&nbsp;&bull;&nbsp;' + mobile_date;
                        }

                        var additional_summits = '';
                        if (summitval.num_summits_in_group > 1) {
                            var num_additional_summits = parseInt(summitval.num_summits_in_group) - 1;
                            var additional_peak_names = '';
                            $.each(summitval.addl_summits, function (addlsummitkey, addlsummitval) {
                                additional_peak_names = additional_peak_names + addlsummitval.peak_name + '\n';
                            });
                            additional_summits = '<span class="summit-list-stats addl-summits-stats" style="font-weight: 300; color: #0ae;" data-toggle="tooltip" data-placement="top" data-original-title="' + additional_peak_names + '">&nbsp;&nbsp;+ '  + num_additional_summits + ' other peak' + ((num_additional_summits != 1) ? 's' : '') + '</span>';
                        }

                        $('#summits-list').append(dividerDiv + '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"><div class="row" style="' + header_border + 'cursor: pointer;" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');"><div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 summit-card-header" style="background-color: #fff; border-right: 1px solid #c0c0c0; letter-spacing: 1.0px;"><div class="summit-list-stats summit-card-user-stats hidden-xs hidden-sm" style="float: right;">&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + avatar + '<a href="/members/' + summitval.username + '/">' + summitval.username + '</a>&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + desktop_date + '</div><div class="summit-list-stats summit-card-user-stats hidden-lg hidden-md hidden-xs" style="float: right;">&nbsp;&nbsp;&bull;&nbsp;&nbsp;<a href="/members/' + summitval.username + '/">' + summitval.username + '</a>&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + mobile_date + '</div><div class="summit-list-stats summit-card-peak-stats hidden-lg hidden-md hidden-xs" style="float: right;">' + attempt + region + '</div><div class="stats-header summit-card-peak-title" style="height: 70px; margin-top: 0px; margin-bottom: 0px;"><div class="summit-list-stats summit-card-peak-stats hidden-xs hidden-sm" style="float: right;">' + attempt + numberWithCommas(Math.floor(summitval.elevation)) + ' ft / ' + numberWithCommas(Math.floor(summitval.elevation * .3048)) + ' m&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + region + '</div><div class="ellipsis" style="overflow: hidden;"><a style="color: #000000;" href="/' + summitval.peak_slug + '/">' + summitval.peak_name + '</a>' + additional_summits + '</div></div><div class="summit-list-stats summit-card-mobile-stats hidden-lg hidden-md hidden-sm ellipsis">' + mobile_stats + '</div></div></div>');

                        //get favorites count
                        var favorites_arr = summitval.favorites;
                        var favorites_count = favorites_arr.length;
                        if (favorites_count == '0') {
                            favorites_count = '&nbsp;';
                        }

                        //get comments count
                        var comments_arr = summitval.comments;
                        var comments_count = comments_arr.length;
                        if (comments_count == '0') {
                            comments_count = '&nbsp;';
                        }

                        var staticMapZoom = '/' + summitval.peak_long + ',' + summitval.peak_lat + ',10,0.00,0.00';
                        var twoPhotoMapClass = 'col-lg-6 col-md-6 col-sm-4 hidden-xs map-card-web-2 map-card-mobile-2';
                        //gpx file?
                        if (summitval.gpx_file != 'None' && summitval.gpx_file != '') {
                            staticMapZoom = '/auto';
                            twoPhotoMapClass = 'col-lg-6 col-md-6 col-sm-4 col-xs-12 map-card-web-2 map-card-mobile-2';
                        }

                        //add photos
                        var photo_index = 0;
                        var photo_arr = summitval.photos;
                        var photos_count = photo_arr.length;
                        var photos_style = '';
                        if (photos_count == '0') {
                            photos_count = '&nbsp;';
                            photos_style = 'display: none;'
                        }

                        var statsOverlayText = '';
                        var mobileStatsOverlayText = '';
                        var gpxStatsOverlay = '';
                        var mobileStatsOverlay = '';
                        var divStyle = '';
                        if (summitval.gpx_file != 'None' && summitval.gpx_file != '') {
                            //build stats overlay
                            if (summitval.total_distance > 0 || summitval.total_trip_time > 0 || summitval.elevation_gain > 0) {
                                if (isUsa) {
                                    if (summitval.total_distance > 0) {
                                        gpxStatsOverlay = gpxStatsOverlay + parseFloat(summitval.total_distance).toFixed(1) + ' mi &bull; ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="fas fa-arrows-alt-h" style="margin-right: 5px;"></i>' + parseFloat(summitval.total_distance).toFixed(1) + ' mi</span><div style="width: 1px; height: 30px; background-color: #ccc; margin-top: 10px;"></div> ';
                                    }
                                    if (summitval.elevation_gain > 0) {
                                        gpxStatsOverlay = gpxStatsOverlay + parseInt(summitval.elevation_gain).toLocaleString() + ' ft gain &bull; ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="fas fa-long-arrow-alt-up fa-rotate-45" style="margin-right: 5px;"></i>' + parseInt(summitval.elevation_gain).toLocaleString() + ' ft gain</span><div style="width: 1px; height: 30px; background-color: #ccc; margin-top: 10px;"></div> ';
                                    }
                                    if (summitval.total_trip_time > 0) {
                                        totalSeconds = parseFloat(summitval.total_trip_time);
                                        hours = Math.floor(totalSeconds / 3600);
                                        totalSeconds %= 3600;
                                        minutes = Math.floor(totalSeconds / 60);
                                        gpxStatsOverlay = gpxStatsOverlay + hours + 'h ' + minutes + 'm ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="far fa-clock" style="margin-right: 5px;"></i>' + hours + ' hr ' + minutes + ' min </span></div>';
                                    }
                                } else {
                                    if (summitval.total_distance > 0) {
                                        km = parseFloat(summitval.total_distance) * 1.609344
                                        gpxStatsOverlay = gpxStatsOverlay + km.toFixed(1) + ' km &bull; ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="fas fa-arrows-alt-h" style="margin-right: 5px;"></i>' + km.toFixed(1) + ' km</span><div style="width: 1px; height: 30px; background-color: #ccc; margin-top: 10px;"></div> ';
                                    }
                                    if (summitval.elevation_gain > 0) {
                                        m = parseFloat(summitval.elevation_gain) * 0.3048
                                        gpxStatsOverlay = gpxStatsOverlay + parseInt(m).toLocaleString() + ' m gain &bull; ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="fas fa-long-arrow-alt-up" style="margin-right: 5px;"></i>' + parseInt(m).toLocaleString() + ' m gain</span><div style="width: 1px; height: 30px; background-color: #ccc; margin-top: 10px;"></div> ';
                                    }
                                    if (summitval.total_trip_time > 0) {
                                        totalSeconds = parseFloat(summitval.total_trip_time);
                                        hours = Math.floor(totalSeconds / 3600);
                                        totalSeconds %= 3600;
                                        minutes = Math.floor(totalSeconds / 60);
                                        gpxStatsOverlay = gpxStatsOverlay + hours + 'h ' + minutes + 'm ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="far fa-clock" style="margin-right: 5px;"></i>' + hours + ' hr ' + minutes + ' min </span>';
                                    }
                                }
                            }

                            statsOverlayText = '<div class="user-photo-info hidden-xs" style="display: block; height: 60px;"><span class="data photo-caption" style="position: absolute; bottom: -5px; right: 10px; color: #fff;"><p class="bagger" style="font-size: 16px; margin: 0 10px 15px 0;">' + gpxStatsOverlay + '</p></span></div>';
                            mobileStatsOverlayText = '<div class="hidden-lg hidden-md hidden-sm col-xs-12" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; background-color: #fff; border-bottom: 1px solid #c0c0c0; margin-top: -1px; padding-left: 0px; padding-right: 0px;"><div class="col-md-12" style="height: 50px;line-height: 50px;font-size: 12px;color: #999;display: flex;justify-content: space-between;background-color: #f2f2f2;padding-left: 0px;padding-right: 1px;">' + mobileStatsOverlay + '</div></div>';
                        }

                        var staticMapUrl = summitval.map_mapbox_thumbnail;
                        if (photo_arr.length == 0) {
                            if (summitval.peak_thumbnail_url == 'images/img/cache/default.png.350x245_q95_crop.jpg') {
                                //gpx file?
                                if (summitval.gpx_file != 'None' && summitval.gpx_file != '' && staticMapUrl.length > 0) {
                                    $('#summits-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" id="map-' + summitval.id + '" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="img-responsive peakeryPhoto photography peakimg-responsive" src="{% static 'img/spacer.png' %}"></div>' + statsOverlayText + '</div>');
                                }
                            } else {
                                $('#summits-list').append('<div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}' + summitval.peak_thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><img src="{% static 'img/spacer.png' %}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                $('#summits-list').append('<div class="col-lg-9 col-md-9 col-sm-8 col-xs-6 map-card-web-1 map-card-mobile-1 map-card-tablet-1" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="img-responsive peakimg-responsive" src="{% static 'img/spacer.png' %}"></div>' + statsOverlayText + '</div>');
                            }
                        } else if (photo_arr.length == 1) {
                            $.each(summitval.photos, function (photokey, photoval) {
                                var photoCaption = photoval.caption;
                                if (photoCaption == 'None') {
                                    photoCaption = '';
                                }
                                $('#summits-list').append('<a class="gallery-link" data-summitlogid="' + summitval.id + '" data-user="' + photoval.username + '" data-credit="" data-description="' + encodeDoubleQuotes(photoCaption) + '" data-gallery href="{{ MEDIA_URL }}images/' + photoval.fullsize_url + '"><div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><img src="{% static 'img/spacer.png'%}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div></a>');
                                photo_index++;
                                if (photo_index > 3) {
                                    return false;
                                }
                            });
                            $('#summits-list').append('<div class="col-lg-9 col-md-9 col-sm-8 col-xs-6 map-card-web-1 map-card-tablet-1" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png'%}"></div>' + statsOverlayText + '</div>');
                        } else if (photo_arr.length == 2) {
                            $.each(summitval.photos, function (photokey, photoval) {
                                var photoCaption = photoval.caption;
                                if (photoCaption == 'None') {
                                    photoCaption = '';
                                }
                                $('#summits-list').append('<a class="gallery-link map-card-web-2 map-card-mobile-2 map-card-tablet-1" data-summitlogid="' + summitval.id + '" data-user="' + photoval.username + '" data-credit="" data-description="' + encodeDoubleQuotes(photoCaption) + '" data-gallery href="{{ MEDIA_URL }}images/' + photoval.fullsize_url + '"><div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><img src="{% static 'img/spacer.png'%}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div></a>');
                                photo_index++;
                                if (photo_index > 3) {
                                    return false;
                                }
                            });
                            $('#summits-list').append('<div class="' + twoPhotoMapClass + '" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png'%}"></div>' + statsOverlayText + '</div>');
                        } else if (photo_arr.length == 3) {
                            $.each(summitval.photos, function (photokey, photoval) {
                                var photoCaption = photoval.caption;
                                if (photoCaption == 'None') {
                                    photoCaption = '';
                                }
                                divClass = 'col-lg-3 col-md-3 col-sm-4 col-xs-6';
                                $('#summits-list').append('<a class="gallery-link" data-summitlogid="' + summitval.id + '" data-user="' + photoval.username + '" data-credit="" data-description="' + encodeDoubleQuotes(photoCaption) + '" data-gallery href="{{ MEDIA_URL }}images/' + photoval.fullsize_url + '"><div class="' + divClass + ' summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><img src="{% static 'img/spacer.png'%}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div></a>');
                                photo_index++;
                                if (photo_index > 3) {
                                    return false;
                                }
                            });
                            $('#summits-list').append('<div class="col-lg-3 col-md-3 col-sm-12 col-xs-6 map-card-tablet-3" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png'%}"></div>' + statsOverlayText + '</div>');
                        } else {
                            var display_photo = '';
                            $.each(summitval.photos, function (photokey, photoval) {
                                if (summitval.gpx_file != 'None' && summitval.gpx_file != '') {
                                    if (photo_index < 3) {
                                        divClass = 'col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-card-web-3';
                                    } else {
                                        divClass = 'hidden-lg hidden-md hidden-sm hidden-xs photo-card-web-3';
                                    }
                                } else {
                                    if (photo_index < 3) {
                                        divClass = 'col-lg-3 col-md-3 col-sm-4 col-xs-6 photo-card-web-3';
                                    } else {
                                        divClass = 'col-lg-3 col-md-3 hidden-sm col-xs-6 photo-card-web-3';
                                    }
                                }
                                var photoCaption = photoval.caption;
                                if (photoCaption == 'None') {
                                    photoCaption = '';
                                }
                                $('#summits-list').append('<a style="' + display_photo + '" class="gallery-link" data-summitlogid="' + summitval.id + '" data-user="' + photoval.username + '" data-credit="" data-description="' + encodeDoubleQuotes(photoCaption) + '" data-gallery href="{{ MEDIA_URL }}images/' + photoval.fullsize_url + '"><div class="' + divClass + ' summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><img src="{% static 'img/spacer.png'%}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div></a>');
                                photo_index++;
                                if (photo_index > 3) {
                                    //return false;
                                    display_photo = 'display: none;';
                                }
                            });
                            //when photos > 3
                            if (summitval.gpx_file != 'None' && summitval.gpx_file != '') {
                                $('#summits-list').append('<div class="col-lg-3 col-md-3 col-sm-12 col-xs-6 map-card-tablet-3" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png'%}"></div>' + statsOverlayText + '</div>');
                            }
                        }
                        if (summitval.gpx_file != 'None' && summitval.gpx_file != '') {
                            $('#summits-list').append(mobileStatsOverlayText);
                        }

                        //add log
                        //don't truncate to 700 characters anymore
                        if (summitval.log != null && summitval.log != '') {
                            var log_text = summitval.log.replace(/(?:\r\n|\r|\n)/g, '<br>');
                            if (log_text.length > 9999) {
                                log_text = summitval.log.substring(0, 700)+'...';
                            }
                            $('#summits-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; background-color: #fff; border-bottom: 1px solid #c0c0c0;"><div><p class="summit-log-summary">' + log_text + '</p></div></div>');
                        }
                        //footer
                        classLiked = '';
                        if (summitval.log != null || (summitval.gpx_file != 'None' && summitval.gpx_file != '') || photo_arr.length > 0 || summitval.peak_thumbnail_url != 'images/img/cache/default.png.350x245_q95_crop.jpg') {
                            $('#summits-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');"><div class="row" style="border-bottom-left-radius: 12px; border-bottom-right-radius: 12px;"><div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="height: 70px; background-color: #fff; z-index: 1; border-bottom-left-radius: 12px; border-bottom-right-radius: 12px; box-shadow: 0px 10px 10px rgba(0, 0, 0, 1);"><div><p><span class="summit-list-footer" style="font-weight: 600; line-height: 70px;"><a href="/' + summitval.peak_slug + '/summits/' + summitval.id + '/"><button style="padding-left: 13px; width: 100%; height: 100%; text-align: left; border: none; color: #00B1F2; font-weight: 600; position: absolute; left: -2px; top: -2px; z-index: 1; border-bottom-left-radius: 12px; border-bottom-right-radius: 12px;" class="btn btn-default btn-see-full-log">SEE FULL LOG</button></a></span><span class="summit-list-footer pull-right" style="margin-top: 8px; position: absolute; right: 30px; z-index: 2;"><span style="margin-left: 40px;"><a href="javascript:like_summit(' + summitval.id + ');" class="like_summit{{ is_authenticated }}" id="summitlog-with-' + summitval.id + '"><span class="' + classLiked + '" id="summitlog-like-' + summitval.id + '" style="width: 32px; height: 32px; display: inline-block; color: #ffffff; line-height: 32px; text-align: center; padding-left: 2px; font-size: 10px; font-weight: bold; background-size: 100%; background-image: url({% static 'img/fa-heart_256_0_00b1f2_none.png'%});">&nbsp;</span></a></span><span style="margin-left: 40px;"><a href="/' + summitval.peak_slug + '/summits/' + summitval.id + '/"><span id="summitlog-comment-' + summitval.id + '" style="width: 32px; height: 32px; display: inline-block; color: #ffffff; line-height: 30px; text-align: center; padding-left: 3px; font-size: 10px; font-weight: bold; background-size: 100%; background-image: url({% static 'img/fa-comment_256_0_00b1f2_none.png'%});">&nbsp;</span></a></span><span style="margin-left: 40px; ' + photos_style + '"><a href="/' + summitval.peak_slug + '/summits/' + summitval.id + '/"><span style="width: 32px; height: 32px; display: inline-block; color: #ffffff; line-height: 36px; text-align: center; padding-left: 2px; font-size: 10px; font-weight: bold; background-size: 100%; background-image: url({% static 'img/fa-camera_256_0_00b1f2_none.png'%});">' + photos_count + '</span></a></span></span></p><span class="peak-list-highlights"><p></p></span></div></div></div>');
                        }
                        $('#summits-list').append('</div>');

                        if (summitval.gpx_mapbox_thumbnail != '') {
                            $("#map-"+summitval.id).css("background-image", "url('" + summitval.gpx_mapbox_thumbnail + "')");
                        }

                        //tooltips
                        $('.addl-summits-stats').tooltip();

                        counter++;
                        summitIds = summitIds + summitval.id + ',';

                    });
                }
            });

            if (summitIds != '') {
                summitIds = summitIds.substring(0, summitIds.length - 1)
                loadLikesComments(summitIds)
            }
            $("time.timeago").timeago();
            $('#ajax-data-loading').css('display', 'none');
            $('#more-summits').css('display', 'inline');

            if (firstFeaturedSummit > 0) {
                $('#slideshow1').cycle({
                    fx: 'fade',
                    timeout: 3000
                });
            }
            if (secondFeaturedSummit > 0) {
                $('#slideshow2').cycle({
                    fx: 'fade',
                    timeout: 3000
                });
            }
            is_loading = false;
        });
    }


// load next batch of logs
    function loadMoreSummits(page) {

        is_loading = true;
        $('#more-ajax-data-loading').css('display', 'inline');
        $('#more-summits-btn').html('<i class="fa fa-spinner fa-spin"></i>');

        var country_id = $("#hdnCountryId").val();
        var region_id = $("#hdnRegionId").val();
        if (region_id == "-1"){
            region_id = "";
        }
        var counter = 1;
        var summitIds = '';
        $.getJSON('{% url "summits_latest" %}?country_id=' + country_id + '&region_id=' + region_id + '&page=' + page , function(data) {
            $.each( data, function( key, val ) {
                if (key=='summits') {
                    $.each( val, function( summitkey, summitval ) {
                        var photo_arr = summitval.photos;
                        var avatar;
                        var isUsa = false;
                        if (summitval.avatar_url != 'None') {
                            avatar = '<img src="{{MEDIA_URL}}' + summitval.avatar_url + '" class="summit-card-user-avatar">&nbsp;&nbsp;';
                        } else {
                            avatar = '';
                        }

                        //build country string
                        var country = '';
                        $.each( summitval.country, function( countrykey, countryval ) {
                            country = country + countryval.country_name + ' / ';
                            if (countryval.country_name == 'United States') {
                                isUsa = true;
                            }
                        });
                        country = country.substr(0, country.length-3);
                        if (country == '') {
                            country = 'Unknown Country';
                        }

                        //build region string
                        var region = '';
                        $.each( summitval.region, function( regionkey, regionval ) {
                            region = region + regionval.region_name + ', ' + regionval.country_name + ' / ';
                        });
                        region = region.substr(0, region.length-3);
                        if (region == '') {
                            region = country;
                        }

                        var dividerDiv = '';
                        if (counter > 0) {
                            dividerDiv = '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"><div class="row dark-background-row"><div class="sp-60"></div></div></div>';
                        }

                        if (summitval.date_entered == 'False') {
                            var desktop_date = 'date unknown';
                            var mobile_date = 'date unknown';
                        } else {
                            var display_date = $.format.date(summitval.summitlog_date + 'T00:00:00', 'MMM d, yyyy');
                            var desktop_date = display_date + '&nbsp;/&nbsp;<time class="timeago" datetime="' + summitval.summitlog_date + 'T00:00:00">' + summitval.summitlog_date + '</time>';
                            var mobile_date = '<time class="timeago" datetime="' + summitval.summitlog_date + 'T00:00:00">' + summitval.summitlog_date + '</time>';
                        }

                        var header_border = '';
                        if (photo_arr.length == 0 && (summitval.gpx_file == 'None' || summitval.gpx_file == '') && summitval.log != null) {
                            header_border = 'border-bottom: 1px solid #c0c0c0; ';
                        }

                        var attempt = '';
                        var mobile_stats = '';

                        var max_username_length = 15;
                        var max_region_length = 27;
                        var trimmed_username = summitval.username.length > max_username_length ? summitval.username.substring(0, max_username_length - 3) + "..." : summitval.username;
                        var trimmed_region = region.length > max_region_length ? region.substring(0, max_region_length - 3) + "..." : region;

                        if (summitval.attempt == 'True') {
                            attempt = '<span class="summit-attempt">Attempt</span>&nbsp;&nbsp;&bull;&nbsp;&nbsp;';
                            mobile_stats = '<span class="summit-attempt">Attempt</span>&nbsp;&bull;&nbsp;'  + '<a href="/members/' + summitval.username + '/">' + trimmed_username + '</a>&nbsp;&bull;&nbsp;' + mobile_date;
                        } else {
                            mobile_stats = trimmed_region + '&nbsp;&bull;&nbsp;<a href="/members/' + summitval.username + '/">' + trimmed_username + '</a>&nbsp;&bull;&nbsp;' + mobile_date;
                        }

                        var additional_summits = '';
                        if (summitval.num_summits_in_group > 1) {
                            var num_additional_summits = parseInt(summitval.num_summits_in_group) - 1;
                            var additional_peak_names = '';
                            $.each(summitval.addl_summits, function (addlsummitkey, addlsummitval) {
                                additional_peak_names = additional_peak_names + addlsummitval.peak_name + '\n';
                            });
                            additional_summits = '<span class="summit-list-stats addl-summits-stats" style="font-weight: 300; color: #0ae;" data-toggle="tooltip" data-placement="top" data-original-title="' + additional_peak_names + '">&nbsp;&nbsp;+ '  + num_additional_summits + ' other peak' + ((num_additional_summits != 1) ? 's' : '') + '</span>';
                        }

                        $('#summits-list').append(dividerDiv + '<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12"><div class="row" style="' + header_border + 'cursor: pointer;" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');"><div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 summit-card-header" style="background-color: #fff; border-right: 1px solid #c0c0c0; letter-spacing: 1.0px;"><div class="summit-list-stats summit-card-user-stats hidden-xs hidden-sm" style="float: right;">&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + avatar + '<a href="/members/' + summitval.username + '/">' + summitval.username + '</a>&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + desktop_date + '</div><div class="summit-list-stats summit-card-user-stats hidden-lg hidden-md hidden-xs" style="float: right;">&nbsp;&nbsp;&bull;&nbsp;&nbsp;<a href="/members/' + summitval.username + '/">' + summitval.username + '</a>&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + mobile_date + '</div><div class="summit-list-stats summit-card-peak-stats hidden-lg hidden-md hidden-xs" style="float: right;">' + attempt + region + '</div><div class="stats-header summit-card-peak-title" style="height: 70px; margin-top: 0px; margin-bottom: 0px;"><div class="summit-list-stats summit-card-peak-stats hidden-xs hidden-sm" style="float: right;">' + attempt + numberWithCommas(Math.floor(summitval.elevation)) + ' ft / ' + numberWithCommas(Math.floor(summitval.elevation * .3048)) + ' m&nbsp;&nbsp;&bull;&nbsp;&nbsp;' + region + '</div><div class="ellipsis" style="overflow: hidden;"><a style="color: #000000;" href="/' + summitval.peak_slug + '/">' + summitval.peak_name + '</a>' + additional_summits + '</div></div><div class="summit-list-stats summit-card-mobile-stats hidden-lg hidden-md hidden-sm ellipsis">' + mobile_stats + '</div></div></div>');

                        var staticMapZoom = '/' + summitval.peak_long + ',' + summitval.peak_lat + ',10,0.00,0.00';
                        var twoPhotoMapClass = 'col-lg-6 col-md-6 col-sm-4 hidden-xs map-card-web-2 map-card-mobile-2';
                        //gpx file?
                        if (summitval.gpx_file != 'None' && summitval.gpx_file != '') {
                            staticMapZoom = '/auto';
                            twoPhotoMapClass = 'col-lg-6 col-md-6 col-sm-4 col-xs-12 map-card-web-2 map-card-mobile-2';
                        }

                        //add photos
                        var photo_index = 0;
                        var photo_arr = summitval.photos;
                        var photos_count = photo_arr.length;
                        var photos_style = '';
                        if (photos_count == '0') {
                            photos_count = '&nbsp;';
                            photos_style = 'display: none;'
                        }

                        var statsOverlayText = '';
                        var mobileStatsOverlayText = '';
                        var gpxStatsOverlay = '';
                        var mobileStatsOverlay = '';

                        if (summitval.gpx_file != 'None' && summitval.gpx_file != '') {
                            //build stats overlay
                            if (summitval.total_distance > 0 || summitval.total_trip_time > 0 || summitval.elevation_gain > 0) {
                                if (isUsa) {
                                    if (summitval.total_distance > 0) {
                                        gpxStatsOverlay = gpxStatsOverlay + parseFloat(summitval.total_distance).toFixed(1) + ' mi &bull; ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="fas fa-arrows-alt-h" style="margin-right: 5px;"></i>' + parseFloat(summitval.total_distance).toFixed(1) + ' mi</span><div style="width: 1px; height: 30px; background-color: #ccc; margin-top: 10px;"></div> ';
                                    }
                                    if (summitval.elevation_gain > 0) {
                                        gpxStatsOverlay = gpxStatsOverlay + parseInt(summitval.elevation_gain).toLocaleString() + ' ft gain &bull; ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="fas fa-long-arrow-alt-up fa-rotate-45" style="margin-right: 5px;"></i>' + parseInt(summitval.elevation_gain).toLocaleString() + ' ft gain</span><div style="width: 1px; height: 30px; background-color: #ccc; margin-top: 10px;"></div> ';
                                    }
                                    if (summitval.total_trip_time > 0) {
                                        totalSeconds = parseFloat(summitval.total_trip_time);
                                        hours = Math.floor(totalSeconds / 3600);
                                        totalSeconds %= 3600;
                                        minutes = Math.floor(totalSeconds / 60);
                                        gpxStatsOverlay = gpxStatsOverlay + hours + 'h ' + minutes + 'm ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="far fa-clock" style="margin-right: 5px;"></i>' + hours + ' hr ' + minutes + ' min </span></div>';
                                    }
                                } else {
                                    if (summitval.total_distance > 0) {
                                        km = parseFloat(summitval.total_distance) * 1.609344
                                        gpxStatsOverlay = gpxStatsOverlay + km.toFixed(1) + ' km &bull; ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="fas fa-arrows-alt-h" style="margin-right: 5px;"></i>' + km.toFixed(1) + ' km</span><div style="width: 1px; height: 30px; background-color: #ccc; margin-top: 10px;"></div> ';
                                    }
                                    if (summitval.elevation_gain > 0) {
                                        m = parseFloat(summitval.elevation_gain) * 0.3048
                                        gpxStatsOverlay = gpxStatsOverlay + parseInt(m).toLocaleString() + ' m gain &bull; ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="fas fa-long-arrow-alt-up" style="margin-right: 5px;"></i>' + parseInt(m).toLocaleString() + ' m gain</span><div style="width: 1px; height: 30px; background-color: #ccc; margin-top: 10px;"></div> ';
                                    }
                                    if (summitval.total_trip_time > 0) {
                                        totalSeconds = parseFloat(summitval.total_trip_time);
                                        hours = Math.floor(totalSeconds / 3600);
                                        totalSeconds %= 3600;
                                        minutes = Math.floor(totalSeconds / 60);
                                        gpxStatsOverlay = gpxStatsOverlay + hours + 'h ' + minutes + 'm ';
                                        mobileStatsOverlay = mobileStatsOverlay + '<span style="width: 33%; text-align: center; font-size: 14px; color: #333;"><i class="far fa-clock" style="margin-right: 5px;"></i>' + hours + ' hr ' + minutes + ' min </span>';
                                    }
                                }
                            }

                            statsOverlayText = '<div class="user-photo-info hidden-xs" style="display: block; height: 60px;"><span class="data photo-caption" style="position: absolute; bottom: -5px; right: 10px; color: #fff;"><p class="bagger" style="font-size: 16px; margin: 0 10px 15px 0;">' + gpxStatsOverlay + '</p></span></div>';
                            mobileStatsOverlayText = '<div class="hidden-lg hidden-md hidden-sm col-xs-12" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; background-color: #fff; border-bottom: 1px solid #c0c0c0; margin-top: -1px; padding-left: 0px; padding-right: 0px;"><div class="col-md-12" style="height: 50px;line-height: 50px;font-size: 12px;color: #999;display: flex;justify-content: space-between;background-color: #f2f2f2;padding-left: 0px;padding-right: 1px;">' + mobileStatsOverlay + '</div></div>';
                        }

                        var staticMapUrl = summitval.map_mapbox_thumbnail;
                        if (photo_arr.length == 0) {
                            if (summitval.peak_thumbnail_url != 'images/img/cache/default.png.350x245_q95_crop.jpg') {
                                $('#summits-list').append('<div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}' + summitval.peak_thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><img src="{% static 'img/spacer.png'%}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div>');
                                $('#summits-list').append('<div class="col-lg-9 col-md-9 col-sm-8 col-xs-6 map-card-web-1 map-card-tablet-1" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="img-responsive peakimg-responsive" src="{% static 'img/spacer.png'%}"></div>' + statsOverlayText + '</div>');
                            }
                        } else if (photo_arr.length == 1) {
                            $.each(summitval.photos, function (photokey, photoval) {
                                var photoCaption = photoval.caption;
                                if (photoCaption == 'None') {
                                    photoCaption = '';
                                }
                                $('#summits-list').append('<a class="gallery-link" data-summitlogid="' + summitval.id + '" data-user="' + photoval.username + '" data-credit="" data-description="' + encodeDoubleQuotes(photoCaption) + '" data-gallery href="{{ MEDIA_URL }}images/' + photoval.fullsize_url + '"><div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><img src="{% static 'img/spacer.png'%}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div></a>');
                                photo_index++;
                                if (photo_index > 3) {
                                    return false;
                                }
                            });
                            $('#summits-list').append('<div class="col-lg-9 col-md-9 col-sm-8 col-xs-6 map-card-web-1 map-card-tablet-1" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png'%}"></div>' + statsOverlayText + '</div>');
                        } else if (photo_arr.length == 2) {
                            $.each(summitval.photos, function (photokey, photoval) {
                                var photoCaption = photoval.caption;
                                if (photoCaption == 'None') {
                                    photoCaption = '';
                                }
                                $('#summits-list').append('<a class="map-card-web-2 gallery-link" data-summitlogid="' + summitval.id + '" data-user="' + photoval.username + '" data-credit="" data-description="' + encodeDoubleQuotes(photoCaption) + '" data-gallery href="{{ MEDIA_URL }}images/' + photoval.fullsize_url + '"><div class="col-lg-3 col-md-3 col-sm-4 col-xs-6 summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><img src="{% static 'img/spacer.png'%}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div></a>');
                                photo_index++;
                                if (photo_index > 3) {
                                    return false;
                                }
                            });
                            $('#summits-list').append('<div class="map-card-web-2 .card-mobile-aspect-ratio-12-4 ' + twoPhotoMapClass + '" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png'%}"></div>' + statsOverlayText + '</div>');
                        } else if (photo_arr.length == 3) {
                            $.each(summitval.photos, function (photokey, photoval) {
                                var photoCaption = photoval.caption;
                                if (photoCaption == 'None') {
                                    photoCaption = '';
                                }
                                divClass = 'col-lg-3 col-md-3 col-sm-4 col-xs-6';
                                $('#summits-list').append('<a class="gallery-link" data-summitlogid="' + summitval.id + '" data-user="' + photoval.username + '" data-credit="" data-description="' + encodeDoubleQuotes(photoCaption) + '" data-gallery href="{{ MEDIA_URL }}images/' + photoval.fullsize_url + '"><div class="' + divClass + ' summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><img src="{% static 'img/spacer.png'%}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div></a>');
                                photo_index++;
                                if (photo_index > 3) {
                                    return false;
                                }
                            });
                            $('#summits-list').append('<div class="col-lg-3 col-md-3 col-sm-12 col-xs-6 map-card-tablet-3" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png'%}"></div>' + statsOverlayText + '</div>');
                        } else {
                            var display_photo = '';
                            $.each(summitval.photos, function (photokey, photoval) {
                                if (summitval.gpx_file != 'None' && summitval.gpx_file != '') {
                                    if (photo_index < 3) {
                                        divClass = 'col-lg-3 col-md-3 col-sm-4 col-xs-6';
                                    } else {
                                        divClass = 'hidden-lg hidden-md hidden-sm hidden-xs map-card-web-2 map-card-mobile-3 ';
                                    }
                                } else {
                                    if (photo_index < 3) {
                                        divClass = 'col-lg-3 col-md-3 col-sm-4 col-xs-6';
                                    } else {
                                        divClass = 'col-lg-3 col-md-3 hidden-sm col-xs-6';
                                    }
                                }
                                var photoCaption = photoval.caption;
                                if (photoCaption == 'None') {
                                    photoCaption = '';
                                }
                                $('#summits-list').append('<a style="' + display_photo + '" class="gallery-link" data-summitlogid="' + summitval.id + '" data-user="' + photoval.username + '" data-credit="" data-description="' + encodeDoubleQuotes(photoCaption) + '" data-gallery href="{{ MEDIA_URL }}images/' + photoval.fullsize_url + '"><div class="' + divClass + ' summitimg-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'{{ MEDIA_URL }}images/' + photoval.thumbnail_url + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; border-right: solid 1px #e0e0e0; overflow: hidden;"><div><img src="{% static 'img/spacer.png'%}" class="img-responsive peakeryPhoto photography peakimg-responsive"></div></div></a>');
                                photo_index++;
                                if (photo_index > 3) {
                                    //return false;
                                    display_photo = 'display: none;';
                                }
                            });
                            if (summitval.gpx_file != 'None' && summitval.gpx_file != '') {
                                $('#summits-list').append('<div class="col-lg-3 col-md-3 col-sm-12 col-xs-6 map-card-tablet-3" id="map-' + summitval.id + '" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; padding-right: 0px; padding-left: 0px; background-image: url(\'' + staticMapUrl + '\'); background-size: cover; background-position: center center; background-repeat: no-repeat; overflow: hidden;"><div><img class="peakimg-responsive" src="{% static 'img/spacer.png'%}"></div>' + statsOverlayText + '</div>');
                            }
                        }
                        if (summitval.gpx_file != 'None' && summitval.gpx_file != '') {
                            $('#summits-list').append(mobileStatsOverlayText);
                        }

                        //add log
                        if (summitval.log != null && summitval.log != '') {
                            var log_text = summitval.log.replace(/(?:\r\n|\r|\n)/g, '<br>');
                            if (log_text.length > 9999) {
                                log_text = summitval.log.substring(0, 700)+'...';
                            }
                            $('#summits-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');" onclick="viewSummit(\'/' + summitval.peak_slug + '/summits/' + summitval.id + '/\');" style="cursor: pointer; background-color: #fff; border-bottom: 1px solid #c0c0c0;"><div><p class="summit-log-summary">' + log_text + '</p></div></div>');
                        }
                        //footer
                        classLiked = '';
                        if (summitval.log != null || (summitval.gpx_file != 'None' && summitval.gpx_file != '') || photo_arr.length > 0 || summitval.peak_thumbnail_url != 'images/img/cache/default.png.350x245_q95_crop.jpg') {
                            $('#summits-list').append('<div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" onmouseover="highlightPhotos(' + summitval.id + ');" onmouseout="unhighlightPhotos(' + summitval.id + ');"><div class="row" style="border-bottom-left-radius: 12px; border-bottom-right-radius: 12px;"><div class="col-lg-12 col-md-12 col-sm-12 col-xs-12" style="height: 70px; background-color: #fff; z-index: 1; border-bottom-left-radius: 12px; border-bottom-right-radius: 12px; box-shadow: 0px 10px 10px rgba(0, 0, 0, 1);"><div><p><span class="summit-list-footer" style="font-weight: 600; line-height: 70px;"><a href="/' + summitval.peak_slug + '/summits/' + summitval.id + '/"><button style="padding-left: 13px; width: 100%; height: 100%; text-align: left; border: none; color: #00B1F2; font-weight: 600; position: absolute; left: -2px; top: -2px; z-index: 1; border-bottom-left-radius: 12px; border-bottom-right-radius: 12px;" class="btn btn-default btn-see-full-log">SEE FULL LOG</button></a></span><span class="summit-list-footer pull-right" style="margin-top: 8px; position: absolute; right: 30px; z-index: 2;"><span style="margin-left: 40px;"><a href="javascript:like_summit(' + summitval.id + ');" class="like_summit{{ is_authenticated }}" id="summitlog-with-' + summitval.id + '"><span class="' + classLiked + '" id="summitlog-like-' + summitval.id + '" style="width: 32px; height: 32px; display: inline-block; color: #ffffff; line-height: 32px; text-align: center; padding-left: 2px; font-size: 10px; font-weight: bold; background-size: 100%; background-image: url({% static 'img/fa-heart_256_0_00b1f2_none.png'%});">&nbsp;</span></a></span><span style="margin-left: 40px;"><a href="/' + summitval.peak_slug + '/summits/' + summitval.id + '/"><span id="summitlog-comment-' + summitval.id + '" style="width: 32px; height: 32px; display: inline-block; color: #ffffff; line-height: 30px; text-align: center; padding-left: 3px; font-size: 10px; font-weight: bold; background-size: 100%; background-image: url({% static 'img/fa-comment_256_0_00b1f2_none.png'%});">&nbsp;</span></a></span><span style="margin-left: 40px; ' + photos_style + '"><a href="/' + summitval.peak_slug + '/summits/' + summitval.id + '/"><span style="width: 32px; height: 32px; display: inline-block; color: #ffffff; line-height: 36px; text-align: center; padding-left: 2px; font-size: 10px; font-weight: bold; background-size: 100%; background-image: url({% static 'img/fa-camera_256_0_00b1f2_none.png'%});">' + photos_count + '</span></a></span></span></p><span class="peak-list-highlights"><p></p></span></div></div></div>');
                        }
                        $('#summits-list').append('</div>');

                        if (summitval.gpx_mapbox_thumbnail != '') {
                            $("#map-"+summitval.id).css("background-image", "url('" + summitval.gpx_mapbox_thumbnail + "')");
                        }

                        //tooltips
                        $('.addl-summits-stats').tooltip();

                        counter++;
                        summitIds = summitIds + summitval.id + ',';

                    });
                }
            });

            if (summitIds != '') {
                summitIds = summitIds.substring(0, summitIds.length - 1)
                loadLikesComments(summitIds)
            }
            $("time.timeago").timeago();
            $('#more-ajax-data-loading').css('display', 'none');
            $('#more-summits-btn').html('See more summits');

            is_loading = false;

        });
    }

    function loadLikesComments(summit_ids) {
        $.getJSON('{% url "summits_latest_likes_comments" %}?summit_ids=' + summit_ids, function(data) {
            $.each( data, function( key, val ) {
                if (key=='summits') {
                    $.each(val, function (summitkey, summitval) {
                        heart = '#summitlog-like-' + summitval.id;
                        bubble = '#summitlog-comment-' + summitval.id;
                        if (summitval.you_liked != '0') {
                            $(heart).addClass('liked');
                        }
                        if (summitval.like_count != '0') {
                            $(heart).html(summitval.like_count);
                        }
                        if (summitval.comment_count != '0') {
                            $(bubble).html(summitval.comment_count);
                        }
                    });
                }
            });
        });
    }

    function loadRegionSummits(region, regionname) {
        lastRegion = region;
        lastRegionName = regionname;
        createCookie('last_region',lastRegion,365);
        createCookie('last_region_name',lastRegionName,365);
        $(".autocomplete-suggestions").hide();
        document.title = 'Latest summits in '+regionname;
        var regionval = region.split("|");
        var regiontype = regionval[1];
        var regiondata = regionval[0];
        if (regiontype == 'region') {
            var tempTitle = regionname.split(",");
            $("#region-title").html(tempTitle[0]);
            $("#hdnRegionId").val(regiondata);
            $("#hdnCountryId").val('');
            $("#hdnCityId").val('');
            $(".latest-in").html('Latest in');
            createCookie('latest_suggest_region_id',regiondata,365);
            createCookie('latest_suggest_country_id','',365);
            createCookie('latest_suggest_city_id','',365);
        } else if (regiontype == 'city') {
            var tempTitle = regionname.split(",");
            $("#region-title").html(tempTitle[0]);
            $("#hdnCityId").val(regiondata);
            $("#hdnRegionId").val('');
            $("#hdnCountryId").val('');
            $(".latest-in").html('Latest near');
            createCookie('latest_suggest_city_id',regiondata,365);
            createCookie('latest_suggest_country_id','',365);
            createCookie('latest_suggest_region_id','',365);
        } else {
            $("#region-title").html(regionname);
            $("#hdnCountryId").val(regiondata);
            $("#hdnRegionId").val('');
            $("#hdnCityId").val('');
            $(".latest-in").html('Latest in');
            createCookie('latest_suggest_country_id',regiondata,365);
            createCookie('latest_suggest_region_id','',365);
            createCookie('latest_suggest_city_id','',365);
        }
        $('#see-worldwide').html('<a href="javascript:loadWorldwide();">see world</a>');
        $('#mobile-see-worldwide').html('<a id="mobile-see-worldwide-link"><i class="fa fa-globe-americas" aria-hidden="true"></i></a>');
        $('#sq1').css('display','none');
        //$('#sq1').val('');
        $('#region-filter-button').fadeIn(300);
        createCookie('suggest_worldwide','false',365);
        loadSummits('1');
    }

    function loadWorldwide() {
        $("#hdnCountryId").val('');
        $("#hdnRegionId").val('');
        $("#hdnCityId").val('');
        $(".latest-in").html('Latest in');
        document.title = 'Latest summits in the World';
        createCookie('latest_suggest_city_id','',365);
        createCookie('latest_suggest_region_id','',365);
        createCookie('latest_suggest_country_id','',365);
        $('#mobile-see-worldwide').html('');
        if (lastRegion.length > 0) {
            $('#see-worldwide').html('<a href="javascript:loadRegionSummits(\'' + lastRegion + '\', \'' + lastRegionName + '\');">see ' + lastRegionName + '</a>');
        } else {
            $('#see-worldwide').html('');
        }
        $('#sq1').css('display','none');
        //$('#sq1').val('');
        $('#region-filter-button').fadeIn(300);
        $('#region-title').html('the World');
        createCookie('suggest_worldwide','true',365);
        loadSummits('1');
    }

    function numberWithCommas(x) {
        return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    function change_like_count(id,count){
        $('#summitlog-like-'+id).html(count+" like");
    }

    {% if user.is_authenticated %}
    function like_summit(id) {
        var likeButton = $('#summitlog-like-'+id);
        var summitID = id;
        var summitlogID = '18';
        if (likeButton.hasClass("login")){
            $.facebox( {ajax:'/accounts/login/?next=/accounts/login_reload/login/'});
        } else {
            if (likeButton.hasClass("liked")) {
                likeButton.removeClass("liked");
                likeButton.addClass("liking");
                var count = $('#summitlog-like-'+summitID).html();
                count = parseInt(count)-1;
                if (count > 0) {
                    $('#summitlog-like-'+summitID).html(count);
                } else {
                    $('#summitlog-like-'+summitID).html('&nbsp;');
                }
                var request = $.ajax({
                  method: "POST",
                  url: "/favorites/remove/",
                  dataType: "json",
                  data: {object_id: summitID, content_type_id: summitlogID}
                });
                request.done(function( data ) {
                  //$('#summitlog-like-'+summitID).html(data.count);
                  likeButton.removeClass("liking");
                });
            } else {
                likeButton.addClass("liked");
                likeButton.addClass("liking");
                var count = $('#summitlog-like-'+summitID).html();
                if (count == '&nbsp;') {
                    count = 1;
                } else {
                    count = parseInt(count)+1;
                }
                $('#summitlog-like-'+summitID).html(count);
                var request = $.ajax({
                  method: "POST",
                  url: "/favorites/add/",
                  dataType: "json",
                  data: {object_id: summitID, content_type_id: summitlogID}
                });
                request.done(function( data ) {
                  //$('#summitlog-like-'+summitID).html(data.count);
                  likeButton.removeClass("liking");
                });
            }
        }
        return false;
    }
    {% else %}
    function like_summit(id) {
        $('#accounts-login').modal('show');
    }
    {% endif %}


    function get_gpx_distance(lat1, lon1, lat2, lon2) {
        var radlat1 = Math.PI * lat1/180
        var radlat2 = Math.PI * lat2/180
        var theta = lon1-lon2
        var radtheta = Math.PI * theta/180
        var dist = Math.sin(radlat1) * Math.sin(radlat2) + Math.cos(radlat1) * Math.cos(radlat2) * Math.cos(radtheta);
        dist = Math.acos(dist)
        dist = dist * 180/Math.PI
        dist = dist * 60 * 1.1515
        return dist
    }

    function viewSummit(url) {
        window.location.href = url;
    }

    function highlightPhotos(summitid) {
        $('.summitimg-'+summitid).addClass('hover-photos-hover');
    }

    function unhighlightPhotos(summitid) {
        $('.summitimg-'+summitid).removeClass('hover-photos-hover');
    }

    $(document).ready(function() {

        var viewport_height = $(window).height();
        var viewport_width = $(window).width();
        var footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
        var data_loading_margin = (viewport_height - footer_height) / 3;
        var photo_summitlogid = '0';
        $('.data-loading').css('margin-top', data_loading_margin);
        $(window).resize(function() {
            viewport_height = $(window).height();
            viewport_width = $(window).width();
            footer_height = $('.navbar-fixed-top').height() + $('.footer-nav').height();
            data_loading_margin = (viewport_height - footer_height) / 3;
            $('.data-loading').css('margin-top', data_loading_margin);
        });

        $("#region-filter-form").submit(function(event){
            event.preventDefault();
        });

        $('#more-summits-btn').on('click', function() {
            var page = parseInt($('#hdnPage').val());
            loadMoreSummits(page);
            $('#hdnPage').val(page+1);
        });

        //region searchbox on member homepage
        $('#sq1').autocomplete({
            serviceUrl: '/api/homepage/suggestions/?category=regions',
            minChars: 3,
            triggerSelectOnValidInput: false,
            showNoSuggestionNotice: true,
            formatResult: function(suggestion, currentValue){
                var suggestiondata = suggestion.data.split("|");
                var suggestion_id = suggestiondata[0];
                var suggestion_type = suggestiondata[1];
                var suggestion_icon = '';
                var suggestionHtml = '';
                suggestionHtml = '<div class="suggestion-div">';
                suggestionHtml = suggestionHtml + '<div class="peak-listitem-footer" style="float: left; margin-left: 0px; margin-top: 0px; padding-top: 10px; height: 50px; border-bottom-left-radius: 0px; border-bottom-right-radius: 0px;">';
                suggestionHtml = suggestionHtml + '<div class="map-tooltip-peak-name ellipsis" style="color: #333; text-align: left; padding-left: 10px; font-size: 15px; width: 490px;">' + suggestion.value + '</div>';
                suggestionHtml = suggestionHtml + '</div>';
                suggestionHtml = suggestionHtml + '</div>';
                return suggestionHtml;
            },
            onSelect: function (suggestion) {
                //alert('You selected: ' + suggestion.value + ', ' + suggestion.data);
                //window.location.href = '/'+suggestion.data;
                $('#sq1').val('');
                window.location.href = 'javascript:loadRegionSummits(\''+suggestion.data+'\', \''+suggestion.value+'\');';
            },
            onSearchStart: function (query) {
                $('#region-searchbox-icon').html('<i class="fa fa-spinner fa-spin"></i>');
                $('#region-searchbox-icon').show();
            },
            onSearchComplete: function (query, suggestions) {
                $('#region-searchbox-icon').html('');
                $('#region-searchbox-icon').hide();
                if (suggestions.length == 1) {
                //$('#sq1').val('');
                $('#sq1').autocomplete().hide();
                window.location.href = 'javascript:loadRegionSummits(\''+suggestions[0].data+'\', \''+suggestions[0].value+'\');';
                }
            },
            onHide: function (container) {
                $('#sq1').val('');
            }
        });

        $('#sq1').on('focusout', function() {
            setTimeout(function(){ $('#mobile-see-worldwide').css('display','none'); }, 100);
            $('#sq1').css('display','none');
            $('.mobile-section-title').css('display','inline');
            $('.section-title-stats').css('display','inline');
            $('#see-worldwide').css('display','inline');
            $('#region-filter-button').fadeIn(300);
        });

        $('#region-filter-button').on('click', function() {
            $('#region-filter-button').css('display','none');
            $('.mobile-section-title').css('display','none');
            $('.section-title-stats').css('display','none');
            $('#mobile-see-worldwide').css('display','inline');
            $('#see-worldwide').css('display','none');
            $('#sq1').val('');
            $('#sq1').fadeIn(300);
            $('#sq1').focus();
        });

        $('#mobile-see-worldwide').on('click', function() {
            loadWorldwide();
        });

        $('.timefilter-list').on('click', '.timefilter-item', function (e) {
            e.preventDefault();
            $("#timefilter-title").html($(this).html());
            var since = $(this).attr("href");
            $("#hdnTimeFilter").val(since);
            loadSummits('1');
        });

        {% if country.code == 'US' or country.code == 'CA' %}
            {% if region.id %}
                loadRegionSummits('{{ region.id }}|region','{{ region.name }}, {{ country.name }}');
            {% else %}
                loadRegionSummits('{{ country.id }}|country','{{ country.name }}');
            {% endif %}
        {% elif country.id %}
            loadRegionSummits('{{ country.id }}|country','{{ country.name }}');
        {% elif city.id %}
            loadRegionSummits('{{ city.id }}|city','{{ city.name }}');
        {% else %}
            createCookie('suggest_worldwide','true',365);
            loadSummits('1');
        {% endif %}

        $('#blueimp-gallery').on('open', function (event) {
            photo_summitlogid = 0;
            $('body,html').css('overflow','visible');
        });

        $("#blueimp-gallery").on('slide', function (event, index, slide) {
            var gallery = $('#blueimp-gallery').data('gallery');
            var new_photo_summitlogid = gallery.list[index].getAttribute('data-summitlogid');
            if (photo_summitlogid != '0') {
                if (new_photo_summitlogid != photo_summitlogid) {
                    gallery.close();
                }
            }
            var caption = gallery.list[index].getAttribute('data-description'),
                username = gallery.list[index].getAttribute('data-user'),
                credit = gallery.list[index].getAttribute('data-credit'),
                photo_url = gallery.list[index].getAttribute('data-photo-url'),
                caption_node = gallery.container.find('.description-text-caption');
                username_node = gallery.container.find('.description-text-user');
            caption_node.empty();
            username_node.empty();
            if (caption) {
                caption_node[0].appendChild(document.createTextNode(caption));
            }
            if (username) {
                var newdiv = document.createElement('div');
                if (credit) {
                    newdiv.innerHTML = '<a style="color: #fff;" target="_blank" href="' + photo_url + '/"><i style="margin-right: 5px;" class="fa fa-camera" aria-hidden="true"></i> ' + username + ' ' + credit + '</a>';
                } else {
                    newdiv.innerHTML = '<a style="color: #fff;" href="/members/' + username + '/"><i style="margin-right: 5px;" class="fa fa-camera" aria-hidden="true"></i> ' + username;
                }
                username_node[0].appendChild(newdiv);
            }
            photo_summitlogid = gallery.list[index].getAttribute('data-summitlogid');
        });

        //Infinite Scroll
        $(window).on("scroll", function() {
            //page height
            var scrollHeight = $(document).height();
            //scroll position
            var scrollPos = $(window).height() + $(window).scrollTop();
            // fire if the scroll position is 300 pixels above the bottom of the page
            if(((scrollHeight - 300) >= scrollPos) / scrollHeight == 0 && is_loading == false){
                $('#more-summits-btn').click();
            }
        });

    });

</script>

{% endblock %}

{% block gallery %}
<div id="blueimp-gallery" class="blueimp-gallery blueimp-gallery-controls" data-use-bootstrap-modal="false" data-slideshow-interval="4000">
    <!-- The container for the modal slides -->
    <div class="slides"></div>
    <!-- Controls for the borderless lightbox -->
    <h3 class="title"></h3>
    <div class="description">
        <div class="description-text">
            <div class="description-text-caption"></div>
            <div class="description-text-user"></div>
        </div>
    </div>
    <div class="splash-badge-content"></div>
    <a class="prev">‹</a>
    <a class="next">›</a>
    <a class="close">×</a>
    <a class="play-pause"></a>
    <ol class="indicator"></ol>
    <!-- The modal dialog, which will be used to wrap the lightbox content -->
    <div class="modal fade">
        <div class="modal-dialog" style="width: 80%;">
            <div class="modal-content">
                <div class="modal-header">
                    <button style="color: #fff;" type="button" class="close" aria-hidden="true">&times;</button>
                    <h4 class="modal-title"></h4>
                </div>
                <div class="modal-body next"></div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default pull-left prev">
                        <i class="glyphicon glyphicon-chevron-left"></i>
                        Previous
                    </button>
                    <button type="button" class="btn btn-primary next">
                        Next
                        <i class="glyphicon glyphicon-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}