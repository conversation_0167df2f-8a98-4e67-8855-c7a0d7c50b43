<div class="action">
    <div class="a">
        <div class="peakimg" style="background: url('{{ peak.get_thumbnail_160_crop }}')">
            <span class="peakname">
                <span class="name">{{ peak.name }}</span>
                <span class="info-elevation">{{ peak.get_elevation }}</span>
            </span><!-- ENd peakname -->
        </div><!-- ENd peakimg -->
    </div>
    {% if summit_times %}
        <div class="b">
            <h1>You summited <span class="green-text">{{ summit_times }} time{{ summit_times|pluralize }}</span>.</h1>
            <h1>Did you summit again?</h1>
            <a class="btn set3" id="bag1" rel="facebox" href="{% url "summit_1" peak.id %}"><span style="font-size: 30px; ">YES I DID!</span></a>
        </div>
    {% else %}
        <div class="b">
            <h1>Did you summit?</h1>
            <h4>Sign the log and get the summit badge</h4>
            <a class="btn set3" id="bag1" rel="facebox" href="{% url "summit_1" peak.id %}"><span style="font-size: 30px; ">YES I DID!</span></a>
        </div>
    {% endif %}

</div>