{% load avatar_tags %}
<script type="text/javascript">
    $(function(){
        $.getScript("{{ MEDIA_URL }}js/jquery.OnEnter.js");
    });
</script>
<li id="comment_form">
    <div id="comments_output" style="display: none"></div>
    <form id="add-comment" method="post" action="{% url "comments" item_id %}">
        <a name="comments"></a>
        <div class="image">
            {% avatar user 40 %}
            <span class="image_desc">{% if user.is_authenticated %}{{ user }}{% else %}{% endif %}</span>
        </div>
        <div class="comment_box">
            <div class="arrow"></div>
                <span class="comment_area" style="position: relative;">
                    <span class="spinner" style="width: 32px; height: 32px; background: url('{{ MEDIA_URL }}img/misc/loading.gif') no-repeat center center; position: absolute; left: 226px; top: -56px; display: none;"></span>
                    <textarea name="comment" id="comment" class="blur" onload="removeVisaje(this);" onenter="sendComment(this);">Write your comment</textarea>
                </span>
        </div>
    </form>
</li><!-- END comment -->