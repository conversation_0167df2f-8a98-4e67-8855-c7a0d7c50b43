1. Actualizar base de datos de repositorios: sudo aptitude update
2. Instalar estos paquetes: sudo apt-get install postgresql-plperl-9.0 unaccent libtext-unaccent-perl libunac1-dev
3. Loguearse como usuario postgres: sudo su - postgres
4. Instalar lenguaje perl para postgres: createlang plperlu dbname
5. Salirse del usuario postgres: Ctrl + C luego Ctrl + D
6. Ejecutar consola postgresql: psql db_name
7. Ejecutar este query:

CREATE FUNCTION unaccent(charset text, string text) RETURNS text AS $$
use Text::Unaccent;
return unac_string($_[0], $_[1]);
$$ LANGUAGE plperlu IMMUTABLE STRICT;

8. Probar query:

SELECT unaccent('UTF-8', 'ěščřžýáíé');

9. Ejecutar migracion: python manage.py migrate items
10. Ejecutar comando para arreglar names: python manage.py generate_unaccented_names
11. Sobreescribir en los site-packages el archivo django/contrib/gis/db/backends/postgis/base.py con esto:

from django.db.backends.postgresql_psycopg2.base import *
from django.db.backends.postgresql_psycopg2.base import DatabaseWrapper as Psycopg2DatabaseWrapper
from django.contrib.gis.db.backends.postgis.creation import PostGISCreation
from django.contrib.gis.db.backends.postgis.introspection import PostGISIntrospection
from django.contrib.gis.db.backends.postgis.operations import PostGISOperations

class DatabaseOperations(DatabaseOperations):
    def lookup_cast(self, lookup_type):
        if lookup_type in('icontains', 'istartswith'):
            return "UPPER(unaccent('UTF8', %s::text))"
        else:
            return super(DatabaseOperations, self).lookup_cast(lookup_type)

class DatabaseWrapper(Psycopg2DatabaseWrapper):
    def __init__(self, *args, **kwargs):
        super(DatabaseWrapper, self).__init__(*args, **kwargs)
        self.creation = PostGISCreation(self)
        self.operators['icontains'] = 'LIKE UPPER(unaccent(\'UTF8\', %s))'
        self.operators['istartswith'] = 'LIKE UPPER(unaccent(\'UTF8\', %s))'
        self.ops = PostGISOperations(self)
        self.introspection = PostGISIntrospection(self)


12. Reiniciar Peakery