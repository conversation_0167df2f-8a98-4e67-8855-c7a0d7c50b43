__author__ = 'luispallareslopez'
from items.models import Item
from django.contrib.gis.geos import Point

def run():
    items = Item.objects.filter(location=None)
    for item in items:
        lat = float( item.lat )
        lon = float( item.long )
        point = Point(lat, lon)
        item.location = point
        #item.save()
        print item
        print item.location.coords
        print '=' * 100
