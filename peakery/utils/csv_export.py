from django.utils.encoding import smart_str
import datetime
first_date = datetime.date(2000, 1, 31)
last_date = datetime.date(2016, 1, 31)

data = []


c = 0
l = User.objects.all().count()

for u in User.objects.all():
  c += 1
  d = {}
  if u.person_set.all():
    p = u.person_set.all()[0]
    d['pk'] = u.pk
    d['username'] = smart_str(u.username)
    d['email'] = u.email
    d['date_joined'] = u.date_joined
    d['last_login'] = u.last_login

    # summit logs added w/in date range
    d['summit_logs_added'] = u.summit_log.filter(created__range=(first_date, last_date)).count()

    # summit logs *dated* w/in date range
    d['summit_logs_dated'] = u.summit_log.filter(date_entered=True, date__range=(first_date, last_date)).count()

    # of likes
    d['likes'] = u.favorite_set.filter(created_on__range=(first_date, last_date)).count()

    # of comments
    d['item_comments'] = u.item_comments.filter(created__range=(first_date, last_date)).count()
    d['summit_comments'] = u.summit_comments.filter(created__range=(first_date, last_date)).count()

    # data corrections
    d['item_corrections'] = u.items_corrections.filter(created__range=(first_date, last_date)).count()

    # peaks added
    d['peaks_added'] = u.items.filter(created__range=(first_date, last_date)).count()

    total_photos = u.item_photos.filter(created__range=(first_date, last_date)).count()
    # peak photos added
    d['peak_photos_added'] = u.item_photos.filter(created__range=(first_date, last_date)).exclude(summit_log_id__isnull=True).count()
    # summit log photos added
    d['summit_log_photos_added'] = total_photos - d['peak_photos_added']

    d['following'] = u.following.count()
    d['followers'] = len(u.followers())
    regions = p.top_climbing_regions()
    d['region_1_name'] = ''
    d['region_1_count'] = 0
    d['region_2_name'] = ''
    d['region_2_count'] = 0
    d['region_3_name'] = ''
    d['region_3_count'] = 0

    try:

      d['region_1_name'] = smart_str(regions[0][0])
      d['region_1_count'] = regions[0][1]
      d['region_2_name'] = smart_str(regions[1][0])
      d['region_2_count'] = regions[1][1]
      d['region_3_name'] = smart_str(regions[2][0])
      d['region_3_count'] = regions[2][1]
    except:
      pass

    d['has_avatar'] = p.has_avatar()
    d['has_facebook'] = u.facebookprofile_set.count() > 0
    d['about'] = p.about_me
    if d['about']:
      d['about'] = smart_str(d['about'].strip())
    else:
      d['about'] = ''
    print '%s of %s' % (c, l)

    data.append(d)


import csv

fieldnames = ['pk', 'username', 'email', 'date_joined', 'last_login', 'summit_logs_added', 'summit_logs_dated', 'likes', 'item_comments', 'summit_comments', 'item_corrections',
              'peaks_added', 'peak_photos_added', 'summit_log_photos_added', 'following', 'followers', 'region_1_name', 'region_1_count', 'region_2_name', 'region_2_count',
              'region_3_name', 'region_3_count', 'has_avatar', 'has_facebook', 'about']
 
#----------------------------------------------------------------------
def csv_dict_writer(path, fieldnames, data):
  """
  Writes a CSV file using DictWriter
  """
  with open(path, "wb") as out_file:
    writer = csv.DictWriter(out_file, delimiter=',', fieldnames=fieldnames, quotechar='"', quoting=csv.QUOTE_ALL)
    writer.writerow(dict((fn,fn) for fn in fieldnames))
    for row in data:
      try:
        writer.writerow(row)
      except Exception as e:
        print str(e)
        print row

path = "user_stats.csv"
csv_dict_writer(path, fieldnames, data)
