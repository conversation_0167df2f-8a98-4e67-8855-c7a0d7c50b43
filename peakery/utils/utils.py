import datetime
from peakery.items.models import Item, ItemCountry
from django.template.defaultfilters import slugify


# dictfectall for cursors
def dictfetchall(cursor):
    "Return all rows from a cursor as a dict"
    columns = [col[0] for col in cursor.description]
    return [
        dict(zip(columns, row))
        for row in cursor.fetchall()
    ]


def fix_elevation_items():
    # The Google Elevation API Has a 2500 hit limitation
    items = Item.objects.filter(elevation=0, created__gt=datetime.date(2011, 5, 26)).order_by('id')[:500]
    for item in items:
        print("Fixing Item: %s" % (item.name))
        item.fix_elevation_from_point()


def fix_item_with_bad_name():
    items = Item.objects.filter(name__contains=u'\u251c\u2592')
    for item in items:
        item.name = item.name.replace(u"\u251c\u2592", u"\xF1")
        item.slug = slugify(item.name)
        item.save()


def fix_items_without_country():
    items = Item.objects.filter(has_country=False, has_region=True)
    for item in items:
        print("Fixing Item:%s" % item)
        for region in item.region.all():
            print("Using Region: %s and Country: %s" % (region, region.country))
            itemcountry = ItemCountry(item=item, country=region.country)
            itemcountry.save()
    print("Done")


def reduce_gpx_points(gpx):
    total_points = 0
    if gpx.tracks:
        for track in gpx.tracks:
            for segment in track.segments:
                total_points += len(segment.points)
    elif gpx.routes:
        for route in gpx.routes:
            total_points += len(route.points)

    if total_points <= 7200:
        reduction_factor = 10
    else:
        reduction_factor = 16
    gpx.reduce_points(None, reduction_factor)
