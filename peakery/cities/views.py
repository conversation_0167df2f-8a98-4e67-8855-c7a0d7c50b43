from django.http import HttpResponse
import json
from peakery.cities.models import City, Region

def autocomplete_city(request):
    term = request.GET.get('term')
    res = []
    if term is not None:
        cities = City.objects.filter(name__istartswith=term)
        for c in cities:
            dict = {'id':c.id, 'label':c.__unicode__(), 'value':c.__unicode__()}
            res.append(dict)
    return HttpResponse(json.dumps(res))

def get_regions_by_country(request):
    country_id = request.GET.get('country_id')
    res = []
    if country_id is not None:
        country_id = int(country_id)
        regions = Region.objects.filter(country = country_id).order_by('name')
        res = [{'id':'','label':'All regions'}]
        for region in regions:
            dict = {'id':region.id,'label':region.name}
            res.append(dict)
    return HttpResponse(json.dumps(res))