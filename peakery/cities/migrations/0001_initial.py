# Generated by Django 3.2 on 2024-02-20 11:22

import datetime
from django.conf import settings
import django.contrib.gis.db.models.fields
from django.db import migrations, models
import django.db.models.deletion
import peakery.django_extensions.db.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='City',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('slug', models.CharField(blank=True, db_index=True, max_length=200, null=True)),
                ('location', django.contrib.gis.db.models.fields.PointField(srid=4326)),
                ('location_x', models.DecimalField(db_index=True, decimal_places=8, max_digits=11, verbose_name='Latitude')),
                ('location_y', models.DecimalField(db_index=True, decimal_places=8, max_digits=11, verbose_name='Longitude')),
                ('population', models.IntegerField()),
            ],
            options={
                'verbose_name_plural': 'cities',
                'ordering': ('name',),
            },
        ),
        migrations.CreateModel(
            name='Continent',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('code', models.CharField(db_index=True, max_length=2)),
                ('slug', models.CharField(max_length=255, unique=True)),
                ('lat', models.FloatField(blank=True, null=True, verbose_name='Latitude')),
                ('long', models.FloatField(blank=True, null=True, verbose_name='Longitude')),
            ],
            options={
                'ordering': ('name',),
            },
        ),
        migrations.CreateModel(
            name='Country',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('code', models.CharField(db_index=True, max_length=2)),
                ('population', models.IntegerField()),
                ('continent', models.CharField(max_length=2)),
                ('tld', models.CharField(max_length=5, unique=True)),
                ('slug', models.CharField(max_length=255, unique=True)),
                ('lat', models.FloatField(blank=True, null=True, verbose_name='Latitude')),
                ('long', models.FloatField(blank=True, null=True, verbose_name='Longitude')),
                ('map_zoom', models.IntegerField(blank=True, null=True)),
                ('show_in_challenges', models.BooleanField(default=False, help_text='Indicate if a Country should display on World Challenges view.', verbose_name='Show In World Challenges')),
            ],
            options={
                'verbose_name_plural': 'Countries',
                'ordering': ('name',),
            },
        ),
        migrations.CreateModel(
            name='Region',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('slug', models.CharField(db_index=True, max_length=200)),
                ('code', models.CharField(db_index=True, max_length=10)),
                ('lat', models.FloatField(blank=True, null=True, verbose_name='Latitude')),
                ('long', models.FloatField(blank=True, null=True, verbose_name='Longitude')),
                ('map_zoom', models.IntegerField(blank=True, null=True)),
                ('show_in_challenges', models.BooleanField(default=False, help_text='Indicate if a Region should display on World Challenges view.', verbose_name='Show In World Challenges')),
                ('google_region_name', models.CharField(blank=True, max_length=255, null=True)),
                ('google_region_name_alt', models.CharField(blank=True, max_length=255, null=True)),
                ('country', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='regions', to='cities.country')),
            ],
            options={
                'ordering': ('name',),
            },
        ),
        migrations.CreateModel(
            name='RegionHighlightLogGroup',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('log_date', peakery.django_extensions.db.fields.CreationDateTimeField(blank=True, default=datetime.datetime.now, editable=False)),
                ('region', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='highlights_log_group', to='cities.region')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='region_highlights_log_group', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='RegionHighlightLog',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('highlight', models.TextField()),
                ('log_group_id', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='region_highlights_log', to='cities.regionhighlightloggroup')),
            ],
        ),
        migrations.CreateModel(
            name='RegionHighlight',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('highlight', models.TextField()),
                ('created', peakery.django_extensions.db.fields.CreationDateTimeField(blank=True, default=datetime.datetime.now, editable=False)),
                ('modified', peakery.django_extensions.db.fields.ModificationDateTimeField(blank=True, default=datetime.datetime.now, editable=False)),
                ('region', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='highlights', to='cities.region')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='region_highlights', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='District',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('slug', models.CharField(db_index=True, max_length=200)),
                ('location', django.contrib.gis.db.models.fields.PointField(srid=4326)),
                ('population', models.IntegerField()),
                ('location_x', models.DecimalField(db_index=True, decimal_places=8, max_digits=11)),
                ('location_y', models.DecimalField(db_index=True, decimal_places=8, max_digits=11)),
                ('city', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, to='cities.city')),
            ],
        ),
        migrations.CreateModel(
            name='CountryHighlightLogGroup',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('log_date', peakery.django_extensions.db.fields.CreationDateTimeField(blank=True, default=datetime.datetime.now, editable=False)),
                ('country', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='highlights_log_group', to='cities.country')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='country_highlights_log_group', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='CountryHighlightLog',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('highlight', models.TextField()),
                ('log_group_id', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='country_highlights_log', to='cities.countryhighlightloggroup')),
            ],
        ),
        migrations.CreateModel(
            name='CountryHighlight',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('highlight', models.TextField()),
                ('created', peakery.django_extensions.db.fields.CreationDateTimeField(blank=True, default=datetime.datetime.now, editable=False)),
                ('modified', peakery.django_extensions.db.fields.ModificationDateTimeField(blank=True, default=datetime.datetime.now, editable=False)),
                ('country', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='highlights', to='cities.country')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='country_highlights', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='ContinentHighlightLogGroup',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('log_date', peakery.django_extensions.db.fields.CreationDateTimeField(blank=True, default=datetime.datetime.now, editable=False)),
                ('continent', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='highlights_log_group', to='cities.continent')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='continent_highlights_log_group', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='ContinentHighlightLog',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('highlight', models.TextField()),
                ('log_group_id', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='continent_highlights_log', to='cities.continenthighlightloggroup')),
            ],
        ),
        migrations.CreateModel(
            name='ContinentHighlight',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('highlight', models.TextField()),
                ('created', peakery.django_extensions.db.fields.CreationDateTimeField(blank=True, default=datetime.datetime.now, editable=False)),
                ('modified', peakery.django_extensions.db.fields.ModificationDateTimeField(blank=True, default=datetime.datetime.now, editable=False)),
                ('continent', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='highlights', to='cities.continent')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='continent_highlights', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='CityHighlightLogGroup',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('log_date', peakery.django_extensions.db.fields.CreationDateTimeField(blank=True, default=datetime.datetime.now, editable=False)),
                ('city', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='highlights_log_group', to='cities.city')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='city_highlights_log_group', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='CityHighlightLog',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('highlight', models.TextField()),
                ('log_group_id', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='city_highlights_log', to='cities.cityhighlightloggroup')),
            ],
        ),
        migrations.CreateModel(
            name='CityHighlight',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('highlight', models.TextField()),
                ('created', peakery.django_extensions.db.fields.CreationDateTimeField(blank=True, default=datetime.datetime.now, editable=False)),
                ('modified', peakery.django_extensions.db.fields.ModificationDateTimeField(blank=True, default=datetime.datetime.now, editable=False)),
                ('city', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='highlights', to='cities.city')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='city_highlights', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='city',
            name='region',
            field=models.ForeignKey(on_delete=django.db.models.deletion.DO_NOTHING, related_name='cities', to='cities.region'),
        ),
    ]
