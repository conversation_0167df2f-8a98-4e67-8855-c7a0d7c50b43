from django.conf import settings
from django.urls import resolve


def IS_MOBILE_APP_ACCESS(request):
    if request.META.get('HTTP_PEAKMOBILEAPP', None) == 'com.peakery.android' or request.META.get('HTTP_PEAKMOBILEAPP', None) == 'com.peakery.ios' or request.META.get('HTTP_HTTP_PEAKMOBILEAPP', None) == 'com.peakery.android' or request.META.get('HTTP_HTTP_PEAKMOBILEAPP', None) == 'com.peakery.ios':
        return {'IS_MOBILE_APP_ACCESS': 'True'}
    else:
        return {'IS_MOBILE_APP_ACCESS': 'False'}


def MOBILE_APP_BACK(request):
    if request.META.get('HTTP_PEAKMOBILEAPP', None) == 'com.peakery.android' or request.META.get('HTTP_PEAKMOBILEAPP', None) == 'com.peakery.ios':
        if request.META.get('HTTP_REFERER', None):
            # if we have browser history then back arrow will be present
            return {'MOBILE_APP_BACK': 'True'}
        else:
            #if user profile or latest summits page, then no back arrow
            url_name = None
            try:
                url_name = resolve(request.path_info).url_name
            except:
                pass
            if url_name:
                if url_name == 'user_profile' or url_name == 'latest':
                    return {'MOBILE_APP_BACK': 'False'}
                else:
                    return {'MOBILE_APP_BACK': 'True'}
            else:
                # assume we have mobile app back arrow
                return {'MOBILE_APP_BACK': 'True'}
    else:
        return {'MOBILE_APP_BACK': 'False'}


def read_only_helper(request):
    return {'IS_READ_ONLY': settings.IS_READ_ONLY}


def ssi(request):
    return {'SSI_ENABLED': settings.SSI_ENABLED}


def S3_URL(request):
    return {'S3_URL': settings.S3_URL}


def S3_MEDIA_URL(request):
    return {'S3_MEDIA_URL': settings.S3_MEDIA_URL}


def API_URL(request):
    return {'API_URL': settings.API_URL}


def SITE_URL(request):
    return {'SITE_URL': settings.SITE_URL}