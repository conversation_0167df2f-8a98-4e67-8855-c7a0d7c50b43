from django.core.cache import cache
from django.http import Http404
from django.http import HttpResponse
from django.template.context import RequestContext
from django.shortcuts import render
from django.shortcuts import get_object_or_404
from peakery.accounts.models import Person
from peakery.cities.models import Continent, Country, Region, ContinentHighlight, ContinentHighlightLog, ContinentHighlightLogGroup, CountryHighlight, CountryHighlightLog, CountryHighlightLogGroup, RegionHighlight, RegionHighlightLog, RegionHighlightLogGroup
from django.contrib.auth.models import User
from peakery.items.models import Item, ItemRegion, SummitLog, ItemGroup, PeakRoute, ItemHighlight, ItemHighlightLogGroup, ItemHighlightLog, ItemGroupHighlight, ItemGroupHighlightLogGroup, ItemGroupHighlightLog, PeakRouteHighlight, PeakRouteHighlightLogGroup, PeakRouteHighlightLog, ItemCountry
from peakery.favorites.models import Favorite
from django.conf import settings
from django.core.exceptions import FieldError
from operator import itemgetter
from django.contrib.admin.views.decorators import staff_member_required
from peakery.items.models import STATUS_SUMMIT_ACTIVE, STATUS_SUMMIT_PENDING, STATUS_ROUTE_ACTIVE, STATUS_ROUTE_PENDING
import locale, string, datetime

from peakery.main.services import mapbox_thumbnail_service

locale.setlocale(locale.LC_ALL, settings.LOCALE)
from django.db import connection
from peakery.accounts.models import UserContributorStats

def dictfetchall(cursor):
    "Return all rows from a cursor as a dict"
    columns = [col[0] for col in cursor.description]
    return [
        dict(zip(columns, row))
        for row in cursor.fetchall()
    ]

@staff_member_required
def tools_home(request):
    return render(request, 'tools/tools_home.html', {})


@staff_member_required
def routes_cleanup(request):

    sort = request.GET.get('sort', '').lower()
    keyword = request.GET.get('keyword', '').lower()

    if sort == 'peak':
        sql = "select a.id, a.name, count(distinct b.id) as summit_count, count(distinct d.id) as route_count, max(d.modified) as last_modified " + \
            "from items_item a " + \
            "left join items_summitlog b on b.item_id = a.id " + \
            "left join items_peakroute d on d.item_id = a.id " + \
            "where d.status = 1 " + \
            "@KEYWORD " + \
            "group by a.id, a.name " + \
            "order by a.name asc limit 250 "
    elif sort == 'routes':
        sql = "select a.id, a.name, count(distinct b.id) as summit_count, count(distinct d.id) as route_count, max(d.modified) as last_modified " + \
            "from items_item a " + \
            "left join items_summitlog b on b.item_id = a.id " + \
            "left join items_peakroute d on d.item_id = a.id " + \
            "where d.status = 1 " + \
            "@KEYWORD " + \
            "group by a.id, a.name " + \
            "order by count(distinct d.id) desc limit 250 "
    elif sort == 'modified':
        sql = "select a.id, a.name, count(distinct b.id) as summit_count, count(distinct d.id) as route_count, max(d.modified) as last_modified " + \
            "from items_item a " + \
            "left join items_summitlog b on b.item_id = a.id " + \
            "left join items_peakroute d on d.item_id = a.id " + \
            "where d.status = 1 " + \
            "@KEYWORD " + \
            "group by a.id, a.name " + \
            "order by max(d.modified) asc limit 250 "
    else:
        sql = "select a.id, a.name, count(distinct b.id) as summit_count, count(distinct d.id) as route_count, max(d.modified) as last_modified " + \
            "from items_item a " + \
            "left join items_summitlog b on b.item_id = a.id " + \
            "left join items_peakroute d on d.item_id = a.id " + \
            "where d.status = 1 " + \
            "@KEYWORD " + \
            "group by a.id, a.name " + \
            "order by count(distinct b.id) desc limit 250 "

    if len(keyword) > 0:
        search_keyword = "%" + keyword + "%"
        sql = sql.replace("@KEYWORD", "and lower(a.name) like %s ")
    else:
        sql = sql.replace("@KEYWORD", "")

    sql_params = []
    if len(keyword) > 0:
        sql_params.append(search_keyword)

    with connection.cursor() as cursor:

        cursor.execute(sql, sql_params)

        peaks = dictfetchall(cursor)

    return render(request, 'tools/routes_cleanup.html', {
        'peaks':peaks,
        'peak_keyword':keyword
    })

@staff_member_required
def user_report(request):
    import csv

    sql = "select " + \
        "a.id as user_id, " + \
        "a.username, " + \
        "a.first_name, " + \
        "a.last_name, " + \
        "a.email, " + \
        "a.date_joined, " + \
        "b.signup_source, " + \
        "case when f.date_of_last_activity isnull then 'N' else 'Y' end as active_on_android, " + \
        "c.total_climbs, " + \
        "coalesce(d.climbs_last_365_days,0) as climbs_last_365_days, " + \
        "e.date_of_last_climb " + \
        "from " + \
        "auth_user a " + \
        "join accounts_person b on b.user_id = a.id " + \
        "join (select x.user_id, count(x.id) as total_climbs from items_summitlog x where x.status = 1 and x.attempt = false group by x.user_id) c on c.user_id = a.id " + \
        "left join (select x.user_id, count(x.id) as climbs_last_365_days from items_summitlog x where x.status = 1 and x.attempt = false and x.date >= '2019-11-12' group by x.user_id) d on d.user_id = a.id " + \
        "join (select x.user_id, max(x.date) as date_of_last_climb from items_summitlog x where x.status = 1 and x.attempt = false group by x.user_id) e on e.user_id = a.id " + \
        "left join (select x.user_id, max(x.activity_date) as date_of_last_activity from activity_log x where x.activity_source = 'Android' group by x.user_id) f on f.user_id = a.id " + \
        "union " + \
        "select " + \
        "a.id as user_id, " + \
        "a.username, " + \
        "a.first_name, " + \
        "a.last_name, " + \
        "a.email, " + \
        "a.date_joined, " + \
        "b.signup_source, " + \
        "case when f.date_of_last_activity isnull then 'N' else 'Y' end as active_on_android, " + \
        "0 as total_climbs, " + \
        "0 as climbs_last_365_days, " + \
        "null as date_of_last_climb " + \
        "from " + \
        "auth_user a " + \
        "join accounts_person b on b.user_id = a.id " + \
        "left join (select x.user_id, max(x.activity_date) as date_of_last_activity from activity_log x where x.activity_source = 'Android' group by x.user_id) f on f.user_id = a.id " + \
        "where not exists (select 1 from items_summitlog y where y.user_id = a.id and y.status = 1 and y.attempt = false) "

    with connection.cursor() as cursor:

        cursor.execute(sql)

        users = dictfetchall(cursor)

    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="user_report.csv"'

    writer = csv.writer(response)
    writer.writerow(['user_id','username','first_name','last_name','email','signup_date','signup_source','active_on_android','total_climbs','climbs_last_365_days','date_of_last_climb'])
    for u in users:
        writer.writerow([u['user_id'],u['username'],u['first_name'],u['last_name'],u['email'],u['date_joined'],u['signup_source'],u['active_on_android'],u['total_climbs'],u['climbs_last_365_days'],u['date_of_last_climb']])

    return response

@staff_member_required
def peak_data_report(request):
    import csv
    import collections

    sql = """
        select c.name as country_name, a.name as region_name, a.id as region_id, count(d.id) as location_edited, count(e.id) as location_unedited, count(f.id) as elevation_edited, count(g.id) as elevation_unedited
        from cities_region a 
        join items_item_region b on a.id = b.region_id 
        join cities_country c on a.country_id = c.id 
        left join items_item d on b.item_id = d.id and d.location_edited = true and d.active = true 
        left join items_item e on b.item_id = e.id and e.location_edited = false and e.active = true 
        left join items_item f on b.item_id = f.id and f.elevation_edited = true and f.active = true 
        left join items_item g on b.item_id = g.id and g.elevation_edited = false and g.active = true 
        group by c.name, a.name, a.id
        order by c.name, a.name
    """

    with connection.cursor() as cursor:
        cursor.execute(sql)
        peaks = dictfetchall(cursor)

    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="peak_data_report.csv"'

    writer = csv.writer(response)
    writer.writerow(['country','region','location_edited','location_unedited','elevation_edited','elevation_unedited','has_prominence','has_range'])

    sql = """  
        SELECT cr.id as region_id, COUNT(*) as region_count
        FROM items_item ii, items_item_region iir, cities_region cr
        WHERE iir.item_id = ii.id 
        AND cr.id = iir.region_id
        AND ii.active = true
        AND ii.range IS NOT NULL
        GROUP BY cr.id;
    """

    range_count_per_region = collections.defaultdict(int)
    with connection.cursor() as cursor:
        cursor.execute(sql)
        for row in cursor.fetchall():
            range_count_per_region[row[0]] = row[1]


    sql = """  
        SELECT cr.id as region_id, COUNT(*) as region_count
        FROM items_item ii, items_item_region iir, cities_region cr
        WHERE iir.item_id = ii.id 
        AND cr.id = iir.region_id
        AND ii.active = true
        AND ii.prominence IS NOT NULL
        GROUP BY cr.id;
    """

    prominence_count_per_region = collections.defaultdict(int)
    with connection.cursor() as cursor:
        cursor.execute(sql)
        for row in cursor.fetchall():
            prominence_count_per_region[row[0]] = row[1]

    for p in peaks:
        writer.writerow([p['country_name'],p['region_name'],p['location_edited'],p['location_unedited'],p['elevation_edited'],p['elevation_unedited'], prominence_count_per_region[p['region_id']], range_count_per_region[p['region_id']]])

    return response


@staff_member_required
def peak_routes_cleanup(request, peak_id):

    peak = Item.objects.get(id=peak_id)
    sort = request.GET.get('sort', '').lower()

    if request.method == 'POST':
        #process add route form
        route_ids = request.POST.getlist('chkRoutes')
        primary_route_id = request.POST.get('rdoRoutes')
        new_route_name = request.POST.get('txtNewRouteName')

        primary_route = PeakRoute.objects.get(id=primary_route_id)

        route = PeakRoute(user=request.user, item=peak, name=new_route_name, status = STATUS_ROUTE_ACTIVE, difficulty = primary_route.difficulty, start_location = primary_route.start_location, gpx_file = primary_route.gpx_file, gpx_start_index = primary_route.gpx_start_index, gpx_end_index = primary_route.gpx_end_index, gpx_start_lat = primary_route.gpx_start_lat, gpx_start_lon = primary_route.gpx_start_lon, gpx_end_lat = primary_route.gpx_end_lat, gpx_end_lon = primary_route.gpx_end_lon, distance_to_summit = primary_route.distance_to_summit, start_elevation = primary_route.start_elevation, elevation_gain = primary_route.elevation_gain, route_metadata = primary_route.route_metadata, getting_there = primary_route.getting_there, red_tape = primary_route.red_tape)
        encoded_mapbox_polyline = mapbox_thumbnail_service.get_encoded_polyline_for_mapbox(route.gpx_file)
        route.encoded_polyline = encoded_mapbox_polyline
        route.save()
        new_route_id = route.id

        for r in route_ids:
            #update summit routes to this new route we created
            sql = "update items_summitroute set peak_route_id = %s where peak_route_id = %s"
            with connection.cursor() as cursor:
                cursor.execute(sql, [new_route_id, r])

            #update summit logs to this new route we created
            sql = "update items_summitlog set peak_route_id = %s where peak_route_id = %s"
            with connection.cursor() as cursor:
                cursor.execute(sql, [new_route_id, r])

            #update new route to pending status (to hide it) and set the combined route id
            sql = "update items_peakroute set status = 2, combined_route_id = %s where id = %s"
            with connection.cursor() as cursor:
                cursor.execute(sql, [new_route_id, r])

    if sort == 'route':
        sql = "select a.id, a.name as route_name, count(b.id) as summit_count " + \
            "from items_peakroute a " + \
            "left join items_summitlog b on b.peak_route_id = a.id " + \
            "where a.item_id = %s " + \
            "and a.status = 1 " + \
            "group by a.id, a.name " + \
            "order by a.name asc "
    else:
        sql = "select a.id, a.name as route_name, count(b.id) as summit_count " + \
            "from items_peakroute a " + \
            "left join items_summitlog b on b.peak_route_id = a.id " + \
            "where a.item_id = %s " + \
            "and a.status = 1 " + \
            "group by a.id, a.name " + \
            "order by count(b.id) desc "

    with connection.cursor() as cursor:

        cursor.execute(sql, [peak_id])

        routes = dictfetchall(cursor)

    new_routes = PeakRoute.objects.filter(item_id=peak.id)

    return render(request, 'tools/peak_routes_cleanup.html', {
        'peak':peak,
        'routes':routes,
        'peak_id':peak_id,
        'new_routes':new_routes
    })

@staff_member_required
def combine_peaks(request):

    from_peak_id = request.POST.get('from_peak_id', '')
    to_peak_id = request.POST.get('to_peak_id', '')

    process_from_peak_id = request.POST.get('process_from_peak_id', '')
    process_to_peak_id = request.POST.get('process_to_peak_id', '')
    process_action = request.POST.get('hdnAction', '')

    from_peak = None
    to_peak = None
    message = None

    if request.method == 'POST':
        if process_from_peak_id and process_to_peak_id and process_action == 'combine':
            #process combine peaks form
            from_peak = Item.objects.get(id=process_from_peak_id)
            to_peak = Item.objects.get(id=process_to_peak_id)

            #update summit logs to the new peak
            sql = "update items_summitlog set item_id = %s where item_id = %s"
            with connection.cursor() as cursor:
                cursor.execute(sql, [process_to_peak_id, process_from_peak_id])

            # update photos to the new peak
            sql = "update items_itemphoto set item_id = %s where item_id = %s"
            with connection.cursor() as cursor:
                cursor.execute(sql, [process_to_peak_id, process_from_peak_id])

            # delete the old peak
            from_peak.delete()

            message = "Combined %s into %s." % (from_peak.name, to_peak.name)
        elif from_peak_id and to_peak_id:
            from_peak = Item.objects.get(id=from_peak_id)
            to_peak = Item.objects.get(id=to_peak_id)

    return render(request, 'tools/combine_peaks.html', {
        'from_peak':from_peak,
        'to_peak':to_peak,
        'message':message
    })

@staff_member_required
def highlights_log(request):

    keyword = request.GET.get('keyword', '').lower()

    sql = "select 'peak' as item_type, concat('/',c.slug_new_text) as item_url, c.id as item_id, c.name as item_name, b.username, a.log_date as last_modified, string_agg(d.highlight, concat(chr(10),chr(10)) order by d.id) as highlights " + \
        "from items_itemhighlightloggroup a " + \
        "join auth_user b on b.id = a.user_id " + \
        "join items_item c on c.id = a.item_id @KEYWORD " + \
        "join items_itemhighlightlog d on d.log_group_id = a.id " + \
        "group by c.slug_new_text, c.id, c.name, b.username, a.log_date " + \
        "union " + \
        "select 'challenge' as item_type, concat('/challenges/',c.slug) as item_url, c.id as item_id, c.name as item_name, b.username, a.log_date as last_modified, string_agg(d.highlight, concat(chr(10),chr(10)) order by d.id) as highlights " + \
        "from items_itemgrouphighlightloggroup a " + \
        "join auth_user b on b.id = a.user_id " + \
        "join items_itemgroup c on c.id = a.group_id @KEYWORD " + \
        "join items_itemgrouphighlightlog d on d.log_group_id = a.id " + \
        "group by c.slug, c.id, c.name, b.username, a.log_date " + \
        "union " + \
        "select 'route' as item_type, concat('/', e.slug_new_text,'/routes/', c.id) as item_url, c.id as item_id, c.name as item_name, b.username, a.log_date as last_modified, string_agg(d.highlight, concat(chr(10),chr(10)) order by d.id) as highlights " + \
        "from items_peakroutehighlightloggroup a " + \
        "join auth_user b on b.id = a.user_id " + \
        "join items_peakroute c on c.id = a.peak_route_id @KEYWORD " + \
        "join items_peakroutehighlightlog d on d.log_group_id = a.id " + \
        "join items_item e on e.id = c.item_id " + \
        "group by e.slug_new_text, c.id, c.name, b.username, a.log_date " + \
        "union " + \
        "select 'continent' as item_type, concat('/region/', c.slug, '-mountains/') as item_url, c.id as item_id, c.name as item_name, b.username, a.log_date as last_modified, string_agg(d.highlight, concat(chr(10),chr(10)) order by d.id) as highlights " + \
        "from cities_continenthighlightloggroup a " + \
        "join auth_user b on b.id = a.user_id " + \
        "join cities_continent c on c.id = a.continent_id @KEYWORD " + \
        "join cities_continenthighlightlog d on d.log_group_id = a.id " + \
        "group by c.slug, c.id, c.name, b.username, a.log_date " + \
        "union " + \
        "select 'country' as item_type, concat('/region/', c.slug, '-mountains/') as item_url, c.id as item_id, c.name as item_name, b.username, a.log_date as last_modified, string_agg(d.highlight, concat(chr(10),chr(10)) order by d.id) as highlights " + \
        "from cities_countryhighlightloggroup a " + \
        "join auth_user b on b.id = a.user_id " + \
        "join cities_country c on c.id = a.country_id @KEYWORD " + \
        "join cities_countryhighlightlog d on d.log_group_id = a.id " + \
        "group by c.slug, c.id, c.name, b.username, a.log_date " + \
        "union " + \
        "select 'region' as item_type, concat('/', cc.slug, '-mountains/', c.slug) as item_url, c.id as item_id, c.name as item_name, b.username, a.log_date as last_modified, string_agg(d.highlight, concat(chr(10),chr(10)) order by d.id) as highlights " + \
        "from cities_regionhighlightloggroup a " + \
        "join auth_user b on b.id = a.user_id " + \
        "join cities_region c on c.id = a.region_id @KEYWORD " + \
        "join cities_country cc on cc.id = c.country_id " + \
        "join cities_regionhighlightlog d on d.log_group_id = a.id " + \
        "group by cc.slug, c.slug, c.id, c.name, b.username, a.log_date " + \
        "order by last_modified desc"

    if len(keyword) > 0:
        search_keyword = "%" + keyword + "%"
        sql = str.replace(sql,"@KEYWORD","and lower(c.name) like %s ")
    else:
        sql = str.replace(sql,"@KEYWORD","")

    sql_params = []
    if len(keyword) > 0:
        sql_params.append(search_keyword)
        sql_params.append(search_keyword)
        sql_params.append(search_keyword)
        sql_params.append(search_keyword)
        sql_params.append(search_keyword)
        sql_params.append(search_keyword)

    with connection.cursor() as cursor:

        cursor.execute(sql, sql_params)

        highlights_log = dictfetchall(cursor)

    return render(request, 'tools/highlights_log.html', {
        'highlights_log':highlights_log,
        'keyword':keyword
    })

@staff_member_required
def highlights_setup(request):
    countries = Country.objects.filter(id=231)
    for c in countries:
        log_group = CountryHighlightLogGroup(user=request.user, country=c, log_date=datetime.datetime.now())
        log_group.save()
        sql = "insert into cities_countryhighlightlog (log_group_id, highlight) select %s, highlight from cities_countryhighlight where country_id = %s order by id"
        with connection.cursor() as cursor:
            cursor.execute(sql, [log_group.id, c.id])
    return render(request, 'tools/highlights_setup.html', {})

@staff_member_required
def highlights_peak_log(request, peak_id):

    peak = Item.objects.get(id=peak_id)

    if request.method == 'POST':
        action = request.POST.get('action')
        if action == 'revert':
            revision_id = request.POST.get('rdoHighlights')
            if revision_id > 0:
                #create new log group
                log_group = ItemHighlightLogGroup(user=request.user, item=peak, log_date=datetime.datetime.now())
                log_group.save()
                #delete existing highlights
                sql = "delete from items_itemhighlight where item_id = %s"
                with connection.cursor() as cursor:
                    cursor.execute(sql, [peak_id])
                #add new highlights
                sql = "insert into items_itemhighlight (user_id, item_id, highlight, created, modified) select %s, %s, highlight, now(), now() from items_itemhighlightlog where log_group_id = %s order by id"
                with connection.cursor() as cursor:
                    cursor.execute(sql, [request.user.id, peak_id, revision_id])
                #log what we've done
                sql = "insert into items_itemhighlightlog (log_group_id, highlight) select %s, highlight from items_itemhighlight where item_id = %s order by id"
                with connection.cursor() as cursor:
                    cursor.execute(sql, [log_group.id, peak_id])
        elif action == 'delete':
            #delete existing highlights
            sql = "delete from items_itemhighlight where item_id = %s"
            with connection.cursor() as cursor:
                cursor.execute(sql, [peak_id])
            #create new log group
            log_group = ItemHighlightLogGroup(user=request.user, item=peak, log_date=datetime.datetime.now())
            log_group.save()
            #log what we've done
            sql = "insert into items_itemhighlightlog (log_group_id, highlight) values (%s, '(deleted)')"
            with connection.cursor() as cursor:
                cursor.execute(sql, [log_group.id])

    sql = "select a.id, a.log_date, c.username, string_agg(b.highlight, concat(chr(10),chr(10)) order by b.id) as highlights " + \
        "from items_itemhighlightloggroup a " + \
        "join items_itemhighlightlog b on b.log_group_id = a.id " + \
        "join auth_user c on c.id = a.user_id " + \
        "where a.item_id = %s " + \
        "group by a.id, a.log_date, c.username " + \
        "order by a.id desc"

    sql_params = []
    sql_params.append(peak_id)

    with connection.cursor() as cursor:

        cursor.execute(sql, sql_params)

        highlights = dictfetchall(cursor)

    return render(request, 'tools/highlights_peak_log.html', {
        'peak':peak,
        'highlights':highlights
    })

@staff_member_required
def highlights_challenge_log(request, challenge_id):

    challenge = ItemGroup.objects.get(id=challenge_id)

    if request.method == 'POST':
        action = request.POST.get('action')
        if action == 'revert':
            revision_id = request.POST.get('rdoHighlights')
            if revision_id > 0:
                #create new log group
                log_group = ItemGroupHighlightLogGroup(user=request.user, group=challenge, log_date=datetime.datetime.now())
                log_group.save()
                #delete existing highlights
                sql = "delete from items_itemgrouphighlight where group_id = %s"
                with connection.cursor() as cursor:
                    cursor.execute(sql, [challenge_id])
                #add new highlights
                sql = "insert into items_itemgrouphighlight (user_id, group_id, highlight, created, modified) select %s, %s, highlight, now(), now() from items_itemgrouphighlightlog where log_group_id = %s order by id"
                with connection.cursor() as cursor:
                    cursor.execute(sql, [request.user.id, challenge_id, revision_id])
                #log what we've done
                sql = "insert into items_itemgrouphighlightlog (log_group_id, highlight) select %s, highlight from items_itemgrouphighlight where group_id = %s order by id"
                with connection.cursor() as cursor:
                    cursor.execute(sql, [log_group.id, challenge_id])
        elif action == 'delete':
            #delete existing highlights
            sql = "delete from items_itemgrouphighlight where group_id = %s"
            with connection.cursor() as cursor:
                cursor.execute(sql, [challenge_id])
            #create new log group
            log_group = ItemGroupHighlightLogGroup(user=request.user, group=challenge, log_date=datetime.datetime.now())
            log_group.save()
            #log what we've done
            sql = "insert into items_itemgrouphighlightlog (log_group_id, highlight) values (%s, '(deleted)')"
            with connection.cursor() as cursor:
                cursor.execute(sql, [log_group.id])


    sql = "select a.id, a.log_date, c.username, string_agg(b.highlight, concat(chr(10),chr(10)) order by b.id) as highlights " + \
        "from items_itemgrouphighlightloggroup a " + \
        "join items_itemgrouphighlightlog b on b.log_group_id = a.id " + \
        "join auth_user c on c.id = a.user_id " + \
        "where a.group_id = %s " + \
        "group by a.id, a.log_date, c.username " + \
        "order by a.id desc"

    sql_params = []
    sql_params.append(challenge_id)

    with connection.cursor() as cursor:

        cursor.execute(sql, sql_params)

        highlights = dictfetchall(cursor)

    return render(request, 'tools/highlights_challenge_log.html', {
        'challenge':challenge,
        'highlights':highlights
    })

@staff_member_required
def highlights_route_log(request, peak_route_id):

    route = PeakRoute.objects.get(id=peak_route_id)

    if request.method == 'POST':
        action = request.POST.get('action')
        if action == 'revert':
            revision_id = request.POST.get('rdoHighlights')
            if revision_id > 0:
                #create new log group
                log_group = PeakRouteHighlightLogGroup(user=request.user, route=route, log_date=datetime.datetime.now())
                log_group.save()
                #delete existing highlights
                sql = "delete from items_peakroutehighlight where peak_route_id = %s"
                with connection.cursor() as cursor:
                    cursor.execute(sql, [peak_id])
                #add new highlights
                sql = "insert into items_peakroutehighlight (user_id, peak_route_id, highlight, created, modified) select %s, %s, highlight, now(), now() from items_peakroutehighlightlog where log_group_id = %s order by id"
                with connection.cursor() as cursor:
                    cursor.execute(sql, [request.user.id, peak_route_id, revision_id])
                #log what we've done
                sql = "insert into items_peakroutehighlightlog (log_group_id, highlight) select %s, highlight from items_peakroutehighlight where peak_route_id = %s order by id"
                with connection.cursor() as cursor:
                    cursor.execute(sql, [log_group.id, peak_route_id])
        elif action == 'delete':
            #delete existing highlights
            sql = "delete from items_peakroutehighlight where peak_route_id = %s"
            with connection.cursor() as cursor:
                cursor.execute(sql, [peak_route_id])
            #create new log group
            log_group = PeakRouteHighlightLogGroup(user=request.user, peak_route=route, log_date=datetime.datetime.now())
            log_group.save()
            #log what we've done
            sql = "insert into items_peakroutehighlightlog (log_group_id, highlight) values (%s, '(deleted)')"
            with connection.cursor() as cursor:
                cursor.execute(sql, [log_group.id])

    sql = "select a.id, a.log_date, c.username, string_agg(b.highlight, concat(chr(10),chr(10)) order by b.id) as highlights " + \
        "from items_peakroutehighlightloggroup a " + \
        "join items_peakroutehighlightlog b on b.log_group_id = a.id " + \
        "join auth_user c on c.id = a.user_id " + \
        "where a.peak_route_id = %s " + \
        "group by a.id, a.log_date, c.username " + \
        "order by a.id desc"

    sql_params = []
    sql_params.append(peak_route_id)

    with connection.cursor() as cursor:

        cursor.execute(sql, sql_params)

        highlights = dictfetchall(cursor)

    return render(request, 'tools/highlights_route_log.html', {
        'route':route,
        'highlights':highlights
    })

@staff_member_required
def highlights_continent_log(request, continent_id):

    continent = Continent.objects.get(id=continent_id)

    if request.method == 'POST':
        action = request.POST.get('action')
        if action == 'revert':
            revision_id = request.POST.get('rdoHighlights')
            if revision_id > 0:
                #create new log group
                log_group = ContinentHighlightLogGroup(user=request.user, continent=continent, log_date=datetime.datetime.now())
                log_group.save()
                #delete existing highlights
                sql = "delete from cities_continenthighlight where continent_id = %s"
                with connection.cursor() as cursor:
                    cursor.execute(sql, [continent_id])
                #add new highlights
                sql = "insert into cities_continenthighlight (user_id, continent_id, highlight, created, modified) select %s, %s, highlight, now(), now() from cities_continenthighlightlog where log_group_id = %s order by id"
                with connection.cursor() as cursor:
                    cursor.execute(sql, [request.user.id, continent_id, revision_id])
                #log what we've done
                sql = "insert into cities_continenthighlightlog (log_group_id, highlight) select %s, highlight from cities_continenthighlight where continent_id = %s order by id"
                with connection.cursor() as cursor:
                    cursor.execute(sql, [log_group.id, continent_id])
        elif action == 'delete':
            #delete existing highlights
            sql = "delete from cities_continenthighlight where continent_id = %s"
            with connection.cursor() as cursor:
                cursor.execute(sql, [continent_id])
            #create new log group
            log_group = ContinentHighlightLogGroup(user=request.user, continent=continent, log_date=datetime.datetime.now())
            log_group.save()
            #log what we've done
            sql = "insert into cities_continenthighlightlog (log_group_id, highlight) values (%s, '(deleted)')"
            with connection.cursor() as cursor:
                cursor.execute(sql, [log_group.id])

    sql = "select a.id, a.log_date, c.username, string_agg(b.highlight, concat(chr(10),chr(10)) order by b.id) as highlights " + \
        "from cities_continenthighlightloggroup a " + \
        "join cities_continenthighlightlog b on b.log_group_id = a.id " + \
        "join auth_user c on c.id = a.user_id " + \
        "where a.continent_id = %s " + \
        "group by a.id, a.log_date, c.username " + \
        "order by a.id desc"

    sql_params = []
    sql_params.append(continent_id)

    with connection.cursor() as cursor:

        cursor.execute(sql, sql_params)

        highlights = dictfetchall(cursor)

    return render(request, 'tools/highlights_continent_log.html', {
        'continent':continent,
        'highlights':highlights
    })

@staff_member_required
def highlights_country_log(request, country_id):

    country = Country.objects.get(id=country_id)

    if request.method == 'POST':
        action = request.POST.get('action')
        if action == 'revert':
            revision_id = request.POST.get('rdoHighlights')
            if revision_id > 0:
                #create new log group
                log_group = CountryHighlightLogGroup(user=request.user, country=country, log_date=datetime.datetime.now())
                log_group.save()
                #delete existing highlights
                sql = "delete from cities_countryhighlight where country_id = %s"
                with connection.cursor() as cursor:
                    cursor.execute(sql, [country_id])
                #add new highlights
                sql = "insert into cities_countryhighlight (user_id, country_id, highlight, created, modified) select %s, %s, highlight, now(), now() from cities_countryhighlightlog where log_group_id = %s order by id"
                with connection.cursor() as cursor:
                    cursor.execute(sql, [request.user.id, country_id, revision_id])
                #log what we've done
                sql = "insert into cities_countryhighlightlog (log_group_id, highlight) select %s, highlight from cities_countryhighlight where country_id = %s order by id"
                with connection.cursor() as cursor:
                    cursor.execute(sql, [log_group.id, country_id])
        elif action == 'delete':
            #delete existing highlights
            sql = "delete from cities_countryhighlight where country_id = %s"
            with connection.cursor() as cursor:
                cursor.execute(sql, [country_id])
            #create new log group
            log_group = CountryHighlightLogGroup(user=request.user, country=country, log_date=datetime.datetime.now())
            log_group.save()
            #log what we've done
            sql = "insert into cities_countryhighlightlog (log_group_id, highlight) values (%s, '(deleted)')"
            with connection.cursor() as cursor:
                cursor.execute(sql, [log_group.id])

    sql = "select a.id, a.log_date, c.username, string_agg(b.highlight, concat(chr(10),chr(10)) order by b.id) as highlights " + \
        "from cities_countryhighlightloggroup a " + \
        "join cities_countryhighlightlog b on b.log_group_id = a.id " + \
        "join auth_user c on c.id = a.user_id " + \
        "where a.country_id = %s " + \
        "group by a.id, a.log_date, c.username " + \
        "order by a.id desc"

    sql_params = []
    sql_params.append(country_id)

    with connection.cursor() as cursor:

        cursor.execute(sql, sql_params)

        highlights = dictfetchall(cursor)

    return render(request, 'tools/highlights_country_log.html', {
        'country':country,
        'highlights':highlights
    })

@staff_member_required
def highlights_region_log(request, region_id):

    region = Region.objects.get(id=region_id)

    if request.method == 'POST':
        action = request.POST.get('action')
        if action == 'revert':
            revision_id = request.POST.get('rdoHighlights')
            if revision_id > 0:
                #create new log group
                log_group = RegionHighlightLogGroup(user=request.user, region=region, log_date=datetime.datetime.now())
                log_group.save()
                #delete existing highlights
                sql = "delete from cities_regionhighlight where region_id = %s"
                with connection.cursor() as cursor:
                    cursor.execute(sql, [region_id])
                #add new highlights
                sql = "insert into cities_regionhighlight (user_id, region_id, highlight, created, modified) select %s, %s, highlight, now(), now() from cities_regionhighlightlog where log_group_id = %s order by id"
                with connection.cursor() as cursor:
                    cursor.execute(sql, [request.user.id, region_id, revision_id])
                #log what we've done
                sql = "insert into cities_regionhighlightlog (log_group_id, highlight) select %s, highlight from cities_regionhighlight where region_id = %s order by id"
                with connection.cursor() as cursor:
                    cursor.execute(sql, [log_group.id, region_id])
        elif action == 'delete':
            #delete existing highlights
            sql = "delete from cities_regionhighlight where region_id = %s"
            with connection.cursor() as cursor:
                cursor.execute(sql, [region_id])
            #create new log group
            log_group = RegionHighlightLogGroup(user=request.user, region=region, log_date=datetime.datetime.now())
            log_group.save()
            #log what we've done
            sql = "insert into cities_regionhighlightlog (log_group_id, highlight) values (%s, '(deleted)')"
            with connection.cursor() as cursor:
                cursor.execute(sql, [log_group.id])

    sql = "select a.id, a.log_date, c.username, string_agg(b.highlight, concat(chr(10),chr(10)) order by b.id) as highlights " + \
        "from cities_regionhighlightloggroup a " + \
        "join cities_regionhighlightlog b on b.log_group_id = a.id " + \
        "join auth_user c on c.id = a.user_id " + \
        "where a.region_id = %s " + \
        "group by a.id, a.log_date, c.username " + \
        "order by a.id desc"

    sql_params = []
    sql_params.append(region_id)

    with connection.cursor() as cursor:

        cursor.execute(sql, sql_params)

        highlights = dictfetchall(cursor)

    return render(request, 'tools/highlights_region_log.html', {
        'region':region,
        'highlights':highlights
    })

@staff_member_required
def dashboard(request):

    from datetime import datetime, timedelta
    start_date = request.POST.get('from', '').lower()
    end_date = request.POST.get('to', '').lower()
    display_type = request.POST.get('display_type', '').lower()
    display_format = request.POST.get('display_format', '').lower()

    if not start_date:
        d = datetime.now() - timedelta(days=30)
        start_month = '%02d' % d.month
        start_day = '%02d' % d.day
        start_year = d.year
        start_date = '%s/%s/%s 00:00:00' % (start_month, start_day, start_year)
    else:
        start_date = '%s 00:00:00' % start_date
    if not end_date:
        d = datetime.now()
        end_month = '%02d' % d.month
        end_day = '%02d' % d.day
        end_year = d.year
        end_date = '%s/%s/%s 23:59:59' % (end_month, end_day, end_year)
    else:
        end_date = '%s 23:59:59' % end_date
    if not display_type:
        display_type = 'daily'
    if not display_format:
        display_format = 'chart'

    if display_type == 'daily':

        sql = "select a.day as date, count(b.id) as num_signups " + \
            "from calendar_day a " + \
            "left join auth_user b on b.date_joined >= a.start_date and b.date_joined <= a.end_date " + \
            "left join accounts_person c on c.user_id = b.id " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "and coalesce(signup_source, 'web-desktop') = 'web-desktop' " + \
            "group by a.day " + \
            "union " + \
            "select a.day as date, 0 as num_signups " + \
            "from calendar_day a " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "and not exists(select 1 from auth_user b, accounts_person c where b.date_joined >= a.start_date and b.date_joined <= a.end_date and c.user_id = b.id and coalesce(c.signup_source, 'web-desktop') = 'web-desktop') " + \
            "group by a.day " + \
            "order by date "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            signups = dictfetchall(cursor)


        sql = "select a.day as date, sum(case when c.signup_source = 'web-mobile' then 1 else 0 end) as mobile_signups " + \
              "from calendar_day a " + \
              "left join auth_user b on b.date_joined >= a.start_date and b.date_joined <= a.end_date " + \
              "left join accounts_person c on c.user_id = b.id " + \
              "where a.start_date >= %s " + \
              "and a.end_date <= %s " + \
              "group by a.day " + \
              "order by a.day "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            mobile_signups = dictfetchall(cursor)


        sql = "select a.day as date, sum(case when c.signup_source = 'android-native' then 1 else 0 end) as android_signups " + \
              "from calendar_day a " + \
              "left join auth_user b on b.date_joined >= a.start_date and b.date_joined <= a.end_date " + \
              "left join accounts_person c on c.user_id = b.id " + \
              "where a.start_date >= %s " + \
              "and a.end_date <= %s " + \
              "group by a.day " + \
              "order by a.day "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            android_signups = dictfetchall(cursor)

        sql = "select a.day as date, count(distinct b.user_id) as active_members " + \
              "from calendar_day a " + \
              "left join activity_log b on b.activity_date >= a.start_date and b.activity_date <= a.end_date " + \
              "where activity_source = 'Web' and a.start_date >= %s " + \
              "and a.end_date <= %s " + \
              "group by a.day " + \
              "order by a.day "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            active_members = dictfetchall(cursor)

        sql = "select a.day as date, count(distinct b.user_id) as android_active_members " + \
              "from calendar_day a " + \
              "left join activity_log b on b.activity_date >= a.start_date and b.activity_date <= a.end_date and b.activity_source = 'Android' " + \
              "where a.start_date >= %s " + \
              "and a.end_date <= %s " + \
              "group by a.day " + \
              "order by a.day "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:
            cursor.execute(sql, sql_params)
            android_active_members = dictfetchall(cursor)


        sql = "select a.day as date, count(distinct b.user_id) as ios_active_members " + \
              "from calendar_day a " + \
              "left join activity_log b on b.activity_date >= a.start_date and b.activity_date <= a.end_date and b.activity_source = 'IOS' " + \
              "where a.start_date >= %s " + \
              "and a.end_date <= %s " + \
              "group by a.day " + \
              "order by a.day "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:
            cursor.execute(sql, sql_params)
            ios_active_members = dictfetchall(cursor)

        sql = "select a.day as date, count(distinct b.id) as signups_that_logged " + \
            "from calendar_day a " + \
            "left join (select b.id, b.date_joined from auth_user b, items_summitlog c where c.user_id = b.id and c.status = 1) b on b.date_joined >= a.start_date and b.date_joined <= a.end_date " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.day " + \
            "order by a.day "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            signups_that_logged = dictfetchall(cursor)

        sql = "select a.day as date, count(distinct b.user_id) as members_that_logged " + \
            "from calendar_day a " + \
            "left join items_summitlog b on b.created >= a.start_date and b.created <= a.end_date and b.status = 1 " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.day " + \
            "order by a.day "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            members_that_logged = dictfetchall(cursor)

        sql = "select a.day as date, count(b.id) as climbs_logged " + \
            "from calendar_day a " + \
            "left join items_summitlog b on b.created >= a.start_date and b.created <= a.end_date and b.status = 1 " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.day " + \
            "order by a.day "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            climbs_logged = dictfetchall(cursor)

        sql = "select a.day as date, count(b.id) as climbs_with_photos " + \
            "from calendar_day a " + \
            "left join items_summitlog b on b.created >= a.start_date and b.created <= a.end_date and b.status = 1 " + \
            "and exists (select 1 from items_itemphoto c where c.summit_log_id = b.id) " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.day " + \
            "order by a.day "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            climbs_with_photos = dictfetchall(cursor)

        sql = "select a.day as date, count(b.id) as climbs_with_trip_report " + \
            "from calendar_day a " + \
            "left join items_summitlog b on b.created >= a.start_date and b.created <= a.end_date and b.status = 1 and length(b.log) > 0 " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.day " + \
            "order by a.day "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            climbs_with_trip_report = dictfetchall(cursor)

        sql = "select a.day as date, count(b.id) as climbs_with_gpx " + \
            "from calendar_day a " + \
            "left join items_summitlog b on b.created >= a.start_date and b.created <= a.end_date and b.status = 1 and length(b.gpx_file) > 0 " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.day " + \
            "order by a.day "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            climbs_with_gpx = dictfetchall(cursor)

        sql = "select a.day as date, count(b.id) as num_likes " + \
            "from calendar_day a " + \
            "left join favorites_favorite b on b.created_on >= a.start_date and b.created_on <= a.end_date " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.day " + \
            "order by a.day "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_likes = dictfetchall(cursor)

        sql = "select a.day as date, count(distinct b.user_id) as num_users_that_liked " + \
            "from calendar_day a " + \
            "left join favorites_favorite b on b.created_on >= a.start_date and b.created_on <= a.end_date " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.day " + \
            "order by a.day "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_users_that_liked = dictfetchall(cursor)

        sql = "select a.day as date, count(b.id) as num_comments " + \
            "from calendar_day a " + \
            "left join items_summitlogcomment b on b.created >= a.start_date and b.created <= a.end_date " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.day " + \
            "order by a.day "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_comments = dictfetchall(cursor)

        sql = "select a.day as date, count(distinct b.user_id) as num_users_that_commented " + \
            "from calendar_day a " + \
            "left join items_summitlogcomment b on b.created >= a.start_date and b.created <= a.end_date " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.day " + \
            "order by a.day "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_users_that_commented = dictfetchall(cursor)

        sql = "select a.day as date, count(b.id) as num_follows " + \
            "from calendar_day a " + \
            "left join follow_follow b on b.datetime >= a.start_date and b.datetime <= a.end_date " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.day " + \
            "order by a.day "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_follows = dictfetchall(cursor)

        sql = "select a.day as date, count(distinct b.user_id) as num_users_that_followed " + \
            "from calendar_day a " + \
            "left join follow_follow b on b.datetime >= a.start_date and b.datetime <= a.end_date " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.day " + \
            "order by a.day "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_users_that_followed = dictfetchall(cursor)

        sql = "select a.day as date, count(b.id) as num_item_corrections " + \
            "from calendar_day a " + \
            "left join items_itemcorrection b on b.decision_date >= a.start_date and b.decision_date <= a.end_date and b.status = 2 " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.day " + \
            "order by a.day "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_item_corrections = dictfetchall(cursor)

        sql = "select a.day as date, count(distinct b.user_id) as num_users_with_item_corrections " + \
            "from calendar_day a " + \
            "left join items_itemcorrection b on b.decision_date >= a.start_date and b.decision_date <= a.end_date and b.status = 2 " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.day " + \
            "order by a.day "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_users_with_item_corrections = dictfetchall(cursor)

        sql = "select a.day as date, count(b.id) as num_peaks_approved " + \
            "from calendar_day a " + \
            "left join items_item b on b.created >= a.start_date and b.created <= a.end_date and b.user_id > 1 and not exists (select 1 from auth_user c where c.id = b.user_id and c.is_superuser = true) " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.day " + \
            "order by a.day "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_peaks_approved = dictfetchall(cursor)

        sql = "select a.day as date, count(distinct b.user_id) as num_users_with_peaks_approved " + \
            "from calendar_day a " + \
            "left join items_item b on b.created >= a.start_date and b.created <= a.end_date and b.user_id > 1 and not exists (select 1 from auth_user c where c.id = b.user_id and c.is_superuser = true) " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.day " + \
            "order by a.day "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_users_with_peaks_approved = dictfetchall(cursor)

    elif display_type == 'weekly':

        sql = "select a.week as date, count(b.id) as num_signups " + \
            "from calendar_week a " + \
            "left join auth_user b on b.date_joined >= a.start_date and b.date_joined <= a.end_date " + \
            "left join accounts_person c on c.user_id = b.id " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "and coalesce(signup_source, 'web-desktop') = 'web-desktop' " + \
            "group by a.week " + \
            "order by a.week "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            signups = dictfetchall(cursor)

        sql = "select a.week as date, sum(case when c.signup_source = 'web-mobile' then 1 else 0 end) as mobile_signups " + \
              "from calendar_week a " + \
              "left join auth_user b on b.date_joined >= a.start_date and b.date_joined <= a.end_date " + \
              "left join accounts_person c on c.user_id = b.id " + \
              "where a.start_date >= %s " + \
              "and a.end_date <= %s " + \
              "group by a.week " + \
              "order by a.week "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            mobile_signups = dictfetchall(cursor)

        sql = "select a.week as date, sum(case when c.signup_source = 'android-native' then 1 else 0 end) as android_signups " + \
              "from calendar_week a " + \
              "left join auth_user b on b.date_joined >= a.start_date and b.date_joined <= a.end_date " + \
              "left join accounts_person c on c.user_id = b.id " + \
              "where a.start_date >= %s " + \
              "and a.end_date <= %s " + \
              "group by a.week " + \
              "order by a.week "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            android_signups = dictfetchall(cursor)

        sql = "select a.week as date, count(distinct b.user_id) as active_members " + \
              "from calendar_week a " + \
              "left join activity_log b on b.activity_date >= a.start_date and b.activity_date <= a.end_date " + \
              "where activity_source = 'Web' and a.start_date >= %s " + \
              "and a.end_date <= %s " + \
              "group by a.week " + \
              "order by a.week "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            active_members = dictfetchall(cursor)

        sql = "select a.week as date, count(distinct b.user_id) as android_active_members " + \
              "from calendar_week a " + \
              "left join activity_log b on b.activity_date >= a.start_date and b.activity_date <= a.end_date and b.activity_source = 'Android' " + \
              "where a.start_date >= %s " + \
              "and a.end_date <= %s " + \
              "group by a.week " + \
              "order by a.week "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:
            cursor.execute(sql, sql_params)
            android_active_members = dictfetchall(cursor)


        sql = "select a.week as date, count(distinct b.user_id) as ios_active_members " + \
              "from calendar_week a " + \
              "left join activity_log b on b.activity_date >= a.start_date and b.activity_date <= a.end_date and b.activity_source = 'IOS' " + \
              "where a.start_date >= %s " + \
              "and a.end_date <= %s " + \
              "group by a.week " + \
              "order by a.week "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:
            cursor.execute(sql, sql_params)
            ios_active_members = dictfetchall(cursor)

        sql = "select a.week as date, count(distinct b.id) as signups_that_logged " + \
              "from calendar_week a " + \
              "left join (select b.id, b.date_joined from auth_user b, items_summitlog c where c.user_id = b.id and c.status = 1) b on b.date_joined >= a.start_date and b.date_joined <= a.end_date " + \
              "where a.start_date >= %s " + \
              "and a.end_date <= %s " + \
              "group by a.week " + \
              "order by a.week "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            signups_that_logged = dictfetchall(cursor)

        sql = "select a.week as date, count(distinct b.user_id) as members_that_logged " + \
            "from calendar_week a " + \
            "left join items_summitlog b on b.created >= a.start_date and b.created <= a.end_date and b.status = 1 " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.week " + \
            "order by a.week "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            members_that_logged = dictfetchall(cursor)

        sql = "select a.week as date, count(b.id) as climbs_logged " + \
            "from calendar_week a " + \
            "left join items_summitlog b on b.created >= a.start_date and b.created <= a.end_date and b.status = 1 " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.week " + \
            "order by a.week "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            climbs_logged = dictfetchall(cursor)

        sql = "select a.week as date, count(b.id) as climbs_with_photos " + \
            "from calendar_week a " + \
            "left join items_summitlog b on b.created >= a.start_date and b.created <= a.end_date and b.status = 1 " + \
            "and exists (select 1 from items_itemphoto c where c.summit_log_id = b.id) " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.week " + \
            "order by a.week "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            climbs_with_photos = dictfetchall(cursor)

        sql = "select a.week as date, count(b.id) as climbs_with_trip_report " + \
            "from calendar_week a " + \
            "left join items_summitlog b on b.created >= a.start_date and b.created <= a.end_date and b.status = 1 and length(b.log) > 0 " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.week " + \
            "order by a.week "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            climbs_with_trip_report = dictfetchall(cursor)

        sql = "select a.week as date, count(b.id) as climbs_with_gpx " + \
            "from calendar_week a " + \
            "left join items_summitlog b on b.created >= a.start_date and b.created <= a.end_date and b.status = 1 and length(b.gpx_file) > 0 " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.week " + \
            "order by a.week "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            climbs_with_gpx = dictfetchall(cursor)

        sql = "select a.week as date, count(b.id) as num_likes " + \
            "from calendar_week a " + \
            "left join favorites_favorite b on b.created_on >= a.start_date and b.created_on <= a.end_date " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.week " + \
            "order by a.week "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_likes = dictfetchall(cursor)

        sql = "select a.week as date, count(distinct b.user_id) as num_users_that_liked " + \
            "from calendar_week a " + \
            "left join favorites_favorite b on b.created_on >= a.start_date and b.created_on <= a.end_date " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.week " + \
            "order by a.week "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_users_that_liked = dictfetchall(cursor)

        sql = "select a.week as date, count(b.id) as num_comments " + \
            "from calendar_week a " + \
            "left join items_summitlogcomment b on b.created >= a.start_date and b.created <= a.end_date " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.week " + \
            "order by a.week "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_comments = dictfetchall(cursor)

        sql = "select a.week as date, count(distinct b.user_id) as num_users_that_commented " + \
            "from calendar_week a " + \
            "left join items_summitlogcomment b on b.created >= a.start_date and b.created <= a.end_date " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.week " + \
            "order by a.week "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_users_that_commented = dictfetchall(cursor)

        sql = "select a.week as date, count(b.id) as num_follows " + \
            "from calendar_week a " + \
            "left join follow_follow b on b.datetime >= a.start_date and b.datetime <= a.end_date " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.week " + \
            "order by a.week "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_follows = dictfetchall(cursor)

        sql = "select a.week as date, count(distinct b.user_id) as num_users_that_followed " + \
            "from calendar_week a " + \
            "left join follow_follow b on b.datetime >= a.start_date and b.datetime <= a.end_date " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.week " + \
            "order by a.week "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_users_that_followed = dictfetchall(cursor)

        sql = "select a.week as date, count(b.id) as num_item_corrections " + \
            "from calendar_week a " + \
            "left join items_itemcorrection b on b.decision_date >= a.start_date and b.decision_date <= a.end_date and b.status = 2 " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.week " + \
            "order by a.week "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_item_corrections = dictfetchall(cursor)

        sql = "select a.week as date, count(distinct b.user_id) as num_users_with_item_corrections " + \
            "from calendar_week a " + \
            "left join items_itemcorrection b on b.decision_date >= a.start_date and b.decision_date <= a.end_date and b.status = 2 " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.week " + \
            "order by a.week "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_users_with_item_corrections = dictfetchall(cursor)

        sql = "select a.week as date, count(b.id) as num_peaks_approved " + \
            "from calendar_week a " + \
            "left join items_item b on b.created >= a.start_date and b.created <= a.end_date and b.user_id > 1 and not exists (select 1 from auth_user c where c.id = b.user_id and c.is_superuser = true) " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.week " + \
            "order by a.week "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_peaks_approved = dictfetchall(cursor)

        sql = "select a.week as date, count(distinct b.user_id) as num_users_with_peaks_approved " + \
            "from calendar_week a " + \
            "left join items_item b on b.created >= a.start_date and b.created <= a.end_date and b.user_id > 1 and not exists (select 1 from auth_user c where c.id = b.user_id and c.is_superuser = true) " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.week " + \
            "order by a.week "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_users_with_peaks_approved = dictfetchall(cursor)

    elif display_type == 'monthly':

        sql = "select a.month as date, count(b.id) as num_signups " + \
            "from calendar_month a " + \
            "left join auth_user b on b.date_joined >= a.start_date and b.date_joined <= a.end_date " + \
            "left join accounts_person c on c.user_id = b.id " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "and coalesce(signup_source, 'web-desktop') = 'web-desktop' " + \
            "group by a.month " + \
            "order by a.month "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            signups = dictfetchall(cursor)

        sql = "select a.month as date, sum(case when c.signup_source = 'web-mobile' then 1 else 0 end) as mobile_signups " + \
              "from calendar_month a " + \
              "left join auth_user b on b.date_joined >= a.start_date and b.date_joined <= a.end_date " + \
              "left join accounts_person c on c.user_id = b.id " + \
              "where a.start_date >= %s " + \
              "and a.end_date <= %s " + \
              "group by a.month " + \
              "order by a.month "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            mobile_signups = dictfetchall(cursor)

        sql = "select a.month as date, sum(case when c.signup_source = 'android-native' then 1 else 0 end) as android_signups " + \
              "from calendar_month a " + \
              "left join auth_user b on b.date_joined >= a.start_date and b.date_joined <= a.end_date " + \
              "left join accounts_person c on c.user_id = b.id " + \
              "where a.start_date >= %s " + \
              "and a.end_date <= %s " + \
              "group by a.month " + \
              "order by a.month "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            android_signups = dictfetchall(cursor)

        sql = "select a.month as date, count(distinct b.user_id) as active_members " + \
              "from calendar_month a " + \
              "left join activity_log b on b.activity_date >= a.start_date and b.activity_date <= a.end_date " + \
              "where activity_source = 'Web' and a.start_date >= %s " + \
              "and a.end_date <= %s " + \
              "group by a.month " + \
              "order by a.month "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            active_members = dictfetchall(cursor)

        sql = "select a.month as date, count(distinct b.user_id) as android_active_members " + \
              "from calendar_month a " + \
              "left join activity_log b on b.activity_date >= a.start_date and b.activity_date <= a.end_date and b.activity_source = 'Android' " + \
              "where a.start_date >= %s " + \
              "and a.end_date <= %s " + \
              "group by a.month " + \
              "order by a.month "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:
            cursor.execute(sql, sql_params)
            android_active_members = dictfetchall(cursor)


        sql = "select a.month as date, count(distinct b.user_id) as ios_active_members " + \
              "from calendar_month a " + \
              "left join activity_log b on b.activity_date >= a.start_date and b.activity_date <= a.end_date and b.activity_source = 'IOS' " + \
              "where a.start_date >= %s " + \
              "and a.end_date <= %s " + \
              "group by a.month " + \
              "order by a.month "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:
            cursor.execute(sql, sql_params)
            ios_active_members = dictfetchall(cursor)

        sql = "select a.month as date, count(distinct b.id) as signups_that_logged " + \
            "from calendar_month a " + \
            "left join (select b.id, b.date_joined from auth_user b, items_summitlog c where c.user_id = b.id and c.status = 1) b on b.date_joined >= a.start_date and b.date_joined <= a.end_date " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.month " + \
            "order by a.month "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            signups_that_logged = dictfetchall(cursor)

        sql = "select a.month as date, count(distinct b.user_id) as members_that_logged " + \
            "from calendar_month a " + \
            "left join items_summitlog b on b.created >= a.start_date and b.created <= a.end_date and b.status = 1 " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.month " + \
            "order by a.month "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            members_that_logged = dictfetchall(cursor)

        sql = "select a.month as date, count(b.id) as climbs_logged " + \
            "from calendar_month a " + \
            "left join items_summitlog b on b.created >= a.start_date and b.created <= a.end_date and b.status = 1 " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.month " + \
            "order by a.month "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            climbs_logged = dictfetchall(cursor)

        sql = "select a.month as date, count(b.id) as climbs_with_photos " + \
            "from calendar_month a " + \
            "left join items_summitlog b on b.created >= a.start_date and b.created <= a.end_date and b.status = 1 " + \
            "and exists (select 1 from items_itemphoto c where c.summit_log_id = b.id) " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.month " + \
            "order by a.month "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            climbs_with_photos = dictfetchall(cursor)

        sql = "select a.month as date, count(b.id) as climbs_with_trip_report " + \
            "from calendar_month a " + \
            "left join items_summitlog b on b.created >= a.start_date and b.created <= a.end_date and b.status = 1 and length(b.log) > 0 " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.month " + \
            "order by a.month "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            climbs_with_trip_report = dictfetchall(cursor)

        sql = "select a.month as date, count(b.id) as climbs_with_gpx " + \
            "from calendar_month a " + \
            "left join items_summitlog b on b.created >= a.start_date and b.created <= a.end_date and b.status = 1 and length(b.gpx_file) > 0 " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.month " + \
            "order by a.month "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            climbs_with_gpx = dictfetchall(cursor)

        sql = "select a.month as date, count(b.id) as num_likes " + \
            "from calendar_month a " + \
            "left join favorites_favorite b on b.created_on >= a.start_date and b.created_on <= a.end_date " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.month " + \
            "order by a.month "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_likes = dictfetchall(cursor)

        sql = "select a.month as date, count(distinct b.user_id) as num_users_that_liked " + \
            "from calendar_month a " + \
            "left join favorites_favorite b on b.created_on >= a.start_date and b.created_on <= a.end_date " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.month " + \
            "order by a.month "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_users_that_liked = dictfetchall(cursor)

        sql = "select a.month as date, count(b.id) as num_comments " + \
            "from calendar_month a " + \
            "left join items_summitlogcomment b on b.created >= a.start_date and b.created <= a.end_date " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.month " + \
            "order by a.month "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_comments = dictfetchall(cursor)

        sql = "select a.month as date, count(distinct b.user_id) as num_users_that_commented " + \
            "from calendar_month a " + \
            "left join items_summitlogcomment b on b.created >= a.start_date and b.created <= a.end_date " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.month " + \
            "order by a.month "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_users_that_commented = dictfetchall(cursor)

        sql = "select a.month as date, count(b.id) as num_follows " + \
            "from calendar_month a " + \
            "left join follow_follow b on b.datetime >= a.start_date and b.datetime <= a.end_date " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.month " + \
            "order by a.month "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_follows = dictfetchall(cursor)

        sql = "select a.month as date, count(distinct b.user_id) as num_users_that_followed " + \
            "from calendar_month a " + \
            "left join follow_follow b on b.datetime >= a.start_date and b.datetime <= a.end_date " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.month " + \
            "order by a.month "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_users_that_followed = dictfetchall(cursor)

        sql = "select a.month as date, count(b.id) as num_item_corrections " + \
            "from calendar_month a " + \
            "left join items_itemcorrection b on b.decision_date >= a.start_date and b.decision_date <= a.end_date and b.status = 2 " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.month " + \
            "order by a.month "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_item_corrections = dictfetchall(cursor)

        sql = "select a.month as date, count(distinct b.user_id) as num_users_with_item_corrections " + \
            "from calendar_month a " + \
            "left join items_itemcorrection b on b.decision_date >= a.start_date and b.decision_date <= a.end_date and b.status = 2 " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.month " + \
            "order by a.month "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_users_with_item_corrections = dictfetchall(cursor)

        sql = "select a.month as date, count(b.id) as num_peaks_approved " + \
            "from calendar_month a " + \
            "left join items_item b on b.created >= a.start_date and b.created <= a.end_date and b.user_id > 1 and not exists (select 1 from auth_user c where c.id = b.user_id and c.is_superuser = true) " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.month " + \
            "order by a.month "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_peaks_approved = dictfetchall(cursor)

        sql = "select a.month as date, count(distinct b.user_id) as num_users_with_peaks_approved " + \
            "from calendar_month a " + \
            "left join items_item b on b.created >= a.start_date and b.created <= a.end_date and b.user_id > 1 and not exists (select 1 from auth_user c where c.id = b.user_id and c.is_superuser = true) " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.month " + \
            "order by a.month "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_users_with_peaks_approved = dictfetchall(cursor)

    elif display_type == 'yearly':

        sql = "select a.year as date, count(b.id) as num_signups " + \
            "from calendar_year a " + \
            "left join auth_user b on b.date_joined >= a.start_date and b.date_joined <= a.end_date " + \
            "left join accounts_person c on c.user_id = b.id " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "and coalesce(signup_source, 'web-desktop') = 'web-desktop' " + \
            "group by a.year " + \
            "order by a.year "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            signups = dictfetchall(cursor)

        sql = "select a.year as date, sum(case when c.signup_source = 'web-mobile' then 1 else 0 end) as mobile_signups " + \
              "from calendar_year a " + \
              "left join auth_user b on b.date_joined >= a.start_date and b.date_joined <= a.end_date " + \
              "left join accounts_person c on c.user_id = b.id " + \
              "where a.start_date >= %s " + \
              "and a.end_date <= %s " + \
              "group by a.year " + \
              "order by a.year "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            mobile_signups = dictfetchall(cursor)

        sql = "select a.year as date, sum(case when c.signup_source = 'android-native' then 1 else 0 end) as android_signups " + \
              "from calendar_year a " + \
              "left join auth_user b on b.date_joined >= a.start_date and b.date_joined <= a.end_date " + \
              "left join accounts_person c on c.user_id = b.id " + \
              "where a.start_date >= %s " + \
              "and a.end_date <= %s " + \
              "group by a.year " + \
              "order by a.year "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            android_signups = dictfetchall(cursor)

        sql = "select a.year as date, count(distinct b.user_id) as active_members " + \
              "from calendar_year a " + \
              "left join activity_log b on b.activity_date >= a.start_date and b.activity_date <= a.end_date " + \
              "where activity_source = 'Web' and a.start_date >= %s " + \
              "and a.end_date <= %s " + \
              "group by a.year " + \
              "order by a.year "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            active_members = dictfetchall(cursor)

        sql = "select a.year as date, count(distinct b.user_id) as android_active_members " + \
              "from calendar_year a " + \
              "left join activity_log b on b.activity_date >= a.start_date and b.activity_date <= a.end_date and b.activity_source = 'Android' " + \
              "where a.start_date >= %s " + \
              "and a.end_date <= %s " + \
              "group by a.year " + \
              "order by a.year "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:
            cursor.execute(sql, sql_params)
            android_active_members = dictfetchall(cursor)


        sql = "select a.year as date, count(distinct b.user_id) as ios_active_members " + \
              "from calendar_year a " + \
              "left join activity_log b on b.activity_date >= a.start_date and b.activity_date <= a.end_date and b.activity_source = 'IOS' " + \
              "where a.start_date >= %s " + \
              "and a.end_date <= %s " + \
              "group by a.year " + \
              "order by a.year "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:
            cursor.execute(sql, sql_params)
            ios_active_members = dictfetchall(cursor)

        sql = "select a.year as date, count(distinct b.id) as signups_that_logged " + \
            "from calendar_year a " + \
            "left join (select b.id, b.date_joined from auth_user b, items_summitlog c where c.user_id = b.id and c.status = 1) b on b.date_joined >= a.start_date and b.date_joined <= a.end_date " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.year " + \
            "order by a.year "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            signups_that_logged = dictfetchall(cursor)

        sql = "select a.year as date, count(distinct b.user_id) as members_that_logged " + \
            "from calendar_year a " + \
            "left join items_summitlog b on b.created >= a.start_date and b.created <= a.end_date and b.status = 1 " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.year " + \
            "order by a.year "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            members_that_logged = dictfetchall(cursor)

        sql = "select a.year as date, count(b.id) as climbs_logged " + \
            "from calendar_year a " + \
            "left join items_summitlog b on b.created >= a.start_date and b.created <= a.end_date and b.status = 1 " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.year " + \
            "order by a.year "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            climbs_logged = dictfetchall(cursor)

        sql = "select a.year as date, count(b.id) as climbs_with_photos " + \
            "from calendar_year a " + \
            "left join items_summitlog b on b.created >= a.start_date and b.created <= a.end_date and b.status = 1 " + \
            "and exists (select 1 from items_itemphoto c where c.summit_log_id = b.id) " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.year " + \
            "order by a.year "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            climbs_with_photos = dictfetchall(cursor)

        sql = "select a.year as date, count(b.id) as climbs_with_trip_report " + \
            "from calendar_year a " + \
            "left join items_summitlog b on b.created >= a.start_date and b.created <= a.end_date and b.status = 1 and length(b.log) > 0 " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.year " + \
            "order by a.year "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            climbs_with_trip_report = dictfetchall(cursor)

        sql = "select a.year as date, count(b.id) as climbs_with_gpx " + \
            "from calendar_year a " + \
            "left join items_summitlog b on b.created >= a.start_date and b.created <= a.end_date and b.status = 1 and length(b.gpx_file) > 0 " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.year " + \
            "order by a.year "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            climbs_with_gpx = dictfetchall(cursor)

        sql = "select a.year as date, count(b.id) as num_likes " + \
            "from calendar_year a " + \
            "left join favorites_favorite b on b.created_on >= a.start_date and b.created_on <= a.end_date " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.year " + \
            "order by a.year "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_likes = dictfetchall(cursor)

        sql = "select a.year as date, count(distinct b.user_id) as num_users_that_liked " + \
            "from calendar_year a " + \
            "left join favorites_favorite b on b.created_on >= a.start_date and b.created_on <= a.end_date " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.year " + \
            "order by a.year "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_users_that_liked = dictfetchall(cursor)

        sql = "select a.year as date, count(b.id) as num_comments " + \
            "from calendar_year a " + \
            "left join items_summitlogcomment b on b.created >= a.start_date and b.created <= a.end_date " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.year " + \
            "order by a.year "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_comments = dictfetchall(cursor)

        sql = "select a.year as date, count(distinct b.user_id) as num_users_that_commented " + \
            "from calendar_year a " + \
            "left join items_summitlogcomment b on b.created >= a.start_date and b.created <= a.end_date " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.year " + \
            "order by a.year "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_users_that_commented = dictfetchall(cursor)

        sql = "select a.year as date, count(b.id) as num_follows " + \
            "from calendar_year a " + \
            "left join follow_follow b on b.datetime >= a.start_date and b.datetime <= a.end_date " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.year " + \
            "order by a.year "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_follows = dictfetchall(cursor)

        sql = "select a.year as date, count(distinct b.user_id) as num_users_that_followed " + \
            "from calendar_year a " + \
            "left join follow_follow b on b.datetime >= a.start_date and b.datetime <= a.end_date " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.year " + \
            "order by a.year "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_users_that_followed = dictfetchall(cursor)

        sql = "select a.year as date, count(b.id) as num_item_corrections " + \
            "from calendar_year a " + \
            "left join items_itemcorrection b on b.decision_date >= a.start_date and b.decision_date <= a.end_date and b.status = 2 " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.year " + \
            "order by a.year "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_item_corrections = dictfetchall(cursor)

        sql = "select a.year as date, count(distinct b.user_id) as num_users_with_item_corrections " + \
            "from calendar_year a " + \
            "left join items_itemcorrection b on b.decision_date >= a.start_date and b.decision_date <= a.end_date and b.status = 2 " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.year " + \
            "order by a.year "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_users_with_item_corrections = dictfetchall(cursor)

        sql = "select a.year as date, count(b.id) as num_peaks_approved " + \
            "from calendar_year a " + \
            "left join items_item b on b.created >= a.start_date and b.created <= a.end_date and b.user_id > 1 and not exists (select 1 from auth_user c where c.id = b.user_id and c.is_superuser = true) " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.year " + \
            "order by a.year "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_peaks_approved = dictfetchall(cursor)

        sql = "select a.year as date, count(distinct b.user_id) as num_users_with_peaks_approved " + \
            "from calendar_year a " + \
            "left join items_item b on b.created >= a.start_date and b.created <= a.end_date and b.user_id > 1 and not exists (select 1 from auth_user c where c.id = b.user_id and c.is_superuser = true) " + \
            "where a.start_date >= %s " + \
            "and a.end_date <= %s " + \
            "group by a.year " + \
            "order by a.year "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_users_with_peaks_approved = dictfetchall(cursor)

    else:

        sql = "select count(a.id) as num_signups " + \
            "from auth_user a " + \
            "left join accounts_person b on b.user_id = a.id " + \
            "where a.date_joined between %s and %s " + \
            "and coalesce(b.signup_source, 'web-desktop') = 'web-desktop' "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            signups = dictfetchall(cursor)
            signups = signups[0]['num_signups']

        sql = "select count(a.id) as mobile_signups " + \
              "from auth_user a " + \
              "left join accounts_person b on b.user_id = a.id " + \
              "where a.date_joined between %s and %s " + \
              "and b.signup_source = 'web-mobile' "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            mobile_signups = dictfetchall(cursor)
            mobile_signups = mobile_signups[0]['mobile_signups']

        sql = "select count(a.id) as android_signups " + \
              "from auth_user a " + \
              "left join accounts_person b on b.user_id = a.id " + \
              "where a.date_joined between %s and %s " + \
              "and b.signup_source = 'android-native' "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            android_signups = dictfetchall(cursor)
            android_signups = android_signups[0]['android_signups']

        sql = "select count(distinct a.user_id) as active_members " + \
              "from activity_log a " + \
              "where activity_source = 'Web' and a.activity_date >= %s " + \
              "and a.activity_date <= %s "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            active_members = dictfetchall(cursor)
            active_members = active_members[0]['active_members']

        sql = "select count(distinct a.user_id) as android_active_members " + \
              "from activity_log a " + \
              "where activity_source = 'Android' and a.activity_date >= %s " + \
              "and a.activity_date <= %s "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:
            cursor.execute(sql, sql_params)
            android_active_members = dictfetchall(cursor)
            android_active_members = android_active_members[0]['android_active_members']


        sql = "select count(distinct a.user_id) as ios_active_members " + \
              "from activity_log a " + \
              "where activity_source = 'Android' and a.activity_date >= %s " + \
              "and a.activity_date <= %s "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:
            cursor.execute(sql, sql_params)
            ios_active_members = dictfetchall(cursor)
            ios_active_members = ios_active_members[0]['ios_active_members']

        sql = "select count(distinct a.id) as signups_that_logged " + \
            "from auth_user a " + \
            "join items_summitlog b on b.user_id = a.id " + \
            "where a.date_joined between %s and %s " + \
            "and b.status = 1 and b.created between %s and %s "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            signups_that_logged = dictfetchall(cursor)
            signups_that_logged = signups_that_logged[0]['signups_that_logged']

        sql = "select count(distinct a.id) as members_that_logged " + \
            "from auth_user a " + \
            "join items_summitlog b on b.user_id = a.id " + \
            "where b.status = 1 and b.created between %s and %s "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            members_that_logged = dictfetchall(cursor)
            members_that_logged = members_that_logged[0]['members_that_logged']

        sql = "select count(a.id) as climbs_logged " + \
            "from items_summitlog a " + \
            "where a.status = 1 and a.created between %s and %s "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            climbs_logged = dictfetchall(cursor)
            climbs_logged = climbs_logged[0]['climbs_logged']

        sql = "select count(a.id) as climbs_with_photos " + \
            "from items_summitlog a " + \
            "where a.status = 1 and a.created between %s and %s " + \
            "and exists (select 1 from items_itemphoto b where b.summit_log_id = a.id) "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            climbs_with_photos = dictfetchall(cursor)
            climbs_with_photos = climbs_with_photos[0]['climbs_with_photos']

        sql = "select count(a.id) as climbs_with_trip_report " + \
            "from items_summitlog a " + \
            "where a.status = 1 and a.created between %s and %s " + \
            "and length(a.log) > 0 "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            climbs_with_trip_report = dictfetchall(cursor)
            climbs_with_trip_report = climbs_with_trip_report[0]['climbs_with_trip_report']

        sql = "select count(a.id) as climbs_with_gpx " + \
            "from items_summitlog a " + \
            "where a.status = 1 and a.created between %s and %s " + \
            "and length(a.gpx_file) > 0 "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            climbs_with_gpx = dictfetchall(cursor)
            climbs_with_gpx = climbs_with_gpx[0]['climbs_with_gpx']

        sql = "select count(a.id) as num_likes " + \
            "from favorites_favorite a " + \
            "where a.created_on between %s and %s "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_likes = dictfetchall(cursor)
            num_likes = num_likes[0]['num_likes']

        sql = "select count(distinct a.user_id) as num_users_that_liked " + \
            "from favorites_favorite a " + \
            "where a.created_on between %s and %s "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_users_that_liked = dictfetchall(cursor)
            num_users_that_liked = num_users_that_liked[0]['num_users_that_liked']

        sql = "select count(a.id) as num_comments " + \
            "from items_summitlogcomment a " + \
            "where a.created between %s and %s "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_comments = dictfetchall(cursor)
            num_comments = num_comments[0]['num_comments']

        sql = "select count(distinct a.user_id) as num_users_that_commented " + \
            "from items_summitlogcomment a " + \
            "where a.created between %s and %s "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_users_that_commented = dictfetchall(cursor)
            num_users_that_commented = num_users_that_commented[0]['num_users_that_commented']

        sql = "select count(a.id) as num_follows " + \
            "from follow_follow a " + \
            "where a.datetime between %s and %s "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_follows = dictfetchall(cursor)
            num_follows = num_follows[0]['num_follows']

        sql = "select count(distinct a.user_id) as num_users_that_followed " + \
            "from follow_follow a " + \
            "where a.datetime between %s and %s "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_users_that_followed = dictfetchall(cursor)
            num_users_that_followed = num_users_that_followed[0]['num_users_that_followed']

        sql = "select count(a.id) as num_item_corrections " + \
            "from items_itemcorrection a " + \
            "where a.status = 2 and a.decision_date between %s and %s "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_item_corrections = dictfetchall(cursor)
            num_item_corrections = num_item_corrections[0]['num_item_corrections']

        sql = "select count(distinct a.user_id) as num_users_with_item_corrections " + \
            "from items_itemcorrection a " + \
            "where a.status = 2 and a.decision_date between %s and %s "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_users_with_item_corrections = dictfetchall(cursor)
            num_users_with_item_corrections = num_users_with_item_corrections[0]['num_users_with_item_corrections']

        sql = "select count(a.id) as num_peaks_approved " + \
            "from items_item a " + \
            "join auth_user b on b.id = a.user_id and b.is_superuser = false " + \
            "where a.user_id > 1 and a.created between %s and %s "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_peaks_approved = dictfetchall(cursor)
            num_peaks_approved = num_peaks_approved[0]['num_peaks_approved']

        sql = "select count(distinct a.user_id) as num_users_with_peaks_approved " + \
            "from items_item a " + \
            "join auth_user b on b.id = a.user_id and b.is_superuser = false " + \
            "where a.user_id > 1 and a.created between %s and %s "

        sql_params = []
        sql_params.append(start_date)
        sql_params.append(end_date)

        with connection.cursor() as cursor:

            cursor.execute(sql, sql_params)

            num_users_with_peaks_approved = dictfetchall(cursor)
            num_users_with_peaks_approved = num_users_with_peaks_approved[0]['num_users_with_peaks_approved']

    return render(request, 'tools/dashboard.html', {
        'start_date':start_date,
        'end_date':end_date,
        'display_type':display_type,
        'display_format':display_format,
        'num_signups':signups,
        'mobile_signups': mobile_signups,
        'android_signups': android_signups,
        'active_members': active_members,
        'android_active_members':android_active_members,
        'ios_active_members': ios_active_members,
        'signups_that_logged':signups_that_logged,
        'members_that_logged':members_that_logged,
        'climbs_logged':climbs_logged,
        'climbs_with_photos':climbs_with_photos,
        'climbs_with_trip_report':climbs_with_trip_report,
        'climbs_with_gpx':climbs_with_gpx,
        'num_likes':num_likes,
        'num_users_that_liked':num_users_that_liked,
        'num_comments':num_comments,
        'num_users_that_commented':num_users_that_commented,
        'num_follows':num_follows,
        'num_users_that_followed':num_users_that_followed,
        'num_item_corrections':num_item_corrections,
        'num_users_with_item_corrections':num_users_with_item_corrections,
        'num_peaks_approved':num_peaks_approved,
        'num_users_with_peaks_approved':num_users_with_peaks_approved,
    })