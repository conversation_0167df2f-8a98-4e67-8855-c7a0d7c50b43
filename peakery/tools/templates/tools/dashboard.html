{% extends "admin/base_site.html" %}
{% load static %}
{% block content %}

<link rel="stylesheet" href="{% static '' %}css/jquery-ui.min.css">
<script src="{% static '' %}js/jquery.min.js"></script>
<script src="{% static '' %}js/jquery-ui.min.js"></script>

<script src="https://www.gstatic.com/charts/loader.js"></script>

<h4>KPI Dashboard</h4>

<style>

    .section-header {
        margin-bottom: 20px;
    }

</style>

<form action="/tools/dashboard/" method="post" style="margin-bottom: 20px;">
<label for="from">From</label>
<input type="text" id="from" name="from">
<label for="to">to</label>
<input type="text" id="to" name="to">
<select name="display_type" id="display_type" size="1">
    <option value="yearly">Yearly</option>
    <option value="monthly">Monthly</option>
    <option value="weekly">Weekly</option>
    <option value="daily">Daily</option>
    <option value="total">Total</option>
</select>
<select name="display_format" id="display_format" size="1">
    <option value="chart">Chart</option>
    <option value="table">Table</option>
</select>
<input type="submit" name="submit" id="submit" value="Submit">
</form>

{% if display_type != 'total' %}

{% if num_signups %}

    <div id="num-signups-header" class="col-md-12 section-header"><h2>Number of Web Signups</h2></div>

    <div id="num-signups-table-container" class="row sub-header-row" style="width: 94%; margin-left: 3%; background-color: #fff; display: none;">
        <div class="col-md-12">
            <div id="num-signups-table-div"></div>
        </div>
    </div>

    <div id="num-signups-chart-container" class="row sub-header-row" style="margin: 20px; height: 500px; background-color: #fff; display: none;">
        <div class="col-md-12" style="height: 500px;">
            <div id="num-signups-chart-div" style="height: 500px;"></div>
        </div>
    </div>

    <div id="num-mobile-signups-header" class="col-md-12 section-header"><h2>Number of Mobile Signups</h2></div>

    <div id="num-mobile-signups-table-container" class="row sub-header-row" style="width: 94%; margin-left: 3%; background-color: #fff; display: none;">
        <div class="col-md-12">
            <div id="num-mobile-signups-table-div"></div>
        </div>
    </div>

    <div id="num-mobile-signups-chart-container" class="row sub-header-row" style="margin: 20px; height: 500px; background-color: #fff; display: none;">
        <div class="col-md-12" style="height: 500px;">
            <div id="num-mobile-signups-chart-div" style="height: 500px;"></div>
        </div>
    </div>

    <div id="num-android-signups-header" class="col-md-12 section-header"><h2>Number of Android Signups</h2></div>

    <div id="num-android-signups-table-container" class="row sub-header-row" style="width: 94%; margin-left: 3%; background-color: #fff; display: none;">
        <div class="col-md-12">
            <div id="num-android-signups-table-div"></div>
        </div>
    </div>

    <div id="num-android-signups-chart-container" class="row sub-header-row" style="margin: 20px; height: 500px; background-color: #fff; display: none;">
        <div class="col-md-12" style="height: 500px;">
            <div id="num-android-signups-chart-div" style="height: 500px;"></div>
        </div>
    </div>

    <div id="active-members-header" class="col-md-12 section-header"><h2>Number of Active Members - Web</h2></div>

    <div id="active-members-table-container" class="row sub-header-row" style="width: 94%; margin-left: 3%; background-color: #fff; display: none;">
        <div class="col-md-12">
            <div id="active-members-table-div"></div>
        </div>
    </div>

    <div id="active-members-chart-container" class="row sub-header-row" style="margin: 20px; height: 500px; background-color: #fff; display: none;">
        <div class="col-md-12" style="height: 500px;">
            <div id="active-members-chart-div" style="height: 500px;"></div>
        </div>
    </div>

    <div id="android-active-members-header" class="col-md-12 section-header"><h2>Number of Active Members - Android</h2></div>

    <div id="android-active-members-table-container" class="row sub-header-row" style="width: 94%; margin-left: 3%; background-color: #fff; display: none;">
        <div class="col-md-12">
            <div id="android-active-members-table-div"></div>
        </div>
    </div>

    <div id="android-active-members-chart-container" class="row sub-header-row" style="margin: 20px; height: 500px; background-color: #fff; display: none;">
        <div class="col-md-12" style="height: 500px;">
            <div id="android-active-members-chart-div" style="height: 500px;"></div>
        </div>
    </div>

    <div id="ios-active-members-header" class="col-md-12 section-header"><h2>Number of Active Members - IOS</h2></div>

    <div id="ios-active-members-table-container" class="row sub-header-row" style="width: 94%; margin-left: 3%; background-color: #fff; display: none;">
        <div class="col-md-12">
            <div id="ios-active-members-table-div"></div>
        </div>
    </div>

    <div id="ios-active-members-chart-container" class="row sub-header-row" style="margin: 20px; height: 500px; background-color: #fff; display: none;">
        <div class="col-md-12" style="height: 500px;">
            <div id="ios-active-members-chart-div" style="height: 500px;"></div>
        </div>
    </div>

    <div id="num-signups-that-logged-header" class="col-md-12 section-header"><h2>Signups That Logged Climbs</h2></div>

    <div id="num-signups-that-logged-table-container" class="row sub-header-row" style="width: 94%; margin-left: 3%; background-color: #fff; display: none;">
        <div class="col-md-12">
            <div id="num-signups-that-logged-table-div"></div>
        </div>
    </div>

    <div id="num-signups-that-logged-chart-container" class="row sub-header-row" style="margin: 20px; height: 500px; background-color: #fff; display: none;">
        <div class="col-md-12" style="height: 500px;">
            <div id="num-signups-that-logged-chart-div" style="height: 500px;"></div>
        </div>
    </div>

    <div id="members-that-logged-header" class="col-md-12 section-header"><h2>Members That Logged Climbs</h2></div>

    <div id="members-that-logged-table-container" class="row sub-header-row" style="width: 94%; margin-left: 3%; background-color: #fff; display: none;">
        <div class="col-md-12">
            <div id="members-that-logged-table-div"></div>
        </div>
    </div>

    <div id="members-that-logged-chart-container" class="row sub-header-row" style="margin: 20px; height: 500px; background-color: #fff; display: none;">
        <div class="col-md-12" style="height: 500px;">
            <div id="members-that-logged-chart-div" style="height: 500px;"></div>
        </div>
    </div>

    <div id="climbs-logged-header" class="col-md-12 section-header"><h2>Climbs Logged</h2></div>

    <div id="climbs-logged-table-container" class="row sub-header-row" style="width: 94%; margin-left: 3%; background-color: #fff; display: none;">
        <div class="col-md-12">
            <div id="climbs-logged-table-div"></div>
        </div>
    </div>

    <div id="climbs-logged-chart-container" class="row sub-header-row" style="margin: 20px; height: 500px; background-color: #fff; display: none;">
        <div class="col-md-12" style="height: 500px;">
            <div id="climbs-logged-chart-div" style="height: 500px;"></div>
        </div>
    </div>

    <div id="climbs-with-photos-header" class="col-md-12 section-header"><h2>Climbs With Photos</h2></div>

    <div id="climbs-with-photos-table-container" class="row sub-header-row" style="width: 94%; margin-left: 3%; background-color: #fff; display: none;">
        <div class="col-md-12">
            <div id="climbs-with-photos-table-div"></div>
        </div>
    </div>

    <div id="climbs-with-photos-chart-container" class="row sub-header-row" style="margin: 20px; height: 500px; background-color: #fff; display: none;">
        <div class="col-md-12" style="height: 500px;">
            <div id="climbs-with-photos-chart-div" style="height: 500px;"></div>
        </div>
    </div>

    <div id="climbs-with-trip-report-header" class="col-md-12 section-header"><h2>Climbs With Trip Report</h2></div>

    <div id="climbs-with-trip-report-table-container" class="row sub-header-row" style="width: 94%; margin-left: 3%; background-color: #fff; display: none;">
        <div class="col-md-12">
            <div id="climbs-with-trip-report-table-div"></div>
        </div>
    </div>

    <div id="climbs-with-trip-report-chart-container" class="row sub-header-row" style="margin: 20px; height: 500px; background-color: #fff; display: none;">
        <div class="col-md-12" style="height: 500px;">
            <div id="climbs-with-trip-report-chart-div" style="height: 500px;"></div>
        </div>
    </div>

    <div id="climbs-with-gpx-header" class="col-md-12 section-header"><h2>Climbs With GPX</h2></div>

    <div id="climbs-with-gpx-table-container" class="row sub-header-row" style="width: 94%; margin-left: 3%; background-color: #fff; display: none;">
        <div class="col-md-12">
            <div id="climbs-with-gpx-table-div"></div>
        </div>
    </div>

    <div id="climbs-with-gpx-chart-container" class="row sub-header-row" style="margin: 20px; height: 500px; background-color: #fff; display: none;">
        <div class="col-md-12" style="height: 500px;">
            <div id="climbs-with-gpx-chart-div" style="height: 500px;"></div>
        </div>
    </div>

    <div id="num-likes-header" class="col-md-12 section-header"><h2>Number of Likes</h2></div>

    <div id="num-likes-table-container" class="row sub-header-row" style="width: 94%; margin-left: 3%; background-color: #fff; display: none;">
        <div class="col-md-12">
            <div id="num-likes-table-div"></div>
        </div>
    </div>

    <div id="num-likes-chart-container" class="row sub-header-row" style="margin: 20px; height: 500px; background-color: #fff; display: none;">
        <div class="col-md-12" style="height: 500px;">
            <div id="num-likes-chart-div" style="height: 500px;"></div>
        </div>
    </div>

    <div id="num-users-that-liked-header" class="col-md-12 section-header"><h2>Number of Users That Liked</h2></div>

    <div id="num-users-that-liked-table-container" class="row sub-header-row" style="width: 94%; margin-left: 3%; background-color: #fff; display: none;">
        <div class="col-md-12">
            <div id="num-users-that-liked-table-div"></div>
        </div>
    </div>

    <div id="num-users-that-liked-chart-container" class="row sub-header-row" style="margin: 20px; height: 500px; background-color: #fff; display: none;">
        <div class="col-md-12" style="height: 500px;">
            <div id="num-users-that-liked-chart-div" style="height: 500px;"></div>
        </div>
    </div>

    <div id="num-comments-header" class="col-md-12 section-header"><h2>Number of Comments</h2></div>

    <div id="num-comments-table-container" class="row sub-header-row" style="width: 94%; margin-left: 3%; background-color: #fff; display: none;">
        <div class="col-md-12">
            <div id="num-comments-table-div"></div>
        </div>
    </div>

    <div id="num-comments-chart-container" class="row sub-header-row" style="margin: 20px; height: 500px; background-color: #fff; display: none;">
        <div class="col-md-12" style="height: 500px;">
            <div id="num-comments-chart-div" style="height: 500px;"></div>
        </div>
    </div>

    <div id="num-users-that-commented-header" class="col-md-12 section-header"><h2>Number of Users That Commented</h2></div>

    <div id="num-users-that-commented-table-container" class="row sub-header-row" style="width: 94%; margin-left: 3%; background-color: #fff; display: none;">
        <div class="col-md-12">
            <div id="num-users-that-commented-table-div"></div>
        </div>
    </div>

    <div id="num-users-that-commented-chart-container" class="row sub-header-row" style="margin: 20px; height: 500px; background-color: #fff; display: none;">
        <div class="col-md-12" style="height: 500px;">
            <div id="num-users-that-commented-chart-div" style="height: 500px;"></div>
        </div>
    </div>

    <div id="num-follows-header" class="col-md-12 section-header"><h2>Number of Follows</h2></div>

    <div id="num-follows-table-container" class="row sub-header-row" style="width: 94%; margin-left: 3%; background-color: #fff; display: none;">
        <div class="col-md-12">
            <div id="num-follows-table-div"></div>
        </div>
    </div>

    <div id="num-follows-chart-container" class="row sub-header-row" style="margin: 20px; height: 500px; background-color: #fff; display: none;">
        <div class="col-md-12" style="height: 500px;">
            <div id="num-follows-chart-div" style="height: 500px;"></div>
        </div>
    </div>

    <div id="num-users-that-followed-header" class="col-md-12 section-header"><h2>Number of Users That Followed</h2></div>

    <div id="num-users-that-followed-table-container" class="row sub-header-row" style="width: 94%; margin-left: 3%; background-color: #fff; display: none;">
        <div class="col-md-12">
            <div id="num-users-that-followed-table-div"></div>
        </div>
    </div>

    <div id="num-users-that-followed-chart-container" class="row sub-header-row" style="margin: 20px; height: 500px; background-color: #fff; display: none;">
        <div class="col-md-12" style="height: 500px;">
            <div id="num-users-that-followed-chart-div" style="height: 500px;"></div>
        </div>
    </div>

    <div id="num-item-corrections-header" class="col-md-12 section-header"><h2>Number of Item Corrections Approved</h2></div>

    <div id="num-item-corrections-table-container" class="row sub-header-row" style="width: 94%; margin-left: 3%; background-color: #fff; display: none;">
        <div class="col-md-12">
            <div id="num-item-corrections-table-div"></div>
        </div>
    </div>

    <div id="num-item-corrections-chart-container" class="row sub-header-row" style="margin: 20px; height: 500px; background-color: #fff; display: none;">
        <div class="col-md-12" style="height: 500px;">
            <div id="num-item-corrections-chart-div" style="height: 500px;"></div>
        </div>
    </div>

    <div id="num-users-with-item-corrections-header" class="col-md-12 section-header"><h2>Number of Users With Item Corrections Approved</h2></div>

    <div id="num-users-with-item-corrections-table-container" class="row sub-header-row" style="width: 94%; margin-left: 3%; background-color: #fff; display: none;">
        <div class="col-md-12">
            <div id="num-users-with-item-corrections-table-div"></div>
        </div>
    </div>

    <div id="num-users-with-item-corrections-chart-container" class="row sub-header-row" style="margin: 20px; height: 500px; background-color: #fff; display: none;">
        <div class="col-md-12" style="height: 500px;">
            <div id="num-users-with-item-corrections-chart-div" style="height: 500px;"></div>
        </div>
    </div>

    <div id="num-peaks-header" class="col-md-12 section-header"><h2>Number of Peaks Approved</h2></div>

    <div id="num-peaks-table-container" class="row sub-header-row" style="width: 94%; margin-left: 3%; background-color: #fff; display: none;">
        <div class="col-md-12">
            <div id="num-peaks-table-div"></div>
        </div>
    </div>

    <div id="num-peaks-chart-container" class="row sub-header-row" style="margin: 20px; height: 500px; background-color: #fff; display: none;">
        <div class="col-md-12" style="height: 500px;">
            <div id="num-peaks-chart-div" style="height: 500px;"></div>
        </div>
    </div>

    <div id="num-users-with-peaks-header" class="col-md-12 section-header"><h2>Number of Users With Peaks Approved</h2></div>

    <div id="num-users-with-peaks-table-container" class="row sub-header-row" style="width: 94%; margin-left: 3%; background-color: #fff; display: none;">
        <div class="col-md-12">
            <div id="num-users-with-peaks-table-div"></div>
        </div>
    </div>

    <div id="num-users-with-peaks-chart-container" class="row sub-header-row" style="margin: 20px; height: 500px; background-color: #fff; display: none;">
        <div class="col-md-12" style="height: 500px;">
            <div id="num-users-with-peaks-chart-div" style="height: 500px;"></div>
        </div>
    </div>

{% endif %}

{% else %}

{% if num_signups %}
<br />
Web Signups: {{ num_signups }}
<br />
Mobile Signups: {{ mobile_signups }}
<br />
Android Signups: {{ android_signups }}
<br />
Active Members: {{ active_members }}
<br />
Android Active Members: {{ android_active_members }}
<br />
Signups That Logged Climbs: {{ signups_that_logged }}
<br />
Members That Logged Climbs: {{ members_that_logged }}
<br />
Climbs Logged: {{ climbs_logged }}
<br />
Climbs With Photos: {{ climbs_with_photos }}
<br />
Climbs With Trip Report: {{ climbs_with_trip_report }}
<br />
Climbs With GPX: {{ climbs_with_gpx }}
<br />
Number of Likes: {{ num_likes }}
<br />
Number of Users That Liked: {{ num_users_that_liked }}
<br />
Number of Comments: {{ num_comments }}
<br />
Number of Users That Commented: {{ num_users_that_commented }}
<br />
Number of Follows: {{ num_follows }}
<br />
Number of Users That Followed: {{ num_users_that_followed }}
<br />
Number of Item Corrections Approved: {{ num_item_corrections }}
<br />
Number of Users With Item Corrections Approved: {{ num_users_with_item_corrections }}
<br />
Number of Peaks Approved: {{ num_peaks_approved }}
<br />
Number of Users With Peaks Approved: {{ num_users_with_peaks_approved }}

{% endif %}

{% endif %}

<script>

    var numSignupsChartData = [];
    var numSignupsTableData = [];
    var mobileSignupsChartData = [];
    var mobileSignupsTableData = [];
    var androidSignupsChartData = [];
    var androidSignupsTableData = [];
    var activeMembersChartData = [];
    var activeMembersTableData = [];
    var androidActiveMembersChartData = [];
    var androidActiveMembersTableData = [];
    var iosActiveMembersChartData = [];
    var iosActiveMembersTableData = [];
    var numSignupsThatLoggedChartData = [];
    var numSignupsThatLoggedTableData = [];
    var membersThatLoggedChartData = [];
    var membersThatLoggedTableData = [];
    var climbsLoggedChartData = [];
    var climbsLoggedTableData = [];
    var climbsWithPhotosChartData = [];
    var climbsWithPhotosTableData = [];
    var climbsWithTripReportChartData = [];
    var climbsWithTripReportTableData = [];
    var climbsWithGpxChartData = [];
    var climbsWithGpxTableData = [];
    var numLikesChartData = [];
    var numLikesTableData = [];
    var numUsersThatLikedChartData = [];
    var numUsersThatLikedTableData = [];
    var numCommentsChartData = [];
    var numCommentsTableData = [];
    var numUsersThatCommentedChartData = [];
    var numUsersThatCommentedTableData = [];
    var numFollowsChartData = [];
    var numFollowsTableData = [];
    var numUsersThatFollowedChartData = [];
    var numUsersThatFollowedTableData = [];
    var numItemCorrectionsChartData = [];
    var numItemCorrectionsTableData = [];
    var numUsersWithItemCorrectionsChartData = [];
    var numUsersWithItemCorrectionsTableData = [];
    var numPeaksChartData = [];
    var numPeaksTableData = [];
    var numUsersWithPeaksChartData = [];
    var numUsersWithPeaksTableData = [];

    var tableData = [];

    $( function() {
    var dateFormat = "mm/dd/yy",
      from = $( "#from" )
        .datepicker({
          defaultDate: "+1w",
          changeMonth: true,
          changeYear: true,
          numberOfMonths: 1
        })
        .on( "change", function() {
          to.datepicker( "option", "minDate", getDate( this ) );
        }),
      to = $( "#to" ).datepicker({
        defaultDate: "+1w",
        changeMonth: true,
        changeYear: true,
        numberOfMonths: 1
      })
      .on( "change", function() {
        from.datepicker( "option", "maxDate", getDate( this ) );
      });

    function getDate( element ) {
      var date;
      try {
        date = $.datepicker.parseDate( dateFormat, element.value );
      } catch( error ) {
        date = null;
      }

      return date;
    }

    var startDate = '{{ start_date }}';
    var endDate = '{{ end_date }}';

    if (startDate != '') {
        $( "#from" ).datepicker( "setDate", startDate.substring(0,10) );
        $( "#from" ).val(startDate.substring(0,10));
    }
    if (endDate != '') {
        $( "#to" ).datepicker( "setDate", endDate.substring(0,10) );
        $( "#to" ).val(endDate.substring(0,10));
    }

    $( "#display_type").val('{{ display_type }}');
    $( "#display_format").val('{{ display_format }}');

    google.charts.load('current', {packages: ['corechart', 'table']});
    google.charts.setOnLoadCallback(init);

    } );

    function init() {

        {% if display_type != 'total' %}

            numSignupsChartData = [
                ['Date', 'Signups', {role: 'annotation'}],
                {% for n in num_signups %}
                    ['{{ n.date }}', {{ n.num_signups }}, '{{ n.num_signups }}'],
                {% endfor %}
            ];

            numSignupsTableData = [
                ['Date', 'Signups'],
                {% for n in num_signups %}
                    ['{{ n.date }}', {{ n.num_signups }}],
                {% endfor %}
            ];

            mobileSignupsChartData = [
                ['Date', 'Signups', {role: 'annotation'}],
                {% for n in mobile_signups %}
                    ['{{ n.date }}', {{ n.mobile_signups }}, '{{ n.mobile_signups }}'],
                {% endfor %}
            ];

            mobileSignupsTableData = [
                ['Date', 'Signups'],
                {% for n in mobile_signups %}
                    ['{{ n.date }}', {{ n.mobile_signups }}],
                {% endfor %}
            ];

            androidSignupsChartData = [
                ['Date', 'Signups', {role: 'annotation'}],
                {% for n in android_signups %}
                    ['{{ n.date }}', {{ n.android_signups }}, '{{ n.android_signups }}'],
                {% endfor %}
            ];

            androidSignupsTableData = [
                ['Date', 'Signups'],
                {% for n in android_signups %}
                    ['{{ n.date }}', {{ n.android_signups }}],
                {% endfor %}
            ];

            activeMembersChartData = [
                ['Date', 'Members', {role: 'annotation'}],
                {% for n in active_members %}
                    ['{{ n.date }}', {{ n.active_members }}, '{{ n.active_members }}'],
                {% endfor %}
            ];

            activeMembersTableData = [
                ['Date', 'Members'],
                {% for n in active_members %}
                    ['{{ n.date }}', {{ n.active_members }}],
                {% endfor %}
            ];

            androidActiveMembersChartData = [
                ['Date', 'Members', {role: 'annotation'}],
                {% for n in android_active_members %}
                    ['{{ n.date }}', {{ n.android_active_members }}, '{{ n.android_active_members }}'],
                {% endfor %}
            ];

            androidActiveMembersTableData = [
                ['Date', 'Members'],
                {% for n in android_active_members %}
                    ['{{ n.date }}', {{ n.android_active_members }}],
                {% endfor %}
            ];

            iosActiveMembersChartData = [
                ['Date', 'Members', {role: 'annotation'}],
                {% for n in ios_active_members %}
                    ['{{ n.date }}', {{ n.ios_active_members }}, '{{ n.ios_active_members }}'],
                {% endfor %}
            ];

            iosActiveMembersTableData = [
                ['Date', 'Members'],
                {% for n in ios_active_members %}
                    ['{{ n.date }}', {{ n.ios_active_members }}],
                {% endfor %}
            ];

            numSignupsThatLoggedChartData = [
                ['Date', 'Signups', {role: 'annotation'}],
                {% for n in signups_that_logged %}
                    ['{{ n.date }}', {{ n.signups_that_logged }}, '{{ n.signups_that_logged }}'],
                {% endfor %}
            ];

            numSignupsThatLoggedTableData = [
                ['Date', 'Signups'],
                {% for n in signups_that_logged %}
                    ['{{ n.date }}', {{ n.signups_that_logged }}],
                {% endfor %}
            ];

            membersThatLoggedChartData = [
                ['Date', 'Members', {role: 'annotation'}],
                {% for n in members_that_logged %}
                    ['{{ n.date }}', {{ n.members_that_logged }}, '{{ n.members_that_logged }}'],
                {% endfor %}
            ];

            membersThatLoggedTableData = [
                ['Date', 'Members'],
                {% for n in members_that_logged %}
                    ['{{ n.date }}', {{ n.members_that_logged }}],
                {% endfor %}
            ];

            climbsLoggedChartData = [
                ['Date', 'Climbs', {role: 'annotation'}],
                {% for n in climbs_logged %}
                    ['{{ n.date }}', {{ n.climbs_logged }}, '{{ n.climbs_logged }}'],
                {% endfor %}
            ];

            climbsLoggedTableData = [
                ['Date', 'Climbs'],
                {% for n in climbs_logged %}
                    ['{{ n.date }}', {{ n.climbs_logged }}],
                {% endfor %}
            ];

            climbsWithPhotosChartData = [
                ['Date', 'Climbs', {role: 'annotation'}],
                {% for n in climbs_with_photos %}
                    ['{{ n.date }}', {{ n.climbs_with_photos }}, '{{ n.climbs_with_photos }}'],
                {% endfor %}
            ];

            climbsWithPhotosTableData = [
                ['Date', 'Climbs'],
                {% for n in climbs_with_photos %}
                    ['{{ n.date }}', {{ n.climbs_with_photos }}],
                {% endfor %}
            ];

            climbsWithTripReportChartData = [
                ['Date', 'Climbs', {role: 'annotation'}],
                {% for n in climbs_with_trip_report %}
                    ['{{ n.date }}', {{ n.climbs_with_trip_report }}, '{{ n.climbs_with_trip_report }}'],
                {% endfor %}
            ];

            climbsWithTripReportTableData = [
                ['Date', 'Climbs'],
                {% for n in climbs_with_trip_report %}
                    ['{{ n.date }}', {{ n.climbs_with_trip_report }}],
                {% endfor %}
            ];

            climbsWithGpxChartData = [
                ['Date', 'Climbs', {role: 'annotation'}],
                {% for n in climbs_with_gpx %}
                    ['{{ n.date }}', {{ n.climbs_with_gpx }}, '{{ n.climbs_with_gpx }}'],
                {% endfor %}
            ];

            climbsWithGpxTableData = [
                ['Date', 'Climbs'],
                {% for n in climbs_with_gpx %}
                    ['{{ n.date }}', {{ n.climbs_with_gpx }}],
                {% endfor %}
            ];

            numLikesChartData = [
                ['Date', 'Likes', {role: 'annotation'}],
                {% for n in num_likes %}
                    ['{{ n.date }}', {{ n.num_likes }}, '{{ n.num_likes }}'],
                {% endfor %}
            ];

            numLikesTableData = [
                ['Date', 'Likes'],
                {% for n in num_likes %}
                    ['{{ n.date }}', {{ n.num_likes }}],
                {% endfor %}
            ];

            numUsersThatLikedChartData = [
                ['Date', 'Users', {role: 'annotation'}],
                {% for n in num_users_that_liked %}
                    ['{{ n.date }}', {{ n.num_users_that_liked }}, '{{ n.num_users_that_liked }}'],
                {% endfor %}
            ];

            numUsersThatLikedTableData = [
                ['Date', 'Users'],
                {% for n in num_users_that_liked %}
                    ['{{ n.date }}', {{ n.num_users_that_liked }}],
                {% endfor %}
            ];

            numCommentsChartData = [
                ['Date', 'Comments', {role: 'annotation'}],
                {% for n in num_comments %}
                    ['{{ n.date }}', {{ n.num_comments }}, '{{ n.num_comments }}'],
                {% endfor %}
            ];

            numCommentsTableData = [
                ['Date', 'Comments'],
                {% for n in num_comments %}
                    ['{{ n.date }}', {{ n.num_comments }}],
                {% endfor %}
            ];

            numUsersThatCommentedChartData = [
                ['Date', 'Users', {role: 'annotation'}],
                {% for n in num_users_that_commented %}
                    ['{{ n.date }}', {{ n.num_users_that_commented }}, '{{ n.num_users_that_commented }}'],
                {% endfor %}
            ];

            numUsersThatCommentedTableData = [
                ['Date', 'Users'],
                {% for n in num_users_that_commented %}
                    ['{{ n.date }}', {{ n.num_users_that_commented }}],
                {% endfor %}
            ];

            numFollowsChartData = [
                ['Date', 'Follows', {role: 'annotation'}],
                {% for n in num_follows %}
                    ['{{ n.date }}', {{ n.num_follows }}, '{{ n.num_follows }}'],
                {% endfor %}
            ];

            numFollowsTableData = [
                ['Date', 'Follows'],
                {% for n in num_follows %}
                    ['{{ n.date }}', {{ n.num_follows }}],
                {% endfor %}
            ];

            numUsersThatFollowedChartData = [
                ['Date', 'Users', {role: 'annotation'}],
                {% for n in num_users_that_followed %}
                    ['{{ n.date }}', {{ n.num_users_that_followed }}, '{{ n.num_users_that_followed }}'],
                {% endfor %}
            ];

            numUsersThatFollowedTableData = [
                ['Date', 'Users'],
                {% for n in num_users_that_followed %}
                    ['{{ n.date }}', {{ n.num_users_that_followed }}],
                {% endfor %}
            ];

            numItemCorrectionsChartData = [
                ['Date', 'Item Corrections', {role: 'annotation'}],
                {% for n in num_item_corrections %}
                    ['{{ n.date }}', {{ n.num_item_corrections }}, '{{ n.num_item_corrections }}'],
                {% endfor %}
            ];

            numItemCorrectionsTableData = [
                ['Date', 'Item Corrections'],
                {% for n in num_item_corrections %}
                    ['{{ n.date }}', {{ n.num_item_corrections }}],
                {% endfor %}
            ];

            numUsersWithItemCorrectionsChartData = [
                ['Date', 'Users', {role: 'annotation'}],
                {% for n in num_users_with_item_corrections %}
                    ['{{ n.date }}', {{ n.num_users_with_item_corrections }}, '{{ n.num_users_with_item_corrections }}'],
                {% endfor %}
            ];

            numUsersWithItemCorrectionsTableData = [
                ['Date', 'Users'],
                {% for n in num_users_with_item_corrections %}
                    ['{{ n.date }}', {{ n.num_users_with_item_corrections }}],
                {% endfor %}
            ];

            numPeaksChartData = [
                ['Date', 'Peaks', {role: 'annotation'}],
                {% for n in num_peaks_approved %}
                    ['{{ n.date }}', {{ n.num_peaks_approved }}, '{{ n.num_peaks_approved }}'],
                {% endfor %}
            ];

            numPeaksTableData = [
                ['Date', 'Peaks'],
                {% for n in num_peaks_approved %}
                    ['{{ n.date }}', {{ n.num_peaks_approved }}],
                {% endfor %}
            ];

            numUsersWithPeaksChartData = [
                ['Date', 'Users', {role: 'annotation'}],
                {% for n in num_users_with_peaks_approved %}
                    ['{{ n.date }}', {{ n.num_users_with_peaks_approved }}, '{{ n.num_users_with_peaks_approved }}'],
                {% endfor %}
            ];

            numUsersWithPeaksTableData = [
                ['Date', 'Users'],
                {% for n in num_users_with_peaks_approved %}
                    ['{{ n.date }}', {{ n.num_users_with_peaks_approved }}],
                {% endfor %}
            ];

            tableData = [
                ['Chart Time Period', 'Signups', 'Mobile Signups', 'Android Signups', 'Active Members', 'Android Active Members', 'Signups That Logged', 'Signups That Logged Pct', 'Members That Logged', 'Climbs Logged', 'Climbs With Photos', 'Climbs With Photos Pct', 'Climbs With Trip Report', 'Climbs With Trip Report Pct', 'Climbs With GPX', 'Climbs With GPX Pct', 'Likes', 'Users That Liked', 'Comments', 'Users That Commented', 'Follows', 'Users That Follwed', 'Item Corrections', 'Users With Item Corrections', 'Peaks Approved', 'Users With Peaks Approved'],
                {% for n in num_signups %}
                    ['{{ n.date }}', {{ n.num_signups }}, 0],
                {% endfor %}
            ];

            {% for n in mobile_signups %}
                tableData[{{ forloop.counter }}][2] = {{ n.mobile_signups }};
            {% endfor %}

            {% for n in android_signups %}
                tableData[{{ forloop.counter }}][3] = {{ n.android_signups }};
            {% endfor %}

            {% for n in active_members %}
                tableData[{{ forloop.counter }}][4] = {{ n.active_members }};
            {% endfor %}

            {% for n in android_active_members %}
                tableData[{{ forloop.counter }}][5] = {{ n.android_active_members }};
            {% endfor %}

            {% for n in signups_that_logged %}
                tableData[{{ forloop.counter }}][6] = {{ n.signups_that_logged }};
                tableData[{{ forloop.counter }}][7] = parseFloat((100 * (parseFloat({{ n.signups_that_logged }}) / parseFloat(tableData[{{ forloop.counter }}][1])))).toFixed(2);
            {% endfor %}

            {% for n in members_that_logged %}
                tableData[{{ forloop.counter }}][8] = {{ n.members_that_logged }};
            {% endfor %}

            {% for n in climbs_logged %}
                tableData[{{ forloop.counter }}][9] = {{ n.climbs_logged }};
            {% endfor %}

            {% for n in climbs_with_photos %}
                tableData[{{ forloop.counter }}][10] = {{ n.climbs_with_photos }};
                tableData[{{ forloop.counter }}][11] = parseFloat((100 * (parseFloat({{ n.climbs_with_photos }}) / parseFloat(tableData[{{ forloop.counter }}][5])))).toFixed(2);
            {% endfor %}

            {% for n in climbs_with_trip_report %}
                tableData[{{ forloop.counter }}][12] = {{ n.climbs_with_trip_report }};
                tableData[{{ forloop.counter }}][13] = parseFloat((100 * (parseFloat({{ n.climbs_with_trip_report }}) / parseFloat(tableData[{{ forloop.counter }}][5])))).toFixed(2);
            {% endfor %}

            {% for n in climbs_with_gpx %}
                tableData[{{ forloop.counter }}][14] = {{ n.climbs_with_gpx }};
                tableData[{{ forloop.counter }}][15] = parseFloat((100 * (parseFloat({{ n.climbs_with_gpx }}) / parseFloat(tableData[{{ forloop.counter }}][5])))).toFixed(2);
            {% endfor %}

            {% for n in num_likes %}
                tableData[{{ forloop.counter }}][16] = {{ n.num_likes }};
            {% endfor %}

            {% for n in num_users_that_liked %}
                tableData[{{ forloop.counter }}][17] = {{ n.num_users_that_liked }};
            {% endfor %}

            {% for n in num_comments %}
                tableData[{{ forloop.counter }}][18] = {{ n.num_comments }};
            {% endfor %}

            {% for n in num_users_that_commented %}
                tableData[{{ forloop.counter }}][19] = {{ n.num_users_that_commented }};
            {% endfor %}

            {% for n in num_follows %}
                tableData[{{ forloop.counter }}][20] = {{ n.num_follows }};
            {% endfor %}

            {% for n in num_users_that_followed %}
                tableData[{{ forloop.counter }}][21] = {{ n.num_users_that_followed }};
            {% endfor %}

            {% for n in num_item_corrections %}
                tableData[{{ forloop.counter }}][22] = {{ n.num_item_corrections }};
            {% endfor %}

            {% for n in num_users_with_item_corrections %}
                tableData[{{ forloop.counter }}][23] = {{ n.num_users_with_item_corrections }};
            {% endfor %}

            {% for n in num_peaks_approved %}
                tableData[{{ forloop.counter }}][24] = {{ n.num_peaks_approved }};
            {% endfor %}

            {% for n in num_users_with_peaks_approved %}
                tableData[{{ forloop.counter }}][25] = {{ n.num_users_with_peaks_approved }};
            {% endfor %}

            drawAnnotations();

        {% endif %}

    }

    function drawAnnotations() {

        var numSignupsChartDataTable = google.visualization.arrayToDataTable(numSignupsChartData);
        var mobileSignupsChartDataTable = google.visualization.arrayToDataTable(mobileSignupsChartData);
        var androidSignupsChartDataTable = google.visualization.arrayToDataTable(androidSignupsChartData);
        var activeMembersChartDataTable = google.visualization.arrayToDataTable(activeMembersChartData);
        var androidActiveMembersChartDataTable = google.visualization.arrayToDataTable(androidActiveMembersChartData);
        var iosActiveMembersChartDataTable = google.visualization.arrayToDataTable(iosActiveMembersChartData);
        var numSignupsTableDataTable = google.visualization.arrayToDataTable(tableData);
        var numSignupsThatLoggedChartDataTable = google.visualization.arrayToDataTable(numSignupsThatLoggedChartData);
        //var numSignupsThatLoggedTableDataTable = google.visualization.arrayToDataTable(numSignupsThatLoggedTableData);
        var membersThatLoggedChartDataTable = google.visualization.arrayToDataTable(membersThatLoggedChartData);
        //var membersThatLoggedTableDataTable = google.visualization.arrayToDataTable(membersThatLoggedTableData);
        var climbsLoggedChartDataTable = google.visualization.arrayToDataTable(climbsLoggedChartData);
        //var climbsLoggedTableDataTable = google.visualization.arrayToDataTable(climbsLoggedTableData);
        var climbsWithPhotosChartDataTable = google.visualization.arrayToDataTable(climbsWithPhotosChartData);
        //var climbsWithPhotosTableDataTable = google.visualization.arrayToDataTable(climbsWithPhotosTableData);
        var climbsWithTripReportChartDataTable = google.visualization.arrayToDataTable(climbsWithTripReportChartData);
        //var climbsWithTripReportTableDataTable = google.visualization.arrayToDataTable(climbsWithTripReportTableData);
        var climbsWithGpxChartDataTable = google.visualization.arrayToDataTable(climbsWithGpxChartData);
        //var climbsWithGpxTableDataTable = google.visualization.arrayToDataTable(climbsWithGpxTableData);
        var numLikesChartDataTable = google.visualization.arrayToDataTable(numLikesChartData);
        //var numLikesTableDataTable = google.visualization.arrayToDataTable(numLikesTableData);
        var numUsersThatLikedChartDataTable = google.visualization.arrayToDataTable(numUsersThatLikedChartData);
        //var numUsersThatLikedTableDataTable = google.visualization.arrayToDataTable(numUsersThatLikedTableData);
        var numCommentsChartDataTable = google.visualization.arrayToDataTable(numCommentsChartData);
        //var numCommentsTableDataTable = google.visualization.arrayToDataTable(numCommentsTableData);
        var numUsersThatCommentedChartDataTable = google.visualization.arrayToDataTable(numUsersThatCommentedChartData);
        //var numUsersThatCommentedTableDataTable = google.visualization.arrayToDataTable(numUsersThatCommentedTableData);
        var numFollowsChartDataTable = google.visualization.arrayToDataTable(numFollowsChartData);
        //var numFollowsTableDataTable = google.visualization.arrayToDataTable(numFollowsTableData);
        var numUsersThatFollowedChartDataTable = google.visualization.arrayToDataTable(numUsersThatFollowedChartData);
        //var numUsersThatFollowedTableDataTable = google.visualization.arrayToDataTable(numUsersThatFollowedTableData);
        var numItemCorrectionsChartDataTable = google.visualization.arrayToDataTable(numItemCorrectionsChartData);
        //var numItemCorrectionsTableDataTable = google.visualization.arrayToDataTable(numItemCorrectionsTableData);
        var numUsersWithItemCorrectionsChartDataTable = google.visualization.arrayToDataTable(numUsersWithItemCorrectionsChartData);
        //var numUsersWithItemCorrectionsTableDataTable = google.visualization.arrayToDataTable(numUsersWithItemCorrectionsTableData);
        var numPeaksChartDataTable = google.visualization.arrayToDataTable(numPeaksChartData);
        //var numPeaksTableDataTable = google.visualization.arrayToDataTable(numPeaksTableData);
        var numUsersWithPeaksChartDataTable = google.visualization.arrayToDataTable(numUsersWithPeaksChartData);
        //var numUsersWithPeaksTableDataTable = google.visualization.arrayToDataTable(numUsersWithPeaksTableData);

        if ($( "#display_format").val() == 'chart') {

            var options = {
                backgroundColor: '#ffffff',
                legend: 'none',
                vAxis: {
                    baselineColor: '#ffffff',
                    gridlineColor: '#ffffff',
                    textPosition: 'none'
                },
                hAxis: {
                    textStyle: {color: '#00B1F2'}
                },
                animation: {
                    startup: true,
                    duration: 1000,
                    easing: 'out'
                },
                annotations: {
                    alwaysOutside: true,
                    stemLength: 3,
                    stemColor: '#ffffff',
                    textStyle: {
                        fontSize: 14,
                        color: '#000000',
                        auraColor: 'none'
                    }
                },
                chartArea: {
                    left: "0",
                    top: "5",
                    height: "80%",
                    width: "100%"
                },
                width: '100%',
                height: '100%',
                'tooltip': {
                    trigger: 'none'
                }
            };

            var numSignupsChart = new google.visualization.ColumnChart(document.getElementById('num-signups-chart-div'));
            var mobileSignupsChart = new google.visualization.ColumnChart(document.getElementById('num-mobile-signups-chart-div'));
            var androidSignupsChart = new google.visualization.ColumnChart(document.getElementById('num-android-signups-chart-div'));
            var activeMembersChart = new google.visualization.ColumnChart(document.getElementById('active-members-chart-div'));
            var androidActiveMembersChart = new google.visualization.ColumnChart(document.getElementById('android-active-members-chart-div'));
            var iosActiveMembersChart = new google.visualization.ColumnChart(document.getElementById('ios-active-members-chart-div'));
            var numSignupsThatLoggedChart = new google.visualization.ColumnChart(document.getElementById('num-signups-that-logged-chart-div'));
            var membersThatLoggedChart = new google.visualization.ColumnChart(document.getElementById('members-that-logged-chart-div'));
            var climbsLoggedChart = new google.visualization.ColumnChart(document.getElementById('climbs-logged-chart-div'));
            var climbsWithPhotosChart = new google.visualization.ColumnChart(document.getElementById('climbs-with-photos-chart-div'));
            var climbsWithTripReportChart = new google.visualization.ColumnChart(document.getElementById('climbs-with-trip-report-chart-div'));
            var climbsWithGpxChart = new google.visualization.ColumnChart(document.getElementById('climbs-with-gpx-chart-div'));
            var numLikesChart = new google.visualization.ColumnChart(document.getElementById('num-likes-chart-div'));
            var numUsersThatLikedChart = new google.visualization.ColumnChart(document.getElementById('num-users-that-liked-chart-div'));
            var numCommentsChart = new google.visualization.ColumnChart(document.getElementById('num-comments-chart-div'));
            var numUsersThatCommentedChart = new google.visualization.ColumnChart(document.getElementById('num-users-that-commented-chart-div'));
            var numFollowsChart = new google.visualization.ColumnChart(document.getElementById('num-follows-chart-div'));
            var numUsersThatFollowedChart = new google.visualization.ColumnChart(document.getElementById('num-users-that-followed-chart-div'));
            var numItemCorrectionsChart = new google.visualization.ColumnChart(document.getElementById('num-item-corrections-chart-div'));
            var numUsersWithItemCorrectionsChart = new google.visualization.ColumnChart(document.getElementById('num-users-with-item-corrections-chart-div'));
            var numPeaksChart = new google.visualization.ColumnChart(document.getElementById('num-peaks-chart-div'));
            var numUsersWithPeaksChart = new google.visualization.ColumnChart(document.getElementById('num-users-with-peaks-chart-div'));

            $('#num-signups-header').show();
            $('#num-mobile-signups-header').show();
            $('#num-android-signups-header').show();
            $('#active-members-header').show();
            $('#android-active-members-header').show();
            $('#ios-active-members-header').show();
            $('#num-signups-that-logged-header').show();
            $('#members-that-logged-header').show();
            $('#climbs-logged-header').show();
            $('#climbs-with-photos-header').show();
            $('#climbs-with-trip-report-header').show();
            $('#climbs-with-gpx-header').show();
            $('#num-likes-header').show();
            $('#num-users-that-liked-header').show();
            $('#num-comments-header').show();
            $('#num-users-that-commented-header').show();
            $('#num-follows-header').show();
            $('#num-users-that-followed-header').show();
            $('#num-item-corrections-header').show();
            $('#num-users-with-item-corrections-header').show();
            $('#num-peaks-header').show();
            $('#num-users-with-peaks-header').show();

            $('#num-signups-chart-container').show();
            $('#num-mobile-signups-chart-container').show();
            $('#num-android-signups-chart-container').show();
            $('#active-members-chart-container').show();
            $('#android-active-members-chart-container').show();
            $('#ios-active-members-chart-container').show();
            $('#num-signups-that-logged-chart-container').show();
            $('#members-that-logged-chart-container').show();
            $('#climbs-logged-chart-container').show();
            $('#climbs-with-photos-chart-container').show();
            $('#climbs-with-trip-report-chart-container').show();
            $('#climbs-with-gpx-chart-container').show();
            $('#num-likes-chart-container').show();
            $('#num-users-that-liked-chart-container').show();
            $('#num-comments-chart-container').show();
            $('#num-users-that-commented-chart-container').show();
            $('#num-follows-chart-container').show();
            $('#num-users-that-followed-chart-container').show();
            $('#num-item-corrections-chart-container').show();
            $('#num-users-with-item-corrections-chart-container').show();
            $('#num-peaks-chart-container').show();
            $('#num-users-with-peaks-chart-container').show();

            numSignupsChart.draw(numSignupsChartDataTable, options);
            mobileSignupsChart.draw(mobileSignupsChartDataTable, options);
            androidSignupsChart.draw(androidSignupsChartDataTable, options);
            activeMembersChart.draw(activeMembersChartDataTable, options);
            androidActiveMembersChart.draw(androidActiveMembersChartDataTable, options);
            iosActiveMembersChart.draw(iosActiveMembersChartDataTable, options);
            numSignupsThatLoggedChart.draw(numSignupsThatLoggedChartDataTable, options);
            membersThatLoggedChart.draw(membersThatLoggedChartDataTable, options);
            climbsLoggedChart.draw(climbsLoggedChartDataTable, options);
            climbsWithPhotosChart.draw(climbsWithPhotosChartDataTable, options);
            climbsWithTripReportChart.draw(climbsWithTripReportChartDataTable, options);
            climbsWithGpxChart.draw(climbsWithGpxChartDataTable, options);
            numLikesChart.draw(numLikesChartDataTable, options);
            numUsersThatLikedChart.draw(numUsersThatLikedChartDataTable, options);
            numCommentsChart.draw(numCommentsChartDataTable, options);
            numUsersThatCommentedChart.draw(numUsersThatCommentedChartDataTable, options);
            numFollowsChart.draw(numFollowsChartDataTable, options);
            numUsersThatFollowedChart.draw(numUsersThatFollowedChartDataTable, options);
            numItemCorrectionsChart.draw(numItemCorrectionsChartDataTable, options);
            numUsersWithItemCorrectionsChart.draw(numUsersWithItemCorrectionsChartDataTable, options);
            numPeaksChart.draw(numPeaksChartDataTable, options);
            numUsersWithPeaksChart.draw(numUsersWithPeaksChartDataTable, options);

        } else {

            var numSignupsTable = new google.visualization.Table(document.getElementById('num-signups-table-div'));
            //var numSignupsThatLoggedTable = new google.visualization.Table(document.getElementById('num-signups-that-logged-table-div'));
            //var membersThatLoggedTable = new google.visualization.Table(document.getElementById('members-that-logged-table-div'));
            //var climbsLoggedTable = new google.visualization.Table(document.getElementById('climbs-logged-table-div'));
            //var climbsWithPhotosTable = new google.visualization.Table(document.getElementById('climbs-with-photos-table-div'));
            //var climbsWithTripReportTable = new google.visualization.Table(document.getElementById('climbs-with-trip-report-table-div'));
            //var climbsWithGpxTable = new google.visualization.Table(document.getElementById('climbs-with-gpx-table-div'));
            //var numLikesTable = new google.visualization.Table(document.getElementById('num-likes-table-div'));
            //var numUsersThatLikedTable = new google.visualization.Table(document.getElementById('num-users-that-liked-table-div'));
            //var numCommentsTable = new google.visualization.Table(document.getElementById('num-comments-table-div'));
            //var numUsersThatCommentedTable = new google.visualization.Table(document.getElementById('num-users-that-commented-table-div'));
            //var numFollowsTable = new google.visualization.Table(document.getElementById('num-follows-table-div'));
            //var numUsersThatFollowedTable = new google.visualization.Table(document.getElementById('num-users-that-followed-table-div'));
            //var numItemCorrectionsTable = new google.visualization.Table(document.getElementById('num-item-corrections-table-div'));
            //var numUsersWithItemCorrectionsTable = new google.visualization.Table(document.getElementById('num-users-with-item-corrections-table-div'));
            //var numPeaksTable = new google.visualization.Table(document.getElementById('num-peaks-table-div'));
            //var numUsersWithPeaksTable = new google.visualization.Table(document.getElementById('num-users-with-peaks-table-div'));

            $('#num-signups-header').hide();
            $('#num-mobile-signups-header').hide();
            $('#num-android-signups-header').hide();
            $('#active-members-header').hide();
            $('#android-active-members-header').hide();
            $('#ios-active-members-header').hide();
            $('#num-signups-that-logged-header').hide();
            $('#members-that-logged-header').hide();
            $('#climbs-logged-header').hide();
            $('#climbs-with-photos-header').hide();
            $('#climbs-with-trip-report-header').hide();
            $('#climbs-with-gpx-header').hide();
            $('#num-likes-header').hide();
            $('#num-users-that-liked-header').hide();
            $('#num-comments-header').hide();
            $('#num-users-that-commented-header').hide();
            $('#num-follows-header').hide();
            $('#num-users-that-followed-header').hide();
            $('#num-item-corrections-header').hide();
            $('#num-users-with-item-corrections-header').hide();
            $('#num-peaks-header').hide();
            $('#num-users-with-peaks-header').hide();

            $('#num-signups-table-container').show();

            numSignupsTable.draw(numSignupsTableDataTable, {showRowNumber: false, width: '100%', height: '100%'});
            //numSignupsThatLoggedTable.draw(numSignupsThatLoggedTableDataTable, {showRowNumber: true, width: '100%', height: '100%'});
            //membersThatLoggedTable.draw(membersThatLoggedTableDataTable, {showRowNumber: true, width: '100%', height: '100%'});
            //climbsLoggedTable.draw(climbsLoggedTableDataTable, {showRowNumber: true, width: '100%', height: '100%'});
            //climbsWithPhotosTable.draw(climbsWithPhotosTableDataTable, {showRowNumber: true, width: '100%', height: '100%'});
            //climbsWithTripReportTable.draw(climbsWithTripReportTableDataTable, {showRowNumber: true, width: '100%', height: '100%'});
            //climbsWithGpxTable.draw(climbsWithGpxTableDataTable, {showRowNumber: true, width: '100%', height: '100%'});
            //numLikesTable.draw(numLikesTableDataTable, {showRowNumber: true, width: '100%', height: '100%'});
            //numUsersThatLikedTable.draw(numUsersThatLikedTableDataTable, {showRowNumber: true, width: '100%', height: '100%'});
            //numCommentsTable.draw(numCommentsTableDataTable, {showRowNumber: true, width: '100%', height: '100%'});
            //numUsersThatCommentedTable.draw(numUsersThatCommentedTableDataTable, {showRowNumber: true, width: '100%', height: '100%'});
            //numFollowsTable.draw(numFollowsTableDataTable, {showRowNumber: true, width: '100%', height: '100%'});
            //numUsersThatFollowedTable.draw(numUsersThatFollowedTableDataTable, {showRowNumber: true, width: '100%', height: '100%'});
            //numItemCorrectionsTable.draw(numItemCorrectionsTableDataTable, {showRowNumber: true, width: '100%', height: '100%'});
            //numUsersWithItemCorrectionsTable.draw(numUsersWithItemCorrectionsTableDataTable, {showRowNumber: true, width: '100%', height: '100%'});
            //numPeaksTable.draw(numPeaksTableDataTable, {showRowNumber: true, width: '100%', height: '100%'});
            //numUsersWithPeaksTable.draw(numUsersWithPeaksTableDataTable, {showRowNumber: true, width: '100%', height: '100%'});
        }

    }

    //create trigger to resizeEnd event
    $(window).resize(function() {
        if(this.resizeTO) clearTimeout(this.resizeTO);
        this.resizeTO = setTimeout(function() {
            $(this).trigger('resizeEnd');
        }, 500);
    });

    //redraw graph when window resize is completed
    $(window).on('resizeEnd', function() {
        drawAnnotations();
    });

  </script>

{% endblock %}