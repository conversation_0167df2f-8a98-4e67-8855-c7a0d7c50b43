{% extends "admin/base_site.html" %}
{% load static %}
{% block extrastyle %}
  <link rel="stylesheet" type="text/css" href="{% static 'admin/css/changelists.css' %}" />
  <link rel="stylesheet" type="text/css" href="{% static 'admin/css/forms.css' %}" />
{% endblock %}

{% block content %}

<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.3/jquery.min.js"></script>

<!-- Content -->
<div id="content" class="flex">
    <div>
        <a href="/tools/">Tools</a> > <a href="/tools/highlights/log/">Highlights Log</a> > {{ route.name }}
    </div>
    <br />
    <h1>Highlights Log - {{ route.name }}</h1>
    <div id="content-main">
        <div class="module" id="changelist">
            <form id="changelist-form" action="/tools/highlights/route/{{ route.id }}/" method="post">
                <div class="actions">
                    <label>Action: <select name="action" id="action">
                    <option value="" selected="selected">---------</option>
                    <option value="revert">Revert to Selected</option>
                    <option value="delete">Delete Current Highlights</option>
                    </select>
                    </label>
                    <button id="submit-button" class="button" title="Run the selected action" name="index" value="0">Go</button>
                </div>
                <div class="results">
                    <table id="result_list">
                        <tbody>
                            <tr>
                                <td style="width: 50px;">Select</td>
                                <td>Username</td>
                                <td>Log Date</td>
                                <td>Highlights</td>
                            </tr>
                            {% for h in highlights %}
                            <tr class="{% cycle 'row1' 'row2' %}">
                                <td><input class="highlights-version" type="radio" id="rdoHighlights{{ p.id }}" name="rdoHighlights" value="{{ h.id }}"></td>
                                <td>{{ h.username }}</td>
                                <td>{{ h.log_date }}</td>
                                <td style="white-space: pre-wrap;">{{ h.highlights }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </form>
        </div>
    </div>
    <br class="clear" />
</div>
<!-- END Content -->

<script type="text/javascript">

    $(document).ready(function() {

        $('#submit-button').on('click', function(e) {
            e.preventDefault();
            var optionChosen = false;
            $('.highlights-version').each(function(i, obj) {
                if ($(this).prop('checked')) {
                    optionChosen = true;
                }
            });

            if (optionChosen || $('#action').val() == 'delete') {
                $('#changelist-form').submit();
            } else {
                alert('Please choose a revision to revert to.');
            }
        });

    });

</script>

{% endblock %}