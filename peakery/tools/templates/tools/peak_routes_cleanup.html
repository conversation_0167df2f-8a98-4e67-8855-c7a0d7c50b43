{% extends "admin/base_site.html" %}
{% load static %}
{% block extrastyle %}
  <link rel="stylesheet" type="text/css" href="{% static 'admin/css/changelists.css' %}" />
  <link rel="stylesheet" type="text/css" href="{% static 'admin/css/forms.css' %}" />
{% endblock %}

{% block content %}

<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.3/jquery.min.js"></script>

<!-- Content -->
<div id="content" class="flex">
    <div>
        <a href="/tools/">Tools</a> > <a href="/tools/routes/cleanup/">Routes Cleanup Tool</a> > {{ peak.name }}
    </div>
    <br />
    <h1>Routes Cleanup Tool</h1>
    <div id="content-main">
        <div class="module" id="changelist">
            <form id="changelist-form" action="/tools/routes/cleanup/{{ peak_id }}/" method="post">
                <div class="actions">
                    <label>Action: <select name="action" id="action">
                    <option value="" selected="selected">---------</option>
                    <option value="combine">Combine Selected</option>
                    </select>
                    </label>
                    <input style="width: 500px;" type="text" name="txtNewRouteName" id="txtNewRouteName" placeholder="Combined Route Name" />
                    <button id="submit-button" class="button" title="Run the selected action" name="index" value="0">Go</button>
                </div>
                <div class="results">
                    <table id="result_list">
                        <tbody>
                            <tr>
                                <td style="width: 50px;">Select</td>
                                <td style="width: 50px;">Primary</td>
                                <td><a href="/tools/routes/cleanup/{{ peak_id }}?sort=route">Route</a></td>
                                <td><a href="/tools/routes/cleanup/{{ peak_id }}?sort=summits">Summits</a></td>
                            </tr>
                            {% for r in routes %}
                            <tr class="{% cycle 'row1' 'row2' %}">
                                <td><input class="route-checkbox" type="checkbox" name="chkRoutes" value="{{ r.id }}"></td>
                                <td><input type="radio" id="rdoRoute{{ r.id }}" name="rdoRoutes" value="{{ r.id }}"></td>
                                <td><a style="cursor: pointer;" class="route-name">{{ r.route_name }}</a></td>
                                <td>{{ r.summit_count }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </form>
        </div>
    </div>
    <br class="clear" />
</div>
<!-- END Content -->

<script type="text/javascript">

    $(document).ready(function() {

        $('.route-name').on('click', function() {
            $('#txtNewRouteName').val($(this).text());
        });

        $('.route-checkbox').on('click', function() {
            if ($(this).prop('checked')) {
                $('#rdoRoute'+$(this).val()).prop('checked', true);
            }
        });

        $('#submit-button').on('click', function(e) {
            e.preventDefault();
            var primarySet = false;
            var routesChosen = false;
            var counter = 0;
            $('.route-checkbox').each(function(i, obj) {
                if ($(this).prop('checked')) {
                    if ($('#rdoRoute'+$(this).val()).prop('checked')) {
                        primarySet = true;
                    }
                    counter++;
                }
            });
            if (counter > 1) {
                routesChosen = true;
            }

            if ($('#txtNewRouteName').val() == '') {
                alert('Please enter a new route name.');
            } else if (primarySet && routesChosen) {
                $('#changelist-form').submit();
            } else if (!routesChosen) {
                alert('Please select at least two routes to combine.');
            } else {
                alert('Please choose a primary route.');
            }
        });

    });

</script>

{% endblock %}