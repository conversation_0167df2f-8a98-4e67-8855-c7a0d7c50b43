{% extends "admin/base_site.html" %}
{% load static %}
{% block extrastyle %}
  <link rel="stylesheet" type="text/css" href="{% static 'admin/css/changelists.css' %}" />
  <link rel="stylesheet" type="text/css" href="{% static 'admin/css/forms.css' %}" />
{% endblock %}

{% block content %}

<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.3/jquery.min.js"></script>

<!-- Content -->
<div id="content" class="flex">
    <div>
        <a href="/tools/">Tools</a> > Routes Cleanup Tool
    </div>
    <br />
    <h1>Routes Cleanup Tool</h1>
    <div id="content-main">
        <div class="module" id="changelist">
            <form id="changelist-form" action="#" method="post">
                <div class="actions">
                    <label>Search: <input style="width: 500px;" type="text" name="txtPeakSearch" id="txtPeakSearch" placeholder="Search For Peak" value="{{ peak_keyword }}" />
                    </label><input class="select-across" name="select_across" type="hidden" value="0" />
                    <button type="submit" class="button" title="Search For Peak" id="cmdPeakSearch" name="cmdPeakSearch">Go</button>
                </div>
                <div class="results">
                    <table id="result_list">
                        <tbody>
                            <tr>
                                <td><a href="/tools/routes/cleanup/?keyword={{ peak_keyword }}&sort=peak">Peak</a></td>
                                <td><a href="/tools/routes/cleanup/?keyword={{ peak_keyword }}&sort=summits">Summits</a></td>
                                <td><a href="/tools/routes/cleanup/?keyword={{ peak_keyword }}&sort=routes">Routes</a></td>
                                <td><a href="/tools/routes/cleanup/?keyword={{ peak_keyword }}&sort=modified">Last Modified</a></td>
                            </tr>
                            {% for p in peaks %}
                            <tr class="{% cycle 'row1' 'row2' %}">
                                <th><a href="/tools/routes/cleanup/{{ p.id }}">{{ p.name }}</a></th>
                                <td>{{ p.summit_count }}</td>
                                <td>{{ p.route_count }}</td>
                                <td>{{ p.last_modified }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </form>
        </div>
    </div>
    <br class="clear" />
</div>
<!-- END Content -->

<script type="text/javascript">

    $(document).ready(function() {

        $('#changelist-form').on('submit', function() {
            window.location.href = '/tools/routes/cleanup/?keyword='+$('#txtPeakSearch').val();
            return false;
        });

    });

</script>

{% endblock %}