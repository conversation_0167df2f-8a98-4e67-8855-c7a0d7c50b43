{% extends "admin/base_site.html" %}
{% load static %}
{% block extrastyle %}
  <link rel="stylesheet" type="text/css" href="{% static 'admin/css/changelists.css' %}" />
  <link rel="stylesheet" type="text/css" href="{% static 'admin/css/forms.css' %}" />
{% endblock %}

{% block content %}

<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.3/jquery.min.js"></script>

<!-- Content -->
<div id="content" class="flex">
    <div>
        <a href="/tools/">Tools</a> > Highlights Log
    </div>
    <br />
    <h1>Highlights Log</h1>
    <div id="content-main">
        <div class="module" id="changelist">
            <form id="changelist-form" action="#" method="post">
                <div class="actions">
                    <label>Search: <input style="width: 500px;" type="text" name="txtSearch" id="txtSearch" placeholder="Search For Keyword" value="{{ keyword }}" />
                    </label><input class="select-across" name="select_across" type="hidden" value="0" />
                    <button type="submit" class="button" title="Search For Keyword" id="cmdSearch" name="cmdSearch">Go</button>
                </div>
                <div class="results">
                    <table id="result_list">
                        <tbody>
                            <tr>
                                <td>Type</td>
                                <td>ID</td>
                                <td style="width: 200px;">Name</td>
                                <td style="width: 200px;">Username</td>
                                <td style="width: 200px;">Log Date</td>
                                <td>Highlights</td>
                            </tr>
                            {% for h in highlights_log %}
                            <tr class="{% cycle 'row1' 'row2' %}">
                                <td>{{ h.item_type }}</td>
                                <td><a href="/tools/highlights/{{ h.item_type }}/{{ h.item_id }}">{{ h.item_id }}</a></td>
                                <td><a href="{{ h.item_url }}" target="_blank">{{ h.item_name }}</a></td>
                                <td>{{ h.username }}</td>
                                <td>{{ h.last_modified }}</td>
                                <td style="white-space: pre-wrap;">{{ h.highlights }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </form>
        </div>
    </div>
    <br class="clear" />
</div>
<!-- END Content -->

<script type="text/javascript">

    $(document).ready(function() {

        $('#changelist-form').on('submit', function() {
            window.location.href = '/tools/highlights/log/?keyword='+$('#txtSearch').val();
            return false;
        });

    });

</script>

{% endblock %}