from django.urls import path
from django.views.decorators.csrf import csrf_exempt

from peakery.tools.views import tools_home, peak_routes_cleanup, routes_cleanup, combine_peaks, highlights_peak_log
from peakery.tools.views import highlights_challenge_log, highlights_route_log, highlights_continent_log, highlights_country_log
from peakery.tools.views import highlights_region_log, highlights_log, highlights_setup, dashboard, user_report, peak_data_report

urlpatterns = [
    path('', tools_home, name='tools_home'),
    path('routes/cleanup/<int:peak_id>/', csrf_exempt(peak_routes_cleanup), name='peak_routes_cleanup'),
    path('routes/cleanup/', csrf_exempt(routes_cleanup), name='routes_cleanup'),
    path('peaks/combine/', csrf_exempt(combine_peaks), name='combine_peaks'),
    path('highlights/peak/<int:peak_id>/', highlights_peak_log, name='highlights_peak_log'),
    path('highlights/challenge/<int:challenge_id>/', highlights_challenge_log, name='highlights_challenge_log'),
    path('highlights/route/<int:peak_route_id>/', highlights_route_log, name='highlights_route_log'),
    path('highlights/continent/<int:continent_id>/', highlights_continent_log, name='highlights_continent_log'),
    path('highlights/country/<int:country_id>/', highlights_country_log, name='highlights_country_log'),
    path('highlights/region/<int:region_id>/', highlights_region_log, name='highlights_region_log'),
    path('highlights/log/', highlights_log, name='highlights_log'),
    path('highlights/setup/', highlights_setup, name='highlights_setup'),
    path('dashboard/', csrf_exempt(dashboard), name='dashboard'),
    path('users/', csrf_exempt(user_report), name='user_report'),
    path('peakdata/', csrf_exempt(peak_data_report), name='peak_data_report'),
]