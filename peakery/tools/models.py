from django.contrib.gis.db import models


class ActivityLog(models.Model):
    user_id = models.IntegerField(null=False)
    ip_address = models.CharField(max_length=200)
    activity_date = models.DateTimeField(blank=True, null=True)
    activity_source = models.CharField(max_length=255)

    class Meta:
        db_table = 'activity_log'


class CalendarDay(models.Model):
    day = models.CharField(max_length=10, null=False)
    start_date = models.DateTimeField(null=False)
    end_date = models.DateTimeField(null=False)

    class Meta:
        db_table = 'calendar_day'


class CalendarMonth(models.Model):
    month = models.CharField(max_length=10, null=False)
    start_date = models.DateTimeField(null=False)
    end_date = models.DateTimeField(null=False)

    class Meta:
        db_table = 'calendar_month'


class CalendarWeek(models.Model):
    week = models.CharField(max_length=10, null=False)
    start_date = models.DateTimeField(null=False)
    end_date = models.DateTimeField(null=False)

    class Meta:
        db_table = 'calendar_week'


class CalendarYear(models.Model):
    year = models.CharField(max_length=10, null=False)
    start_date = models.DateTimeField(null=False)
    end_date = models.DateTimeField(null=False)

    class Meta:
        db_table = 'calendar_year'
