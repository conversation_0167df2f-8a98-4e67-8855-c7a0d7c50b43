# We import the settings.py file and we override some settings for dev purposes
from peakery.settings import *
import sys

DEBUG = True
TEMPLATE_DEBUG = True
ALLOWED_HOSTS = ['*']
SECURE_SSL_REDIRECT = False

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console':{
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'DEBUG',
            'propagate': True,
        },
        'django.utils.autoreload': { # We set this one to info so we don't get flooded with autoreload logs.
            'level': 'INFO'
        }
    },
}


if sys.platform != "win32" and 'linux' not in sys.platform: # Set the following variables only if not on windows nor linux. These variables work for MAC
    GDAL_LIBRARY_PATH = '/opt/homebrew/opt/gdal/lib/libgdal.dylib'
    GEOS_LIBRARY_PATH = '/opt/homebrew/opt/geos/lib/libgeos_c.dylib'


# In the local environment, instead of reading static files from S3, we read them from our local machine for rapid development
BASE_DIR = os.path.dirname(os.path.dirname(__file__))

STATIC_URL = '/peakery/static/'
STATICFILES_DIRS = (
    os.path.join(BASE_DIR, os.path.join(BASE_DIR, "peakery"), "static"),
)
